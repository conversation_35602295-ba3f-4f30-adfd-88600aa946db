
import { <PERSON><PERSON><PERSON> } from 'jsdom';
import { configure } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';

configure({ adapter: new Adapter() });

const exposedProperties = ['window', 'navigator', 'document'];
const dom = new JSDOM(``, {
  url: "http://localhost"
});
const { document } = dom.window;
global.document = document;
global.window = document.defaultView;
global.HTMLElement = window.HTMLElement;
global.HTMLAnchorElement = window.HTMLAnchorElement;

// Object.defineProperty(global.window.location, 'reload', {
//   configurable: true,
// });

Object.keys(document.defaultView).forEach(property => {
  if (typeof global[property] === 'undefined') {
    exposedProperties.push(property);
    global[property] = document.defaultView[property];
  }
});

delete global.window.location;
global.window.location = {};

global.navigator = {
  userAgent: 'node.js',
};
