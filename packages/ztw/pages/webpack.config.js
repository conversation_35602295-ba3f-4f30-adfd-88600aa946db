const webpackMerge = require('webpack-merge');
const common = require('./webpack/webpack.common');

const envs = {
  development: 'dev',
  production: 'prod',
  copy: 'copy',
  svn: 'svn',
  mini: 'mini',
  oneui: 'oneui',
  test: 'test',
};

/* eslint-disable global-require,import/no-dynamic-require */
const env = envs[process.env.NODE_ENV || 'production'];
const envConfig = require(`./webpack/webpack.${env}.js`);
// speed-measure can interfere with dev build hmr
module.exports = webpackMerge(common, envConfig);
