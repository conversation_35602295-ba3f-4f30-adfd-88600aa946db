const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const path = require('path');
const commonPaths = require('./paths');

module.exports = {
  entry: {
    main: commonPaths.entryPath,
  },
  stats: { warnings: false, errorDetails: false, errors: false },
  node: {
    global: false,
  },
  // trustedTypes: true,
  mode: 'production',
  output: {
    path: commonPaths.svnOutputPath,
    chunkFilename: `${commonPaths.jsFolder}/vendors[id].js`,
    filename: `${commonPaths.jsFolder}/react-app.js`,
    clean: true,
    publicPath: '/',
  },
  module: {
    rules: [
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          'style-loader',
          MiniCssExtractPlugin.loader,
          'css-loader',
          'resolve-url-loader',
          {
            loader: 'sass-loader',
            options: {
              // trustedTypes: true,
              implementation: require.resolve('sass'),
              sassOptions: {
                fiber: false,
                includePaths: [
                  path.resolve(__dirname, '../node_modules/*'),
                  path.resolve(__dirname, '../src/commonConnectedComponents/*'),
                  path.resolve(__dirname, '../src/components/*'),
                  path.resolve(__dirname, '../src/pages/*'),
                ],
              },
              sourceMap: true, // Required for resolve-url-loader to work correctly with sass-loader
            },
          },
        ],
      },
      {
        test: /\.(js|jsx)$/,
        // exclude: /(node_modules)/,
        // enforce: 'post',
        use: {
          // babel-loader to convert ES6 code to ES5 + and
          // Cleaning requirejs code into simple JS code, taking care of modules to load as desired
          loader: 'babel-loader',
          options: {
            // trustedTypes: true,
            presets: ['@babel/preset-env'],
            plugins: [
              [
                '@babel/plugin-transform-function-name',
                {
                  loose: true,
                  spec: false,
                },
              ],
            ],
          },
        },
      },
    ],
  },
  optimization: {
    minimize: true,
    // trustedTypes: true,
    minimizer: [
      new TerserPlugin({
        parallel: true,
        terserOptions: {
          compress: {
            unsafe: false,
            unsafe_methods: false,
            reduce_funcs: false,
            dead_code: true,
            drop_console: true,
            drop_debugger: true,
            keep_fargs: false,
            keep_fnames: false,
            passes: 2,
          },
          output: {
            ascii_only: true,
          },
          mangle: {
            reserved: ['React', 'ReactDOM'],
          },
        },
      }),
    ],
    splitChunks: {
      cacheGroups: {
        default: false,
        vendors: false,
        defaultVendors: {
          reuseExistingChunk: true,
        },
        // vendor: {
        //   chunks: 'all',
        //   test: /node_modules/,
        // },
      },
      // chunks: 'all',
      // minSize: 500,
    },
  },
  plugins: [
    new CompressionPlugin({
      cache: true,
      include: /\/includes/,
      test: /\.(js|css|html|svg)$/,
      algorithm: 'gzip',
    }),
    new CleanWebpackPlugin([commonPaths.oneUiPath.split('/').pop()], {
      root: commonPaths.root,
    }),
    new MiniCssExtractPlugin({
      filename: `${commonPaths.cssFolder}/reactmain.css`,
      // allChunks: true,
    }),
    new Dotenv({ path: './.env.production' }),
    // new GenerateSW({
    //   clientsClaim: true,
    // }),
  ],
  devtool: false,
  experiments: {
    topLevelAwait: true,
  },
};
