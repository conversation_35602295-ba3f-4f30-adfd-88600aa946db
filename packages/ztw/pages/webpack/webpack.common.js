const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');

const gitRevisionPlugin = new GitRevisionPlugin();

const commonPaths = require('./paths');

module.exports = {
  entry: commonPaths.entryPath,
  module: {
    rules: [
      // {
      //   test: /\.(js|jsx)$/,
      //   loader: 'babel-loader',
      //   options: {
      //     plugins: ['babel-plugin-istanbul'],
      //   },
      // },
      {
        test: /\.(png|jpg|gif|svg)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              outputPath: commonPaths.imagesFolder,
            },
          },
        ],
      },
      {
        test: /\.(woff2|ttf|woff|eot)$/,
        use: [
          {
            loader: 'file-loader',
            options: {
              outputPath: commonPaths.fontsFolder,
            },
          },
        ],
      },
    ],
  },
  resolve: {
    modules: ['src', 'node_modules'],
    extensions: ['.*', '.js', '.jsx', '.css', '.scss'],
    preferRelative: true,
    fallback: {
      fs: false,
      child_process: false,
      worker_threads: false,
      '@swc/core': false,
      esbuild: false,
    },
  },
  plugins: [
    // new ESLintPlugin({
    //   lintDirtyModulesOnly: true,
    // }),
    new webpack.ProgressPlugin(),
    new Dotenv(),
    new NodePolyfillPlugin(),
    new HtmlWebpackPlugin({
      template: commonPaths.templatePath,
      base: '',
      inlineSource: '.(js|css|scss)$',
    }),
    new webpack.DefinePlugin({
      VERSION: JSON.stringify(gitRevisionPlugin.version()),
      COMMITHASH: JSON.stringify(gitRevisionPlugin.commithash()),
      BRANCH: JSON.stringify(gitRevisionPlugin.branch()),
      LASTCOMMITDATETIME: JSON.stringify(gitRevisionPlugin.lastcommitdatetime()),
    }),
  ],
};
