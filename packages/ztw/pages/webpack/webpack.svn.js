const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const WebpackDynamicPublicPathPlugin = require('webpack-dynamic-public-path');
const path = require('path');
const commonPaths = require('./paths');

module.exports = {
  entry: {
    main: commonPaths.entryPath,
  },
  mode: 'production',
  output: {
    path: commonPaths.svnOutputPath,
    chunkFilename: `${commonPaths.jsFolder}/vendors.js`,
    filename: `${commonPaths.jsFolder}/react-app.js`,
    library: 'ReactApp',
    libraryTarget: 'var',
    publicPath: '/ec',
  },
  module: {
    rules: [
      {
        test: /\.(css|scss)$/,
        use: [
          'style-loader',
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              sourceMap: false,
              modules: true,
              camelCase: true,
              localIdentName: '[local]',
            },
          },
          {
            loader: 'sass-loader?indentedSyntax=false',
            options: {
              includePaths: [
                path.resolve(__dirname, '../node_modules/*'),
                path.resolve(__dirname, '../src/components/*'),
                path.resolve(__dirname, '../src/pages/*'),
              ],
            },
          },
        ],
      },
      {
        test: /\.(js|jsx)$/,
        exclude: /(node_modules)/,
        use: {
          // babel-loader to convert ES6 code to ES5 + and
          // Cleaning requirejs code into simple JS code, taking care of modules to load as desired
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: [],
          },
        },
      },
    ],
  },
  optimization: {
    minimize: false,
  },
  devServer: {
    historyApiFallback: true,
    contentBase: './',
  },
  plugins: [
    new WebpackDynamicPublicPathPlugin({
      externalPublicPath: 'window.externalPublicPath',
    }),
    new CleanWebpackPlugin([commonPaths.svnOutputPath.split('/').pop()], {
      root: commonPaths.root,
    }),
    new MiniCssExtractPlugin({
      filename: `${commonPaths.cssFolder}/ecmain.css`,
      chunkFilename: 'ecmain.css',
      allChunks: true,
    }),
  ],
  devtool: 'eval-source-map',
};
