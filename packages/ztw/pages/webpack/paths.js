const path = require('path');

module.exports = {
  root: path.resolve(__dirname, '../'),
  outputPath: path.resolve(__dirname, '../', 'build'),
  svnOutputPath: path.resolve(__dirname, '../', 'app_dist'),
  oneUiPath: path.resolve(__dirname, '../', 'oneUI_dist'),
  entryPath: path.resolve(__dirname, '../', 'src/App.jsx'),
  templatePath: path.resolve(__dirname, '../', 'src/template.html'),
  imagesFolder: 'images',
  fontsFolder: 'fonts',
  cssFolder: 'css',
  jsFolder: 'js',
  backboneOutputPath: path.resolve(__dirname, '../', 'backbone-bundle'),
  backboneEntryPath: path.resolve(__dirname, '../', 'src/BackboneEntry.js'),
};
