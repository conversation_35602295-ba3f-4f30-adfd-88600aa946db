const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const commonPaths = require('./paths');

module.exports = {
  entry: {
    main: commonPaths.backboneEntryPath,
  },
  mode: 'development',
  devtool: 'eval-source-map',
  output: {
    path: commonPaths.backboneOutputPath,
    chunkFilename: `${commonPaths.jsFolder}/vendors.js`,
    filename: `${commonPaths.jsFolder}/react-app.js`,
    library: 'ReactApp',
    libraryTarget: 'var',
  },

  resolve: {
    modules: ['node_modules'],
    extensions: ['*', '.js', '.json', '.css'],
  },
  module: {
    rules: [
      {
        test: /\.(css|scss)$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              sourceMap: false,
              modules: true,
              camelCase: true,
              localIdentName: '[local]___[hash:base64:5]',
            },
          },
          'sass-loader',
        ],
      },
      {
        test: /\.(js|jsx)$/,
        exclude: /(node_modules)/,
        use: {
          // babel-loader to convert ES6 code to ES5 +
          // amdCleaning requirejs code into simple JS code,
          // taking care of modules to load as desired
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: [],
          },
        },
      },
    ],
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: `${commonPaths.cssFolder}/ecmain.css`,
    }),

    new CopyPlugin([
      {
        from: './backbone-bundle/js/*',
        to: '/usr/local/tomcat/webapps/zmanage/js/libs',
        force: true,
        flatten: true,
      },
      {
        from: './backbone-bundle/css/*',
        to: '/usr/local/tomcat/webapps/zmanage/css',
        force: true,
        flatten: true,
      },
    ]),
  ],
};
