const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');

const webpack = require('webpack');

const path = require('path');
const commonPaths = require('./paths');

module.exports = {
  cache: {
    type: 'filesystem',
  },
  stats: { warnings: false },
  entry: commonPaths.entryPath,
  output: {
    publicPath: '/',
    path: commonPaths.outputPath,
    clean: true,
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: [
          {
            loader: require.resolve('babel-loader'),
            options: {
              plugins: [require.resolve('react-refresh/babel')].filter(Boolean),
            },
          },
        ],
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          'style-loader',
          'css-loader',
          'resolve-url-loader',
          {
            loader: 'sass-loader',
            options: {
              implementation: require.resolve('sass'),
              sassOptions: {
                fiber: false,
                includePaths: [
                  path.resolve(__dirname, '../node_modules/*'),
                  path.resolve(__dirname, '../src/components/*'),
                  path.resolve(__dirname, '../src/pages/*'),
                ],
              },
              sourceMap: true,
            },
          },
        ],
      },
    ],
  },
  mode: 'development',
  devtool: 'eval-source-map',
  devServer: {
    liveReload: false,
    static: [
      {
        directory: commonPaths.outputPath,
        watch: true,
      },
    ],
    historyApiFallback: true,
    compress: true,
    hot: true,
    https: true,
    open: true,
    host: '0.0.0.0',
    port: 8095,
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    proxy: {
      '/ec/wapi': {
        target: 'https://connector.zsdevel.net', // working
        pathRewrite: { '^/ec': '' },

        // target: 'https://connector.zspreview.net', // not working
        
        // target: 'https://*************', // working
        // pathRewrite: { '^/ec': '' },

        // target: 'https://***********', // working
        // target: 'https://localhost:8443', // local tomcat
        secure: false,
        
        // changeOrigin: true,
        // cookieDomainRewrite: 'localhost',
      },
      '/ec/bac-adminsso.do': {
        target: 'https://connector.zsdevel.net', // working
        pathRewrite: { '^/ec': '' },

        // target: 'https://connector.zspreview.net', // not working

        // target: 'https://*************', // working
        // pathRewrite: { '^/ec': '' },
        
        // target: 'https://***********', // working
        // target: 'https://localhost:8443', // local tomcat
        secure: false,
        
        // changeOrigin: true,
        // cookieDomainRewrite: 'localhost',
      },
      '/ec/images': {
        // target: 'https://connector.zspreview.net', // not working
        target: 'https://localhost:8443',
        // target: 'https://localhost:8443', // local tomcat
        // target: 'https://0.0.0.0:8443',
        secure: false,
        // changeOrigin: true,
        // cookieDomainRewrite: 'localhost',
      },
      '/images': {
        // target: 'https://connector.zspreview.net', // not working
        target: 'https://localhost:8443/ec',
        // target: 'https://localhost:8443', // local tomcat
        // target: 'https://0.0.0.0:8443',
        secure: false,
        // changeOrigin: true,
        // cookieDomainRewrite: 'localhost',
      },
    },
    headers: {
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': false,
    },
    devMiddleware: {
      writeToDisk: true,
    },
  },
  plugins: [
    new webpack.ProgressPlugin((percentage, message, ...args) => {
      // eslint-disable-next-line no-console
      console.log(percentage, message, ...args);
    }),
    new ReactRefreshWebpackPlugin(),
  ],
};
