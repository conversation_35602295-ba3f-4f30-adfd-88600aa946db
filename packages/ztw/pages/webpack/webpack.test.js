const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

const webpack = require('webpack');
const commonPaths = require('./paths');

module.exports = {
  cache: {
    type: 'filesystem',
  },
  stats: { warnings: false },
  entry: commonPaths.entryPath,
  output: {
    publicPath: '/',
    path: commonPaths.outputPath,
    // filename: '[name].[contenthash].js',
    // chunkFilename: '[name].[contenthash].js',
    clean: true,
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        loader: 'babel-loader',
        options: {
          plugins: ['babel-plugin-istanbul'],
        },
      },
      {
        test: /\.(css|scss)$/,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              sourceMap: false,
              modules: true,
              camelCase: true,
              localIdentName: '[local]',
            },
          },
          'sass-loader',
        ],
      },
    ],
  },
  resolve: {
    modules: ['src', 'node_modules'],
    extensions: ['.*', '.js', '.jsx', '.css', '.scss'],
  },
  mode: 'development',
  devtool: 'eval-source-map',
  devServer: {
    liveReload: false,
    static: [
      {
        directory: commonPaths.outputPath,
        watch: true,
      },
    ],
    historyApiFallback: true,
    compress: true,
    hot: true,
    https: true,
    open: true,
    host: '0.0.0.0',
    port: 8085,
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    proxy: {
      '/ec/bac-adminsso.do': {
        target: 'https://localhost:8443',
        secure: false,
      },
      '/ec/wapi': {
        target: 'https://localhost:8443',
        // target: 'https://0.0.0.0:8443',
        secure: false,
        // changeOrigin: true,
        // cookieDomainRewrite: 'localhost',
      },
    },
    headers: {
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': false,
    },
    devMiddleware: {
      writeToDisk: true,
    },
  },
  plugins: [
    new webpack.ProgressPlugin((percentage, message, ...args) => {
      // eslint-disable-next-line no-console
      console.log(percentage, message, ...args);
    }),
    new ReactRefreshWebpackPlugin(),
  ],
};
