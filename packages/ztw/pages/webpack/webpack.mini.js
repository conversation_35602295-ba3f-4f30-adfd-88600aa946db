const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { GenerateSW } = require('workbox-webpack-plugin');
// const WebpackDynamicPublicPathPlugin = require('webpack-dynamic-public-path');
// const WebpackObfuscatorPlugin = require('webpack-obfuscator');
const UnusedWebpackPlugin = require('unused-webpack-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const path = require('path');
const commonPaths = require('./paths');

module.exports = {
  entry: {
    main: commonPaths.entryPath,
  },
  mode: 'production',
  output: {
    path: commonPaths.svnOutputPath,
    chunkFilename: `${commonPaths.jsFolder}/vendors[id].js`,
    filename: `${commonPaths.jsFolder}/react-app[id].js`,
    library: 'ReactApp',
    libraryTarget: 'var',
    clean: true,
    publicPath: '',
  },
  module: {
    rules: [
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          'style-loader',
          'css-loader',
          'resolve-url-loader',
          {
            loader: 'sass-loader',
            options: {
              implementation: require.resolve('sass'),
              sassOptions: {
                fiber: false,
                includePaths: [
                  path.resolve(__dirname, '../node_modules/*'),
                  path.resolve(__dirname, '../src/commonConnectedComponents/*'),
                  path.resolve(__dirname, '../src/components/*'),
                  path.resolve(__dirname, '../src/pages/*'),
                ],
              },
              sourceMap: true,
            },
          },
        ],
      },
      {
        test: /\.(js|jsx)$/,
        exclude: /(node_modules)/,
        // enforce: 'post',
        use: {
          // babel-loader to convert ES6 code to ES5 + and
          // Cleaning requirejs code into simple JS code, taking care of modules to load as desired
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: [],
          },
        },
      },
    ],
  },
  optimization: {
    minimize: false,
    mergeDuplicateChunks: true, // to merge chunks which contain the same modules
    splitChunks: {
      cacheGroups: {
        default: false,
        vendors: false,
        defaultVendors: {
          reuseExistingChunk: true,
        },
        // vendor chunk
        vendor: {
          // sync + async chunks
          chunks: 'all',
          // minSize: 1000 * 600,
          // import file path containing node_modules
          test: /node_modules/,
        },
      },
    },
  },
  plugins: [
    // new WebpackDynamicPublicPathPlugin({
    //   externalPublicPath: 'window.externalPublicPath',
    // }),
    new CompressionPlugin({
      cache: true,
      include: /\/includes/,
      test: /\.(js|css|html|svg)$/,
      algorithm: 'gzip',
    }),
    new CleanWebpackPlugin([commonPaths.svnOutputPath.split('/').pop()], {
      root: commonPaths.root,
    }),
    new MiniCssExtractPlugin({
      filename: `${commonPaths.cssFolder}/ecmain.css`,
      allChunks: true,
    }),
    new GenerateSW({
      clientsClaim: true,
      // navigateFallback: 'https://localhost:8443/cwp/index.html',
    }),
    // new BundleAnalyzerPlugin(),
    // new UnusedWebpackPlugin({
    //   // Source directories
    //   directories: [path.join(__dirname, '../src')],
    //   // Exclude patterns
    //   exclude: ['*.test.js'],
    //   // Root directory (optional)
    //   root: __dirname,
    // }),
  ],
  devtool: false,
};
