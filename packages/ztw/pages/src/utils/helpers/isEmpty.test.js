// tests/isEmpty.test.js

import isEmpty from './isEmpty';

describe('isEmpty function', () => {
  it('returns true for undefined', () => {
    expect(isEmpty(undefined)).toBe(true);
  });

  it('returns true for null', () => {
    expect(isEmpty(null)).toBe(true);
  });

  it('returns true for empty object', () => {
    expect(isEmpty({})).toBe(true);
  });

  it('returns true for empty string', () => {
    expect(isEmpty('')).toBe(true);
    expect(isEmpty('   ')).toBe(true); // test for trimmed string
  });

  it('returns false for non-empty object', () => {
    expect(isEmpty({ foo: 'bar' })).toBe(false);
  });

  it('returns false for non-empty string', () => {
    expect(isEmpty('hello')).toBe(false);
  });

  it('returns false for number', () => {
    expect(isEmpty(42)).toBe(false);
  });

  it('returns false for boolean', () => {
    expect(isEmpty(true)).toBe(false);
    expect(isEmpty(false)).toBe(false);
  });

  it('returns false for array', () => {
    expect(isEmpty([1, 2, 3])).toBe(false);
  });
});
