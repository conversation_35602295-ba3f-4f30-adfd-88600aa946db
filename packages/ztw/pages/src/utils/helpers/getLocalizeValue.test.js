import GetLocalizeValue from './getLocalizeValue';
import i18n from '../i18n';

jest.mock('../i18n', () => ({
  t: jest.fn(),
}));

describe('GetLocalizeValue', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return translated value when input is a string', () => {
    const value = 'test';
    const expectedTranslation = 'translated test';
    i18n.t.mockReturnValue(expectedTranslation);

    const result = GetLocalizeValue(value);

    expect(result).toBe(expectedTranslation);
    expect(i18n.t).toHaveBeenCalledTimes(1);
    expect(i18n.t).toHaveBeenCalledWith(value);
  });

  it('should return translated array when input is an array', () => {
    const value = ['test1', 'test2'];
    const expectedTranslations = ['translated test1', 'translated test2'];
    i18n.t.mockImplementation((el) => `translated ${el}`);

    const result = GetLocalizeValue(value);

    expect(result).toEqual(expectedTranslations);
    expect(i18n.t).toHaveBeenCalledTimes(value.length);
    value.forEach((el, index) => {
      expect(i18n.t).toHaveBeenNthCalledWith(index + 1, el);
    });
  });

  it('should return translated array when input is an array with non-string values', () => {
    const value = [1, 'test2', null, undefined];
    const expectedTranslations = ['translated 1', 'translated test2', 'translated null', 'translated undefined'];
    i18n.t.mockImplementation((el) => `translated ${el}`);

    const result = GetLocalizeValue(value);

    expect(result).toEqual(expectedTranslations);
    expect(i18n.t).toHaveBeenCalledTimes(value.length);
    value.forEach((el, index) => {
      expect(i18n.t).toHaveBeenNthCalledWith(index + 1, el);
    });
  });
});
