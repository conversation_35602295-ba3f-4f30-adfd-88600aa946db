import adjustHoursToMeetInterval from './adjustHoursToMeetInterval';

describe('adjustHoursToMeetInterval', () => {
  it('should return an empty object when interval is greater than or equal to real time interval', () => {
    const result = adjustHoursToMeetInterval(1, 0, 'AM', 2, 0, 'PM', 'FROM', 780);
    expect(result).toEqual({});
  });

  it('should adjust time based on FROM when baseValue is FROM', () => {
    const result = adjustHoursToMeetInterval(1, 0, 'AM', 1, 0, 'AM', 'FROM', 120);
    expect(result).toEqual({ hh: 3, mm: 0, period: 'AM' });
  });

  it('should adjust time based on TO when baseValue is TO', () => {
    const result = adjustHoursToMeetInterval(11, 0, 'AM', 11, 0, 'AM', 'TO', 120);
    expect(result).toEqual({ hh: 9, mm: 0, period: 'AM' });
  });

  it('should handle edge case where toMinutes is less than fromMinutes', () => {
    const result = adjustHoursToMeetInterval(11, 22, 'AM', 11, 11, 'AM', 'TO', 1500);
    expect(result).toEqual({ hh: 10, mm: 11, period: 'AM' });
  });

  it('should use default interval when interval is not provided', () => {
    const result = adjustHoursToMeetInterval(1, 0, 'AM', 1, 1, 'AM', 'FROM');
    expect(result).toEqual({ hh: 2, mm: 0, period: 'AM' });
  });
});
