const getAvgGeoLoc = (coords) => {
  if (coords.length === 1) {
    return coords[0];
  }
  
  let x = 0.0;
  let y = 0.0;
  let z = 0.0;

  coords.forEach((coord) => {
    const latitude = coord.latitude * Math.PI / 180;
    const longitude = coord.longitude * Math.PI / 180;

    x += Math.cos(latitude) * Math.cos(longitude);
    y += Math.cos(latitude) * Math.sin(longitude);
    z += Math.sin(latitude);
  });

  const total = coords.length;
  
  x /= total;
  y /= total;
  z /= total;
  
  const centralLongitude = Math.atan2(y, x);
  const centralSquareRoot = Math.sqrt(x * x + y * y);
  const centralLatitude = Math.atan2(z, centralSquareRoot);
  
  return {
    latitude: centralLatitude * 180 / Math.PI,
    longitude: centralLongitude * 180 / Math.PI,
  };
};

export default getAvgGeoLoc;
