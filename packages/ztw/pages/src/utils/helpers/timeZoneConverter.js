const TIME_ZONES = [{
  uid: 0, index: 0, name: 'NOT_SPECIFIED', parent: 'NONE', tz: 'NOT_SPECIFIED', utc: 'NOT_SPECIFIED', code: 'NOT_SPECIFIED',
},
{
  uid: 1, index: 0, name: 'GMT-12:00 Dateline', parent: 'NONE', tz: 'GMT-12:00', utc: 'UTC+12:00', code: 'GMT_12_00_DATELINE',
},
{
  uid: 2, index: 2, name: 'GMT-11:00 Samoa', parent: 'NONE', tz: 'GMT-11:00', utc: 'UTC+11:00', code: 'GMT_11_00_SAMOA',
},
{
  uid: 3, index: 4, name: 'GMT-10:00 U.S. Hawaiian Time', parent: 'NONE', tz: 'GMT-10:00', utc: 'UTC+10:00', code: 'GMT_10_00_US_HAWAIIAN_TIME',
},
{
  uid: 4, index: 5, name: 'GMT-09:30 Marquesas', parent: 'NONE', tz: 'GMT-09:30', utc: 'UTC+09:30', code: 'GMT_09_30_MARQUESAS',
},
{
  uid: 5, index: 6, name: 'GMT-09:00 U.S. Alaska Time', parent: 'NONE', tz: 'GMT-09:00', utc: 'UTC+09:00', code: 'GMT_09_00_US_ALASKA_TIME',
},
{
  uid: 6, index: 7, name: 'GMT-08:30 Pitcarn', parent: 'NONE', tz: 'GMT-08:30', utc: 'UTC+08:30', code: 'GMT_08_30_PITCARN',
},
{
  uid: 7, index: 8, name: 'GMT-08:00 Pacific Time', parent: 'NONE', tz: 'GMT-08:00', utc: 'UTC+08:00', code: 'GMT_08_00_PACIFIC_TIME',
},
{
  uid: 8, index: 10, name: 'GMT-07:00 U.S. Mountain Time', parent: 'NONE', tz: 'GMT-07:00', utc: 'UTC+07:00', code: 'GMT_07_00_US_MOUNTAIN_TIME',
},
{
  uid: 9, index: 10, name: 'GMT-07:00 U.S. Mountain Time (Arizona)', parent: 'NONE', tz: 'GMT-07:00', utc: 'UTC+07:00', code: 'GMT_07_00_US_MOUNTAIN_TIME_ARIZONA',
},
{
  uid: 10, index: 12, name: 'GMT-06:00 U.S. Central Time', parent: 'NONE', tz: 'GMT-06:00', utc: 'UTC+06:00', code: 'GMT_06_00_US_CENTRAL_TIME',
},
{
  uid: 11, index: 12, name: 'GMT-06:00 Mexico', parent: 'NONE', tz: 'GMT-06:00', utc: 'UTC+06:00', code: 'GMT_06_00_MEXICO',
},
{
  uid: 12, index: 14, name: 'GMT-05:00 U.S. Eastern Time', parent: 'NONE', tz: 'GMT-05:00', utc: 'UTC+05:00', code: 'GMT_05_00_US_EASTERN_TIME',
},
{
  uid: 13, index: 14, name: 'GMT-05:00 U.S. Eastern Time (Indiana)', parent: 'NONE', tz: 'GMT-05:00', utc: 'UTC+05:00', code: 'GMT_05_00_US_EASTERN_TIME_INDIANA',
},
{
  uid: 14, index: 14, name: 'GMT-05:00 Columbia, Peru, South America', parent: 'NONE', tz: 'GMT-05:00', utc: 'UTC+05:00', code: 'GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA',
},
{
  uid: 15, index: 16, name: 'GMT-04:00 Atlantic Time', parent: 'NONE', tz: 'GMT-04:00', utc: 'UTC+04:00', code: 'GMT_04_00_ATLANTIC_TIME',
},
{
  uid: 16, index: 17, name: 'GMT-03:30 Newfoundland, Canada', parent: 'NONE', tz: 'GMT-03:30', utc: 'UTC+03:30', code: 'GMT_03_30_NEWFOUNDLAND_CANADA',
},
{
  uid: 17, index: 18, name: 'GMT-03:00 Argentina', parent: 'NONE', tz: 'GMT-03:00', utc: 'UTC+03:00', code: 'GMT_03_00_ARGENTINA',
},
{
  uid: 18, index: 18, name: 'GMT-03:00 Brazil', parent: 'NONE', tz: 'GMT-03:00', utc: 'UTC+03:00', code: 'GMT_03_00_BRAZIL',
},
{
  uid: 19, index: 20, name: 'GMT-02:00 Mid-Atlantic', parent: 'NONE', tz: 'GMT-02:00', utc: 'UTC+02:00', code: 'GMT_02_00_MID_ATLANTIC',
},
{
  uid: 20, index: 22, name: 'GMT-01:00 Azores', parent: 'NONE', tz: 'GMT-01:00', utc: 'UTC+01:00', code: 'GMT_01_00_AZORES',
},
{
  uid: 21, index: 24, name: 'GMT U.K., Spain', parent: 'NONE', tz: 'GMT', utc: 'UTC', code: 'GMT',
},
{
  uid: 22, index: 26, name: 'GMT+01:00 Western Europe', parent: 'NONE', tz: 'GMT+01:00', utc: 'UTC-01:00', code: 'GMT_01_00_WESTERN_EUROPE_GMT_01_00',
},
{
  uid: 23, index: 28, name: 'GMT+02:00 Eastern Europe', parent: 'NONE', tz: 'GMT+02:00', utc: 'UTC-02:00', code: 'GMT_02_00_EASTERN_EUROPE_GMT_02_00',
},
{
  uid: 24, index: 28, name: 'GMT+02:00 Egypt', parent: 'NONE', tz: 'GMT+02:00', utc: 'UTC-02:00', code: 'GMT_02_00_EGYPT_GMT_02_00',
},
{
  uid: 25, index: 28, name: 'GMT+02:00 Israel', parent: 'NONE', tz: 'GMT+02:00', utc: 'UTC-02:00', code: 'GMT_02_00_ISRAEL_GMT_02_00',
},
{
  uid: 26, index: 30, name: 'GMT+03:00 Russia', parent: 'NONE', tz: 'GMT+03:00', utc: 'UTC-03:00', code: 'GMT_03_00_RUSSIA_GMT_03_00',
},
{
  uid: 27, index: 30, name: 'GMT+03:00 Saudi Arabia', parent: 'NONE', tz: 'GMT+03:00', utc: 'UTC-03:00', code: 'GMT_03_00_SAUDI_ARABIA_GMT_03_00',
},
{
  uid: 28, index: 31, name: 'GMT+03:30 Iran', parent: 'NONE', tz: 'GMT+03:30', utc: 'UTC-03:30', code: 'GMT_03_30_IRAN_GMT_03_30',
},
{
  uid: 29, index: 32, name: 'GMT+04:00 Arabian', parent: 'NONE', tz: 'GMT+04:00', utc: 'UTC-04:00', code: 'GMT_04_00_ARABIAN_GMT_04_00',
},
{
  uid: 30, index: 33, name: 'GMT+04:30 Afghanistan', parent: 'NONE', tz: 'GMT+04:30', utc: 'UTC-04:30', code: 'GMT_04_30_AFGHANISTAN_GMT_04_30',
},
{
  uid: 31, index: 34, name: 'GMT+05:00 Pakistan, West Asia', parent: 'NONE', tz: 'GMT+05:00', utc: 'UTC-05:00', code: 'GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00',
},
{
  uid: 32, index: 35, name: 'GMT+05:30 India', parent: 'NONE', tz: 'GMT+05:30', utc: 'UTC-05:30', code: 'GMT_05_30_INDIA_GMT_05_30',
},
{
  uid: 33, index: 36, name: 'GMT+06:00 Bangladesh, Central Asia', parent: 'NONE', tz: 'GMT+06:00', utc: 'UTC-06:00', code: 'GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00',
},
{
  uid: 34, index: 37, name: 'GMT+06:30 Burma', parent: 'NONE', tz: 'GMT+06:30', utc: 'UTC-06:30', code: 'GMT_06_30_BURMA_GMT_06_30',
},
{
  uid: 35, index: 38, name: 'GMT+07:00 Bangkok, Hanoi, Jakarta', parent: 'NONE', tz: 'GMT+07:00', utc: 'UTC-07:00', code: 'GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00',
},
{
  uid: 36, index: 29, name: 'GMT+08:00 China, Taiwan', parent: 'NONE', tz: 'GMT+08:00', utc: 'UTC-08:00', code: 'GMT_08_00_CHINA_TAIWAN_GMT_08_00',
},
{
  uid: 37, index: 29, name: 'GMT+08:00 Singapore', parent: 'NONE', tz: 'GMT+08:00', utc: 'UTC-08:00', code: 'GMT_08_00_SINGAPORE_GMT_08_00',
},
{
  uid: 38, index: 29, name: 'GMT+08:00 Australia (WT)', parent: 'NONE', tz: 'GMT+08:00', utc: 'UTC-08:00', code: 'GMT_08_00_AUSTRALIA_WT_GMT_08_00',
},
{
  uid: 39, index: 27, name: 'GMT+09:00 Japan', parent: 'NONE', tz: 'GMT+09:00', utc: 'UTC-09:00', code: 'GMT_09_00_JAPAN_GMT_09_00',
},
{
  uid: 40, index: 27, name: 'GMT+09:00 Korea', parent: 'NONE', tz: 'GMT+09:00', utc: 'UTC-09:00', code: 'GMT_09_00_KOREA_GMT_09_00',
},
{
  uid: 41, index: 25, name: 'GMT+09:30 Australia (CT)', parent: 'NONE', tz: 'GMT+09:30', utc: 'UTC-09:30', code: 'GMT_09_30_AUSTRALIA_CT_GMT_09_30',
},
{
  uid: 42, index: 23, name: 'GMT+10:00 Australia (ET)', parent: 'NONE', tz: 'GMT+10:00', utc: 'UTC-10:00', code: 'GMT_10_00_AUSTRALIA_ET_GMT_10_00',
},
{
  uid: 43, index: 21, name: 'GMT+10:30 Australia (Lord Howe)', parent: 'NONE', tz: 'GMT+10:30', utc: 'UTC-10:30', code: 'GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30',
},
{
  uid: 44, index: 13, name: 'GMT+11:00 Central Pacific', parent: 'NONE', tz: 'GMT+11:00', utc: 'UTC-11:00', code: 'GMT_11_00_CENTRAL_PACIFIC_GMT_11_00',
},
{
  uid: 45, index: 11, name: 'GMT+11:30 Norfolk Islands', parent: 'NONE', tz: 'GMT+11:30', utc: 'UTC-11:30', code: 'GMT_11_30_NORFOLK_ISLANDS_GMT_11_30',
},
{
  uid: 46, index: 9, name: 'GMT+12:00 Fiji, New Zealand', parent: 'NONE', tz: 'GMT+12:00', utc: 'UTC-12:00', code: 'GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00',
},
{
  uid: 47, index: 33, name: 'Afghanistan', parent: 'AFGHANISTAN', tz: 'Asia/Kabul', utc: 'Asia/Kabul', code: 'AFGHANISTAN_ASIA_KABUL',
},
{
  uid: 48, index: 30, name: 'Aland Islands', parent: 'ALAND_ISLANDS', tz: 'Europe/Mariehamn', utc: 'Europe/Mariehamn', code: 'ALAND_ISLANDS_EUROPE_MARIEHAMN',
},
{
  uid: 49, index: 28, name: 'Albania', parent: 'ALBANIA', tz: 'Europe/Tirane', utc: 'Europe/Tirane', code: 'ALBANIA_EUROPE_TIRANE',
},
{
  uid: 50, index: 26, name: 'Algeria', parent: 'ALGERIA', tz: 'Africa/Algiers', utc: 'Africa/Algiers', code: 'ALGERIA_AFRICA_ALGIERS',
},
{
  uid: 51, index: 2, name: 'American Samoa', parent: 'AMERICAN_SAMOA', tz: 'Pacific/Pago Pago', utc: 'Pacific/Pago_Pago', code: 'AMERICAN_SAMOA_PACIFIC_PAGO_PAGO',
},
{
  uid: 52, index: 28, name: 'Andorra', parent: 'ANDORRA', tz: 'Europe/Andorra', utc: 'Europe/Andorra', code: 'ANDORRA_EUROPE_ANDORRA',
},
{
  uid: 53, index: 26, name: 'Angola', parent: 'ANGOLA', tz: 'Africa/Luanda', utc: 'Africa/Luanda', code: 'ANGOLA_AFRICA_LUANDA',
},
{
  uid: 54, index: 16, name: 'Anguilla', parent: 'ANGUILLA', tz: 'America/Anguilla', utc: 'America/Anguilla', code: 'ANGUILLA_AMERICA_ANGUILLA',
},
{
  uid: 55, index: 29, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Casey', utc: 'Antarctica/Casey', code: 'ANTARCTICA_CASEY',
},
{
  uid: 56, index: 38, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Davis', utc: 'Antarctica/Davis', code: 'ANTARCTICA_DAVIS',
},
{
  uid: 57, index: 23, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/DumontDUrville', utc: 'Antarctica/DumontDUrville', code: 'ANTARCTICA_DUMONTDURVILLE',
},
{
  uid: 58, index: 34, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Mawson', utc: 'Antarctica/Mawson', code: 'ANTARCTICA_MAWSON',
},
{
  uid: 59, index: 9, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/McMurdo', utc: 'Antarctica/McMurdo', code: 'ANTARCTICA_MCMURDO',
},
{
  uid: 60, index: 16, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Palmer', utc: 'Antarctica/Palmer', code: 'ANTARCTICA_PALMER',
},
{
  uid: 61, index: 18, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Rothera', utc: 'Antarctica/Rothera', code: 'ANTARCTICA_ROTHERA',
},
{
  uid: 62, index: 9, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/South Pole', utc: 'Antarctica/South_Pole', code: 'ANTARCTICA_SOUTH_POLE',
},
{
  uid: 63, index: 30, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Syowa', utc: 'Antarctica/Syowa', code: 'ANTARCTICA_SYOWA',
},
{
  uid: 64, index: 36, name: 'Antarctica', parent: 'ANTARCTICA', tz: 'Antarctica/Vostok', utc: 'Antarctica/Vostok', code: 'ANTARCTICA_VOSTOK',
},
{
  uid: 65, index: 16, name: 'Antigua and Barbuda', parent: 'ANTIGUA_AND_BARBUDA', tz: 'America/Antigua', utc: 'America/Antigua', code: 'ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA',
},
{
  uid: 66, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Buenos Aires', utc: 'America/Argentina/Buenos_Aires', code: 'ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES',
},
{
  uid: 67, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Catamarca', utc: 'America/Argentina/Catamarca', code: 'ARGENTINA_AMERICA_ARGENTINA_CATAMARCA',
},
{
  uid: 68, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Cordoba', utc: 'America/Argentina/Cordoba', code: 'ARGENTINA_AMERICA_ARGENTINA_CORDOBA',
},
{
  uid: 69, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Jujuy', utc: 'America/Argentina/Jujuy', code: 'ARGENTINA_AMERICA_ARGENTINA_JUJUY',
},
{
  uid: 70, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/La Rioja', utc: 'America/Argentina/La_Rioja', code: 'ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA',
},
{
  uid: 71, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Mendoza', utc: 'America/Argentina/Mendoza', code: 'ARGENTINA_AMERICA_ARGENTINA_MENDOZA',
},
{
  uid: 72, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Rio Gallegos', utc: 'America/Argentina/Rio_Gallegos', code: 'ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS',
},
{
  uid: 73, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/San Juan', utc: 'America/Argentina/San_Juan', code: 'ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN',
},
{
  uid: 74, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Tucuman', utc: 'America/Argentina/Tucuman', code: 'ARGENTINA_AMERICA_ARGENTINA_TUCUMAN',
},
{
  uid: 75, index: 18, name: 'Argentina', parent: 'ARGENTINA', tz: 'America/Argentina/Ushuaia', utc: 'America/Argentina/Ushuaia', code: 'ARGENTINA_AMERICA_ARGENTINA_USHUAIA',
},
{
  uid: 76, index: 32, name: 'Armenia', parent: 'ARMENIA', tz: 'Asia/Yerevan', utc: 'Asia/Yerevan', code: 'ARMENIA_ASIA_YEREVAN',
},
{
  uid: 77, index: 16, name: 'Aruba', parent: 'ARUBA', tz: 'America/Aruba', utc: 'America/Aruba', code: 'ARUBA_AMERICA_ARUBA',
},
{
  uid: 78, index: 25, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Adelaide', utc: 'Australia/Adelaide', code: 'AUSTRALIA_ADELAIDE',
},
{
  uid: 79, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Brisbane', utc: 'Australia/Brisbane', code: 'AUSTRALIA_BRISBANE',
},
{
  uid: 80, index: 25, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Broken Hill', utc: 'Australia/Broken_Hill', code: 'AUSTRALIA_BROKEN_HILL',
},
{
  uid: 81, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Currie', utc: 'Australia/Currie', code: 'AUSTRALIA_CURRIE',
},
{
  uid: 82, index: 25, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Darwin', utc: 'Australia/Darwin', code: 'AUSTRALIA_DARWIN',
},
{
  uid: 83, index: 29, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Eucla', utc: 'Australia/Eucla', code: 'AUSTRALIA_EUCLA',
},
{
  uid: 84, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Hobart', utc: 'Australia/Hobart', code: 'AUSTRALIA_HOBART',
},
{
  uid: 85, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Lindeman', utc: 'Australia/Lindeman', code: 'AUSTRALIA_LINDEMAN',
},
{
  uid: 86, index: 21, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Lord Howe', utc: 'Australia/Lord_Howe', code: 'AUSTRALIA_LORD_HOWE',
},
{
  uid: 87, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Melbourne', utc: 'Australia/Melbourne', code: 'AUSTRALIA_MELBOURNE',
},
{
  uid: 88, index: 29, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Perth', utc: 'Australia/Perth', code: 'AUSTRALIA_PERTH',
},
{
  uid: 89, index: 23, name: 'Australia', parent: 'AUSTRALIA', tz: 'Australia/Sydney', utc: 'Australia/Sydney', code: 'AUSTRALIA_SYDNEY',
},
{
  uid: 90, index: 28, name: 'Austria', parent: 'AUSTRIA', tz: 'Europe/Vienna', utc: 'Europe/Vienna', code: 'AUSTRIA_EUROPE_VIENNA',
},
{
  uid: 91, index: 34, name: 'Azerbaijan', parent: 'AZERBAIJAN', tz: 'Asia/Baku', utc: 'Asia/Baku', code: 'AZERBAIJAN_ASIA_BAKU',
},
{
  uid: 92, index: 16, name: 'Bahamas', parent: 'BAHAMAS', tz: 'America/Nassau', utc: 'America/Nassau', code: 'BAHAMAS_AMERICA_NASSAU',
},
{
  uid: 93, index: 30, name: 'Bahrain', parent: 'BAHRAIN', tz: 'Asia/Bahrain', utc: 'Asia/Bahrain', code: 'BAHRAIN_ASIA_BAHRAIN',
},
{
  uid: 94, index: 36, name: 'Bangladesh', parent: 'BANGLADESH', tz: 'Asia/Dhaka', utc: 'Asia/Dhaka', code: 'BANGLADESH_ASIA_DHAKA',
},
{
  uid: 95, index: 16, name: 'Barbados', parent: 'BARBADOS', tz: 'America/Barbados', utc: 'America/Barbados', code: 'BARBADOS_AMERICA_BARBADOS',
},
{
  uid: 96, index: 30, name: 'Belarus', parent: 'BELARUS', tz: 'Europe/Minsk', utc: 'Europe/Minsk', code: 'BELARUS_EUROPE_MINSK',
},
{
  uid: 97, index: 28, name: 'Belgium', parent: 'BELGIUM', tz: 'Europe/Brussels', utc: 'Europe/Brussels', code: 'BELGIUM_EUROPE_BRUSSELS',
},
{
  uid: 98, index: 12, name: 'Belize', parent: 'BELIZE', tz: 'America/Belize', utc: 'America/Belize', code: 'BELIZE_AMERICA_BELIZE',
},
{
  uid: 99, index: 26, name: 'Benin', parent: 'BENIN', tz: 'Africa/Porto-Novo', utc: 'Africa/Porto-Novo', code: 'BENIN_AFRICA_PORTO_NOVO',
},
{
  uid: 100, index: 18, name: 'Bermuda', parent: 'BERMUDA', tz: 'Atlantic/Bermuda', utc: 'Atlantic/Bermuda', code: 'BERMUDA_ATLANTIC_BERMUDA',
},
{
  uid: 101, index: 36, name: 'Bhutan', parent: 'BHUTAN', tz: 'Asia/Thimphu', utc: 'Asia/Thimphu', code: 'BHUTAN_ASIA_THIMPHU',
},
{
  uid: 102, index: 16, name: 'Bolivia', parent: 'BOLIVIA', tz: 'America/La Paz', utc: 'America/La_Paz', code: 'BOLIVIA_AMERICA_LA_PAZ',
},
{
  uid: 103, index: 28, name: 'Bosnia and Herzegovina', parent: 'BOSNIA_AND_HERZEGOVINA', tz: 'Europe/Sarajevo', utc: 'Europe/Sarajevo', code: 'BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO',
},
{
  uid: 104, index: 28, name: 'Botswana', parent: 'BOTSWANA', tz: 'Africa/Gaborone', utc: 'Africa/Gaborone', code: 'BOTSWANA_AFRICA_GABORONE',
},
{
  uid: 105, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Araguaina', utc: 'America/Araguaina', code: 'BRAZIL_AMERICA_ARAGUAINA',
},
{
  uid: 106, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Bahia', utc: 'America/Bahia', code: 'BRAZIL_AMERICA_BAHIA',
},
{
  uid: 107, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Belem', utc: 'America/Belem', code: 'BRAZIL_AMERICA_BELEM',
},
{
  uid: 108, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Boa Vista', utc: 'America/Boa_Vista', code: 'BRAZIL_AMERICA_BOA_VISTA',
},
{
  uid: 109, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Campo Grande', utc: 'America/Campo_Grande', code: 'BRAZIL_AMERICA_CAMPO_GRANDE',
},
{
  uid: 110, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Cuiaba', utc: 'America/Cuiaba', code: 'BRAZIL_AMERICA_CUIABA',
},
{
  uid: 111, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Eirunepe', utc: 'America/Eirunepe', code: 'BRAZIL_AMERICA_EIRUNEPE',
},
{
  uid: 112, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Fortaleza', utc: 'America/Fortaleza', code: 'BRAZIL_AMERICA_FORTALEZA',
},
{
  uid: 113, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Maceio', utc: 'America/Maceio', code: 'BRAZIL_AMERICA_MACEIO',
},
{
  uid: 114, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Manaus', utc: 'America/Manaus', code: 'BRAZIL_AMERICA_MANAUS',
},
{
  uid: 115, index: 20, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Noronha', utc: 'America/Noronha', code: 'BRAZIL_AMERICA_NORONHA',
},
{
  uid: 116, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Porto Velho', utc: 'America/Porto_Velho', code: 'BRAZIL_AMERICA_PORTO_VELHO',
},
{
  uid: 117, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Recife', utc: 'America/Recife', code: 'BRAZIL_AMERICA_RECIFE',
},
{
  uid: 118, index: 16, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Rio Branco', utc: 'America/Rio_Branco', code: 'BRAZIL_AMERICA_RIO_BRANCO',
},
{
  uid: 119, index: 18, name: 'Brazil', parent: 'BRAZIL', tz: 'America/Sao Paulo', utc: 'UTC+03:00', code: 'BRAZIL_AMERICA_SAO_PAULO',
},
{
  uid: 120, index: 36, name: 'British Indian Ocean Territory', parent: 'BRITISH_INDIAN_OCEAN_TERRITORY', tz: 'Indian/Chagos', utc: 'Indian/Chagos', code: 'BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS',
},
{
  uid: 121, index: 29, name: 'Brunei Darussalam', parent: 'BRUNEI_DARUSSALAM', tz: 'Asia/Brunei', utc: 'Asia/Brunei', code: 'BRUNEI_DARUSSALAM_ASIA_BRUNEI',
},
{
  uid: 122, index: 30, name: 'Bulgaria', parent: 'BULGARIA', tz: 'Europe/Sofia', utc: 'Europe/Sofia', code: 'BULGARIA_EUROPE_SOFIA',
},
{
  uid: 123, index: 24, name: 'Burkina Faso', parent: 'BURKINA_FASO', tz: 'Africa/Ouagadougou', utc: 'Africa/Ouagadougou', code: 'BURKINA_FASO_AFRICA_OUAGADOUGOU',
},
{
  uid: 124, index: 28, name: 'Burundi', parent: 'BURUNDI', tz: 'Africa/Bujumbura', utc: 'Africa/Bujumbura', code: 'BURUNDI_AFRICA_BUJUMBURA',
},
{
  uid: 125, index: 38, name: 'Cambodia', parent: 'CAMBODIA', tz: 'Asia/Phnom Penh', utc: 'Asia/Phnom_Penh', code: 'CAMBODIA_ASIA_PHNOM_PENH',
},
{
  uid: 126, index: 26, name: 'Cameroon', parent: 'CAMEROON', tz: 'Africa/Douala', utc: 'Africa/Douala', code: 'CAMEROON_AFRICA_DOUALA',
},
{
  uid: 127, index: 14, name: 'Canada', parent: 'CANADA', tz: 'America/Atikokan', utc: 'America/Atikokan', code: 'CANADA_AMERICA_ATIKOKAN',
},
{
  uid: 128, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Blanc-Sablon', utc: 'America/Blanc-Sablon', code: 'CANADA_AMERICA_BLANC_SABLON',
},
{
  uid: 129, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Cambridge Bay', utc: 'America/Cambridge_Bay', code: 'CANADA_AMERICA_CAMBRIDGE_BAY',
},
{
  uid: 130, index: 10, name: 'Canada', parent: 'CANADA', tz: 'America/Dawson Creek', utc: 'America/Dawson_Creek', code: 'CANADA_AMERICA_DAWSON_CREEK',
},
{
  uid: 131, index: 10, name: 'Canada', parent: 'CANADA', tz: 'America/Dawson', utc: 'America/Dawson', code: 'CANADA_AMERICA_DAWSON',
},
{
  uid: 132, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Edmonton', utc: 'America/Edmonton', code: 'CANADA_AMERICA_EDMONTON',
},
{
  uid: 133, index: 18, name: 'Canada', parent: 'CANADA', tz: 'America/Glace Bay', utc: 'America/Glace_Bay', code: 'CANADA_AMERICA_GLACE_BAY',
},
{
  uid: 134, index: 18, name: 'Canada', parent: 'CANADA', tz: 'America/Goose Bay', utc: 'America/Goose_Bay', code: 'CANADA_AMERICA_GOOSE_BAY',
},
{
  uid: 135, index: 18, name: 'Canada', parent: 'CANADA', tz: 'America/Halifax', utc: 'America/Halifax', code: 'CANADA_AMERICA_HALIFAX',
},
{
  uid: 136, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Inuvik', utc: 'America/Inuvik', code: 'CANADA_AMERICA_INUVIK',
},
{
  uid: 137, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Iqaluit', utc: 'America/Iqaluit', code: 'CANADA_AMERICA_IQALUIT',
},
{
  uid: 138, index: 18, name: 'Canada', parent: 'CANADA', tz: 'America/Moncton', utc: 'America/Moncton', code: 'CANADA_AMERICA_MONCTON',
},
{
  uid: 139, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Montreal', utc: 'America/Montreal', code: 'CANADA_AMERICA_MONTREAL',
},
{
  uid: 140, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Nipigon', utc: 'America/Nipigon', code: 'CANADA_AMERICA_NIPIGON',
},
{
  uid: 141, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Pangnirtung', utc: 'America/Pangnirtung', code: 'CANADA_AMERICA_PANGNIRTUNG',
},
{
  uid: 142, index: 14, name: 'Canada', parent: 'CANADA', tz: 'America/Rainy River', utc: 'America/Rainy_River', code: 'CANADA_AMERICA_RAINY_RIVER',
},
{
  uid: 143, index: 14, name: 'Canada', parent: 'CANADA', tz: 'America/Rankin Inlet', utc: 'America/Rankin_Inlet', code: 'CANADA_AMERICA_RANKIN_INLET',
},
{
  uid: 144, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Regina', utc: 'America/Regina', code: 'CANADA_AMERICA_REGINA',
},
{
  uid: 145, index: 14, name: 'Canada', parent: 'CANADA', tz: 'America/Resolute', utc: 'America/Resolute', code: 'CANADA_AMERICA_RESOLUTE',
},
{
  uid: 146, index: 19, name: 'Canada', parent: 'CANADA', tz: 'America/St Johns', utc: 'America/St_Johns', code: 'CANADA_AMERICA_ST_JOHNS',
},
{
  uid: 147, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Swift Current', utc: 'America/Swift_Current', code: 'CANADA_AMERICA_SWIFT_CURRENT',
},
{
  uid: 148, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Thunder Bay', utc: 'America/Thunder_Bay', code: 'CANADA_AMERICA_THUNDER_BAY',
},
{
  uid: 149, index: 16, name: 'Canada', parent: 'CANADA', tz: 'America/Toronto', utc: 'America/Toronto', code: 'CANADA_AMERICA_TORONTO',
},
{
  uid: 150, index: 10, name: 'Canada', parent: 'CANADA', tz: 'America/Vancouver', utc: 'America/Vancouver', code: 'CANADA_AMERICA_VANCOUVER',
},
{
  uid: 151, index: 10, name: 'Canada', parent: 'CANADA', tz: 'America/Whitehorse', utc: 'America/Whitehorse', code: 'CANADA_AMERICA_WHITEHORSE',
},
{
  uid: 152, index: 14, name: 'Canada', parent: 'CANADA', tz: 'America/Winnipeg', utc: 'America/Winnipeg', code: 'CANADA_AMERICA_WINNIPEG',
},
{
  uid: 153, index: 12, name: 'Canada', parent: 'CANADA', tz: 'America/Yellowknife', utc: 'America/Yellowknife', code: 'CANADA_AMERICA_YELLOWKNIFE',
},
{
  uid: 154, index: 22, name: 'Cape Verde', parent: 'CAPE_VERDE', tz: 'Atlantic/Cape Verde', utc: 'Atlantic/Cape_Verde', code: 'CAPE_VERDE_ATLANTIC_CAPE_VERDE',
},
{
  uid: 155, index: 14, name: 'Cayman Islands', parent: 'CAYMAN_ISLANDS', tz: 'America/Cayman', utc: 'America/Cayman', code: 'CAYMAN_ISLANDS_AMERICA_CAYMAN',
},
{
  uid: 156, index: 26, name: 'Central African Republic', parent: 'CENTRAL_AFRICAN_REPUBLIC', tz: 'Africa/Bangui', utc: 'Africa/Bangui', code: 'CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI',
},
{
  uid: 157, index: 26, name: 'Chad', parent: 'CHAD', tz: 'Africa/Ndjamena', utc: 'Africa/Ndjamena', code: 'CHAD_AFRICA_NDJAMENA',
},
{
  uid: 158, index: 16, name: 'Chile', parent: 'CHILE', tz: 'America/Santiago', utc: 'America/Santiago', code: 'CHILE_AMERICA_SANTIAGO',
},
{
  uid: 159, index: 12, name: 'Chile', parent: 'CHILE', tz: 'Pacific/Easter', utc: 'Pacific/Easter', code: 'CHILE_PACIFIC_EASTER',
},
{
  uid: 160, index: 29, name: 'China', parent: 'CHINA', tz: 'Asia/Chongqing', utc: 'Asia/Chongqing', code: 'CHINA_ASIA_CHONGQING',
},
{
  uid: 161, index: 29, name: 'China', parent: 'CHINA', tz: 'Asia/Harbin', utc: 'Asia/Harbin', code: 'CHINA_ASIA_HARBIN',
},
{
  uid: 162, index: 29, name: 'China', parent: 'CHINA', tz: 'Asia/Kashgar', utc: 'Asia/Kashgar', code: 'CHINA_ASIA_KASHGAR',
},
{
  uid: 163, index: 29, name: 'China', parent: 'CHINA', tz: 'Asia/Shanghai', utc: 'Asia/Shanghai', code: 'CHINA_ASIA_SHANGHAI',
},
{
  uid: 164, index: 29, name: 'China', parent: 'CHINA', tz: 'Asia/Urumqi', utc: 'Asia/Urumqi', code: 'CHINA_ASIA_URUMQI',
},
{
  uid: 165, index: 38, name: 'Christmas Island', parent: 'CHRISTMAS_ISLAND', tz: 'Indian/Christmas', utc: 'Indian/Christmas', code: 'CHRISTMAS_ISLAND_INDIAN_CHRISTMAS',
},
{
  uid: 166, index: 37, name: 'Cocos (Keeling) Islands', parent: 'COCOS_KEELING_ISLANDS', tz: 'Indian/Cocos', utc: 'Indian/Cocos', code: 'COCOS_KEELING_ISLANDS_INDIAN_COCOS',
},
{
  uid: 167, index: 14, name: 'Colombia', parent: 'COLOMBIA', tz: 'America/Bogota', utc: 'America/Bogota', code: 'COLOMBIA_AMERICA_BOGOTA',
},
{
  uid: 168, index: 30, name: 'Comoros', parent: 'COMOROS', tz: 'Indian/Comoro', utc: 'Indian/Comoro', code: 'COMOROS_INDIAN_COMORO',
},
{
  uid: 169, index: 26, name: 'Democratic Republic of Congo (Congo-Kinshasa)', parent: 'DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA', tz: 'Africa/Kinshasa', utc: 'Africa/Kinshasa', code: 'DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA_AFRICA_KINSHASA',
},
{
  uid: 170, index: 28, name: 'Democratic Republic of Congo (Congo-Kinshasa)', parent: 'DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA', tz: 'Africa/Lubumbashi', utc: 'Africa/Lubumbashi', code: 'DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA_AFRICA_LUBUMBASHI',
},
{
  uid: 171, index: 26, name: 'Congo (Congo-Brazzaville)', parent: 'CONGO_CONGO_BRAZZAVILLE', tz: 'Africa/Brazzaville', utc: 'Africa/Brazzaville', code: 'CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE',
},
{
  uid: 172, index: 4, name: 'Cook Islands', parent: 'COOK_ISLANDS', tz: 'Pacific/Rarotonga', utc: 'Pacific/Rarotonga', code: 'COOK_ISLANDS_PACIFIC_RAROTONGA',
},
{
  uid: 173, index: 12, name: 'Costa Rica', parent: 'COSTA_RICA', tz: 'America/Costa Rica', utc: 'America/Costa_Rica', code: 'COSTA_RICA_AMERICA_COSTA_RICA',
},
{
  uid: 174, index: 24, name: "Cote d'Ivoire", parent: 'COTE_DIVOIRE', tz: 'Africa/Abidjan', utc: 'Africa/Abidjan', code: 'COTE_DIVOIRE_AFRICA_ABIDJAN',
},
{
  uid: 175, index: 28, name: 'Croatia', parent: 'CROATIA', tz: 'Europe/Zagreb', utc: 'Europe/Zagreb', code: 'CROATIA_EUROPE_ZAGREB',
},
{
  uid: 176, index: 16, name: 'Cuba', parent: 'CUBA', tz: 'America/Havana', utc: 'America/Havana', code: 'CUBA_AMERICA_HAVANA',
},
{
  uid: 177, index: 30, name: 'Cyprus', parent: 'CYPRUS', tz: 'Asia/Nicosia', utc: 'Asia/Nicosia', code: 'CYPRUS_ASIA_NICOSIA',
},
{
  uid: 178, index: 28, name: 'Czech Republic', parent: 'CZECH_REPUBLIC', tz: 'Europe/Prague', utc: 'Europe/Prague', code: 'CZECH_REPUBLIC_EUROPE_PRAGUE',
},
{
  uid: 179, index: 28, name: 'Denmark', parent: 'DENMARK', tz: 'Europe/Copenhagen', utc: 'Europe/Copenhagen', code: 'DENMARK_EUROPE_COPENHAGEN',
},
{
  uid: 180, index: 30, name: 'Djibouti', parent: 'DJIBOUTI', tz: 'Africa/Djibouti', utc: 'Africa/Djibouti', code: 'DJIBOUTI_AFRICA_DJIBOUTI',
},
{
  uid: 181, index: 16, name: 'Dominican Republic', parent: 'DOMINICAN_REPUBLIC', tz: 'America/Santo Domingo', utc: 'America/Santo_Domingo', code: 'DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO',
},
{
  uid: 182, index: 16, name: 'Dominica', parent: 'DOMINICA', tz: 'America/Dominica', utc: 'America/Dominica', code: 'DOMINICA_AMERICA_DOMINICA',
},
{
  uid: 183, index: 14, name: 'Ecuador', parent: 'ECUADOR', tz: 'America/Guayaquil', utc: 'America/Guayaquil', code: 'ECUADOR_AMERICA_GUAYAQUIL',
},
{
  uid: 184, index: 12, name: 'Ecuador', parent: 'ECUADOR', tz: 'Pacific/Galapagos', utc: 'Pacific/Galapagos', code: 'ECUADOR_PACIFIC_GALAPAGOS',
},
{
  uid: 185, index: 28, name: 'Egypt', parent: 'EGYPT', tz: 'Africa/Cairo', utc: 'Africa/Cairo', code: 'EGYPT_AFRICA_CAIRO',
},
{
  uid: 186, index: 12, name: 'El Salvador', parent: 'EL_SALVADOR', tz: 'America/El Salvador', utc: 'America/El_Salvador', code: 'EL_SALVADOR_AMERICA_EL_SALVADOR',
},
{
  uid: 187, index: 26, name: 'Equatorial Guinea', parent: 'EQUATORIAL_GUINEA', tz: 'Africa/Malabo', utc: 'Africa/Malabo', code: 'EQUATORIAL_GUINEA_AFRICA_MALABO',
},
{
  uid: 188, index: 30, name: 'Eritrea', parent: 'ERITREA', tz: 'Africa/Asmara', utc: 'Africa/Asmara', code: 'ERITREA_AFRICA_ASMARA',
},
{
  uid: 189, index: 30, name: 'Estonia', parent: 'ESTONIA', tz: 'Europe/Tallinn', utc: 'Europe/Tallinn', code: 'ESTONIA_EUROPE_TALLINN',
},
{
  uid: 190, index: 30, name: 'Ethiopia', parent: 'ETHIOPIA', tz: 'Africa/Addis Ababa', utc: 'Africa/Addis_Ababa', code: 'ETHIOPIA_AFRICA_ADDIS_ABABA',
},
{
  uid: 191, index: 18, name: 'Falkland Islands (Malvinas)', parent: 'FALKLAND_ISLANDS_MALVINAS', tz: 'Atlantic/Stanley', utc: 'Atlantic/Stanley', code: 'FALKLAND_ISLANDS_MALVINAS_ATLANTIC_STANLEY',
},
{
  uid: 192, index: 26, name: 'Faroe Islands', parent: 'FAROE_ISLANDS', tz: 'Atlantic/Faroe', utc: 'Atlantic/Faroe', code: 'FAROE_ISLANDS_ATLANTIC_FAROE',
},
{
  uid: 193, index: 9, name: 'Fiji', parent: 'FIJI', tz: 'Pacific/Fiji', utc: 'Pacific/Fiji', code: 'FIJI_PACIFIC_FIJI',
},
{
  uid: 194, index: 30, name: 'Finland', parent: 'FINLAND', tz: 'Europe/Helsinki', utc: 'Europe/Helsinki', code: 'FINLAND_EUROPE_HELSINKI',
},
{
  uid: 195, index: 28, name: 'France', parent: 'FRANCE', tz: 'Europe/Paris', utc: 'Europe/Paris', code: 'FRANCE_EUROPE_PARIS',
},
{
  uid: 196, index: 18, name: 'French Guiana', parent: 'FRENCH_GUIANA', tz: 'America/Cayenne', utc: 'America/Cayenne', code: 'FRENCH_GUIANA_AMERICA_CAYENNE',
},
{
  uid: 197, index: 6, name: 'French Polynesia', parent: 'FRENCH_POLYNESIA', tz: 'Pacific/Gambier', utc: 'Pacific/Gambier', code: 'FRENCH_POLYNESIA_PACIFIC_GAMBIER',
},
{
  uid: 198, index: 5, name: 'French Polynesia', parent: 'FRENCH_POLYNESIA', tz: 'Pacific/Marquesas', utc: 'Pacific/Marquesas', code: 'FRENCH_POLYNESIA_PACIFIC_MARQUESAS',
},
{
  uid: 199, index: 4, name: 'French Polynesia', parent: 'FRENCH_POLYNESIA', tz: 'Pacific/Tahiti', utc: 'Pacific/Tahiti', code: 'FRENCH_POLYNESIA_PACIFIC_TAHITI',
},
{
  uid: 200, index: 34, name: 'French Southern Territories', parent: 'FRENCH_SOUTHERN_TERRITORIES', tz: 'Indian/Kerguelen', utc: 'Indian/Kerguelen', code: 'FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN',
},
{
  uid: 201, index: 26, name: 'Gabon', parent: 'GABON', tz: 'Africa/Libreville', utc: 'Africa/Libreville', code: 'GABON_AFRICA_LIBREVILLE',
},
{
  uid: 202, index: 24, name: 'Gambia', parent: 'GAMBIA', tz: 'Africa/Banjul', utc: 'Africa/Banjul', code: 'GAMBIA_AFRICA_BANJUL',
},
{
  uid: 203, index: 32, name: 'Georgia', parent: 'GEORGIA', tz: 'Asia/Tbilisi', utc: 'Asia/Tbilisi', code: 'GEORGIA_ASIA_TBILISI',
},
{
  uid: 204, index: 28, name: 'Germany', parent: 'GERMANY', tz: 'Europe/Berlin', utc: 'Europe/Berlin', code: 'GERMANY_EUROPE_BERLIN',
},
{
  uid: 205, index: 24, name: 'Ghana', parent: 'GHANA', tz: 'Africa/Accra', utc: 'Africa/Accra', code: 'GHANA_AFRICA_ACCRA',
},
{
  uid: 206, index: 28, name: 'Gibraltar', parent: 'GIBRALTAR', tz: 'Europe/Gibraltar', utc: 'Europe/Gibraltar', code: 'GIBRALTAR_EUROPE_GIBRALTAR',
},
{
  uid: 207, index: 30, name: 'Greece', parent: 'GREECE', tz: 'Europe/Athens', utc: 'Europe/Athens', code: 'GREECE_EUROPE_ATHENS',
},
{
  uid: 208, index: 24, name: 'Greenland', parent: 'GREENLAND', tz: 'America/Danmarkshavn', utc: 'America/Danmarkshavn', code: 'GREENLAND_AMERICA_DANMARKSHAVN',
},
{
  uid: 209, index: 20, name: 'Greenland', parent: 'GREENLAND', tz: 'America/Godthab', utc: 'America/Godthab', code: 'GREENLAND_AMERICA_GODTHAB',
},
{
  uid: 210, index: 24, name: 'Greenland', parent: 'GREENLAND', tz: 'America/Scoresbysund', utc: 'America/Scoresbysund', code: 'GREENLAND_AMERICA_SCORESBYSUND',
},
{
  uid: 211, index: 18, name: 'Greenland', parent: 'GREENLAND', tz: 'America/Thule', utc: 'America/Thule', code: 'GREENLAND_AMERICA_THULE',
},
{
  uid: 212, index: 16, name: 'Grenada', parent: 'GRENADA', tz: 'America/Grenada', utc: 'America/Grenada', code: 'GRENADA_AMERICA_GRENADA',
},
{
  uid: 213, index: 16, name: 'Guadeloupe', parent: 'GUADELOUPE', tz: 'America/Guadeloupe', utc: 'America/Guadeloupe', code: 'GUADELOUPE_AMERICA_GUADELOUPE',
},
{
  uid: 214, index: 23, name: 'Guam', parent: 'GUAM', tz: 'Pacific/Guam', utc: 'Pacific/Guam', code: 'GUAM_PACIFIC_GUAM',
},
{
  uid: 215, index: 12, name: 'Guatemala', parent: 'GUATEMALA', tz: 'America/Guatemala', utc: 'America/Guatemala', code: 'GUATEMALA_AMERICA_GUATEMALA',
},
{
  uid: 216, index: 26, name: 'Guernsey', parent: 'GUERNSEY', tz: 'Europe/Guernsey', utc: 'Europe/Guernsey', code: 'GUERNSEY_EUROPE_GUERNSEY',
},
{
  uid: 217, index: 24, name: 'Guinea-Bissau', parent: 'GUINEA_BISSAU', tz: 'Africa/Bissau', utc: 'Africa/Bissau', code: 'GUINEA_BISSAU_AFRICA_BISSAU',
},
{
  uid: 218, index: 24, name: 'Guinea', parent: 'GUINEA', tz: 'Africa/Conakry', utc: 'Africa/Conakry', code: 'GUINEA_AFRICA_CONAKRY',
},
{
  uid: 219, index: 16, name: 'Guyana', parent: 'GUYANA', tz: 'America/Guyana', utc: 'America/Guyana', code: 'GUYANA_AMERICA_GUYANA',
},
{
  uid: 220, index: 14, name: 'Haiti', parent: 'HAITI', tz: 'America/Port-au-Prince', utc: 'America/Port-au-Prince', code: 'HAITI_AMERICA_PORT_AU_PRINCE',
},
{
  uid: 221, index: 12, name: 'Honduras', parent: 'HONDURAS', tz: 'America/Tegucigalpa', utc: 'America/Tegucigalpa', code: 'HONDURAS_AMERICA_TEGUCIGALPA',
},
{
  uid: 222, index: 29, name: 'Hong Kong', parent: 'HONG_KONG', tz: 'Asia/Hong Kong', utc: 'Asia/Hong_Kong', code: 'HONG_KONG_ASIA_HONG_KONG',
},
{
  uid: 223, index: 28, name: 'Hungary', parent: 'HUNGARY', tz: 'Europe/Budapest', utc: 'Europe/Budapest', code: 'HUNGARY_EUROPE_BUDAPEST',
},
{
  uid: 224, index: 24, name: 'Iceland', parent: 'ICELAND', tz: 'Atlantic/Reykjavik', utc: 'Atlantic/Reykjavik', code: 'ICELAND_ATLANTIC_REYKJAVIK',
},
{
  uid: 225, index: 35, name: 'India', parent: 'INDIA', tz: 'Asia/Kolkata', utc: 'Asia/Kolkata', code: 'INDIA_ASIA_KOLKATA',
},
{
  uid: 226, index: 38, name: 'Indonesia', parent: 'INDONESIA', tz: 'Asia/Jakarta', utc: 'Asia/Jakarta', code: 'INDONESIA_ASIA_JAKARTA',
},
{
  uid: 227, index: 27, name: 'Indonesia', parent: 'INDONESIA', tz: 'Asia/Jayapura', utc: 'Asia/Jayapura', code: 'INDONESIA_ASIA_JAYAPURA',
},
{
  uid: 228, index: 29, name: 'Indonesia', parent: 'INDONESIA', tz: 'Asia/Makassar', utc: 'Asia/Makassar', code: 'INDONESIA_ASIA_MAKASSAR',
},
{
  uid: 229, index: 38, name: 'Indonesia', parent: 'INDONESIA', tz: 'Asia/Pontianak', utc: 'Asia/Pontianak', code: 'INDONESIA_ASIA_PONTIANAK',
},
{
  uid: 230, index: 33, name: 'Iran', parent: 'IRAN', tz: 'Asia/Tehran', utc: 'Asia/Tehran', code: 'IRAN_ASIA_TEHRAN',
},
{
  uid: 231, index: 30, name: 'Iraq', parent: 'IRAQ', tz: 'Asia/Baghdad', utc: 'Asia/Baghdad', code: 'IRAQ_ASIA_BAGHDAD',
},
{
  uid: 232, index: 26, name: 'Ireland', parent: 'IRELAND', tz: 'Europe/Dublin', utc: 'Europe/Dublin', code: 'IRELAND_EUROPE_DUBLIN',
},
{
  uid: 233, index: 26, name: 'Isle of Man', parent: 'ISLE_OF_MAN', tz: 'Europe/Isle of Man', utc: 'Europe/Isle_of_Man', code: 'ISLE_OF_MAN_EUROPE_ISLE_OF_MAN',
},
{
  uid: 234, index: 30, name: 'Israel', parent: 'ISRAEL', tz: 'Asia/Jerusalem', utc: 'Asia/Jerusalem', code: 'ISRAEL_ASIA_JERUSALEM',
},
{
  uid: 235, index: 28, name: 'Italy', parent: 'ITALY', tz: 'Europe/Rome', utc: 'Europe/Rome', code: 'ITALY_EUROPE_ROME',
},
{
  uid: 236, index: 14, name: 'Jamaica', parent: 'JAMAICA', tz: 'America/Jamaica', utc: 'America/Jamaica', code: 'JAMAICA_AMERICA_JAMAICA',
},
{
  uid: 237, index: 27, name: 'Japan', parent: 'JAPAN', tz: 'Asia/Tokyo', utc: 'Asia/Tokyo', code: 'JAPAN_ASIA_TOKYO',
},
{
  uid: 238, index: 26, name: 'Jersey', parent: 'JERSEY', tz: 'Europe/Jersey', utc: 'Europe/Jersey', code: 'JERSEY_EUROPE_JERSEY',
},
{
  uid: 239, index: 30, name: 'Jordan', parent: 'JORDAN', tz: 'Asia/Amman', utc: 'Asia/Amman', code: 'JORDAN_ASIA_AMMAN',
},
{
  uid: 240, index: 36, name: 'Kazakhstan', parent: 'KAZAKHSTAN', tz: 'Asia/Almaty', utc: 'Asia/Almaty', code: 'KAZAKHSTAN_ASIA_ALMATY',
},
{
  uid: 241, index: 34, name: 'Kazakhstan', parent: 'KAZAKHSTAN', tz: 'Asia/Aqtau', utc: 'Asia/Aqtau', code: 'KAZAKHSTAN_ASIA_AQTAU',
},
{
  uid: 242, index: 34, name: 'Kazakhstan', parent: 'KAZAKHSTAN', tz: 'Asia/Aqtobe', utc: 'Asia/Aqtobe', code: 'KAZAKHSTAN_ASIA_AQTOBE',
},
{
  uid: 243, index: 34, name: 'Kazakhstan', parent: 'KAZAKHSTAN', tz: 'Asia/Oral', utc: 'Asia/Oral', code: 'KAZAKHSTAN_ASIA_ORAL',
},
{
  uid: 244, index: 36, name: 'Kazakhstan', parent: 'KAZAKHSTAN', tz: 'Asia/Qyzylorda', utc: 'Asia/Qyzylorda', code: 'KAZAKHSTAN_ASIA_QYZYLORDA',
},
{
  uid: 245, index: 30, name: 'Kenya', parent: 'KENYA', tz: 'Africa/Nairobi', utc: 'Africa/Nairobi', code: 'KENYA_AFRICA_NAIROBI',
},
{
  uid: 246, index: 3, name: 'Kiribati', parent: 'KIRIBATI', tz: 'Pacific/Enderbury', utc: 'Pacific/Enderbury', code: 'KIRIBATI_PACIFIC_ENDERBURY',
},
{
  uid: 247, index: 1, name: 'Kiribati', parent: 'KIRIBATI', tz: 'Pacific/Kiritimati', utc: 'Pacific/Kiritimati', code: 'KIRIBATI_PACIFIC_KIRITIMATI',
},
{
  uid: 248, index: 9, name: 'Kiribati', parent: 'KIRIBATI', tz: 'Pacific/Tarawa', utc: 'Pacific/Tarawa', code: 'KIRIBATI_PACIFIC_TARAWA',
},
{
  uid: 249, index: 27, name: 'North Korea', parent: 'NORTH_KOREA', tz: 'Asia/Pyongyang', utc: 'Asia/Pyongyang', code: 'NORTH_KOREA_ASIA_PYONGYANG',
},
{
  uid: 250, index: 27, name: 'South Korea', parent: 'SOUTH_KOREA', tz: 'Asia/Seoul', utc: 'Asia/Seoul', code: 'SOUTH_KOREA_ASIA_SEOUL',
},
{
  uid: 251, index: 30, name: 'Kuwait', parent: 'KUWAIT', tz: 'Asia/Kuwait', utc: 'Asia/Kuwait', code: 'KUWAIT_ASIA_KUWAIT',
},
{
  uid: 252, index: 36, name: 'Kyrgyzstan', parent: 'KYRGYZSTAN', tz: 'Asia/Bishkek', utc: 'Asia/Bishkek', code: 'KYRGYZSTAN_ASIA_BISHKEK',
},
{
  uid: 253, index: 38, name: 'Laos', parent: 'LAOS', tz: 'Asia/Vientiane', utc: 'Asia/Vientiane', code: 'LAOS_ASIA_VIENTIANE',
},
{
  uid: 254, index: 30, name: 'Latvia', parent: 'LATVIA', tz: 'Europe/Riga', utc: 'Europe/Riga', code: 'LATVIA_EUROPE_RIGA',
},
{
  uid: 255, index: 30, name: 'Lebanon', parent: 'LEBANON', tz: 'Asia/Beirut', utc: 'Asia/Beirut', code: 'LEBANON_ASIA_BEIRUT',
},
{
  uid: 256, index: 28, name: 'Lesotho', parent: 'LESOTHO', tz: 'Africa/Maseru', utc: 'Africa/Maseru', code: 'LESOTHO_AFRICA_MASERU',
},
{
  uid: 257, index: 24, name: 'Liberia', parent: 'LIBERIA', tz: 'Africa/Monrovia', utc: 'Africa/Monrovia', code: 'LIBERIA_AFRICA_MONROVIA',
},
{
  uid: 258, index: 28, name: 'Libya', parent: 'LIBYA', tz: 'Africa/Tripoli', utc: 'Africa/Tripoli', code: 'LIBYA_AFRICA_TRIPOLI',
},
{
  uid: 259, index: 28, name: 'Liechtenstein', parent: 'LIECHTENSTEIN', tz: 'Europe/Vaduz', utc: 'Europe/Vaduz', code: 'LIECHTENSTEIN_EUROPE_VADUZ',
},
{
  uid: 260, index: 30, name: 'Lithuania', parent: 'LITHUANIA', tz: 'Europe/Vilnius', utc: 'Europe/Vilnius', code: 'LITHUANIA_EUROPE_VILNIUS',
},
{
  uid: 261, index: 28, name: 'Luxembourg', parent: 'LUXEMBOURG', tz: 'Europe/Luxembourg', utc: 'Europe/Luxembourg', code: 'LUXEMBOURG_EUROPE_LUXEMBOURG',
},
{
  uid: 262, index: 29, name: 'Macau', parent: 'MACAU', tz: 'Asia/Macau', utc: 'Asia/Macau', code: 'MACAU_ASIA_MACAU',
},
{
  uid: 263, index: 28, name: 'Macedonia', parent: 'MACEDONIA', tz: 'Europe/Skopje', utc: 'Europe/Skopje', code: 'MACEDONIA_EUROPE_SKOPJE',
},
{
  uid: 264, index: 30, name: 'Madagascar', parent: 'MADAGASCAR', tz: 'Indian/Antananarivo', utc: 'Indian/Antananarivo', code: 'MADAGASCAR_INDIAN_ANTANANARIVO',
},
{
  uid: 265, index: 28, name: 'Malawi', parent: 'MALAWI', tz: 'Africa/Blantyre', utc: 'Africa/Blantyre', code: 'MALAWI_AFRICA_BLANTYRE',
},
{
  uid: 266, index: 29, name: 'Malaysia', parent: 'MALAYSIA', tz: 'Asia/Kuala Lumpur', utc: 'Asia/Kuala_Lumpur', code: 'MALAYSIA_ASIA_KUALA_LUMPUR',
},
{
  uid: 267, index: 29, name: 'Malaysia', parent: 'MALAYSIA', tz: 'Asia/Kuching', utc: 'Asia/Kuching', code: 'MALAYSIA_ASIA_KUCHING',
},
{
  uid: 268, index: 34, name: 'Maldives', parent: 'MALDIVES', tz: 'Indian/Maldives', utc: 'Indian/Maldives', code: 'MALDIVES_INDIAN_MALDIVES',
},
{
  uid: 269, index: 24, name: 'Mali', parent: 'MALI', tz: 'Africa/Bamako', utc: 'Africa/Bamako', code: 'MALI_AFRICA_BAMAKO',
},
{
  uid: 270, index: 28, name: 'Malta', parent: 'MALTA', tz: 'Europe/Malta', utc: 'Europe/Malta', code: 'MALTA_EUROPE_MALTA',
},
{
  uid: 271, index: 9, name: 'Marshall Islands', parent: 'MARSHALL_ISLANDS', tz: 'Pacific/Kwajalein', utc: 'Pacific/Kwajalein', code: 'MARSHALL_ISLANDS_PACIFIC_KWAJALEIN',
},
{
  uid: 272, index: 9, name: 'Marshall Islands', parent: 'MARSHALL_ISLANDS', tz: 'Pacific/Majuro', utc: 'Pacific/Majuro', code: 'MARSHALL_ISLANDS_PACIFIC_MAJURO',
},
{
  uid: 273, index: 16, name: 'Martinique', parent: 'MARTINIQUE', tz: 'America/Martinique', utc: 'America/Martinique', code: 'MARTINIQUE_AMERICA_MARTINIQUE',
},
{
  uid: 274, index: 24, name: 'Mauritania', parent: 'MAURITANIA', tz: 'Africa/Nouakchott', utc: 'Africa/Nouakchott', code: 'MAURITANIA_AFRICA_NOUAKCHOTT',
},
{
  uid: 275, index: 32, name: 'Mauritius', parent: 'MAURITIUS', tz: 'Indian/Mauritius', utc: 'Indian/Mauritius', code: 'MAURITIUS_INDIAN_MAURITIUS',
},
{
  uid: 276, index: 30, name: 'Mayotte', parent: 'MAYOTTE', tz: 'Indian/Mayotte', utc: 'Indian/Mayotte', code: 'MAYOTTE_INDIAN_MAYOTTE',
},
{
  uid: 277, index: 14, name: 'Mexico', parent: 'MEXICO', tz: 'America/Cancun', utc: 'America/Cancun', code: 'MEXICO_AMERICA_CANCUN',
},
{
  uid: 278, index: 12, name: 'Mexico', parent: 'MEXICO', tz: 'America/Chihuahua', utc: 'America/Chihuahua', code: 'MEXICO_AMERICA_CHIHUAHUA',
},
{
  uid: 279, index: 10, name: 'Mexico', parent: 'MEXICO', tz: 'America/Hermosillo', utc: 'America/Hermosillo', code: 'MEXICO_AMERICA_HERMOSILLO',
},
{
  uid: 280, index: 12, name: 'Mexico', parent: 'MEXICO', tz: 'America/Mazatlan', utc: 'America/Mazatlan', code: 'MEXICO_AMERICA_MAZATLAN',
},
{
  uid: 281, index: 14, name: 'Mexico', parent: 'MEXICO', tz: 'America/Merida', utc: 'America/Merida', code: 'MEXICO_AMERICA_MERIDA',
},
{
  uid: 282, index: 14, name: 'Mexico', parent: 'MEXICO', tz: 'America/Mexico City', utc: 'America/Mexico_City', code: 'MEXICO_AMERICA_MEXICO_CITY',
},
{
  uid: 283, index: 14, name: 'Mexico', parent: 'MEXICO', tz: 'America/Monterrey', utc: 'America/Monterrey', code: 'MEXICO_AMERICA_MONTERREY',
},
{
  uid: 284, index: 10, name: 'Mexico', parent: 'MEXICO', tz: 'America/Tijuana', utc: 'America/Tijuana', code: 'MEXICO_AMERICA_TIJUANA',
},
{
  uid: 285, index: 13, name: 'Federated States of Micronesia', parent: 'FEDERATED_STATES_OF_MICRONESIA', tz: 'Pacific/Kosrae', utc: 'Pacific/Kosrae', code: 'FEDERATED_STATES_OF_MICRONESIA_PACIFIC_KOSRAE',
},
{
  uid: 286, index: 24, name: 'Federated States of Micronesia', parent: 'FEDERATED_STATES_OF_MICRONESIA', tz: 'Pacific/Ponape', utc: 'Pacific/Ponape', code: 'FEDERATED_STATES_OF_MICRONESIA_PACIFIC_PONAPE',
},
{
  uid: 287, index: 24, name: 'Federated States of Micronesia', parent: 'FEDERATED_STATES_OF_MICRONESIA', tz: 'Pacific/Truk', utc: 'Pacific/Truk', code: 'FEDERATED_STATES_OF_MICRONESIA_PACIFIC_TRUK',
},
{
  uid: 288, index: 30, name: 'Moldova', parent: 'MOLDOVA', tz: 'Europe/Chisinau', utc: 'Europe/Chisinau', code: 'MOLDOVA_EUROPE_CHISINAU',
},
{
  uid: 289, index: 28, name: 'Monaco', parent: 'MONACO', tz: 'Europe/Monaco', utc: 'Europe/Monaco', code: 'MONACO_EUROPE_MONACO',
},
{
  uid: 290, index: 29, name: 'Mongolia', parent: 'MONGOLIA', tz: 'Asia/Choibalsan', utc: 'Asia/Choibalsan', code: 'MONGOLIA_ASIA_CHOIBALSAN',
},
{
  uid: 291, index: 38, name: 'Mongolia', parent: 'MONGOLIA', tz: 'Asia/Hovd', utc: 'Asia/Hovd', code: 'MONGOLIA_ASIA_HOVD',
},
{
  uid: 292, index: 29, name: 'Mongolia', parent: 'MONGOLIA', tz: 'Asia/Ulaanbaatar', utc: 'Asia/Ulaanbaatar', code: 'MONGOLIA_ASIA_ULAANBAATAR',
},
{
  uid: 293, index: 28, name: 'Montenegro', parent: 'MONTENEGRO', tz: 'Europe/Podgorica', utc: 'Europe/Podgorica', code: 'MONTENEGRO_EUROPE_PODGORICA',
},
{
  uid: 294, index: 16, name: 'Montserrat', parent: 'MONTSERRAT', tz: 'America/Montserrat', utc: 'America/Montserrat', code: 'MONTSERRAT_AMERICA_MONTSERRAT',
},
{
  uid: 295, index: 26, name: 'Morocco', parent: 'MOROCCO', tz: 'Africa/Casablanca', utc: 'Africa/Casablanca', code: 'MOROCCO_AFRICA_CASABLANCA',
},
{
  uid: 296, index: 28, name: 'Mozambique', parent: 'MOZAMBIQUE', tz: 'Africa/Maputo', utc: 'Africa/Maputo', code: 'MOZAMBIQUE_AFRICA_MAPUTO',
},
{
  uid: 297, index: 37, name: 'Myanmar', parent: 'MYANMAR', tz: 'Asia/Rangoon', utc: 'Asia/Rangoon', code: 'MYANMAR_ASIA_RANGOON',
},
{
  uid: 298, index: 26, name: 'Namibia', parent: 'NAMIBIA', tz: 'Africa/Windhoek', utc: 'Africa/Windhoek', code: 'NAMIBIA_AFRICA_WINDHOEK',
},
{
  uid: 299, index: 9, name: 'Nauru', parent: 'NAURU', tz: 'Pacific/Nauru', utc: 'Pacific/Nauru', code: 'NAURU_PACIFIC_NAURU',
},
{
  uid: 300, index: 24, name: 'Nepal', parent: 'NEPAL', tz: 'Asia/Katmandu', utc: 'Asia/Katmandu', code: 'NEPAL_ASIA_KATMANDU',
},
{
  uid: 301, index: 16, name: 'Netherlands Antilles', parent: 'NETHERLANDS_ANTILLES', tz: 'America/Curacao', utc: 'America/Curacao', code: 'NETHERLANDS_ANTILLES_AMERICA_CURACAO',
},
{
  uid: 302, index: 28, name: 'Netherlands', parent: 'NETHERLANDS', tz: 'Europe/Amsterdam', utc: 'Europe/Amsterdam', code: 'NETHERLANDS_EUROPE_AMSTERDAM',
},
{
  uid: 303, index: 13, name: 'New Caledonia', parent: 'NEW_CALEDONIA', tz: 'Pacific/Noumea', utc: 'Pacific/Noumea', code: 'NEW_CALEDONIA_PACIFIC_NOUMEA',
},
{
  uid: 304, index: 9, name: 'New Zealand', parent: 'NEW_ZEALAND', tz: 'Pacific/Auckland', utc: 'Pacific/Auckland', code: 'NEW_ZEALAND_PACIFIC_AUCKLAND',
},
{
  uid: 305, index: 9, name: 'New Zealand', parent: 'NEW_ZEALAND', tz: 'Pacific/Chatham', utc: 'Pacific/Chatham', code: 'NEW_ZEALAND_PACIFIC_CHATHAM',
},
{
  uid: 306, index: 12, name: 'Nicaragua', parent: 'NICARAGUA', tz: 'America/Managua', utc: 'America/Managua', code: 'NICARAGUA_AMERICA_MANAGUA',
},
{
  uid: 307, index: 26, name: 'Nigeria', parent: 'NIGERIA', tz: 'Africa/Lagos', utc: 'Africa/Lagos', code: 'NIGERIA_AFRICA_LAGOS',
},
{
  uid: 308, index: 26, name: 'Niger', parent: 'NIGER', tz: 'Africa/Niamey', utc: 'Africa/Niamey', code: 'NIGER_AFRICA_NIAMEY',
},
{
  uid: 309, index: 2, name: 'Niue', parent: 'NIUE', tz: 'Pacific/Niue', utc: 'Pacific/Niue', code: 'NIUE_PACIFIC_NIUE',
},
{
  uid: 310, index: 11, name: 'Norfolk Island', parent: 'NORFOLK_ISLAND', tz: 'Pacific/Norfolk', utc: 'Pacific/Norfolk', code: 'NORFOLK_ISLAND_PACIFIC_NORFOLK',
},
{
  uid: 311, index: 23, name: 'Northern Mariana Islands', parent: 'NORTHERN_MARIANA_ISLANDS', tz: 'Pacific/Saipan', utc: 'Pacific/Saipan', code: 'NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN',
},
{
  uid: 312, index: 28, name: 'Norway', parent: 'NORWAY', tz: 'Europe/Oslo', utc: 'Europe/Oslo', code: 'NORWAY_EUROPE_OSLO',
},
{
  uid: 313, index: 28, name: 'Palestinian Territory', parent: 'PALESTINIAN_TERRITORY', tz: 'Asia/Gaza', utc: 'Asia/Gaza', code: 'PALESTINIAN_TERRITORY_ASIA_GAZA',
},
{
  uid: 314, index: 32, name: 'Oman', parent: 'OMAN', tz: 'Asia/Muscat', utc: 'Asia/Muscat', code: 'OMAN_ASIA_MUSCAT',
},
{
  uid: 315, index: 34, name: 'Pakistan', parent: 'PAKISTAN', tz: 'Asia/Karachi', utc: 'Asia/Karachi', code: 'PAKISTAN_ASIA_KARACHI',
},
{
  uid: 316, index: 27, name: 'Palau', parent: 'PALAU', tz: 'Pacific/Palau', utc: 'Pacific/Palau', code: 'PALAU_PACIFIC_PALAU',
},
{
  uid: 317, index: 14, name: 'Panama', parent: 'PANAMA', tz: 'America/Panama', utc: 'America/Panama', code: 'PANAMA_AMERICA_PANAMA',
},
{
  uid: 318, index: 23, name: 'Papua New Guinea', parent: 'PAPUA_NEW_GUINEA', tz: 'Pacific/Port Moresby', utc: 'Pacific/Port_Moresby', code: 'PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY',
},
{
  uid: 319, index: 16, name: 'Paraguay', parent: 'PARAGUAY', tz: 'America/Asuncion', utc: 'America/Asuncion', code: 'PARAGUAY_AMERICA_ASUNCION',
},
{
  uid: 320, index: 14, name: 'Peru', parent: 'PERU', tz: 'America/Lima', utc: 'America/Lima', code: 'PERU_AMERICA_LIMA',
},
{
  uid: 321, index: 29, name: 'Philippines', parent: 'PHILIPPINES', tz: 'Asia/Manila', utc: 'Asia/Manila', code: 'PHILIPPINES_ASIA_MANILA',
},
{
  uid: 322, index: 8, name: 'Pitcairn Islands', parent: 'PITCAIRN_ISLANDS', tz: 'Pacific/Pitcairn', utc: 'Pacific/Pitcairn', code: 'PITCAIRN_ISLANDS_PACIFIC_PITCAIRN',
},
{
  uid: 323, index: 28, name: 'Poland', parent: 'POLAND', tz: 'Europe/Warsaw', utc: 'Europe/Warsaw', code: 'POLAND_EUROPE_WARSAW',
},
{
  uid: 324, index: 24, name: 'Portugal', parent: 'PORTUGAL', tz: 'Atlantic/Azores', utc: 'Atlantic/Azores', code: 'PORTUGAL_ATLANTIC_AZORES',
},
{
  uid: 325, index: 26, name: 'Portugal', parent: 'PORTUGAL', tz: 'Atlantic/Madeira', utc: 'Atlantic/Madeira', code: 'PORTUGAL_ATLANTIC_MADEIRA',
},
{
  uid: 326, index: 26, name: 'Portugal', parent: 'PORTUGAL', tz: 'Europe/Lisbon', utc: 'Europe/Lisbon', code: 'PORTUGAL_EUROPE_LISBON',
},
{
  uid: 327, index: 16, name: 'Puerto Rico', parent: 'PUERTO_RICO', tz: 'America/Puerto Rico', utc: 'America/Puerto_Rico', code: 'PUERTO_RICO_AMERICA_PUERTO_RICO',
},
{
  uid: 328, index: 30, name: 'Qatar', parent: 'QATAR', tz: 'Asia/Qatar', utc: 'Asia/Qatar', code: 'QATAR_ASIA_QATAR',
},
{
  uid: 329, index: 32, name: 'Reunion', parent: 'REUNION', tz: 'Indian/Reunion', utc: 'Indian/Reunion', code: 'REUNION_INDIAN_REUNION',
},
{
  uid: 330, index: 30, name: 'Romania', parent: 'ROMANIA', tz: 'Europe/Bucharest', utc: 'Europe/Bucharest', code: 'ROMANIA_EUROPE_BUCHAREST',
},
{
  uid: 331, index: 9, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Anadyr', utc: 'Asia/Anadyr', code: 'RUSSIA_ASIA_ANADYR',
},
{
  uid: 332, index: 27, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Irkutsk', utc: 'Asia/Irkutsk', code: 'RUSSIA_ASIA_IRKUTSK',
},
{
  uid: 333, index: 9, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Kamchatka', utc: 'Asia/Kamchatka', code: 'RUSSIA_ASIA_KAMCHATKA',
},
{
  uid: 334, index: 29, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Krasnoyarsk', utc: 'Asia/Krasnoyarsk', code: 'RUSSIA_ASIA_KRASNOYARSK',
},
{
  uid: 335, index: 9, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Magadan', utc: 'Asia/Magadan', code: 'RUSSIA_ASIA_MAGADAN',
},
{
  uid: 336, index: 38, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Novosibirsk', utc: 'Asia/Novosibirsk', code: 'RUSSIA_ASIA_NOVOSIBIRSK',
},
{
  uid: 337, index: 38, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Omsk', utc: 'Asia/Omsk', code: 'RUSSIA_ASIA_OMSK',
},
{
  uid: 338, index: 13, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Sakhalin', utc: 'Asia/Sakhalin', code: 'RUSSIA_ASIA_SAKHALIN',
},
{
  uid: 339, index: 13, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Vladivostok', utc: 'Asia/Vladivostok', code: 'RUSSIA_ASIA_VLADIVOSTOK',
},
{
  uid: 340, index: 23, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Yakutsk', utc: 'Asia/Yakutsk', code: 'RUSSIA_ASIA_YAKUTSK',
},
{
  uid: 341, index: 36, name: 'Russia', parent: 'RUSSIA', tz: 'Asia/Yekaterinburg', utc: 'Asia/Yekaterinburg', code: 'RUSSIA_ASIA_YEKATERINBURG',
},
{
  uid: 342, index: 30, name: 'Russia', parent: 'RUSSIA', tz: 'Europe/Kaliningrad', utc: 'Europe/Kaliningrad', code: 'RUSSIA_EUROPE_KALININGRAD',
},
{
  uid: 343, index: 32, name: 'Russia', parent: 'RUSSIA', tz: 'Europe/Moscow', utc: 'Europe/Moscow', code: 'RUSSIA_EUROPE_MOSCOW',
},
{
  uid: 344, index: 32, name: 'Russia', parent: 'RUSSIA', tz: 'Europe/Samara', utc: 'Europe/Samara', code: 'RUSSIA_EUROPE_SAMARA',
},
{
  uid: 345, index: 32, name: 'Russia', parent: 'RUSSIA', tz: 'Europe/Volgograd', utc: 'Europe/Volgograd', code: 'RUSSIA_EUROPE_VOLGOGRAD',
},
{
  uid: 346, index: 28, name: 'Rwanda', parent: 'RWANDA', tz: 'Africa/Kigali', utc: 'Africa/Kigali', code: 'RWANDA_AFRICA_KIGALI',
},
{
  uid: 347, index: 16, name: 'Saint Barthelemy', parent: 'SAINT_BARTHELEMY', tz: 'America/St Barthelemy', utc: 'America/St_Barthelemy', code: 'SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY',
},
{
  uid: 348, index: 16, name: 'Saint Kitts and Nevis', parent: 'SAINT_KITTS_AND_NEVIS', tz: 'America/St Kitts', utc: 'America/St_Kitts', code: 'SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS',
},
{
  uid: 349, index: 16, name: 'Saint Lucia', parent: 'SAINT_LUCIA', tz: 'America/St Lucia', utc: 'America/St_Lucia', code: 'SAINT_LUCIA_AMERICA_ST_LUCIA',
},
{
  uid: 350, index: 16, name: 'Saint Martin', parent: 'SAINT_MARTIN', tz: 'America/Marigot', utc: 'America/Marigot', code: 'SAINT_MARTIN_AMERICA_MARIGOT',
},
{
  uid: 351, index: 16, name: 'Saint Vincent and the Grenadines', parent: 'SAINT_VINCENT_AND_THE_GRENADINES', tz: 'America/St Vincent', utc: 'America/St_Vincent', code: 'SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT',
},
{
  uid: 352, index: 3, name: 'Samoa', parent: 'SAMOA', tz: 'Pacific/Apia', utc: 'Pacific/Apia', code: 'SAMOA_PACIFIC_APIA',
},
{
  uid: 353, index: 28, name: 'San Marino', parent: 'SAN_MARINO', tz: 'Europe/San Marino', utc: 'Europe/San_Marino', code: 'SAN_MARINO_EUROPE_SAN_MARINO',
},
{
  uid: 354, index: 24, name: 'Sao Tome and Principe', parent: 'SAO_TOME_AND_PRINCIPE', tz: 'Africa/Sao Tome', utc: 'Africa/Sao_Tome', code: 'SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME',
},
{
  uid: 355, index: 30, name: 'Saudi Arabia', parent: 'SAUDI_ARABIA', tz: 'Asia/Riyadh', utc: 'Asia/Riyadh', code: 'SAUDI_ARABIA_ASIA_RIYADH',
},
{
  uid: 356, index: 24, name: 'Senegal', parent: 'SENEGAL', tz: 'Africa/Dakar', utc: 'Africa/Dakar', code: 'SENEGAL_AFRICA_DAKAR',
},
{
  uid: 357, index: 28, name: 'Serbia', parent: 'SERBIA', tz: 'Europe/Belgrade', utc: 'Europe/Belgrade', code: 'SERBIA_EUROPE_BELGRADE',
},
{
  uid: 358, index: 32, name: 'Seychelles', parent: 'SEYCHELLES', tz: 'Indian/Mahe', utc: 'Indian/Mahe', code: 'SEYCHELLES_INDIAN_MAHE',
},
{
  uid: 359, index: 24, name: 'Sierra Leone', parent: 'SIERRA_LEONE', tz: 'Africa/Freetown', utc: 'Africa/Freetown', code: 'SIERRA_LEONE_AFRICA_FREETOWN',
},
{
  uid: 360, index: 29, name: 'Singapore', parent: 'SINGAPORE', tz: 'Asia/Singapore', utc: 'Asia/Singapore', code: 'SINGAPORE_ASIA_SINGAPORE',
},
{
  uid: 361, index: 28, name: 'Slovakia', parent: 'SLOVAKIA', tz: 'Europe/Bratislava', utc: 'Europe/Bratislava', code: 'SLOVAKIA_EUROPE_BRATISLAVA',
},
{
  uid: 362, index: 28, name: 'Slovenia', parent: 'SLOVENIA', tz: 'Europe/Ljubljana', utc: 'Europe/Ljubljana', code: 'SLOVENIA_EUROPE_LJUBLJANA',
},
{
  uid: 363, index: 13, name: 'Solomon Islands', parent: 'SOLOMON_ISLANDS', tz: 'Pacific/Guadalcanal', utc: 'Pacific/Guadalcanal', code: 'SOLOMON_ISLANDS_PACIFIC_GUADALCANAL',
},
{
  uid: 364, index: 30, name: 'Somalia', parent: 'SOMALIA', tz: 'Africa/Mogadishu', utc: 'Africa/Mogadishu', code: 'SOMALIA_AFRICA_MOGADISHU',
},
{
  uid: 365, index: 28, name: 'South Africa', parent: 'SOUTH_AFRICA', tz: 'Africa/Johannesburg', utc: 'Africa/Johannesburg', code: 'SOUTH_AFRICA_AFRICA_JOHANNESBURG',
},
{
  uid: 366, index: 20, name: 'South Georgia and the South Sandwich Islands', parent: 'SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS', tz: 'Atlantic/South Georgia', utc: 'Atlantic/South_Georgia', code: 'SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA',
},
{
  uid: 367, index: 28, name: 'Spain', parent: 'SPAIN', tz: 'Africa/Ceuta', utc: 'Africa/Ceuta', code: 'SPAIN_AFRICA_CEUTA',
},
{
  uid: 368, index: 26, name: 'Spain', parent: 'SPAIN', tz: 'Atlantic/Canary', utc: 'Atlantic/Canary', code: 'SPAIN_ATLANTIC_CANARY',
},
{
  uid: 369, index: 28, name: 'Spain', parent: 'SPAIN', tz: 'Europe/Madrid', utc: 'Europe/Madrid', code: 'SPAIN_EUROPE_MADRID',
},
{
  uid: 370, index: 35, name: 'Sri Lanka', parent: 'SRI_LANKA', tz: 'Asia/Colombo', utc: 'Asia/Colombo', code: 'SRI_LANKA_ASIA_COLOMBO',
},
{
  uid: 371, index: 24, name: 'Saint Helena', parent: 'SAINT_HELENA', tz: 'Atlantic/St Helena', utc: 'Atlantic/St_Helena', code: 'SAINT_HELENA_ATLANTIC_ST_HELENA',
},
{
  uid: 372, index: 20, name: 'Saint Pierre and Miquelon', parent: 'SAINT_PIERRE_AND_MIQUELON', tz: 'America/Miquelon', utc: 'America/Miquelon', code: 'SAINT_PIERRE_AND_MIQUELON_AMERICA_MIQUELON',
},
{
  uid: 373, index: 30, name: 'Sudan', parent: 'SUDAN', tz: 'Africa/Khartoum', utc: 'Africa/Khartoum', code: 'SUDAN_AFRICA_KHARTOUM',
},
{
  uid: 374, index: 18, name: 'Suriname', parent: 'SURINAME', tz: 'America/Paramaribo', utc: 'America/Paramaribo', code: 'SURINAME_AMERICA_PARAMARIBO',
},
{
  uid: 375, index: 28, name: 'Svalbard and Jan Mayen', parent: 'SVALBARD_AND_JAN_MAYEN', tz: 'Arctic/Longyearbyen', utc: 'Arctic/Longyearbyen', code: 'SVALBARD_AND_JAN_MAYEN_ARCTIC_LONGYEARBYEN',
},
{
  uid: 376, index: 28, name: 'Swaziland', parent: 'SWAZILAND', tz: 'Africa/Mbabane', utc: 'Africa/Mbabane', code: 'SWAZILAND_AFRICA_MBABANE',
},
{
  uid: 377, index: 28, name: 'Sweden', parent: 'SWEDEN', tz: 'Europe/Stockholm', utc: 'Europe/Stockholm', code: 'SWEDEN_EUROPE_STOCKHOLM',
},
{
  uid: 378, index: 28, name: 'Switzerland', parent: 'SWITZERLAND', tz: 'Europe/Zurich', utc: 'Europe/Zurich', code: 'SWITZERLAND_EUROPE_ZURICH',
},
{
  uid: 379, index: 30, name: 'Syria', parent: 'SYRIA', tz: 'Asia/Damascus', utc: 'Asia/Damascus', code: 'SYRIA_ASIA_DAMASCUS',
},
{
  uid: 380, index: 29, name: 'Taiwan', parent: 'TAIWAN', tz: 'Asia/Taipei', utc: 'Asia/Taipei', code: 'TAIWAN_ASIA_TAIPEI',
},
{
  uid: 381, index: 34, name: 'Tajikistan', parent: 'TAJIKISTAN', tz: 'Asia/Dushanbe', utc: 'Asia/Dushanbe', code: 'TAJIKISTAN_ASIA_DUSHANBE',
},
{
  uid: 382, index: 30, name: 'Tanzania', parent: 'TANZANIA', tz: 'Africa/Dar es Salaam', utc: 'Africa/Dar_es_Salaam', code: 'TANZANIA_AFRICA_DAR_ES_SALAAM',
},
{
  uid: 383, index: 38, name: 'Thailand', parent: 'THAILAND', tz: 'Asia/Bangkok', utc: 'Asia/Bangkok', code: 'THAILAND_ASIA_BANGKOK',
},
{
  uid: 384, index: 27, name: 'Timor-Leste', parent: 'TIMOR_LESTE', tz: 'Asia/Dili', utc: 'Asia/Dili', code: 'TIMOR_LESTE_ASIA_DILI',
},
{
  uid: 385, index: 24, name: 'Togo', parent: 'TOGO', tz: 'Africa/Lome', utc: 'Africa/Lome', code: 'TOGO_AFRICA_LOME',
},
{
  uid: 386, index: 3, name: 'Tokelau', parent: 'TOKELAU', tz: 'Pacific/Fakaofo', utc: 'Pacific/Fakaofo', code: 'TOKELAU_PACIFIC_FAKAOFO',
},
{
  uid: 387, index: 3, name: 'Tonga', parent: 'TONGA', tz: 'Pacific/Tongatapu', utc: 'Pacific/Tongatapu', code: 'TONGA_PACIFIC_TONGATAPU',
},
{
  uid: 388, index: 16, name: 'Trinidad and Tobago', parent: 'TRINIDAD_AND_TOBAGO', tz: 'America/Port of Spain', utc: 'America/Port_of_Spain', code: 'TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN',
},
{
  uid: 389, index: 26, name: 'Tunisia', parent: 'TUNISIA', tz: 'Africa/Tunis', utc: 'Africa/Tunis', code: 'TUNISIA_AFRICA_TUNIS',
},
{
  uid: 390, index: 30, name: 'Turkey', parent: 'TURKEY', tz: 'Europe/Istanbul', utc: 'UTC-03:00', code: 'TURKEY_EUROPE_ISTANBUL',
},
{
  uid: 391, index: 34, name: 'Turkmenistan', parent: 'TURKMENISTAN', tz: 'Asia/Ashgabat', utc: 'Asia/Ashgabat', code: 'TURKMENISTAN_ASIA_ASHGABAT',
},
{
  uid: 392, index: 16, name: 'Turks and Caicos Islands', parent: 'TURKS_AND_CAICOS_ISLANDS', tz: 'America/Grand Turk', utc: 'America/Grand_Turk', code: 'TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK',
},
{
  uid: 393, index: 9, name: 'Tuvalu', parent: 'TUVALU', tz: 'Pacific/Funafuti', utc: 'Pacific/Funafuti', code: 'TUVALU_PACIFIC_FUNAFUTI',
},
{
  uid: 394, index: 30, name: 'Uganda', parent: 'UGANDA', tz: 'Africa/Kampala', utc: 'Africa/Kampala', code: 'UGANDA_AFRICA_KAMPALA',
},
{
  uid: 395, index: 30, name: 'Ukraine', parent: 'UKRAINE', tz: 'Europe/Kiev', utc: 'Europe/Kiev', code: 'UKRAINE_EUROPE_KIEV',
},
{
  uid: 396, index: 30, name: 'Ukraine', parent: 'UKRAINE', tz: 'Europe/Simferopol', utc: 'Europe/Simferopol', code: 'UKRAINE_EUROPE_SIMFEROPOL',
},
{
  uid: 397, index: 30, name: 'Ukraine', parent: 'UKRAINE', tz: 'Europe/Uzhgorod', utc: 'Europe/Uzhgorod', code: 'UKRAINE_EUROPE_UZHGOROD',
},
{
  uid: 398, index: 30, name: 'Ukraine', parent: 'UKRAINE', tz: 'Europe/Zaporozhye', utc: 'Europe/Zaporozhye', code: 'UKRAINE_EUROPE_ZAPOROZHYE',
},
{
  uid: 399, index: 32, name: 'United Arab Emirates', parent: 'UNITED_ARAB_EMIRATES', tz: 'Asia/Dubai', utc: 'Asia/Dubai', code: 'UNITED_ARAB_EMIRATES_ASIA_DUBAI',
},
{
  uid: 400, index: 26, name: 'United Kingdom', parent: 'UNITED_KINGDOM', tz: 'Europe/London', utc: 'Europe/London', code: 'UNITED_KINGDOM_EUROPE_LONDON',
},
{
  uid: 401, index: 4, name: 'United States Minor Outlying Islands', parent: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS', tz: 'Pacific/Johnston', utc: 'Pacific/Johnston', code: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON',
},
{
  uid: 402, index: 2, name: 'United States Minor Outlying Islands', parent: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS', tz: 'Pacific/Midway', utc: 'Pacific/Midway', code: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY',
},
{
  uid: 403, index: 9, name: 'United States Minor Outlying Islands', parent: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS', tz: 'Pacific/Wake', utc: 'Pacific/Wake', code: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE',
},
{
  uid: 404, index: 6, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Adak', utc: 'America/Adak', code: 'UNITED_STATES_AMERICA_ADAK',
},
{
  uid: 405, index: 8, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Anchorage', utc: 'America/Anchorage', code: 'UNITED_STATES_AMERICA_ANCHORAGE',
},
{
  uid: 406, index: 12, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Boise', utc: 'America/Boise', code: 'UNITED_STATES_AMERICA_BOISE',
},
{
  uid: 407, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Chicago', utc: 'America/Chicago', code: 'UNITED_STATES_AMERICA_CHICAGO',
},
{
  uid: 408, index: 12, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Denver', utc: 'America/Denver', code: 'UNITED_STATES_AMERICA_DENVER',
},
{
  uid: 409, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Detroit', utc: 'America/Detroit', code: 'UNITED_STATES_AMERICA_DETROIT',
},
{
  uid: 410, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Indianapolis', utc: 'America/Indiana/Indianapolis', code: 'UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS',
},
{
  uid: 411, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Knox', utc: 'America/Indiana/Knox', code: 'UNITED_STATES_AMERICA_INDIANA_KNOX',
},
{
  uid: 412, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Marengo', utc: 'America/Indiana/Marengo', code: 'UNITED_STATES_AMERICA_INDIANA_MARENGO',
},
{
  uid: 413, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Petersburg', utc: 'America/Indiana/Petersburg', code: 'UNITED_STATES_AMERICA_INDIANA_PETERSBURG',
},
{
  uid: 414, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Tell City', utc: 'America/Indiana/Tell_City', code: 'UNITED_STATES_AMERICA_INDIANA_TELL_CITY',
},
{
  uid: 415, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Vevay', utc: 'America/Indiana/Vevay', code: 'UNITED_STATES_AMERICA_INDIANA_VEVAY',
},
{
  uid: 416, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Vincennes', utc: 'America/Indiana/Vincennes', code: 'UNITED_STATES_AMERICA_INDIANA_VINCENNES',
},
{
  uid: 417, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Indiana/Winamac', utc: 'America/Indiana/Winamac', code: 'UNITED_STATES_AMERICA_INDIANA_WINAMAC',
},
{
  uid: 418, index: 8, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Juneau', utc: 'America/Juneau', code: 'UNITED_STATES_AMERICA_JUNEAU',
},
{
  uid: 419, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Kentucky/Louisville', utc: 'America/Kentucky/Louisville', code: 'UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE',
},
{
  uid: 420, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Kentucky/Monticello', utc: 'America/Kentucky/Monticello', code: 'UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO',
},
{
  uid: 421, index: 10, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Los Angeles', utc: 'America/Los_Angeles', code: 'UNITED_STATES_AMERICA_LOS_ANGELES',
},
{
  uid: 422, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Menominee', utc: 'America/Menominee', code: 'UNITED_STATES_AMERICA_MENOMINEE',
},
{
  uid: 423, index: 16, name: 'United States', parent: 'UNITED_STATES', tz: 'America/New York', utc: 'America/New_York', code: 'UNITED_STATES_AMERICA_NEW_YORK',
},
{
  uid: 424, index: 8, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Nome', utc: 'America/Nome', code: 'UNITED_STATES_AMERICA_NOME',
},
{
  uid: 425, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/North Dakota/Center', utc: 'America/North_Dakota/Center', code: 'UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER',
},
{
  uid: 426, index: 14, name: 'United States', parent: 'UNITED_STATES', tz: 'America/North Dakota/New Salem', utc: 'America/North_Dakota/New_Salem', code: 'UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM',
},
{
  uid: 427, index: 10, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Phoenix', utc: 'America/Phoenix', code: 'UNITED_STATES_AMERICA_PHOENIX',
},
{
  uid: 428, index: 12, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Shiprock', utc: 'America/Shiprock', code: 'UNITED_STATES_AMERICA_SHIPROCK',
},
{
  uid: 429, index: 8, name: 'United States', parent: 'UNITED_STATES', tz: 'America/Yakutat', utc: 'America/Yakutat', code: 'UNITED_STATES_AMERICA_YAKUTAT',
},
{
  uid: 430, index: 4, name: 'United States', parent: 'UNITED_STATES', tz: 'Pacific/Honolulu', utc: 'Pacific/Honolulu', code: 'UNITED_STATES_PACIFIC_HONOLULU',
},
{
  uid: 431, index: 18, name: 'Uruguay', parent: 'URUGUAY', tz: 'America/Montevideo', utc: 'America/Montevideo', code: 'URUGUAY_AMERICA_MONTEVIDEO',
},
{
  uid: 432, index: 34, name: 'Uzbekistan', parent: 'UZBEKISTAN', tz: 'Asia/Samarkand', utc: 'Asia/Samarkand', code: 'UZBEKISTAN_ASIA_SAMARKAND',
},
{
  uid: 433, index: 34, name: 'Uzbekistan', parent: 'UZBEKISTAN', tz: 'Asia/Tashkent', utc: 'Asia/Tashkent', code: 'UZBEKISTAN_ASIA_TASHKENT',
},
{
  uid: 434, index: 13, name: 'Vanuatu', parent: 'VANUATU', tz: 'Pacific/Efate', utc: 'Pacific/Efate', code: 'VANUATU_PACIFIC_EFATE',
},
{
  uid: 435, index: 28, name: 'Holy See (Vatican City State)', parent: 'HOLY_SEE_VATICAN_CITY_STATE', tz: 'Europe/Vatican', utc: 'Europe/Vatican', code: 'HOLY_SEE_VATICAN_CITY_STATE_EUROPE_VATICAN',
},
{
  uid: 436, index: 15, name: 'Venezuela', parent: 'VENEZUELA', tz: 'America/Caracas', utc: 'America/Caracas', code: 'VENEZUELA_AMERICA_CARACAS',
},
{
  uid: 437, index: 24, name: 'Vietnam', parent: 'VIETNAM', tz: 'Asia/Saigon', utc: 'Asia/Saigon', code: 'VIETNAM_ASIA_SAIGON',
},
{
  uid: 438, index: 16, name: 'Virgin Islands (British)', parent: 'VIRGIN_ISLANDS_BRITISH', tz: 'America/Tortola', utc: 'America/Tortola', code: 'VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA',
},
{
  uid: 439, index: 16, name: 'Virgin Islands (U.S.)', parent: 'VIRGIN_ISLANDS_US', tz: 'America/St Thomas', utc: 'America/St_Thomas', code: 'VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS',
},
{
  uid: 440, index: 9, name: 'Wallis and Futuna', parent: 'WALLIS_AND_FUTUNA', tz: 'Pacific/Wallis', utc: 'Pacific/Wallis', code: 'WALLIS_AND_FUTUNA_PACIFIC_WALLIS',
},
{
  uid: 441, index: 24, name: 'Western Sahara', parent: 'WESTERN_SAHARA', tz: 'Africa/El Aaiun', utc: 'Africa/El_Aaiun', code: 'WESTERN_SAHARA_AFRICA_EL_AAIUN',
},
{
  uid: 442, index: 30, name: 'Yemen', parent: 'YEMEN', tz: 'Asia/Aden', utc: 'Asia/Aden', code: 'YEMEN_ASIA_ADEN',
},
{
  uid: 443, index: 28, name: 'Zambia', parent: 'ZAMBIA', tz: 'Africa/Lusaka', utc: 'Africa/Lusaka', code: 'ZAMBIA_AFRICA_LUSAKA',
},
{
  uid: 444, index: 28, name: 'Zimbabwe', parent: 'ZIMBABWE', tz: 'Africa/Harare', utc: 'Africa/Harare', code: 'ZIMBABWE_AFRICA_HARARE',
}];

const offset = [
  720,
  -840,
  660,
  -780,
  600,
  570,
  540,
  510,
  480,
  -720,
  420,
  -690,
  360,
  -660,
  300,
  270,
  240,
  210,
  180,
  150,
  120,
  -630,
  60,
  -600,
  0,
  -570,
  -60,
  -540,
  -120,
  -480,
  -180,
  -210,
  -240,
  -270,
  -300,
  -330,
  -360,
  -390,
  -420,
  -525,
  -825,
];
//
// #define sm_unique_timezone_list(val, mask, comment)
// . __BTN(val 0, 720, comment "UTC+12:00")
// . __BTN(val 1, -840, comment "UTC-14:00")
// . __BTN(val 2, 660, comment "UTC+11:00")
// . __BTN(val 3, -780, comment "UTC-13:00")
// . __BTN(val 4, 600, comment "UTC+10:00")
// . __BTN(val 5, 570, comment "UTC+09:30")
// . __BTN(val 6, 540, comment "UTC+09:00")
// . __BTN(val 7, 510, comment "UTC+08:30")
// . __BTN(val 8, 480, comment "UTC+08:00")
// . __BTN(val 9, -720, comment "UTC-12:00")
// . __BTN(val 10, 420, comment "UTC+07:00")
// . __BTN(val 11, -690, comment "UTC-11:30")
// . __BTN(val 12, 360, comment "UTC+06:00")
// . __BTN(val 13, -660, comment "UTC-11:00")
// . __BTN(val 14, 300, comment "UTC+05:00")
// . __BTN(val 15, 270, comment "UTC+04:30")
// . __BTN(val 16, 240, comment "UTC+04:00")
// . __BTN(val 17, 210, comment "UTC+03:30")
// . __BTN(val 18, 180, comment "UTC+03:00")
// . __BTN(val 19, 150, comment "UTC+02:30")
// . __BTN(val 20, 120, comment "UTC+02:00")
// . __BTN(val 21, -630, comment "UTC-10:30")
// . __BTN(val 22, 60, comment "UTC+01:00")
// . __BTN(val 23, -600, comment "UTC-10:00")
// . __BTN(val 24, 0, comment "UTC")
// . __BTN(val 25, -570, comment "UTC-09:30")
// . __BTN(val 26, -60, comment "UTC-01:00")
// . __BTN(val 27, -540, comment "UTC-09:00")
// . __BTN(val 28, -120, comment "UTC-02:00")
// . __BTN(val 29, -480, comment "UTC-08:00")
// . __BTN(val 30, -180, comment "UTC-03:00")
// . __BTN(val 31, -210, comment "UTC-03:30")
// . __BTN(val 32, -240, comment "UTC-04:00")
// . __BTN(val 33, -270, comment "UTC-04:30")
// . __BTN(val 34, -300, comment "UTC-05:00")
// . __BTN(val 35, -330, comment "UTC-05:30")
// . __BTN(val 36, -360, comment "UTC-06:00")
// . __BTN(val 37, -390, comment "UTC-06:30")
// . __BTN(val 38, -420, comment "UTC-07:00")
// . __BTN(val 39, -525, comment "UTC-08:45")
// . __BTN(val 40, -825, comment "UTC-13:45")
// /* end of list */

const timeZoneConverter = (value) => {
  const result = TIME_ZONES.find((x) => x.code === value);
  if (!result) return null;

  return ({ ...result, offset: offset[result.index] });
};

export default timeZoneConverter;
