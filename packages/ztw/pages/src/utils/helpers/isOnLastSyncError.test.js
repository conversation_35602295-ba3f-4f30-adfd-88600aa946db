import { get } from 'utils/lodash';
import isOnLastSyncError from './isOnLastSyncError';

describe('isOnLastSyncError', () => {
  it('should return null when formSyncError is undefined', () => {
    expect(isOnLastSyncError(undefined, 'field')).toBeNull();
  });

  it('should return null when field is not present in formSyncError', () => {
    const formSyncError = { otherField: 'error' };
    expect(isOnLastSyncError(formSyncError, 'field')).toBeNull();
  });

  it('should return the value of the field when it is present in formSyncError', () => {
    const formSyncError = { field: 'error' };
    expect(isOnLastSyncError(formSyncError, 'field')).toBe('error');
  });

  it('should return the value of the nested field when it is present in formSyncError', () => {
    const formSyncError = { nested: { field: 'error' } };
    expect(isOnLastSyncError(formSyncError, 'nested.field')).toBe('error');
  });

  it('should return null when the nested field is not present in formSyncError', () => {
    const formSyncError = { nested: {} };
    expect(isOnLastSyncError(formSyncError, 'nested.field')).toBeNull();
  });
});
