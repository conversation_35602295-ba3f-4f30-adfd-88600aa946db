import getTrendBoundsData from './getTrendBoundsData';
import getTrend from './getTrend';

jest.mock('./getTrend');

describe('getTrendBoundsData', () => {
  it('should not have calls getTrend for entries with unknown name', () => {
    // Arrange
    const entriesUnknown = [
      {
        name: 'UNKNOWN',
        entries: [
          { trend: 'trend1' },
          { trend: 'trend2' },
        ],
      },
    ];
    const chartData = 'chartData';

    // Act
    getTrendBoundsData(entriesUnknown, chartData);

    // Assert
    expect(getTrend).toHaveBeenCalledTimes(0);
  });

  it('should call getTrend for each entry in INBOUND and OUTBOUND', () => {
    // Arrange
    const entries = [
      {
        name: 'INBOUND',
        entries: [
          { trend: 'trend1' },
          { trend: 'trend2' },
        ],
      },
      {
        name: 'OUTBOUND',
        entries: [
          { trend: 'trend3' },
          { trend: 'trend4' },
        ],
      },
    ];
    const chartData = 'chartData';

    // Act
    getTrendBoundsData(entries, chartData);

    // Assert
    expect(getTrend).toHaveBeenCalledTimes(4);
    expect(getTrend).toHaveBeenCalledWith('trend1', chartData, 'Inbound');
    expect(getTrend).toHaveBeenCalledWith('trend2', chartData, 'Inbound');
    expect(getTrend).toHaveBeenCalledWith('trend3', chartData, 'Outbound');
    expect(getTrend).toHaveBeenCalledWith('trend4', chartData, 'Outbound');
  });
});
