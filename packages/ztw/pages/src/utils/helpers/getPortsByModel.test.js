import getPortsByModel from './getPortsByModel';

describe('getPortsByModel function', () => {
  it('should return the correct number of ports for ZT400 model', () => {
    expect(getPortsByModel('ZT400')).toBe('4 GigE Ports');
  });

  it('should return the correct number of ports for ZT600 model', () => {
    expect(getPortsByModel('ZT600')).toBe('6 GigE Ports');
  });

  it('should return the correct number of ports for ZT800 model', () => {
    expect(getPortsByModel('ZT800')).toBe('8 GigE Ports');
  });

  it('should return 0 for an unknown model type', () => {
    expect(getPortsByModel('Unknown')).toBe(0);
  });

  it('should return 0 for an empty model type', () => {
    expect(getPortsByModel('')).toBe(0);
  });

  it('should return 0 for a null model type', () => {
    expect(getPortsByModel(null)).toBe(0);
  });

  it('should return 0 for an undefined model type', () => {
    expect(getPortsByModel(undefined)).toBe(0);
  });
});
