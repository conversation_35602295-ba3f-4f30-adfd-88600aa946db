import getRangeNumberOfIPs from './getRangeNumberOfIPs';

describe('getRangeNumberOfIPs', () => {
  it('should return the correct number of IP addresses in a valid range', () => {
    const startIP = '********';
    const endIP = '********';
    const expectedCount = 5;
    expect(getRangeNumberOfIPs(startIP, endIP)).toBe(expectedCount);
  });

  it('should return the correct number of IP addresses in a range with different octets', () => {
    const startIP = '********';
    const endIP = '********';
    const expectedCount = 261;
    expect(getRangeNumberOfIPs(startIP, endIP)).toBe(expectedCount);
  });

  it('should throw an error for an invalid start IP address', () => {
    const startIP = '10.6.6';
    const endIP = '********';
    expect(() => getRangeNumberOfIPs(startIP, endIP)).toThrowError('Invalid IP address. It must have four octets.');
  });

  it('should throw an error for an invalid end IP address', () => {
    const startIP = '********';
    const endIP = '10.6.6';
    expect(() => getRangeNumberOfIPs(startIP, endIP)).toThrowError('Invalid IP address. It must have four octets.');
  });

  it('should throw an error for an empty start IP address', () => {
    const startIP = '';
    const endIP = '********';
    expect(() => getRangeNumberOfIPs(startIP, endIP)).toThrowError('Invalid IP range. It must be a non-empty string.');
  });

  it('should throw an error for an empty end IP address', () => {
    const startIP = '********';
    const endIP = '';
    expect(() => getRangeNumberOfIPs(startIP, endIP)).toThrowError('Invalid IP range. It must be a non-empty string.');
  });

  it('should throw an error for an invalid IP range', () => {
    const startIP = '********';
    const endIP = '********';
    expect(() => getRangeNumberOfIPs(startIP, endIP)).toThrowError('Invalid IP range. The start IP address must be less than or equal to the end IP address.');
  });
});
