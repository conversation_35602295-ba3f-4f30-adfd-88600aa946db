/* eslint-disable no-useless-escape */
import stringify from './stringify';

describe('stringify function', () => {
  it('should handle circular references', () => {
    const obj = { a: 1 };
    obj.b = obj;
    const result = stringify(obj);
    expect(result).toBe('{"a":1}');
  });

  it('should handle nested objects', () => {
    const obj = { a: 1, b: { c: 2, d: { e: 3 } } };
    const result = stringify(obj);
    expect(result).toBe('{"a":1,"b":{"c":2,"d":{"e":3}}}');
  });

  it('should handle arrays', () => {
    const obj = { a: 1, b: [1, 2, 3] };
    const result = stringify(obj);
    expect(result).toBe('{"a":1,"b":[1,2,3]}');
  });

  it('should handle null and undefined values', () => {
    const obj = { a: null, b: undefined };
    const result = stringify(obj);
    expect(result).toBe('{"a":null}');
  });

  it('should handle functions', () => {
    const obj = { a: () => {} };
    const result = stringify(obj);
    expect(result).toBe('{}');
  });

  it('should handle date objects', () => {
    const obj = { a: new Date('2022-01-01T00:00:00.000Z') };
    const result = stringify(obj);
    expect(result).toBe('{"a":"2022-01-01T00:00:00.000Z"}');
  });

  it('should handle regex objects', () => {
    const obj = { a: /test/ };
    const result = stringify(obj);
    expect(result).toBe('{\"a\":{}}');
  });
});
