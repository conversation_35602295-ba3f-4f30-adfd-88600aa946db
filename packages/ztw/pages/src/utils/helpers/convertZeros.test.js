import convertZeros from './convertZeros';

describe('convertZeros function', () => {
  it('should return "---" for "0.0.0.0"', () => {
    expect(convertZeros('0.0.0.0')).toBe('---');
  });

  it('should return "---" for "0.0.0"', () => {
    expect(convertZeros('0.0.0')).toBe('---');
  });

  it('should return the original IP for other inputs', () => {
    expect(convertZeros('***********')).toBe('***********');
    expect(convertZeros('***************')).toBe('***************');
    expect(convertZeros('********')).toBe('********');
  });

  it('should return the original IP for non-string inputs', () => {
    expect(convertZeros(123)).toBe(123);
    expect(convertZeros(null)).toBe(null);
    expect(convertZeros(undefined)).toBe(undefined);
  });
});
