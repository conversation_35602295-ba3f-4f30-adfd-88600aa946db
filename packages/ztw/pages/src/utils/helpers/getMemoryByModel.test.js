import getMemoryByModel from './getMemoryByModel';

describe('getMemoryByModel function', () => {
  it('should return 16GB RAM for ZT400 model', () => {
    expect(getMemoryByModel('ZT400')).toBe('16GB RAM');
  });

  it('should return 16GB RAM for ZT600 model', () => {
    expect(getMemoryByModel('ZT600')).toBe('16GB RAM');
  });

  it('should return 32GB RAM for ZT800 model', () => {
    expect(getMemoryByModel('ZT800')).toBe('32GB RAM');
  });

  it('should return 0 for unknown model', () => {
    expect(getMemoryByModel('Unknown')).toBe(0);
  });

  it('should return 0 for empty string model', () => {
    expect(getMemoryByModel('')).toBe(0);
  });

  it('should return 0 for null model', () => {
    expect(getMemoryByModel(null)).toBe(0);
  });

  it('should return 0 for undefined model', () => {
    expect(getMemoryByModel(undefined)).toBe(0);
  });
});
