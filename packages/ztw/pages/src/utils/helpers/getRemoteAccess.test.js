import getRemoteAccess from './getRemoteAccess';

describe('getRemoteAccess function', () => {
  it('should return true for SUPPORT_ACCESS_READ_ONLY', () => {
    expect(getRemoteAccess('SUPPORT_ACCESS_READ_ONLY')).toBe(true);
  });

  it('should return true for SUPPORT_ACCESS_FULL', () => {
    expect(getRemoteAccess('SUPPORT_ACCESS_FULL')).toBe(true);
  });

  it('should return false for other auth types', () => {
    expect(getRemoteAccess('OTHER_AUTH_TYPE')).toBe(false);
    expect(getRemoteAccess('')).toBe(false);
    expect(getRemoteAccess(null)).toBe(false);
    expect(getRemoteAccess(undefined)).toBe(false);
  });
});
