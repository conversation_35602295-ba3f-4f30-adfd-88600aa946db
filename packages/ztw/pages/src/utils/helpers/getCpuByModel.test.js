import getCpuByModel from './getCpuByModel';

describe('getCpuByModel function', () => {
  it('should return 4 for ZT400 model', () => {
    expect(getCpuByModel('ZT400')).toBe(4);
  });

  it('should return 4 for ZT600 model', () => {
    expect(getCpuByModel('ZT600')).toBe(4);
  });

  it('should return 8 for ZT800 model', () => {
    expect(getCpuByModel('ZT800')).toBe(8);
  });

  it('should return 0 for unknown model', () => {
    expect(getCpuByModel('Unknown')).toBe(0);
  });

  it('should return 0 for empty string model', () => {
    expect(getCpuByModel('')).toBe(0);
  });

  it('should return 0 for null model', () => {
    expect(getCpuByModel(null)).toBe(0);
  });

  it('should return 0 for undefined model', () => {
    expect(getCpuByModel(undefined)).toBe(0);
  });
});
