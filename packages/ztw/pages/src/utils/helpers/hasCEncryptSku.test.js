import hasCEncryptSku from './hasCEncryptSku';

describe('hasCEncryptSku function', () => {
  it('should return true when sku contains B_AND_C_CONNECTOR_ENCRYPT', () => {
    const sku = ['B_AND_C_CONNECTOR_ENCRYPT', 'OTHER_SKU'];
    expect(hasCEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains WORKLOAD_TUNNEL_ENCRYPT', () => {
    const sku = ['WORKLOAD_TUNNEL_ENCRYPT', 'OTHER_SKU'];
    expect(hasCEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains both B_AND_C_CONNECTOR_ENCRYPT and WORKLOAD_TUNNEL_ENCRYPT', () => {
    const sku = ['B_AND_C_CONNECTOR_ENCRYPT', 'WORKLOAD_TUNNEL_ENCRYPT', 'OTHER_SKU'];
    expect(hasCEncryptSku(sku)).toBe(true);
  });

  it('should return false when sku does not contain B_AND_C_CONNECTOR_ENCRYPT or WORKLOAD_TUNNEL_ENCRYPT', () => {
    const sku = ['OTHER_SKU1', 'OTHER_SKU2'];
    expect(hasCEncryptSku(sku)).toBe(false);
  });

  it('should return false when sku is empty', () => {
    const sku = [];
    expect(hasCEncryptSku(sku)).toBe(false);
  });
});
