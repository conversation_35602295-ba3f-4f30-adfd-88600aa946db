import convertWorloadTags from './convertWorloadTags';

describe('convertWorloadTags', () => {
  it('should return an empty object when tags are empty', () => {
    const tags = [];
    const resources = {};
    const result = convertWorloadTags(tags, resources);
    expect(result).toEqual({});
  });

  it('should return an object with attributes and userTags when tags are not empty', () => {
    const tags = [
      'attr.key.GroupId.value.123',
      'attr.key.GroupName.value.Test',
      'attr.key.IamInstanceProfile-Arn.value.arn:aws:iam::123456789012:instance-profile/MyInstanceProfile',
      'attr.key.PlatformDetails.value.Linux',
      'attr.key.ImageId.value.ami-12345678',
      'attr.key.Vpc-id.value.vpc-12345678',
      'custom.key.Test.value.TestValue',
    ];
    const resources = {};
    const result = convertWorloadTags(tags, resources);
    expect(result).toEqual({
      attributes: {
        groupId: ['123'],
        groupName: ['Test'],
        iamInstanceProfileArn: 'arn:aws:iam::123456789012:instance-profile/MyInstanceProfile',
        platformDetails: 'Linux',
        imageId: 'ami-12345678',
        vpcId: 'vpc-12345678',
      },
      userTags: [
        {
          name: 'CUSTOM',
          key: 'Test',
          value: 'TestValue',
        },
      ],
    });
  });

  it('should handle tags with missing fields', () => {
    const tags = [
      'attr.key.GroupId.value.123',
      'attr.key.GroupName.value.Test',
      'attr.key.IamInstanceProfile-Arn.value.',
      'attr.key.PlatformDetails.value.',
      'attr.key.ImageId.value.',
      'attr.key.Vpc-id.value.',
      'custom.key.Test.value.TestValue',
    ];
    const resources = {};
    const result = convertWorloadTags(tags, resources);
    expect(result).toEqual({
      attributes: {
        groupId: ['123'],
        groupName: ['Test'],
        iamInstanceProfileArn: '',
        platformDetails: '',
        imageId: '',
        vpcId: '',
      },
      userTags: [
        {
          name: 'CUSTOM',
          key: 'Test',
          value: 'TestValue',
        },
      ],
    });
  });
});
