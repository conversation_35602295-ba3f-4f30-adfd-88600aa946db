import { isEmpty } from 'utils/lodash';

const convertWorloadTags = (tags, resources) => {
  const attributes = resources || {};
  attributes.groupId = [];
  attributes.groupName = [];
  const userTags = [];
  if (isEmpty(tags)) return {};

  const groupIdField = 'attr.key.GroupId.value.';
  const groupNameField = 'attr.key.GroupName.value.';
  const iamInstanceProfileArnField = 'attr.key.IamInstanceProfile-Arn.value.';
  const imageIdField = 'attr.key.ImageId.value.';
  const platformDetailsField = 'attr.key.PlatformDetails.value.';
  const vpcIdField = 'attr.key.Vpc-id.value.';

  // eslint-disable-next-line array-callback-return
  tags.map((tag) => {
    if (tag.includes(groupIdField)) {
      attributes.groupId.push(tag.slice(groupIdField.length));
    } else if (tag.includes(groupNameField)) {
      attributes.groupName.push(tag.slice(groupNameField.length));
    } else if (tag.includes(iamInstanceProfileArnField)) {
      attributes.iamInstanceProfileArn = tag.slice(iamInstanceProfileArnField.length);
    } else if (tag.includes(platformDetailsField)) {
      attributes.platformDetails = tag.slice(platformDetailsField.length);
    } else if (tag.includes(imageIdField)) {
      attributes.imageId = tag.slice(imageIdField.length);
    } else if (tag.includes(vpcIdField)) attributes.vpcId = tag.slice(vpcIdField.length);
    else {
      const firstBreak = tag.split('.key.');
      const secondBreak = firstBreak[1].split('.value.');
      userTags.push({
        name: firstBreak[0].toUpperCase() === 'VPC' ? 'VPC/VNET' : firstBreak[0].toUpperCase(),
        key: secondBreak[0],
        value: secondBreak[1],
      });
    }
  });

  return ({ attributes, userTags });
};
export default convertWorloadTags;
