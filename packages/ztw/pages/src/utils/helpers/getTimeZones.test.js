import getTimeZones from './getTimeZones';

describe('getTimeZones', () => {
  it('should return an array of time zones', () => {
    const timeZones = getTimeZones();
    expect(timeZones).toBeInstanceOf(Array);
  });

  it('should return an array with 477 time zones', () => {
    const timeZones = getTimeZones();
    expect(timeZones.length).toBe(477);
  });

  it('should return an array of objects with value and label properties', () => {
    const timeZones = getTimeZones();
    expect(timeZones.every((timeZone) => typeof timeZone.value === 'string' && typeof timeZone.label === 'string')).toBe(true);
  });

  it('should return an array sorted by label', () => {
    const timeZones = getTimeZones();
    expect(timeZones.every((timeZone, index, array) => index === 0 || timeZone.label >= array[index - 1].label)).toBe(true);
  });

  it('should format labels without a colon', () => {
    const timeZones = getTimeZones();
    expect(timeZones.every((timeZone) => !timeZone.label.includes(':'))).toBe(false);
  });
});
