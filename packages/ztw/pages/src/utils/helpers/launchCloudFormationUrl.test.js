import launchCloudFormationUrl from './launchCloudFormationUrl';

describe('launchCloudFormationUrl', () => {
  it('should return the correct CloudFormation URL', () => {
    const trustedRole = 'arn:aws:iam::************:role/TrustedRole';
    const externalId = 'ExternalId123';
    const awsRoleName = 'AWSRoleName123';

    const expectedUrl = `https://us-east-1.console.aws.amazon.com/cloudformation/home
?region=us-east-1
#/stacks/create/review
?templateURL=https://zscaler-discovery-role.s3.amazonaws.com/zscaler_discovery_role.yaml
&stackName=ZscalerTagDiscoveryTrustingRole
&param_ZscalerTrustedRole=${trustedRole}
&param_ExternalId=${externalId}
&param_TrustingAccountRoleName=${awsRoleName}
`;

    expect(launchCloudFormationUrl({ trustedRole, externalId, awsRoleName })).toBe(expectedUrl);
  });

  it('should handle special characters in the parameters correctly', () => {
    const trustedRole = 'arn:aws:iam::************:role/TrustedRole+with+special+chars';
    const externalId = 'ExternalId123!@#$%^&*()_+-={}:<>?,./';
    const awsRoleName = 'AWSRoleName123!@#$%^&*()_+-={}:<>?,./';

    const expectedUrl = `https://us-east-1.console.aws.amazon.com/cloudformation/home
?region=us-east-1
#/stacks/create/review
?templateURL=https://zscaler-discovery-role.s3.amazonaws.com/zscaler_discovery_role.yaml
&stackName=ZscalerTagDiscoveryTrustingRole
&param_ZscalerTrustedRole=${trustedRole}
&param_ExternalId=${externalId}
&param_TrustingAccountRoleName=${awsRoleName}
`;

    expect(launchCloudFormationUrl({ trustedRole, externalId, awsRoleName })).toBe(expectedUrl);
  });
});
