const getUnitRange = (range, type = 'BYTES') => {
  let scaledUnits = '';
  let factor = 1;
  if (type === 'NUMBER') {
    const tRange = 1000 * 1000 * 1000 * 1000;
    const gRange = 1000 * 1000 * 1000;
    const mRange = 1000 * 1000;
    if (range > tRange) {
      scaledUnits = 'T';
      factor = tRange;
    } else if (range > gRange) {
      scaledUnits = 'G';
      factor = gRange;
    } else if (range > mRange) {
      scaledUnits = 'M';
      factor = mRange;
    } else if (range > 1000) {
      scaledUnits = 'K';
      factor = 1000;
    }
    return {
      scaledUnits,
      factor,
    };
  }
  // type = 'BYTES'
  const tRange = 1024 * 1024 * 1024 * 1024;
  const gRange = 1024 * 1024 * 1024;
  const mRange = 1024 * 1024;
  if (range > tRange) {
    // 1099511627776 (1024 * 1024 * 1024 * 1024)
    scaledUnits = 'Tbps';
    factor = tRange;
  } else if (range > gRange) {
    // 1073741824(1024 * 1024 * 1024)
    scaledUnits = 'Gbps';
    factor = gRange;
  } else if (range > mRange) {
    // 1048576 (1024 * 1024)
    scaledUnits = 'Mbps';
    factor = mRange;
  } else if (range > 1024) {
    scaledUnits = 'Kbps';
    factor = 1024;
  }
  return {
    scaledUnits,
    factor,
  };
};
          
export default getUnitRange;
