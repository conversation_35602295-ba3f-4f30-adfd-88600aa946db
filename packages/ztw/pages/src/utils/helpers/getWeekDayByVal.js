const getWeekDayByVal = (value, t) => {
  const listWeek = [
    { id: 'MONDAY', name: t('MONDAY'), val: 2 },
    { id: 'TUESDAY', name: t('TUESDAY'), val: 3 },
    { id: 'WEDNESDAY', name: t('WEDNESDAY'), val: 4 },
    { id: 'THURSDAY', name: t('THURSDAY'), val: 5 },
    { id: 'FRIDAY', name: t('FRIDAY'), val: 6 },
    { id: 'SATURDAY', name: t('SATURDAY'), val: 7 },
    { id: 'SUNDAY', name: t('SUNDAY'), val: 1 },
  ];

  return listWeek.find((x) => x.val === value);
};

export default getWeekDayByVal;
