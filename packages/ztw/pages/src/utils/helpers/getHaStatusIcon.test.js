import React from 'react';
import { render } from '@testing-library/react';
import getHaStatusIcon from './getHaStatusIcon';

describe('getHaStatusIcon component', () => {
  it('renders with ENABLE status', () => {
    const { getByText, container } = render(getHaStatusIcon('ENABLE'));
    expect(getByText('Enable')).toBeInTheDocument();
    expect(container.querySelector('.os-green-circle')).toBeInTheDocument();
  });

  it('renders with DISABLED status', () => {
    const { getByText, container } = render(getHaStatusIcon('DISABLED'));
    expect(getByText('Disabled')).toBeInTheDocument();
    expect(container.querySelector('.os-gray-circle')).toBeInTheDocument();
  });

  it('renders with STANDBY status', () => {
    const { getByText, container } = render(getHaStatusIcon('STANDBY'));
    expect(getByText('Standby')).toBeInTheDocument();
    expect(container.querySelector('.os-yellow-circle')).toBeInTheDocument();
  });

  it('renders with default status', () => {
    const { getByText, container } = render(getHaStatusIcon());
    expect(getByText('---')).toBeInTheDocument();
    expect(container.querySelector('.container-row-ha-status')).toBeInTheDocument();
  });

  it('renders with empty status', () => {
    const { getByText, container } = render(getHaStatusIcon(''));
    expect(getByText('---')).toBeInTheDocument();
    expect(container.querySelector('.container-row-ha-status')).toBeInTheDocument();
  });
});
