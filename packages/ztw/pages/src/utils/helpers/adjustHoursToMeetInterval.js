import PropTypes from 'prop-types';
import convertAMPMtoMinutes from './convertAMPMtoMinutes';
import convertMinutesToAMPM from './convertMinutesToAMPM';

function adjustHoursToMeetInterval(hFrom, mFrom, periodFrom, hTo, mTo, periodTo, baseValue, interval = 60) {
  const fromMinutes = convertAMPMtoMinutes(hFrom, mFrom, periodFrom);
  const toMinutes = convertAMPMtoMinutes(hTo, mTo, periodTo);
  const realTimeInterval = toMinutes - fromMinutes < 0
    ? (24 * 60) + (toMinutes - fromMinutes)
    : (toMinutes - fromMinutes);

  let adjustedTime = {};
  if (realTimeInterval < interval) {
    if (baseValue === 'FROM') {
      const newTime = (fromMinutes + interval) % (24 * 60);
      adjustedTime = convertMinutesToAMPM(newTime);
    } else {
      const newTime = toMinutes - interval < 0
        ? (24 * 60 + (toMinutes - interval))
        : toMinutes - interval;
      adjustedTime = convertMinutesToAMPM(newTime);
    }
  }

  return adjustedTime;
}

adjustHoursToMeetInterval.propTypes = {
  hFrom: PropTypes.number.required,
  mFrom: PropTypes.number.required,
  periodFrom: PropTypes.number.required,
  hTo: PropTypes.number.required,
  mTo: PropTypes.number.required,
  periodTo: PropTypes.oneOf(['AM', 'PM']).required,
  baseValue: PropTypes.oneOf(['FROM', 'TO']).required,
  interval: PropTypes.number,
};
  
adjustHoursToMeetInterval.defaultProps = {
  interval: 60,
};

export default adjustHoursToMeetInterval;
