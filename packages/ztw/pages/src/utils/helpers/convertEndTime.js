import moment from 'moment-timezone';

const convertEndTime = (timeFrame) => {
  // default - Current Time
  let convertedTime = moment().unix() * 1000;

  if (timeFrame === 'previous_day') {
    convertedTime = moment().subtract(1, 'days').endOf('day').unix() * 1000;
  } else if (timeFrame === 'previous_week') {
    convertedTime = moment().subtract(1, 'week').endOf('week').unix() * 1000;
  } else if (timeFrame === 'previous_month') {
    convertedTime = moment().subtract(1, 'month').endOf('month').unix() * 1000;
  }
  return convertedTime;
};
  
export default convertEndTime;
