import isZPA from './isZPA';

describe('isZPA function', () => {
  it('should return true when forwarding method is ECZPA', () => {
    expect(isZPA('ECZPA')).toBe(true);
  });

  it('should return true when forwarding method is ECZPASCTPXFORM', () => {
    expect(isZPA('ECZPASCTPXFORM')).toBe(true);
  });

  it('should return false when forwarding method is not ECZPA or ECZPASCTPXFORM', () => {
    expect(isZPA('OTHER_METHOD')).toBe(false);
  });

  it('should return false when forwarding method is null', () => {
    expect(isZPA(null)).toBe(false);
  });

  it('should return false when forwarding method is undefined', () => {
    expect(isZPA(undefined)).toBe(false);
  });

  it('should return false when forwarding method is an empty string', () => {
    expect(isZPA('')).toBe(false);
  });
});
