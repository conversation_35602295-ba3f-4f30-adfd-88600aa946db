import validatePassword from './validatePassword';

describe('validatePassword function', () => {
  it('should return an empty object when status is disabled', () => {
    const values = { status: { id: 'disabled' } };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({});
  });

  it('should return an error when locationName is empty and newLocationCreateAllowed is false', () => {
    const values = { scope: { id: 'LOCATION' }, locationName: '', newLocationCreateAllowed: false };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({ locationName: 'REQUIRED' });
  });

  it('should not check for password when addAdmin is false and isPasswordLoginAllowed is true and originalIsPasswordLoginAllowed is the same', () => {
    const values = {
      addAdmin: false, isPasswordLoginAllowed: true, password: '', confirmPassword: '',
    };
    const props = { initialValues: { isPasswordLoginAllowed: true } };
    const result = validatePassword(values, props);
    expect(result).toEqual({});
  });

  it('should return an error when password is required and hasGoodPasswordStrength returns an error', () => {
    const values = { isPasswordLoginAllowed: true, password: 'weak', confirmPassword: 'weak' };
    const props = { initialValues: { isPasswordLoginAllowed: true } };
    const result = validatePassword(values, props);
    expect(result).toEqual({ password: 'PASSWORD_STRENGTH_REQUIRED', confirmPassword: 'PASSWORD_STRENGTH_REQUIRED' });
  });

  it('should return an error when confirmPassword is required and hasGoodPasswordStrength returns an error', () => {
    const values = { isPasswordLoginAllowed: true, password: 'weak', confirmPassword: 'weak' };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({ password: 'PASSWORD_STRENGTH_REQUIRED', confirmPassword: 'PASSWORD_STRENGTH_REQUIRED' });
  });

  it('should return an error when password is required', () => {
    const values = { isPasswordLoginAllowed: true, confirmPassword: 'weak' };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({ password: 'PASSWORD_DONT_MATCH', confirmPassword: 'PASSWORD_DONT_MATCH' });
  });

  it('should return an error when confirmPassword is required', () => {
    const values = { isPasswordLoginAllowed: true, confirmPassword: '' };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({ password: 'PASSWORD_DONT_MATCH', confirmPassword: 'PASSWORD_DONT_MATCH' });
  });

  it('should return an error when password and confirmPassword do not match', () => {
    const values = { isPasswordLoginAllowed: true, password: 'password', confirmPassword: 'different' };
    const props = {};
    const result = validatePassword(values, props);
    expect(result).toEqual({ password: 'PASSWORD_DONT_MATCH', confirmPassword: 'PASSWORD_DONT_MATCH' });
  });
});
