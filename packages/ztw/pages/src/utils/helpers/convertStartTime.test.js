import moment from 'moment-timezone';
import convertStartTime from './convertStartTime';

describe('convertStartTime function', () => {
  beforeEach(() => {
    // Mock the current time to ensure consistent results
    jest.spyOn(moment, 'now').mockImplementation(() => 1643723400000);
  });

  afterEach(() => {
    // Restore the original implementation
    moment.now.mockRestore();
  });

  it('should return the default time (last 24 hours) when no timeFrame is provided', () => {
    const result = convertStartTime();
    expect(result).toBe(moment().subtract(24, 'hours').unix() * 1000);
  });

  it('should return the start of the current day', () => {
    const result = convertStartTime('current_day');
    expect(result).toBe(moment().startOf('day').unix() * 1000);
  });

  it('should return the start of the current week', () => {
    const result = convertStartTime('current_week');
    expect(result).toBe(moment().startOf('week').unix() * 1000);
  });

  it('should return the start of the current month', () => {
    const result = convertStartTime('current_month');
    expect(result).toBe(moment().startOf('month').unix() * 1000); // 2022-01-01T00:00:00.000Z
  });

  it('should return the start of the previous day', () => {
    const result = convertStartTime('previous_day');
    expect(result).toBe(moment().subtract(1, 'days').startOf('day').unix() * 1000); // 2022-01-31T00:00:00.000Z
  });

  it('should return the start of the previous week', () => {
    const result = convertStartTime('previous_week');
    expect(result).toBe(moment().subtract(1, 'week').startOf('week').unix() * 1000); // 2022-01-24T00:00:00.000Z
  });

  it('should return the start of the previous month', () => {
    const result = convertStartTime('previous_month');
    expect(result).toBe(moment().subtract(1, 'month').startOf('month').unix() * 1000); // 2021-12-01T00:00:00.000Z
  });

  it('should return the time 1 minute ago', () => {
    const result = convertStartTime('last_1_min');
    expect(result).toBe(moment().subtract(1, 'minutes').unix() * 1000); // 2022-02-01T11:59:00.000Z
  });

  it('should return the time 2 minutes ago', () => {
    const result = convertStartTime('last_2_mins');
    expect(result).toBe(moment().subtract(2, 'minutes').unix() * 1000); // 2022-02-01T11:58:00.000Z
  });

  it('should return the time 5 minutes ago', () => {
    const result = convertStartTime('last_5_mins');
    expect(result).toBe(moment().subtract(5, 'minutes').unix() * 1000); // 2022-02-01T11:55:00.000Z
  });

  it('should return the time 15 minutes ago', () => {
    const result = convertStartTime('last_15_mins');
    expect(result).toBe(moment().subtract(15, 'minutes').unix() * 1000); // 2022-02-01T11:45:00.000Z
  });

  it('should return the time 30 minutes ago', () => {
    const result = convertStartTime('last_30_mins');
    expect(result).toBe(moment().subtract(30, 'minutes').unix() * 1000); // 2022-02-01T11:30:00.000Z
  });

  it('should return the time 1 hour ago', () => {
    const result = convertStartTime('last_1_hour');
    expect(result).toBe(moment().subtract(60, 'minutes').unix() * 1000); // 2022-02-01T11:00:00.000Z
  });

  it('should return the time 2 hours ago', () => {
    const result = convertStartTime('last_2_hours');
    expect(result).toBe(moment().subtract(120, 'minutes').unix() * 1000); // 2022-02-01T10:00:00.000Z
  });

  it('should return the time 5 hours ago', () => {
    const result = convertStartTime('last_5_hours');
    expect(result).toBe(moment().subtract(300, 'minutes').unix() * 1000); // 2022-02-01T07:00:00.000Z
  });

  it('should return the time 10 hours ago', () => {
    const result = convertStartTime('last_10_hours');
    expect(result).toBe(moment().subtract(600, 'minutes').unix() * 1000); // 2022-02-01T02:00:00.000Z
  });

  it('should return the time 1 week ago', () => {
    const result = convertStartTime('last_1_week');
    expect(result).toBe(moment().subtract(1, 'week').unix() * 1000); // 2022-01-25T12:00:00.000Z
  });

  it('should return the time 1 month ago', () => {
    const result = convertStartTime('last_1_month');
    expect(result).toBe(moment().subtract(1, 'month').unix() * 1000); // 2022-01-01T12:00:00.000Z
  });
});
