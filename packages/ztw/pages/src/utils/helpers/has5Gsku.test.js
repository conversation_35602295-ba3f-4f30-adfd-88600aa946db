import has5Gsku from './has5Gsku';

describe('has5Gsku function', () => {
  it('should return true when skuArray contains ZT_BC_CELLULAR_PRE', () => {
    const skuArray = ['ZT_BC_CELLULAR_PRE', 'other_sku'];
    expect(has5Gsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray contains ZT_CC_CELLULAR_PRE', () => {
    const skuArray = ['other_sku', 'ZT_CC_CELLULAR_PRE'];
    expect(has5Gsku(skuArray)).toBe(true);
  });

  it('should return false when skuArray does not contain ZT_BC_CELLULAR_PRE or ZT_CC_CELLULAR_PRE', () => {
    const skuArray = ['other_sku1', 'other_sku2'];
    expect(has5Gsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is empty', () => {
    const skuArray = [];
    expect(has5Gsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is null or undefined', () => {
    expect(has5Gsku(null)).toBe(false);
    expect(has5Gsku(undefined)).toBe(false);
  });
});
