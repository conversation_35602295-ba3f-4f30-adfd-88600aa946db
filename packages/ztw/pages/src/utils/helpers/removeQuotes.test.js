// removeQuotes.test.js
import removeQuotes from './removeQuotes';

describe('removeQuotes function', () => {
  it('removes double quotes from a string', () => {
    const input = '"Hello World"';
    const expectedOutput = 'Hello World';
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('removes single quotes from a string', () => {
    const input = "'Hello World'";
    const expectedOutput = 'Hello World';
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('does not modify a string without quotes', () => {
    const input = 'Hello World';
    const expectedOutput = 'Hello World';
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('does not modify a string with only one quote', () => {
    const input = '"Hello World';
    const expectedOutput = '"Hello World';
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('does not modify a string with only one double quote', () => {
    const input = "'Hello World";
    const expectedOutput = "'Hello World";
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('handles an empty string', () => {
    const input = '';
    const expectedOutput = '';
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('handles a null input', () => {
    const input = null;
    const expectedOutput = null;
    expect(removeQuotes(input)).toBe(expectedOutput);
  });

  it('handles an undefined input', () => {
    const input = undefined;
    const expectedOutput = undefined;
    expect(removeQuotes(input)).toBe(expectedOutput);
  });
});
