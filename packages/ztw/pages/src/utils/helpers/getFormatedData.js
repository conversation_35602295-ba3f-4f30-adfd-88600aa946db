import getNumberRounded from './getNumberRounded';

/**
 *
 * @param {'K' | 'M' | 'B' | 'T' | 'Q' | 'QT' | 'KB' | 'MB' | 'GB' | 'TB' |
 *  'PB' | 'EB' | 'ZB' | 'YB'} unit K thousands, M millions
 */
export const unitLabel = (unit) => {
  let label = unit;

  if (unit === 'K') {
    label = unit + ' (1000)';
  } else if (unit === 'M') {
    label = unit + ' (1,000,000)';
  } else if (unit === 'KB') {
    label = unit + ' (1000 bytes)';
  } else if (unit === 'MB') {
    label = unit + ' (1000 KB)';
  } else if (unit === 'GB') {
    label = unit + ' (1000 MB)';
  }

  return label;
};

/**
 *
 * @param {'K' | 'M' | 'B' | 'T' | 'Q' | 'QT' | 'KB' | 'MB' | 'GB' | 'TB'
 *  | 'PB' | 'EB' | 'ZB' | 'YB'} unit K thousands, M millions
 */
export const formatNumber = (data, unit) => {
  let source = data;

  if (typeof source === 'number') {
    if (unit === 'K') {
      source = getNumberRounded(source / (10 ** 3), 2);
    } else if (unit === 'M') {
      source = getNumberRounded(source / (10 ** 6), 2);
    } else if (unit === 'B') {
      source = getNumberRounded(source / (10 ** 9), 2);
    } else if (unit === 'T') {
      source = getNumberRounded(source / (10 ** 12), 2);
    } else if (unit === 'Q') {
      // Q - quadrillion
      source = getNumberRounded(source / (10 ** 15), 2);
    } else if (unit === 'QT') {
      // QT - quintillion
      source = getNumberRounded(source / (10 ** 18), 2);
    } else if (unit === 'KB') {
      source = getNumberRounded(source / 1024, 2);
    } else if (unit === 'MB') {
      source = getNumberRounded(source / (1024 ** 2), 2);
    } else if (unit === 'GB') {
      source = getNumberRounded(source / (1024 ** 3), 2);
    } else if (unit === 'TB') {
      source = getNumberRounded(source / (1024 ** 4), 2);
    } else if (unit === 'PB') {
      source = getNumberRounded(source / (1024 ** 5), 2);
    } else if (unit === 'EB') {
      source = getNumberRounded(source / (1024 ** 6), 2);
    } else if (unit === 'ZB') {
      source = getNumberRounded(source / (1024 ** 7), 2);
    } else if (unit === 'YB') {
      source = getNumberRounded(source / (1024 ** 8), 2);
    }
  }

  return source;
};

/**
 * Sort the data excluding 0 as this would not
 */
export const getMinMaxInData = (source, dataKey) => {
  let minNumber = 0;
  let maxNumber = 0;

  const filteredSortedData = source
    .filter((data) => (dataKey ? data[dataKey] !== 0 : data !== 0))
    .sort((first, second) => (dataKey ? first[dataKey] - second[dataKey] : first - second));

  if (filteredSortedData.length) {
    if (dataKey) {
      minNumber = filteredSortedData[0][dataKey];
      maxNumber = filteredSortedData[filteredSortedData.length - 1][dataKey];
    } else {
      [minNumber] = filteredSortedData;
      maxNumber = filteredSortedData[filteredSortedData.length - 1];
    }
  }

  return [minNumber, maxNumber];
};

export const getBestUnitForRange = (min, max, type) => {
  let bestUnit = '';

  const maxNumberLength = (max + '').length;
  const minMaxDifferenceLength = (Number.parseInt(Math.abs(max - min), 10) + '').length;

  if (type === 'NUMBER') {
    if (minMaxDifferenceLength >= 9) {
      bestUnit = 'M';
    } else if (minMaxDifferenceLength >= 6) {
      bestUnit = 'K';
    } else if (maxNumberLength >= 9) {
      bestUnit = 'M';
    } else if (maxNumberLength >= 6) {
      bestUnit = 'K';
    }
  }

  if (type === 'BYTES') {
    if (minMaxDifferenceLength >= 18) {
      bestUnit = 'TB';
    } else if (minMaxDifferenceLength >= 12) {
      bestUnit = 'GB';
    } else if (minMaxDifferenceLength >= 6) {
      bestUnit = 'MB';
    } else if (maxNumberLength > 18) {
      bestUnit = 'PB';
    } else if (maxNumberLength > 15) {
      bestUnit = 'TB';
    } else if (maxNumberLength > 12) {
      bestUnit = 'GB';
    } else if (maxNumberLength > 9) {
      bestUnit = 'MB';
    } else if (maxNumberLength > 6) {
      bestUnit = 'KB';
    }
  }

  return bestUnit;
};

export const getBestUnitForANumber = (number, type) => {
  let bestUnit = '';

  const numberLength = (Number.parseInt(Math.abs(number), 10) + '').length;

  if (type === 'NUMBER') {
    if (numberLength > 19) {
      bestUnit = 'QT';
    } else if (numberLength > 15) {
      bestUnit = 'Q';
    } else if (numberLength > 12) {
      bestUnit = 'T';
    } else if (numberLength > 9) {
      bestUnit = 'B';
    } else if (numberLength > 6) {
      bestUnit = 'M';
    } else if (numberLength > 3) {
      bestUnit = 'K';
    }
  }

  if (type === 'BYTES') {
    if (numberLength >= 17) {
      bestUnit = 'PB';
    } else if (numberLength >= 14) {
      bestUnit = 'TB';
    } else if (numberLength >= 10) {
      bestUnit = 'GB';
    } else if (numberLength >= 7) {
      bestUnit = 'MB';
    } else if (numberLength >= 4) {
      bestUnit = 'KB';
    }
  }

  return bestUnit;
};

export const getFormatedNumberWithUnit = (num, config) => {
  const { type, dataKey, unit } = config;

  const unitToDisplay = unit || getBestUnitForANumber(typeof num === 'number' ? num : num[dataKey], type);

  if (typeof num === 'number') {
    return formatNumber(num, unitToDisplay) + ` ${unitToDisplay}`;
  }

  if (dataKey in num) {
    const source = num;

    source.raw = num[dataKey];
    source[dataKey] = formatNumber(num[dataKey], unitToDisplay) + `${unitToDisplay}`;
    source.unit = unitToDisplay;
    source.unitLabel = unitLabel(unitToDisplay);
    source.type = type;

    return source;
  }

  return num;
};

/**
 *
 * @param {number[] | object[]} source
 * @param {object} config config.unit for explicit conversion
 * @param {'NUMBER' | 'BYTES'} [config.type] data type
 * @param {string} config.dataKey Key for accessing data
 * @param {'K' | 'M' | 'B' | 'T' | 'Q' | 'QT' | 'KB' | 'MB' | 'GB' | 'TB' |
 *  'PB' | 'EB' | 'ZB' | 'YB'} config.unit for explicit conversion
 */
export const formatNumberList = (source, config) => {
  if (Array.isArray(source) && source.length && config) {
    const { type, dataKey, unit } = config;

    const unitToDisplay = unit || getBestUnitForRange(...getMinMaxInData(source, dataKey), type);

    const formatedList = source.map((data) => {
      if (typeof data === 'number') {
        return formatNumber(data, unit);
      }

      if (dataKey in data) {
        const tempData = data;

        tempData.raw = data[dataKey];
        tempData[dataKey] = formatNumber(tempData[dataKey], unitToDisplay);
        tempData.unit = unitToDisplay;
        tempData.unitLabel = unitLabel(unitToDisplay);
        tempData.type = type;

        return tempData;
      }

      return data;
    });

    return formatedList;
  }

  return [];
};
