// getNumericData.test.js
import getNumericData from './getNumericData';

describe('getNumericData function', () => {
  it('should return 0 when array is null or undefined', () => {
    expect(getNumericData('inbound', 'total', null)).toBe(0);
    expect(getNumericData('inbound', 'total', undefined)).toBe(0);
  });

  it('should return 0 when mapKey is not in array', () => {
    const arr = { outbound: { total: 100 } };
    expect(getNumericData('inbound', 'total', arr)).toBe(0);
  });

  it('should return NaN when dataKey is not in mapKey object', () => {
    const arr = { inbound: { otherKey: 100 } };
    expect(getNumericData('inbound', 'total', arr)).toBe(NaN);
  });

  it('should return numeric value when mapKey and dataKey exist in array', () => {
    const arr = { inbound: { total: '100' } };
    expect(getNumericData('inbound', 'total', arr)).toBe(100);
  });

  it('should return numeric value when mapKey and dataKey exist in array with non-string value', () => {
    const arr = { inbound: { total: 100 } };
    expect(getNumericData('inbound', 'total', arr)).toBe(100);
  });

  it('should return NaN when mapKey and dataKey exist in array but value is not numeric', () => {
    const arr = { inbound: { total: 'abc' } };
    expect(getNumericData('inbound', 'total', arr)).toBe(NaN);
  });
});
