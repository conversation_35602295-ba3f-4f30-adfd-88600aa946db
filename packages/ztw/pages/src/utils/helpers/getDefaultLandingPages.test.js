import { BASE_LAYOUT } from 'config';
import getDefaultLandingPages from './getDefaultLandingPages';

describe('getDefaultLandingPages', () => {
  it('should return the default landing page URL', () => {
    const expectedUrl = `${BASE_LAYOUT}/dashboard/connector-monitoring`;
    expect(getDefaultLandingPages()).toBe(expectedUrl);
  });

  it('should return a string', () => {
    expect(typeof getDefaultLandingPages()).toBe('string');
  });

  it('should include the BASE_LAYOUT in the returned URL', () => {
    expect(getDefaultLandingPages()).toContain(BASE_LAYOUT);
  });
});
