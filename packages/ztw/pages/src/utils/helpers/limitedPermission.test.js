import limitedPermission from './limitedPermission';

describe('limitedPermission function', () => {
  it('should return the original data when isReadOnly is false', () => {
    // Arrange
    const data = {
      featurePermissions: {
        key1: 'READ_WRITE',
        key2: 'READ_ONLY',
      },
    };

    // Act
    const result = limitedPermission(data, false);

    // Assert
    expect(result).toEqual(data);
  });

  it('should update featurePermissions to READ_ONLY when isReadOnly is true and permission is READ_WRITE', () => {
    // Arrange
    const data = {
      featurePermissions: {
        key1: 'READ_WRITE',
        key2: 'READ_ONLY',
      },
    };

    // Act
    const result = limitedPermission(data, true);

    // Assert
    expect(result.featurePermissions).toEqual({
      key1: 'READ_ONLY',
      key2: 'READ_ONLY',
    });
  });

  it('should not update featurePermissions when isReadOnly is true and permission is not READ_WRITE', () => {
    // Arrange
    const data = {
      featurePermissions: {
        key1: 'READ_ONLY',
        key2: 'WRITE_ONLY',
      },
    };

    // Act
    const result = limitedPermission(data, true);

    // Assert
    expect(result.featurePermissions).toEqual(data.featurePermissions);
  });

  it('should return a new object with updated featurePermissions', () => {
    // Arrange
    const data = {
      featurePermissions: {
        key1: 'READ_WRITE',
        key2: 'READ_ONLY',
      },
    };

    // Act
    const result = limitedPermission(data, true);

    // Assert
    expect(result).not.toBe(data);
    expect(result).toEqual({ ...data, featurePermissions: result.featurePermissions });
  });
});
