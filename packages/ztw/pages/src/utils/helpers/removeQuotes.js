/**
 * Removes quotes or double quotes from a string.
 *
 * @param {string} str - The input string.
 * @returns {string} The string without quotes or double quotes.
 */
function removeQuotes(str) {
  if (!str) return str;
  // Check if the string starts and ends with quotes or double quotes
  if (str.startsWith('"') && str.endsWith('"')) {
    // Remove the quotes
    return str.slice(1, -1);
  } if (str.startsWith("'") && str.endsWith("'")) {
    // Remove the double quotes
    return str.slice(1, -1);
  }
  // If the string does not start and end with quotes or double quotes, return the original string
  return str;
}
  
export default removeQuotes;
