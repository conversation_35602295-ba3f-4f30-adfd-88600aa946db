import convertPortObjectToString from './convertPortObjectToString';

describe('convertPortObjectToString', () => {
  it('should convert a single port object to a string', () => {
    const ports = [{ start: 7000, end: 7010 }];
    const expected = ['7000-7010'];
    expect(convertPortObjectToString(ports)).toEqual(expected);
  });

  it('should convert multiple port objects to strings', () => {
    const ports = [
      { start: 7000, end: 7010 },
      { start: 7020, end: 7030 },
      { start: 7040, end: 7050 },
    ];
    const expected = ['7000-7010', '7020-7030', '7040-7050'];
    expect(convertPortObjectToString(ports)).toEqual(expected);
  });

  it('should convert a port object with only start property to a string', () => {
    const ports = [{ start: 7000 }];
    const expected = ['7000'];
    expect(convertPortObjectToString(ports)).toEqual(expected);
  });

  it('should convert a port object with only end property to a string', () => {
    const ports = [7000];
    const expected = ['7000'];
    expect(convertPortObjectToString(ports)).toEqual(expected);
  });

  it('should convert an empty array to an empty array', () => {
    const ports = [];
    const expected = [];
    expect(convertPortObjectToString(ports)).toEqual(expected);
  });
});
