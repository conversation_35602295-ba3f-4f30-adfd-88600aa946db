import convertAMPMtoMinutes from './convertAMPMtoMinutes';

test('convertAMPMtoMinutes', () => {
  // Test cases
  // Test case 1: MidNight
  expect(convertAMPMtoMinutes(12, 0, 'AM')).toBe(0);
  // Test case 2: Between Midnight AND Midday
  expect(convertAMPMtoMinutes(5, 50, 'AM')).toBe(350);
  // Test case 3: Midday
  expect(convertAMPMtoMinutes(12, 0, 'PM')).toBe(720);
  // Test case 4: Between Midday AND Midnight
  expect(convertAMPMtoMinutes(5, 50, 'PM')).toBe(1070);
});
