import moment from 'moment-timezone';
import getTrend from './getTrend';

describe('getTrend function', () => {
  it('should return the holder array when it is an array of objects', () => {
    const trend = {
      trendStartTime: moment().unix(),
      trendValues: [10, 20, 30],
      trendInterval: 86400000,
    };
    const holder = [
      { plotElement: 0 },
      { plotElement: 0 },
      { plotElement: 0 },
    ];
    const plotElement = 'plotElement';

    const result = getTrend(trend, holder, plotElement);
    expect(result).toEqual([
      { plotElement: 10 },
      { plotElement: 20 },
      { plotElement: 30 },
    ]);
  });

  it('should return a new array of chart items when holder is not an array of objects', () => {
    const trend = {
      trendStartTime: moment().unix(),
      trendValues: [10, 20, 30],
      trendInterval: 86400000,
    };
    const holder = [];
    const plotElement = 'plotElement';

    const result = getTrend(trend, holder, plotElement);
    expect(result).toHaveLength(3);
    expect(result[0]).toHaveProperty('name');
    expect(result[0]).toHaveProperty('orgTime');
    expect(result[0]).toHaveProperty('plotElement');
  });

  it('should handle daily trend interval', () => {
    const trend = {
      trendStartTime: moment().unix(),
      trendValues: [10, 20, 30],
      trendInterval: 86400000,
    };
    const holder = [];
    const plotElement = 'plotElement';

    const result = getTrend(trend, holder, plotElement);
    expect(result[0].name).toBe(moment(trend.trendStartTime).format('MM/DD'));
    expect(result[1].name).toBe(moment(trend.trendStartTime).add(1, 'day').format('MM/DD'));
    expect(result[2].name).toBe(moment(trend.trendStartTime).add(2, 'day').format('MM/DD'));
  });

  it('should handle hourly trend interval', () => {
    const trend = {
      trendStartTime: moment().unix(),
      trendValues: [10, 20, 30],
      trendInterval: 3600000,
    };
    const holder = [];
    const plotElement = 'plotElement';

    const result = getTrend(trend, holder, plotElement);
    expect(result[0].name).toBe(moment(trend.trendStartTime).add(1, 'hour').format('HH:mm'));
    expect(result[1].name).toBe(moment(trend.trendStartTime).add(2, 'hour').format('HH:mm'));
    expect(result[2].name).toBe(moment(trend.trendStartTime).add(3, 'hour').format('HH:mm'));
  });

  it('should handle millisecond trend interval', () => {
    const trend = {
      trendStartTime: moment().unix(),
      trendValues: [10, 20, 30],
      trendInterval: 1000,
    };
    const holder = [];
    const plotElement = 'plotElement';

    const result = getTrend(trend, holder, plotElement);
    expect(result[0].name).toBe(moment(trend.trendStartTime).add(1, 'second').format('HH:mm'));
    expect(result[1].name).toBe(moment(trend.trendStartTime).add(2, 'second').format('HH:mm'));
    expect(result[2].name).toBe(moment(trend.trendStartTime).add(3, 'second').format('HH:mm'));
  });
});
