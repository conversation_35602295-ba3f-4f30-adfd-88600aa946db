import { render } from '@testing-library/react';
import getVmNames from './getVmNames';

describe('getVmNames function', () => {
  it('returns an array of span elements with vm names', () => {
    // Arrange
    const array = [
      { name: 'vm1' },
      { name: 'vm2' },
      { name: 'vm3' },
    ];

    // Act
    const result = getVmNames(array);

    // Assert
    expect(result).toHaveLength(3);
    expect(result[0].props.children).toBe('vm1');
    expect(result[1].props.children).toBe('vm2');
    expect(result[2].props.children).toBe('vm3');
  });

  it('returns an empty array when input array is empty', () => {
    // Arrange
    const array = [];

    // Act
    const result = getVmNames(array);

    // Assert
    expect(result).toHaveLength(0);
  });

  it('returns an array with a single span element when input array has one element', () => {
    // Arrange
    const array = [{ name: 'vm1' }];

    // Act
    const result = getVmNames(array);

    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].props.children).toBe('vm1');
  });

  it('renders span elements with correct class name', () => {
    // Arrange
    const array = [{ name: 'vm1' }];

    // Act
    const { getByText } = render(getVmNames(array));

    // Assert
    expect(getByText('vm1')).toHaveClass('vm-name');
  });
});
