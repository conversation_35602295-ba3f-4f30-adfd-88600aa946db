import React from 'react';
import { render } from '@testing-library/react';
import { configure } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/pro-regular-svg-icons';
import getIcon from './getIcon';

jest.mock('images/aws.png', () => 'aws.png');
jest.mock('images/awsActive.png', () => 'awsActive.png');
jest.mock('images/awsInactive.png', () => 'awsInactive.png');
jest.mock('images/azure.png', () => 'azure.png');
jest.mock('images/azureActive.png', () => 'azureActive.png');
jest.mock('images/azureInactive.png', () => 'azureInactive.png');
jest.mock('images/gcp.png', () => 'gcp.png');
jest.mock('images/gcpActive.png', () => 'gcpActive.png');
jest.mock('images/gcpInactive.png', () => 'gcpInactive.png');

jest.mock('@fortawesome/react-fontawesome', () => ({
  FontAwesomeIcon: () => <div>FontAwesomeIcon</div>,
}));

describe('getIcon function', () => {
  it('should return AWS icon when deploymentType is AWS and no status', () => {
    const { getByAltText } = render(getIcon('AWS'));
    expect(getByAltText('AWS')).toBeInTheDocument();
  });

  it('should return AWS Active icon when deploymentType is AWS and status is Active', () => {
    const { getByAltText } = render(getIcon('AWS', 'Active'));
    expect(getByAltText('AWS Active')).toBeInTheDocument();
  });

  it('should return AWS Inactive icon when deploymentType is AWS and status is Inactive', () => {
    const { getByAltText } = render(getIcon('AWS', 'Inactive'));
    expect(getByAltText('AWS Inactive')).toBeInTheDocument();
  });

  it('should return Azure icon when deploymentType is AZURE and no status', () => {
    const { getByAltText } = render(getIcon('AZURE'));
    expect(getByAltText('Azure')).toBeInTheDocument();
  });

  it('should return Azure Active icon when deploymentType is AZURE and status is Active', () => {
    const { getByAltText } = render(getIcon('AZURE', 'Active'));
    expect(getByAltText('Azure Active')).toBeInTheDocument();
  });

  it('should return Azure Inactive icon when deploymentType is AZURE and status is Inactive', () => {
    const { getByAltText } = render(getIcon('AZURE', 'Inactive'));
    expect(getByAltText('Azure Inactive')).toBeInTheDocument();
  });

  it('should return GCP icon when deploymentType is GCP and no status', () => {
    const { getByAltText } = render(getIcon('GCP'));
    expect(getByAltText('GCP')).toBeInTheDocument();
  });

  it('should return GCP Active icon when deploymentType is GCP and status is Active', () => {
    const { getByAltText } = render(getIcon('GCP', 'Active'));
    expect(getByAltText('GCP Active')).toBeInTheDocument();
  });

  it('should return GCP Inactive icon when deploymentType is GCP and status is Inactive', () => {
    const { getByAltText } = render(getIcon('GCP', 'Inactive'));
    expect(getByAltText('GCP Inactive')).toBeInTheDocument();
  });

  it('should return Branch icon when deviceType is PHYSICAL and no status', () => {
    const { getByText } = render(getIcon('', '', 'PHYSICAL'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Active icon when deviceType is PHYSICAL and status is Active', () => {
    const { getByText } = render(getIcon('', 'Active', 'PHYSICAL'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Inactive icon when deviceType is PHYSICAL and status is Inactive', () => {
    const { getByText } = render(getIcon('', 'Inactive', 'PHYSICAL'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch icon when deploymentType is CENTOS and no status', () => {
    const { getByText } = render(getIcon('CENTOS'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Active icon when deploymentType is CENTOS and status is Active', () => {
    const { getByText } = render(getIcon('CENTOS', 'Active'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Inactive icon when deploymentType is CENTOS and status is Inactive', () => {
    const { getByText } = render(getIcon('CENTOS', 'Inactive'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch icon when deploymentType is REDHAT_LINUX and no status', () => {
    const { getByText } = render(getIcon('REDHAT_LINUX'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Active icon when deploymentType is REDHAT_LINUX and status is Active', () => {
    const { getByText } = render(getIcon('REDHAT_LINUX', 'Active'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Inactive icon when deploymentType is REDHAT_LINUX and status is Inactive', () => {
    const { getByText } = render(getIcon('REDHAT_LINUX', 'Inactive'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch icon when deploymentType is MICROSOFT_HYPER_V and no status', () => {
    const { getByText } = render(getIcon('MICROSOFT_HYPER_V'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Active icon when deploymentType is MICROSOFT_HYPER_V and status is Active', () => {
    const { getByText } = render(getIcon('MICROSOFT_HYPER_V', 'Active'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Inactive icon when deploymentType is MICROSOFT_HYPER_V and status is Inactive', () => {
    const { getByText } = render(getIcon('MICROSOFT_HYPER_V', 'Inactive'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch icon when deploymentType is VMWARE_ESXI and no status', () => {
    const { getByText } = render(getIcon('VMWARE_ESXI'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Active icon when deploymentType is VMWARE_ESXI and status is Active', () => {
    const { getByText } = render(getIcon('VMWARE_ESXI', 'Active'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch Inactive icon when deploymentType is VMWARE_ESXI and status is Inactive', () => {
    const { getByText } = render(getIcon('VMWARE_ESXI', 'Inactive'));
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });

  it('should return Branch icon when no deploymentType and no status', () => {
    const { getByText } = render(getIcon());
    expect(getByText('FontAwesomeIcon')).toBeInTheDocument();
  });
});
