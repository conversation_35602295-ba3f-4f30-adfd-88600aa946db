import getTxRx from './getTxRx';

describe('getTxRx function', () => {
  it('should return an object with txBy, rxBy, and total properties', () => {
    // Arrange
    const bound = {
      entries: [
        { name: 'TX', total: 200 },
        { name: 'RX', total: 100 },
      ],
      total: 300,
    };

    // Act
    const result = getTxRx(bound);

    // Assert
    expect(result).toEqual({
      rxBy: 200,
      txBy: 100,
      total: 300,
    });
  });

  it('should return an object with default values if no RX or TX entries are found', () => {
    // Arrange
    const bound = {
      entries: [
        { name: 'OTHER', total: 100 },
      ],
      total: 100,
    };

    // Act
    const result = getTxRx(bound);

    // Assert
    expect(result).toEqual({
      txBy: 0,
      rxBy: 0,
      total: 100,
    });
  });

  it('should return an object with default values if no entries are found', () => {
    // Arrange
    const bound = {
      entries: [],
      total: 0,
    };

    // Act
    const result = getTxRx(bound);

    // Assert
    expect(result).toEqual({
      txBy: 0,
      rxBy: 0,
      total: 0,
    });
  });

  it('should throw an error if bound is null or undefined', () => {
    // Act and Assert
    expect(() => getTxRx(null)).toThrowError();
    expect(() => getTxRx(undefined)).toThrowError();
  });
});
