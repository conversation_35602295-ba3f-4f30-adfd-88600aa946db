import getTrend from './getTrend';

const getTrendBoundsData = (entries, chartData) => {
  return entries.forEach((k) => {
    if (k.name === 'INBOUND') {
      k.entries.forEach((l) => {
        getTrend(l.trend, chartData, 'Inbound');
      });
    } else if (k.name === 'OUTBOUND') {
      k.entries.forEach((l) => {
        getTrend(l.trend, chartData, 'Outbound');
      });
    }
  });
};

export default getTrendBoundsData;
// E.g
// j.entries.forEach((k) => {
//   if (k.name === 'INBOUND') {
//     k.entries.forEach((l) => {
//       getTrend(l.trend, trafficZiaChartData, 'Inbound');
//     });
//   } else if (k.name === 'OUTBOUND') {
//     k.entries.forEach((l) => {
//       getTrend(l.trend, trafficZiaChartData, 'Outbound');
//     });
//   }
// });
