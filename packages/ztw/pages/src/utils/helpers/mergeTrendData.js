const mergeTrendData = (mapKey, total, zia, zpa, cloud, direct) => {
  return total.map((ele, idx) => {
    const merger = ele;
    merger[mapKey] = (zia && zia[idx] && zia[idx][mapKey] ? Number(zia[idx][mapKey]) : 0)
      + (zpa && zpa[idx] && zpa[idx][mapKey] ? Number(zpa[idx][mapKey]) : 0)
      + (cloud && cloud[idx] && cloud[idx][mapKey] ? Number(cloud[idx][mapKey]) : 0)
      + (direct && direct[idx] && direct[idx][mapKey] ? Number(direct[idx][mapKey]) : 0);
    return merger;
  });
};

export default mergeTrendData;
// E.g
// ele.Inbound
// + ((trafficZpaChartData[idx] && trafficZpaChartData[idx].Inbound)
// ? trafficZpaChartData[idx].Inbound : '')
// + ((trafficCloudChartData[idx] && trafficCloudChartData[idx].Inbound)
// ? trafficCloudChartData[idx].Inbound : '')
// + ((trafficDirectChartData[idx] && trafficDirectChartData[idx].Inbound)
// ? trafficDirectChartData[idx].Inbound : '');
