import { isEmpty } from 'utils/lodash';
import { portRange } from 'utils/validations';
import { convertPortObjectToString } from 'utils/helpers';
import networkServiceValidation from './networkServiceValidation';

jest.mock('utils/lodash', () => ({
  isEmpty: jest.fn(),
}));

jest.mock('utils/validations', () => ({
  portRange: jest.fn(),
}));

jest.mock('utils/helpers', () => ({
  convertPortObjectToString: jest.fn(),
}));

describe('networkServiceValidation', () => {
  beforeEach(() => {
    isEmpty.mockImplementation((value) => (!value));
    portRange.mockImplementation((value) => (value === 'invalid' ? 'error' : ''));
    convertPortObjectToString.mockImplementation((ports) => ports.map((port) => `${port.start}-${port.end}`));
  });

  it('should return empty object when all fields are valid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    const result = networkServiceValidation(values);
    expect(result).toEqual({});
  });

  it('should return error when destTcpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: ['invalid'],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      destTcpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return error when destUdpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: ['invalid'],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      destUdpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return error when destSctpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: ['invalid'],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      destSctpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return error when srcTcpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: ['invalid'],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      srcTcpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return error when srcUdpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: ['invalid'],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      srcUdpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return error when srcSctpPorts is invalid', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [{ start: 1, end: 10 }],
      destUdpPorts: [{ start: 20, end: 30 }],
      destSctpPorts: [{ start: 40, end: 50 }],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: ['invalid'],
    };

    portRange.mockImplementationOnce((value) => (value === 'invalid' ? 'error' : ''));

    const result = networkServiceValidation(values);
    expect(result).toEqual({
      srcSctpPorts: 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS',
    });
  });

  it('should return no error when no destination ports are provided', () => {
    const values = {
      formMode: 'EDIT',
      name: 'test',
      destTcpPorts: [],
      destUdpPorts: [],
      destSctpPorts: [],
      srcTcpPorts: [{ start: 60, end: 70 }],
      srcUdpPorts: [{ start: 80, end: 90 }],
      srcSctpPorts: [{ start: 100, end: 110 }],
    };

    isEmpty.mockImplementationOnce((value) => (value.length === 0));

    const result = networkServiceValidation(values);
    expect(result).toEqual({});
  });
});
