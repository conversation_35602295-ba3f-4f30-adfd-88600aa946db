import PropTypes from 'prop-types';

function fromMaskToCIDR(mask) {
  if (mask === '255.255.255.255') return 32;
  if (mask === '255.255.255.254') return 31;
  if (mask === '255.255.255.252') return 30;
  if (mask === '255.255.255.248') return 29;
  if (mask === '255.255.255.240') return 28;
  if (mask === '255.255.255.224') return 27;
  if (mask === '255.255.255.192') return 26;
  if (mask === '255.255.255.128') return 25;
  if (mask === '255.255.255.0') return 24;
  if (mask === '255.255.254.0') return 23;
  if (mask === '255.255.252.0') return 22;
  if (mask === '255.255.248.0') return 21;
  if (mask === '255.255.240.0') return 20;
  if (mask === '255.255.224.0') return 19;
  if (mask === '255.255.192.0') return 18;
  if (mask === '255.255.128.0') return 17;
  if (mask === '255.255.0.0') return 16;
  if (mask === '255.254.0.0') return 15;
  if (mask === '255.252.0.0') return 14;
  if (mask === '255.248.0.0') return 13;
  if (mask === '255.240.0.0') return 12;
  if (mask === '255.224.0.0') return 11;
  if (mask === '255.192.0.0') return 10;
  if (mask === '255.128.0.0') return 9;
  if (mask === '255.0.0.0') return 8;
  if (mask === '254.0.0.0') return 7;
  if (mask === '252.0.0.0') return 6;
  if (mask === '248.0.0.0') return 5;
  if (mask === '240.0.0.0') return 4;
  if (mask === '224.0.0.0') return 3;
  if (mask === '192.0.0.0') return 2;
  if (mask === '128.0.0.0') return 1;
  if (mask === '0.0.0.0') return 0;

  return 0;
}

fromMaskToCIDR.propTypes = {
  mask: PropTypes.number,
};
fromMaskToCIDR.defaultProps = {
  mask: null,
};

export default fromMaskToCIDR;
