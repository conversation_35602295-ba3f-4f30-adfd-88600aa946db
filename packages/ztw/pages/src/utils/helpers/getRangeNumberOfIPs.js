/* eslint-disable no-bitwise */
function ipToInteger(ip) {
  // Split the IP address into its octets
  const octets = ip.split('.');

  // Validate the octets
  if (octets.length !== 4) {
    throw new Error('Invalid IP address. It must have four octets.');
  }
    
  
  // Calculate the integer representation of the IP address
  // eslint-disable-next-line max-len
  const integer = (parseInt(octets[0], 10) << 24) + (parseInt(octets[1], 10) << 16) + (parseInt(octets[2], 10) << 8) + parseInt(octets[3], 10);
  
  return integer;
}


const getRangeNumberOfIPs = (startIP, endIP) => {
  if (typeof startIP !== 'string' || startIP.length === 0) {
    throw new Error('Invalid IP range. It must be a non-empty string.');
  }

  if (typeof endIP !== 'string' || endIP.length === 0) {
    throw new Error('Invalid IP range. It must be a non-empty string.');
  }


  const startIPInt = ipToInteger(startIP);
  const endIPInt = ipToInteger(endIP);

  // Validate the IP range
  if (startIPInt > endIPInt) {
    throw new Error('Invalid IP range. The start IP address must be less than or equal to the end IP address.');
  }
  
  // Calculate the number of IP addresses in the range
  const count = endIPInt - startIPInt + 1;
  
  return count;
};

export default getRangeNumberOfIPs;
