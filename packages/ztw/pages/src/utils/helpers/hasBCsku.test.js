import hasBCsku from './hasBCsku';

describe('hasBCsku function', () => {
  it('should return true when sku contains BC_CONNECTOR', () => {
    const sku = ['BC_CONNECTOR'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains B_AND_C_CONNECTOR', () => {
    const sku = ['B_AND_C_CONNECTOR'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_CONNECTOR_400', () => {
    const sku = ['BC_DEVICE_CONNECTOR_400'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_CONNECTOR_600', () => {
    const sku = ['BC_DEVICE_CONNECTOR_600'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_CONNECTOR_800', () => {
    const sku = ['BC_DEVICE_CONNECTOR_800'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains B-CONNECTOR and C-CONNECTOR', () => {
    const sku = ['B-CONNECTOR', 'C-CONNECTOR'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains B_CONNECTOR and C_CONNECTOR', () => {
    const sku = ['B_CONNECTOR', 'C_CONNECTOR'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_CONNECTOR_VM and WORKLOAD_CONNECTOR_VM', () => {
    const sku = ['BC_DEVICE_CONNECTOR_VM', 'WORKLOAD_CONNECTOR_VM'];
    expect(hasBCsku(sku)).toBe(true);
  });

  it('should return false when sku does not contain any of the specified values', () => {
    const sku = ['OTHER_SKU'];
    expect(hasBCsku(sku)).toBe(false);
  });

  it('should return false when sku is empty', () => {
    const sku = [];
    expect(hasBCsku(sku)).toBe(false);
  });
});
