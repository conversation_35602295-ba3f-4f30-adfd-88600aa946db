import getWeekDayByVal from './getWeekDayByVal';

// Mock the translation function
const t = (key) => key;

describe('getWeekDayByVal function', () => {
  it('should return the correct weekday object when given a valid value', () => {
    // Test with a valid value
    const result = getWeekDayByVal(2, t);
    expect(result).toEqual({
      id: 'MONDAY',
      name: '<PERSON>ONDAY',
      val: 2,
    });

    // Test with another valid value
    const result2 = getWeekDayByVal(7, t);
    expect(result2).toEqual({
      id: 'SATURDAY',
      name: 'SATURDAY',
      val: 7,
    });
  });

  it('should return undefined when given an invalid value', () => {
    // Test with an invalid value
    const result = getWeekDayByVal(10, t);
    expect(result).toBeUndefined();
  });

  it('should return undefined when given a non-numeric value', () => {
    // Test with a non-numeric value
    const result = getWeekDayByVal('abc', t);
    expect(result).toBeUndefined();
  });

  it('should return undefined when given null or undefined', () => {
    // Test with null
    const result = getWeekDayByVal(null, t);
    expect(result).toBeUndefined();

    // Test with undefined
    const result2 = getWeekDayByVal(undefined, t);
    expect(result2).toBeUndefined();
  });
});
