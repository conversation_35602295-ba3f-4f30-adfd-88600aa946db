import getHealthStatus from './getHealthStatus';

describe('getHealthStatus function', () => {
  it('should return HEALTHY when current.healthStatus is HEALTHY', () => {
    const current = { healthStatus: 'HEALTHY' };
    expect(getHealthStatus(current)).toBe('HEALTHY');
  });

  it('should return UNHEALTHY when current.healthStatus is not HEALTHY', () => {
    const current = { healthStatus: 'UNHEALTHY' };
    expect(getHealthStatus(current)).toBe('UNHEALTHY');
  });

  it('should return UNHEALTHY when current is null or undefined', () => {
    expect(getHealthStatus(null)).toBe('UNHEALTHY');
    expect(getHealthStatus(undefined)).toBe('UNHEALTHY');
    expect(getHealthStatus('NOT_AVAILABLE')).toBe('UNHEALTHY');
  });

  it('should return UNHEALTHY when current.healthStatus is null or undefined', () => {
    const current = {};
    expect(getHealthStatus(current)).toBe('UNHEALTHY');
  });
});
