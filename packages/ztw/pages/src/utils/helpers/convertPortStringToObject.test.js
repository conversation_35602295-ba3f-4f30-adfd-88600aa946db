import convertPortStringToObject from './convertPortStringToObject';

describe('convertPortStringToObject', () => {
  it('should return an empty array when input is null or undefined', () => {
    expect(convertPortStringToObject(null)).toEqual({});
    expect(convertPortStringToObject(undefined)).toEqual({});
  });

  it('should convert a single port string to an object', () => {
    const input = ['7000'];
    const expectedOutput = [{ start: 7000 }];
    expect(convertPortStringToObject(input)).toEqual(expectedOutput);
  });

  it('should convert a port range string to an object', () => {
    const input = ['7000-7010'];
    const expectedOutput = [{ start: 7000, end: 7010 }];
    expect(convertPortStringToObject(input)).toEqual(expectedOutput);
  });

  it('should convert multiple port strings to objects', () => {
    const input = ['7000', '7001-7010', '7011'];
    const expectedOutput = [
      { start: 7000 },
      { start: 7001, end: 7010 },
      { start: 7011 },
    ];
    expect(convertPortStringToObject(input)).toEqual(expectedOutput);
  });
});
