import hasBCEncryptSku from './hasBCEncryptSku';

describe('hasBCEncryptSku function', () => {
  it('should return true when sku contains B_AND_C_CONNECTOR_ENCRYPT', () => {
    const sku = ['B_AND_C_CONNECTOR_ENCRYPT', 'OTHER_SKU'];
    expect(hasBCEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains WORKLOAD_TUNNEL_ENCRYPT', () => {
    const sku = ['WORKLOAD_TUNNEL_ENCRYPT', 'OTHER_SKU'];
    expect(hasBCEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_TUNNEL_ENCRYPT', () => {
    const sku = ['BC_DEVICE_TUNNEL_ENCRYPT', 'OTHER_SKU'];
    expect(hasBCEncryptSku(sku)).toBe(true);
  });

  it('should return false when sku does not contain any of the specified values', () => {
    const sku = ['OTHER_SKU', 'ANOTHER_SKU'];
    expect(hasBCEncryptSku(sku)).toBe(false);
  });

  it('should return false when sku is empty', () => {
    const sku = [];
    expect(hasBCEncryptSku(sku)).toBe(false);
  });
});
