import getRealOperationalStatus from './getRealOperationalStatus';

describe('getRealOperationalStatus function', () => {
  it('returns the original operational status when no overriding status is present', () => {
    const operationalStatus = 'ENABLED';
    const status = [];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe(operationalStatus);
  });

  it('overrides the operational status to DISABLING when DISABLING is present in the status array', () => {
    const operationalStatus = 'ENABLED';
    const status = ['DISABLING'];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('DISABLING');
  });

  it('overrides the operational status to DISABLED when DISABLED is present in the status array', () => {
    const operationalStatus = 'ENABLED';
    const status = ['DISABLED'];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('DISABLED');
  });

  it('overrides the operational status to ENABLING when ENABLING is present in the status array', () => {
    const operationalStatus = 'DISABLED';
    const status = ['ENABLING'];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('ENABLING');
  });

  it('overrides the operational status to DELETING when DELETING is present in the status array', () => {
    const operationalStatus = 'ENABLED';
    const status = ['DELETING'];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('DELETING');
  });

  it('prioritizes the order of overriding statuses', () => {
    const operationalStatus = 'ENABLED';
    const status = ['ENABLING', 'DISABLING', 'DISABLED', 'DELETING'];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('DELETING');
  });

  it('handles an empty operational status', () => {
    const operationalStatus = '';
    const status = [];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe('');
  });

  it('handles a null operational status', () => {
    const operationalStatus = null;
    const status = [];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe(null);
  });

  it('handles an undefined operational status', () => {
    const operationalStatus = undefined;
    const status = [];
    expect(getRealOperationalStatus(operationalStatus, status)).toBe(undefined);
  });
});
