import hasC5Gsku from './hasC5Gsku';

describe('hasC5Gsku function', () => {
  it('should return true when skuArray contains ZT_CC_CELLULAR_PRE', () => {
    const skuArray = ['ZT_CC_CELLULAR_PRE', 'OTHER_SKU'];
    expect(hasC5Gsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray only contains ZT_CC_CELLULAR_PRE', () => {
    const skuArray = ['ZT_CC_CELLULAR_PRE'];
    expect(hasC5Gsku(skuArray)).toBe(true);
  });

  it('should return false when skuArray does not contain ZT_CC_CELLULAR_PRE', () => {
    const skuArray = ['OTHER_SKU', 'ANOTHER_SKU'];
    expect(hasC5Gsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is empty', () => {
    const skuArray = [];
    expect(hasC5Gsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is null or undefined', () => {
    expect(hasC5Gsku(null)).toBe(false);
    expect(hasC5Gsku(undefined)).toBe(false);
  });
});
