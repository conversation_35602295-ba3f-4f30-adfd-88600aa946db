const getRealOperationalStatus = (operationalStatus, status = []) => {
  let realOperationalStatus = operationalStatus;
  realOperationalStatus = status.includes('DISABLING') ? 'DISABLING' : realOperationalStatus;
  realOperationalStatus = status.includes('DISABLED') ? 'DISABLED' : realOperationalStatus;
  realOperationalStatus = status.includes('ENABLING') ? 'ENABLING' : realOperationalStatus;
  realOperationalStatus = status.includes('DELETING') ? 'DELETING' : realOperationalStatus;
  
  return realOperationalStatus;
};

export default getRealOperationalStatus;
