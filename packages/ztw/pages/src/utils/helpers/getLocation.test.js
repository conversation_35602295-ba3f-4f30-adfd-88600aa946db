import getLocation from './getLocation';

describe('getLocation function', () => {
  it('should return an object with name and id when data is provided', () => {
    const data = ['New York', 'USA'];
    const result = getLocation(...data);
    expect(result).toEqual({
      name: 'New York, USA',
      id: 'New York-USA',
    });
  });

  it('should return an object with name as "Unknown" when no valid data is provided', () => {
    const data = [null, undefined, ''];
    const result = getLocation(...data);
    expect(result).toEqual({
      name: 'Unknown',
      id: '--',
    });
  });

  it('should return an object with name as "Unknown" when all data is "Unknown"', () => {
    const data = ['Unknown', 'Unknown'];
    const result = getLocation(...data);
    expect(result).toEqual({
      name: 'Unknown',
      id: 'Unknown-Unknown',
    });
  });

  it('should return an object with name as a comma-separated string when multiple valid data is provided', () => {
    const data = ['New York', 'USA', 'North America'];
    const result = getLocation(...data);
    expect(result).toEqual({
      name: 'New York, USA, North America',
      id: 'New York-USA-North America',
    });
  });

  it('should return an object with id as a hyphen-separated string when multiple data is provided', () => {
    const data = ['New York', 'USA', 'North America'];
    const result = getLocation(...data);
    expect(result.id).toBe('New York-USA-North America');
  });
});
