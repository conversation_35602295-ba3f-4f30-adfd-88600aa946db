// tests/getTheme.test.js

import getTheme from './getTheme';

describe('getTheme function', () => {
  beforeEach(() => {
    // Clear local storage before each test
    window.localStorage.clear();
  });

  afterEach(() => {
    // Clear local storage after each test
    window.localStorage.clear();
  });

  it('should return "light" when theme key is not present in local storage', () => {
    // Act
    const theme = getTheme();

    // Assert
    expect(theme).toBe('light');
  });

  it('should return the theme value from local storage when key is present', () => {
    // Arrange
    window.localStorage.setItem('theme', 'dark');

    // Act
    const theme = getTheme();

    // Assert
    expect(theme).toBe('dark');
  });

  it('should return the theme value from local storage when key is present with different casing', () => {
    // Arrange
    window.localStorage.setItem('theme', 'DARK');

    // Act
    const theme = getTheme();

    // Assert
    expect(theme).toBe('DARK');
  });
});
