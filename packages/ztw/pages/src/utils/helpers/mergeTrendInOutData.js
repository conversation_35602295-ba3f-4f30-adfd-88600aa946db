const mergeTrendInOutData = (inKey, outKey, total, zia, zpa, cloud, direct) => {
  return total.map((ele, idx) => {
    const merger = ele;
    merger[inKey] = ((zia[idx] && zia[idx][inKey])
      ? Number(zia[idx][inKey]) : 0)
      + ((zpa[idx] && zpa[idx][inKey])
        ? Number(zpa[idx][inKey]) : 0)
      + ((cloud[idx] && cloud[idx][inKey])
        ? Number(cloud[idx][inKey]) : 0)
      + ((direct[idx] && direct[idx][inKey])
        ? Number(direct[idx][inKey]) : 0);
    
    merger[outKey] = ((zia[idx] && zia[idx][outKey])
      ? Number(zia[idx][outKey]) : 0)
      + ((zpa[idx] && zpa[idx][outKey])
        ? Number(zpa[idx][outKey]) : 0)
      + ((cloud[idx] && cloud[idx][outKey])
        ? Number(cloud[idx][outKey]) : 0)
      + ((direct[idx] && direct[idx][outKey])
        ? Number(direct[idx][outKey]) : 0);
  
    return merger;
  });
};

export default mergeTrendInOutData;
// E.g
// merger.Inbound = ele.Inbound
//                   + ((trafficZpaChartData[idx] && trafficZpaChartData[idx].Inbound)
//                     ? trafficZpaChartData[idx].Inbound : '')
//                   + ((trafficCloudChartData[idx] && trafficCloudChartData[idx].Inbound)
//                     ? trafficCloudChartData[idx].Inbound : '')
//                   + ((trafficDirectChartData[idx] && trafficDirectChartData[idx].Inbound)
//                     ? trafficDirectChartData[idx].Inbound : '');
// merger.Outbound = ele.Outbound
//                   + ((trafficZpaChartData[idx] && trafficZpaChartData[idx].Outbound)
//                     ? trafficZpaChartData[idx].Outbound : '')
//                   + ((trafficCloudChartData[idx] && trafficCloudChartData[idx].Outbound)
//                     ? trafficCloudChartData[idx].Outbound : '')
//                   + ((trafficDirectChartData[idx] && trafficDirectChartData[idx].Outbound)
//                     ? trafficDirectChartData[idx].Outbound : '');
