import React from 'react';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle, faExclamationCircle, faMinusCircle,
} from '@fortawesome/pro-solid-svg-icons';

const getStatusIcon = (status = '') => {
  if (status && status.toUpperCase() === 'ACTIVE') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faCheckCircle}
          title={i18n.t('ACTIVE')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="active" />
        {i18n.t('ACTIVE')}
      </span>
    );
  }
  if (status && status === 'Enabled') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faCheckCircle}
          title={i18n.t('ENABLED')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="active" />
        {i18n.t('ENABLED')}
      </span>
    );
  }

  if (status && status === 'Up') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faCheckCircle}
          title={i18n.t('UP')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="active" />
        {i18n.t('UP')}
      </span>
    );
  }

  if (status && status.toUpperCase() === 'STANDBY') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faMinusCircle}
          title={i18n.t('STANDBY')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="disabled" />
        {i18n.t('STANDBY')}
      </span>
    );
  }
  if (status && status.toUpperCase() === 'INIT') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faMinusCircle}
          title={i18n.t('INIT')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="disabled" />
        {i18n.t('INIT')}
      </span>
    );
  }
  if (status && status === 'Down') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faExclamationCircle}
          title={i18n.t('DOWN')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="inactive" />
        {i18n.t('DOWN')}
      </span>
    );
  }

  if (status && (status === 'Active' || status === 'Enabled')) {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faCheckCircle}
          title={i18n.t(status === 'Active' ? 'ACTIVE' : 'ENABLED')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="active" />
        {i18n.t(status === 'Active' ? 'ACTIVE' : 'ENABLED')}
      </span>
    );
  }

  if (status && (status === 'Active' || status === 'Enabled')) {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faCheckCircle}
          title="Scheduled"
          // onClick={() => onHandleShowHideChilds(id)}
          className="active" />
        {i18n.t(status === 'Active' ? 'ACTIVE' : 'ENABLED')}
      </span>
    );
  }
    
  if (status && status === 'Inactive') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faExclamationCircle}
          title={i18n.t('INACTIVE')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="inactive" />
        {i18n.t('INACTIVE')}
      </span>
    );
  }
  if (status && status === 'Disabled') {
    return (
      <span className="operational-staus-icon">
        <FontAwesomeIcon
          icon={faMinusCircle}
          title={i18n.t('DISABLED')}
          // onClick={() => onHandleShowHideChilds(id)}
          className="disabled" />
        {i18n.t('DISABLED')}
      </span>
    );
  }

  return <></>;
};

export default getStatusIcon;
