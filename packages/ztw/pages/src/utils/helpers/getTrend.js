import moment from 'moment-timezone';

const getTrend = (trend, holder, plotElement) => {
  const { trendStartTime, trendValues } = trend;
  let startTime = trendStartTime;

  if (Array.isArray(holder) && holder.length
  && holder[0] && typeof holder[0] === 'object') {
    holder.forEach((i, key) => {
      if (i[plotElement]) {
        // eslint-disable-next-line no-param-reassign
        i[plotElement] += trendValues[key];
      } else {
        // eslint-disable-next-line no-param-reassign
        i[plotElement] = trendValues[key];
      }
    });
    return holder;
  }

  trendValues.forEach((value, key) => {
    const chartItem = {};
    if (trend.trendInterval === 86400000) {
      // Daily (86400000 = 24hrs (86400000 / 60*60*1000))
      chartItem.name = moment(startTime).add(key, 'day').format('MM/DD');
      chartItem.orgTime = moment(trendStartTime).add(key, 'day').unix();
      // startTime = moment(startTime).add(key, 'day');
    } else if (trend.trendInterval === 3600000) {
      // Hourly
      chartItem.name = moment(startTime).add(1, 'hour').format('HH:mm');
      chartItem.orgTime = moment(startTime).add(1, 'hour').unix();
      startTime = moment(startTime).add(1, 'hour');
    } else {
      // for ms
      chartItem.name = moment(startTime).add(trend.trendInterval, 'ms').format('HH:mm');
      chartItem.orgTime = moment(startTime).add(trend.trendInterval, 'ms').unix();
      startTime = moment(startTime).add(trend.trendInterval, 'ms');
    }
    chartItem[plotElement] = value;
    holder.push(chartItem);
  });
  return holder;
};

export default getTrend;
