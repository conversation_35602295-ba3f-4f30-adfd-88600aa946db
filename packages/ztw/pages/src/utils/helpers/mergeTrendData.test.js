import mergeTrendData from './mergeTrendData';

describe('mergeTrendData function', () => {
  it('should return an array with merged trend data', () => {
    const total = [
      { Inbound: 10 },
      { Inbound: 20 },
      { Inbound: 30 },
    ];

    const zia = [
      { Inbound: 5 },
      { Inbound: 10 },
      { Inbound: 15 },
    ];

    const zpa = [
      { Inbound: 2 },
      { Inbound: 4 },
      { Inbound: 6 },
    ];

    const cloud = [
      { Inbound: 1 },
      { Inbound: 2 },
      { Inbound: 3 },
    ];

    const direct = [
      { Inbound: 0.5 },
      { Inbound: 1 },
      { Inbound: 1.5 },
    ];

    const result = mergeTrendData('Inbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 8.5 },
      { Inbound: 17 },
      { Inbound: 25.5 },
    ]);
  });

  it('should handle missing values in trend data', () => {
    const total = [
      { Inbound: 10 },
      { Inbound: 20 },
      { Inbound: 30 },
    ];

    const zia = [
      { Inbound: 5 },
      null,
      { Inbound: 15 },
    ];

    const zpa = [
      { Inbound: 2 },
      { Inbound: 4 },
      null,
    ];

    const cloud = [
      { Inbound: 1 },
      { Inbound: 2 },
      { Inbound: 3 },
    ];

    const direct = [
      { Inbound: 0.5 },
      { Inbound: 1 },
      { Inbound: 1.5 },
    ];

    const result = mergeTrendData('Inbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 8.5 },
      { Inbound: 7 },
      { Inbound: 19.5 },
    ]);
  });

  it('should handle empty arrays', () => {
    const total = [
      { Inbound: 10 },
      { Inbound: 20 },
      { Inbound: 30 },
    ];

    const zia = [];
    const zpa = [];
    const cloud = [];
    const direct = [];

    const result = mergeTrendData('Inbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 0 },
      { Inbound: 0 },
      { Inbound: 0 },
    ]);
  });
});
