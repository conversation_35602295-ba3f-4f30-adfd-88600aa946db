/* eslint-disable no-nested-ternary */
function trinary(boolValue, trueValue, falseValue) {
  // Use a function to delay the evaluation of the values
  return boolValue
    // If the condition is true, return the true value
    ? (typeof trueValue === 'function' ? trueValue() : trueValue)
    // eslint-disable-next-line operator-linebreak
    :
    // If the condition is false, return the false value
    (typeof falseValue === 'function' ? falseValue() : falseValue);
}
export default trinary;
