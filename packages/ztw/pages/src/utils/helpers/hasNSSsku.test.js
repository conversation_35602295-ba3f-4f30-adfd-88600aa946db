import hasNSSsku from './hasNSSsku';

describe('hasNSSsku function', () => {
  it('should return true when skuArray contains Z_LOGFEED', () => {
    const skuArray = ['Z_LOGFEED', 'other_sku'];
    expect(hasNSSsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray contains Z_LOGFEED_FW', () => {
    const skuArray = ['Z_LOGFEED_FW', 'other_sku'];
    expect(hasNSSsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray contains Z_LOGFEED_LIVE', () => {
    const skuArray = ['Z_LOGFEED_LIVE', 'other_sku'];
    expect(hasNSSsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray contains Z_LOGFEED_FW_LIVE', () => {
    const skuArray = ['Z_LOGFEED_FW_LIVE', 'other_sku'];
    expect(hasNSSsku(skuArray)).toBe(true);
  });

  it('should return false when skuArray does not contain any of the specified skus', () => {
    const skuArray = ['other_sku1', 'other_sku2'];
    expect(hasNSSsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is empty', () => {
    const skuArray = [];
    expect(hasNSSsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is null or undefined', () => {
    expect(hasNSSsku(null)).toBe(false);
    expect(hasNSSsku(undefined)).toBe(false);
  });
});
