import formatIpValues from './formatIpValues';

describe('formatIpValues function', () => {
  it('should return an empty string for 0.0.0.0', () => {
    expect(formatIpValues('0.0.0.0')).toBe('');
  });

  it('should return an empty string for 0.0.0.0/0', () => {
    expect(formatIpValues('0.0.0.0/0')).toBe('');
  });

  it('should return an empty string for ***************', () => {
    expect(formatIpValues('***************')).toBe('');
  });

  it('should return an empty string for 0', () => {
    expect(formatIpValues('0')).toBe('');
  });

  it('should return the original IP for other values', () => {
    expect(formatIpValues('***********')).toBe('***********');
    expect(formatIpValues('********/24')).toBe('********/24');
  });
});
