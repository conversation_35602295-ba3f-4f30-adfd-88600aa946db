/**
 * Convert from an array of port string "7000-7010" into an object of { start: 7000, end: 7010 }
 *
 * @param {Array} ports An array of port string
 * @return An array of port object
 */
const convertPortStringToObject = (ports) => {
  let result = {};
  if (ports) {
    result = ports.map((item) => {
      const portObj = {};
      const port = item.split('-');
      portObj.start = parseInt(port[0], 10);

      if (port[1]) {
        portObj.end = parseInt(port[1], 10);
      }
      return portObj;
    });
  }

  return result;
};

export default convertPortStringToObject;
