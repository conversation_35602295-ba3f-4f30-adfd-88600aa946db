import { xorWith } from 'lodash';
import { isEmpty, isEqual } from 'utils/lodash';
import objectsAreSame from './objectsAreSame';

describe('objectsAreSame function', () => {
  it('should return true when two objects are the same', () => {
    const obj1 = [{ a: 1, b: 2 }];
    const obj2 = [{ a: 1, b: 2 }];
    expect(objectsAreSame(obj1, obj2)).toBe(true);
  });

  it('should return false when two objects are not the same', () => {
    const obj1 = [{ a: 1, b: 2 }];
    const obj2 = [{ a: 1, b: 3 }];
    expect(objectsAreSame(obj1, obj2)).toBe(false);
  });

  it('should return true when two objects are empty', () => {
    const obj1 = [{}];
    const obj2 = [{}];
    expect(objectsAreSame(obj1, obj2)).toBe(true);
  });

  it('should return false when one object is empty and the other is not', () => {
    const obj1 = [{ a: 1 }];
    const obj2 = [{}];
    expect(objectsAreSame(obj1, obj2)).toBe(false);
  });

  it('should return true when two objects have the same properties but in different order', () => {
    const obj1 = [{ a: 1, b: 2 }];
    const obj2 = [{ b: 2, a: 1 }];
    expect(objectsAreSame(obj1, obj2)).toBe(true);
  });

  it('should return false when two objects have different properties', () => {
    const obj1 = [{ a: 1, b: 2 }];
    const obj2 = [{ a: 1, c: 2 }];
    expect(objectsAreSame(obj1, obj2)).toBe(false);
  });

  it('should return true when two objects have nested objects that are the same', () => {
    const obj1 = [{ a: 1, b: { c: 2 } }];
    const obj2 = [{ a: 1, b: { c: 2 } }];
    expect(objectsAreSame(obj1, obj2)).toBe(true);
  });

  it('should return false when two objects have nested objects that are not the same', () => {
    const obj1 = [{ a: 1, b: { c: 2 } }];
    const obj2 = [{ a: 1, b: { c: 3 } }];
    expect(objectsAreSame(obj1, obj2)).toBe(false);
  });
});
