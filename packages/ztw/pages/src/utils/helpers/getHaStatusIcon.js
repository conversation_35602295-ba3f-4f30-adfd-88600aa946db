/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import i18n from 'utils/i18n';

const getHaStatusIcon = (status = '') => {
  return (
    <label htmlFor="status" className="container-row-ha-status">
      { (status === 'ENABLE' || status === 'ACTIVE')
          && <span className="os-green-circle"></span>}
      {(status === 'DISABLED')
            && <span className="os-gray-circle"></span>}
      {(status === 'STANDBY')
            && <span className="os-yellow-circle"></span>}
      {i18n.t(status || '---')}
    </label>
  );
};

export default getHaStatusIcon;
