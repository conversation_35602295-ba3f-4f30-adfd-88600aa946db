const setToolTipPosition = (event, modalParent) => {
  if (!event) return;
  if (!document.querySelector(modalParent)) return;
  event.preventDefault();
  const target = event.currentTarget;
  const elPosition = target.getBoundingClientRect();
  const reactModalPosition = document.querySelector(modalParent).getBoundingClientRect();
  const tooltipElement = target.parentElement.querySelector('.rTooltip');

  if (tooltipElement) {
    tooltipElement.style.top = (elPosition.top - reactModalPosition.top - tooltipElement.offsetHeight - 10) + 'px';
    tooltipElement.style.left = (elPosition.left - reactModalPosition.left) + 'px';
  }
};

export default setToolTipPosition;
