import chunk from './chunk';

describe('chunk function', () => {
  it('should return an empty array when input array is empty', () => {
    expect(chunk([], 2)).toEqual([]);
  });

  it('should return the original array when size is greater than array length', () => {
    expect(chunk([1, 2, 3], 4)).toEqual([[1, 2, 3]]);
  });

  it('should chunk the array correctly when size is less than array length', () => {
    expect(chunk([1, 2, 3, 4, 5], 2)).toEqual([[1, 2], [3, 4], [5]]);
  });

  it('should chunk the array correctly when size is equal to array length', () => {
    expect(chunk([1, 2, 3], 3)).toEqual([[1, 2, 3]]);
  });

  it('should chunk the array correctly when size is 1', () => {
    expect(chunk([1, 2, 3], 1)).toEqual([[1], [2], [3]]);
  });

  it('should throw an error when size is not a positive integer', () => {
    expect(() => chunk([1, 2, 3], -1)).toThrowError('Size must be a positive integer');
    expect(() => chunk([1, 2, 3], 0)).toThrowError('Size must be a positive integer');
    expect(() => chunk([1, 2, 3], 2.5)).toThrowError('Size must be a positive integer');
  });
});
