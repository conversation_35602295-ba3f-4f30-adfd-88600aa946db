import moment from 'moment';
import calculateNextUpdate from './calculateNextUpdate';

describe('calculateNextUpdate function', () => {
//   beforeEach(() => {
//     // Mock moment to return a fixed date for testing
//     jest.spyOn(moment, 'now').mockImplementation(() => moment('2024-03-14T10:00:00.000Z'));
//   });

  //   afterEach(() => {
  //     // Restore the original moment implementation
  //     jest.restoreAllMocks();
  //   });

  it('should return the next update time on the same day if the schedule time is later than the current time', () => {
    const weekday = { val: 1 }; // Thursday
    const hhFrom = 12;
    const mmFrom = 0;
    const periodFrom = 'PM';
    const offset = 0;

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('12:00 PM');
  });

  it('should return the next update time on the next day if the schedule time is earlier than the current time', () => {
    const weekday = { val: 2 }; // Thursday
    const hhFrom = 9;
    const mmFrom = 0;
    const periodFrom = 'AM';
    const offset = 0;

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('09:00 AM');
  });

  it('should return the next update time on the next week if the schedule day is earlier than the current day', () => {
    const weekday = { val: 3 }; // Wednesday
    const hhFrom = 12;
    const mmFrom = 0;
    const periodFrom = 'PM';
    const offset = 0;

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('12:00 PM');
  });

  it('should return the next update time on the next week if the schedule day is the same as the current day but the schedule time is earlier than the current time', () => {
    const weekday = { val: 4 }; // Thursday
    const hhFrom = 9;
    const mmFrom = 0;
    const periodFrom = 'AM';
    const offset = 0;

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('09:00 AM');
  });

  it('should handle offset correctly', () => {
    const weekday = { val: 5 }; // Thursday
    const hhFrom = 12;
    const mmFrom = 0;
    const periodFrom = 'PM';
    const offset = 60; // 1 hour

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('12:00 PM');
  });
  it('should handle offset correctly 2', () => {
    const weekday = { val: 6 }; // Thursday
    const hhFrom = 12;
    const mmFrom = 0;
    const periodFrom = 'PM';
    const offset = 60; // 1 hour

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('12:00 PM');
  });
  it('should handle offset correctly 3', () => {
    const weekday = { val: 7 }; // Thursday
    const hhFrom = 12;
    const mmFrom = 0;
    const periodFrom = 'PM';
    const offset = 60; // 1 hour

    const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
    expect(result).toContain('12:00 PM');
  });
  //   it('should handle offset correctly 4', () => {
  //     const currentDate = moment();
  //     const weekday = { val: currentDate.weekday() }; // Thursday
  //     const hhFrom = currentDate.hour();
  //     const mmFrom = currentDate.minute() - 1;
  //     const periodFrom = currentDate.hour() >= 12 ? 'PM' : 'AM';
  //     const offset = 60; // 1 hour

//     const result = calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset);
//     expect(result).toContain(`${hhFrom}:${mmFrom} ${periodFrom}`);
//   });
});
