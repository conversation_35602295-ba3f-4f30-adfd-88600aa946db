const convertMinutesToAMPM = (minutes, toString = false) => {
  const zeroPad = (num, places) => String(num).padStart(places, '0');

  const hh = Math.floor(minutes / 60) % 12 || 12;
  const mm = Math.round(minutes % 60);
  const period = minutes < 12 * 60 ? 'AM' : 'PM';

  if (toString) return `${hh}:${zeroPad(mm, 2)} ${period}`;

  return { hh, mm, period };
};

export default convertMinutesToAMPM;
