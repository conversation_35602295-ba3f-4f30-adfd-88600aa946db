import convertMinutesToAMPM from './convertMinutesToAMPM';

describe('convertMinutesToAMPM function', () => {
  it('should return an object with hours, minutes, and period when toString is false', () => {
    const result = convertMinutesToAMPM(720);
    expect(result).toEqual({ hh: 12, mm: 0, period: 'PM' });
  });

  it('should return a string in the format HH:MM AM/PM when toString is true', () => {
    const result = convertMinutesToAMPM(720, true);
    expect(result).toBe('12:00 PM');
  });

  it('should handle edge cases where minutes is 0', () => {
    const result = convertMinutesToAMPM(0);
    expect(result).toEqual({ hh: 12, mm: 0, period: 'AM' });
  });

  it('should handle edge cases where minutes is a multiple of 60', () => {
    const result = convertMinutesToAMPM(60);
    expect(result).toEqual({ hh: 1, mm: 0, period: 'AM' });
  });

  it('should handle edge cases where minutes is greater than 12 hours', () => {
    const result = convertMinutesToAMPM(13 * 60);
    expect(result).toEqual({ hh: 1, mm: 0, period: 'PM' });
  });

  it('should handle edge cases where minutes is a decimal number', () => {
    const result = convertMinutesToAMPM(90.5);
    expect(result).toEqual({ hh: 1, mm: 31, period: 'AM' });
  });
});
