import moment from 'moment';
import padZeroLeft from './padZeroLeft';
import convertAMPMtoMinutes from './convertAMPMtoMinutes';

const calculateNextUpdate = (weekday, hhFrom, mmFrom, periodFrom, offset) => {
  let daysInterval;
  const currentVmDate = moment().add(offset, 'm');
  const currentVmTime = moment().add(offset, 'm').toDate();

  const today = currentVmTime.getDay() + 1;

  const midnight = currentVmDate.startOf('day');
  const todayHHmm = moment(currentVmTime).diff(midnight, 'minutes');

  const scheduleHHmm = convertAMPMtoMinutes(hhFrom, mmFrom, periodFrom);
  if (weekday.val - today === 0) {
    daysInterval = scheduleHHmm <= todayHHmm
      ? 7 + (weekday.val - today)
      : (weekday.val - today);
  } else {
    daysInterval = weekday.val - today <= 0
      ? 7 + (weekday.val - today)
      : (weekday.val - today);
  }

  const startTime = ((weekday.val - today === 0)
                    && (todayHHmm - scheduleHHmm > 0 && todayHHmm - scheduleHHmm <= 60))
    ? `${currentVmDate.add(daysInterval, 'days').format('dddd')} ${padZeroLeft(hhFrom, 2)}:${padZeroLeft(mmFrom, 2)} ${periodFrom}`
    : `${currentVmDate.add(daysInterval, 'days').format('LL')} ${padZeroLeft(hhFrom, 2)}:${padZeroLeft(mmFrom, 2)} ${periodFrom}`;

  return startTime;
};

export default calculateNextUpdate;
