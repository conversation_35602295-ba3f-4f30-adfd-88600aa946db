// getPermissionText.test.js
import getPermissionText from './getPermissionText';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

describe('getPermissionText', () => {
  it('returns FULL when permission is READ_WRITE', () => {
    const result = getPermissionText('READ_WRITE');
    expect(result).toBe('FULL');
  });

  it('returns VIEW_ONLY when permission is READ_ONLY', () => {
    const result = getPermissionText('READ_ONLY');
    expect(result).toBe('VIEW_ONLY');
  });

  it('returns NONE when permission is unknown', () => {
    const result = getPermissionText('UNKNOWN');
    expect(result).toBe('NONE');
  });

  it('returns NONE when permission is null or undefined', () => {
    const result = getPermissionText(null);
    expect(result).toBe('NONE');

    const result2 = getPermissionText(undefined);
    expect(result2).toBe('NONE');
  });
});
