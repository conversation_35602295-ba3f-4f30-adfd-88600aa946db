import trinary from './trinary';

describe('trinary function', () => {
  it('returns trueValue when boolValue is true', () => {
    // Arrange
    const boolValue = true;
    const trueValue = 'Expected true value';
    const falseValue = 'Unexpected false value';

    // Act
    const result = trinary(boolValue, trueValue, falseValue);

    // Assert
    expect(result).toBe(trueValue);
  });

  it('returns falseValue when boolValue is false', () => {
    // Arrange
    const boolValue = false;
    const trueValue = 'Unexpected true value';
    const falseValue = 'Expected false value';

    // Act
    const result = trinary(boolValue, trueValue, falseValue);

    // Assert
    expect(result).toBe(falseValue);
  });

  it('returns trueValue when boolValue is not a boolean', () => {
    // Arrange
    const boolValue = 'not a boolean';
    const trueValue = 'Unexpected true value';
    const falseValue = 'Expected false value';

    // Act
    const result = trinary(boolValue, trueValue, falseValue);

    // Assert
    expect(result).toBe(trueValue);
  });

  it('returns falseValue when boolValue is null or undefined', () => {
    // Arrange
    const boolValue = null;
    const trueValue = 'Unexpected true value';
    const falseValue = 'Expected false value';

    // Act
    const result = trinary(boolValue, trueValue, falseValue);

    // Assert
    expect(result).toBe(falseValue);
  });
});
