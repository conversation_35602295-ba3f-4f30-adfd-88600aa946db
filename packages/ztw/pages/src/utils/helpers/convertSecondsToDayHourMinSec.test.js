import convertSecondsToDayHourMinSec from './convertSecondsToDayHourMinSec';

describe('convertSecondsToDayHourMinSec function', () => {
  it('returns "---" when input is 0', () => {
    expect(convertSecondsToDayHourMinSec(0)).toBe('---');
  });

  it('returns correct format for seconds only', () => {
    expect(convertSecondsToDayHourMinSec(10)).toBe('10 seconds');
  });

  it('returns correct format for minutes only', () => {
    expect(convertSecondsToDayHourMinSec(60)).toBe('1 minute, ');
  });

  it('returns correct format for minutes only', () => {
    expect(convertSecondsToDayHourMinSec(120)).toBe('2 minutes, ');
  });

  it('returns correct format for hours only', () => {
    expect(convertSecondsToDayHourMinSec(3600)).toBe('1 hour, ');
  });

  it('returns correct format for days only', () => {
    expect(convertSecondsToDayHourMinSec(86400)).toBe('1 day, ');
  });

  it('returns correct format for multiple units', () => {
    expect(convertSecondsToDayHourMinSec(93600)).toBe('1 day, 2 hours, ');
  });

  it('returns correct format for multiple units with seconds', () => {
    expect(convertSecondsToDayHourMinSec(93610)).toBe('1 day, 2 hours, 10 seconds');
  });

  it('handles large inputs correctly', () => {
    expect(convertSecondsToDayHourMinSec(31536000)).toBe('365 days, ');
  });

  it('handles decimal inputs correctly', () => {
    expect(convertSecondsToDayHourMinSec(10.5)).toBe('10 seconds');
  });

  it('returns correct format for hours only', () => {
    expect(convertSecondsToDayHourMinSec(39339198)).toBe('455 days, 7 hours, 33 minutes, 18 seconds');
  });

  it('handles negative inputs correctly', () => {
    expect(convertSecondsToDayHourMinSec(-10)).toBe('---');
  });
});
