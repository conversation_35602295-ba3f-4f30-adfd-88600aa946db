import mergeTrendInOutData from './mergeTrendInOutData';

describe('mergeTrendInOutData function', () => {
  it('should return an array with merged data', () => {
    const total = [{}, {}];
    const zia = [{ Inbound: '10', Outbound: '20' }, { Inbound: '30', Outbound: '40' }];
    const zpa = [{ Inbound: '50', Outbound: '60' }, { Inbound: '70', Outbound: '80' }];
    const cloud = [{ Inbound: '90', Outbound: '100' }, { Inbound: '110', Outbound: '120' }];
    const direct = [{ Inbound: '130', Outbound: '140' }, { Inbound: '150', Outbound: '160' }];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 280, Outbound: 320 },
      { Inbound: 360, Outbound: 400 },
    ]);
  });

  it('should handle missing data in zia', () => {
    const total = [{}, {}];
    const zia = [{}, { Inbound: '30', Outbound: '40' }];
    const zpa = [{ Inbound: '50', Outbound: '60' }, { Inbound: '70', Outbound: '80' }];
    const cloud = [{ Inbound: '90', Outbound: '100' }, { Inbound: '110', Outbound: '120' }];
    const direct = [{ Inbound: '130', Outbound: '140' }, { Inbound: '150', Outbound: '160' }];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 270, Outbound: 300 },
      { Inbound: 360, Outbound: 400 },
    ]);
  });

  it('should handle missing data in zpa', () => {
    const total = [{}, {}];
    const zia = [{ Inbound: '10', Outbound: '20' }, { Inbound: '30', Outbound: '40' }];
    const zpa = [{}, { Inbound: '70', Outbound: '80' }];
    const cloud = [{ Inbound: '90', Outbound: '100' }, { Inbound: '110', Outbound: '120' }];
    const direct = [{ Inbound: '130', Outbound: '140' }, { Inbound: '150', Outbound: '160' }];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 230, Outbound: 260 },
      { Inbound: 360, Outbound: 400 },
    ]);
  });

  it('should handle missing data in cloud', () => {
    const total = [{}, {}];
    const zia = [{ Inbound: '10', Outbound: '20' }, { Inbound: '30', Outbound: '40' }];
    const zpa = [{ Inbound: '50', Outbound: '60' }, { Inbound: '70', Outbound: '80' }];
    const cloud = [{}, { Inbound: '110', Outbound: '120' }];
    const direct = [{ Inbound: '130', Outbound: '140' }, { Inbound: '150', Outbound: '160' }];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 190, Outbound: 220 },
      { Inbound: 360, Outbound: 400 },
    ]);
  });

  it('should handle missing data in direct', () => {
    const total = [{}, {}];
    const zia = [{ Inbound: '10', Outbound: '20' }, { Inbound: '30', Outbound: '40' }];
    const zpa = [{ Inbound: '50', Outbound: '60' }, { Inbound: '70', Outbound: '80' }];
    const cloud = [{ Inbound: '90', Outbound: '100' }, { Inbound: '110', Outbound: '120' }];
    const direct = [{}, { Inbound: '150', Outbound: '160' }];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([
      { Inbound: 150, Outbound: 180 },
      { Inbound: 360, Outbound: 400 },
    ]);
  });

  it('should handle empty input arrays', () => {
    const total = [];
    const zia = [];
    const zpa = [];
    const cloud = [];
    const direct = [];

    const result = mergeTrendInOutData('Inbound', 'Outbound', total, zia, zpa, cloud, direct);

    expect(result).toEqual([]);
  });
});
