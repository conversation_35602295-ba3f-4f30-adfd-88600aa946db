import moment from 'moment';

const convertTrendInterval = (startDate, endDate) => {
  // default - Last 24 Hours
  let trendInterval = 'HOUR';
  const timeInterval = (moment(endDate).unix() * 1000) - (moment(startDate).unix() * 1000);
  
  if (timeInterval < 3600 * 1000) { // 5 Hours
    trendInterval = 'FIVE_MINUTE';
  } else if (timeInterval < 24 * 3600 * 1000) { // 1 week
    trendInterval = 'HOUR';
  } else {
    trendInterval = 'DAY';
  }
  return trendInterval;
};
  
export default convertTrendInterval;
