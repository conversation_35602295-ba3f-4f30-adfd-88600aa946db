import fromMaskToCIDR from './fromMaskToCIDR';

describe('fromMaskToCIDR function', () => {
  it('should return the correct CIDR for a given mask', () => {
    expect(fromMaskToCIDR('255.255.255.255')).toBe(32);
    expect(fromMaskToCIDR('255.255.255.254')).toBe(31);
    expect(fromMaskToCIDR('255.255.255.252')).toBe(30);
    expect(fromMaskToCIDR('255.255.255.248')).toBe(29);
    expect(fromMaskToCIDR('255.255.255.240')).toBe(28);
    expect(fromMaskToCIDR('255.255.255.224')).toBe(27);
    expect(fromMaskToCIDR('255.255.255.192')).toBe(26);
    expect(fromMaskToCIDR('255.255.255.128')).toBe(25);
    expect(fromMaskToCIDR('255.255.255.0')).toBe(24);
    expect(fromMaskToCIDR('255.255.254.0')).toBe(23);
    expect(fromMaskToCIDR('255.255.252.0')).toBe(22);
    expect(fromMaskToCIDR('255.255.248.0')).toBe(21);
    expect(fromMaskToCIDR('255.255.240.0')).toBe(20);
    expect(fromMaskToCIDR('255.255.224.0')).toBe(19);
    expect(fromMaskToCIDR('255.255.192.0')).toBe(18);
    expect(fromMaskToCIDR('255.255.128.0')).toBe(17);
    expect(fromMaskToCIDR('255.255.0.0')).toBe(16);
    expect(fromMaskToCIDR('255.254.0.0')).toBe(15);
    expect(fromMaskToCIDR('255.252.0.0')).toBe(14);
    expect(fromMaskToCIDR('255.248.0.0')).toBe(13);
    expect(fromMaskToCIDR('255.240.0.0')).toBe(12);
    expect(fromMaskToCIDR('255.224.0.0')).toBe(11);
    expect(fromMaskToCIDR('255.192.0.0')).toBe(10);
    expect(fromMaskToCIDR('255.128.0.0')).toBe(9);
    expect(fromMaskToCIDR('255.0.0.0')).toBe(8);
    expect(fromMaskToCIDR('254.0.0.0')).toBe(7);
    expect(fromMaskToCIDR('252.0.0.0')).toBe(6);
    expect(fromMaskToCIDR('248.0.0.0')).toBe(5);
    expect(fromMaskToCIDR('240.0.0.0')).toBe(4);
    expect(fromMaskToCIDR('224.0.0.0')).toBe(3);
    expect(fromMaskToCIDR('192.0.0.0')).toBe(2);
    expect(fromMaskToCIDR('128.0.0.0')).toBe(1);
    expect(fromMaskToCIDR('0.0.0.0')).toBe(0);
  });

  it('should return 0 for an invalid mask', () => {
    expect(fromMaskToCIDR('256.255.255.255')).toBe(0);
    expect(fromMaskToCIDR('255.256.255.255')).toBe(0);
    expect(fromMaskToCIDR('255.255.256.255')).toBe(0);
    expect(fromMaskToCIDR('255.255.255.256')).toBe(0);
    expect(fromMaskToCIDR('abc.def.ghi.jkl')).toBe(0);
    expect(fromMaskToCIDR(null)).toBe(0);
    expect(fromMaskToCIDR(undefined)).toBe(0);
  });
});
