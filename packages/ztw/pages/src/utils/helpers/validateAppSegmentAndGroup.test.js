import { isEmpty } from 'utils/lodash';
import { isZPA } from 'utils/helpers';
import validateAppSegmentAndGroup from './validateAppSegmentAndGroup';

jest.mock('utils/lodash', () => ({
  isEmpty: jest.fn(),
}));

jest.mock('utils/helpers', () => ({
  isZPA: jest.fn(),
}));

describe('validateAppSegmentAndGroup', () => {
  beforeEach(() => {
    isEmpty.mockImplementation((value) => (!value));
    isZPA.mockImplementation((value) => (value === 'zpa'));
  });

  it('should return empty object when all fields are valid', () => {
    const values = {
      allAppSegments: true,
      forwardingMethod: { id: 'zpa' },
      zpaApplicationSegments: ['segment1'],
      zpaApplicationSegmentGroups: ['group1'],
    };

    const result = validateAppSegmentAndGroup(values);
    expect(result).toEqual({});
  });

  it('should return no error when no app segments or groups are provided for ZPA', () => {
    const values = {
      allAppSegments: false,
      forwardingMethod: { id: 'zpa' },
      zpaApplicationSegments: [],
      zpaApplicationSegmentGroups: [],
    };

    isEmpty.mockImplementationOnce((value) => (value.length === 0));

    const result = validateAppSegmentAndGroup(values);
    expect(result).toEqual({});
  });

  it('should return empty object when forwarding method is not ZPA', () => {
    const values = {
      allAppSegments: false,
      forwardingMethod: { id: 'non-zpa' },
      zpaApplicationSegments: [],
      zpaApplicationSegmentGroups: [],
    };

    isZPA.mockImplementationOnce((value) => (value !== 'non-zpa'));

    const result = validateAppSegmentAndGroup(values);
    expect(result).toEqual({});
  });

  it('should return empty object when all app segments are selected', () => {
    const values = {
      allAppSegments: true,
      forwardingMethod: { id: 'zpa' },
      zpaApplicationSegments: [],
      zpaApplicationSegmentGroups: [],
    };

    const result = validateAppSegmentAndGroup(values);
    expect(result).toEqual({});
  });
});
