import { get } from '../lodash';

let csrfToken = 'Fetch';
const setCsrfToken = (response) => {
  csrfToken = get(response, 'headers.x-csrf-token', 'Fetch');
};
  
const getCsrfToken = () => csrfToken;

describe('csrfToken', () => {
  beforeEach(() => {
    // Reset csrfToken before each test
    setCsrfToken({ headers: {} });
  });

  it('should return default csrfToken when not set', () => {
    expect(getCsrfToken()).toBe('Fetch');
  });

  it('should set csrfToken from response headers', () => {
    const response = { headers: { 'x-csrf-token': 'new-token' } };
    setCsrfToken(response);
    expect(getCsrfToken()).toBe('new-token');
  });

  it('should set csrfToken to default when response headers are missing', () => {
    const response = { headers: {} };
    setCsrfToken(response);
    expect(getCsrfToken()).toBe('Fetch');
  });

  it('should set csrfToken to default when response is missing', () => {
    setCsrfToken(undefined);
    expect(getCsrfToken()).toBe('Fetch');
  });

  it('should set csrfToken to default when response headers are null', () => {
    const response = { headers: null };
    setCsrfToken(response);
    expect(getCsrfToken()).toBe(null);
  });
});
