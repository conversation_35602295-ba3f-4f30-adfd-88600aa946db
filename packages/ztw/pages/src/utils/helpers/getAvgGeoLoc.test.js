import getAvgGeoLoc from './getAvgGeoLoc';

describe('getAvgGeoLoc', () => {
  it('returns the single coordinate when only one is provided', () => {
    const coords = [{ latitude: 37.7749, longitude: -122.4194 }];
    expect(getAvgGeoLoc(coords)).toEqual(coords[0]);
  });

  it('calculates the average geolocation for multiple coordinates', () => {
    const coords = [
      { latitude: 37.7749, longitude: -122.4194 },
      { latitude: 34.0522, longitude: -118.2437 },
      { latitude: 40.7128, longitude: -74.0060 },
    ];
    const result = getAvgGeoLoc(coords);
    expect(result).toHaveProperty('latitude');
    expect(result).toHaveProperty('longitude');
  });

  it('handles coordinates at the poles', () => {
    const coords = [
      { latitude: 90, longitude: 0 },
      { latitude: -90, longitude: 0 },
    ];
    const result = getAvgGeoLoc(coords);
    expect(result).toHaveProperty('latitude');
    expect(result).toHaveProperty('longitude');
  });

  it('handles coordinates at the antimeridian', () => {
    const coords = [
      { latitude: 0, longitude: 179.9999 },
      { latitude: 0, longitude: -179.9999 },
    ];
    const result = getAvgGeoLoc(coords);
    expect(result).toHaveProperty('latitude');
    expect(result).toHaveProperty('longitude');
  });

  it('throws no error when given an empty array', () => {
    expect(() => getAvgGeoLoc([])).not.toThrow();
  });
});
