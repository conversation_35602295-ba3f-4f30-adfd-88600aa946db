import convertFromMinutesToTimeUnit from './convertFromMinutesToTimeUnit';

describe('convertFromMinutesToTimeUnit function', () => {
  it('should convert minutes to days', () => {
    const timeInMinutes = 1440; // 24 hours * 60 minutes
    const result = convertFromMinutesToTimeUnit('DAY', timeInMinutes);
    expect(result).toBe(1);
  });

  it('should convert minutes to hours', () => {
    const timeInMinutes = 60;
    const result = convertFromMinutesToTimeUnit('HOUR', timeInMinutes);
    expect(result).toBe(1);
  });

  it('should return the same value for minutes', () => {
    const timeInMinutes = 10;
    const result = convertFromMinutesToTimeUnit('MINUTE', timeInMinutes);
    expect(result).toBe(timeInMinutes);
  });

  it('should return the same value for unknown units', () => {
    const timeInMinutes = 10;
    const result = convertFromMinutesToTimeUnit('SECOND', timeInMinutes);
    expect(result).toBe(timeInMinutes);
  });

  it('should handle zero minutes', () => {
    const timeInMinutes = 0;
    const result = convertFromMinutesToTimeUnit('DAY', timeInMinutes);
    expect(result).toBe(0);
  });

  it('should handle negative minutes', () => {
    const timeInMinutes = -10;
    const result = convertFromMinutesToTimeUnit('DAY', timeInMinutes);
    expect(result).toBe(-10 / (24 * 60));
  });
});
