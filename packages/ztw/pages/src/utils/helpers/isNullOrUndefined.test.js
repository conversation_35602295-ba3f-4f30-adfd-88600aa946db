import isNullOrUndefined from './isNullOrUndefined';

describe('isNullOrUndefined function', () => {
  it('should return true for undefined', () => {
    expect(isNullOrUndefined(undefined)).toBe(true);
  });

  it('should return true for null', () => {
    expect(isNullOrUndefined(null)).toBe(true);
  });

  it('should return false for a number', () => {
    expect(isNullOrUndefined(123)).toBe(false);
  });

  it('should return false for a string', () => {
    expect(isNullOrUndefined('hello')).toBe(false);
  });

  it('should return false for an object', () => {
    expect(isNullOrUndefined({})).toBe(false);
  });

  it('should return false for an array', () => {
    expect(isNullOrUndefined([])).toBe(false);
  });

  it('should return false for a boolean', () => {
    expect(isNullOrUndefined(true)).toBe(false);
  });

  it('should return false for a function', () => {
    expect(isNullOrUndefined(() => {})).toBe(false);
  });
});
