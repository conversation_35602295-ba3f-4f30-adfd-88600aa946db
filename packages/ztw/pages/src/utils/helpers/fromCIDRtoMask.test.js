import fromCIDRtoMask from './fromCIDRtoMask';

describe('fromCIDRtoMask function', () => {
  it('should return the correct mask for a given CIDR', () => {
    expect(fromCIDRtoMask(32)).toBe('255.255.255.255');
    expect(fromCIDRtoMask(31)).toBe('255.255.255.254');
    expect(fromCIDRtoMask(30)).toBe('255.255.255.252');
    expect(fromCIDRtoMask(29)).toBe('255.255.255.248');
    expect(fromCIDRtoMask(28)).toBe('255.255.255.240');
    expect(fromCIDRtoMask(27)).toBe('255.255.255.224');
    expect(fromCIDRtoMask(26)).toBe('255.255.255.192');
    expect(fromCIDRtoMask(25)).toBe('255.255.255.128');
    expect(fromCIDRtoMask(24)).toBe('255.255.255.0');
    expect(fromCIDRtoMask(23)).toBe('255.255.254.0');
    expect(fromCIDRtoMask(22)).toBe('255.255.252.0');
    expect(fromCIDRtoMask(21)).toBe('255.255.248.0');
    expect(fromCIDRtoMask(20)).toBe('255.255.240.0');
    expect(fromCIDRtoMask(19)).toBe('255.255.224.0');
    expect(fromCIDRtoMask(18)).toBe('255.255.192.0');
    expect(fromCIDRtoMask(17)).toBe('255.255.128.0');
    expect(fromCIDRtoMask(16)).toBe('255.255.0.0');
    expect(fromCIDRtoMask(15)).toBe('255.254.0.0');
    expect(fromCIDRtoMask(14)).toBe('255.252.0.0');
    expect(fromCIDRtoMask(13)).toBe('255.248.0.0');
    expect(fromCIDRtoMask(12)).toBe('255.240.0.0');
    expect(fromCIDRtoMask(11)).toBe('255.224.0.0');
    expect(fromCIDRtoMask(10)).toBe('255.192.0.0');
    expect(fromCIDRtoMask(9)).toBe('255.128.0.0');
    expect(fromCIDRtoMask(8)).toBe('255.0.0.0');
    expect(fromCIDRtoMask(7)).toBe('254.0.0.0');
    expect(fromCIDRtoMask(6)).toBe('252.0.0.0');
    expect(fromCIDRtoMask(5)).toBe('248.0.0.0');
    expect(fromCIDRtoMask(4)).toBe('240.0.0.0');
    expect(fromCIDRtoMask(3)).toBe('224.0.0.0');
    expect(fromCIDRtoMask(2)).toBe('192.0.0.0');
    expect(fromCIDRtoMask(1)).toBe('128.0.0.0');
    expect(fromCIDRtoMask(0)).toBe('0.0.0.0');
  });

  it('should return an empty string for invalid CIDR values', () => {
    expect(fromCIDRtoMask(-1)).toBe('');
    expect(fromCIDRtoMask(33)).toBe('');
    expect(fromCIDRtoMask('a')).toBe('');
    expect(fromCIDRtoMask(null)).toBe('');
    expect(fromCIDRtoMask(undefined)).toBe('');
  });
});
