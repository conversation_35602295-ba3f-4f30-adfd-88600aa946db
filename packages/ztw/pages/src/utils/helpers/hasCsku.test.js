import hasCsku from './hasCsku';

describe('hasCsku function', () => {
  it('should return true when the sku array contains a C-Connector sku', () => {
    const sku = ['BC_CONNECTOR', 'OTHER_SKU'];
    expect(hasCsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains B_AND_C_CONNECTOR sku', () => {
    const sku = ['B_AND_C_CONNECTOR', 'OTHER_SKU'];
    expect(hasCsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains C-CONNECTOR sku', () => {
    const sku = ['C-CONNECTOR', 'OTHER_SKU'];
    expect(hasCsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains C_CONNECTOR sku', () => {
    const sku = ['C_CONNECTOR', 'OTHER_SKU'];
    expect(hasCsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains WORKLOAD_CONNECTOR_VM sku', () => {
    const sku = ['WORKLOAD_CONNECTOR_VM', 'OTHER_SKU'];
    expect(hasCsku(sku)).toBe(true);
  });

  it('should return false when the sku array does not contain any C-Connector sku', () => {
    const sku = ['OTHER_SKU', 'ANOTHER_SKU'];
    expect(hasCsku(sku)).toBe(false);
  });

  it('should return false when the sku array is empty', () => {
    const sku = [];
    expect(hasCsku(sku)).toBe(false);
  });
});
