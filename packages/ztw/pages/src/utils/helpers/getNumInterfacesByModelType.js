import PropTypes from 'prop-types';

function getNumInterfacesByModelType(modelType) {
  if (modelType === 'ZT400') return 4;
  if (modelType === 'ZT600') return 6;
  if (modelType === 'ZT800') return 8;
  return 0;
}

getNumInterfacesByModelType.propTypes = {
  modelType: PropTypes.string,
};
getNumInterfacesByModelType.defaultProps = {
  modelType: '',
};

export default getNumInterfacesByModelType;
