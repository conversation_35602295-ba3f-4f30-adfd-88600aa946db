import convertArrayToObject from './convertArrayToObject';

describe('convertArrayToObject', () => {
  it('should return an empty array when input array is empty', () => {
    expect(convertArrayToObject([])).toEqual([]);
  });

  it('should return an object when input array has one item', () => {
    const array = [{ key: 'value' }];
    expect(convertArrayToObject(array)).toEqual({ key: 'value' });
  });

  it('should merge objects when input array has multiple items', () => {
    const array = [{ key1: 'value1' }, { key2: 'value2' }];
    expect(convertArrayToObject(array)).toEqual({ key1: 'value1', key2: 'value2' });
  });

  it('should handle nested objects', () => {
    const array = [{ key1: 'value1' }, { key2: { nestedKey: 'nestedValue' } }];
    expect(convertArrayToObject(array)).toEqual({ key1: 'value1', key2: { nestedKey: 'nestedValue' } });
  });

  it('should handle duplicate keys', () => {
    const array = [{ key: 'value1' }, { key: 'value2' }];
    expect(convertArrayToObject(array)).toEqual({ key: 'value2' });
  });
});
