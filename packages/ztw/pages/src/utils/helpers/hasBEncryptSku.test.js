import hasBEncryptSku from './hasBEncryptSku';

describe('hasBEncryptSku function', () => {
  it('should return true when sku contains B_AND_C_CONNECTOR_ENCRYPT', () => {
    const sku = ['B_AND_C_CONNECTOR_ENCRYPT', 'OTHER_SKU'];
    expect(hasBEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains BC_DEVICE_TUNNEL_ENCRYPT', () => {
    const sku = ['OTHER_SKU', 'BC_DEVICE_TUNNEL_ENCRYPT'];
    expect(hasBEncryptSku(sku)).toBe(true);
  });

  it('should return true when sku contains both B_AND_C_CONNECTOR_ENCRYPT and BC_DEVICE_TUNNEL_ENCRYPT', () => {
    const sku = ['B_AND_C_CONNECTOR_ENCRYPT', 'BC_DEVICE_TUNNEL_ENCRYPT'];
    expect(hasBEncryptSku(sku)).toBe(true);
  });

  it('should return false when sku does not contain B_AND_C_CONNECTOR_ENCRYPT or BC_DEVICE_TUNNEL_ENCRYPT', () => {
    const sku = ['OTHER_SKU1', 'OTHER_SKU2'];
    expect(hasBEncryptSku(sku)).toBe(false);
  });

  it('should return false when sku is empty', () => {
    const sku = [];
    expect(hasBEncryptSku(sku)).toBe(false);
  });
});
