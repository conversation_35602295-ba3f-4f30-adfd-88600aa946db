import ipRange from './ipRange';

describe('ipRange function', () => {
  it('should return an empty string when either ipString1 or ipString2 is undefined', () => {
    expect(ipRange('***********', undefined)).toBe('');
    expect(ipRange(undefined, '***********')).toBe('');
  });

  it('should return the first IP address when both IP addresses are the same', () => {
    expect(ipRange('***********', '***********')).toBe('***********');
  });

  it('should return a range of IP addresses when they are different', () => {
    expect(ipRange('***********', '***********')).toBe('*********** - ***********');
  });

  it('should return a range of IP addresses when the difference is in the last octet', () => {
    expect(ipRange('***********', '***********0')).toBe('*********** - ***********0');
  });

  it('should return a range of IP addresses when the difference is in the second octet', () => {
    expect(ipRange('***********', '***********')).toBe('*********** - ***********');
  });

  it('should return a range of IP addresses when the difference is in the first octet', () => {
    expect(ipRange('***********', '***********')).toBe('*********** - ***********');
  });
});
