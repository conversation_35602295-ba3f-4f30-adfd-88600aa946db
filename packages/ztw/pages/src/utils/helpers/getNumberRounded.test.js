import getNumberRounded from './getNumberRounded';

describe('getNumberRounded function', () => {
  it('should return 0 when the input number is NaN', () => {
    expect(getNumberRounded(NaN, 2)).toBe(0);
  });

  it('should return 0 when the precision is NaN', () => {
    expect(getNumberRounded(10.1234, NaN)).toBe(0);
  });

  it('should round the number to the specified precision', () => {
    expect(getNumberRounded(10.1234, 2)).toBe(10.12);
  });

  it('should round the number up when the decimal value is greater than or equal to 0.5', () => {
    expect(getNumberRounded(10.125, 2)).toBe(10.13);
  });

  it('should round the number down when the decimal value is less than 0.5', () => {
    expect(getNumberRounded(10.124, 2)).toBe(10.12);
  });

  it('should handle negative numbers correctly', () => {
    expect(getNumberRounded(-10.1234, 2)).toBe(-10.12);
  });

  it('should handle zero correctly', () => {
    expect(getNumberRounded(0, 2)).toBe(0);
  });

  it('should handle large numbers correctly', () => {
    expect(getNumberRounded(123456789.1234, 2)).toBe(123456789.12);
  });
});
