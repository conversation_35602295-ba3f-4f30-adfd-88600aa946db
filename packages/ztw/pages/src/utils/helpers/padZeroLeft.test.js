import padZeroLeft from './padZeroLeft';

describe('padZeroLeft function', () => {
  it('should pad a single digit number with zeros to the specified size', () => {
    expect(padZeroLeft(1, 3)).toBe('001');
  });

  it('should pad a multi-digit number with zeros to the specified size', () => {
    expect(padZeroLeft(123, 5)).toBe('00123');
  });

  it('should not pad a number that is already the specified size', () => {
    expect(padZeroLeft(123, 1)).toBe('123');
  });

  it('should pad a number with zeros to the specified size when the number is zero', () => {
    expect(padZeroLeft(0, 3)).toBe('000');
  });

  it('should return the number as a string when no size is specified', () => {
    expect(padZeroLeft(123)).toBe('123');
  });

  it('should return the number as a string when the size is less than the number of digits', () => {
    expect(padZeroLeft(123, 2)).toBe('123');
  });

  it('should handle non-numeric inputs by converting them to strings', () => {
    expect(padZeroLeft('abc', 5)).toBe('00abc');
  });

  it('should handle non-numeric size inputs by converting them to numbers', () => {
    expect(padZeroLeft(123, '5')).toBe('00123');
  });
});
