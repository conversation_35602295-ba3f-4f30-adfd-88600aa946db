import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding } from '@fortawesome/pro-regular-svg-icons';
import AWS from 'images/aws.png';
import awsActive from 'images/awsActive.png';
import awsInactive from 'images/awsInactive.png';
import azure from 'images/azure.png';
import azureActive from 'images/azureActive.png';
import azureInactive from 'images/azureInactive.png';
import gcp from 'images/gcp.png';
import gcpActive from 'images/gcpActive.png';
import gcpInactive from 'images/gcpInactive.png';

const getIcon = (deploymentType = '', status = '', deviceType = '') => {
  if (deploymentType && deploymentType === 'AWS') {
    if (status && status === 'Active') {
      return <img src={awsActive} className="filter-icon" alt="AWS Active" />;
    } if (status && status === 'Inactive') {
      return <img src={awsInactive} className="filter-icon" alt="AWS Inactive" />;
    }
    return <img src={AWS} className="filter-icon" alt="AWS" />;
  }
  
  if (deploymentType && deploymentType === 'AZURE') {
    if (status && status === 'Active') {
      return <img src={azureActive} className="filter-icon" alt="Azure Active" />;
    } if (status && status === 'Inactive') {
      return <img src={azureInactive} className="filter-icon" alt="Azure Inactive" />;
    }
    return <img src={azure} className="filter-icon" alt="Azure" />;
  }
  
  if (deploymentType && deploymentType === 'GCP') {
    if (status && status === 'Active') {
      return <img src={gcpActive} className="filter-icon" alt="GCP Active" />;
    } if (status && status === 'Inactive') {
      return <img src={gcpInactive} className="filter-icon" alt="GCP Inactive" />;
    }
    return <img src={gcp} className="filter-icon" alt="GCP" />;
  }

  if (deviceType === 'PHYSICAL' || (deploymentType
              && (deploymentType === 'CENTOS'
                || deploymentType === 'REDHAT_LINUX'
                || deploymentType === 'MICROSOFT_HYPER_V'
                || deploymentType === 'VMWARE_ESXI'))) {
    if (status && status === 'Active') {
      return <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-active" alt="Branch Active" />;
    } if (status && status === 'Inactive') {
      return <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch-inactive" alt="Branch Inactive" />;
    }
    return <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch" alt="Branch" />;
  } // CENTOS REDHAT_LINUX VMWARE_ESXI
  return <FontAwesomeIcon icon={faBuilding} className="fontStyle fas map-icon-branch" alt="Branch" />;
};

export default getIcon;
