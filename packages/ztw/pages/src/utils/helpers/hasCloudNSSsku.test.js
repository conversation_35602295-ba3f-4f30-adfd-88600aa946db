import hasCloudNSSsku from './hasCloudNSSsku';

describe('hasCloudNSSsku function', () => {
  it('should return true when skuArray contains Z_CLOUD_NSS_FW', () => {
    const skuArray = ['Z_CLOUD_NSS_FW', 'OTHER_SKU'];
    expect(hasCloudNSSsku(skuArray)).toBe(true);
  });

  it('should return true when skuArray contains Z_CLOUD_NSS_FW at any position', () => {
    const skuArray = ['OTHER_SKU', 'Z_CLOUD_NSS_FW', 'ANOTHER_SKU'];
    expect(hasCloudNSSsku(skuArray)).toBe(true);
  });

  it('should return false when skuArray does not contain Z_CLOUD_NSS_FW', () => {
    const skuArray = ['OTHER_SKU', 'ANOTHER_SKU'];
    expect(hasCloudNSSsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is empty', () => {
    const skuArray = [];
    expect(hasCloudNSSsku(skuArray)).toBe(false);
  });

  it('should return false when skuArray is null or undefined', () => {
    expect(hasCloudNSSsku(null)).toBe(false);
    expect(hasCloudNSSsku(undefined)).toBe(false);
  });
});
