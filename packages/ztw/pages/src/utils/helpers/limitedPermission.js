const limitedPermission = (data, isReadOnly) => {
  if (!isReadOnly) return data;

  const { featurePermissions } = data;
  const dataKeys = Object.keys(featurePermissions);
  
  dataKeys.forEach((key) => {
    featurePermissions[key] = featurePermissions[key] === 'READ_WRITE' ? 'READ_ONLY' : featurePermissions[key];
  });

  return { ...data, featurePermissions };
};
  
export default limitedPermission;
