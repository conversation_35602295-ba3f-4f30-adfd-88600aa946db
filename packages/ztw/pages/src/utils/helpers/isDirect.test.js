import isDirect from './isDirect';

describe('isDirect function', () => {
  it('should return true when forwarding method is DIRECT', () => {
    expect(isDirect('DIRECT')).toBe(true);
  });

  it('should return true when forwarding method is ECDIRECTSCTPXFORM', () => {
    expect(isDirect('ECDIRECTSCTPXFORM')).toBe(true);
  });

  it('should return false when forwarding method is not DIRECT or ECDIRECTSCTPXFORM', () => {
    expect(isDirect('OTHER_METHOD')).toBe(false);
  });

  it('should return false when forwarding method is null', () => {
    expect(isDirect(null)).toBe(false);
  });

  it('should return false when forwarding method is undefined', () => {
    expect(isDirect(undefined)).toBe(false);
  });

  it('should return false when forwarding method is an empty string', () => {
    expect(isDirect('')).toBe(false);
  });
});
