import getSubscriptionLicenses from './getSubscriptionLicenses';

describe('getSubscriptionLicenses function', () => {
  it('should return 0 when subscriptions array is empty', () => {
    expect(getSubscriptionLicenses([], 'any-value')).toBe(0);
  });

  it('should return 0 when subscriptions array does not contain the specified id', () => {
    const subscriptions = [
      { id: 'subscription-1', licenses: 10 },
      { id: 'subscription-2', licenses: 20 },
    ];
    expect(getSubscriptionLicenses(subscriptions, 'subscription-3')).toBe(0);
  });

  it('should return the licenses of the subscription with the specified id', () => {
    const subscriptions = [
      { id: 'subscription-1', licenses: 10 },
      { id: 'subscription-2', licenses: 20 },
    ];
    expect(getSubscriptionLicenses(subscriptions, 'subscription-1')).toBe(10);
    expect(getSubscriptionLicenses(subscriptions, 'subscription-2')).toBe(20);
  });

  it('should return 0 when subscriptions is null or undefined', () => {
    expect(getSubscriptionLicenses(null, 'any-value')).toBe(0);
    expect(getSubscriptionLicenses(undefined, 'any-value')).toBe(0);
  });

  it('should return 0 when value is null or undefined', () => {
    const subscriptions = [
      { id: 'subscription-1', licenses: 10 },
      { id: 'subscription-2', licenses: 20 },
    ];
    expect(getSubscriptionLicenses(subscriptions, null)).toBe(0);
    expect(getSubscriptionLicenses(subscriptions, undefined)).toBe(0);
  });
});
