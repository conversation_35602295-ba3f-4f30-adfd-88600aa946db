import i18n from 'utils/i18n';
import orderCustomizeColumns from './orderCustomizeColumns';

jest.mock('utils/i18n', () => ({
  t: jest.fn(),
}));

describe('orderCustomizeColumns', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return the original array if it has less than 3 elements', () => {
    const input = [{}, {}, {}];
    const result = orderCustomizeColumns(input);
    expect(result).toEqual(input);
  });

  it('should reorder the array correctly', () => {
    const input = [
      { name: 'A', visible: true },
      { name: 'B', visible: true },
      { name: 'C', visible: false },
      { name: 'D', visible: true },
      { name: 'E', visible: false },
    ];
    i18n.t.mockImplementation((name) => name);
    const result = orderCustomizeColumns(input);
    expect(result).toEqual([
      { name: 'A', visible: true },
      { name: 'B', visible: true },
      { labelTralslation: 'D', name: 'D', visible: true },
      { labelTralslation: 'C', name: 'C', visible: false },
      { name: 'E', visible: false },
    ]);
  });

  it('should handle an empty array', () => {
    const input = [];
    const result = orderCustomizeColumns(input);
    expect(result).toEqual([]);
  });
});
