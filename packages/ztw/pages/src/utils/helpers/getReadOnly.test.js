import getReadOnly from './getReadOnly';

describe('getReadOnly function', () => {
  it('should return true when authType is SUPPORT_ACCESS_READ_ONLY', () => {
    const permissionReadOnly = 'READ_WRITE';
    const authType = 'SUPPORT_ACCESS_READ_ONLY';
    expect(getReadOnly(permissionReadOnly, authType)).toBe(true);
  });

  it('should return true when permissionReadOnly is READ_ONLY and authType is not SUPPORT_ACCESS_FULL', () => {
    const permissionReadOnly = 'READ_ONLY';
    const authType = 'OTHER_AUTH_TYPE';
    expect(getReadOnly(permissionReadOnly, authType)).toBe(true);
  });

  it('should return false when permissionReadOnly is READ_ONLY and authType is SUPPORT_ACCESS_FULL', () => {
    const permissionReadOnly = 'READ_ONLY';
    const authType = 'SUPPORT_ACCESS_FULL';
    expect(getReadOnly(permissionReadOnly, authType)).toBe(false);
  });

  it('should return false when permissionReadOnly is READ_WRITE and authType is not SUPPORT_ACCESS_READ_ONLY', () => {
    const permissionReadOnly = 'READ_WRITE';
    const authType = 'OTHER_AUTH_TYPE';
    expect(getReadOnly(permissionReadOnly, authType)).toBe(false);
  });

  it('should return false when permissionReadOnly is undefined and authType is not SUPPORT_ACCESS_READ_ONLY', () => {
    const permissionReadOnly = undefined;
    const authType = 'OTHER_AUTH_TYPE';
    expect(getReadOnly(permissionReadOnly, authType)).toBe(false);
  });

  it('should return false when authType is undefined', () => {
    const permissionReadOnly = 'READ_ONLY';
    const authType = undefined;
    expect(getReadOnly(permissionReadOnly, authType)).toBe(true);
  });
});
