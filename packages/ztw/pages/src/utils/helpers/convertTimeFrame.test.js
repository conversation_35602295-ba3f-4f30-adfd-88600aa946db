import convertTimeFrame from './convertTimeFrame';

const tolerance = 2 * 10000; // 20 seconds

describe('convertTimeFrame function', () => {
  it('should return the default time frame (last 24 hours) when no time frame is provided', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 24 * 60 * 60 * 1000);
    expect(convertTimeFrame()).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame()).toBeGreaterThanOrEqual(expectedTime - tolerance);
  });

  it('should return the correct time frame for last 1 minute', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 1 * 60 * 1000);
    expect(convertTimeFrame('last_1_min')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_1_min')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 2 minutes', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 2 * 60 * 1000);
    expect(convertTimeFrame('last_2_mins')).toBeLessThanOrEqual(expectedTime) + tolerance;
    expect(convertTimeFrame('last_2_mins')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 5 minutes', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 5 * 60 * 1000);
    expect(convertTimeFrame('last_5_mins')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_5_mins')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 15 minutes', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 15 * 60 * 1000);
    expect(convertTimeFrame('last_15_mins')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_15_mins')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 30 minutes', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 30 * 60 * 1000);
    expect(convertTimeFrame('last_30_mins')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_30_mins')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 1 hour', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 1 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_1_hour')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_1_hour')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 2 hours', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 2 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_2_hours')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_2_hours')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 5 hours', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 5 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_5_hours')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_5_hours')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 10 hours', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 10 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_10_hours')).toBeLessThanOrEqual(expectedTime);
    expect(convertTimeFrame('last_10_hours')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 1 week', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (7 * 24 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_1_week')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_1_week')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the correct time frame for last 1 month', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (30 * 24 * 60 * 60 * 1000);
    expect(convertTimeFrame('last_1_month')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('last_1_month')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });

  it('should return the default time frame when an invalid time frame is provided', () => {
    const currentTime = Date.now();
    const expectedTime = currentTime - (1 * 24 * 60 * 60 * 1000);
    expect(convertTimeFrame('invalid_time_frame')).toBeLessThanOrEqual(expectedTime + tolerance);
    expect(convertTimeFrame('invalid_time_frame')).toBeGreaterThanOrEqual(expectedTime - tolerance); // account for minor timing discrepancies
  });
});
