import convertHexaToDecimal from './convertHexaToDecimal';

describe('convertHexaToDecimal function', () => {
  it('should convert a hexadecimal string to a decimal number', () => {
    expect(convertHexaToDecimal('A')).toBe(10);
    expect(convertHexaToDecimal('FF')).toBe(255);
    expect(convertHexaToDecimal('100')).toBe(256);
  });

  it('should handle uppercase and lowercase hexadecimal strings', () => {
    expect(convertHexaToDecimal('a')).toBe(10);
    expect(convertHexaToDecimal('fF')).toBe(255);
  });

  it('should handle hexadecimal strings with leading zeros', () => {
    expect(convertHexaToDecimal('00A')).toBe(10);
    expect(convertHexaToDecimal('000FF')).toBe(255);
  });
});
