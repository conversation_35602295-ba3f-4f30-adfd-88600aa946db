import { orderBy, cloneDeep } from 'utils/lodash';
import i18n from 'utils/i18n';

const orderCustomizeColumns = (input) => {
  const array = cloneDeep(input);
  if (array.length < 2) return array;
  const firstColumn = array[0];
  const secondColumn = array[1];
  const lastColumn = array[array.length - 1];
  const newArray = orderBy(
    array
      .slice(2, array.length - 1)
      .map((x) => ({ ...x, labelTralslation: i18n.t(x.name) })),
    ['labelTralslation'],
  );
  
  const selectedColumns = newArray.filter((x) => x.visible);
  const unselectedColumns = newArray.filter((x) => !x.visible);
  
  return [firstColumn, secondColumn, ...selectedColumns, ...unselectedColumns, lastColumn];
};

export default orderCustomizeColumns;
