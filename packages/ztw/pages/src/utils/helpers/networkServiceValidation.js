import { isEmpty } from 'utils/lodash';
import { portRange } from 'utils/validations';
import { convertPortObjectToString } from 'utils/helpers';

const networkServiceValidation = (values) => {
  const { formMode, name } = values;
  const errors = {};
  let {
    destTcpPorts, destUdpPorts, destSctpPorts, srcTcpPorts, srcUdpPorts, srcSctpPorts,
  } = values || {};

  if (formMode === 'EDIT') {
    destTcpPorts = destTcpPorts && typeof destTcpPorts[0] === 'string' ? destTcpPorts : convertPortObjectToString(destTcpPorts || []);
    destUdpPorts = destUdpPorts && typeof destUdpPorts[0] === 'string' ? destUdpPorts : convertPortObjectToString(destUdpPorts || []);
    destSctpPorts = destSctpPorts && typeof destSctpPorts[0] === 'string' ? destSctpPorts : convertPortObjectToString(destSctpPorts || []);
    srcTcpPorts = srcTcpPorts && typeof srcTcpPorts[0] === 'string' ? srcTcpPorts : convertPortObjectToString(srcTcpPorts || []);
    srcUdpPorts = srcUdpPorts && typeof srcUdpPorts[0] === 'string' ? srcUdpPorts : convertPortObjectToString(srcUdpPorts || []);
    srcSctpPorts = srcSctpPorts && typeof srcSctpPorts[0] === 'string' ? srcSctpPorts : convertPortObjectToString(srcSctpPorts || []);
  }
  
  if (!isEmpty(destTcpPorts) && destTcpPorts.some((x) => portRange(x) !== '')) {
    errors.destTcpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }
  if (!isEmpty(destUdpPorts) && destUdpPorts.some((x) => portRange(x) !== '')) {
    errors.destUdpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }
  if (!isEmpty(destSctpPorts) && destSctpPorts.some((x) => portRange(x) !== '')) {
    errors.destSctpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }
  if (!isEmpty(srcTcpPorts) && srcTcpPorts.some((x) => portRange(x) !== '')) {
    errors.srcTcpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }
  if (!isEmpty(srcUdpPorts) && srcUdpPorts.some((x) => portRange(x) !== '')) {
    errors.srcUdpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }
  if (!isEmpty(srcSctpPorts) && srcSctpPorts.some((x) => portRange(x) !== '')) {
    errors.srcSctpPorts = 'VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS';
  }

  if (isEmpty(destTcpPorts) && isEmpty(destUdpPorts) && isEmpty(destSctpPorts)
    && !isEmpty(name)) {
    errors.destTcpPorts = 'VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT';
    errors.destUdpPorts = 'VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT';
    errors.destSctpPorts = 'VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT';
  }

  return errors;
};
  
export default networkServiceValidation;
