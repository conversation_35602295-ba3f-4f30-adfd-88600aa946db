/* eslint-disable no-bitwise */
const ipv4SubnetToBinStr = (addressBinStr, netmaskBinStr) => {
  let pos = 0;
  let netaddressBinStr = '';
  let aBit = 0; let nmBit = 0;
  for (pos = 0; pos < 32; pos += 1) {
    aBit = addressBinStr.substr(pos, 1);
    nmBit = netmaskBinStr.substr(pos, 1);
    if (aBit === nmBit) {
      netaddressBinStr += aBit.toString();
    } else {
      netaddressBinStr += '0';
    }
  }
  return netaddressBinStr;
};
  
/* logical OR between address & NOT netmask */
const ipv4SubnetCastToBinStr = (addressBinStr, netmaskBinStr) => {
  let pos = 0;
  let netbcastBinStr = '';
  let aBit = 0; let nmBit = 0;
  for (pos = 0; pos < 32; pos += 1) {
    aBit = Number(addressBinStr.substr(pos, 1));
    nmBit = Number(netmaskBinStr.substr(pos, 1));
    if (nmBit) {
      nmBit = 0;
    } else {
      nmBit = 1;
    }
      
    if (aBit || nmBit) {
      netbcastBinStr += '1';
    } else {
      netbcastBinStr += '0';
    }
  }
  return netbcastBinStr;
};

/* dotted-quad IP to integer */
const ipv4DotQuadToInt = (strbits) => {
  const split = strbits.split('.', 4);
  const myInt = (
    parseFloat(split[0] * 16777216) // 2^24
    + parseFloat(split[1] * 65536) // 2^16
    + parseFloat(split[2] * 256) // 2^8
    + parseFloat(split[3])
  );
  return myInt;
};

/* integer IP to dotted-quad */
const ipv4IntToDotQuad = (strnum) => {
  const byte1 = (strnum >>> 24);
  const byte2 = (strnum >>> 16) & 255;
  const byte3 = (strnum >>> 8) & 255;
  const byte4 = strnum & 255;
  return (byte1 + '.' + byte2 + '.' + byte3 + '.' + byte4);
};

/* integer IP to binary string representation */
const ipv4IntToBinStr = (strnum) => {
  let numStr = strnum.toString(2); /* Initialize return value as string */
  const numZeros = 32 - numStr.length; /* Calculate no. of zeros */
  if (numZeros > 0) {
    for (let i = 1; i <= numZeros; i += 1) {
      numStr = '0' + numStr;
    }
  }
  return numStr;
};

/* binary string IP to integer representation */
const ipv4BinStrtoInt = (binstr) => {
  return parseInt(binstr, 2);
};

/* convert # of bits to a string representation of the binary value */
const ipv4BitsNMtoBinStrNM = (bitsNM) => {
  let bitString = '';
  let numberOfOnes = bitsNM;
  while (numberOfOnes > 0) {
    bitString += '1'; /* fill in ones */
    numberOfOnes -= 1;
  }
  let numberOfZeros = 32 - bitsNM;
  while (numberOfZeros > 0) {
    bitString += '0'; /* pad remaining with zeros */
    numberOfZeros -= 1;
  }
  return bitString;
};

export {
  ipv4SubnetToBinStr,
  ipv4SubnetCastToBinStr,
  ipv4DotQuadToInt,
  ipv4IntToDotQuad,
  ipv4IntToBinStr,
  ipv4BinStrtoInt,
  ipv4BitsNMtoBinStrNM,
};
