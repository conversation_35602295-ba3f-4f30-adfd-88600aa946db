// getPermission.test.js
import getPermission from './getPermission';

describe('getPermission function', () => {
  it('should return false when permissions is not a string and accessPermissions is empty', () => {
    const permissions = [];
    const accessPermissions = {};
    expect(getPermission(permissions, accessPermissions)).toBe(false);
  });

  it('should return true when permissions is not a string and accessPermissions has a valid permission', () => {
    const permissions = ['read', 'write'];
    const accessPermissions = { read: 'READ', write: 'WRITE' };
    expect(getPermission(permissions, accessPermissions)).toBe(true);
  });

  it('should return false when permissions is not a string and accessPermissions has no valid permissions', () => {
    const permissions = ['read', 'write'];
    const accessPermissions = { read: 'NONE', write: 'NONE' };
    expect(getPermission(permissions, accessPermissions)).toBe(false);
  });

  it('should return true when permissions is a string and accessPermissions has a valid permission', () => {
    const permissions = 'read';
    const accessPermissions = { read: 'READ' };
    expect(getPermission(permissions, accessPermissions)).toBe(true);
  });

  it('should return false when permissions is a string and accessPermissions has no valid permission', () => {
    const permissions = 'read';
    const accessPermissions = { read: 'NONE' };
    expect(getPermission(permissions, accessPermissions)).toBe(false);
  });
});
