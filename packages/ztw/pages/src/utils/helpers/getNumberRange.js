const getNumberRange = (value) => {
  let updatedValue = {};
  // Represent the numbers in minimalized format
  // If the value is more than 1000000000000
  if (value > 1000000000000) {
    updatedValue = {
      value: parseFloat((value / 1000000000000).toFixed(2)),
      unit: 'T',
    };
    // If the value is more than 1000000000
  } else if (value > 1000000000) {
    updatedValue = {
      value: parseFloat((value / 1000000000).toFixed(2)),
      unit: 'B',
    };
    // If the value is more than 1000000
  } else if (value > 1000000) {
    updatedValue = {
      value: parseFloat((value / 1000000).toFixed(2)),
      unit: 'M',
    };
    // If the value is more than 1000000
  } else if (value > 1000) {
    updatedValue = {
      value: parseFloat((value / 1000).toFixed(2)),
      unit: 'K',
    };
  } else {
    updatedValue = {
      value,
      unit: null,
    };
  }
  return updatedValue;
};
          
export default getNumberRange;
