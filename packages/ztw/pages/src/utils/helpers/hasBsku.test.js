import hasBsku from './hasBsku';

describe('hasBsku function', () => {
  it('should return true when the sku array contains a B connector', () => {
    const sku = ['BC_CONNECTOR', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a B and C connector', () => {
    const sku = ['B_AND_C_CONNECTOR', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a B-CONNECTOR', () => {
    const sku = ['B-CONNECTOR', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a BC_DEVICE_CONNECTOR_400', () => {
    const sku = ['BC_DEVICE_CONNECTOR_400', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a BC_DEVICE_CONNECTOR_600', () => {
    const sku = ['BC_DEVICE_CONNECTOR_600', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a BC_DEVICE_CONNECTOR_800', () => {
    const sku = ['BC_DEVICE_CONNECTOR_800', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return true when the sku array contains a BC_DEVICE_CONNECTOR_VM', () => {
    const sku = ['BC_DEVICE_CONNECTOR_VM', 'OTHER'];
    expect(hasBsku(sku)).toBe(true);
  });

  it('should return false when the sku array does not contain a B connector', () => {
    const sku = ['OTHER', 'OTHER'];
    expect(hasBsku(sku)).toBe(false);
  });

  it('should return false when the sku array is empty', () => {
    const sku = [];
    expect(hasBsku(sku)).toBe(false);
  });
});
