import getUnitRange from './getUnitRange';

describe('getUnitRange function', () => {
  it('should return correct scaled units and factor for NUMBER type', () => {
    expect(getUnitRange(1000000000000000, 'NUMBER')).toEqual({ scaledUnits: 'T', factor: 1000000000000 });
    expect(getUnitRange(1000000000000, 'NUMBER')).toEqual({ scaledUnits: 'G', factor: 1000000000 });
    expect(getUnitRange(1000000000, 'NUMBER')).toEqual({ scaledUnits: 'M', factor: 1000000 });
    expect(getUnitRange(1000000, 'NUMBER')).toEqual({ scaledUnits: 'K', factor: 1000 });
    expect(getUnitRange(1000, 'NUMBER')).toEqual({ scaledUnits: '', factor: 1 });
  });

  it('should return correct scaled units and factor for BYTES type', () => {
    expect(getUnitRange(1099511627777, 'BYTES')).toEqual({ scaledUnits: 'Tbps', factor: 1099511627776 });
    expect(getUnitRange(1073741825, 'BYTES')).toEqual({ scaledUnits: 'Gbps', factor: 1073741824 });
    expect(getUnitRange(1048577, 'BYTES')).toEqual({ scaledUnits: 'Mbps', factor: 1048576 });
    expect(getUnitRange(1025, 'BYTES')).toEqual({ scaledUnits: 'Kbps', factor: 1024 });
    expect(getUnitRange(100, 'BYTES')).toEqual({ scaledUnits: '', factor: 1 });
  });

  it('should return correct scaled units and factor for default BYTES type', () => {
    expect(getUnitRange(1099511627777)).toEqual({ scaledUnits: 'Tbps', factor: 1099511627776 });
    expect(getUnitRange(1073741825)).toEqual({ scaledUnits: 'Gbps', factor: 1073741824 });
    expect(getUnitRange(1048577)).toEqual({ scaledUnits: 'Mbps', factor: 1048576 });
    expect(getUnitRange(1025)).toEqual({ scaledUnits: 'Kbps', factor: 1024 });
    expect(getUnitRange(100)).toEqual({ scaledUnits: '', factor: 1 });
  });
});
