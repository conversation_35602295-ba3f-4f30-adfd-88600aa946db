const mergeEcLiteData = (connectorData, tcData, item) => {
  const checkcData = connectorData.find((ele) => (ele.ecId === (('id' in item) ? item.id : 'NULL')));
  if (checkcData && checkcData.location) {
    const location = checkcData.location || '';
    const geoLocation = checkcData.geoLocation || {};
    const group = checkcData.group || '';
    const deploymentType = checkcData?.deploymentType || '';
    const deviceType = checkcData?.deviceType || '';
    // const deviceModelNo = checkcData.deviceModelNo || '';
    const status = checkcData.status || '"Inactive"';
    tcData.push({
      ...item, location, geoLocation, group, deploymentType, status, deviceType,
    });
  }
  return tcData;
};

export default mergeEcLiteData;
