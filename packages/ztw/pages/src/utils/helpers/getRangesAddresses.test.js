import {
  ipAddressOnly,
  ipAddressesOrRanges,
  ipAddressWithNetmaskOnly,
} from 'utils/validations';
import getRangesAddresses from './getRangesAddresses';

describe('getRangesAddresses', () => {
  it('should return Invalid when ip address with netmask is invalid', () => {
    jest.spyOn(ipAddressWithNetmaskOnly, 'toString').mockImplementation(() => 'Invalid');
    const ipString = '***********/34';
    expect(getRangesAddresses(ipString)).toBe('Invalid');
  });

  it('should return Invalid when ip address range is invalid', () => {
    jest.spyOn(ipAddressesOrRanges, 'toString').mockImplementation(() => 'Invalid');
    const ipString = '***********-192.168.1.910';
    expect(getRangesAddresses(ipString)).toBe('Invalid');
  });

  it('should return Invalid when ip address is invalid', () => {
    jest.spyOn(ipAddressOnly, 'toString').mockImplementation(() => 'Invalid');
    const ipString = '192.168.1.991';
    expect(getRangesAddresses(ipString)).toBe('Invalid');
  });

  it('should return object with ranges, addresses and masks when ip string contains ip address with netmask', () => {
    const ipString = '***********/24';
    const result = getRangesAddresses(ipString);
    expect(result).toHaveProperty('ranges');
    expect(result).toHaveProperty('addresses');
    expect(result).toHaveProperty('masks');
    expect(result.masks).toHaveLength(1);
    expect(result.ranges).toHaveLength(0);
    expect(result.addresses).toHaveLength(0);
  });

  it('should return object with ranges, addresses and masks when ip string contains ip address range', () => {
    const ipString = '***********-***********0';
    const result = getRangesAddresses(ipString);
    expect(result).toHaveProperty('ranges');
    expect(result).toHaveProperty('addresses');
    expect(result).toHaveProperty('masks');
    expect(result.ranges).toHaveLength(1);
    expect(result.masks).toHaveLength(0);
    expect(result.addresses).toHaveLength(0);
  });

  it('should return object with ranges, addresses and masks when ip string contains ip address', () => {
    const ipString = '***********';
    const result = getRangesAddresses(ipString);
    expect(result).toHaveProperty('ranges');
    expect(result).toHaveProperty('addresses');
    expect(result).toHaveProperty('masks');
    expect(result.ranges).toHaveLength(0);
    expect(result.masks).toHaveLength(0);
    expect(result.addresses).toHaveLength(1);
  });

  it('should return object with ranges, addresses and masks when ip string contains multiple ip addresses', () => {
    const ipString = '***********, ***********-***********0, ************/24';
    const result = getRangesAddresses(ipString);
    expect(result).toHaveProperty('ranges');
    expect(result).toHaveProperty('addresses');
    expect(result).toHaveProperty('masks');
    expect(result.ranges).toHaveLength(1);
    expect(result.masks).toHaveLength(1);
    expect(result.addresses).toHaveLength(1);
  });
});
