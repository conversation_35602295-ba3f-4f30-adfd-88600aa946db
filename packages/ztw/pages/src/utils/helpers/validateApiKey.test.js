import { isApi<PERSON>eyOK } from 'utils/validations';
import validate<PERSON><PERSON><PERSON><PERSON> from './validateApiKey';

describe('validateApiKey function', () => {
  it('should return an empty object when the API key is valid', () => {
    const values = { newApiKey: '123456789012' };
    const props = { selectedRow: { keyValue: '123456789013' } };
    const result = validateApiKey(values, props);
    expect(result).toEqual({});
  });

  it('should return an object with an error message when the API key is invalid', () => {
    const values = { newApiKey: 'invalid-api-key' };
    const props = { selectedRow: { keyValue: 'invalid-key-value' } };
    const result = validateApiKey(values, props);
    expect(result).toEqual({ newApiKey: 'Invalid API Key' });
  });

  it('should return an empty object when the API key is valid', () => {
    const values = { newApiKey: '123456789013' };
    const props = { selectedRow: { keyValue: '123456789013' } };
    const result = validateApiKey(values, props);
    expect(result).toEqual({ newApiKey: 'The new API key cannnot be the same as the current key' });
  });

  it('should handle undefined values and props', () => {
    const result = validateApiKey(undefined, undefined);
    expect(result).toEqual({ newApiKey: 'This field cannot be empty.' });
  });

  it('should handle null values and props', () => {
    const result = validateApiKey(null, null);
    expect(result).toEqual({ newApiKey: 'This field cannot be empty.' });
  });
});
