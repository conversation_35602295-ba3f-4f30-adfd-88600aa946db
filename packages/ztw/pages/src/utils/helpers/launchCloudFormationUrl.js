const launchCloudFormationUrl = ({ trustedRole, externalId, awsRoleName }) => `https://us-east-1.console.aws.amazon.com/cloudformation/home
?region=us-east-1
#/stacks/create/review
?templateURL=https://zscaler-discovery-role.s3.amazonaws.com/zscaler_discovery_role.yaml
&stackName=ZscalerTagDiscoveryTrustingRole
&param_ZscalerTrustedRole=${trustedRole}
&param_ExternalId=${externalId}
&param_TrustingAccountRoleName=${awsRoleName}
`;
  
export default launchCloudFormationUrl;
