// getStatusIcon.test.js
import React from 'react';
import { render } from '@testing-library/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faExclamationCircle, faMinusCircle } from '@fortawesome/pro-solid-svg-icons';
import i18n from 'utils/i18n';
import getStatusIcon from './getStatusIcon';

jest.mock('utils/i18n', () => ({
  t: (key) => key,
}));

describe('getStatusIcon', () => {
  it('returns active icon for ACTIVE status', () => {
    const { getAllByText } = render(getStatusIcon('ACTIVE'));
    const activeElements = getAllByText('ACTIVE');
    expect(activeElements[0]).toBeInTheDocument();
    expect(activeElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-check active');
  });

  it('returns active icon for Enabled status', () => {
    const { getAllByText } = render(getStatusIcon('Enabled'));
    const enabledElements = getAllByText('ENABLED');
    expect(enabledElements[0]).toBeInTheDocument();
    expect(enabledElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-check active');
  });

  it('returns active icon for Up status', () => {
    const { getAllByText } = render(getStatusIcon('Up'));
    const upElements = getAllByText('UP');
    expect(upElements[0]).toBeInTheDocument();
    expect(upElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-check active');
  });

  it('returns standby icon for STANDBY status', () => {
    const { getAllByText } = render(getStatusIcon('STANDBY'));
    const standbyElements = getAllByText('STANDBY');
    expect(standbyElements[0]).toBeInTheDocument();
    expect(standbyElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-minus disabled');
  });

  it('returns standby icon for INIT status', () => {
    const { getAllByText } = render(getStatusIcon('INIT'));
    const initElements = getAllByText('INIT');
    expect(initElements[0]).toBeInTheDocument();
    expect(initElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-minus disabled');
  });

  it('returns inactive icon for Down status', () => {
    const { getAllByText } = render(getStatusIcon('Down'));
    const downElements = getAllByText('DOWN');
    expect(downElements[0]).toBeInTheDocument();
    expect(downElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-exclamation inactive');
  });

  it('returns inactive icon for Inactive status', () => {
    const { getAllByText } = render(getStatusIcon('Inactive'));
    const inactiveElements = getAllByText('INACTIVE');
    expect(inactiveElements[0]).toBeInTheDocument();
    expect(inactiveElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-exclamation inactive');
  });

  it('returns disabled icon for Disabled status', () => {
    const { getAllByText } = render(getStatusIcon('Disabled'));
    const disabledElements = getAllByText('DISABLED');
    expect(disabledElements[0]).toBeInTheDocument();
    expect(disabledElements[0].parentNode.querySelector('svg')).toHaveClass('svg-inline--fa fa-circle-minus disabled');
  });

  it('returns empty fragment for unknown status', () => {
    const { container } = render(getStatusIcon('Unknown'));
    expect(container).toBeEmptyDOMElement();
  });
});
