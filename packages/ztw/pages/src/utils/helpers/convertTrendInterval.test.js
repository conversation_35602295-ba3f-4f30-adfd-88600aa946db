import moment from 'moment';
import convertTrendInterval from './convertTrendInterval';

describe('convertTrendInterval function', () => {
  it('should return HOUR for time intervals less than 1 week and greater than or equal to 5 hours', () => {
    const startDate = moment().subtract(6, 'hours').toDate();
    const endDate = moment().toDate();
    expect(convertTrendInterval(startDate, endDate)).toBe('HOUR');
  });

  it('should return HOUR for time intervals less than 5 hours', () => {
    const startDate = moment().subtract(4, 'hours').toDate();
    const endDate = moment().toDate();
    expect(convertTrendInterval(startDate, endDate)).toBe('HOUR');
  });

  it('should return DAY for time intervals greater than or equal to 1 week', () => {
    const startDate = moment().subtract(1, 'week').toDate();
    const endDate = moment().toDate();
    expect(convertTrendInterval(startDate, endDate)).toBe('DAY');
  });

  it('should return DAY for time intervals equal to 24 hours', () => {
    const startDate = moment().subtract(1, 'day').toDate();
    const endDate = moment().toDate();
    expect(convertTrendInterval(startDate, endDate)).toBe('DAY');
  });

  it('should return FIVE_MINUTE for time intervals equal to 0 hours', () => {
    const startDate = moment().toDate();
    const endDate = moment().toDate();
    expect(convertTrendInterval(startDate, endDate)).toBe('FIVE_MINUTE');
  });
});
