import mergeEcLiteData from './mergeEcLiteData';

describe('mergeEcLiteData function', () => {
  it('should return the original tcData if connectorData is empty', () => {
    const connectorData = [];
    const tcData = [];
    const item = { id: 'test-id' };
    expect(mergeEcLiteData(connectorData, tcData, item)).toEqual(tcData);
  });

  it('should return the original tcData if no matching ecId is found in connectorData', () => {
    const connectorData = [{ ecId: 'different-id' }];
    const tcData = [];
    const item = { id: 'test-id' };
    expect(mergeEcLiteData(connectorData, tcData, item)).toEqual(tcData);
  });

  it('should merge the data correctly when a matching ecId is found in connectorData', () => {
    const connectorData = [
      {
        ecId: 'test-id',
        location: 'test-location',
        geoLocation: { lat: 1, lon: 2 },
        group: 'test-group',
        deploymentType: 'test-deployment-type',
        deviceType: 'test-device-type',
        status: 'test-status',
      },
    ];
    const tcData = [];
    const item = { id: 'test-id' };
    const expectedOutput = [
      {
        id: 'test-id',
        location: 'test-location',
        geoLocation: { lat: 1, lon: 2 },
        group: 'test-group',
        deploymentType: 'test-deployment-type',
        deviceType: 'test-device-type',
        status: 'test-status',
      },
    ];
    expect(mergeEcLiteData(connectorData, tcData, item)).toEqual(expectedOutput);
  });

  it('should handle missing properties in connectorData', () => {
    const connectorData = [
      {
        ecId: 'test-id',
        location: 'test-location',
      },
    ];
    const tcData = [];
    const item = { id: 'test-id' };
    const expectedOutput = [
      {
        id: 'test-id',
        location: 'test-location',
        geoLocation: {},
        group: '',
        deploymentType: '',
        deviceType: '',
        status: '"Inactive"',
      },
    ];
    expect(mergeEcLiteData(connectorData, tcData, item)).toEqual(expectedOutput);
  });

  it('should handle item without id property', () => {
    const connectorData = [
      {
        ecId: 'NULL',
        location: 'test-location',
      },
    ];
    const tcData = [];
    const item = {};
    const expectedOutput = [
      {
        location: 'test-location',
        geoLocation: {},
        group: '',
        deploymentType: '',
        deviceType: '',
        status: '"Inactive"',
      },
    ];
    expect(mergeEcLiteData(connectorData, tcData, item)).toEqual(expectedOutput);
  });
});
