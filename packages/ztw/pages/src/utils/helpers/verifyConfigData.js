import PropTypes from 'prop-types';

function verifyConfigData(props) {
  const { key, configData } = props;
  const hasProperty = Object.hasOwn(configData, key);
    
  // if (key==='enableAppSvcInPolicy') return false ;
  return hasProperty ? configData[key] : true;
}

verifyConfigData.propTypes = {
  key: PropTypes.string,
};
verifyConfigData.defaultProps = {
  key: '',
};
  
export default verifyConfigData;
