import {
  ipv4SubnetToBinStr,
  ipv4SubnetCastToBinStr,
  ipv4DotQuadToInt,
  ipv4IntToDotQuad,
  ipv4IntToBinStr,
  ipv4BinStrtoInt,
  ipv4BitsNMtoBinStrNM,
} from './ipv4SubnetValidation';

describe('ipv4 functions', () => {
  describe('ipv4SubnetToBinStr', () => {
    it('should return the binary string representation of the subnet address', () => {
      const addressBinStr = '11111111000000000000000000000000';
      const netmaskBinStr = '11111111000000000000000000000000';
      const expected = '11111111000000000000000000000000';
      expect(ipv4SubnetToBinStr(addressBinStr, netmaskBinStr)).toBe(expected);
    });

    it('should return the binary string representation of the subnet address with different address and netmask', () => {
      const addressBinStr = '11111111000000000000000000000000';
      const netmaskBinStr = '11111111000000000000000000000011';
      const expected = '11111111000000000000000000000000';
      expect(ipv4SubnetToBinStr(addressBinStr, netmaskBinStr)).toBe(expected);
    });
  });

  describe('ipv4SubnetCastToBinStr', () => {
    it('should return the binary string representation of the subnet broadcast address', () => {
      const addressBinStr = '11111111000000000000000000000000';
      const netmaskBinStr = '11111111000000000000000000000000';
      const expected = '11111111111111111111111111111111';
      expect(ipv4SubnetCastToBinStr(addressBinStr, netmaskBinStr)).toBe(expected);
    });

    it('should return the binary string representation of the subnet broadcast address with different address and netmask', () => {
      const addressBinStr = '11111111000000000000000000000000';
      const netmaskBinStr = '11111111000000000000000000000011';
      const expected = '11111111111111111111111111111100';
      expect(ipv4SubnetCastToBinStr(addressBinStr, netmaskBinStr)).toBe(expected);
    });
  });

  describe('ipv4DotQuadToInt', () => {
    it('should convert a dotted-quad IP to an integer', () => {
      const ip = '***********';
      const expected = 3232235777;
      expect(ipv4DotQuadToInt(ip)).toBe(expected);
    });

    it('should convert a dotted-quad IP to an integer with different IP', () => {
      const ip = '********';
      const expected = 167772161;
      expect(ipv4DotQuadToInt(ip)).toBe(expected);
    });
  });

  describe('ipv4IntToDotQuad', () => {
    it('should convert an integer IP to a dotted-quad IP', () => {
      const ip = 3232235777;
      const expected = '***********';
      expect(ipv4IntToDotQuad(ip)).toBe(expected);
    });

    it('should convert an integer IP to a dotted-quad IP with different IP', () => {
      const ip = 167772161;
      const expected = '********';
      expect(ipv4IntToDotQuad(ip)).toBe(expected);
    });
  });

  describe('ipv4IntToBinStr', () => {
    it('should convert an integer IP to a binary string representation', () => {
      const ip = 3232235777;
      const expected = '11000000101010000000000100000001';
      expect(ipv4IntToBinStr(ip)).toBe(expected);
    });

    it('should convert an integer IP to a binary string representation with different IP', () => {
      const ip = 2684354561;
      const expected = '10100000000000000000000000000001';
      expect(ipv4IntToBinStr(ip)).toBe(expected);
    });
  });

  describe('ipv4BinStrtoInt', () => {
    it('should convert a binary string IP to an integer representation', () => {
      const ip = '11000000101010000000000100000001';
      const expected = 3232235777;
      expect(ipv4BinStrtoInt(ip)).toBe(expected);
    });

    it('should convert a binary string IP to an integer representation with different IP', () => {
      const ip = '10100000000000000000000000000001';
      const expected = 2684354561;
      expect(ipv4BinStrtoInt(ip)).toBe(expected);
    });
  });

  describe('ipv4BitsNMtoBinStrNM', () => {
    it('should convert the number of bits to a string representation of the binary value', () => {
      const bits = 24;
      const expected = '11111111111111111111111100000000';
      expect(ipv4BitsNMtoBinStrNM(bits)).toBe(expected);
    });

    it('should convert the number of bits to a string representation of the binary value with different bits', () => {
      const bits = 16;
      const expected = '11111111111111110000000000000000';
      expect(ipv4BitsNMtoBinStrNM(bits)).toBe(expected);
    });
  });
});
