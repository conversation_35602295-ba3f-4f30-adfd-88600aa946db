import setToolTipPosition from './setToolTipPosition';

describe('setToolTipPosition function', () => {
  let mockGetBoundingClientRect;
  let mockQuerySelector;

  beforeEach(() => {
    // Mock the document object
    mockQuerySelector = jest.fn();
    document.querySelector = mockQuerySelector;

    // Mock the getBoundingClientRect function
    mockGetBoundingClientRect = jest.fn().mockReturnValue({
      top: 0,
      left: 0,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should not call preventDefault if the event object is null', () => {
    const event = null;
    setToolTipPosition(event, '.modal-parent');
    expect(mockQuerySelector).not.toHaveBeenCalled();
  });

  it('should not call preventDefault if the modal parent element does not exist', () => {
    mockQuerySelector.mockReturnValue(null);
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            offsetHeight: 10,
          }),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(event.preventDefault).not.toHaveBeenCalled();
  });

  it('should call preventDefault on the event object', () => {
    mockQuerySelector.mockReturnValue({
      getBoundingClientRect: mockGetBoundingClientRect,
    });
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            offsetHeight: 10,
          }),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(event.preventDefault).toHaveBeenCalledTimes(1);
  });

  it('should get the bounding client rectangle of the target element', () => {
    mockQuerySelector.mockReturnValue({
      getBoundingClientRect: mockGetBoundingClientRect,
    });
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            offsetHeight: 10,
          }),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(event.currentTarget.getBoundingClientRect).toHaveBeenCalledTimes(2);
  });

  it('should get the bounding client rectangle of the modal parent element', () => {
    mockQuerySelector.mockReturnValue({
      getBoundingClientRect: mockGetBoundingClientRect,
    });
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            offsetHeight: 10,
          }),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(mockQuerySelector().getBoundingClientRect).toHaveBeenCalledTimes(2);
  });

  it('should set the top and left styles of the tooltip element', () => {
    mockQuerySelector.mockReturnValue({
      getBoundingClientRect: mockGetBoundingClientRect,
    });
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            offsetHeight: 10,
          }),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(event.currentTarget.parentElement.querySelector('.rTooltip').style.top).toBe('-20px');
    expect(event.currentTarget.parentElement.querySelector('.rTooltip').style.left).toBe('0px');
  });

  it('should not set the top and left styles of the tooltip element if it does not exist', () => {
    mockQuerySelector.mockReturnValue({
      getBoundingClientRect: mockGetBoundingClientRect,
    });
    const event = {
      preventDefault: jest.fn(),
      currentTarget: {
        getBoundingClientRect: mockGetBoundingClientRect,
        parentElement: {
          querySelector: jest.fn().mockReturnValue(null),
        },
      },
    };
    setToolTipPosition(event, '.modal-parent');
    expect(event.currentTarget.parentElement.querySelector('.rTooltip')).toBeNull();
  });
});
