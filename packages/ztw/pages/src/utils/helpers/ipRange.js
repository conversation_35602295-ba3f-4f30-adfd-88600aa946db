function ipRange(ipString1, ipString2) {
  if (typeof ipString1 === 'undefined' || typeof ipString2 === 'undefined') return '';
  
  const ipArray1 = ipString1.split('.');
  const ipArray2 = ipString2.split('.');
  let idealValues = true;

  ipArray1.forEach((element, index) => {
    if (element !== ipArray2[index]) {
      idealValues = false;
    }
  });

  if (idealValues) {
    return ipString1;
  }

  return `${ipString1} - ${ipString2}`;
}

export default ipRange;
