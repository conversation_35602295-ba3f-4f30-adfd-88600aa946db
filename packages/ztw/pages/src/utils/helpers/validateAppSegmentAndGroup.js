import { isEmpty } from 'utils/lodash';
import { isZPA } from 'utils/helpers';

const validateAppSegmentAndGroup = (values) => {
  const {
    allAppSegments, forwardingMethod, zpaApplicationSegmentGroups, zpaApplicationSegments,
  } = values;
  const errors = {};

  if (!allAppSegments && isZPA(forwardingMethod && forwardingMethod.id)
  && isEmpty(zpaApplicationSegments) && isEmpty(zpaApplicationSegmentGroups)) {
    errors.zpaApplicationSegments = 'SELECT_APP_SEGMENT_OR_APP_SEGMENT_GROUP';
    errors.zpaApplicationSegmentGroups = 'SELECT_APP_SEGMENT_OR_APP_SEGMENT_GROUP';
  }

  return errors;
};

export default validateAppSegmentAndGroup;
