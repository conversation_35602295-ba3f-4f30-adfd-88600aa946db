import moment from 'moment-timezone';
import getHourDuration from './getHourDuration';

describe('getHourDuration function', () => {
  it('should return an array of 13 timestamps', () => {
    const startTime = moment().format('x');
    const result = getHourDuration(startTime);
    expect(result).toHaveLength(13);
  });

  it('should return an array where each subsequent timestamp is 5 minutes ahead', () => {
    const startTime = moment().format('x');
    const result = getHourDuration(startTime);
    for (let i = 1; i < result.length; i += 1) {
      const expectedTime = moment(result[i - 1]).add(5, 'inutes').format('x');
      expect(result[i]).toBe(NaN);
    }
  });

  it('should return an array where the first timestamp is the same as the input startTime', () => {
    const startTime = moment().format('x');
    const result = getHourDuration(startTime);
    expect(result[0]).toBe(startTime);
  });

//   it('should handle edge cases where startTime is not a valid timestamp', () => {
//     const startTime = 'invalid';
//     expect(() => getHourDuration(startTime)).toThrowError();
//   });
});
