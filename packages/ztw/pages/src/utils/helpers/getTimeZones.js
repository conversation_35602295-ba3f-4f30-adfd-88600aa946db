import { orderBy } from 'utils/lodash';

const getTimeZones = () => {
  let timeZones = [
    { value: 'Pacific/Apia', label: 'SAMOA_PACIFIC_APIA' },
    { value: 'Pacific/Midway', label: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY' },
    { value: 'Pacific/Niue', label: 'NIUE_PACIFIC_NIUE' },
    { value: 'Pacific/Pago_Pago', label: 'AMERICAN_SAMOA_PACIFIC_PAGO_PAGO' },
    { value: 'Pacific/Samoa', label: '(GMT-11:00) Pacific/Samoa' },
    { value: 'US/Samoa', label: 'GMT_11_00_SAMOA' },
    { value: 'America/Adak', label: 'UNITED_STATES_AMERICA_ADAK' },
    { value: 'America/Atka', label: '(GMT-10:00) America/Atka' },
    { value: 'Pacific/Honolulu', label: 'UNITED_STATES_PACIFIC_HONOLULU' },
    { value: 'Pacific/Johnston', label: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON' },
    { value: 'Pacific/Fakaofo', label: 'TOKELAU_PACIFIC_FAKAOFO' },
    { value: 'Pacific/Tahiti', label: 'FRENCH_POLYNESIA_PACIFIC_TAHITI' },
    { value: 'US/Aleutian', label: '(GMT-10:00) US/Aleutian' },
    { value: 'US/Hawaii', label: 'GMT_10_00_US_HAWAIIAN_TIME' },
    { value: 'Pacific/Marquesas', label: 'GMT_09_30_MARQUESAS' },
    { value: 'America/Anchorage', label: 'UNITED_STATES_AMERICA_ANCHORAGE' },
    { value: 'America/Juneau', label: 'UNITED_STATES_AMERICA_JUNEAU' },
    { value: 'America/Nome', label: 'UNITED_STATES_AMERICA_NOME' },
    { value: 'America/Yakutat', label: 'UNITED_STATES_AMERICA_YAKUTAT' },
    { value: 'Pacific/Gambier', label: 'FRENCH_POLYNESIA_PACIFIC_GAMBIER' },
    { value: 'US/Alaska', label: 'GMT_09_00_US_ALASKA_TIME' },
    { value: 'America/Dawson', label: 'CANADA_AMERICA_DAWSON' },
    { value: 'America/Ensenada', label: '(GMT-08:00) America/Ensenada' },
    { value: 'America/Los_Angeles', label: 'UNITED_STATES_AMERICA_LOS_ANGELES' },
    { value: 'America/Louisville', label: 'UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE' },
    { value: 'America/Tijuana', label: 'MEXICO_AMERICA_TIJUANA' },
    { value: 'America/Vancouver', label: 'CANADA_AMERICA_VANCOUVER' },
    { value: 'America/Whitehorse', label: 'CANADA_AMERICA_WHITEHORSE' },
    { value: 'Canada/Pacific', label: '(GMT-08:00) Canada/Pacific' },
    { value: 'Canada/Yukon', label: '(GMT-08:00) Canada/Yukon' },
    { value: 'Mexico/BajaNorte', label: '(GMT-08:00) Mexico/Baja Norte' },
    { value: 'Pacific/Pitcairn', label: 'PITCAIRN_PACIFIC_PITCAIRN' },
    { value: 'US/Pacific', label: 'GMT_08_00_PACIFIC_TIME' },
    { value: 'America/Boise', label: 'UNITED_STATES_AMERICA_BOISE' },
    { value: 'America/Cambridge_Bay', label: 'CANADA_AMERICA_CAMBRIDGE_BAY' },
    { value: 'America/Chihuahua', label: 'MEXICO_AMERICA_CHIHUAHUA' },
    { value: 'America/Dawson_Creek', label: 'CANADA_AMERICA_DAWSON_CREEK' },
    { value: 'America/Denver', label: 'UNITED_STATES_AMERICA_DENVER' },
    { value: 'America/Edmonton', label: 'CANADA_AMERICA_EDMONTON' },
    { value: 'America/Hermosillo', label: 'MEXICO_AMERICA_HERMOSILLO' },
    { value: 'America/Inuvik', label: 'CANADA_AMERICA_INUVIK' },
    { value: 'America/Phoenix', label: 'UNITED_STATES_AMERICA_PHOENIX' },
    { value: 'America/Shiprock', label: 'UNITED_STATES_AMERICA_SHIPROCK' },
    { value: 'America/Yellowknife', label: 'CANADA_AMERICA_YELLOWKNIFE' },
    { value: 'Canada/Mountain', label: '(GMT-07:00) Canada/Mountain' },
    { value: 'Mexico/BajaSur', label: '(GMT-07:00) Mexico/Baja Sur' },
    { value: 'US/Arizona', label: 'GMT_07_00_US_MOUNTAIN_TIME_ARIZONA' },
    { value: 'US/Mountain', label: 'GMT_07_00_US_MOUNTAIN_TIME' },
    { value: 'America/Belize', label: 'BELIZE_AMERICA_BELIZE' },
    { value: 'America/Chicago', label: 'UNITED_STATES_AMERICA_CHICAGO' },
    { value: 'America/Costa_Rica', label: 'COSTA_RICA_AMERICA_COSTA_RICA' },
    { value: 'America/Cancun', label: 'MEXICO_AMERICA_CANCUN' },
    { value: 'America/El_Salvador', label: 'EL_SALVADOR_AMERICA_EL_SALVADOR' },
    { value: 'America/Guatemala', label: 'GUATEMALA_AMERICA_GUATEMALA' },
    { value: 'America/Indiana/Tell_City', label: 'UNITED_STATES_AMERICA_INDIANA_TELL_CITY' },
    { value: 'America/Knox_IN', label: '(GMT-06:00) America/Knox IN' },
    { value: 'America/Managua', label: 'NICARAGUA_AMERICA_MANAGUA' },
    { value: 'America/Menominee', label: 'UNITED_STATES_AMERICA_MENOMINEE' },
    { value: 'America/Merida', label: 'MEXICO_AMERICA_MERIDA' },
    { value: 'America/Mexico_City', label: 'MEXICO_AMERICA_MEXICO_CITY' },
    { value: 'America/Monterrey', label: 'MEXICO_AMERICA_MONTERREY' },
    { value: 'America/North_Dakota/Center', label: 'UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER' },
    { value: 'America/North_Dakota/New_Salem', label: 'UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM' },
    { value: 'America/Rainy_River', label: 'CANADA_AMERICA_RAINY_RIVER' },
    { value: 'America/Rankin_Inlet', label: 'CANADA_AMERICA_RANKIN_INLET' },
    { value: 'America/Regina', label: 'CANADA_AMERICA_REGINA' },
    { value: 'America/Resolute', label: 'CANADA_AMERICA_RESOLUTE' },
    { value: 'America/Swift_Current', label: 'CANADA_AMERICA_SWIFT_CURRENT' },
    { value: 'America/Tegucigalpa', label: 'HONDURAS_AMERICA_TEGUCIGALPA' },
    { value: 'America/Winnipeg', label: 'CANADA_AMERICA_WINNIPEG' },
    { value: 'Canada/Central', label: '(GMT-06:00) Canada/Central' },
    { value: 'Canada/East-Saskatchewan', label: '(GMT-06:00) Canada/East Saskatchewan' },
    { value: 'Canada/Saskatchewan', label: '(GMT-06:00) Canada/Saskatchewan' },
    { value: 'Chile/EasterIsland', label: '(GMT-06:00) Chile/Easter Island' },
    { value: 'Mexico/General', label: '(GMT-06:00) Mexico/General' },
    { value: 'Pacific/Easter', label: 'CHILE_PACIFIC_EASTER' },
    { value: 'Pacific/Galapagos', label: 'ECUADOR_PACIFIC_GALAPAGOS' },
    { value: 'US/Central', label: 'GMT_06_00_US_CENTRAL_TIME' },
    { value: 'America/Atikokan', label: 'CANADA_AMERICA_ATIKOKAN' },
    { value: 'America/Bogota', label: 'COLOMBIA_AMERICA_BOGOTA' },
    { value: 'America/Cayman', label: 'CAYMAN_ISLANDS_AMERICA_CAYMAN' },
    { value: 'America/Coral_Harbour', label: '(GMT-05:00) America/Coral Harbour' },
    { value: 'America/Detroit', label: 'UNITED_STATES_AMERICA_DETROIT' },
    { value: 'America/Fort_Wayne', label: '(GMT-05:00) America/Fort Wayne' },
    { value: 'America/Grand_Turk', label: 'TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK' },
    { value: 'America/Guayaquil', label: 'ECUADOR_AMERICA_GUAYAQUIL' },
    { value: 'America/Havana', label: 'CUBA_AMERICA_HAVANA' },
    { value: 'America/Indiana/Indianapolis', label: 'UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS' },
    { value: 'America/Indiana/Knox', label: 'UNITED_STATES_AMERICA_INDIANA_KNOX' },
    { value: 'America/Indiana/Marengo', label: 'UNITED_STATES_AMERICA_INDIANA_MARENGO' },
    { value: 'America/Indiana/Petersburg', label: 'UNITED_STATES_AMERICA_INDIANA_PETERSBURG' },
    { value: 'America/Indiana/Vevay', label: 'UNITED_STATES_AMERICA_INDIANA_VEVAY' },
    { value: 'America/Indiana/Vincennes', label: 'UNITED_STATES_AMERICA_INDIANA_VINCENNES' },
    { value: 'America/Indiana/Winamac', label: 'UNITED_STATES_AMERICA_INDIANA_WINAMAC' },
    { value: 'America/Indianapolis', label: 'UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS' },
    { value: 'America/Iqaluit', label: 'CANADA_AMERICA_IQALUIT' },
    { value: 'America/Jamaica', label: 'JAMAICA_AMERICA_JAMAICA' },
    { value: 'America/Kentucky/Louisville', label: 'UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE' },
    { value: 'America/Kentucky/Monticello', label: 'UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO' },
    { value: 'America/Lima', label: 'PERU_AMERICA_LIMA' },
    { value: 'America/Montreal', label: 'CANADA_AMERICA_MONTREAL' },
    { value: 'America/Nassau', label: 'BAHAMAS_AMERICA_NASSAU' },
    { value: 'America/New_York', label: 'UNITED_STATES_AMERICA_NEW_YORK' },
    { value: 'America/Nipigon', label: 'CANADA_AMERICA_NIPIGON' },
    { value: 'America/Panama', label: 'PANAMA_AMERICA_PANAMA' },
    { value: 'America/Port-au-Prince', label: 'HAITI_AMERICA_PORT_AU_PRINCE' },
    { value: 'America/Rio_Branco', label: 'BRAZIL_AMERICA_RIO_BRANCO' },
    { value: 'America/Thunder_Bay', label: 'CANADA_AMERICA_THUNDER_BAY' },
    { value: 'America/Toronto', label: 'CANADA_AMERICA_TORONTO' },
    { value: 'Brazil/Acre', label: '(GMT-05:00) Brazil/Acre' },
    { value: 'Canada/Eastern', label: '(GMT-05:00) Canada/Eastern' },
    { value: 'US/East-Indiana', label: 'GMT_05_00_US_EASTERN_TIME_INDIANA' },
    { value: 'US/Eastern', label: 'GMT_05_00_US_EASTERN_TIME' },
    { value: 'US/Indiana-Starke', label: '(GMT-05:00) US/Indiana Starke' },
    { value: 'US/Michigan', label: '(GMT-05:00) US/Michigan' },
    { value: 'America/Anguilla', label: 'ANGUILLA_AMERICA_ANGUILLA' },
    { value: 'America/Antigua', label: 'ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA' },
    { value: 'America/Aruba', label: 'ARUBA_AMERICA_ARUBA' },
    { value: 'America/Asuncion', label: 'PARAGUAY_AMERICA_ASUNCION' },
    { value: 'America/Barbados', label: 'BARBADOS_AMERICA_BARBADOS' },
    { value: 'America/Blanc-Sablon', label: 'CANADA_AMERICA_BLANC_SABLON' },
    { value: 'America/Boa_Vista', label: 'BRAZIL_AMERICA_BOA_VISTA' },
    { value: 'America/Caracas', label: 'VENEZUELA_AMERICA_CARACAS' },
    { value: 'America/Cuiaba', label: 'BRAZIL_AMERICA_CUIABA' },
    { value: 'America/Curacao', label: 'NETHERLANDS_ANTILLES_AMERICA_CURACAO' },
    { value: 'America/Dominica', label: 'DOMINICA_AMERICA_DOMINICA' },
    { value: 'America/Eirunepe', label: 'BRAZIL_AMERICA_EIRUNEPE' },
    { value: 'America/Glace_Bay', label: 'CANADA_AMERICA_GLACE_BAY' },
    { value: 'America/Goose_Bay', label: 'CANADA_AMERICA_GOOSE_BAY' },
    { value: 'America/Grenada', label: 'GRENADA_AMERICA_GRENADA' },
    { value: 'America/Guadeloupe', label: 'GUADELOUPE_AMERICA_GUADELOUPE' },
    { value: 'America/Guyana', label: 'GUYANA_AMERICA_GUYANA' },
    { value: 'America/Halifax', label: 'CANADA_AMERICA_HALIFAX' },
    { value: 'America/La_Paz', label: 'BOLIVIA_AMERICA_LA_PAZ' },
    { value: 'America/Manaus', label: 'BRAZIL_AMERICA_MANAUS' },
    { value: 'America/Marigot', label: 'SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT' },
    { value: 'America/Martinique', label: 'MARTINIQUE_AMERICA_MARTINIQUE' },
    { value: 'America/Mazatlan', label: 'MEXICO_AMERICA_MAZATLAN' },
    { value: 'America/Moncton', label: 'CANADA_AMERICA_MONCTON' },
    { value: 'America/Montserrat', label: 'MONTSERRAT_AMERICA_MONTSERRAT' },
    { value: 'America/Port_of_Spain', label: 'TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN' },
    { value: 'America/Porto_Acre', label: '(GMT-04:00) America/Porto Acre' },
    { value: 'America/Porto_Velho', label: 'BRAZIL_AMERICA_PORTO_VELHO' },
    { value: 'America/Puerto_Rico', label: 'PUERTO_RICO_AMERICA_PUERTO_RICO' },
    { value: 'America/Santo_Domingo', label: 'DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO' },
    { value: 'America/St_Barthelemy', label: 'SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY' },
    { value: 'America/St_Kitts', label: 'SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS' },
    { value: 'America/St_Lucia', label: 'SAINT_LUCIA_AMERICA_ST_LUCIA' },
    { value: 'America/St_Thomas', label: 'VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS' },
    { value: 'America/St_Vincent', label: 'SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT' },
    { value: 'America/Thule', label: 'GREENLAND_AMERICA_THULE' },
    { value: 'America/Tortola', label: 'VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA' },
    { value: 'America/Virgin', label: '(GMT-04:00) America/Virgin' },
    { value: 'Antarctica/Palmer', label: 'ANTARCTICA_PALMER' },
    { value: 'Atlantic/Bermuda', label: 'BERMUDA_ATLANTIC_BERMUDA' },
    { value: 'Atlantic/Stanley', label: 'FALKLAND_ISLANDS_ATLANTIC_STANLEY' },
    { value: 'Brazil/West', label: '(GMT-04:00) Brazil/West' },
    { value: 'Canada/Atlantic', label: '(GMT-04:00) Canada/Atlantic' },
    { value: 'Chile/Continental', label: '(GMT-04:00) Chile/Continental' },
    { value: 'America/St_Johns', label: 'CANADA_AMERICA_ST_JOHNS' },
    { value: 'Canada/Newfoundland', label: 'GMT_03_30_NEWFOUNDLAND_CANADA' },
    { value: 'America/Araguaina', label: 'BRAZIL_AMERICA_ARAGUAINA' },
    { value: 'America/Argentina/Buenos_Aires', label: 'ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES' },
    { value: 'America/Argentina/Catamarca', label: 'ARGENTINA_AMERICA_ARGENTINA_CATAMARCA' },
    { value: 'America/Argentina/ComodRivadavia', label: '(GMT-03:00) America/Argentina/Comodoro Rivadavia' },
    { value: 'America/Argentina/Cordoba', label: 'ARGENTINA_AMERICA_ARGENTINA_CORDOBA' },
    { value: 'America/Argentina/Jujuy', label: 'ARGENTINA_AMERICA_ARGENTINA_JUJUY' },
    { value: 'America/Argentina/La_Rioja', label: 'ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA' },
    { value: 'America/Argentina/Mendoza', label: 'ARGENTINA_AMERICA_ARGENTINA_MENDOZA' },
    { value: 'America/Argentina/Rio_Gallegos', label: 'ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS' },
    { value: 'America/Argentina/San_Juan', label: 'ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN' },
    { value: 'America/Argentina/Tucuman', label: 'ARGENTINA_AMERICA_ARGENTINA_TUCUMAN' },
    { value: 'America/Argentina/Ushuaia', label: 'ARGENTINA_AMERICA_ARGENTINA_USHUAIA' },
    { value: 'America/Bahia', label: 'BRAZIL_AMERICA_BAHIA' },
    { value: 'America/Belem', label: 'BRAZIL_AMERICA_BELEM' },
    { value: 'America/Buenos_Aires', label: '(GMT-03:00) America/Buenos Aires' },
    { value: 'America/Campo_Grande', label: 'BRAZIL_AMERICA_CAMPO_GRANDE' },
    { value: 'America/Catamarca', label: 'ARGENTINA_AMERICA_ARGENTINA_CATAMARCA' },
    { value: 'America/Cayenne', label: 'FRENCH_GUIANA_AMERICA_CAYENNE' },
    { value: 'America/Cordoba', label: 'ARGENTINA_AMERICA_ARGENTINA_CORDOBA' },
    { value: 'America/Fortaleza', label: 'BRAZIL_AMERICA_FORTALEZA' },
    { value: 'America/Godthab', label: 'GREENLAND_AMERICA_GODTHAB' },
    { value: 'America/Jujuy', label: '(GMT-03:00) America/Jujuy' },
    { value: 'America/Maceio', label: 'BRAZIL_AMERICA_MACEIO' },
    { value: 'America/Mendoza', label: 'ARGENTINA_AMERICA_ARGENTINA_MENDOZA' },
    { value: 'America/Miquelon', label: 'ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON' },
    { value: 'America/Montevideo', label: 'URUGUAY_AMERICA_MONTEVIDEO' },
    { value: 'America/Pangnirtung', label: 'CANADA_AMERICA_PANGNIRTUNG' },
    { value: 'America/Paramaribo', label: 'SURINAME_AMERICA_PARAMARIBO' },
    { value: 'America/Recife', label: 'BRAZIL_AMERICA_RECIFE' },
    { value: 'America/Rosario', label: '(GMT-03:00) America/Rosario' },
    { value: 'America/Santiago', label: 'CHILE_AMERICA_SANTIAGO' },
    { value: 'Antarctica/Rothera', label: 'ANTARCTICA_ROTHERA' },
    { value: 'Brazil/East', label: '(GMT-03:00) Brazil/East' },
    { value: 'America/Noronha', label: 'BRAZIL_AMERICA_NORONHA' },
    { value: 'America/Sao_Paulo', label: 'BRAZIL_AMERICA_SAO_PAULO' },
    { value: 'Atlantic/South_Georgia', label: 'SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA' },
    { value: 'Brazil/DeNoronha', label: '(GMT-02:00) Brazil/De Noronha' },
    { value: 'America/Scoresbysund', label: 'GREENLAND_AMERICA_SCORESBYSUND' },
    { value: 'Atlantic/Azores', label: 'PORTUGAL_ATLANTIC_AZORES' },
    { value: 'Atlantic/Cape_Verde', label: 'CAPE_VERDE_ATLANTIC_CAPE_VERDE' },
    { value: 'Africa/Abidjan', label: 'COTE_DIVOIRE_AFRICA_ABIDJAN' },
    { value: 'Africa/Accra', label: 'GHANA_AFRICA_ACCRA' },
    { value: 'Africa/Bamako', label: 'MALI_AFRICA_BAMAKO' },
    { value: 'Africa/Banjul', label: 'GAMBIA_AFRICA_BANJUL' },
    { value: 'Africa/Bissau', label: 'GUINEA_BISSAU_AFRICA_BISSAU' },
    { value: 'Africa/Casablanca', label: 'MOROCCO_AFRICA_CASABLANCA' },
    { value: 'Africa/Conakry', label: 'GUINEA_AFRICA_CONAKRY' },
    { value: 'Africa/Dakar', label: 'SENEGAL_AFRICA_DAKAR' },
    { value: 'Africa/Freetown', label: 'SIERRA_LEONE_AFRICA_FREETOWN' },
    { value: 'Africa/Lome', label: 'TOGO_AFRICA_LOME' },
    { value: 'Africa/Nouakchott', label: 'MAURITANIA_AFRICA_NOUAKCHOTT' },
    { value: 'Africa/Ouagadougou', label: 'BURKINA_FASO_AFRICA_OUAGADOUGOU' },
    { value: 'Africa/Sao_Tome', label: 'SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME' },
    { value: 'Africa/Timbuktu', label: '(GMT+00:00) Africa/Timbuktu' },
    { value: 'America/Danmarkshavn', label: 'GREENLAND_AMERICA_DANMARKSHAVN' },
    { value: 'Atlantic/Canary', label: 'SPAIN_ATLANTIC_CANARY' },
    { value: 'Atlantic/Faeroe', label: '(GMT+00:00) Atlantic/Faeroe' },
    { value: 'Atlantic/Faroe', label: 'FAROE_ISLANDS_ATLANTIC_FAROE' },
    { value: 'Atlantic/Madeira', label: 'PORTUGAL_ATLANTIC_MADEIRA' },
    { value: 'Atlantic/Reykjavik', label: 'ICELAND_ATLANTIC_REYKJAVIK' },
    { value: 'Atlantic/St_Helena', label: 'ST_HELENA_ATLANTIC_ST_HELENA' },
    { value: 'Europe/Dublin', label: 'IRELAND_EUROPE_DUBLIN' },
    { value: 'Europe/Gibraltar', label: 'GIBRALTAR_EUROPE_GIBRALTAR' },
    { value: 'Europe/Guernsey', label: 'GUERNSEY_EUROPE_GUERNSEY' },
    { value: 'Europe/Belfast', label: '(GMT+00:00) Europe/Belfast' },
    { value: 'Europe/Isle_of_Man', label: 'ISLE_OF_MAN_EUROPE_ISLE_OF_MAN' },
    { value: 'Europe/Jersey', label: 'JERSEY_EUROPE_JERSEY' },
    { value: 'Europe/Lisbon', label: 'PORTUGAL_EUROPE_LISBON' },
    { value: 'Europe/London', label: 'UNITED_KINGDOM_EUROPE_LONDON' },
    { value: 'Africa/Algiers', label: 'ALGERIA_AFRICA_ALGIERS' },
    { value: 'Africa/Bangui', label: 'CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI' },
    { value: 'Africa/Brazzaville', label: 'CONGO_REP_AFRICA_BRAZZAVILLE' },
    { value: 'Africa/Ceuta', label: 'SPAIN_AFRICA_CEUTA' },
    { value: 'Africa/El_Aaiun', label: 'WESTERN_SAHARA_AFRICA_EL_AAIUN' },
    { value: 'Africa/Kinshasa', label: 'CONGO_DEM_REP_AFRICA_KINSHASA' },
    { value: 'Africa/Lagos', label: 'NIGERIA_AFRICA_LAGOS' },
    { value: 'Africa/Libreville', label: 'GABON_AFRICA_LIBREVILLE' },
    { value: 'Africa/Luanda', label: 'ANGOLA_AFRICA_LUANDA' },
    { value: 'Africa/Malabo', label: 'EQUATORIAL_GUINEA_AFRICA_MALABO' },
    { value: 'Africa/Ndjamena', label: 'CHAD_AFRICA_NDJAMENA' },
    { value: 'Africa/Niamey', label: 'NIGER_AFRICA_NIAMEY' },
    { value: 'Africa/Porto-Novo', label: 'BENIN_AFRICA_PORTO_NOVO' },
    { value: 'Africa/Tunis', label: 'TUNISIA_AFRICA_TUNIS' },
    { value: 'Africa/Windhoek', label: 'NAMIBIA_AFRICA_WINDHOEK' },
    { value: 'Arctic/Longyearbyen', label: 'SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN' },
    { value: 'Atlantic/Jan_Mayen', label: '(GMT+01:00) Atlantic/Jan Mayen' },
    { value: 'Europe/Amsterdam', label: 'NETHERLANDS_EUROPE_AMSTERDAM' },
    { value: 'Europe/Andorra', label: 'ANDORRA_EUROPE_ANDORRA' },
    { value: 'Europe/Belgrade', label: 'SERBIA_EUROPE_BELGRADE' },
    { value: 'Europe/Berlin', label: 'GERMANY_EUROPE_BERLIN' },
    { value: 'Europe/Bratislava', label: 'SLOVAKIA_EUROPE_BRATISLAVA' },
    { value: 'Europe/Brussels', label: 'BELGIUM_EUROPE_BRUSSELS' },
    { value: 'Europe/Budapest', label: 'HUNGARY_EUROPE_BUDAPEST' },
    { value: 'Europe/Copenhagen', label: 'DENMARK_EUROPE_COPENHAGEN' },
    { value: 'Europe/Ljubljana', label: 'SLOVENIA_EUROPE_LJUBLJANA' },
    { value: 'Europe/Luxembourg', label: 'LUXEMBOURG_EUROPE_LUXEMBOURG' },
    { value: 'Europe/Madrid', label: 'SPAIN_EUROPE_MADRID' },
    { value: 'Europe/Malta', label: 'MALTA_EUROPE_MALTA' },
    { value: 'Europe/Monaco', label: 'MONACO_EUROPE_MONACO' },
    { value: 'Europe/Oslo', label: 'NORWAY_EUROPE_OSLO' },
    { value: 'Europe/Paris', label: 'FRANCE_EUROPE_PARIS' },
    { value: 'Europe/Podgorica', label: 'MONTENEGRO_EUROPE_PODGORICA' },
    { value: 'Europe/Prague', label: 'CZECH_REPUBLIC_EUROPE_PRAGUE' },
    { value: 'Europe/Rome', label: 'ITALY_EUROPE_ROME' },
    { value: 'Europe/San_Marino', label: 'SAN_MARINO_EUROPE_SAN_MARINO' },
    { value: 'Europe/Sarajevo', label: 'BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO' },
    { value: 'Europe/Skopje', label: 'MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE' },
    { value: 'Europe/Stockholm', label: 'SWEDEN_EUROPE_STOCKHOLM' },
    { value: 'Europe/Tirane', label: 'ALBANIA_EUROPE_TIRANE' },
    { value: 'Europe/Vaduz', label: 'LIECHTENSTEIN_EUROPE_VADUZ' },
    { value: 'Europe/Vatican', label: 'VATICAN_CITY_STATE_EUROPE_VATICAN' },
    { value: 'Europe/Vienna', label: 'AUSTRIA_EUROPE_VIENNA' },
    { value: 'Europe/Warsaw', label: 'POLAND_EUROPE_WARSAW' },
    { value: 'Europe/Zagreb', label: 'CROATIA_EUROPE_ZAGREB' },
    { value: 'Europe/Zurich', label: 'SWITZERLAND_EUROPE_ZURICH' },
    { value: 'Africa/Blantyre', label: 'MALAWI_AFRICA_BLANTYRE' },
    { value: 'Africa/Bujumbura', label: 'BURUNDI_AFRICA_BUJUMBURA' },
    { value: 'Africa/Cairo', label: 'EGYPT_AFRICA_CAIRO' },
    { value: 'Africa/Gaborone', label: 'BOTSWANA_AFRICA_GABORONE' },
    { value: 'Africa/Harare', label: 'ZIMBABWE_AFRICA_HARARE' },
    { value: 'Africa/Johannesburg', label: 'SOUTH_AFRICA_AFRICA_JOHANNESBURG' },
    { value: 'Africa/Kigali', label: 'RWANDA_AFRICA_KIGALI' },
    { value: 'Africa/Lubumbashi', label: 'CONGO_DEM_REP_AFRICA_LUBUMBASHI' },
    { value: 'Africa/Lusaka', label: 'ZAMBIA_AFRICA_LUSAKA' },
    { value: 'Africa/Maputo', label: 'MOZAMBIQUE_AFRICA_MAPUTO' },
    { value: 'Africa/Maseru', label: 'LESOTHO_AFRICA_MASERU' },
    { value: 'Africa/Mbabane', label: 'SWAZILAND_AFRICA_MBABANE' },
    { value: 'Africa/Tripoli', label: 'LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI' },
    { value: 'Asia/Amman', label: 'JORDAN_ASIA_AMMAN' },
    { value: 'Asia/Beirut', label: 'LEBANON_ASIA_BEIRUT' },
    { value: 'Asia/Damascus', label: 'SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS' },
    { value: 'Asia/Gaza', label: 'OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA' },
    { value: 'Asia/Istanbul', label: 'TURKEY_EUROPE_ISTANBUL' },
    { value: 'Asia/Jerusalem', label: 'ISRAEL_ASIA_JERUSALEM' },
    { value: 'Asia/Nicosia', label: 'CYPRUS_ASIA_NICOSIA' },
    { value: 'Asia/Tel_Aviv', label: '(GMT+02:00) Asia/Tel Aviv' },
    { value: 'Europe/Athens', label: 'GREECE_EUROPE_ATHENS' },
    { value: 'Europe/Bucharest', label: 'ROMANIA_EUROPE_BUCHAREST' },
    { value: 'Europe/Chisinau', label: 'MOLDOVA_EUROPE_CHISINAU' },
    { value: 'Europe/Helsinki', label: 'FINLAND_EUROPE_HELSINKI' },
    { value: 'Europe/Istanbul', label: 'TURKEY_EUROPE_ISTANBUL' },
    { value: 'Europe/Kaliningrad', label: 'RUSSIAN_FEDERATION_EUROPE_KALININGRAD' },
    { value: 'Europe/Kiev', label: 'UKRAINE_EUROPE_KIEV' },
    { value: 'Europe/Mariehamn', label: 'ALAND_ISLANDS_EUROPE_MARIEHAMN' },
    { value: 'Europe/Minsk', label: 'BELARUS_EUROPE_MINSK' },
    { value: 'Europe/Nicosia', label: 'CYPRUS_ASIA_NICOSIA' },
    { value: 'Europe/Riga', label: 'LATVIA_EUROPE_RIGA' },
    { value: 'Europe/Simferopol', label: 'UKRAINE_EUROPE_SIMFEROPOL' },
    { value: 'Europe/Sofia', label: 'BULGARIA_EUROPE_SOFIA' },
    { value: 'Europe/Tallinn', label: 'ESTONIA_EUROPE_TALLINN' },
    { value: 'Europe/Tiraspol', label: '(GMT+02:00) Europe/Tiraspol' },
    { value: 'Europe/Uzhgorod', label: 'UKRAINE_EUROPE_UZHGOROD' },
    { value: 'Europe/Vilnius', label: 'LITHUANIA_EUROPE_VILNIUS' },
    { value: 'Europe/Zaporozhye', label: 'UKRAINE_EUROPE_ZAPOROZHYE' },
    { value: 'Africa/Addis_Ababa', label: 'ETHIOPIA_AFRICA_ADDIS_ABABA' },
    { value: 'Africa/Asmara', label: 'ERITREA_AFRICA_ASMARA' },
    { value: 'Africa/Asmera', label: '(GMT+03:00) Africa/Asmera' },
    { value: 'Africa/Dar_es_Salaam', label: 'TANZANIA_AFRICA_DAR_ES_SALAAM' },
    { value: 'Africa/Djibouti', label: 'DJIBOUTI_AFRICA_DJIBOUTI' },
    { value: 'Africa/Douala', label: 'CAMEROON_AFRICA_DOUALA' },
    { value: 'Africa/Kampala', label: 'UGANDA_AFRICA_KAMPALA' },
    { value: 'Africa/Khartoum', label: 'SUDAN_AFRICA_KHARTOUM' },
    { value: 'Africa/Mogadishu', label: 'SOMALIA_AFRICA_MOGADISHU' },
    { value: 'Africa/Monrovia', label: 'LIBERIA_AFRICA_MONROVIA' },
    { value: 'Africa/Nairobi', label: 'KENYA_AFRICA_NAIROBI' },
    { value: 'Antarctica/Syowa', label: 'ANTARCTICA_SYOWA' },
    { value: 'Asia/Aden', label: 'YEMEN_ASIA_ADEN' },
    { value: 'Asia/Baghdad', label: 'IRAQ_ASIA_BAGHDAD' },
    { value: 'Asia/Bahrain', label: 'BAHRAIN_ASIA_BAHRAIN' },
    { value: 'Asia/Kuwait', label: 'KUWAIT_ASIA_KUWAIT' },
    { value: 'Asia/Qatar', label: 'QATAR_ASIA_QATAR' },
    { value: 'Asia/Riyadh', label: 'SAUDI_ARABIA_ASIA_RIYADH' },
    { value: 'Asia/Tbilisi', label: 'GEORGIA_ASIA_TBILISI' },
    { value: 'Indian/Antananarivo', label: 'MADAGASCAR_INDIAN_ANTANANARIVO' },
    { value: 'Indian/Comoro', label: 'COMOROS_INDIAN_COMORO' },
    { value: 'Indian/Mayotte', label: 'MAYOTTE_INDIAN_MAYOTTE' },
    { value: 'Asia/Riyadh87', label: '(GMT+03:07) Asia/Riyadh87' },
    { value: 'Asia/Riyadh88', label: '(GMT+03:07) Asia/Riyadh88' },
    { value: 'Asia/Riyadh89', label: '(GMT+03:07) Asia/Riyadh89' },
    { value: 'Asia/Tehran', label: 'IRAN_ASIA_TEHRAN' },
    { value: 'Asia/Aqtau', label: 'KAZAKHSTAN_ASIA_AQTAU' },
    { value: 'Asia/Baku', label: 'AZERBAIJAN_ASIA_BAKU' },
    { value: 'Asia/Dubai', label: 'UNITED_ARAB_EMIRATES_ASIA_DUBAI' },
    { value: 'Asia/Muscat', label: 'OMAN_ASIA_MUSCAT' },
    { value: 'Asia/Yerevan', label: 'ARMENIA_ASIA_YEREVAN' },
    { value: 'Europe/Moscow', label: 'RUSSIAN_FEDERATION_EUROPE_MOSCOW' },
    { value: 'Europe/Samara', label: 'RUSSIAN_FEDERATION_EUROPE_SAMARA' },
    { value: 'Europe/Volgograd', label: 'RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD' },
    { value: 'Indian/Mahe', label: 'SEYCHELLES_INDIAN_MAHE' },
    { value: 'Indian/Mauritius', label: 'MAURITIUS_INDIAN_MAURITIUS' },
    { value: 'Indian/Reunion', label: 'REUNION_INDIAN_REUNION' },
    { value: 'Asia/Kabul', label: 'AFGHANISTAN_ASIA_KABUL' },
    { value: 'Asia/Aqtobe', label: 'KAZAKHSTAN_ASIA_AQTOBE' },
    { value: 'Asia/Ashgabat', label: 'TURKMENISTAN_ASIA_ASHGABAT' },
    { value: 'Asia/Ashkhabad', label: '(GMT+05:00) Asia/Ashkhabad' },
    { value: 'Asia/Bishkek', label: 'KYRGYZSTAN_ASIA_BISHKEK' },
    { value: 'Asia/Dushanbe', label: 'TAJIKISTAN_ASIA_DUSHANBE' },
    { value: 'Asia/Karachi', label: 'PAKISTAN_ASIA_KARACHI' },
    { value: 'Asia/Oral', label: 'KAZAKHSTAN_ASIA_ORAL' },
    { value: 'Asia/Samarkand', label: 'UZBEKISTAN_ASIA_SAMARKAND' },
    { value: 'Asia/Tashkent', label: 'UZBEKISTAN_ASIA_TASHKENT' },
    { value: 'Asia/Yekaterinburg', label: 'RUSSIAN_FEDERATION_ASIA_YEKATERINBURG' },
    { value: 'Indian/Kerguelen', label: 'FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN' },
    { value: 'Indian/Maldives', label: 'MALDIVES_INDIAN_MALDIVES' },
    { value: 'Asia/Calcutta', label: 'INDIA_ASIA_KOLKATA' },
    { value: 'Asia/Katmandu', label: 'NEPAL_ASIA_KATMANDU' },
    { value: 'Antarctica/Mawson', label: 'ANTARCTICA_MAWSON' },
    { value: 'Antarctica/Vostok', label: 'ANTARCTICA_VOSTOK' },
    { value: 'Asia/Almaty', label: 'KAZAKHSTAN_ASIA_ALMATY' },
    { value: 'Asia/Colombo', label: 'SRI_LANKA_ASIA_COLOMBO' },
    { value: 'Asia/Dacca', label: '(GMT+06:00) Asia/Dacca' },
    { value: 'Asia/Dhaka', label: 'BANGLADESH_ASIA_DHAKA' },
    { value: 'Asia/Novosibirsk', label: 'RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK' },
    { value: 'Asia/Omsk', label: 'RUSSIAN_FEDERATION_ASIA_OMSK' },
    { value: 'Asia/Qyzylorda', label: 'KAZAKHSTAN_ASIA_QYZYLORDA' },
    { value: 'Asia/Thimbu', label: '(GMT+06:00) Asia/Thimbu' },
    { value: 'Asia/Thimphu', label: 'BHUTAN_ASIA_THIMPHU' },
    { value: 'Indian/Chagos', label: 'BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS' },
    { value: 'Asia/Rangoon', label: 'MYANMAR_ASIA_RANGOON' },
    { value: 'Indian/Cocos', label: 'COCOS_KEELING_ISLANDS_INDIAN_COCOS' },
    { value: 'Antarctica/Davis', label: 'ANTARCTICA_DAVIS' },
    { value: 'Asia/Bangkok', label: 'THAILAND_ASIA_BANGKOK' },
    { value: 'Asia/Hovd', label: 'MONGOLIA_ASIA_HOVD' },
    { value: 'Asia/Jakarta', label: 'GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00' },
    { value: 'Asia/Krasnoyarsk', label: 'RUSSIAN_FEDERATION_ASIA_KRASNOYARSK' },
    { value: 'Asia/Phnom_Penh', label: 'CAMBODIA_ASIA_PHNOM_PENH' },
    { value: 'Asia/Pontianak', label: 'INDONESIA_ASIA_PONTIANAK' },
    { value: 'Asia/Saigon', label: 'VIET_NAM_ASIA_SAIGON' },
    { value: 'Asia/Vientiane', label: 'LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE' },
    { value: 'Indian/Christmas', label: 'CHRISTMAS_ISLAND_INDIAN_CHRISTMAS' },
    { value: 'Antarctica/Casey', label: 'ANTARCTICA_CASEY' },
    { value: 'Asia/Brunei', label: 'BRUNEI_DARUSSALAM_ASIA_BRUNEI' },
    { value: 'Asia/Choibalsan', label: 'MONGOLIA_ASIA_CHOIBALSAN' },
    { value: 'Asia/Chongqing', label: 'CHINA_ASIA_CHONGQING' },
    { value: 'Asia/Chungking', label: '(GMT+08:00) Asia/Chungking' },
    { value: 'Asia/Harbin', label: 'CHINA_ASIA_HARBIN' },
    { value: 'Asia/Hong_Kong', label: 'HONG_KONG_ASIA_HONG_KONG' },
    { value: 'Asia/Kashgar', label: 'CHINA_ASIA_KASHGAR' },
    { value: 'Asia/Kuala_Lumpur', label: 'MALAYSIA_ASIA_KUALA_LUMPUR' },
    { value: 'Asia/Kuching', label: 'MALAYSIA_ASIA_KUCHING' },
    { value: 'Asia/Macao', label: '(GMT+08:00) Asia/Macao' },
    { value: 'Asia/Macau', label: 'MACAO_ASIA_MACAU' },
    { value: 'Asia/Makassar', label: 'INDONESIA_ASIA_MAKASSAR' },
    { value: 'Asia/Manila', label: 'PHILIPPINES_ASIA_MANILA' },
    { value: 'Asia/Shanghai', label: 'CHINA_ASIA_SHANGHAI' },
    { value: 'Asia/Singapore', label: 'GMT_08_00_SINGAPORE_GMT_08_00' },
    { value: 'Asia/Taipei', label: 'TAIWAN_ASIA_TAIPEI' },
    { value: 'Asia/Ujung_Pandang', label: '(GMT+08:00) Asia/Ujung Pandang' },
    { value: 'Asia/Ulaanbaatar', label: 'MONGOLIA_ASIA_ULAANBAATAR' },
    { value: 'Asia/Ulan_Bator', label: '(GMT+08:00) Asia/Ulan Bator' },
    { value: 'Asia/Urumqi', label: 'CHINA_ASIA_URUMQI' },
    { value: 'Australia/Perth', label: 'AUSTRALIA_PERTH' },
    { value: 'Australia/West', label: '(GMT+08:00) Australia/West' },
    { value: 'Australia/Eucla', label: 'AUSTRALIA_EUCLA' },
    { value: 'Asia/Dili', label: 'TIMOR_LESTE_ASIA_DILI' },
    { value: 'Asia/Irkutsk', label: 'RUSSIAN_FEDERATION_ASIA_IRKUTSK' },
    { value: 'Asia/Jayapura', label: 'INDONESIA_ASIA_JAYAPURA' },
    { value: 'Asia/Pyongyang', label: 'KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG' },
    { value: 'Asia/Seoul', label: 'KOREA_REPUBLIC_OF_ASIA_SEOUL' },
    { value: 'Asia/Tokyo', label: 'JAPAN_ASIA_TOKYO' },
    { value: 'Asia/Yakutsk', label: 'RUSSIAN_FEDERATION_ASIA_YAKUTSK' },
    { value: 'Pacific/Palau', label: 'PALAU_PACIFIC_PALAU' },
    { value: 'Australia/Adelaide', label: 'AUSTRALIA_ADELAIDE' },
    { value: 'Australia/Darwin', label: 'AUSTRALIA_DARWIN' },
    { value: 'Australia/North', label: '(GMT+09:30) Australia/North' },
    { value: 'Australia/South', label: '(GMT+09:30) Australia/South' },
    { value: 'Australia/Yancowinna', label: '(GMT+09:30) Australia/Yancowinna' },
    { value: 'Antarctica/DumontDUrville', label: 'ANTARCTICA_DUMONTDURVILLE' },
    { value: 'Asia/Vladivostok', label: 'RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK' },
    { value: 'Australia/ACT', label: '(GMT+10:00) Australia/ACT' },
    { value: 'Australia/Brisbane', label: 'AUSTRALIA_BRISBANE' },
    { value: 'Australia/Canberra', label: '(GMT+10:00) Australia/Canberra' },
    { value: 'Australia/Hobart', label: 'AUSTRALIA_HOBART' },
    { value: 'Australia/Lindeman', label: 'AUSTRALIA_LINDEMAN' },
    { value: 'Australia/Melbourne', label: 'AUSTRALIA_MELBOURNE' },
    { value: 'Australia/NSW', label: '(GMT+10:00) Australia/NSW' },
    { value: 'Australia/Queensland', label: '(GMT+10:00) Australia/Queensland' },
    { value: 'Australia/Sydney', label: 'AUSTRALIA_SYDNEY' },
    { value: 'Australia/Tasmania', label: '(GMT+10:00) Australia/Tasmania' },
    { value: 'Australia/Victoria', label: '(GMT+10:00) Australia/Victoria' },
    { value: 'Pacific/Guam', label: 'GUAM_PACIFIC_GUAM' },
    { value: 'Pacific/Port_Moresby', label: 'PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY' },
    { value: 'Pacific/Rarotonga', label: 'COOK_ISLANDS_PACIFIC_RAROTONGA' },
    { value: 'Pacific/Saipan', label: 'NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN' },
    { value: 'Pacific/Truk', label: 'MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK' },
    { value: 'Pacific/Yap', label: '(GMT+10:00) Pacific/Yap' },
    { value: 'Australia/Broken_Hill', label: 'AUSTRALIA_BROKEN_HILL' },
    { value: 'Australia/LHI', label: '(GMT+10:30) Australia/LHI' },
    { value: 'Australia/Lord_Howe', label: 'GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30' },
    { value: 'Asia/Magadan', label: 'RUSSIAN_FEDERATION_ASIA_MAGADAN' },
    { value: 'Asia/Sakhalin', label: 'RUSSIAN_FEDERATION_ASIA_SAKHALIN' },
    { value: 'Australia/Currie', label: 'AUSTRALIA_CURRIE' },
    { value: 'Pacific/Efate', label: 'VANUATU_PACIFIC_EFATE' },
    { value: 'Pacific/Guadalcanal', label: 'SOLOMON_ISLANDS_PACIFIC_GUADALCANAL' },
    { value: 'Pacific/Kosrae', label: 'MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE' },
    { value: 'Pacific/Noumea', label: 'NEW_CALEDONIA_PACIFIC_NOUMEA' },
    { value: 'Pacific/Ponape', label: 'MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE' },
    { value: 'Pacific/Norfolk', label: 'NORFOLK_ISLAND_PACIFIC_NORFOLK' },
    { value: 'Antarctica/McMurdo', label: 'ANTARCTICA_MCMURDO' },
    { value: 'Antarctica/South_Pole', label: 'ANTARCTICA_SOUTH_POLE' },
    { value: 'Asia/Anadyr', label: 'RUSSIAN_FEDERATION_ASIA_ANADYR' },
    { value: 'Asia/Kamchatka', label: 'RUSSIAN_FEDERATION_ASIA_KAMCHATKA' },
    { value: 'Pacific/Auckland', label: 'NEW_ZEALAND_PACIFIC_AUCKLAND' },
    { value: 'Pacific/Fiji', label: 'GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00' },
    { value: 'Pacific/Funafuti', label: 'TUVALU_PACIFIC_FUNAFUTI' },
    { value: 'Pacific/Kwajalein', label: 'MARSHALL_ISLANDS_PACIFIC_KWAJALEIN' },
    { value: 'Pacific/Majuro', label: 'MARSHALL_ISLANDS_PACIFIC_MAJURO' },
    { value: 'Pacific/Nauru', label: 'NAURU_PACIFIC_NAURU' },
    { value: 'Pacific/Tarawa', label: 'KIRIBATI_PACIFIC_TARAWA' },
    { value: 'Pacific/Wake', label: 'UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE' },
    { value: 'Pacific/Wallis', label: 'WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS' },
    { value: 'Pacific/Chatham', label: 'NEW_ZEALAND_PACIFIC_CHATHAM' },
    { value: 'Pacific/Enderbury', label: 'KIRIBATI_PACIFIC_ENDERBURY' },
    { value: 'Pacific/Tongatapu', label: 'TONGA_PACIFIC_TONGATAPU' },
    { value: 'Pacific/Kiritimati', label: 'KIRIBATI_PACIFIC_KIRITIMATI' },
    { value: 'GMT', label: 'GMT' },
  ];
  
  timeZones = timeZones.map((x) => ({ ...x, label: x.label.includes(':') ? x.label : (x.label) }));

  return orderBy(timeZones, 'label');
};

export default getTimeZones;
