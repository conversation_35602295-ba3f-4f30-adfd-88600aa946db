const stringify = (obj) => {
  let cache = [];
  const str = JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (cache.indexOf(value) !== -1) {
        // Circular reference found, discard key
        return;
      }
      // Store value in our collection
      cache.push(value);
    }
    // eslint-disable-next-line consistent-return
    return value;
  });
  cache = null; // reset the cache
  return str;
};

export default stringify;
