import moment from 'moment-timezone';
import convertEndTime from './convertEndTime';

describe('convertEndTime function', () => {
  beforeEach(() => {
    // Mock the current time to ensure consistent test results
    jest.spyOn(moment, 'unix').mockImplementation(() => 1643723400); // Example timestamp
  });

  afterEach(() => {
    // Restore the original implementation
    jest.restoreAllMocks();
  });

  it('should return the current time in milliseconds when no timeFrame is provided', () => {
    const result = convertEndTime();
    expect(result).toBe(moment().unix() * 1000);
  });

  it('should return the end of the previous day in milliseconds when timeFrame is "previous_day"', () => {
    const result = convertEndTime('previous_day');
    expect(result).toBe(moment().subtract(1, 'days').endOf('day').unix() * 1000);
  });

  it('should return the end of the previous week in milliseconds when timeFrame is "previous_week"', () => {
    const result = convertEndTime('previous_week');
    expect(result).toBe(moment().subtract(1, 'week').endOf('week').unix() * 1000);
  });

  it('should return the current time in milliseconds when an invalid timeFrame is provided', () => {
    const result = convertEndTime('invalid_timeFrame');
    expect(result).toBe(moment().unix() * 1000);
  });
});
