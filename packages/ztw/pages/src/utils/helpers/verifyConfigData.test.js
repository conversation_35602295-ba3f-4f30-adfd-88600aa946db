import verifyConfigData from './verifyConfigData';

describe('verifyConfigData function', () => {
  it('should return true when configData has the key and its value is true', () => {
    const props = {
      key: 'testKey',
      configData: {
        testKey: true,
      },
    };
    expect(verifyConfigData(props)).toBe(true);
  });

  it('should return false when configData has the key and its value is false', () => {
    const props = {
      key: 'testKey',
      configData: {
        testKey: false,
      },
    };
    expect(verifyConfigData(props)).toBe(false);
  });

  it('should return true when configData does not have the key', () => {
    const props = {
      key: 'testKey',
      configData: {},
    };
    expect(verifyConfigData(props)).toBe(true);
  });

  it('should return true when key is an empty string', () => {
    const props = {
      key: '',
      configData: {},
    };
    expect(verifyConfigData(props)).toBe(true);
  });
});
