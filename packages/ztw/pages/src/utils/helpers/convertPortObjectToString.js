/**
 * Convert from an array of port object { start: 7000, end: 7010 } back to
 * a port string of "7000-7010" when editing the row
 *
 * @param {Array of Object} ports An array of port objects with the convention of start and end key
 * @returns An array of port string
 */
const convertPortObjectToString = (ports) => {
  const portList = [];

  ports.forEach((port) => {
    let range = Object.getOwnPropertyDescriptor(port, 'start') ? port.start : port;
    if (Object.getOwnPropertyDescriptor(port, 'end')) {
      range = `${range}-${port.end}`;
    }
    portList.push(range.toString());
  });

  return portList.sort((a, b) => a - b);
};

export default convertPortObjectToString;
