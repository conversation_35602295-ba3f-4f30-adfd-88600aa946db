import convertToMinutes from './convertToMinutes';

describe('convertToMinutes function', () => {
  it('should convert days to minutes', () => {
    expect(convertToMinutes('DAY', 1)).toBe(1440);
    expect(convertToMinutes('DAY', 2)).toBe(2880);
  });

  it('should convert hours to minutes', () => {
    expect(convertToMinutes('HOUR', 1)).toBe(60);
    expect(convertToMinutes('HOUR', 2)).toBe(120);
  });

  it('should return the same value for minutes', () => {
    expect(convertToMinutes('MINUTE', 1)).toBe(1);
    expect(convertToMinutes('MINUTE', 2)).toBe(2);
  });

  it('should return the same value for unknown units', () => {
    expect(convertToMinutes('SECOND', 1)).toBe(1);
    expect(convertToMinutes('YEAR', 1)).toBe(1);
  });
  it('should handle non-string unit values', () => {
    expect(convertToMinutes(123, 1)).toBe(1);
    expect(convertToMinutes(null, 1)).toBe(1);
  });
});
