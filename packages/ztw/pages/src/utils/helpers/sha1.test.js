import sha1 from './sha1';

describe('sha1 function', () => {
  it('should return a string', () => {
    const result = sha1('Hello, World!');
    expect(typeof result).toBe('string');
  });

  it('should return a string of length 40', () => {
    const result = sha1('Hello, World!');
    expect(result.length).toBe(40);
  });

  it('should return the same result for the same input', () => {
    const input = 'Hello, World!';
    const result1 = sha1(input);
    const result2 = sha1(input);
    expect(result1).toBe(result2);
  });

  it('should return different results for different inputs', () => {
    const input1 = 'Hello, World!';
    const input2 = 'Goodbye, World!';
    const result1 = sha1(input1);
    const result2 = sha1(input2);
    expect(result1).not.toBe(result2);
  });

  it('should handle empty strings', () => {
    const result = sha1('');
    expect(result.length).toBe(40);
  });

  it('should handle undefined inputs', () => {
    const result = sha1(undefined);
    expect(result.length).toBe(40);
  });

  it('should handle non-string inputs', () => {
    const result = sha1(123);
    expect(result.length).toBe(40);
  });

  it('should handle very long strings', () => {
    const longString = 'a'.repeat(10000);
    const result = sha1(longString);
    expect(result.length).toBe(40);
  });

  it('should handle strings with different lengths', () => {
    const shortString = 'a';
    const mediumString = 'a'.repeat(100);
    const longString = 'a'.repeat(1000);
    const result1 = sha1(shortString);
    const result2 = sha1(mediumString);
    const result3 = sha1(longString);
    expect(result1.length).toBe(40);
    expect(result2.length).toBe(40);
    expect(result3.length).toBe(40);
  });

  it('should handle strings with different characters', () => {
    const string1 = 'abcdefghijklmnopqrstuvwxyz';
    const string2 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const string3 = '0123456789';
    const string4 = '!@#$%^&*()_+-={}:<>?';
    const result1 = sha1(string1);
    const result2 = sha1(string2);
    const result3 = sha1(string3);
    const result4 = sha1(string4);
    expect(result1.length).toBe(40);
    expect(result2.length).toBe(40);
    expect(result3.length).toBe(40);
    expect(result4.length).toBe(40);
  });

  it('should handle strings with non-ASCII characters', () => {
    const string1 = 'áéíóú';
    const string2 = 'ÁÉÍÓÚ';
    const string3 = 'àèìòù';
    const string4 = 'ÀÈÌÒÙ';
    const result1 = sha1(string1);
    const result2 = sha1(string2);
    const result3 = sha1(string3);
    const result4 = sha1(string4);
    expect(result1.length).toBe(40);
    expect(result2.length).toBe(40);
    expect(result3.length).toBe(40);
    expect(result4.length).toBe(40);
  });

  it('should handle strings with Unicode characters', () => {
    const string1 = '';
    const string2 = '';
    const string3 = '';
    const string4 = '';
    const result1 = sha1(string1);
    const result2 = sha1(string2);
    const result3 = sha1(string3);
    const result4 = sha1(string4);
    expect(result1.length).toBe(40);
    expect(result2.length).toBe(40);
    expect(result3.length).toBe(40);
    expect(result4.length).toBe(40);
  });
});
