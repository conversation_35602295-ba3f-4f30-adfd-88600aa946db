import arrayHasDuplicate from './arrayHasDuplicate';

test('arrayHasDuplicate', () => {
  // Test cases
  // Test case 1: not array
  expect(arrayHasDuplicate(0)).toBeFalsy();
  // Test case 2: Empty
  expect(arrayHasDuplicate([])).toBeFalsy();
  // Test case 3: No DUPLICATE
  expect(arrayHasDuplicate([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])).toBeFalsy();
  // Test case 4: Has Duplicate
  expect(arrayHasDuplicate([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0])).toBeTruthy();
});
