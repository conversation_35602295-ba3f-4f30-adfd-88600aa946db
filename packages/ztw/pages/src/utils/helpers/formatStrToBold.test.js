import React from 'react';
import { render } from '@testing-library/react';
import formatStrToBold from './formatStrToBold';

describe('formatStrToBold function', () => {
  it('should return an empty string when input is empty', () => {
    const result = formatStrToBold('');
    const { container } = render(result);
    expect(container).toHaveTextContent('');
    expect(container.querySelector('b')).toBeNull();
  });
  
  it('should return the original string when no bold formatting is needed', () => {
    const result = formatStrToBold('Hello World');
    const { container } = render(result);
    expect(container).toHaveTextContent('Hello World');
    expect(container.querySelector('b')).toBeNull();
  });
  
  it('should return a JSX element with bold formatting when input contains placeholders', () => {
    const result = formatStrToBold('Hello {0}World{1}');
    const { container } = render(result);
    expect(container).toHaveTextContent('Hello World');
    expect(container.querySelectorAll('b').length).toBe(1);
  });
  
  it('should return an empty string when input is null', () => {
    const result = formatStrToBold(null);
    const { container } = render(result);
    expect(container).toHaveTextContent('');
  });
});
