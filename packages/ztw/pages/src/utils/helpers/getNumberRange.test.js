import getNumberRange from './getNumberRange';

describe('getNumberRange function', () => {
  it('should return the value in trillion format', () => {
    const value = 1234567890123;
    const expectedOutput = {
      value: 1.23,
      unit: 'T',
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value in billion format', () => {
    const value = 1234567890;
    const expectedOutput = {
      value: 1.23,
      unit: 'B',
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value in million format', () => {
    const value = 1234567;
    const expectedOutput = {
      value: 1.23,
      unit: 'M',
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value in thousand format', () => {
    const value = 1234;
    const expectedOutput = {
      value: 1.23,
      unit: 'K',
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value as is', () => {
    const value = 123;
    const expectedOutput = {
      value: 123,
      unit: null,
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value as is when the input is 0', () => {
    const value = 0;
    const expectedOutput = {
      value: 0,
      unit: null,
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });

  it('should return the value as is when the input is negative', () => {
    const value = -123;
    const expectedOutput = {
      value: -123,
      unit: null,
    };
    expect(getNumberRange(value)).toEqual(expectedOutput);
  });
});
