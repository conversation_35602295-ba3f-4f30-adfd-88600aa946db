import generateApiKey from './generateApiKey';

describe('generateApiKey function', () => {
  it('should return a string', () => {
    const result = generateApiKey();
    expect(typeof result).toBe('string');
  });

  it('should return a string of length 12', () => {
    const result = generateApiKey();
    expect(result.length).toBe(12);
  });

  it('should return different api keys for different timestamps', () => {
    const result1 = generateApiKey(1643723400);
    const result2 = generateApiKey(1643723411);
    expect(result1).not.toBe(result2);
  });

  it('should return the same api key for the same timestamp', () => {
    const result1 = generateApiKey(1643723400);
    const result2 = generateApiKey(1643723400);
    expect(result1).toBe(result2);
  });

  it('should return a valid api key when no timestamp is provided', () => {
    const result = generateApiKey();
    expect(result.length).toBe(12);
  });
});
