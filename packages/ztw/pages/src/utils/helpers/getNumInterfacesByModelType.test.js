// getNumInterfacesByModelType.test.js
import getNumInterfacesByModelType from './getNumInterfacesByModelType';

describe('getNumInterfacesByModelType', () => {
  it('returns the correct number of interfaces for ZT400', () => {
    expect(getNumInterfacesByModelType('ZT400')).toBe(4);
  });

  it('returns the correct number of interfaces for ZT600', () => {
    expect(getNumInterfacesByModelType('ZT600')).toBe(6);
  });

  it('returns the correct number of interfaces for ZT800', () => {
    expect(getNumInterfacesByModelType('ZT800')).toBe(8);
  });

  it('returns 0 for an unknown model type', () => {
    expect(getNumInterfacesByModelType('Unknown')).toBe(0);
  });

  it('returns 0 for an empty string model type', () => {
    expect(getNumInterfacesByModelType('')).toBe(0);
  });

  it('returns 0 for a null model type', () => {
    expect(getNumInterfacesByModelType(null)).toBe(0);
  });

  it('returns 0 for an undefined model type', () => {
    expect(getNumInterfacesByModelType(undefined)).toBe(0);
  });
});
