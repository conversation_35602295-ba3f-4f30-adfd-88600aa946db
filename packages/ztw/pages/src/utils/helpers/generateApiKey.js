const obfuscateApiKey = (apiKey, timestamp) => {
  const high = timestamp.substring(timestamp.length - 6);
  //  let low = (parseInt(high,10) >> 1).toString(); // can override the es rule (no-bitwise)
  let low = (Math.floor(parseInt(high, 10) / (2)))?.toString();

  let obfuscatedApiKey = '';

  while (low.length < 6) {
    low = '0' + low;
  }

  for (let i = 0; i < high.length; i += 1) {
    obfuscatedApiKey += apiKey.charAt(parseInt(high.charAt(i), 10));
  }

  for (let j = 0; j < low.length; j += 1) {
    obfuscatedApiKey += apiKey.charAt(parseInt(low.charAt(j), 10) + 2);
  }

  return obfuscatedApiKey;
};

const generateApiKey = (time) => {
  const timestamp = time?.toString() || (new Date()).getTime()?.toString();
  const apiKey = 'jj7tg80fEGao';
  return obfuscateApiKey(apiKey, timestamp);
};

export default generateApiKey;
