import {
  ipAddressOnly,
  ipAddressesOrRanges,
  ipAddressWithNetmaskOnly,
} from 'utils/validations';

const getRangesAddresses = (ipString) => {
  const ipAddresses = ipString.split(',');
  const ranges = [];
  const masks = [];
  const addresses = [];
  for (let i = 0; i < ipAddresses.length; i += 1) {
    const ipAddress = String(ipAddresses[i]).trim();
    const slashIndex = ipAddress.indexOf('/');
    const dashIndex = ipAddress.indexOf('-');
    if (slashIndex !== -1) {
      const value = ipAddress.substring(0, slashIndex);
      const mask = ipAddress.substring(slashIndex + 1);

      if (typeof ipAddressWithNetmaskOnly(ipAddress) === 'string') {
        return 'Invalid';
      }

      masks.push({
        address: {
          value,
        },
        mask,
      });
    } else if (dashIndex !== -1) {
      if (typeof ipAddressesOrRanges(ipAddress) === 'string') {
        return 'Invalid';
      }

      const start = {
        value: ipAddress.substring(0, dashIndex),
      };
      const end = {
        value: ipAddress.substring(dashIndex + 1),
      };
      const range = {
        start,
        end,
      };
      ranges.push(range);
    } else {
      if (typeof ipAddressOnly(ipAddress) === 'string') {
        return 'Invalid';
      }

      addresses.push({
        value: ipAddress,
      });
    }
  }

  return {
    ranges,
    addresses,
    masks,
  };
};

export default getRangesAddresses;
