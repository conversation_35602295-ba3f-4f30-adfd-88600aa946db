import uniqBy from './uniqBy';

describe('uniqBy function', () => {
  it('should return an empty array when given an empty array', () => {
    expect(uniqBy([], (x) => x)).toEqual([]);
  });

  it('should return the original array when all elements are unique', () => {
    const arr = [1, 2, 3, 4, 5];
    expect(uniqBy(arr, (x) => x)).toEqual(arr);
  });

  it('should return an array with unique elements when given an array with duplicates', () => {
    const arr = [1, 2, 2, 3, 3, 3, 4, 5, 5];
    expect(uniqBy(arr, (x) => x)).toEqual([1, 2, 3, 4, 5]);
  });

  it('should return an array with unique elements based on the iteratee function', () => {
    const arr = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 1, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
    ];
    expect(uniqBy(arr, (x) => x.id)).toEqual([
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ]);
  });

  it('should return an array with unique elements based on the iteratee function with a string property', () => {
    const arr = [
      { id: 1, name: 'John' },
      { id: 2, name: 'Jane' },
      { id: 1, name: 'John' },
      { id: 3, name: 'Bob' },
      { id: 2, name: 'Jane' },
    ];
    expect(uniqBy(arr, (x) => x.name)).toEqual([
      { id: 1, name: 'John' },
      { id: 2, name: 'Jane' },
      { id: 3, name: 'Bob' },
    ]);
  });
});
