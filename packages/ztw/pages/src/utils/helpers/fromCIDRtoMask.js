import PropTypes from 'prop-types';

function fromCIDRtoMask(cidr) {
  if (cidr === 32) return '255.255.255.255';
  if (cidr === 31) return '255.255.255.254';
  if (cidr === 30) return '255.255.255.252';
  if (cidr === 29) return '255.255.255.248';
  if (cidr === 28) return '255.255.255.240';
  if (cidr === 27) return '255.255.255.224';
  if (cidr === 26) return '255.255.255.192';
  if (cidr === 25) return '255.255.255.128';
  if (cidr === 24) return '255.255.255.0';
  if (cidr === 23) return '255.255.254.0';
  if (cidr === 22) return '255.255.252.0';
  if (cidr === 21) return '255.255.248.0';
  if (cidr === 20) return '255.255.240.0';
  if (cidr === 19) return '255.255.224.0';
  if (cidr === 18) return '255.255.192.0';
  if (cidr === 17) return '255.255.128.0';
  if (cidr === 16) return '255.255.0.0';
  if (cidr === 15) return '255.254.0.0';
  if (cidr === 14) return '255.252.0.0';
  if (cidr === 13) return '255.248.0.0';
  if (cidr === 12) return '255.240.0.0';
  if (cidr === 11) return '255.224.0.0';
  if (cidr === 10) return '255.192.0.0';
  if (cidr === 9) return '255.128.0.0';
  if (cidr === 8) return '255.0.0.0';
  if (cidr === 7) return '254.0.0.0';
  if (cidr === 6) return '252.0.0.0';
  if (cidr === 5) return '248.0.0.0';
  if (cidr === 4) return '240.0.0.0';
  if (cidr === 3) return '224.0.0.0';
  if (cidr === 2) return '192.0.0.0';
  if (cidr === 1) return '128.0.0.0';
  if (cidr === 0) return '0.0.0.0';

  return '';
}

fromCIDRtoMask.propTypes = {
  cidr: PropTypes.number,
};
fromCIDRtoMask.defaultProps = {
  cidr: null,
};

export default fromCIDRtoMask;
