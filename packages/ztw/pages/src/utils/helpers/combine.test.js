import combine from './combine';

test('combine', () => {
  // Test cases
  // Test case 1: Only 2 empty array
  expect(combine([], [], 'id')).toEqual([]);

  // Test case 1: Only 1 array
  expect(combine([{ id: 1, name: 'name 1' }], [], 'id')).toEqual([{ id: 1, name: 'name 1' }]);

  // Test case 2: 2 arrays
  expect(combine([{ id: 1, name: 'name 1' }], [{ id: 2, name: 'name 2' }], 'id')).toEqual([{ id: 1, name: 'name 1' }, { id: 2, name: 'name 2' }]);

  // Test case 2: 2 arrays merging content
  expect(combine([{ id: 1, name: 'name 1', status: 'INACTIVE' }, { id: 3, name: 'name 3', status: 'ACTIVE' }], [{ id: 1, name: 'name 1', location: 'CANADA' }], 'id'))
    .toEqual([{
      id: 1, name: 'name 1', location: 'CANADA', status: 'INACTIVE',
    }, { id: 3, name: 'name 3', status: 'ACTIVE' }]);
});
