// tests/getScoreBarColor.test.js
import colors from 'css/colors.scss';
import { SCORE_STATUSES } from 'config';
import getScoreBarColor from './getScoreBarColor';

describe('getScoreBarColor function', () => {
  it('should return green2 when score is greater than okay', () => {
    const score = SCORE_STATUSES.okay + 1;
    expect(getScoreBarColor(score)).toBe(colors.green2);
  });

  it('should return orange2 when score is greater than poor but not greater than okay', () => {
    const score = (SCORE_STATUSES.okay + SCORE_STATUSES.poor) / 2;
    expect(getScoreBarColor(score)).toBe(colors.orange2);
  });

  it('should return #D13932 when score is less than or equal to poor', () => {
    const score = SCORE_STATUSES.poor;
    expect(getScoreBarColor(score)).toBe('#D13932');
  });

  it('should return #D13932 when score is less than poor', () => {
    const score = SCORE_STATUSES.poor - 1;
    expect(getScoreBarColor(score)).toBe('#D13932');
  });

  it('should return #D13932 when score is undefined or null', () => {
    expect(getScoreBarColor(undefined)).toBe('#D13932');
    expect(getScoreBarColor(null)).toBe('#D13932');
  });

  it('should return #D13932 when score is not a number', () => {
    expect(getScoreBarColor('string')).toBe('#D13932');
    expect(getScoreBarColor({})).toBe('#D13932');
    expect(getScoreBarColor([])).toBe('#D13932');
  });
});
