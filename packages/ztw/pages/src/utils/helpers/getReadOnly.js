import { isOneUI } from 'config';

const getReadOnly = (permissionReadOnly, authType) => {
  let readOnly = false;

  if (isOneUI && permissionReadOnly === 'READ_ONLY') return true;
  if (isOneUI && permissionReadOnly !== 'READ_ONLY') return false;

  if (authType === 'SUPPORT_ACCESS_READ_ONLY') {
    readOnly = true;
  } else if (permissionReadOnly === 'READ_ONLY' && authType !== 'SUPPORT_ACCESS_FULL') {
    readOnly = true;
  }
  return readOnly;
};

export default getReadOnly;
