import hasVdiSku from './hasVdiSku';

describe('hasVdiSku function', () => {
  it('should return true when sku contains BC_DEVICE_VDI', () => {
    const sku = ['BC_DEVICE_VDI', 'OTHER_SKU'];
    expect(hasVdiSku(sku)).toBe(true);
  });

  it('should return true when sku contains WORKLOAD_VDI', () => {
    const sku = ['WORKLOAD_VDI', 'OTHER_SKU'];
    expect(hasVdiSku(sku)).toBe(true);
  });

  it('should return true when sku contains both BC_DEVICE_VDI and WORKLOAD_VDI', () => {
    const sku = ['BC_DEVICE_VDI', 'WORKLOAD_VDI', 'OTHER_SKU'];
    expect(hasVdiSku(sku)).toBe(true);
  });

  it('should return false when sku does not contain BC_DEVICE_VDI or WORKLOAD_VDI', () => {
    const sku = ['OTHER_SKU', 'ANOTHER_SKU'];
    expect(hasVdiSku(sku)).toBe(false);
  });

  it('should return false when sku is empty', () => {
    const sku = [];
    expect(hasVdiSku(sku)).toBe(false);
  });
});
