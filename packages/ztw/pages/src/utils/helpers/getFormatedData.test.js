import {
  unitLabel,
  formatNumber,
  getMinMaxInData,
  getBestUnitForRange,
  getBestUnitForANumber,
  getFormatedNumberWithUnit,
} from './getFormatedData';
  
describe('unitLabel function', () => {
  it('should return the correct label for K', () => {
    expect(unitLabel('K')).toBe('K (1000)');
  });
  
  it('should return the correct label for M', () => {
    expect(unitLabel('M')).toBe('M (1,000,000)');
  });
  
  it('should return the correct label for KB', () => {
    expect(unitLabel('KB')).toBe('KB (1000 bytes)');
  });
  
  it('should return the correct label for MB', () => {
    expect(unitLabel('MB')).toBe('MB (1000 KB)');
  });
  
  it('should return the correct label for GB', () => {
    expect(unitLabel('GB')).toBe('GB (1000 MB)');
  });
  
  it('should return the unit as is if it does not match any condition', () => {
    expect(unitLabel('T')).toBe('T');
  });
});
  
describe('formatNumber function', () => {
  it('should format the number correctly for K', () => {
    expect(formatNumber(1000, 'K')).toBe(1);
  });
  
  it('should format the number correctly for M', () => {
    expect(formatNumber(1000000, 'M')).toBe(1);
  });

  it('should format the number correctly for B', () => {
    expect(formatNumber((10 ** 9), 'B')).toBe(1);
  });
  
  it('should format the number correctly for T', () => {
    expect(formatNumber((10 ** 12), 'T')).toBe(1);
  });

  it('should format the number correctly for Q', () => {
    expect(formatNumber((10 ** 15), 'Q')).toBe(1);
  });

  it('should format the number correctly for QT', () => {
    expect(formatNumber((10 ** 18), 'QT')).toBe(1);
  });

  it('should format the number correctly for KB', () => {
    expect(formatNumber(1024, 'KB')).toBe(1);
  });
  
  it('should format the number correctly for MB', () => {
    expect(formatNumber(1024 ** 2, 'MB')).toBe(1);
  });
  
  it('should format the number correctly for GB', () => {
    expect(formatNumber(1024 ** 3, 'GB')).toBe(1);
  });

  it('should format the number correctly for TB', () => {
    expect(formatNumber(1024 ** 4, 'TB')).toBe(1);
  });

  it('should format the number correctly for PB', () => {
    expect(formatNumber(1024 ** 5, 'PB')).toBe(1);
  });
  
  it('should format the number correctly for EB', () => {
    expect(formatNumber(1024 ** 6, 'EB')).toBe(1);
  });

  it('should format the number correctly for ZB', () => {
    expect(formatNumber(1024 ** 7, 'ZB')).toBe(1);
  });

  it('should format the number correctly for GY', () => {
    expect(formatNumber(1024 ** 8, 'YB')).toBe(1);
  });

  it('should return the number as is if it is not a number', () => {
    expect(formatNumber('1000', 'K')).toBe('1000');
  });
});
  
describe('getMinMaxInData function', () => {
  it('should return the correct min and max values', () => {
    const data = [1, 2, 3, 4, 5];
    expect(getMinMaxInData(data)).toEqual([1, 5]);
  });
  
  it('should return the correct min and max values with a dataKey', () => {
    const data = [{ value: 1 }, { value: 2 }, { value: 3 }, { value: 4 }, { value: 5 }];
    expect(getMinMaxInData(data, 'value')).toEqual([1, 5]);
  });
  
  it('should return [0, 0] if the data is empty', () => {
    const data = [];
    expect(getMinMaxInData(data)).toEqual([0, 0]);
  });
});
  
describe('getBestUnitForRange function', () => {
  it('should return the correct unit for a range of numbers', () => {
    expect(getBestUnitForRange(10, 1000000000, 'NUMBER')).toBe('M');
  });
  it('should return the correct unit for a range of numbers', () => {
    expect(getBestUnitForRange(100000000, 1000000000, 'NUMBER')).toBe('M');
  });
  it('should return the correct unit for a range of numbers', () => {
    expect(getBestUnitForRange(1000, 10000000, 'NUMBER')).toBe('K');
  });
  it('should return the correct unit for a range of numbers', () => {
    expect(getBestUnitForRange(1000, 10000000, 'NUMBER')).toBe('K');
  });
  
  it('should return the correct unit for a range of bytes', () => {
    expect(getBestUnitForRange(1024 ** 1, 1024 ** 8, 'BYTES')).toBe('PB');
  });
  it('should return the correct unit for a range of bytes', () => {
    expect(getBestUnitForRange(1024 ** 3, 1024 ** 4, 'BYTES')).toBe('GB');
  });
  
  it('should return an empty string if the type is not NUMBER or BYTES', () => {
    expect(getBestUnitForRange(1000, 1000000, 'OTHER')).toBe('');
  });
});
  
describe('getBestUnitForANumber function', () => {
  it('should return the correct unit for a number QT', () => {
    expect(getBestUnitForANumber(10 ** 20, 'NUMBER')).toBe('QT');
  });
  it('should return the correct unit for a number Q', () => {
    expect(getBestUnitForANumber(10 ** 17, 'NUMBER')).toBe('Q');
  });
  it('should return the correct unit for a number T', () => {
    expect(getBestUnitForANumber(10 ** 14, 'NUMBER')).toBe('T');
  });
  it('should return the correct unit for a number B', () => {
    expect(getBestUnitForANumber(10 ** 11, 'NUMBER')).toBe('B');
  });
  it('should return the correct unit for a number M', () => {
    expect(getBestUnitForANumber(1000000, 'NUMBER')).toBe('M');
  });
  it('should return the correct unit for a number K', () => {
    expect(getBestUnitForANumber(1000, 'NUMBER')).toBe('K');
  });

  it('should return the correct unit for a byte PB', () => {
    expect(getBestUnitForANumber(1024 ** 6, 'BYTES')).toBe('PB');
  });
  it('should return the correct unit for a byte TB', () => {
    expect(getBestUnitForANumber(1024 ** 5, 'BYTES')).toBe('TB');
  });
  it('should return the correct unit for a byte GB', () => {
    expect(getBestUnitForANumber(1024 ** 3, 'BYTES')).toBe('GB');
  });
  it('should return the correct unit for a byte MB', () => {
    expect(getBestUnitForANumber(1024 ** 2, 'BYTES')).toBe('MB');
  });
  it('should return the correct unit for a byte KB', () => {
    expect(getBestUnitForANumber(1024 ** 1, 'BYTES')).toBe('KB');
  });
  
  it('should return an empty string if the type is not NUMBER or BYTES', () => {
    expect(getBestUnitForANumber(1000000, 'OTHER')).toBe('');
  });
});
  
describe('getFormatedNumberWithUnit function', () => {
  it('should return the correct formatted number with unit', () => {
    const num = 1000000;
    const config = { type: 'NUMBER' };
    expect(getFormatedNumberWithUnit(num, config)).toBe('1 M');
  });
  
  it('should return the correct formatted number with unit for bytes', () => {
    const num = 1024 ** 3;
    const config = { type: 'BYTES' };
    expect(getFormatedNumberWithUnit(num, config)).toBe('1 GB');
  });
});
