import isBranchType from './isBranchType';

describe('isBranchType function', () => {
  it('should return true for CENTOS', () => {
    expect(isBranchType('CENTOS')).toBe(true);
  });

  it('should return true for REDHAT_LINUX', () => {
    expect(isBranchType('REDHAT_LINUX')).toBe(true);
  });

  it('should return true for MICROSOFT_HYPER_V', () => {
    expect(isBranchType('MICROSOFT_HYPER_V')).toBe(true);
  });

  it('should return true for VMWARE_ESXI', () => {
    expect(isBranchType('VMWARE_ESXI')).toBe(true);
  });

  it('should return false for other types', () => {
    expect(isBranchType('OTHER_TYPE')).toBe(false);
    expect(isBranchType('')).toBe(false);
    expect(isBranchType(null)).toBe(false);
    expect(isBranchType(undefined)).toBe(false);
  });
});
