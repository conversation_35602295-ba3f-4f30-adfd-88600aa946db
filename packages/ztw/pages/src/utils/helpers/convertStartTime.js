import moment from 'moment-timezone';

const convertStartTime = (timeFrame) => {
  // default - Last 24 Hours
  let convertedTime = moment().subtract(24, 'hours').unix() * 1000;

  if (timeFrame === 'current_day') {
    convertedTime = moment().startOf('day').unix() * 1000;
  } else if (timeFrame === 'current_week') {
    convertedTime = moment().startOf('week').unix() * 1000;
  } else if (timeFrame === 'current_month') {
    convertedTime = moment().startOf('month').unix() * 1000;
  } else if (timeFrame === 'previous_day') {
    convertedTime = moment().subtract(1, 'days').startOf('day').unix() * 1000;
  } else if (timeFrame === 'previous_week') {
    convertedTime = moment().subtract(1, 'week').startOf('week').unix() * 1000;
  } else if (timeFrame === 'previous_month') {
    convertedTime = moment().subtract(1, 'month').startOf('month').unix() * 1000;
  } else if (timeFrame === 'last_1_min') {
    convertedTime = moment().subtract(1, 'minutes').unix() * 1000;
  } else if (timeFrame === 'last_2_mins') {
    convertedTime = moment().subtract(2, 'minutes').unix() * 1000;
  } else if (timeFrame === 'last_5_mins') {
    convertedTime = moment().subtract(5, 'minutes').unix() * 1000;
  } else if (timeFrame === 'last_15_mins') {
    convertedTime = moment().subtract(15, 'minutes').unix() * 1000;
  } else if (timeFrame === 'last_30_mins') {
    convertedTime = moment().subtract(30, 'minutes').unix() * 1000;
  } else if (timeFrame === 'last_1_hour') {
    convertedTime = moment().subtract(1, 'hour').unix() * 1000;
  } else if (timeFrame === 'last_2_hours') {
    convertedTime = moment().subtract(2, 'hours').unix() * 1000;
  } else if (timeFrame === 'last_5_hours') {
    convertedTime = moment().subtract(5, 'hours').unix() * 1000;
  } else if (timeFrame === 'last_10_hours') {
    convertedTime = moment().subtract(10, 'hours').unix() * 1000;
  } else if (timeFrame === 'last_1_week') {
    // Prev Week > (7 * 24 * 60 * 60 * 1000)
    convertedTime = moment().subtract(1, 'week').unix() * 1000;
  } else if (timeFrame === 'last_1_month') {
    convertedTime = moment().subtract(1, 'month').unix() * 1000;
  } else {
    convertedTime = moment().subtract(24, 'hours').unix() * 1000;
  }
  return convertedTime;
};
  
export default convertStartTime;
