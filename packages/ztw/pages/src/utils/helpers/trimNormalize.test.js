// trimNormalize.test.js
import trimNormalize from './trimNormalize';

describe('trimNormalize', () => {
  it('trims whitespace from the input value', () => {
    expect(trimNormalize('   Hello World   ')).toBe('Hello World');
  });

  it('returns an empty string if the input value is null or undefined', () => {
    expect(trimNormalize(null)).toBe('');
    expect(trimNormalize(undefined)).toBe('');
  });

  it('returns the input value if it does not contain whitespace', () => {
    expect(trimNormalize('HelloWorld')).toBe('HelloWorld');
  });
});
