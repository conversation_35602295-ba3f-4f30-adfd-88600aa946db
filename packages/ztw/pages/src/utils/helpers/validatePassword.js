import {
  required,
  hasGoodPasswordStrength,
} from 'utils/validations';
import { isEmpty } from 'utils/lodash';

const validatePassword = (values, props) => {
  const errors = {};
  const {
    addAdmin, confirmPassword, password, status, isPasswordLoginAllowed,
    scope, locationName, newLocationCreateAllowed,
  } = values || {};
  const {
    initialValues: {
      isPasswordLoginAllowed: originalIsPasswordLoginAllowed,
    } = {},
  } = props || {};
  const { id: statusId } = status || {};

  if (statusId === 'disabled') return errors;

  if (scope && scope.id === 'LOCATION' && (isEmpty(locationName) && !newLocationCreateAllowed)) errors.locationName = 'REQUIRED';

  // Password is Optonal for Edit
  // Do not check for password if it is an existing Admin
  // OR isPasswordLoginAllowed was not touched.
  if (!addAdmin
    && isPasswordLoginAllowed
    && originalIsPasswordLoginAllowed === isPasswordLoginAllowed
    && !password && !confirmPassword) return errors;

  if (isPasswordLoginAllowed && (required(password) === 'REQUIRED' || hasGoodPasswordStrength(password))) errors.password = 'PASSWORD_STRENGTH_REQUIRED';
  
  if (isPasswordLoginAllowed && (required(confirmPassword) === 'REQUIRED' || hasGoodPasswordStrength(confirmPassword))) errors.confirmPassword = 'PASSWORD_STRENGTH_REQUIRED';

  if (isPasswordLoginAllowed && (required(password) === 'REQUIRED')) errors.password = 'REQUIRED';
  
  if (isPasswordLoginAllowed && (required(confirmPassword) === 'REQUIRED')) errors.confirmPassword = 'REQUIRED';

  if (confirmPassword !== password) {
    errors.password = 'PASSWORD_DONT_MATCH';
    errors.confirmPassword = 'PASSWORD_DONT_MATCH';
  }
  return errors;
};

export default validatePassword;
