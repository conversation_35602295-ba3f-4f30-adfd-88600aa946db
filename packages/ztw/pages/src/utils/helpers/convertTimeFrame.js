const convertTimeFrame = (timeFrame) => {
  // default - Last 24 Hours
  let convertedTime = Date.now() - (1 * 24 * 60 * 60 * 1000);

  if (timeFrame === 'last_1_min') {
    convertedTime = Date.now() - (1 * 1 * 60 * 1000);
  } else if (timeFrame === 'last_2_mins') {
    convertedTime = Date.now() - (1 * 2 * 60 * 1000);
  } else if (timeFrame === 'last_5_mins') {
    convertedTime = Date.now() - (1 * 5 * 60 * 1000);
  } else if (timeFrame === 'last_15_mins') {
    convertedTime = Date.now() - (1 * 15 * 60 * 1000);
  } else if (timeFrame === 'last_30_mins') {
    convertedTime = Date.now() - (1 * 30 * 60 * 1000);
  } else if (timeFrame === 'last_1_hour') {
    convertedTime = Date.now() - (1 * 1 * 60 * 60 * 1000);
  } else if (timeFrame === 'last_2_hours') {
    convertedTime = Date.now() - (1 * 2 * 60 * 60 * 1000);
  } else if (timeFrame === 'last_5_hours') {
    convertedTime = Date.now() - (1 * 5 * 60 * 60 * 1000);
  } else if (timeFrame === 'last_10_hours') {
    convertedTime = Date.now() - (1 * 10 * 60 * 60 * 1000);
  } else if (timeFrame === 'last_1_week') {
    // Prev Week > (7 * 24 * 60 * 60 * 1000)
    convertedTime = Date.now() - (7 * 24 * 60 * 60 * 1000);
  } else if (timeFrame === 'last_1_month') {
    convertedTime = Date.now() - (30 * 24 * 60 * 60 * 1000);
  } else {
    convertedTime = Date.now() - (1 * 24 * 60 * 60 * 1000);
  }
  return convertedTime;
};
  
export default convertTimeFrame;
