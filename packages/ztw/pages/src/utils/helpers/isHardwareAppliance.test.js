import isHardwareAppliance from './isHardwareAppliance';

describe('isHardwareAppliance function', () => {
  it('should return true for ZT400', () => {
    expect(isHardwareAppliance('ZT400')).toBe(true);
  });

  it('should return true for ZT600', () => {
    expect(isHardwareAppliance('ZT600')).toBe(true);
  });

  it('should return true for ZT800', () => {
    expect(isHardwareAppliance('ZT800')).toBe(true);
  });

  it('should return false for other values', () => {
    expect(isHardwareAppliance('other')).toBe(false);
    expect(isHardwareAppliance('')).toBe(false);
    expect(isHardwareAppliance(null)).toBe(false);
    expect(isHardwareAppliance(undefined)).toBe(false);
  });

  it('should return false for non-string values', () => {
    expect(isHardwareAppliance(123)).toBe(false);
    expect(isHardwareAppliance(true)).toBe(false);
    expect(isHardwareAppliance({})).toBe(false);
    expect(isHardwareAppliance([])).toBe(false);
  });
});
