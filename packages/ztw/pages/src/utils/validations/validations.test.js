/* eslint-disable no-unused-vars */
import {
  awsRoleNAmeAddress,
  checkArraySize,
  exactLength,
  hasGoodPasswordStrength,
  ipAddress,
  ipAddresses,
  ipAddressesOrRanges,
  ipAddressesOrRangesList,
  ipAddressesOrRangesListWithPortAndProtocol,
  ipAddressesOrRangesOrFqdn,
  ipAddressesOrRangesOrFqdnList,
  ipAddressesOrRangesOrFqdnListNoWildCard,
  ipAddressesOrRangesOrFqdnNoWildCard,
  ipAddressesOrRangesWithPortAndProtocol,
  ipAddressNotEnding0or255,
  ipAddressNotEnding0or255AndMvp1Validation,
  ipAddressOnly,
  ipAddressOnlyOptional,
  ipAddressOrIpAddressWithNetmask,
  ipAddressRange,
  ipAddressWithNetmaskOnly,
  ipRangeregex,
  isApi<PERSON><PERSON><PERSON>,
  isEmail,
  is<PERSON>mail<PERSON>ist,
  is<PERSON><PERSON>ger,
  isIPv4AddressInSubnet,
  isNumberOnly,
  isSameAsCurrentPassword,
  isSameAsNewPassword,
  isSiemURL,
  isTenantURL,
  isURL,
  isURLHost,
  isValidName,
  isWebURL,
  looseurlAddressSchemeless,
  macAddress,
  maxLength,
  maxValueLimit,
  minValueLimit,
  notZero,
  noWhiteSpacesAllowed,
  portRange,
  pureFqdn,
  pureFqdnOrWildcard,
  range,
  required,
  requiredId,
  requireObjectProperty,
  sameIP,
  sameServiceIP,
  sameServiceIPMask,
  validateAwsAccounts,
  validateCloudCustomEscapedCharacter,
  validatecustomEscapedCharacter,
  validateFrom,
  validateIpFromTo,
  validateIpInsideAnotherRange,
  validateTo,
  validateUnitBatchSize,
  isNotARestrictedCode,
} from './index';

describe('Validation functions', () => {
  describe('awsRoleNAmeAddress', () => {
    it('should return null for valid input', () => {
      expect(awsRoleNAmeAddress('abc123-456')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(awsRoleNAmeAddress('abc123!456')).not.toBeNull();
    });
  });

  describe('checkArraySize', () => {
    it('should return null for valid input', () => {
      expect(checkArraySize([1, 2, 3], 5)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(checkArraySize([1, 2, 3], 2)).not.toBeNull();
    });
  });

  describe('exactLength', () => {
    it('should return null for valid input', () => {
      expect(exactLength(5)('abcde')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(exactLength(5)('abcd')).not.toBeNull();
    });
  });

  describe('hasGoodPasswordStrength', () => {
    it('should return null for valid input', () => {
      expect(hasGoodPasswordStrength('Abcd1234!')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(hasGoodPasswordStrength('abcd1234')).not.toBeNull();
    });
  });

  describe('ipAddress', () => {
    it('should return null for valid input', () => {
      expect(ipAddress('***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddress('256.1.1.1')).not.toBeNull();
    });
  });

  describe('ipAddresses', () => {
    it('should return null for valid input', () => {
      expect(ipAddresses('***********,***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddresses('256.1.1.1,192.168.1.333')).not.toBeNull();
    });
  });

  describe('ipAddressesOrRanges', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRanges('***********-***********0')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRanges('256.1.1.1-***********0')).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesList', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesList(['***********-***********0', '***********1-***********0'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesList(['256.1.1.1-***********0', '***********1-***********0'])).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesListWithPortAndProtocol', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesListWithPortAndProtocol(['***********:80:TCP', '***********:443:UDP'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesListWithPortAndProtocol(['256.1.1.1:80:TCP', '***********:443:UDP'])).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesOrFqdn', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesOrFqdn('example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesOrFqdn('256.1.1.1')).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesOrFqdnList', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesOrFqdnList(['example.com', 'example2.com'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesOrFqdnList(['256.1.1.1', 'example2.com'])).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesOrFqdnListNoWildCard', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesOrFqdnListNoWildCard(['example.com', 'example2.com'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesOrFqdnListNoWildCard(['*.example.com', 'example2.com'])).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesOrFqdnNoWildCard', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesOrFqdnNoWildCard('example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesOrFqdnNoWildCard('*.example.com')).not.toBeNull();
    });
  });

  describe('ipAddressesOrRangesWithPortAndProtocol', () => {
    it('should return null for valid input', () => {
      expect(ipAddressesOrRangesWithPortAndProtocol('***********:80:TCP')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressesOrRangesWithPortAndProtocol('256.1.1.1:80:TCP')).not.toBeNull();
    });
  });

  describe('ipAddressNotEnding0or255', () => {
    it('should return null for valid input', () => {
      expect(ipAddressNotEnding0or255('***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressNotEnding0or255('***********')).not.toBeNull();
    });
  });

  describe('ipAddressNotEnding0or255AndMvp1Validation', () => {
    it('should return null for valid input', () => {
      expect(ipAddressNotEnding0or255AndMvp1Validation('***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressNotEnding0or255AndMvp1Validation('***********')).not.toBeNull();
    });
  });

  describe('ipAddressOnly', () => {
    it('should return null for valid input', () => {
      expect(ipAddressOnly('***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressOnly('256.1.1.1')).not.toBeNull();
    });
  });

  describe('ipAddressOnlyOptional', () => {
    it('should return null for valid input', () => {
      expect(ipAddressOnlyOptional('***********')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressOnlyOptional('256.1.1.1')).not.toBeNull();
    });
  });

  describe('ipAddressOrIpAddressWithNetmask', () => {
    it('should return null for valid input', () => {
      expect(ipAddressOrIpAddressWithNetmask('***********/24')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressOrIpAddressWithNetmask('256.1.1.1/24')).not.toBeNull();
    });
  });

  describe('ipAddressRange', () => {
    it('should return null for valid input', () => {
      expect(ipAddressRange('***********-***********0')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressRange('256.1.1.1-***********0')).not.toBeNull();
    });
  });

  describe('ipAddressWithNetmaskOnly', () => {
    it('should return null for valid input', () => {
      expect(ipAddressWithNetmaskOnly('***********/24')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(ipAddressWithNetmaskOnly('256.1.1.1/24')).not.toBeNull();
    });
  });

  describe('isApiKeyOK', () => {
    it('should return null for valid input', () => {
      expect(isApiKeyOK('***********2', '***********3')).toBeNull();
    });

    it('should return error: The new API key cannnot be the same as the current key', () => {
      expect(isApiKeyOK('***********2', '***********2')).not.toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isApiKeyOK('***********', '***********2')).not.toBeNull();
    });
  });

  describe('isEmail', () => {
    it('should return null for valid input', () => {
      expect(isEmail('<EMAIL>')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isEmail('example')).not.toBeNull();
    });
  });

  describe('isEmailList', () => {
    it('should return null for valid input', () => {
      expect(isEmailList(['<EMAIL>', '<EMAIL>'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isEmailList(['example', '<EMAIL>'])).not.toBeNull();
    });
  });

  describe('isInteger', () => {
    it('should return null for valid input', () => {
      expect(isInteger(123)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isInteger('abc')).not.toBeNull();
    });
  });

  describe('isIPv4AddressInSubnet', () => {
    it('should return null for valid input', () => {
      expect(isIPv4AddressInSubnet('***********', '***********/24')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isIPv4AddressInSubnet('***********', '***********/24')).not.toBeNull();
    });
  });

  describe('isNumberOnly', () => {
    it('should return null for valid input', () => {
      expect(isNumberOnly(123)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isNumberOnly('abc')).not.toBeNull();
    });
  });

  describe('isSameAsCurrentPassword', () => {
    it('should return null for valid input', () => {
      expect(isSameAsCurrentPassword('password', { currentPassword: 'different' })).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isSameAsCurrentPassword('password', { currentPassword: 'password' })).not.toBeNull();
    });
  });

  describe('isSameAsNewPassword', () => {
    it('should return null for valid input', () => {
      expect(isSameAsNewPassword('password', { newPassword: 'password' })).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isSameAsNewPassword('password', { newPassword: 'different' })).not.toBeNull();
    });
  });

  describe('isSiemURL', () => {
    it('should return null for valid input', () => {
      expect(isSiemURL('https://example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isSiemURL('example')).not.toBeNull();
    });
  });

  describe('isTenantURL', () => {
    it('should return null for valid input', () => {
      expect(isTenantURL('https://example.com', 'example')('tenant')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isTenantURL('https://example.com', 'example')('1')).not.toBeNull();
    });
  });

  describe('isURL', () => {
    it('should return null for valid input', () => {
      expect(isURL('https://example.com')).toBeNull();
    });
    it('should return null for valid input', () => {
      expect(isURL('https://example.com/path/to/resource')).toBeNull();
    });
    it('should return null for valid input', () => {
      expect(isURL('http://example.com')).toBeNull();
    });
    it('should return null for valid input', () => {
      expect(isURL('www.example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isURL('example')).not.toBeNull();
    });
    it('should return error message for invalid input', () => {
      expect(isURL('htt://example.com')).not.toBeNull();
    });
    it('should return error message for invalid input', () => {
      expect(isURL('http:/example.com ')).not.toBeNull();
    });
    it('should return error message for invalid input', () => {
      expect(isURL('http://example')).not.toBeNull();
    });
  });

  describe('isURLHost', () => {
    it('should return null for valid input', () => {
      expect(isURLHost('www.example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isURLHost('example')).not.toBeNull();
    });
  });

  describe('isValidName', () => {
    it('should return null for valid input', () => {
      expect(isValidName('example')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isValidName('example!')).not.toBeNull();
    });
  });

  describe('isWebURL', () => {
    it('should return null for valid input', () => {
      expect(isWebURL('https://example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(isWebURL('example')).not.toBeNull();
    });
  });

  describe('looseurlAddressSchemeless', () => {
    it('should return null for valid input', () => {
      expect(looseurlAddressSchemeless('example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(looseurlAddressSchemeless('example')).not.toBeNull();
    });
  });

  describe('macAddress', () => {
    it('should return null for valid input', () => {
      expect(macAddress('00:11:22:33:44:55')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(macAddress('00:11:22:33:44')).not.toBeNull();
    });
  });

  describe('maxLength', () => {
    it('should return null for valid input', () => {
      expect(maxLength(10)('example')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(maxLength(5)('example')).not.toBeNull();
    });
  });

  describe('maxValueLimit', () => {
    it('should return null for valid input', () => {
      expect(maxValueLimit(10)(5)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(maxValueLimit(5)(10)).not.toBeNull();
    });
  });

  describe('minValueLimit', () => {
    it('should return null for valid input', () => {
      expect(minValueLimit(5)(10)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(minValueLimit(10)(5)).not.toBeNull();
    });
  });

  describe('notZero', () => {
    it('should return null for valid input', () => {
      expect(notZero(1)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(notZero(0)).not.toBeNull();
    });
  });

  describe('noWhiteSpacesAllowed', () => {
    it('should return null for valid input', () => {
      expect(noWhiteSpacesAllowed('example')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(noWhiteSpacesAllowed('ex ample')).not.toBeNull();
    });
  });

  describe('portRange', () => {
    it('should return null for valid input', () => {
      expect(portRange('80-90')).toBe('');
    });

    it('should return error message for invalid input', () => {
      expect(portRange('80-80')).not.toBe('');
    });
  });

  describe('pureFqdn', () => {
    it('should return null for valid input', () => {
      expect(pureFqdn('example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(pureFqdn('example')).not.toBeNull();
    });
  });

  describe('pureFqdnOrWildcard', () => {
    it('should return null for valid input', () => {
      expect(pureFqdnOrWildcard('*.example.com')).toBeNull();
    });
    it('should return null for valid input', () => {
      expect(pureFqdnOrWildcard('_ldap._tcp.example.com')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(pureFqdnOrWildcard('example')).not.toBeNull();
    });
  });

  describe('range', () => {
    it('should return null for valid input', () => {
      expect(range(5, 1, 10)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(range(15, 1, 10)).not.toBeNull();
    });
  });

  describe('required', () => {
    it('should return null for valid input', () => {
      expect(required('example')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(required('')).not.toBeNull();
    });
  });

  describe('requiredId', () => {
    it('should return null for valid input', () => {
      expect(requiredId({ id: 'example' })).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(requiredId({})).not.toBeNull();
    });
  });

  describe('requireObjectProperty', () => {
    it('should return null for valid input', () => {
      expect(requireObjectProperty('id')({ id: 'example' })).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(requireObjectProperty('id')({})).not.toBeNull();
    });
  });

  describe('sameIP', () => {
    it('should return null for valid input', () => {
      expect(sameIP(['***********', '***********'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(sameIP(['***********', '***********'])).not.toBeNull();
    });
  });

  describe('sameServiceIP', () => {
    it('should return null for valid input', () => {
      expect(sameServiceIP(['***********/24', '***********/24'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(sameServiceIP(['***********/24', '***********/24'])).not.toBeNull();
    });
  });

  describe('sameServiceIPMask', () => {
    it('should return null for valid input', () => {
      expect(sameServiceIPMask(['***********/24', '***********/24'])).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(sameServiceIPMask(['***********/24', '***********/25'])).not.toBeNull();
    });
  });

  describe('validateAwsAccounts', () => {
    it('should return null for valid input', () => {
      expect(validateAwsAccounts('***********2')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateAwsAccounts('***********')).not.toBeNull();
    });
  });

  describe('validateCloudCustomEscapedCharacter', () => {
    it('should return null for valid input', () => {
      expect(validateCloudCustomEscapedCharacter('abc')).toBeNull();
    });

    it('should return null message for empty input', () => {
      expect(validateCloudCustomEscapedCharacter('')).toBeNull();
    });
  });

  describe('validatecustomEscapedCharacter', () => {
    it('should return null for valid input', () => {
      expect(validatecustomEscapedCharacter('abc')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validatecustomEscapedCharacter('abcd!')).not.toBeNull();
    });
  });

  describe('validateFrom', () => {
    it('should return null for valid input', () => {
      expect(validateFrom(5, 10, 100)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateFrom(15, 10, 100)).not.toBeNull();
    });
  });

  describe('validateIpFromTo', () => {
    it('should return null for valid input', () => {
      expect(validateIpFromTo('***********', '***********0')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateIpFromTo('***********0', '***********')).not.toBeNull();
    });
  });

  describe('validateIpInsideAnotherRange', () => {
    it('should return null for valid input', () => {
      expect(validateIpInsideAnotherRange('***********', '***********55', '***********/24')).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateIpInsideAnotherRange('***********', '***********55', '***********/24')).not.toBeNull();
    });
  });

  describe('validateTo', () => {
    it('should return null for valid input', () => {
      expect(validateTo(5, 10, 100)).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateTo(10, 5, 100)).not.toBeNull();
    });
  });

  describe('validateUnitBatchSize', () => {
    it('should return null for valid input', () => {
      expect(validateUnitBatchSize(10, { sizeUnit: { id: 'MB' }, siemType: { id: 'S3' } })).toBeNull();
    });

    it('should return error message for invalid input', () => {
      expect(validateUnitBatchSize(1000, { sizeUnit: { id: 'MB' }, siemType: { id: 'SPLUNK' } })).not.toBeNull();
    });
  });
});

describe('isNotARestrictedCode function', () => {
  it('should return null for non-restricted codes', () => {
    expect(isNotARestrictedCode('1')).toBeNull();
    expect(isNotARestrictedCode('4')).toBeNull();
    expect(isNotARestrictedCode('7')).toBeNull();
  });

  it('should return SORRY_THIS_CODE_CAN_NOT_BE_USED for restricted codes', () => {
    // eslint-disable-next-line max-len
    // const restrictedCodes = [2, 3, 6, 15, 18, 50, 51, 52, 53, 54, 56, 81, 82, 91, 108, 118, 119, 212];
    expect(isNotARestrictedCode('2')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('3')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('6')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('15')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('53')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('81')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('82')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('91')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('108')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('118')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('119')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
    expect(isNotARestrictedCode('212')).toBe('SORRY_THIS_CODE_CAN_NOT_BE_USED');
  });

  it('should handle non-numeric input', () => {
    expect(isNotARestrictedCode('abc')).toBeNull();
    expect(isNotARestrictedCode('')).toBeNull();
  });

  it('should handle NaN input', () => {
    expect(isNotARestrictedCode('NaN')).toBeNull();
  });
});
