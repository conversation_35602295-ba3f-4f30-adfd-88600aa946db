const validations = require('./index.js');

test('ipAddressesOrRangesWithPortAndProtocol', () => {
  // Empty Case
  expect(validations.ipAddressesOrRangesWithPortAndProtocol(null)).toBe('Please enter a valid IP');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol(undefined)).toBe('Please enter a valid IP');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('')).toBe('Please enter a valid IP');
  
  // Success Cases
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('**************')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('*********-*********0 ')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***********/24')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('*********:80')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('*********:80:TCP')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('*********/24:80-100:UDP')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************:443:Tcp')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************/24')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************/24:443')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************/24:443:tcp')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************-***************')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************-***************:443')).toBeNull();
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************-***************:443:tcp')).toBeNull();

  // Enter a valid IP address, IP address range, or IP CIDR block.
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('123.123.123.333')).toBe('Enter a valid IP address, IP address range, or IP CIDR block.');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************/33')).toBe('Enter a valid IP address, IP address range, or IP CIDR block.');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************-123.123.123.323')).toBe('Enter a valid IP address, IP address range, or IP CIDR block.');

  // Please enter a valid TCP Port (0-65535)
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************:0.2:http')).toBe('Please enter a valid TCP Port (0-65535)');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************:455543:http')).toBe('Please enter a valid TCP Port (0-65535)');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************/24:455543:tcp')).toBe('Please enter a valid TCP Port (0-65535)');
  expect(validations.ipAddressesOrRangesWithPortAndProtocol('***************-***************:455543:tcp')).toBe('Please enter a valid TCP Port (0-65535)');
});
