const validations = require('./index.js');

describe('validations.pureFqdnOrWildcard', () => {
  it('should return null when input is a valid FQDN', () => {
    const fqdn = 'example.com';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBeNull();
  });

  it('should return null when input is a valid wildcard FQDN', () => {
    const fqdn = '*.example.com';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBeNull();
  });

  it('should return an error message when input is an invalid FQDN', () => {
    const fqdn = 'example';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBe(('Enter a valid domain name.'));
  });

  it('should return an error message when input contains an IP address', () => {
    const fqdn = '***********';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBe(('Enter a valid domain name.'));
  });

  it('should return an error message when input contains a dash at the beginning or end of a label', () => {
    const fqdn = '-example.com';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBe(('Enter a valid domain name.'));
  });

  it('should return an error message when input contains consecutive dots', () => {
    const fqdn = 'example..com';
    expect(validations.pureFqdnOrWildcard(fqdn)).toBe(('Enter a valid domain name.'));
  });

  it('should return null when input is a comma-separated list of valid FQDNs', () => {
    const fqdnList = 'example.com,example.net';
    expect(validations.pureFqdnOrWildcard(fqdnList)).toBeNull();
  });

  it('hould return null when input contain underscore of valid FQDNs', () => {
    expect(validations.pureFqdnOrWildcard('_ldap._tcp.example.com')).toBeNull();
  });

  it('should return an error message when input is a comma-separated list containing an invalid FQDN', () => {
    const fqdnList = 'example.com,example';
    expect(validations.pureFqdnOrWildcard(fqdnList)).toBe(('Enter a valid domain name.'));
  });
});
