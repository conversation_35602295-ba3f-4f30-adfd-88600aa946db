/* eslint-disable no-tabs */
/* eslint-disable radix */
/* eslint-disable no-shadow */
/* eslint-disable block-scoped-var */
/* eslint-disable max-len */
/* eslint-disable no-useless-escape */
import i18n from 'utils/i18n';
import {
  isArray, isEmpty, get, isNull,
} from '../lodash';
import {
  ipv4SubnetToBinStr,
  ipv4SubnetCastToBinStr,
  ipv4DotQuadToInt,
  // ipv4IntToDotQuad,
  ipv4IntToBinStr,
  ipv4BinStrtoInt,
  ipv4BitsNMtoBinStrNM,
} from '../helpers/ipv4SubnetValidation';

// ipRegex: includes CIDR
const isNumberOnlyRegex = /^[0-9]*$/;
const ipRegex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([1-2]\d|3[0-2]|\d)){0,1}$/;
const ipRangeregex = /^((([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]))[\s]*-[\s]*((([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]))$/;
const portRangeRegex = /^\s*(\d+)(?:\s*-\s*(\d+))?\s*$/;
// const fqdn = /^[a-zA-Z_0-9-\_]+(\.[a-zA-Z0-9-_\.]*)+$/;
// const fqdn = /^(?!:\/\/)([a-zA-Z0-9]+\.)?[a-zA-Z0-9][a-zA-Z0-9-]+\.[a-zA-Z]{2,6}?$/i;
// const fqdn = /^(?=^.{4,253}$)(^((?!-)[a-zA-Z0-9-]{1,63}(?<!-)\.)+[a-zA-Z]{2,63}$)/i;

// const fqdn = /^(?=^.{4,253}$)(^((?!-)[a-zA-Z0-9-]{1,63}(?<!-)\.)+[a-zA-Z]{2,63}$)/i;
// const fqdnOrWildCard = /^(?:\*\.)?([a-zA-Z0-9-]+\.){0,}[a-zA-Z]{2,}$/;
const fqdn = /^(?!:\/\/)(?!.{256,})(([a-z0-9-][a-z0-9-]*?)|([a-z0-9][a-z0-9-]*?\.)+?[a-z]{2,63}?)$/i;
const fqdnOrWildCard = /^(?:\*\.)?(?!:\/\/)(?!.{256,})(([a-z0-9_-][a-z0-9_-]*?)|([a-z0-9_][a-z0-9_-]*?\.)+?[a-z]{2,63}?)$/i;

// This regex allows all combinations of special characters in the URLs
// mostly used in the URL categories.
// The last part of the regex is used to determine whether url is ending with ".", "./", "/"
// EX:grumpywookie.wordpress.com/category/moss/., yahoo.com:3455, yahoo.com./
const looseurlAddressSchemelessRegEx = /^(\.)?[a-z0-9@_*\+~\-]+(\.[a-z0-9_-]+)+(:[a-z0-9_-]+)?([/\?].+|.[\/]?)?$/i;

// alphaNumericWithHyphen Regex (name not to be confused, Special characters apart from hyphen also validated)
const alphaNumericWithHyphenRegex = /^[A-Za-z0-9-/_*;?|.~+ ]*=*$/;
        
const isInteger = (value) => {
  if (typeof value === 'undefined') return null;

  return (!isNaN(value) ? null : 'INTEGER_REQUIRED'); // eslint-disable-line no-restricted-globals
};

const isURL = (value) => {
  const regex = /^(?:http(s)?:\/\/)?([\w.-]+\.)+[a-zA-Z]{2,}[\w\.-\/\?\=\&\%\#\-\_\.\+\~]*$/;

  return regex.test(value) ? null : 'Please enter a valid URL';
};

const isSiemURL = (value) => {
  const regex = /^https?:\/\/[a-z0-9-]+([\.:][a-z0-9-]+)+([/\?].+|[/])?$/i;
  return regex.test(value) ? null : 'VALIDATION_ERROR_INVALID_URL';
};

const ipAddressRange = (value) => {
  return (ipRegex.test(value) || ipRangeregex.test(value)) ? null : 'Please enter a valid IP';
};

const ipAddressesOrRanges = (values) => {
  const ipList = values ? values.split(',') : 0;
  let validation = null;
  if (ipList.length > 0) {
    // eslint-disable-next-line consistent-return
    ipList.every((item) => {
      validation = (!ipRegex.test(item.trim()) && !ipRangeregex.test(item.trim())) ? i18n.t('VALIDATION_ERROR_INVALID_IP_ADDRESS') : null;
      if (validation) return false;
      return true;
    });
  }
  return validation;
};

const macAddress = (value) => {
  if (isEmpty(value)) return null;
  const regex = /^([0-9A-Fa-f]{2}[:.]){5}([0-9A-Fa-f]{2})|([0-9a-fA-F]{4}\\.[0-9a-fA-F]{4}\\.[0-9a-fA-F]{4})$/;

  if (value.length !== 17) return i18n.t('VALIDATION_ERROR_INVALID_MAC_ADDRESS');

  return ((typeof value === 'string' || value instanceof String) && regex.test(value)) ? null : i18n.t('VALIDATION_ERROR_INVALID_MAC_ADDRESS');
};

const validateIpFromTo = (ipFrom, ipTo) => {
  if (isEmpty(ipFrom) || isEmpty(ipTo)) return null;
  const [e1, e2, e3, e4] = ipFrom && ipFrom.split('.');
  const [s1, s2, s3, s4] = ipTo && ipTo.split('.');
  
  return Number(s1) > Number(e1)
      || (Number(s1) === Number(e1) && Number(s2) > Number(e2))
      || (Number(s1) === Number(e1) && Number(s2) === Number(e2) && Number(s3) > Number(e3))
      || (Number(s1) === Number(e1) && Number(s2) === Number(e2) && Number(s3) === Number(e3) && Number(s4) >= Number(e4))
    ? null
    : 'RANGE_FROM_BIGGER_THAN_TO';
};

const validateIpInsideAnotherRange = (ipFrom, ipTo, ip) => {
  if (isEmpty(ipFrom) || isEmpty(ipTo) || isEmpty(ip)) return null;

  const noMask = ip.split('/')[0];

  const [e1, e2, e3, e4] = ipFrom && ipFrom.split('.');
  const [s1, s2, s3, s4] = ipTo && ipTo.split('.');
  const [i1, i2, i3, i4] = noMask && noMask.split('.');
  
  const ipIsBiggerFrom = Number(i1) > Number(e1)
      || (Number(i1) === Number(e1) && Number(i2) > Number(e2))
      || (Number(i1) === Number(e1) && Number(i2) === Number(e2) && Number(i3) > Number(e3))
      || (Number(i1) === Number(e1) && Number(i2) === Number(e2) && Number(i3) === Number(e3) && Number(i4) >= Number(e4));

  const ipIsSmallerTo = Number(s1) > Number(i1)
    || (Number(s1) === Number(i1) && Number(s2) > Number(i2))
    || (Number(s1) === Number(i1) && Number(s2) === Number(i2) && Number(s3) > Number(i3))
    || (Number(s1) === Number(i1) && Number(s2) === Number(i2) && Number(s3) === Number(i3) && Number(s4) >= Number(i4));

  return ipIsBiggerFrom && ipIsSmallerTo ? 'ADDRESS_RANGES_SHOULD_NOT_OVERLAP' : null;
};

const ipAddressOnly = (value) => {
  if (isEmpty(value)) return null;
  const regex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/;

  return ((typeof value === 'string' || value instanceof String) && regex.test(value)) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP');
};

const notZero = (value) => {
  if (isEmpty(value)) return null;
  
  return (Number(value) === 0) ? i18n.t('ZERO_IS_RESERVED_TO_UNTAGGED_INTERFACES') : null;
};

const ipAddressOnlyOptional = (value) => {
  if (isEmpty(value)) return null;

  const regex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/;

  return ((typeof value === 'string' || value instanceof String) && regex.test(value)) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP');
};

const ipAddresses = (values) => {
  const ipList = values ? values.split(',') : 0;
  if (ipList.length > 0) {
    let validation = null;
    // eslint-disable-next-line consistent-return
    ipList.forEach((item) => {
      validation = (ipRegex.test(item)) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP');
    });
    return validation;
  }

  return (ipRegex.test(values)) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP');
};

const ipAddressWithNetmaskOnly = (value) => {
  if (isEmpty(value)) return null;
  const regex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))$/;
  // const regex = /^([0-9]{1,3}\.){3}[0-9]{1,3}(\/([0-9]|[1-2][0-9]|3[0-2]))?$/;

  return ((typeof value === 'string' || value instanceof String) && regex.test(value)) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP_WITH_CIDR');
};

const ipAddressOrIpAddressWithNetmask = (value) => {
  return ((typeof value === 'string' || value instanceof String)
      && (!(ipAddressOnly(value)) || (!ipAddressWithNetmaskOnly(value)))) ? null : i18n.t('VALIDATION_ERROR_INVALID_IP');
};

const isIPv4URL = (value) => { // http(s):// followed by IPv4 and optional trailing /
  const regex = /^https?:\/\/(?:[0-9]{1,3}\.){3}[0-9]{1,3}\/?$/i;

  return regex.test(value) ? null : 'VALID_IPV4_REQUIRED';
};

const isIPv6URL = (value) => { // http(s):// followed by IPv6 and optional trailing /
  const regex = /^https?:\/\/(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}\/?$/i;

  return regex.test(value) ? null : 'VALID_IPV6_REQUIRED';
};

const isValidURL = (value) => { // includes validation for http(s):// followed by localhost/domain/FQDN
  const regex = /^https?:\/\/[a-z0-9-]+([.:]?[a-z0-9-]+)+([/?].+|[/])?$/i;

  return regex.test(value) ? null : 'VALID_LOCALHOST_REQUIRED';
};

const pureFqdn = (values) => {
  const fqdnList = values ? values.split(',') : 0;
  if (fqdnList.length > 0) {
    let validation = null;
    // eslint-disable-next-line consistent-return
    fqdnList.forEach((item) => {
      validation = (fqdn.test(item) === true
      && !(item.includes('.-') || item.includes('-.'))
      && item.includes('.')
      && ipRegex.test(item) === false) ? null : i18n.t('VALIDATION_ERROR_INVALID_DOMAIN');
    });
    return validation;
  }

  return (fqdn.test(values) === true
            && !(values.includes('.-') || values.includes('-.'))
            && ipRegex.test(values) === false) ? null : i18n.t('VALIDATION_ERROR_INVALID_DOMAIN');
};

const pureFqdnOrWildcard = (values) => {
  const fqdnList = values ? values.split(',') : 0;
  if (fqdnList.length > 0) {
    let validation = null;
    // eslint-disable-next-line consistent-return
    fqdnList.forEach((item) => {
      validation = (fqdnOrWildCard.test(item) === true
      && !(item.includes('.-') || item.includes('-.'))
      && ipRegex.test(item) === false
      && item.includes('.')) ? null : i18n.t('VALIDATION_ERROR_INVALID_DOMAIN');
    });
    return validation;
  }

  return (fqdnOrWildCard.test(values) === true
            && !(values.includes('.-') || values.includes('-.'))
            && ipRegex.test(values) === false) ? null : i18n.t('VALIDATION_ERROR_INVALID_DOMAIN');
};

const looseurlAddressSchemeless = (value) => {
  const fqdnList = value && value.length > 0 ? value.split(',') : 0;
  if (fqdnList.length > 0) {
    let validation = null;
    // eslint-disable-next-line consistent-return
    fqdnList.forEach((item) => {
      validation = (looseurlAddressSchemelessRegEx.test(item)) ? null : i18n.t('VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS');
    });
    return validation;
  }

  return (looseurlAddressSchemelessRegEx.test(value)) ? null : i18n.t('VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS');

  // if (typeof value === 'string' && looseurlAddressSchemelessRegEx.test(value)) {
  //     return null;
  // }
  // return i18n.t('VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS');
};

const ipAddressesOrRangesList = (values = []) => {
  const ipAddressesOrRangesValidation = values.some((x) => ipAddressesOrRanges(x) && ipAddressesOrRanges(x) !== '');

  if (ipAddressesOrRangesValidation) return i18n.t('VALIDATION_ERROR_INVALID_IP_ADDRESS');

  return null;
};

const ipAddressesOrRangesOrFqdn = (value) => {
  const ipAddressesOrRangesValidation = ipAddressesOrRanges(value);
  // looseurlAddressSchemeless or pureFqdn
  const looseurlAddressSchemelessValidation = pureFqdnOrWildcard(value);

  if (!isEmpty(ipAddressesOrRangesValidation) && !isEmpty(looseurlAddressSchemelessValidation)) return i18n.t('VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS');

  return null;
};

const ipAddressesOrRangesOrFqdnList = (values = []) => {
  const validationError = values.some((x) => (ipAddressesOrRanges(x) && ipAddressesOrRanges(x) !== '')
    && (pureFqdnOrWildcard(x) && pureFqdnOrWildcard(x) !== ''));

  if (validationError) return i18n.t('VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS');

  return null;
};

const ipAddressesOrRangesOrFqdnNoWildCard = (value) => {
  const ipAddressesOrRangesValidation = ipAddressesOrRanges(value);
  // looseurlAddressSchemeless or pureFqdn
  const looseurlAddressSchemelessValidation = pureFqdn(value);

  if (!isEmpty(ipAddressesOrRangesValidation) && !isEmpty(looseurlAddressSchemelessValidation)) return i18n.t('VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS');

  return null;
};

const ipAddressesOrRangesOrFqdnListNoWildCard = (values = []) => {
  const validation = values.map((item) => {
    const ipAddressesOrRangesValidation = ipAddressesOrRanges(item);
    // looseurlAddressSchemeless or pureFqdn
    const looseurlAddressSchemelessValidation = pureFqdn(item);
    return (!isEmpty(ipAddressesOrRangesValidation) && !isEmpty(looseurlAddressSchemelessValidation)) ? i18n.t('VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS') : null;
  });
  const hasError = validation.some((x) => !isEmpty(x));

  return hasError || null;
};

const isWebURL = (value) => {
  if ((isValidURL(value) === null || isIPv4URL(value) === null)
    && isIPv6URL(value) !== null) { // isIPv6 not supported yet
    return null;
  }

  return 'VALID_URL_REQUIRED';
};

// Checks if the array size is less than or equal to max.
const checkArraySize = (val, max) => {
  if (val === null || !(val instanceof Array)) {
    return true;
  }
  const maxNum = parseInt(max, 10);

  if (maxNum != null && !Number.isNaN(maxNum) && val.length <= maxNum) {
    return null;
  }
  return 'VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE';
};

const isURLHost = (value) => {
  const regex = /^www\.\w+\.\w+$/i;

  return regex.test(value) ? null : 'Please enter a valid host URL (e.g. www.zscaler.com)';
};

const ipAddress = (val) => {
  const ipAddressRegEx = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([1-2]\d|3[0-2]|\d)){0,1}$/;

  return ipAddressRegEx.test(val) ? null : 'Please enter a valid IP Address';
};

// eslint-disable-next-line func-names
const isTenantURL = (appUrl, appPrefix) => function (value) {
  if (value) {
    const parts = appUrl.split(appPrefix);
    // eslint-disable-next-line react/destructuring-assignment
    const tenant = value.replace(parts[0], '').replace(parts[1], '');
    const regex = /^[a-z]+[a-z0-9._-]+[a-z0-9]+$/i; // alphanumerics, dot, _ and - only

    if (tenant === '') {
      return 'REQUIRED';
    }

    if (!regex.test(tenant)) {
      return 'VALID_TENANT_REQUIRED';
    }
  }

  return null;
};

const isEmail = (value) => {
  // eslint-disable-next-line prefer-regex-literals
  const emailRegex = new RegExp(/^[^.;,()@[\]]{1}[A-Z0-9_!#$%&'*+/=?`{|}~^.-]*@[A-Z0-9.\-_]+[.]+[A-Z0-9]+$/, 'i');

  if (!emailRegex.test(value)) {
    return 'PLEASE_ENTER_VALID_EMAIL_ADDRESS';
  }

  return null;
};

const isEmailList = (list) => {
  // eslint-disable-next-line prefer-regex-literals
  const emailRegex = new RegExp(/^[^.;,()@[\]]{1}[A-Z0-9_!#$%&'*+/=?`{|}~^.-]*@[A-Z0-9.\-_]+[.]+[A-Z0-9]+$/, 'i');

  for (let i = 0, l = list.length; i < l; i += 1) {
    if (!emailRegex.test(list[i])) {
      return 'PLEASE_ENTER_VALID_EMAIL_ADDRESS_COMMA_SEPARATED_LIST';
    }
  }

  return null;
};

const isApiKeyOK = (value, original) => {
  if (!value) return i18n.t('REQUIRED');
  if (value && value.length !== 12) return i18n.t('INVALID_API_KEY');

  // eslint-disable-next-line prefer-regex-literals
  const apiKeyRegex = new RegExp(/^[a-zA-Z0-9]+$/, 'i');

  if (!apiKeyRegex.test(value)) return i18n.t('INVALID_API_KEY');

  if (value === original) return i18n.t('ERROR_API_MUST_BE_DIFFERENT');

  return null;
};

const required = (value) => (
  typeof value === 'undefined'
    || (Array.isArray(value) && value.length === 0)
    || (typeof value === 'string' && !value.trim())
    ? 'REQUIRED' : null);

const requiredId = (value) => (
  isEmpty(value) || required(value.id)
    ? 'REQUIRED' : null);

// eslint-disable-next-line func-names
const requireObjectProperty = (property) => function (object) {
  // eslint-disable-next-line react/destructuring-assignment
  const value = object && object[property];
  if (typeof value === 'undefined'
    || (Array.isArray(value) && value.length === 0)
    || (typeof value === 'string' && !value.trim())) {
    return 'REQUIRED';
  }
  return null;
};

function isNumberOnly(n) {
  if (isEmpty(n)) return null;
  // eslint-disable-next-line no-restricted-globals
  return (isNumberOnlyRegex.test(n)) ? null : i18n.t('NON_NUMERIC_VALUE');
}

// function isInteger(n) {
//   // eslint-disable-next-line no-restricted-globals
//   return (!isNaN(parseFloat(n)) && isFinite(n)) ? null : 'NON_NUMERIC_VALUE';
// }

const isValidName = (val) => {
  const isValidNameRegex = /^[a-zA-Z_0-9-. _]+[a-zA-Z_0-9-._\s]*$/;
  // const isValidNameRegex = /^[a-zA-Z_0-9-\\._]+[a-zA-Z_0-9-\\._\\s]*$/;
  //        specialString: /^[a-zA-Z_0-9-. _]+[a-zA-Z_0-9-._\s]*$/,

  return isValidNameRegex.test(val) ? null : i18n.t('VALIDATION_ERROR_INVALID_NAME');
};

const awsRoleNAmeAddress = (val) => {
  const awsRoleNAmeAddressRegex = /^[\w+=,.@-]+$/;

  return awsRoleNAmeAddressRegex.test(val) ? null : i18n.t('VALIDATION_ERROR_INVALID_AWS_ROLENAME');
};
const maxLength = (max) => (value) => (value && value.length > max ? `${max} ${i18n.t('MAX_CHARACTER_LIMIT_EXCEEDED', { limit: max })} ` : null);

const exactLength = (len) => (value) => (value && value.length === len ? null : `${i18n.t('SIZE_MUST_BE_EXACT_LENGTH')} ${len}`);

const maxValueLimit = (testValue) => (value) => {
  return (value && Number(value) > Number(testValue) ? `${i18n.t('MAX_VALUE_LIMIT_ERROR')} ${testValue}` : null);
};

const minValueLimit = (testValue) => (value) => {
  return (value && Number(value) < Number(testValue) ? `${i18n.t('MIN_VALUE_LIMIT_ERROR')} ${testValue}` : null);
};

const notRequired = (val) => {
  return val == null || val instanceof Array || val === '';
};

const range = (value, min, max) => (
  value >= min && value <= max ? null : i18n.t('RANGE_ERROR', { min, max })
);

// Checks if the port range object has valid start and end property values.
const portRange = (val) => {
  if (notRequired(val)) {
    return '';
  }

  const match = portRangeRegex.exec(val);

  if (typeof val === 'string' === true && match) {
    const start = parseInt(match[1], 10);
    let end;

    if (match[2]) {
      if (match[1] === match[2]) {
        return 'VALIDATION_ERROR_SAME_START_END_PORT_RANGE';
      }
      end = parseInt(match[2], 10);
    }

    if (required(start) !== null || isInteger(start) !== null || range(start, 1, 65535) !== null) {
      return 'VALIDATION_ERROR_INVALID_START_PORT_RANGE';
    }
    if (end != null) {
      if (isInteger(end) !== null || range(end, start, 65535) !== null) {
        return 'VALIDATION_ERROR_INVALID_END_PORT_RANGE';
      }
    }
  } else {
    return 'VALIDATION_ERROR_INVALID_PORT_STRING';
  }

  return '';
};

const validateFrom = (from, to, limit) => {
  if (to && from && (parseInt(to, 10) < parseInt(from, 10))) {
    return '\'From\' should be less than \'To\'';
  } if (from) {
    return range(from, 1, limit);
  }
  return '';
};

const validateTo = (from, to, limit) => {
  if (to && from && (parseInt(to, 10) < parseInt(from, 10))) {
    return '\'To\' should be greath than \'From\'';
  } if (to) {
    return range(to, 1, limit);
  }
  return '';
};

const ipAddressesOrRangesWithPortAndProtocol = (values) => {
  if (isEmpty(values)) return i18n.t('VALIDATION_ERROR_INVALID_IP');
  // IP:PORT:PROTOCOL
  const ipPortProtocol = values ? values.toUpperCase().split(':') : [];
  let validationFail = null;
  if (ipPortProtocol[0]?.length > 0) {
    validationFail = (!ipRegex.test(ipPortProtocol[0].trim()) && !ipRangeregex.test(ipPortProtocol[0].trim())) ? i18n.t('VALIDATION_ERROR_INVALID_IP_ADDRESS') : null;
  }
  if (!validationFail) {
    if (ipPortProtocol?.length > 1) {
      const ports = ipPortProtocol[1]?.split('-');
      if (ports.length === 1) {
        validationFail = (!Number.isInteger(Number(ipPortProtocol[1])) || Number(ipPortProtocol[1]) < 0 || Number(ipPortProtocol[1]) > 65535) ? i18n.t('VALIDATION_ERROR_INVALID_IP_PORT') : null;
      } else if (ports.length === 2) {
        validationFail = (!Number.isInteger(Number(ports[0])) || Number(ports[0]) < 0 || Number(ports[0]) > 65535) ? i18n.t('VALIDATION_ERROR_INVALID_IP_PORT') : null;
        if (!validationFail) {
          validationFail = (!Number.isInteger(Number(ports[1])) || Number(ports[1]) < 0 || Number(ports[1]) > 65535) ? i18n.t('VALIDATION_ERROR_INVALID_IP_PORT') : null;
        }
      } else {
        validationFail = i18n.t('VALIDATION_ERROR_INVALID_IP_PORT');
      }
    }
  }
  if (!validationFail) {
    if (ipPortProtocol?.length > 2) {
      validationFail = isEmpty(ipPortProtocol[2]) ? i18n.t('VALIDATION_ERROR_EMPTY_PROTOCOL') : null;
      validationFail = !(['UDP', 'TCP'].includes(ipPortProtocol[2])) ? i18n.t('VALIDATION_ERROR_INVALID_PROTOCOL') : null;
    }
  }

  return validationFail;
};

const ipAddressesOrRangesListWithPortAndProtocol = (values = []) => {
  const ipAddressesOrRangesWithPortAndProtocolValidation = values.some((x) => ipAddressesOrRangesWithPortAndProtocol(x) && ipAddressesOrRangesWithPortAndProtocol(x) !== '');
  if (ipAddressesOrRangesWithPortAndProtocolValidation) return i18n.t('VALIDATION_ERROR_INVALID_IP_ADDRESS');

  return null;
};

const hasGoodPasswordStrength = (value) => {
  // 1. length is greater or equal to 8
  // 2. at least one digit
  // 3. at least one capital letter
  // 4. at least one non-alphanumeric character
  if (value.length < 8) {
    return i18n.t('PASSWORD_STRENGTH');
  }
  let regex = /[^a-zA-Z0-9]/;
  if (!regex.test(value)) {
    return i18n.t('PASSWORD_STRENGTH');
  }
  regex = /[0-9]/;
  if (!regex.test(value)) {
    return i18n.t('PASSWORD_STRENGTH');
  }
  regex = /[A-Z]/;
  if (!regex.test(value)) {
    return i18n.t('PASSWORD_STRENGTH');
  }
  return null;
};

const isSameAsCurrentPassword = (newPassword, allValues) => {
  const currentPassword = get(allValues, 'currentPassword', '');
  if (newPassword === currentPassword) {
    return i18n.t('NEW_PASSWORD_EQUALITY');
  }
  return null;
};

const isSameAsNewPassword = (confirmPassword, allValues) => {
  const newPassword = get(allValues, 'newPassword', '');
  if (newPassword !== confirmPassword) {
    return i18n.t('CONFIRM_PASSWORD_NON_EQUALITY');
  }
  return null;
};

const noWhiteSpacesAllowed = (value) => {
  if (value && value.indexOf(' ') > -1) {
    return i18n.t('WHITESPACES_ARE_NOT_ALLOWED');
  }
  return null;
};

const characterRange = (val, min, max, formatter) => {
  if (!val) {
    return null;
  }

  let minCharacter = String.fromCharCode(min);
  let maxCharacter = String.fromCharCode(max);
  let valid = i18n.t('VALIDATION_ERROR_CHARACTER_VALUE_OUT_OF_RANGE', minCharacter, maxCharacter);

  if (formatter === 'hex') {
    minCharacter = parseInt(min, 10).toString(16).toUpperCase();
    maxCharacter = parseInt(max, 10).toString(16).toUpperCase();
    valid = i18n.t('VALIDATION_ERROR_CHARACTER_VALUE_OUT_OF_RANGE', '0x' + minCharacter, '0x' + maxCharacter);
  }

  if (val) {
    const decimalValue = val.charCodeAt(0);
    if (decimalValue >= min && decimalValue <= max) {
      valid = null;
    }
  }

  return valid;
};

const validatecustomEscapedCharacter = (value) => {
  if (value === undefined || value === '') {
    return null;
  }
  if (value && value.length === 0) {
    return null;
  }
  if (value && value.length > 4) {
    return 'CUSTOM_CHARACTER_MAX_LENGTH_ERROR';
  }
  if (value && value.length === 1) {
    return characterRange(value, 33, 126, 'hex');
  }
  if (value && value.length > 1) {
    const customEscArr = value.split('');
    let valid = null;
    for (let i = 0; i < customEscArr.length; i += 1) {
      valid = characterRange(customEscArr[i], 33, 126, 'hex');
      if (!isNull(valid)) {
        return valid;
      }
    }
    return valid;
  }

  return null;
};

const validateCloudCustomEscapedCharacter = (value) => {
  if (value === undefined || value === '') {
    return null;
  }
  if (value && value.length === 0) {
    return null;
  }
  if (value && value.length > 4) {
    return 'CUSTOM_CHARACTER_MAX_LENGTH_ERROR';
  }
  if (value && value.length === 1) {
    return characterRange(value, 33, 126, 'hex');
  }
  if (value && value.length > 1) {
    let customEscArr;
    if (!isArray(value)) {
      customEscArr = value.split('');
    } else {
      customEscArr = value;
    }
    let valid = null;
    for (let i = 0; i < customEscArr.length; i += 1) {
      valid = characterRange(customEscArr[i], 33, 126, 'hex');
      if (!isNull(valid)) {
        return valid;
      }
    }
  }
  return null;
};

const sameIP = (ipList) => {
  if (!Array.isArray(ipList) || ipList.length === 0) return 'INVALID_FIELD_ARRAY';
  if (!ipList[0]) return null;
  const first = ipList[0].split('/');
  const newList = ipList.slice(1);

  const isNotOk = newList && newList.some((item) => item
    && (
      (item.includes('/') && (item.split('/'))[0] === first[0])
      || (!item.includes('/') && (item === first[0]))));
  return isNotOk ? 'VALIDATION_ERROR_SAME_IP' : null;
};

const sameServiceIP = (ipList) => {
  if (!Array.isArray(ipList) || ipList.length === 0) return 'INVALID_FIELD_ARRAY';
  if (!ipList[0] || !ipList[0].includes('/')) return null;
  const first = ipList[0].split('/');
  const newList = ipList.slice(1);

  const isNotOk = newList && newList.some((item) => item && item.includes('/') && (item.split('/'))[0] === first[0]);
  return isNotOk ? 'VALIDATION_ERROR_SAME_SERVICE_IP' : null;
};

const sameServiceIPMask = (ipList) => {
  if (!Array.isArray(ipList) || ipList.length === 0) return 'INVALID_FIELD_ARRAY';
  if (!ipList[0] || !ipList[0].includes('/')) return null;
  const first = ipList[0].split('/');

  const isNotOk = ipList.some((item) => item && item.includes('/') && (item.split('/'))[1] !== first[1]);
  return isNotOk ? 'VALIDATION_ERROR_INVALID_SERVICE_IP_MASK' : null;
};

const isIPv4AddressInSubnet = (ipv4AddressOriginal, subNetmask) => {
  if (!ipv4AddressOriginal || ipAddressesOrRanges(ipv4AddressOriginal) || ipv4AddressOriginal === '0.0.0.0' || ipv4AddressOriginal === '0.0.0.0/0'
      || !subNetmask || ipAddressesOrRanges(subNetmask) || subNetmask === '0.0.0.0' || subNetmask === '0.0.0.0/0') {
    return null; // i18n.t('VALIDATION_ERROR_INVALID_IP');
  }

  const ipv4Address = ipv4AddressOriginal.includes('/') ? ipv4AddressOriginal.split('/')[0] : ipv4AddressOriginal;
  const ipAddressWithNetmask = subNetmask.includes('/') ? subNetmask.split('/') : [subNetmask, '0'];
  const ip = ipAddressWithNetmask[0];
  const netmaskBits = Math.max(0, Math.min(32, Number(ipAddressWithNetmask[1])));
  const blocks = ip.split('.', 4);
  /* Only allow values between 0-255 */
  
  const byte1 = Number.isNaN(blocks[0]) ? 0 : Math.max(0, Math.min(255, Number(blocks[0])));
  const byte2 = Number.isNaN(blocks[1]) ? 0 : Math.max(0, Math.min(255, Number(blocks[1])));
  const byte3 = Number.isNaN(blocks[2]) ? 0 : Math.max(0, Math.min(255, Number(blocks[2])));
  const byte4 = Number.isNaN(blocks[3]) ? 0 : Math.max(0, Math.min(255, Number(blocks[3])));

  const addressDotQuad = (byte1 + '.' + byte2 + '.' + byte3 + '.' + byte4);

  // addressDotQuad = addressDotQuad.toString();
  /* sanity check: valid values: = 0-32 */
  // netmaskBits = Math.max(0, Math.min(32, Number(netmaskBits)));
  const ipv4AddressInteger = ipv4DotQuadToInt(ipv4Address);
  const addressInteger = ipv4DotQuadToInt(addressDotQuad);
  // this.addressDotQuad  = ipv4IntToDotQuad( this.addressInteger );
  const addressBinStr = ipv4IntToBinStr(addressInteger);

  const netmaskBinStr = ipv4BitsNMtoBinStrNM(netmaskBits);
  // const netmaskInteger = ipv4BinStrtoInt(netmaskBinStr);
  
  const netaddressBinStr = ipv4SubnetToBinStr(addressBinStr, netmaskBinStr);
  const netbcastBinStr = ipv4SubnetCastToBinStr(addressBinStr, netmaskBinStr);
  const netaddressInteger = ipv4BinStrtoInt(netaddressBinStr);
  const netbcastInteger = ipv4BinStrtoInt(netbcastBinStr);
  // const netaddressDotQuad = ipv4IntToDotQuad(netaddressInteger);
  // const netmaskDotQuad = ipv4IntToDotQuad(netaddressInteger);
  // const netbcastDotQuad = ipv4IntToDotQuad(netbcastInteger);
  const isOk = (ipv4AddressInteger >= netaddressInteger && ipv4AddressInteger <= netbcastInteger);

  return isOk ? null : i18n.t('VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK');
};

const ipAddressNotEnding0or255AndMvp1Validation = (value) => {
  if (isEmpty(value)) return null;
  const noMask = value.split('/')[0];
  const breakNoMask = noMask.split('.');
  if (breakNoMask.length !== 4) return i18n.t('VALIDATION_ERROR_INVALID_IP');
  
  if (!isIPv4AddressInSubnet(noMask, '0.0.0.0/8')) return i18n.t('VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE');
  if (!isIPv4AddressInSubnet(noMask, '**********/10')) return i18n.t('VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE');
  if (!isIPv4AddressInSubnet(noMask, '*********/8')) return i18n.t('VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE');
  if (!isIPv4AddressInSubnet(noMask, '***********/16')) return i18n.t('VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE');
  if (!isIPv4AddressInSubnet(noMask, '240.0.0.0/4')) return i18n.t('VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE');

  if (Number(breakNoMask[0]) === 255 && Number(breakNoMask[1]) === 255 && Number(breakNoMask[2]) === 255 && Number(breakNoMask[3]) === 255) return i18n.t('VALIDATION_ERROR_INVALID_IP');

  return (Number(breakNoMask[3]) === 0 || Number(breakNoMask[3]) === 255) ? i18n.t('VALIDATION_ERROR_INVALID_IP') : null;
};

const ipAddressNotEnding0or255 = (value) => {
  if (isEmpty(value)) return null;
  const noMask = value.split('/')[0];
  const breakNoMask = noMask.split('.');
  if (breakNoMask.length !== 4) return i18n.t('VALIDATION_ERROR_INVALID_IP');

  if (Number(breakNoMask[0]) === 255 && Number(breakNoMask[1]) === 255 && Number(breakNoMask[2]) === 255 && Number(breakNoMask[3]) === 255) return i18n.t('VALIDATION_ERROR_INVALID_IP');

  return (Number(breakNoMask[3]) === 0 || Number(breakNoMask[3]) === 255) ? i18n.t('VALIDATION_ERROR_INVALID_IP') : null;
};

const validateUnitBatchSize = (val, allValues) => {
  const currentUnit = allValues && allValues.sizeUnit && allValues.sizeUnit.id ? allValues.sizeUnit.id : 'KB';
  const currentSiemType = allValues && allValues.siemType && allValues.siemType.id ? allValues.siemType.id : 'SPLUNK';
  if (val === undefined || val === '') {
    return null;
  }
  if (val && val.length === 0) {
    return null;
  }
  // eslint-disable-next-line eqeqeq
  if ((parseInt(val) == val && !/\./.test(val))) {
    if (currentUnit === 'MB') {
      if (currentSiemType === 'S3') {
        return range(val, 1, 128);
      } if (currentSiemType === 'AZURE_SENTINEL') {
        // eslint-disable-next-line eqeqeq
        if (val == 1) {
          return null;
        }
        return 'VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE';
      }
      return 'INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE';
    }
    if (currentSiemType === 'S3') {
      return range(val, 1024, 131072);
    } if (currentSiemType === 'AZURE_SENTINEL') {
      return range(val, 128, 1024);
    }
    return range(val, 16, 512);
  }
  
  return isInteger(val);
};

const validateAwsAccounts = (value) => {
  const exactLength12 = exactLength(12);
  let awsAccountsValidation = noWhiteSpacesAllowed(value);
  if (!awsAccountsValidation) awsAccountsValidation = isNumberOnly(value);
  if (!awsAccountsValidation) awsAccountsValidation = exactLength12(value);

  return awsAccountsValidation;
};

const isNotARestrictedCode = (value) => {
  // 2	time-offset	deprecated by option code 100 and 101
  // 3	routers	pre-defined option
  // 6	domain-name-servers	pre-defined option
  // 15	domain-name	pre-defined option
  // 18	extensions-path	server (internal) configuration
  // 50	dhcp-requested-address	DHCP client option
  // 51	dhcp-lease-time	DHCP client option
  // 52	dhcp-option-overload	not user configurable
  // 53	dhcp-message-type	not user configurable
  // 54	dhcp-server-identifier	not user configurable
  // 56	dhcp-message	DHCP client option
  // 81	fqdn	DHCP client option
  // 82	relay-agent-information	not user configurable
  // 91	client-last-transaction-time	not user configurable
  // 108	v6-only-preferred 	IPv6 specific option
  // 118	subnet-selection	not user configurable
  // 119	domain-search	pre-defined option
  // 212	option-6rd	IPv6 specific option

  const restrictedCodes = [2, 3, 6, 15, 18, 50, 51, 52, 53, 54, 56, 81, 82, 91, 108, 118, 119, 212];
  if (restrictedCodes.includes((parseInt(value, 10)))) {
    return 'SORRY_THIS_CODE_CAN_NOT_BE_USED';
  }
  
  return null;
};

export {
  awsRoleNAmeAddress,
  checkArraySize,
  exactLength,
  hasGoodPasswordStrength,
  ipAddress,
  ipAddresses,
  ipAddressesOrRanges,
  ipAddressesOrRangesList,
  ipAddressesOrRangesListWithPortAndProtocol,
  ipAddressesOrRangesOrFqdn,
  ipAddressesOrRangesOrFqdnList,
  ipAddressesOrRangesOrFqdnListNoWildCard,
  ipAddressesOrRangesOrFqdnNoWildCard,
  ipAddressesOrRangesWithPortAndProtocol,
  ipAddressNotEnding0or255,
  ipAddressNotEnding0or255AndMvp1Validation,
  ipAddressOnly,
  ipAddressOnlyOptional,
  ipAddressOrIpAddressWithNetmask,
  ipAddressRange,
  ipAddressWithNetmaskOnly,
  ipRangeregex,
  isApiKeyOK,
  isEmail,
  isEmailList,
  isInteger,
  isIPv4AddressInSubnet,
  isNotARestrictedCode,
  isNumberOnly,
  isSameAsCurrentPassword,
  isSameAsNewPassword,
  isSiemURL,
  isTenantURL,
  isURL,
  isURLHost,
  isValidName,
  isWebURL,
  looseurlAddressSchemeless,
  macAddress,
  maxLength,
  maxValueLimit,
  minValueLimit,
  notZero,
  noWhiteSpacesAllowed,
  portRange,
  pureFqdn,
  pureFqdnOrWildcard,
  range,
  required,
  requiredId,
  requireObjectProperty,
  sameIP,
  sameServiceIP,
  sameServiceIPMask,
  validateAwsAccounts,
  validateCloudCustomEscapedCharacter,
  validatecustomEscapedCharacter,
  validateFrom,
  validateIpFromTo,
  validateIpInsideAnotherRange,
  validateTo,
  validateUnitBatchSize,
};
