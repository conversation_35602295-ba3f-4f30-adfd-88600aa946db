export const swapObject = (obj, source, target) => {
  const objCopy = obj;
  const objSourceIndex = obj.findIndex((i) => i.key === source);
  const objTargetIndex = obj.findIndex((i) => i.key === target);
  objCopy.splice(
    objTargetIndex,
    0,
    objCopy.splice(objSourceIndex, 1)[0],
  );
  return objCopy;
};
  
export const sortRows = (rowValues, sortColumn, sortDirection) => (rows) => {
  const comparer = (a, b) => {
    if (sortDirection === 'ASC') {
      return a[sortColumn] > b[sortColumn] ? 1 : -1;
    } if (sortDirection === 'DESC') {
      return a[sortColumn] < b[sortColumn] ? 1 : -1;
    }
    return null;
  };
  return sortDirection === 'NONE' ? rowValues : [...rows].sort(comparer).map((x, idx) => ({ ...x, idx: idx + 1 }));
};
  
export const randomNextInt = (max) => Math.floor(Math.random() * Math.floor(max));
  
export const convertArrObj = (data) => {
  const obj = {};
  data.forEach((e) => { obj[e.id] = e.name; });
  return obj;
};
  
export const createArrObj = (data) => {
  const obj = {};
  data.forEach((e) => { obj[e] = e; });
  return obj;
};
