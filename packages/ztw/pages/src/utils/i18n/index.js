import i18n from 'i18next';
import moment from 'moment';
import 'moment/locale/de';
import 'moment/locale/es';
import 'moment/locale/fr';
import 'moment/locale/ja';
import 'moment/locale/zh-cn';
import { initReactI18next } from 'react-i18next';

// translated labels
import deDE from './de-DE';
import enUS from './en-US';
import esES from './es-ES';
import frFR from './fr-FR';
import jaJP from './ja-JP';
import zhCN from './zh-CN';

// Docs
// https://www.i18next.com/
// React https://react.i18next.com/
moment.locale(localStorage.getItem('i18nextLng') || localStorage.getItem('locale') || 'en-US');

i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources: {
      'de-DE': deDE,
      'en-US': enUS,
      'es-ES': esES,
      'fr-FR': frFR,
      'ja-JP': jaJP,
      'zh-CN': zhCN,
      'zh-TW': zhCN,
    },
    lng: localStorage.getItem('i18nextLng') || localStorage.getItem('locale'),
    fallbackLng: ['en-US'],
    load: 'currentOnly',
    keySeparator: false, // we do not use keys in form messages.welcome

    interpolation: {
      escapeValue: false, // react already safes from xss
    },
  });

export default i18n;
