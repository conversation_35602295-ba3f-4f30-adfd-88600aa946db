/* eslint-disable quotes */
/* eslint-disable quote-props */

// Notes: We can add phrases as a key, it does not have to be a key/constant.
// However, use key and constant if the phrase is a long sentence or paragraph.

// Please keep the translations below sorted for easy access and to avoid duplicates

// WIP - will keep this changes until we complete the csv2json conversion

export default {
  translation: {
    "1_HOUR": "1 Hora",
    "1_MONTH": "1 mes",
    "1_WEEK": "1 semana",
    "24_HOURS": "24 horas",
    "4_HOURS": "4 horas",
    "ACCEPT": "Aceptar",
    "ACCEPTED_ON": "Aceptado el",
    "ACCESS_TOKEN": "Token de acceso",
    "ACCOUNT_GROUP": "Grupo de cuentas",
    "ACCOUNT_ID_ONLY": "ID de cuenta",
    "ACCOUNT_ID": "ID de cuenta de AWS",
    "ACCOUNT_LIST": "Lista de cuentas",
    "ACCOUNT_NAME": "Nombre de la cuenta",
    "ACCOUNT": "Cuenta",
    "ACCOUNTS": "Cuentas",
    "ACTION_CAN_NOT_BE_UNDONE": "This action cannot be undone.",
    "ACTION_CAPS": "Acción",
    "ACTION_INTERFACE": "Interfaz",
    "ACTION_RESULT": "Resultado",
    "ACTION_TYPE": "Acción",
    "ACTION": "Acción",
    "ACTIONS": "Acciones",
    "ACTIVATE": "Activar",
    "ACTIVATION_FAILED": "¡Activación Fallida!",
    "ACTIVATION": "Activación",
    "ACTIVE_ACTIVE": "Activo-Activo",
    "ACTIVE_CONNECTION": "Conexión activa",
    "ACTIVE_STANDBY": "Activo-En espera",
    "ACTIVE_STATUS": "Estado activo",
    "ACTIVE": "Activo",
    "ADD_5G_DEPLOYMENT": "Añadir configuración de implementación",
    "ADD_ACCOUNT": "Añadir cuenta",
    "ADD_API_KEY": "Añadir clave de API de servicio en la nube",
    "ADD_AWS_ACCOUNT": "Añadir cuenta de AWS",
    "ADD_AWS_CLOUD_ACCOUNT": "Añadir cuenta de nube de AWS",
    "ADD_AWS_GROUP": "Añadir grupo de AWS",
    "ADD_AZURE_ACCOUNT": "Añadir cuenta de Azure",
    "ADD_AZURE_CLOUD_ACCOUNT": "Añadir cuenta de nube de Azure",
    "ADD_BC_PROVISIONING_TEMPLATE": "Añadir plantilla de configuración de Branch Connector",
    "ADD_BRANCH_CONNECTOR_PROV_TEMPLATE": "Añadir plantilla de configuración de Branch Connector",
    "ADD_CLOUD_APP_PROVIDER": "Añadir proveedor de aplicación de nube",
    "ADD_CLOUD_CONNECTOR_ADMIN": "Añadir administrador",
    "ADD_CLOUD_CONNECTOR_ROLE": "Añadir rol de administrador",
    "ADD_CLOUD_CONNECTOR": "Añadir Cloud Connector",
    "ADD_CLOUD_NSS_FEED": "Añadir feed NSS de nube",
    "ADD_CLOUD_PROVISIONING_TEMPLATE": "Añadir plantilla de aprovisionamiento de Cloud Connector",
    "ADD_CRITERIA": "Añadir criterios",
    "ADD_DEPLOYMENT_CONFIGURATION": "Añadir configuración de implementación",
    "ADD_DESTINATION_IP_GROUP": "Añadir grupo de IP de destino",
    "ADD_DNS_GATEWAY": "Añadir pasarela DNS",
    "ADD_DNS_POLICIES": "Añadir regla de filtrado de DNS",
    "ADD_DYNAMIC_VDI_GROUP": "Añadir grupo de VDI dinámico",
    "ADD_EC_NSS_CLOUD_FEED": "Añadir feed NSS de nube",
    "ADD_EC_NSS_FEED": "Añadir feed de NSS",
    "ADD_EC_NSS_SERVER": "Añadir servidor NSS",
    "ADD_EVENT_GRID": "Añadir Event Grid (opcional)",
    "ADD_FILTER": "Añadir filtro",
    "ADD_FILTERS": "Añadir filtros",
    "ADD_GROUP": "Añadir grupo",
    "ADD_HTTP_HEADER": "Añadir encabezado HTTP",
    "ADD_INTERFACE": "Añadir interfaz",
    "ADD_IP_INFO": "Añadir información de IP",
    "ADD_IP_POOL": "Añadir grupo de IP",
    "ADD_ITEMS": "Añadir elementos",
    "ADD_LOCATION_AND_CCG": "Añadir ubicación y Cloud Connectors",
    "ADD_LOCATION_TEMPLATE": "Añadir plantilla de ubicación",
    "ADD_LOG_AND_CONTROL_FORWARDING_RULE": "Añadir regla de reenvío de registro y control",
    "ADD_LOG_AND_CONTROL_FORWARDING": "Añadir reenvío de registro y control",
    "ADD_LOG_AND_CONTROL_GATEWAY": "Añadir pasarela de registro y control",
    "ADD_MORE": "Añadir más",
    "ADD_NETWORK_SERVICE_GROUP": "Añadir grupo de servicios de red",
    "ADD_NETWORK_SERVICE": "Añadir servicio de red",
    "ADD_NEW_GATEWAY": "Añadir nueva pasarela",
    "ADD_NEW": "Añadir nuevo",
    "ADD_NSS_FEED": "Añadir feed de NSS",
    "ADD_NSS_SERVER": "Añadir servidor NSS",
    "ADD_PORT": "Añadir puerto",
    "ADD_PROVISIONING_TEMPLATE": "Añadir plantilla de aprovisionamiento de Cloud Connector",
    "ADD_SOURCE_IP_GROUP": "Añadir grupo de IP de origen",
    "ADD_STORAGE_ACCOUNT": "Añadir cuenta de almacenamiento",
    "ADD_SUB_INTERFACE": "Añadir subinterfaz",
    "ADD_TENANT": "Añadir inquilino",
    "ADD_TO_A_LOCATION": "Añadir a una ubicación",
    "ADD_TO_AN_EXISTING_GROUP": "Añadir a un grupo existente",
    "ADD_TRAFFIC_FORWARDING_RULE": "Añadir regla de reenvío de tráfico",
    "ADD_TRAFFIC_FORWARDING": "Añadir reenvío de tráfico",
    "ADD_TRAFFIC_FWD_POLICIES": "Añadir reglas de reenvío de tráfico",
    "ADD_UPF": "Añadir función de plano de usuario",
    "ADD_VDI_AGENT_FORWARDING_PROFILE": "Añadir perfil de reenvío de VDI",
    "ADD_VDI_TEMPLATE": "Añadir plantilla VDI",
    "ADD_ZERO_TRUST_GATEWAY": "Añadir pasarela de Zero Trust",
    "ADD_ZIA_GATEWAY": "Añadir pasarela de ZIA",
    "ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128": "El límite de cuentas de AWS adicionales es de 128.",
    "ADDITIONAL_AWS_ACCOUNTS": "Cuentas de AWS adicionales",
    "ADDRESS_RANGES_SHOULD_NOT_OVERLAP": "Los rangos de direcciones no deben superponerse.",
    "ADM_ACTIVATING": "Activando",
    "ADM_ACTV_DONE": "activación de administrador realizada",
    "ADM_ACTV_FAIL": "activación de administrador fallida",
    "ADM_ACTV_QUEUED": "Activación en cola",
    "ADM_EDITING": "Edición",
    "ADM_EXPIRED": "la sesión de administrador ha caducado",
    "ADM_LOGGED_IN": "administrador con sesión iniciada",
    "ADMIN_ID": "ID de administrador",
    "ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE": "Este administrador también existe en el Portal de Administración para otro servicio. Se asociará a la misma cuenta de administrador en el otro Portal de Administración y el servicio Zscaler actualizará los cambios que se realicen en la otra cuenta de administrador, como por ejemplo el correo electrónico, el nombre, el ámbito, la contraseña, el estado y los comentarios. ¿Continuar?",
    "ADMIN_MANAGEMENT": "Gestión de Administradores",
    "ADMIN_ROLE": "Rol de Administrador",
    "ADMIN_SAML_PUBLICCERT_INVALID_EXTENSION": "El certificado público del SAML debería ser de formato .cer o .pem.",
    "ADMINISTRATION_CONFIGURATION": "Configuración de administración",
    "ADMINISTRATION_CONTROL": "Control de administración",
    "ADMINISTRATION": "Administración",
    "ADMINISTRATOR_ADMIN_USER": "Administrador",
    "ADMINISTRATOR_AUDITOR": "Auditor",
    "ADMINISTRATOR_MANAGEMENT": "Gestión de Administradores",
    "ADMINISTRATOR_PASSWORD_BASED_LOGIN": "Inicio de sesión basado en contraseña",
    "ADMINISTRATOR_ROLE": "Gestión de roles",
    "ADMINISTRATOR_SAML_CONFIGURE": "Autenticación SAML para administradores",
    "ADMINISTRATOR_SAML_ENABLED": "Activar autenticación SAML",
    "ADMINISTRATOR_SAML_METADATA": "Descargar metadatos XML",
    "ADMINISTRATOR_SAML": "SAML",
    "ADMINISTRATORS_MANAGEMENT": "Gestión de administradores",
    "ADMINISTRATORS": "Administradores",
    "ADSPYWARE_SITES": "Sitios de adware/spyware",
    "ADULT_SEX_EDUCATION": "Educación sexual para adultos",
    "ADULT_THEMES": "Temas para adultos",
    "ADVANCED_SETTINGS": "Ajustes Avanzados",
    "ADWARE_OR_SPYWARE": "Spyware/Adware",
    "AF_SOUTH_1": "África (Ciudad del Cabo)",
    "AF_SOUTH_1A": "af-south-1a",
    "AF_SOUTH_1B": "af-south-1b",
    "AF_SOUTH_1C": "af-south-1c",
    "AFGHANISTAN_ASIA_KABUL": "Asia/Kabul",
    "AFGHANISTAN": "Afganistán",
    "AGENT_STATUS": "Estado de agente",
    "AGGREGATE_LOGS": "Agregar registros",
    "AIAIGAME_DESC": " Este plug-in de protocolo clasifica el tráfico http al host aiaigame.com. ",
    "AIAIGAME": "AiAi Games",
    "AILI_DESC": " Sitio web chino de compra de moda",
    "AILI": "Aili",
    "AIM_DESC": " AIM (originalmente AOL Instant Messenger) es una aplicación de mensajería instantánea. El nombre del protocolo es OSCAR (Open System for CommunicAtion in Realtime) y se usa tanto para ICQ como para servicios AIM.",
    "AIM_EXPRESS_DESC": " AOL Instant Messaging Express soporta muchas de las funcionalidades de AIM pero no funcionalidades avanzadas como transferencia de ficheros, chat de audio o videoconferencia",
    "AIM_EXPRESS": "Aim_express",
    "AIM_TRANSFER_DESC": " AIM es un protocolo de mensajería instantánea",
    "AIM_TRANSFER": "Transferencia de Ficheros de AIM",
    "AIM": "AIM",
    "AIMEXPRESS": "AIM Express",
    "AIMINI_DESC": " Aimini es una solución online para almacenar, enviar y compartir archivos",
    "AIMINI": "Aimini",
    "AIMS_DESC": " AIMS es la versión segura de AIM",
    "AIMS": "AIMS",
    "AIOWRITE_THROTTLE": "aiowrite throttle. solo ca-ft usándolo hoy",
    "AIRAIM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host airaim.com. También clasifica el tráfico SSL a airaim.com",
    "AIRAIM": "Airaim",
    "ALAND_ISLANDS_EUROPE_MARIEHAMN": "Europa/Mariehamn",
    "ALAND_ISLANDS": "Aland Islands",
    "ALAND": "Aland",
    "ALBANIA_EUROPE_TIRANE": "Europa/Tirane",
    "ALBANIA": "Albania",
    "ALCOHOL_TOBACCO": "Alcohol / Tabaco",
    "ALGERIA_AFRICA_ALGIERS": "Africa / Argel",
    "ALGERIA": "Argelia",
    "ALL_VALUES": "Todos los valores",
    "ALL_ZSCALER_LOCATION_GROUPS": "Todos los grupos de ubicaciones de Zscaler",
    "ALL_ZSCALER_LOCATION_TYPES": "Todos los tipos de ubicaciones de Zscaler",
    "ALL_ZSCALER_LOCATIONS": "Todas las ubicaciones de Zscaler",
    "ALL_ZSCALER_NETWORK_SERVICE": "Todos los servicios de red de Zscaler",
    "ALL": "Todos",
    "ALLOW_TO_CREATE_NEW_LOCATION": "Permitir la creación de nuevas ubicaciones",
    "ALLOW": "Permitir",
    "ALLOWED_ACCOUNT_GROUPS": "Grupos de cuentas permitidos",
    "ALLOWED_ACCOUNTS_GROUPS": "Grupos de cuentas permitidos",
    "ALLOWED_ACCOUNTS": "Cuentas permitidas",
    "ALLOWED": "Permitido",
    "ALT_NEW_AGE": "Alt/New Age",
    "ALTERNATE_LIFESTYLE": "Estilo de vida",
    "AMAZON_WEB_SERVICES_CONSOLE": "Consola de Amazon Web Services",
    "AMAZON_WEB_SERVICES": "Amazon Web Services",
    "AMERICAN_SAMOA_PACIFIC_PAGO_PAGO": "Pacífico / PagoPago",
    "AMERICAN_SAMOA": "American Samoa",
    "AMF_IP_CIDR": "IP/CIDR de AMF",
    "AMF_NAME": "Nombre de AMF",
    "AMI_ID": "ID de AMI",
    "ANALYTICS": "Analítica",
    "ANDORRA_EUROPE_ANDORRA": "Europa/Andorra",
    "ANDORRA": "Andorra",
    "ANDROID_OS": "Android",
    "ANGOLA_AFRICA_LUANDA": "Africa/Luanda",
    "ANGOLA": "Angola",
    "ANGUILLA_AMERICA_ANGUILLA": "America/Anguilla",
    "ANGUILLA": "Anguilla",
    "ANONYMIZER": "P2P y anonimizador",
    "ANTARCTICA_CASEY": "Antartica / Casey",
    "ANTARCTICA_DAVIS": "Antartica / Davis",
    "ANTARCTICA_DUMONTDURVILLE": "Antartica / Dumont Durville",
    "ANTARCTICA_MAWSON": "Antartica / Mawson",
    "ANTARCTICA_MCMURDO": "Antartica / McMurdo",
    "ANTARCTICA_PALMER": "Antartica / Palmer",
    "ANTARCTICA_ROTHERA": "Antartica / Rothera",
    "ANTARCTICA_SOUTH_POLE": "Antartica / Polo Sur",
    "ANTARCTICA_SYOWA": "Antartica / Syowa",
    "ANTARCTICA_VOSTOK": "Antartica / Vostok",
    "ANTARCTICA": "Antártica",
    "ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA": "América / Antigua",
    "ANTIGUA_AND_BARBUDA": "Antigua y Barbuda",
    "ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS": "Cualquier IP no coincidente de grupos de IP de ZPA",
    "ANY_RULE": "Cualquiera",
    "ANY": "Cualquiera",
    "AP_EAST_1": "Asia Pacífico (Hong Kong)",
    "AP_EAST_1A": "ap-east-1a",
    "AP_EAST_1B": "ap-east-1b",
    "AP_EAST_1C": "ap-east-1c",
    "AP_NORTHEAST_1": "ap-northeast-1 (Tokio)",
    "AP_NORTHEAST_1A": "ap-northeast-1a",
    "AP_NORTHEAST_1C": "ap-northeast-1c",
    "AP_NORTHEAST_1D": "ap-northeast-1d",
    "AP_NORTHEAST_1E": "ap-northeast-1e",
    "AP_NORTHEAST_2": "ap-northeast-2 (Seúl)",
    "AP_NORTHEAST_2A": "ap-northeast-2a",
    "AP_NORTHEAST_2B": "ap-northeast-2b",
    "AP_NORTHEAST_2C": "ap-northeast-2c",
    "AP_NORTHEAST_3": "ap-northeast-3 (Osaka-Local)",
    "AP_NORTHEAST_3A": "ap-northeast-3a",
    "AP_SOUTH_1": "ap-south-1 (Bombay)",
    "AP_SOUTH_1A": "ap-south-1a",
    "AP_SOUTH_1B": "ap-south-1b",
    "AP_SOUTH_1C": "ap-south-1c",
    "AP_SOUTH_2": "Asia Pacífico (Hyderabad)",
    "AP_SOUTHEAST_1": "ap-southeast-1 (Singapur)",
    "AP_SOUTHEAST_1A": "ap-southeast-1a",
    "AP_SOUTHEAST_1B": "ap-southeast-1b",
    "AP_SOUTHEAST_1C": "ap-southeast-1c",
    "AP_SOUTHEAST_2": "ap-southeast-2 (Sídney)",
    "AP_SOUTHEAST_2A": "ap-southeast-2a",
    "AP_SOUTHEAST_2B": "ap-southeast-2b",
    "AP_SOUTHEAST_2C": "ap-southeast-2c",
    "AP_SOUTHEAST_3": "ap-southeast-3 (Yakarta)",
    "AP_SOUTHEAST_4": "Asia Pacífico (Melbourne)",
    "API_KEY_MANAGEMENT": "Gestión de clave de API",
    "API_KEY": "Clave de API",
    "APIKEY_MANAGEMENT": "Gestión de clave de API",
    "APP_CONNECTOR_DEPLOYMENT_STATUS": "Estado de implementación de App Connectors",
    "APP_CONNECTOR_DESCRIPTION": "Pueden aprovisionarse App Connectors como parte de esta plantilla. Rellene la siguiente información o continúe con el siguiente paso.",
    "APP_CONNECTOR_GROUP_NAME": "Nombre de grupo de App Connectors",
    "APP_CONNECTOR_GROUP_TYPE": "Tipo de grupo de App Connectors",
    "APP_CONNECTOR_GROUP": "Grupo de App Connectors",
    "APP_CONNECTOR_INTERFACE": "Interfaz de App Connector",
    "APP_CONNECTOR": "Conector de aplicación",
    "APPLIANCE_MANAGEMENT": "Gestión de dispositivos",
    "APPLIANCE_NAME": "Nombre de dispositivo",
    "APPLIANCE": "Dispositivo",
    "APPLIANCES": "Dispositivos",
    "APPLICABLE_FOR": "Aplicable a",
    "APPLICATION_ID": "ID de aplicación",
    "APPLICATION_KEY": "Clave de aplicación",
    "APPLICATION_SEGMENT": "Segmento de aplicación",
    "APPLICATION_SEGMENTS": "Application Segments",
    "APPLICATION_SERVICE_GROUPS": "Grupos de servicios de aplicaciones",
    "APPLICATION_VERSION": "Versión de la aplicación",
    "APPLICATIONS": "Aplicaciones",
    "APPLY_FILTER": "Aplicar filtro",
    "APPLY_FILTERS": "Aplicar filtros",
    "APPLY_TO_ALL_APP_SEGMENTS": "Aplicar a todos los App Segments",
    "APPLY": "Aplicar",
    "ARE_YOU_SURE_YOU_WANT_TO_PROCEED": "¿Está seguro de que desea continuar?",
    "ARE_YOU_SURE": "¿Está seguro de que desea continuar?",
    "ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES": "America/Argentina/Buenos Aires",
    "ARGENTINA_AMERICA_ARGENTINA_CATAMARCA": "America/Argentina/Catamarca",
    "ARGENTINA_AMERICA_ARGENTINA_CORDOBA": "America/Argentina/Cordoba",
    "ARGENTINA_AMERICA_ARGENTINA_JUJUY": "America/Argentina/Jujuy",
    "ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA": "America/Argentina/La Rioja",
    "ARGENTINA_AMERICA_ARGENTINA_MENDOZA": "America/Argentina/Mendoza",
    "ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS": "America/Argentina/Rio Gallegos",
    "ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN": "America/Argentina/San Juan",
    "ARGENTINA_AMERICA_ARGENTINA_TUCUMAN": "America/Argentina/Tucuman",
    "ARGENTINA_AMERICA_ARGENTINA_USHUAIA": "America/Argentina/Ushuaia",
    "ARGENTINA": "Argentina",
    "ARMENIA_ASIA_YEREVAN": "Asia/Yerevan",
    "ARMENIA": "Armenia",
    "ART_CULTURE": "Arte / Cultura",
    "ARUBA_AMERICA_ARUBA": "America/Aruba",
    "ARUBA": "Aruba",
    "ASIA_EAST1_A": "asia-east1-a",
    "ASIA_EAST1_B": "asia-east1-b",
    "ASIA_EAST1_C": "asia-east1-c",
    "ASIA_EAST1": "asia-east1",
    "ASIA_EAST2_A": "asia-east2-a",
    "ASIA_EAST2_B": "asia-east2-b",
    "ASIA_EAST2_C": "asia-east2-c",
    "ASIA_EAST2": "asia-east2",
    "ASIA_NORTHEAST1_A": "asia-northeast1-a",
    "ASIA_NORTHEAST1_B": "asia-northeast1-b",
    "ASIA_NORTHEAST1_C": "asia-northeast1-c",
    "ASIA_NORTHEAST1": "asia-northeast1",
    "ASIA_NORTHEAST2_A": "asia-northeast2-a",
    "ASIA_NORTHEAST2_B": "asia-northeast2-b",
    "ASIA_NORTHEAST2_C": "asia-northeast2-c",
    "ASIA_NORTHEAST2": "asia-northeast2",
    "ASIA_NORTHEAST3_A": "asia-northeast3-a",
    "ASIA_NORTHEAST3_B": "asia-northeast3-b",
    "ASIA_NORTHEAST3_C": "asia-northeast3-c",
    "ASIA_NORTHEAST3": "asia-northeast3",
    "ASIA_SOUTH1_A": "asia-south1-a",
    "ASIA_SOUTH1_B": "asia-south1-b",
    "ASIA_SOUTH1_C": "asia-south1-c",
    "ASIA_SOUTH1": "asia-south1",
    "ASIA_SOUTH2_A": "asia-south2-a",
    "ASIA_SOUTH2_B": "asia-south2-b",
    "ASIA_SOUTH2_C": "asia-south2-c",
    "ASIA_SOUTH2": "asia-south2",
    "ASIA_SOUTHEAST1_A": "asia-southeast1-a",
    "ASIA_SOUTHEAST1_B": "asia-southeast1-b",
    "ASIA_SOUTHEAST1_C": "asia-southeast1-c",
    "ASIA_SOUTHEAST1": "asia-southeast1",
    "ASIA_SOUTHEAST2_A": "asia-southeast2-a",
    "ASIA_SOUTHEAST2_B": "asia-southeast2-b",
    "ASIA_SOUTHEAST2_C": "asia-southeast2-c",
    "ASIA_SOUTHEAST2": "asia-southeast2",
    "ASIA": "Asia",
    "ASIAPACIFIC": "Asia y Pacífico",
    "AT": "a las",
    "ATTRIBUTES_APPLICABLE_ONLY_TO_KNOWN_HOPS": "Estos atributos solo son aplicables a saltos conocidos.",
    "ATTRIBUTES": "Atributos",
    "AUDIT_LOGS": "Registro de auditoría",
    "AUDIT_OPERATION": "Operación de auditoría",
    "AUSTRALIA_ADELAIDE": "Australia/Adelaide",
    "AUSTRALIA_BRISBANE": "Australia/Brisbane",
    "AUSTRALIA_BROKEN_HILL": "Australia/Broken Hill",
    "AUSTRALIA_CURRIE": "Australia/Currie",
    "AUSTRALIA_DARWIN": "Australia/Darwin",
    "AUSTRALIA_EUCLA": "Australia/Eucla",
    "AUSTRALIA_HOBART": "Australia/Hobart",
    "AUSTRALIA_LINDEMAN": "Australia/Lindeman",
    "AUSTRALIA_LORD_HOWE": "Australia/LordHowe",
    "AUSTRALIA_MELBOURNE": "Australia/Melbourne",
    "AUSTRALIA_NEWZEALAND": "Australia y Nueva Zelanda",
    "AUSTRALIA_PERTH": "Australia/Perth",
    "AUSTRALIA_SOUTHEAST1_A": "australia-southeast1-a",
    "AUSTRALIA_SOUTHEAST1_B": "australia-southeast1-b",
    "AUSTRALIA_SOUTHEAST1_C": "australia-southeast1-c",
    "AUSTRALIA_SOUTHEAST1": "australia-southeast1",
    "AUSTRALIA_SOUTHEAST2_A": "australia-southeast2-a",
    "AUSTRALIA_SOUTHEAST2_B": "australia-southeast2-b",
    "AUSTRALIA_SOUTHEAST2_C": "australia-southeast2-c",
    "AUSTRALIA_SOUTHEAST2": "australia-southeast2",
    "AUSTRALIA_SYDNEY": "Australia/Sydney",
    "AUSTRALIA": "Australia",
    "AUSTRALIACENTRAL": "(Asia Pacífico) Centro de Australia",
    "AUSTRALIACENTRAL2": "(Asia Pacífico) Centro de Australia 2",
    "AUSTRALIAEAST": "(Asia Pacífico) Este de Australia",
    "AUSTRALIASOUTHEAST": "(Asia Pacífico) Sureste de Australia",
    "AUSTRIA_EUROPE_VIENNA": "Europa/Viena",
    "AUSTRIA": "Austria",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_AUP_ENABLED": "La autenticación requerida debe desactivarse cuando está habilitada AUP.",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_CAUTION_ENABLED": "La autenticación requerida debe desactivarse cuando está habilitada Precaución.",
    "AUTH_TYPE": "Tipo de autenticación",
    "AUTHENTICATION_CONFIGURATION": "Configuración de autenticación",
    "AUTHENTICATION_FAILED": "Autenticación fallida",
    "AUTO_POPULATE_DNS_CACHE": "Rellenar automáticamente la caché de DNS",
    "AUTO_REFRESH_DASHBOARD": "Actualizar automáticamente el panel de control",
    "AUTO_SCALING_OPTIONS": "Opciones de escala automática",
    "AUTO_SCALING": "Escala automática",
    "AUTO": "Auto",
    "AUTOMATIC_MANAGEMENT_IP": "IP de gestión automática",
    "AUTOMATIC_SERVICE_IP": "IP de servicio automático",
    "AUTOMATIC": "Automático",
    "Availability Zone": "Zona de disponibilidad",
    "AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES": "El ID de zonas de disponibilidad debe tener un mínimo de 2 zonas.",
    "AVAILABILITY_ZONE_ID": "ID de zonas de disponibilidad",
    "AVAILABILITY_ZONE": "Zona de disponibilidad",
    "AVAILABILITY_ZONES": "Zonas de disponibilidad",
    "AVAILABLE": "Disponible",
    "AVERAGE": "Media",
    "AWS_ACCESS_KEY_ID": "ID de clave de acceso a AWS",
    "AWS_ACCOUNT_ID": "ID de cuenta de AWS",
    "AWS_ACCOUNT": "Cuenta de AWS",
    "AWS_AVAILABILITY_ZONE": "Zona de disponibilidad de AWS",
    "AWS_CLOUD_FORMATION": "AWS CloudFormation",
    "AWS_CLOUD": "AWS",
    "AWS_GROUP": "Grupo de AWS",
    "AWS_REGION": "Región de AWS",
    "AWS_REGIONS": "Regiones de AWS",
    "AWS_ROLE_NAME": "Nombre de rol de AWS",
    "AWS_SECRET_ACCESS_KEY": "Clave secreta de acceso a AWS",
    "AWS": "Amazon Web Services",
    "AZERBAIJAN_ASIA_BAKU": "Asia/Baku",
    "AZERBAIJAN": "Azerbaijan",
    "AZURE_ACCOUNT": "Cuenta de Azure",
    "AZURE_AVAILABILITY_ZONE": "Zona de disponibilidad de Azure",
    "AZURE_CLOUD": "Azure",
    "AZURE_REGION": "Región de Azure",
    "AZURE_RESOURCES": "Recursos de Azure",
    "AZURE_SENTINEL": "Azure Sentinel",
    "AZURE": "Azure",
    "BACK": "Volver",
    "BAHAMAS_AMERICA_NASSAU": "America/Nassau",
    "BAHAMAS": "Bahamas",
    "BAHRAIN_ASIA_BAHRAIN": "Asia/Bahrain",
    "BAHRAIN": "Bahrain",
    "BAIDUYUNDNS_DESC": " Actividad de túnel DNS detectada en .baiduyundns.com",
    "BAIDUYUNDNS": "BaiduYunDns",
    "BALANCED_RULE": "Equilibrado",
    "BALANCED": "Equilibrado",
    "BANDWIDTH_CONTROL_ENABLED": "Las ubicaciones secundarias comparten el valor de ancho de banda asignado a esta ubicación",
    "BANDWIDTH_CONTROL": "Control de ancho de banda",
    "BANGLADESH_ASIA_DHAKA": "Asia/Dhaka",
    "BANGLADESH": "Bangladesh",
    "BARBADOS_AMERICA_BARBADOS": "America/Barbados",
    "BARBADOS": "Barbados",
    "BASE_URL": "URL base",
    "BC_APP_CONNECTOR": "Branch Connector y App Connector",
    "BC_CONNECTOR_GROUP": "Grupo de Cloud & Branch Connectors",
    "BC_CONNECTOR_LOCATION": "Ubicación de Cloud & Branch Connector",
    "BC_CONNECTOR_VM": "VM de conector de sucursal",
    "BC_CONNECTOR": "Conector de sucursal",
    "BC_DESCRIPTION": "Descripción de conector de sucursal",
    "BC_DETAILS": "Detalles de conector de sucursal",
    "BC_DEVICE_GROUP": "Grupo de dispositivos Branch Connector",
    "BC_GENERAL_INFORMATION_DESCRIPTION": "Configure una plantilla de aprovisionamiento de Branch Connector en Zscaler Cloud & Branch Connector Admin Portal para implementar un Branch Connector como máquina virtual en su cuenta de sucursal o centro de datos. Para más información, consulte el documento {0}Para empezar{1}.",
    "BC_GROUP_DETAILS": "Detalles de grupo de conectores de sucursal",
    "BC_GROUP_NAME": "Nombre de grupo de conectores de sucursal",
    "BC_GROUP_TYPE": "Tipo de grupo de Branch Connectors",
    "BC_GROUP": "Grupo de conectores de sucursal",
    "BC_IMAGES_DESCRIPTION": "Para implementar Branch Connector o Branch Connector + App Connectors mediante Terraform, visite {0}GitHub{1}. Para obtener más información, consulte el {2}Portal de ayuda de Zscaler.{3}",
    "BC_IMAGES_DETAIL1": "Las imágenes de Branch Connector incluyen App Connectors que aún no se han aprovisionado. Para implementar Branch Connector y App Connectors, debe seleccionar la instancia combinada y configurar las propiedades de implementación según corresponda.",
    "BC_IMAGES_DETAIL2": "Para obtener más información, consulte {0}Administración de implementación para dispositivos virtuales{1}.",
    "BC_VM_SIZE": "Tamaño de VM de conector de sucursal",
    "BELARUS_EUROPE_MINSK": "Europa/Minsk",
    "BELARUS": "Bielorrusia",
    "BELGIUM_EUROPE_BRUSSELS": "Europa/Bruselas",
    "BELGIUM": "Belgium",
    "BELIZE_AMERICA_BELIZE": "América / Belize",
    "BELIZE": "Belize",
    "BENIN_AFRICA_PORTO_NOVO": "Africa/Porto-Novo",
    "BENIN": "Benin",
    "BERMUDA_ATLANTIC_BERMUDA": "Atlantic/Bermuda",
    "BERMUDA": "Bermuda",
    "BEST_LINK": "Mejor enlace",
    "BEST": "Mejor enlace",
    "BESTLINK_RULE": "Mejor enlace",
    "BHUTAN_ASIA_THIMPHU": "Asia/Thimphu",
    "BHUTAN": "Bhutan",
    "BLACKLIST_LOOKUP_RESULTS": "Resultados de la búsqueda en la lista de denegación",
    "BLACKLISTED_IP_CHECK": "Comprobación de IP en la lista de denegación",
    "BLOCK_INTERNET_ACCESS": "Bloquear acceso a Internet",
    "BLOCK": "Bloquear",
    "BLOG": "Blogs",
    "BLUEJEANS": "BlueJeans",
    "BOLIVIA_AMERICA_LA_PAZ": "America/La Paz",
    "BOLIVIA": "Bolivia",
    "BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO": "Europa/Sarajevo",
    "BOSNIA_AND_HERZEGOVINA": "Bosnia y Herzegovina",
    "BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO": "Europa/Sarajevo",
    "BOSNIA_AND_HERZEGOWINA": "Bosnia y Herzegovina",
    "BOTH_REQ_RESP_ALLOW": "Permitir",
    "BOTH_SESSION_AND_AGGREGATE_LOGS": "Registros de sesión y agregación",
    "BOTNET": "Botnet",
    "BOTSWANA_AFRICA_GABORONE": "Africa/Gaborone",
    "BOTSWANA": "Botswana",
    "BRANCH_AND_CLOUD_CONNECTOR_GROUP_NAME": "Nombre de grupo de Cloud & Branch Connectors",
    "BRANCH_AND_CLOUD_CONNECTOR_MONITORING": "Monitorización de Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_CONNECTOR": "Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_MONITORING": "Monitorización de Cloud & Branch",
    "BRANCH_CLOUD_CONNECTOR_GROUP": "Grupos de Cloud & Branch Connectors",
    "BRANCH_CONFIGURATION": "Configuración de sucursal",
    "BRANCH_CONNECTOR_GROUP": "Grupo de conectores de sucursal",
    "BRANCH_CONNECTOR_GROUPS": "Grupos de conectores de sucursal",
    "BRANCH_CONNECTOR_IMAGES": "Imágenes de Branch Connector",
    "BRANCH_CONNECTOR_INFORMATION": "Información de conector",
    "BRANCH_CONNECTOR_LOCS": "Ubicaciones de conectores de sucursal",
    "BRANCH_CONNECTOR_MONITORING": "Supervisión de conectores de sucursal",
    "BRANCH_CONNECTOR": "Conector de sucursal",
    "BRANCH_DEVICES": "Dispositivos de sucursal",
    "BRANCH_MANAGEMENT": "Gestión de sucursal",
    "BRANCH_PROVISIONING": "Aprovisionamiento de sucursal",
    "BRANCH_TYPE": "Tipo de sucursal",
    "BRAZIL_AMERICA_ARAGUAINA": "América/Araguaina",
    "BRAZIL_AMERICA_BAHIA": "América/Bahía",
    "BRAZIL_AMERICA_BELEM": "América/Belem",
    "BRAZIL_AMERICA_BOA_VISTA": "América/Boa Vista",
    "BRAZIL_AMERICA_CAMPO_GRANDE": "América/Campo Grande",
    "BRAZIL_AMERICA_CUIABA": "América/Culaba",
    "BRAZIL_AMERICA_EIRUNEPE": "América/Eirunepe",
    "BRAZIL_AMERICA_FORTALEZA": "América/Fortaleza",
    "BRAZIL_AMERICA_MACEIO": "América/Maceio",
    "BRAZIL_AMERICA_MANAUS": "América/Manaus",
    "BRAZIL_AMERICA_NORONHA": "América/Noronha",
    "BRAZIL_AMERICA_PORTO_VELHO": "América/Porto Velho",
    "BRAZIL_AMERICA_RECIFE": "América/Recife",
    "BRAZIL_AMERICA_RIO_BRANCO": "América/Rio Branco",
    "BRAZIL_AMERICA_SAO_PAULO": "América/Sao Paulo",
    "BRAZIL": "Brasil",
    "BRAZILSOUTH": "(Sudamérica) Sur de Brasil",
    "BRAZILSOUTHEAST": "(Sudamérica) Sudeste de Brasil",
    "BRAZILUS": "(Sudamérica) Brasil, EE. UU.",
    "BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS": "Indian/Chagos",
    "BRITISH_INDIAN_OCEAN_TERRITORY": "British Indian Ocean Territory",
    "BRITISH_VIRGIN_ISLANDS": "Islas Vírgenes Británicas",
    "BROWSER_EXPLOIT": "Vulnerabilidades de Navegador",
    "BRUNEI_DARUSSALAM_ASIA_BRUNEI": "Asia/Brunei",
    "BRUNEI_DARUSSALAM": "Brunei Darussalam",
    "BRUNEI": "Brunéi",
    "BULGARIA_EUROPE_SOFIA": "Europa/Sofia",
    "BULGARIA": "Bulgaria",
    "BURKINA_FASO_AFRICA_OUAGADOUGOU": "Africa/Ouagadougou",
    "BURKINA_FASO": "Burkina Faso",
    "BURUNDI_AFRICA_BUJUMBURA": "Africa/Bujumbura",
    "BURUNDI": "Burundi",
    "BW_DOWNLOAD": "Descarga (Mbps)",
    "BW_UPLOAD": "Subida (Mbps)",
    "BYTES": "Bytes",
    "CA_CENTRAL_1": "Canadá (Central)",
    "CA_CENTRAL_1A": "ca-central-1a",
    "CA_CENTRAL_1B": "ca-central-1b",
    "CA_CENTRAL_1D": "ca-central-1d",
    "CA_INACTIVE": "Fallo de supervisión de estado. El sistema ha estado caído demasiado tiempo.",
    "CABO_VERDE": "Cabo Verde",
    "CAMBODIA_ASIA_PHNOM_PENH": "Asia/Phnom Penh",
    "CAMBODIA": "Camboya",
    "CAMEROON_AFRICA_DOUALA": "Africa/Douala",
    "CAMEROON": "Camerún",
    "CANADA_AMERICA_ATIKOKAN": "America/Atikokan",
    "CANADA_AMERICA_BLANC_SABLON": "America/Blanc-Sablon",
    "CANADA_AMERICA_CAMBRIDGE_BAY": "America/Cambridge Bay",
    "CANADA_AMERICA_DAWSON_CREEK": "America/Dawson Creek",
    "CANADA_AMERICA_DAWSON": "America/Dawson",
    "CANADA_AMERICA_EDMONTON": "America/Edmonton",
    "CANADA_AMERICA_GLACE_BAY": "America/Glace Bay",
    "CANADA_AMERICA_GOOSE_BAY": "America/Goose Bay",
    "CANADA_AMERICA_HALIFAX": "America/Halifax",
    "CANADA_AMERICA_INUVIK": "America/Inuvik",
    "CANADA_AMERICA_IQALUIT": "America/Iqaluit",
    "CANADA_AMERICA_MONCTON": "America/Moncton",
    "CANADA_AMERICA_MONTREAL": "America/Montreal",
    "CANADA_AMERICA_NIPIGON": "America/Nipigon",
    "CANADA_AMERICA_PANGNIRTUNG": "America/Pangnirtung",
    "CANADA_AMERICA_RAINY_RIVER": "America/Rainy River",
    "CANADA_AMERICA_RANKIN_INLET": "America/Rankin Inlet",
    "CANADA_AMERICA_REGINA": "America/Regina",
    "CANADA_AMERICA_RESOLUTE": "America/Resolute",
    "CANADA_AMERICA_ST_JOHNS": "America/St. Johns",
    "CANADA_AMERICA_SWIFT_CURRENT": "America/Swift Current",
    "CANADA_AMERICA_THUNDER_BAY": "America/Thunder Bay",
    "CANADA_AMERICA_TORONTO": "America/Toronto",
    "CANADA_AMERICA_VANCOUVER": "America/Vancouver",
    "CANADA_AMERICA_WHITEHORSE": "America/Whitehorse",
    "CANADA_AMERICA_WINNIPEG": "America/Winnipeg",
    "CANADA_AMERICA_YELLOWKNIFE": "America/Yellowknife",
    "CANADA": "Canada",
    "CANADACENTRAL": "(Canadá) Centro de Canadá",
    "CANADAEAST": "(Canadá) Este de Canadá",
    "CANCEL_SUCCESS_MESSAGE": "Se han cancelado todos los cambios.",
    "CANCEL": "Cancelar",
    "CAPE_VERDE_ATLANTIC_CAPE_VERDE": "Atlántico / Cabo Verde",
    "CAPE_VERDE": "Cabo Verde",
    "CATEGORIES": "Categorías ",
    "CATEGORY": "Categoría",
    "CAYMAN_ISLANDS_AMERICA_CAYMAN": "America/Cayman",
    "CAYMAN_ISLANDS": "Cayman Islands",
    "CC_ADMIN": "Administrador de conector de nube",
    "CC_BC_DETAILS": "Detalles de Cloud & Branch Connector",
    "CC_CLOUD_PROVIDER_DESCRIPTION": "Seleccione uno de los siguientes proveedores de nube.",
    "CC_DETAILS": "Detalles de Cloud Connector",
    "CC_ENABLE_XFF_FORWARDING": "Utilizar XFF de solicitud de cliente",
    "CC_GENERAL_INFORMATION_DESCRIPTION": "Configure una plantilla de aprovisionamiento en la nube dentro de Zscaler Cloud & Branch Connector Admin Portal para implementar Cloud Connector como máquina virtual con Amazon Web Services (AWS), Google Cloud Platform (GCP) o Microsoft Azure. Para obtener más información, consulte {0}Acerca de las plantillas de aprovisionamiento en la nube{1}.",
    "CC_GROUP_NAME": "Nombre de grupo de Cloud Connector",
    "CC_GROUP": "Grupo de CC",
    "CC_INSTANCE": "Instancia de CC",
    "CC_LOCATION": "Ubicación de Cloud Connector",
    "CC_NW": "RED DE CLOUD CONNECTORS",
    "CC_ROLE_NAME": "Nombre de rol de Cloud Connector",
    "CC_SOURCE_IP": "IP de origen de CC",
    "CC_SOURCE_PORT": "Puerto de origen de CC",
    "CC_STATUS": "Estado de Cloud Connector",
    "CC_VERSION": "Versión de Cloud Connector",
    "CC_VM_NAME": "Nombre de VM de Cloud Connector",
    "CC_VM": "VM de CC",
    "CCA_DEVICE_GROUP": "Grupo de dispositivos de VDI",
    "CCA_DEVICE": "Dispositivo de VDI",
    "CCA_FWD_PROFILE": "Perfil de reenvío de VDI",
    "CCA_TEMPLATE_APIKEY": "Clave de API de plantilla de VDI",
    "CCA_TEMPLATE_KEY": "Clave de plantilla de VDI",
    "CCA_TEMPLATE": "Plantilla de VDI",
    "CDN": "CDN",
    "CELLULAR_CONFIGURATION_MODE_CORE": "Implementación dividida - Core",
    "CELLULAR_CONFIGURATION_MODE_EDGE": "Implementación dividida - Edge",
    "CELLULAR_CONFIGURATION_MODE_EDGEONLY": "Solo Edge",
    "CELLULAR_CONFIGURATION_MODE": "Modo de configuración de móvil",
    "CELLULAR_CONFIGURATION_SELECTION": "Selección de configuración móvil",
    "CELLULAR_CONFIGURATION": "Configuración de móvil",
    "CELLULAR_DEPLOYMENT_CONFIGURATION": "Configuración de implementación",
    "CELLULAR": "Móvil",
    "CENTOS": "CentOS",
    "CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI": "Africa/Bangui",
    "CENTRAL_AFRICAN_REPUBLIC": "Central African Republic",
    "CENTRALINDIA": "(Asia Pacífico) Centro de India",
    "CENTRALUS": "(EE. UU.) Centro de EE. UU.",
    "CENTRALUSEUAP": "(EE. UU.) Centro de EE. UU. - EUAP",
    "CENTRALUSSTAGE": "(EE. UU.) Centro de EE. UU. (Stage)",
    "CF_ADD_ON_GWLB_TEMPLATE": "Plantilla complementaria con equilibrador de carga de pasarela (GWLB)",
    "CF_CUSTOM_DEPLOYMENT_TEMPLATE": "Plantilla de despliegue personalizada",
    "CF_DEFAULT_DEPLOYMENT_TEMPLATE": "Plantilla de implementación inicial",
    "CF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Plantilla complementaria con ZPA",
    "CF_PRE_DEPLOYMENT_TEMPLATE": "Plantilla de implementación previa",
    "CHAD_AFRICA_NDJAMENA": "Africa/Ndjamena",
    "CHAD": "Chad",
    "CHANGE_PASSWORD_REMINDER": "Recordatorio de cambio de contraseña",
    "CHANGE_PASSWORD_WINDOW": "Debe cambiar su contraseña cada",
    "CHANGE_PASSWORD": "Cambiar contraseña",
    "CHECK_BLACKLIST": "Comprobar lista de denegación",
    "CHILE_AMERICA_SANTIAGO": "America/Santiago",
    "CHILE_PACIFIC_EASTER": "Pacific/Easter",
    "CHILE": "Chile",
    "CHINA_ASIA_CHONGQING": "Asia/Chongqing",
    "CHINA_ASIA_HARBIN": "Asia/Harbin",
    "CHINA_ASIA_KASHGAR": "Asia/Kashgar",
    "CHINA_ASIA_SHANGHAI": "Asia/Shanghai",
    "CHINA_ASIA_URUMQI": "Asia/Urumqi",
    "CHINA": "China",
    "CHINAEAST": "(Asia Pacífico) Este de China",
    "CHINAEAST2": "(Asia Pacífico) Este de China 2",
    "CHINAEAST3": "(Asia Pacífico) Este de China 3",
    "CHINANORTH": "(Asia Pacífico) Norte de China",
    "CHINANORTH2": "(Asia Pacífico) Norte de China 2",
    "CHINANORTH3": "(Asia Pacífico) Norte de China 3",
    "CHOOSE_EXISTING_LOCATION": "Elegir ubicación existente",
    "CHOOSE_TO_RECEIVE_UPDATES": "ELIJA ESTA OPCIÓN PARA RECIBIR ACTUALIZACIONES",
    "CHRISTMAS_ISLAND_INDIAN_CHRISTMAS": "Indian/Christmas",
    "CHRISTMAS_ISLAND": "Christmas Island",
    "CHROME_OS": "Chrome",
    "CIPHER_PROTOCOL": "Protocolo de cifrado",
    "CIPHER": "Cifrado",
    "CITY_STATE_PROVINCE_OPTIONAL": "Ciudad, Estado, Provincia (Opcional)",
    "CITY": "City",
    "CLASSIFIEDS": "Anuncios Clasificados",
    "CLEAR_ALL": "Borrar todo",
    "CLEAR_FILTERS": "Borrar filtros",
    "CLEAR_SEARCH_AND_SORT": "Borrar búsqueda y clasificación",
    "CLICK_FOR_MORE_INFO": "Haga clic para obtener más información",
    "CLICK_HERE_TO_ACCEPT_EUSA": "Haga clic aquí para aceptar el acuerdo EUSA pendiente",
    "CLIENT_CONNECTOR_FOR_VDI": "Client Connector para VDI",
    "CLIENT_DEST_NAME": "Nombre de destino de cliente",
    "CLIENT_DESTINATION_IP": "IP de destino de cliente",
    "CLIENT_DESTINATION_PORT": "Puerto de destino de cliente",
    "CLIENT_ID": "ID de cliente",
    "CLIENT_IP": "IP de cliente",
    "CLIENT_NETWORK_PROTOCOL": "Protocolo NW del cliente",
    "CLIENT_SECRET": "Secreto de cliente",
    "CLIENT_SOURCE_IP": "IP de origen de cliente",
    "CLIENT_SOURCE_PORT": "Puerto de origen de cliente",
    "CLOSE": "Cerrar",
    "CLOUD_ACCOUNT": "Cuenta de nube",
    "CLOUD_AUTOMATION_SCRIPTS": "Scripts de automatización de nube",
    "CLOUD_CONFIG_REQUIREMENTS": "Requisitos de configuración de nube",
    "CLOUD_CONFIGURATION": "Configuración de nube",
    "CLOUD_CONNECTOR_CONFIGURATION_NOT_APPLICABLE": "Este modo de configuración no es aplicable a Cloud Connector",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_OPTIONAL": "Grupos y espacio de nombres",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_TEXT": "Opcionalmente, seleccione de la lista los grupos de Cloud Connectors que desea incluir en este grupo de suscripciones. Introduzca un espacio de nombres que le permita reutilizar la misma suscripción en diferentes cuentas.",
    "CLOUD_CONNECTOR_GROUP_CREATION": "Creación de grupo de Cloud Connector",
    "CLOUD_CONNECTOR_GROUP": "Grupo de conectores de nube",
    "CLOUD_CONNECTOR_GROUPS": "Grupos de Cloud Connectors",
    "CLOUD_CONNECTOR_INFORMATION": "Información de conector",
    "CLOUD_CONNECTOR_INSTANCE_ROLE_NAME": "Nombre de rol de instancia de Cloud Connector ",
    "CLOUD_CONNECTOR_MANAGEMENT": "Gestión de conectores de nube",
    "CLOUD_CONNECTOR_MONITORING": "Supervisión de Cloud Connector",
    "CLOUD_CONNECTOR_NAME": "Nombre de Cloud Connector",
    "CLOUD_CONNECTOR_PROVISIONING": "Aprovisionamiento de conectores de nube",
    "CLOUD_CONNECTOR_TRAFFIC_FLOW": "Flujo de tráfico de Cloud Connector",
    "CLOUD_CONNECTOR": "Cloud Connector",
    "CLOUD_CONNECTORS_GROUP_AND_NAMESPACE": "Grupos y espacio de nombres",
    "CLOUD_CONNECTORS": "Cloud Connectors",
    "CLOUD_FORMATION": "CloudFormation",
    "CLOUD_MANAGEMENT": "Gestión de nube",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_FAILED": "Ha fallado en",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PENDING": "Validación pendiente. Haga clic en el icono para probar la conectividad.",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PREFIX": "Última validación",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_SUCCESSFUL": "Ha sido correcto en",
    "CLOUD_NSS_HTTP_HEADERS": "Encabezados HTTP",
    "CLOUD_OR_ON_PREMISE": "De nube o en las instalaciones",
    "CLOUD_PROVIDER_TYPE": "Tipo de proveedor de nube",
    "CLOUD_PROVIDER": "Proveedor de nube",
    "CLOUD_PROVISIONING": "Aprovisionamiento de nube",
    "CLOUD_WATCH_GROUP_ARN": "ARN de grupo de Cloud Watch",
    "CLOUD": "Nube",
    "CLOUDFORMATION_TEXT": "La plantillas de Cloudformation se utilizan para proporcionar a Zscaler permisos para acceder a información de cuentas. Utilice el hipervínculo Iniciar Cloudformation para abrir una plantilla de Cloudformation precargada en su cuenta de AWS. {1}Descargue la plantilla de Cloudformation{2} si la opción de inicio no funciona. ",
    "CLOUDFORMATION": "CloudFormation",
    "CLOUDWATCH_ARN_OPTIONAL": "ARN de Cloudwatch (opcional)",
    "CLT_RX_BYTES": "Bytes recibidos por el cliente",
    "CLT_TX_BYTES": "Bytes enviados por el cliente",
    "CLT_TX_DROPS": "Bytes de caídas de cliente",
    "CLUSTERS": "Clústeres",
    "CN_NORTH_1": "China (Pekín)",
    "CN_NORTH_1A": "cn-north-1a",
    "CN_NORTH_1B": "cn-north-1b",
    "CN_NORTHWEST_1": "China (Ningxia)",
    "CN_NORTHWEST_1A": "cn-northwest-1a",
    "CN_NORTHWEST_1B": "cn-northwest-1b",
    "CN_NORTHWEST_1C": "cn-northwest-1c",
    "COCOS_KEELING_ISLANDS_INDIAN_COCOS": "Indian/Cocos",
    "COCOS_KEELING_ISLANDS": "Cocos (Keeling) Islands",
    "CODE": "CODE",
    "COLOMBIA_AMERICA_BOGOTA": "America/Bogota",
    "COLOMBIA": "Colombia",
    "COMMANDS": "Comandos",
    "COMMENTS": "Comentarios",
    "COMOROS_INDIAN_COMORO": "Indian/Comoro",
    "COMOROS": "Comoros",
    "COMPARE_VERSIONS": "Comparar versiones",
    "COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE": "COMPUTAR TIPO DE INSTANCIA EC2 RECOMENDADA",
    "COMPUTE": "Computar",
    "COMPUTER_HACKING": "Hacking de ordenadores",
    "CONFIG": "Config.",
    "CONFIGURATION_INFO": "INFORMACIÓN DE CONFIGURACIÓN",
    "CONFIGURATION_MODE": "Modo de configuración",
    "CONFIGURATION_NAME": "Nombre de configuración",
    "CONFIGURATION_TEMPLATE_NAME": "Nombre de la plantilla de configuración",
    "CONFIGURATION_TEXT": "Zscaler requiere permisos para asumir un rol IAM en su cuenta de AWS. Estos permisos permiten a Zscaler recopilar metadatos de configuración en tiempo real en AWS.  Para obtener más información, consulte {1}Añadir una nueva cuenta de AWS{2}.",
    "CONFIGURATION": "Configuración ",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP": "Puede definir las reglas que controlan las solicitudes y respuestas de reenvío de control.",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD": "Configurar política de reenvío de registro y control",
    "CONFIGURE_TRAFFIC_FORWARD_TIP": "Puede definir las reglas que controlan las solicitudes y respuestas de reenvío de tráfico.",
    "CONFIGURE_TRAFFIC_FORWARD": "Configurar reenvío de tráfico",
    "CONFIGURED_MODE": "Modo configurado",
    "CONFIRM_CHANGES": "Confirmar cambios",
    "CONFIRM_PASSWORD_NON_EQUALITY": "La nueva contraseña y la confirmación de contraseña deben ser iguales",
    "CONFIRM_PASSWORD_PLACEHOLDER": "Introduzca la nueva contraseña para confirmarla",
    "CONFIRM_PASSWORD": "Confirmar contraseña",
    "CONFIRM": "Confirmar",
    "CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE": "África/Brazzaville",
    "CONGO_CONGO_BRAZZAVILLE": "África/Brazzaville",
    "CONGO_DEM_REP_AFRICA_KINSHASA": "África/Kinshasa",
    "CONGO_DEM_REP_AFRICA_LUBUMBASHI": "Africa/Lubumbashi",
    "CONGO_DEM_REP": "Congo (Rep. Democrática)",
    "CONGO_REP_AFRICA_BRAZZAVILLE": "África/Brazzaville",
    "CONGO_REP": "Congo (Rep.)",
    "CONGO_REPUBLIC": "República del Congo",
    "CONNECTOR_GROUP": "Grupo de conectores",
    "CONNECTOR_GROUPS": "Grupos de conectores",
    "CONNECTOR_INSTANCE": "Instancia de conector",
    "CONNECTOR_IP": "IP de conector",
    "CONNECTOR_MANAGEMENT": "Gestión de conectores",
    "CONNECTOR_NAME": "Nombre de conector",
    "CONNECTOR_NAMES": "Nombres de conectores",
    "CONNECTOR_SOURCE_IP": "IP de origen de conector",
    "CONNECTOR_SOURCE_PORT": "Puerto de origen de conector",
    "CONNECTOR_VM_SIZE": "Tamaño de VM de conector",
    "CONNECTOR_VM": "VM de conector",
    "CONTAINS": "Contiene",
    "CONTINUING_EDUCATION_COLLEGES": "Universidades / Educación para Adultos",
    "CONTROL_SLASH_DATA": "Control/Datos",
    "COOK_ISLANDS_PACIFIC_RAROTONGA": "Pacific/Rarotonga",
    "COOK_ISLANDS": "Cook Islands",
    "Copy for provisioning": "Copia para aprovisionamiento",
    "COPY_CLOUD_CONNECTOR": "Copiar Cloud Connector",
    "COPY_FOR_PROVISIONING": "Copia para aprovisionamiento",
    "COPY_PROVISIONING_URL": "Copiar URL de aprovisionamiento",
    "COPY_RIGHT": "Copyright ©2007-2020 Zscaler Inc. Todos los derechos reservados.",
    "COPY": "Copiar",
    "COPYRIGHT_INFRINGEMENT": "Infracción de copyright",
    "COPYRIGHT": "Copyright",
    "CORPORATE_MARKETING": "Marketing Corporativo",
    "CORPORATE": "Tipo de tráfico de usuarios corporativos",
    "COSTA_RICA_AMERICA_COSTA_RICA": "America/Costa Rica",
    "COSTA_RICA": "Costa Rica",
    "COTE_DIVOIRE_AFRICA_ABIDJAN": "Africa/Abidjan",
    "COTE_DIVOIRE": "Cote d'Ivoire",
    "COUNT": "Cuenta",
    "COUNTRIES": "Países",
    "COUNTRY_A1": "Proxy anónimo",
    "COUNTRY_A2": "Proveedor de satélite",
    "COUNTRY_AC": "Isla Ascensión",
    "COUNTRY_AD": "Andorra",
    "COUNTRY_AE": "Emiratos Arabes Unidos",
    "COUNTRY_AF": "Afganistán",
    "COUNTRY_AG": "Antigua y Barbuda",
    "COUNTRY_AI": "Anguilla",
    "COUNTRY_AL": "Albania",
    "COUNTRY_AM": "Armenia",
    "COUNTRY_AN": "Netherlands Antilles",
    "COUNTRY_AO": "Angola",
    "COUNTRY_AP": "Asia/Pacific Region",
    "COUNTRY_AQ": "Antártica",
    "COUNTRY_AR": "Argentina",
    "COUNTRY_AS": "American Samoa",
    "COUNTRY_AT": "Austria",
    "COUNTRY_AU": "Australia",
    "COUNTRY_AW": "Aruba",
    "COUNTRY_AX": "Aland Islands",
    "COUNTRY_AZ": "Azerbaijan",
    "COUNTRY_BA": "Bosnia y Herzegovina",
    "COUNTRY_BB": "Barbados",
    "COUNTRY_BD": "Bangladesh",
    "COUNTRY_BE": "Belgium",
    "COUNTRY_BF": "Burkina Faso",
    "COUNTRY_BG": "Bulgaria",
    "COUNTRY_BH": "Bahrain",
    "COUNTRY_BI": "Burundi",
    "COUNTRY_BJ": "Benin",
    "COUNTRY_BL": "Saint Barthelemy",
    "COUNTRY_BM": "Bermuda",
    "COUNTRY_BN": "Brunei Darussalam",
    "COUNTRY_BO": "Bolivia",
    "COUNTRY_BQ": "Bonaire, Sint Eustatius and Saba",
    "COUNTRY_BR": "Brasil",
    "COUNTRY_BS": "Bahamas",
    "COUNTRY_BT": "Bhutan",
    "COUNTRY_BU": "Burma",
    "COUNTRY_BV": "Bouvet Island",
    "COUNTRY_BW": "Botswana",
    "COUNTRY_BX": "Benelux Trademarks and Design Offices",
    "COUNTRY_BY": "Bielorrusia",
    "COUNTRY_BZ": "Belize",
    "COUNTRY_CA": "Canada",
    "COUNTRY_CC": "Cocos (Keeling) Islands",
    "COUNTRY_CD": "República Democrática del Congo (Congo-Kinshasa)",
    "COUNTRY_CF": "Central African Republic",
    "COUNTRY_CG": "Congo (Congo-Brazzaville)",
    "COUNTRY_CH": "Suiza",
    "COUNTRY_CI": "Costa de Marfil",
    "COUNTRY_CK": "Cook Islands",
    "COUNTRY_CL": "Chile",
    "COUNTRY_CM": "Camerún",
    "COUNTRY_CN": "China",
    "COUNTRY_CO": "Colombia",
    "COUNTRY_CODE": "País",
    "COUNTRY_CP": "Clipperton Island",
    "COUNTRY_CR": "Costa Rica",
    "COUNTRY_CS": "Serbia y Montenegro",
    "COUNTRY_CT": "Canton and Enderbury Islands",
    "COUNTRY_CU": "Cuba",
    "COUNTRY_CV": "Cabo Verde",
    "COUNTRY_CW": "Curacao",
    "COUNTRY_CX": "Christmas Island",
    "COUNTRY_CY": "Chipre",
    "COUNTRY_CZ": "República Checa",
    "COUNTRY_DD": "German Democratic Republic",
    "COUNTRY_DE": "Germany",
    "COUNTRY_DG": "Diego Garcia",
    "COUNTRY_DJ": "Djibouti",
    "COUNTRY_DK": "Dinamarca",
    "COUNTRY_DM": "Dominica",
    "COUNTRY_DO": "República Dominicana",
    "COUNTRY_DY": "Benin",
    "COUNTRY_DZ": "Argelia",
    "COUNTRY_EA": "Ceuta, Melilla",
    "COUNTRY_EC": "Ecuador",
    "COUNTRY_EE": "Estonia",
    "COUNTRY_EF": "Unión de Paises bajo la Convención de Patentes de la Comunidad Europea",
    "COUNTRY_EG": "Egipto",
    "COUNTRY_EH": "Sáhara Occidental",
    "COUNTRY_EM": "European Trademark Office",
    "COUNTRY_EP": "Europaan Patent Organization",
    "COUNTRY_ER": "Eritrea",
    "COUNTRY_ES": "España",
    "COUNTRY_ET": "Etiopía",
    "COUNTRY_EU": "Europa",
    "COUNTRY_EV": "Eurasian Patent Organization",
    "COUNTRY_EW": "Estonia",
    "COUNTRY_FI": "Finland",
    "COUNTRY_FJ": "Fiji",
    "COUNTRY_FK": "Falkland Islands (Malvinas)",
    "COUNTRY_FL": "Liechtenstein",
    "COUNTRY_FM": "Federated States of Micronesia",
    "COUNTRY_FO": "Faroe Islands",
    "COUNTRY_FQ": "French Southern and Antarctic Territories",
    "COUNTRY_FR": "Francia",
    "COUNTRY_FX": "Metropolitan France",
    "COUNTRY_GA": "Gabon",
    "COUNTRY_GB": "Reino Unido",
    "COUNTRY_GC": "Patent Office of the Cooperation Council for the Arab States of the Gulf (GCC)",
    "COUNTRY_GD": "Grenada",
    "COUNTRY_GE": "Georgia",
    "COUNTRY_GF": "French Guiana",
    "COUNTRY_GG": "Guernsey",
    "COUNTRY_GH": "Ghana",
    "COUNTRY_GI": "Gibraltar",
    "COUNTRY_GL": "Groenlandia",
    "COUNTRY_GM": "Gambia",
    "COUNTRY_GN": "Guinea",
    "COUNTRY_GP": "Guadalupe",
    "COUNTRY_GQ": "Guinea Ecuatorial",
    "COUNTRY_GR": "Grecia",
    "COUNTRY_GS": "South Georgia and the South Sandwich Islands",
    "COUNTRY_GT": "Guatemala",
    "COUNTRY_GU": "Guam",
    "COUNTRY_GW": "Guinea-Bissau",
    "COUNTRY_GY": "Guyana",
    "COUNTRY_HK": "Hong Kong",
    "COUNTRY_HM": "Heard Island and McDonald Islands",
    "COUNTRY_HN": "Honduras",
    "COUNTRY_HR": "Croacia",
    "COUNTRY_HT": "Haiti",
    "COUNTRY_HU": "Hungría",
    "COUNTRY_HV": "Alto Volta",
    "COUNTRY_IB": "International Bureau of WIPO",
    "COUNTRY_IC": "Islas Canarias",
    "COUNTRY_ID": "Indonesia",
    "COUNTRY_IE": "Irlanda",
    "COUNTRY_IL": "Israel",
    "COUNTRY_IM": "Isle of Man",
    "COUNTRY_IN": "India",
    "COUNTRY_IO": "British Indian Ocean Territory",
    "COUNTRY_IQ": "Iraq",
    "COUNTRY_IR": "Iran",
    "COUNTRY_IS": "Islandia",
    "COUNTRY_IT": "Italia",
    "COUNTRY_JA": "Jamaica",
    "COUNTRY_JE": "Jersey",
    "COUNTRY_JM": "Jamaica",
    "COUNTRY_JO": "Jordania",
    "COUNTRY_JP": "Japón",
    "COUNTRY_JT": "Johnston Island",
    "COUNTRY_KE": "Kenya",
    "COUNTRY_KG": "Kyrgyzstan",
    "COUNTRY_KH": "Camboya",
    "COUNTRY_KI": "Kiribati",
    "COUNTRY_KM": "Comoros",
    "COUNTRY_KN": "Saint Kitts and Nevis",
    "COUNTRY_KP": "Korea del Norte",
    "COUNTRY_KR": "Korea del Sur",
    "COUNTRY_KW": "Kuwait",
    "COUNTRY_KY": "Cayman Islands",
    "COUNTRY_KZ": "Kazakhstan",
    "COUNTRY_LA": "Laos",
    "COUNTRY_LB": "Líbano",
    "COUNTRY_LC": "Saint Lucia",
    "COUNTRY_LF": "Libya Fezzan",
    "COUNTRY_LI": "Liechtenstein",
    "COUNTRY_LK": "Sri Lanka",
    "COUNTRY_LR": "Liberia",
    "COUNTRY_LS": "Lesotho",
    "COUNTRY_LT": "Lithuania",
    "COUNTRY_LU": "Luxemburgo",
    "COUNTRY_LV": "Latvia",
    "COUNTRY_LY": "Libia",
    "COUNTRY_MA": "Marruecos",
    "COUNTRY_MC": "Monaco",
    "COUNTRY_MD": "Moldova",
    "COUNTRY_ME": "Montenegro",
    "COUNTRY_MF": "Saint Martin",
    "COUNTRY_MG": "Madagascar",
    "COUNTRY_MH": "Marshall Islands",
    "COUNTRY_MI": "Midway Islands",
    "COUNTRY_MK": "Macedonia",
    "COUNTRY_ML": "Mali",
    "COUNTRY_MM": "Myanmar",
    "COUNTRY_MN": "Mongolia",
    "COUNTRY_MO": "Macao",
    "COUNTRY_MP": "Northern Mariana Islands",
    "COUNTRY_MQ": "Martinique",
    "COUNTRY_MR": "Mauritania",
    "COUNTRY_MS": "Montserrat",
    "COUNTRY_MT": "Malta",
    "COUNTRY_MU": "Mauritius",
    "COUNTRY_MV": "Maldivas",
    "COUNTRY_MW": "Malawi",
    "COUNTRY_MX": "Méjico",
    "COUNTRY_MY": "Malasia",
    "COUNTRY_MZ": "Mozambique",
    "COUNTRY_NA": "Namibia",
    "COUNTRY_NC": "New Caledonia",
    "COUNTRY_NE": "Niger",
    "COUNTRY_NF": "Norfolk Island",
    "COUNTRY_NG": "Nigeria",
    "COUNTRY_NH": "New Hebrides",
    "COUNTRY_NI": "Nicaragua",
    "COUNTRY_NL": "Países Bajos",
    "COUNTRY_NO": "Noruega",
    "COUNTRY_NP": "Nepal",
    "COUNTRY_NQ": "Dronning Maud Land",
    "COUNTRY_NR": "Nauru",
    "COUNTRY_NT": "Neutral Zone",
    "COUNTRY_NU": "Niue",
    "COUNTRY_NZ": "Nueva Zelanda",
    "COUNTRY_O1": "Otros",
    "COUNTRY_OA": "African Intellectual Property Organization",
    "COUNTRY_OM": "Oman",
    "COUNTRY_OPTIONAL": "País (opcional)",
    "COUNTRY_PA": "Panama",
    "COUNTRY_PC": "Pacific Islands, Trust Territory of the",
    "COUNTRY_PE": "Peru",
    "COUNTRY_PF": "Polinesia Francesa",
    "COUNTRY_PG": "Papua New Guinea",
    "COUNTRY_PH": "Philippines",
    "COUNTRY_PI": "Philippines",
    "COUNTRY_PK": "Pakistan",
    "COUNTRY_PL": "Polonia",
    "COUNTRY_PM": "Saint Pierre and Miquelon",
    "COUNTRY_PN": "Pitcairn Islands",
    "COUNTRY_PR": "Puerto Rico",
    "COUNTRY_PS": "Palestinian Territory",
    "COUNTRY_PT": "Portugal",
    "COUNTRY_PU": "U.S. Miscellaneous Pacific Islands",
    "COUNTRY_PW": "Palau",
    "COUNTRY_PY": "Paraguay",
    "COUNTRY_PZ": "Panama Canal Zone",
    "COUNTRY_QA": "Qatar",
    "COUNTRY_RA": "Argentina",
    "COUNTRY_RB": "Bolivia cf Botswana",
    "COUNTRY_RC": "China",
    "COUNTRY_RE": "Reunion",
    "COUNTRY_RH": "Haiti",
    "COUNTRY_RI": "Indonesia",
    "COUNTRY_RL": "Líbano",
    "COUNTRY_RM": "Madagascar",
    "COUNTRY_RN": "Niger",
    "COUNTRY_RO": "Rumanía",
    "COUNTRY_RP": "Philippines",
    "COUNTRY_RS": "Serbia",
    "COUNTRY_RU": "Rusia",
    "COUNTRY_RW": "Rwanda",
    "COUNTRY_SA": "Saudi Arabia",
    "COUNTRY_SB": "Solomon Islands",
    "COUNTRY_SC": "Seychelles",
    "COUNTRY_SD": "Sudan",
    "COUNTRY_SE": "Suecia",
    "COUNTRY_SF": "Finland",
    "COUNTRY_SG": "Singapur",
    "COUNTRY_SH": "Saint Helena",
    "COUNTRY_SI": "Eslovenia",
    "COUNTRY_SJ": "Svalbard and Jan Mayen",
    "COUNTRY_SK": "Eslovaquia",
    "COUNTRY_SL": "Sierra Leona",
    "COUNTRY_SM": "San Marino",
    "COUNTRY_SN": "Senegal",
    "COUNTRY_SO": "Somalia",
    "COUNTRY_SR": "Surinam",
    "COUNTRY_SS": "South Sudan",
    "COUNTRY_ST": "Sao Tome and Principe",
    "COUNTRY_SU": "USSR",
    "COUNTRY_SV": "El Salvador",
    "COUNTRY_SX": "Sint Maarten (Dutch part)",
    "COUNTRY_SY": "Siria",
    "COUNTRY_SZ": "Swaziland",
    "COUNTRY_TA": "Tristan da Cunha",
    "COUNTRY_TC": "Turks and Caicos Islands",
    "COUNTRY_TD": "Chad",
    "COUNTRY_TF": "Territorios Franceses del Sur",
    "COUNTRY_TG": "Togo",
    "COUNTRY_TH": "Tailandia",
    "COUNTRY_TJ": "Tajikistan",
    "COUNTRY_TK": "Tokelau",
    "COUNTRY_TL": "Timor-Leste",
    "COUNTRY_TM": "Turkmenistan",
    "COUNTRY_TN": "Túnez",
    "COUNTRY_TO": "Tonga",
    "COUNTRY_TP": "East Timor",
    "COUNTRY_TR": "Turquía",
    "COUNTRY_TT": "Trinidad y Tobago",
    "COUNTRY_TV": "Tuvalu",
    "COUNTRY_TW": "Taiwan",
    "COUNTRY_TZ": "Tanzania",
    "COUNTRY_UA": "Ucrania",
    "COUNTRY_UG": "Uganda",
    "COUNTRY_UK": "Reino Unido",
    "COUNTRY_UM": "United States Minor Outlying Islands",
    "COUNTRY_US": "United States",
    "COUNTRY_USA": "EE. UU.",
    "COUNTRY_UY": "Uruguay",
    "COUNTRY_UZ": "Uzbekistan",
    "COUNTRY_VA": "Santa Sede (Estado de la Ciudad del Vaticano) ",
    "COUNTRY_VC": "Saint Vincent and the Grenadines",
    "COUNTRY_VD": "Viet-Nam, Democratic Republic of",
    "COUNTRY_VE": "Venezuela",
    "COUNTRY_VG": "Virgin Islands (British)",
    "COUNTRY_VI": "Virgin Islands (U.S.)",
    "COUNTRY_VN": "Vietnam",
    "COUNTRY_VU": "Vanuatu",
    "COUNTRY_WF": "Wallis and Futuna",
    "COUNTRY_WG": "Grenada",
    "COUNTRY_WK": "Wake Island",
    "COUNTRY_WL": "Saint Lucia",
    "COUNTRY_WO": "World Intellectual Property Organization",
    "COUNTRY_WS": "Samoa",
    "COUNTRY_WV": "Saint Vincent",
    "COUNTRY_YD": "Yemen, Democratic",
    "COUNTRY_YE": "Yemen",
    "COUNTRY_YT": "Mayotte",
    "COUNTRY_YU": "Yugoslavia",
    "COUNTRY_YV": "Venezuela",
    "COUNTRY_ZA": "Sudáfrica",
    "COUNTRY_ZM": "Zambia",
    "COUNTRY_ZR": "Zaire",
    "COUNTRY_ZW": "Zimbabwe",
    "COUNTRY": "País",
    "CREATE_A_NEW_GROUP": "Crear un nuevo grupo",
    "CREATE_A_NEW_TEST": "Crear una nueva prueba",
    "CREATE_COMPLETE": "Creación finalizada",
    "CREATE_IN_PROGRESS": "Creación en curso",
    "CREATE_TEST_TEXT": "Cree una prueba introduciendo el nombre, la descripción, el destino y el protocolo (HTTP/HTTPS) que deben utilizarse para la prueba. Una vez creada, la prueba se puede ejecutar bajo demanda. Cuando se ejecuta, esta prueba simula un comando curl al destino mediante el protocolo introducido. La transacción debe ser visible en los registros de reenvío, registros de firewall, registros web, etc.",
    "CREATE_TEST": "Crear prueba",
    "CREATE": "Crear",
    "CREATING": "Creando",
    "CREDENTIALS_INFO": "Introduzca las credenciales de la entidad de servicio de Azure para acceder a sus suscripciones de Azure. Puede utilizar una sola entidad de servicio para varias suscripciones o una entidad de servicio para cada suscripción. Asegúrese de que la entidad de servicio tenga {1}permisos de acceso{2}.",
    "CREDENTIALS_TEXT": "Introduzca las credenciales de la cuenta de Azure para acceder a su cuenta de Azure.",
    "CREDENTIALS": "Credenciales",
    "CRITERIA_TEXT": "Criterios",
    "CRITERIA": "CRITERIOS",
    "CROATIA_EUROPE_ZAGREB": "Europa/Zagreb",
    "CROATIA": "Croacia",
    "CRYPTOMINING": "Criptominería",
    "CTL_CONNECTION_FAIL": "Error en la conexión de control de SVPN.",
    "CTL_GW_CONN_CLOSE": "Conexión activa de pasarela de control cerrada.",
    "CTL_GW_CONN_SETUP_FAIL": "Error de configuración de conexión de pasarela de control (error interno).",
    "CTL_GW_CONNECT_FAIL": "Error de conexión de pasarela de control (error de red).",
    "CTL_GW_DNS_RESOLVE_FAIL": "Error de resolución de DNS de pasarela de control.",
    "CTL_GW_KA_FAIL": "Error de keepalive de conexión de pasarela de control.",
    "CTL_GW_NO_CONN": "Conexión de pasarela de control aún no iniciada por el cliente.",
    "CTL_GW_PAC_RESOLVE_FAIL": "Error de resolución de PAC de pasarela de control.",
    "CTL_GW_PAC_RESOLVE_NOIP": "La resolución de PAC de pasarela de control no ha devuelto ningún IPS.",
    "CTL_GW_PROTO_MSG_ERROR": "Error de formato de mensaje en respuesta de pasarela de control.",
    "CTL_GW_SRV_ERR_RESPONSE": "La pasarela de control ha recibido una respuesta de error de HTTP del servidor.",
    "CTL_GW_UNHEALTHY": "La pasarela de control no está en buen estado (estado transitorio).",
    "CTL_KEEAPLIVE_FAIL": "Error de keepalive de conexión de control de SVPN.",
    "CTL_KEEPALIVE_FAIL": "Error de keepalive de conexión de control de SVPN.",
    "CUBA_AMERICA_HAVANA": "America/Havana",
    "CUBA": "Cuba",
    "CULT": "Culto",
    "CURRENT_API_KEY": "Clave de API actual",
    "CURRENT_DAY": "Día actual",
    "CURRENT_MODE": "Modo actual",
    "CURRENT_MONTH": "Mes actual",
    "CURRENT_PASSWORD_NOT_VALID": "Introduzca la contraseña actual válida",
    "CURRENT_PASSWORD": "Contraseña actual",
    "CURRENT_VERSION": "Versión actual",
    "CURRENT_WEEK": "Semana actual",
    "CURRENTLY_EDITING": "EDITANDO ACTUALMENTE",
    "CUSTOM_00": "Categoría personalizada 0",
    "CUSTOM_01": "Categoría personalizada 1",
    "CUSTOM_02": "Categoría personalizada 2",
    "CUSTOM_03": "Categoría personalizada 3",
    "CUSTOM_04": "Categoría personalizada 4",
    "CUSTOM_05": "Categoría personalizada 5",
    "CUSTOM_06": "Categoría personalizada 6",
    "CUSTOM_07": "Categoría personalizada 7",
    "CUSTOM_08": "Categoría personalizada 8",
    "CUSTOM_09": "Categoría personalizada 9",
    "CUSTOM_10": "Categoría personalizada 0",
    "CUSTOM_100": "Categoría personalizada 100",
    "CUSTOM_101": "Categoría personalizada 101",
    "CUSTOM_102": "Categoría personalizada 102",
    "CUSTOM_103": "Categoría personalizada 103",
    "CUSTOM_104": "Categoría personalizada 104",
    "CUSTOM_105": "Categoría personalizada 105",
    "CUSTOM_106": "Categoría personalizada 106",
    "CUSTOM_107": "Categoría personalizada 107",
    "CUSTOM_108": "Categoría personalizada 108",
    "CUSTOM_109": "Categoría personalizada 109",
    "CUSTOM_11": "Categoría personalizada 11",
    "CUSTOM_110": "Categoría personalizada 110",
    "CUSTOM_111": "Categoría personalizada 111",
    "CUSTOM_112": "Categoría personalizada 112",
    "CUSTOM_113": "Categoría personalizada 113",
    "CUSTOM_114": "Categoría personalizada 114",
    "CUSTOM_115": "Categoría personalizada 115",
    "CUSTOM_116": "Categoría personalizada 116",
    "CUSTOM_117": "Categoría personalizada 117",
    "CUSTOM_118": "Categoría personalizada 118",
    "CUSTOM_119": "Categoría personalizada 119",
    "CUSTOM_12": "Categoría personalizada 12",
    "CUSTOM_120": "Categoría personalizada 120",
    "CUSTOM_121": "Categoría personalizada 121",
    "CUSTOM_122": "Categoría personalizada 122",
    "CUSTOM_123": "Categoría personalizada 123",
    "CUSTOM_124": "Categoría personalizada 124",
    "CUSTOM_125": "Categoría personalizada 125",
    "CUSTOM_126": "Categoría personalizada 126",
    "CUSTOM_127": "Categoría personalizada 127",
    "CUSTOM_128": "Categoría personalizada 128",
    "CUSTOM_129": "Categoría personalizada 129",
    "CUSTOM_13": "Categoría personalizada 13",
    "CUSTOM_130": "Categoría personalizada 130",
    "CUSTOM_131": "Categoría personalizada 131",
    "CUSTOM_132": "Categoría personalizada 132",
    "CUSTOM_133": "Categoría personalizada 133",
    "CUSTOM_134": "Categoría personalizada 134",
    "CUSTOM_135": "Categoría personalizada 135",
    "CUSTOM_136": "Categoría personalizada 136",
    "CUSTOM_137": "Categoría personalizada 137",
    "CUSTOM_138": "Categoría personalizada 138",
    "CUSTOM_139": "Categoría personalizada 139",
    "CUSTOM_14": "Categoría personalizada 14",
    "CUSTOM_140": "Categoría personalizada 140",
    "CUSTOM_141": "Categoría personalizada 141",
    "CUSTOM_142": "Categoría personalizada 142",
    "CUSTOM_143": "Categoría personalizada 143",
    "CUSTOM_144": "Categoría personalizada 144",
    "CUSTOM_145": "Categoría personalizada 145",
    "CUSTOM_146": "Categoría personalizada 146",
    "CUSTOM_147": "Categoría personalizada 147",
    "CUSTOM_148": "Categoría personalizada 148",
    "CUSTOM_149": "Categoría personalizada 149",
    "CUSTOM_15": "Categoría personalizada 15",
    "CUSTOM_150": "Categoría personalizada 150",
    "CUSTOM_151": "Categoría personalizada 151",
    "CUSTOM_152": "Categoría personalizada 152",
    "CUSTOM_153": "Categoría personalizada 153",
    "CUSTOM_154": "Categoría personalizada 154",
    "CUSTOM_155": "Categoría personalizada 155",
    "CUSTOM_156": "Categoría personalizada 156",
    "CUSTOM_157": "Categoría personalizada 157",
    "CUSTOM_158": "Categoría personalizada 158",
    "CUSTOM_159": "Categoría personalizada 159",
    "CUSTOM_16": "Categoría personalizada 16",
    "CUSTOM_160": "Categoría personalizada 160",
    "CUSTOM_161": "Categoría personalizada 161",
    "CUSTOM_162": "Categoría personalizada 162",
    "CUSTOM_163": "Categoría personalizada 163",
    "CUSTOM_164": "Categoría personalizada 164",
    "CUSTOM_165": "Categoría personalizada 165",
    "CUSTOM_166": "Categoría personalizada 166",
    "CUSTOM_167": "Categoría personalizada 167",
    "CUSTOM_168": "Categoría personalizada 168",
    "CUSTOM_169": "Categoría personalizada 169",
    "CUSTOM_17": "Categoría personalizada 17",
    "CUSTOM_170": "Categoría personalizada 170",
    "CUSTOM_171": "Categoría personalizada 171",
    "CUSTOM_172": "Categoría personalizada 172",
    "CUSTOM_173": "Categoría personalizada 173",
    "CUSTOM_174": "Categoría personalizada 174",
    "CUSTOM_175": "Categoría personalizada 175",
    "CUSTOM_176": "Categoría personalizada 176",
    "CUSTOM_177": "Categoría personalizada 177",
    "CUSTOM_178": "Categoría personalizada 178",
    "CUSTOM_179": "Categoría personalizada 179",
    "CUSTOM_18": "Categoría personalizada 18",
    "CUSTOM_180": "Categoría personalizada 180",
    "CUSTOM_181": "Categoría personalizada 181",
    "CUSTOM_182": "Categoría personalizada 182",
    "CUSTOM_183": "Categoría personalizada 183",
    "CUSTOM_184": "Categoría personalizada 184",
    "CUSTOM_185": "Categoría personalizada 185",
    "CUSTOM_186": "Categoría personalizada 186",
    "CUSTOM_187": "Categoría personalizada 187",
    "CUSTOM_188": "Categoría personalizada 188",
    "CUSTOM_189": "Categoría personalizada 189",
    "CUSTOM_19": "Categoría personalizada 19",
    "CUSTOM_190": "Categoría personalizada 190",
    "CUSTOM_191": "Categoría personalizada 191",
    "CUSTOM_192": "Categoría personalizada 192",
    "CUSTOM_193": "Categoría personalizada 193",
    "CUSTOM_194": "Categoría personalizada 194",
    "CUSTOM_195": "Categoría personalizada 195",
    "CUSTOM_196": "Categoría personalizada 196",
    "CUSTOM_197": "Categoría personalizada 197",
    "CUSTOM_198": "Categoría personalizada 198",
    "CUSTOM_199": "Categoría personalizada 199",
    "CUSTOM_20": "Categoría personalizada 20",
    "CUSTOM_200": "Categoría personalizada 200",
    "CUSTOM_201": "Categoría personalizada 201",
    "CUSTOM_202": "Categoría personalizada 202",
    "CUSTOM_203": "Categoría personalizada 203",
    "CUSTOM_204": "Categoría personalizada 204",
    "CUSTOM_205": "Categoría personalizada 205",
    "CUSTOM_206": "Categoría personalizada 206",
    "CUSTOM_207": "Categoría personalizada 207",
    "CUSTOM_208": "Categoría personalizada 208",
    "CUSTOM_209": "Categoría personalizada 209",
    "CUSTOM_21": "Categoría personalizada 21",
    "CUSTOM_210": "Categoría personalizada 210",
    "CUSTOM_211": "Categoría personalizada 211",
    "CUSTOM_212": "Categoría personalizada 212",
    "CUSTOM_213": "Categoría personalizada 213",
    "CUSTOM_214": "Categoría personalizada 214",
    "CUSTOM_215": "Categoría personalizada 215",
    "CUSTOM_216": "Categoría personalizada 216",
    "CUSTOM_217": "Categoría personalizada 217",
    "CUSTOM_218": "Categoría personalizada 218",
    "CUSTOM_219": "Categoría personalizada 219",
    "CUSTOM_22": "Categoría personalizada 22",
    "CUSTOM_220": "Categoría personalizada 220",
    "CUSTOM_221": "Categoría personalizada 221",
    "CUSTOM_222": "Categoría personalizada 222",
    "CUSTOM_223": "Categoría personalizada 223",
    "CUSTOM_224": "Categoría personalizada 224",
    "CUSTOM_225": "Categoría personalizada 225",
    "CUSTOM_226": "Categoría personalizada 226",
    "CUSTOM_227": "Categoría personalizada 227",
    "CUSTOM_228": "Categoría personalizada 228",
    "CUSTOM_229": "Categoría personalizada 229",
    "CUSTOM_23": "Categoría personalizada 23",
    "CUSTOM_230": "Categoría personalizada 230",
    "CUSTOM_231": "Categoría personalizada 231",
    "CUSTOM_232": "Categoría personalizada 232",
    "CUSTOM_233": "Categoría personalizada 233",
    "CUSTOM_234": "Categoría personalizada 234",
    "CUSTOM_235": "Categoría personalizada 235",
    "CUSTOM_236": "Categoría personalizada 236",
    "CUSTOM_237": "Categoría personalizada 237",
    "CUSTOM_238": "Categoría personalizada 238",
    "CUSTOM_239": "Categoría personalizada 239",
    "CUSTOM_24": "Categoría personalizada 24",
    "CUSTOM_240": "Categoría personalizada 240",
    "CUSTOM_241": "Categoría personalizada 241",
    "CUSTOM_242": "Categoría personalizada 242",
    "CUSTOM_243": "Categoría personalizada 243",
    "CUSTOM_244": "Categoría personalizada 244",
    "CUSTOM_245": "Categoría personalizada 245",
    "CUSTOM_246": "Categoría personalizada 246",
    "CUSTOM_247": "Categoría personalizada 247",
    "CUSTOM_248": "Categoría personalizada 248",
    "CUSTOM_249": "Categoría personalizada 249",
    "CUSTOM_25": "Categoría personalizada 25",
    "CUSTOM_250": "Categoría personalizada 250",
    "CUSTOM_251": "Categoría personalizada 251",
    "CUSTOM_252": "Categoría personalizada 252",
    "CUSTOM_253": "Categoría personalizada 253",
    "CUSTOM_254": "Categoría personalizada 254",
    "CUSTOM_255": "Categoría personalizada 255",
    "CUSTOM_256": "Categoría personalizada 256",
    "CUSTOM_257": "Categoría personalizada 257",
    "CUSTOM_258": "Categoría personalizada 258",
    "CUSTOM_259": "Categoría personalizada 259",
    "CUSTOM_26": "Categoría personalizada 26",
    "CUSTOM_260": "Categoría personalizada 260",
    "CUSTOM_261": "Categoría personalizada 261",
    "CUSTOM_262": "Categoría personalizada 262",
    "CUSTOM_263": "Categoría personalizada 263",
    "CUSTOM_264": "Categoría personalizada 264",
    "CUSTOM_265": "Categoría personalizada 265",
    "CUSTOM_266": "Categoría personalizada 266",
    "CUSTOM_267": "Categoría personalizada 267",
    "CUSTOM_268": "Categoría personalizada 268",
    "CUSTOM_269": "Categoría personalizada 269",
    "CUSTOM_27": "Categoría personalizada 27",
    "CUSTOM_270": "Categoría personalizada 270",
    "CUSTOM_271": "Categoría personalizada 271",
    "CUSTOM_272": "Categoría personalizada 272",
    "CUSTOM_273": "Categoría personalizada 273",
    "CUSTOM_274": "Categoría personalizada 274",
    "CUSTOM_275": "Categoría personalizada 275",
    "CUSTOM_276": "Categoría personalizada 276",
    "CUSTOM_277": "Categoría personalizada 277",
    "CUSTOM_278": "Categoría personalizada 278",
    "CUSTOM_279": "Categoría personalizada 279",
    "CUSTOM_28": "Categoría personalizada 28",
    "CUSTOM_280": "Categoría personalizada 280",
    "CUSTOM_281": "Categoría personalizada 281",
    "CUSTOM_282": "Categoría personalizada 282",
    "CUSTOM_283": "Categoría personalizada 283",
    "CUSTOM_284": "Categoría personalizada 284",
    "CUSTOM_285": "Categoría personalizada 285",
    "CUSTOM_286": "Categoría personalizada 286",
    "CUSTOM_287": "Categoría personalizada 287",
    "CUSTOM_288": "Categoría personalizada 288",
    "CUSTOM_289": "Categoría personalizada 289",
    "CUSTOM_29": "Categoría personalizada 29",
    "CUSTOM_290": "Categoría personalizada 290",
    "CUSTOM_291": "Categoría personalizada 291",
    "CUSTOM_292": "Categoría personalizada 292",
    "CUSTOM_293": "Categoría personalizada 293",
    "CUSTOM_294": "Categoría personalizada 294",
    "CUSTOM_295": "Categoría personalizada 295",
    "CUSTOM_296": "Categoría personalizada 296",
    "CUSTOM_297": "Categoría personalizada 297",
    "CUSTOM_298": "Categoría personalizada 298",
    "CUSTOM_299": "Categoría personalizada 299",
    "CUSTOM_30": "Categoría personalizada 30",
    "CUSTOM_300": "Categoría personalizada 300",
    "CUSTOM_301": "Categoría personalizada 301",
    "CUSTOM_302": "Categoría personalizada 302",
    "CUSTOM_303": "Categoría personalizada 303",
    "CUSTOM_304": "Categoría personalizada 304",
    "CUSTOM_305": "Categoría personalizada 305",
    "CUSTOM_306": "Categoría personalizada 306",
    "CUSTOM_307": "Categoría personalizada 307",
    "CUSTOM_308": "Categoría personalizada 308",
    "CUSTOM_309": "Categoría personalizada 309",
    "CUSTOM_31": "Categoría personalizada 31",
    "CUSTOM_310": "Categoría personalizada 310",
    "CUSTOM_311": "Categoría personalizada 311",
    "CUSTOM_312": "Categoría personalizada 312",
    "CUSTOM_313": "Categoría personalizada 313",
    "CUSTOM_314": "Categoría personalizada 314",
    "CUSTOM_315": "Categoría personalizada 315",
    "CUSTOM_316": "Categoría personalizada 316",
    "CUSTOM_317": "Categoría personalizada 317",
    "CUSTOM_318": "Categoría personalizada 318",
    "CUSTOM_319": "Categoría personalizada 319",
    "CUSTOM_32": "Categoría personalizada 32",
    "CUSTOM_320": "Categoría personalizada 320",
    "CUSTOM_321": "Categoría personalizada 321",
    "CUSTOM_322": "Categoría personalizada 322",
    "CUSTOM_323": "Categoría personalizada 323",
    "CUSTOM_324": "Categoría personalizada 324",
    "CUSTOM_325": "Categoría personalizada 325",
    "CUSTOM_326": "Categoría personalizada 326",
    "CUSTOM_327": "Categoría personalizada 327",
    "CUSTOM_328": "Categoría personalizada 328",
    "CUSTOM_329": "Categoría personalizada 329",
    "CUSTOM_33": "Categoría personalizada 33",
    "CUSTOM_330": "Categoría personalizada 330",
    "CUSTOM_331": "Categoría personalizada 331",
    "CUSTOM_332": "Categoría personalizada 332",
    "CUSTOM_333": "Categoría personalizada 333",
    "CUSTOM_334": "Categoría personalizada 334",
    "CUSTOM_335": "Categoría personalizada 335",
    "CUSTOM_336": "Categoría personalizada 336",
    "CUSTOM_337": "Categoría personalizada 337",
    "CUSTOM_338": "Categoría personalizada 338",
    "CUSTOM_339": "Categoría personalizada 339",
    "CUSTOM_34": "Categoría personalizada 34",
    "CUSTOM_340": "Categoría personalizada 340",
    "CUSTOM_341": "Categoría personalizada 341",
    "CUSTOM_342": "Categoría personalizada 342",
    "CUSTOM_343": "Categoría personalizada 343",
    "CUSTOM_344": "Categoría personalizada 344",
    "CUSTOM_345": "Categoría personalizada 345",
    "CUSTOM_346": "Categoría personalizada 346",
    "CUSTOM_347": "Categoría personalizada 347",
    "CUSTOM_348": "Categoría personalizada 348",
    "CUSTOM_349": "Categoría personalizada 349",
    "CUSTOM_35": "Categoría personalizada 35",
    "CUSTOM_350": "Categoría personalizada 350",
    "CUSTOM_351": "Categoría personalizada 351",
    "CUSTOM_352": "Categoría personalizada 352",
    "CUSTOM_353": "Categoría personalizada 353",
    "CUSTOM_354": "Categoría personalizada 354",
    "CUSTOM_355": "Categoría personalizada 355",
    "CUSTOM_356": "Categoría personalizada 356",
    "CUSTOM_357": "Categoría personalizada 357",
    "CUSTOM_358": "Categoría personalizada 358",
    "CUSTOM_359": "Categoría personalizada 359",
    "CUSTOM_36": "Categoría personalizada 36",
    "CUSTOM_360": "Categoría personalizada 360",
    "CUSTOM_361": "Categoría personalizada 361",
    "CUSTOM_362": "Categoría personalizada 362",
    "CUSTOM_363": "Categoría personalizada 363",
    "CUSTOM_364": "Categoría personalizada 364",
    "CUSTOM_365": "Categoría personalizada 365",
    "CUSTOM_366": "Categoría personalizada 366",
    "CUSTOM_367": "Categoría personalizada 367",
    "CUSTOM_368": "Categoría personalizada 368",
    "CUSTOM_369": "Categoría personalizada 369",
    "CUSTOM_37": "Categoría personalizada 37",
    "CUSTOM_370": "Categoría personalizada 370",
    "CUSTOM_371": "Categoría personalizada 371",
    "CUSTOM_372": "Categoría personalizada 372",
    "CUSTOM_373": "Categoría personalizada 373",
    "CUSTOM_374": "Categoría personalizada 374",
    "CUSTOM_375": "Categoría personalizada 375",
    "CUSTOM_376": "Categoría personalizada 376",
    "CUSTOM_377": "Categoría personalizada 377",
    "CUSTOM_378": "Categoría personalizada 378",
    "CUSTOM_379": "Categoría personalizada 379",
    "CUSTOM_38": "Categoría personalizada 38",
    "CUSTOM_380": "Categoría personalizada 380",
    "CUSTOM_381": "Categoría personalizada 381",
    "CUSTOM_382": "Categoría personalizada 382",
    "CUSTOM_383": "Categoría personalizada 383",
    "CUSTOM_384": "Categoría personalizada 384",
    "CUSTOM_385": "Categoría personalizada 385",
    "CUSTOM_386": "Categoría personalizada 386",
    "CUSTOM_387": "Categoría personalizada 387",
    "CUSTOM_388": "Categoría personalizada 388",
    "CUSTOM_389": "Categoría personalizada 389",
    "CUSTOM_39": "Categoría personalizada 39",
    "CUSTOM_390": "Categoría personalizada 390",
    "CUSTOM_391": "Categoría personalizada 391",
    "CUSTOM_392": "Categoría personalizada 392",
    "CUSTOM_393": "Categoría personalizada 393",
    "CUSTOM_394": "Categoría personalizada 394",
    "CUSTOM_395": "Categoría personalizada 395",
    "CUSTOM_396": "Categoría personalizada 396",
    "CUSTOM_397": "Categoría personalizada 397",
    "CUSTOM_398": "Categoría personalizada 398",
    "CUSTOM_399": "Categoría personalizada 399",
    "CUSTOM_40": "Categoría personalizada 40",
    "CUSTOM_400": "Categoría personalizada 400",
    "CUSTOM_401": "Categoría personalizada 401",
    "CUSTOM_402": "Categoría personalizada 402",
    "CUSTOM_403": "Categoría personalizada 403",
    "CUSTOM_404": "Categoría personalizada 404",
    "CUSTOM_405": "Categoría personalizada 405",
    "CUSTOM_406": "Categoría personalizada 406",
    "CUSTOM_407": "Categoría personalizada 407",
    "CUSTOM_408": "Categoría personalizada 408",
    "CUSTOM_409": "Categoría personalizada 409",
    "CUSTOM_41": "Categoría personalizada 41",
    "CUSTOM_410": "Categoría personalizada 410",
    "CUSTOM_411": "Categoría personalizada 411",
    "CUSTOM_412": "Categoría personalizada 412",
    "CUSTOM_413": "Categoría personalizada 413",
    "CUSTOM_414": "Categoría personalizada 414",
    "CUSTOM_415": "Categoría personalizada 415",
    "CUSTOM_416": "Categoría personalizada 416",
    "CUSTOM_417": "Categoría personalizada 417",
    "CUSTOM_418": "Categoría personalizada 418",
    "CUSTOM_419": "Categoría personalizada 419",
    "CUSTOM_42": "Categoría personalizada 42",
    "CUSTOM_420": "Categoría personalizada 420",
    "CUSTOM_421": "Categoría personalizada 421",
    "CUSTOM_422": "Categoría personalizada 422",
    "CUSTOM_423": "Categoría personalizada 423",
    "CUSTOM_424": "Categoría personalizada 424",
    "CUSTOM_425": "Categoría personalizada 425",
    "CUSTOM_426": "Categoría personalizada 426",
    "CUSTOM_427": "Categoría personalizada 427",
    "CUSTOM_428": "Categoría personalizada 428",
    "CUSTOM_429": "Categoría personalizada 429",
    "CUSTOM_43": "Categoría personalizada 43",
    "CUSTOM_430": "Categoría personalizada 430",
    "CUSTOM_431": "Categoría personalizada 431",
    "CUSTOM_432": "Categoría personalizada 432",
    "CUSTOM_433": "Categoría personalizada 433",
    "CUSTOM_434": "Categoría personalizada 434",
    "CUSTOM_435": "Categoría personalizada 435",
    "CUSTOM_436": "Categoría personalizada 436",
    "CUSTOM_437": "Categoría personalizada 437",
    "CUSTOM_438": "Categoría personalizada 438",
    "CUSTOM_439": "Categoría personalizada 439",
    "CUSTOM_44": "Categoría personalizada 44",
    "CUSTOM_440": "Categoría personalizada 440",
    "CUSTOM_441": "Categoría personalizada 441",
    "CUSTOM_442": "Categoría personalizada 442",
    "CUSTOM_443": "Categoría personalizada 443",
    "CUSTOM_444": "Categoría personalizada 444",
    "CUSTOM_445": "Categoría personalizada 445",
    "CUSTOM_446": "Categoría personalizada 446",
    "CUSTOM_447": "Categoría personalizada 447",
    "CUSTOM_448": "Categoría personalizada 448",
    "CUSTOM_449": "Categoría personalizada 449",
    "CUSTOM_45": "Categoría personalizada 45",
    "CUSTOM_450": "Categoría personalizada 450",
    "CUSTOM_451": "Categoría personalizada 451",
    "CUSTOM_452": "Categoría personalizada 452",
    "CUSTOM_453": "Categoría personalizada 453",
    "CUSTOM_454": "Categoría personalizada 454",
    "CUSTOM_455": "Categoría personalizada 455",
    "CUSTOM_456": "Categoría personalizada 456",
    "CUSTOM_457": "Categoría personalizada 457",
    "CUSTOM_458": "Categoría personalizada 458",
    "CUSTOM_459": "Categoría personalizada 459",
    "CUSTOM_46": "Categoría personalizada 46",
    "CUSTOM_460": "Categoría personalizada 460",
    "CUSTOM_461": "Categoría personalizada 461",
    "CUSTOM_462": "Categoría personalizada 462",
    "CUSTOM_463": "Categoría personalizada 463",
    "CUSTOM_464": "Categoría personalizada 464",
    "CUSTOM_465": "Categoría personalizada 465",
    "CUSTOM_466": "Categoría personalizada 466",
    "CUSTOM_467": "Categoría personalizada 467",
    "CUSTOM_468": "Categoría personalizada 468",
    "CUSTOM_469": "Categoría personalizada 469",
    "CUSTOM_47": "Categoría personalizada 47",
    "CUSTOM_470": "Categoría personalizada 470",
    "CUSTOM_471": "Categoría personalizada 471",
    "CUSTOM_472": "Categoría personalizada 472",
    "CUSTOM_473": "Categoría personalizada 473",
    "CUSTOM_474": "Categoría personalizada 474",
    "CUSTOM_475": "Categoría personalizada 475",
    "CUSTOM_476": "Categoría personalizada 476",
    "CUSTOM_477": "Categoría personalizada 477",
    "CUSTOM_478": "Categoría personalizada 478",
    "CUSTOM_479": "Categoría personalizada 479",
    "CUSTOM_48": "Categoría personalizada 48",
    "CUSTOM_480": "Categoría personalizada 480",
    "CUSTOM_481": "Categoría personalizada 481",
    "CUSTOM_482": "Categoría personalizada 482",
    "CUSTOM_483": "Categoría personalizada 483",
    "CUSTOM_484": "Categoría personalizada 484",
    "CUSTOM_485": "Categoría personalizada 485",
    "CUSTOM_486": "Categoría personalizada 486",
    "CUSTOM_487": "Categoría personalizada 487",
    "CUSTOM_488": "Categoría personalizada 488",
    "CUSTOM_489": "Categoría personalizada 489",
    "CUSTOM_49": "Categoría personalizada 49",
    "CUSTOM_490": "Categoría personalizada 490",
    "CUSTOM_491": "Categoría personalizada 491",
    "CUSTOM_492": "Categoría personalizada 492",
    "CUSTOM_493": "Categoría personalizada 493",
    "CUSTOM_494": "Categoría personalizada 494",
    "CUSTOM_495": "Categoría personalizada 495",
    "CUSTOM_496": "Categoría personalizada 496",
    "CUSTOM_497": "Categoría personalizada 497",
    "CUSTOM_498": "Categoría personalizada 498",
    "CUSTOM_499": "Categoría personalizada 499",
    "CUSTOM_50": "Categoría personalizada 50",
    "CUSTOM_500": "Categoría personalizada 500",
    "CUSTOM_501": "Categoría personalizada 501",
    "CUSTOM_502": "Categoría personalizada 502",
    "CUSTOM_503": "Categoría personalizada 503",
    "CUSTOM_504": "Categoría personalizada 504",
    "CUSTOM_505": "Categoría personalizada 505",
    "CUSTOM_506": "Categoría personalizada 506",
    "CUSTOM_507": "Categoría personalizada 507",
    "CUSTOM_508": "Categoría personalizada 508",
    "CUSTOM_509": "Categoría personalizada 509",
    "CUSTOM_51": "Categoría personalizada 51",
    "CUSTOM_510": "Categoría personalizada 510",
    "CUSTOM_511": "Categoría personalizada 511",
    "CUSTOM_512": "Categoría personalizada 512",
    "CUSTOM_52": "Categoría personalizada 52",
    "CUSTOM_53": "Categoría personalizada 53",
    "CUSTOM_54": "Categoría personalizada 54",
    "CUSTOM_55": "Categoría personalizada 55",
    "CUSTOM_56": "Categoría personalizada 56",
    "CUSTOM_57": "Categoría personalizada 57",
    "CUSTOM_58": "Categoría personalizada 58",
    "CUSTOM_59": "Categoría personalizada 59",
    "CUSTOM_60": "Categoría personalizada 60",
    "CUSTOM_61": "Categoría personalizada 61",
    "CUSTOM_62": "Categoría personalizada 62",
    "CUSTOM_63": "Categoría personalizada 63",
    "CUSTOM_64": "Categoría personalizada 64",
    "CUSTOM_65": "Categoría personalizada 65",
    "CUSTOM_66": "Categoría personalizada 66",
    "CUSTOM_67": "Categoría personalizada 67",
    "CUSTOM_68": "Categoría personalizada 68",
    "CUSTOM_69": "Categoría personalizada 69",
    "CUSTOM_70": "Categoría personalizada 70",
    "CUSTOM_71": "Categoría personalizada 71",
    "CUSTOM_72": "Categoría personalizada 72",
    "CUSTOM_73": "Categoría personalizada 73",
    "CUSTOM_74": "Categoría personalizada 74",
    "CUSTOM_75": "Categoría personalizada 75",
    "CUSTOM_76": "Categoría personalizada 76",
    "CUSTOM_77": "Categoría personalizada 77",
    "CUSTOM_78": "Categoría personalizada 78",
    "CUSTOM_79": "Categoría personalizada 79",
    "CUSTOM_80": "Categoría personalizada 80",
    "CUSTOM_81": "Categoría personalizada 81",
    "CUSTOM_82": "Categoría personalizada 82",
    "CUSTOM_83": "Categoría personalizada 83",
    "CUSTOM_84": "Categoría personalizada 84",
    "CUSTOM_85": "Categoría personalizada 85",
    "CUSTOM_86": "Categoría personalizada 86",
    "CUSTOM_87": "Categoría personalizada 87",
    "CUSTOM_88": "Categoría personalizada 88",
    "CUSTOM_89": "Categoría personalizada 89",
    "CUSTOM_90": "Categoría personalizada 90",
    "CUSTOM_91": "Categoría personalizada 91",
    "CUSTOM_92": "Categoría personalizada 92",
    "CUSTOM_93": "Categoría personalizada 93",
    "CUSTOM_94": "Categoría personalizada 94",
    "CUSTOM_95": "Categoría personalizada 95",
    "CUSTOM_96": "Categoría personalizada 96",
    "CUSTOM_97": "Categoría personalizada 97",
    "CUSTOM_98": "Categoría personalizada 98",
    "CUSTOM_99": "Categoría personalizada 99",
    "CUSTOM_AUP_FREQUENCY": "Frecuencia de UAP personalizada (días)",
    "CUSTOM_DNS_SERVER": "Servidor DNS personalizado",
    "CUSTOM_OPTION": "Opción personalizada",
    "CUSTOM": "Personalizado",
    "CUSTOMIZE_COLS": "Personalizar columnas",
    "CUSTOMIZE_COLUMNS": "Personalizar columnas",
    "CYPRUS_ASIA_NICOSIA": "Asia/Nicosia",
    "CYPRUS": "Chipre",
    "CZECH_REPUBLIC_EUROPE_PRAGUE": "Europa/Praga",
    "CZECH_REPUBLIC": "República Checa",
    "CZECHIA": "República Checa",
    "DASHBOARD": "Cuadro de Mandos",
    "Data Centers": "Centros de datos",
    "DATA_CENTER": "Centro de datos",
    "DATA_CENTERS": "Centros de datos",
    "DATA_COLLECTION": "Recogida de datos",
    "DATA_CONNECTION_FAIL": "Error de conexión de datos de SVPN.",
    "DATA_TYPE": "Tipo de datos",
    "DATACENTER": "Centro de datos",
    "DAYS": "Día",
    "DC": "Public Service Edge",
    "DEDICATED_BANDWIDTH": "Ancho de banda dedicado",
    "DEFAUL_GATEWAY_IP_ADDRESS": "Dirección IP de pasarela predeterminada",
    "DEFAULT_AWS_REGIONS": "Todas las regiones",
    "DEFAULT_AZURE_REGIONS": "Ninguno",
    "DEFAULT_GATEWAY": "Pasarela predeterminada",
    "DEFAULT_GW": "Pasarela predeterminada",
    "DEFAULT_LEASE_TIME": "Tiempo de arrendamiento predeterminado (s)",
    "DEFAULT_NAMESPACE": "Defecto",
    "DEFAULT_PREFIX": "Prefijo predeterminado",
    "DEFAULT_REGIONS": "Todas las ubicaciones",
    "DEFAULT_ROUTE_CAN_NOT_BE_SET_AS_STATIC_ROUTE": "No se ha podido utilizar la ruta predeterminada como ruta estática.",
    "DEFAULT": "Valor predeterminado",
    "DEFINITION": "Definición",
    "DELETE_5G_DEPLOYMENT_CONFIGURATION": "¿Está seguro de que desea eliminar esta configuración de implementación? Las implementaciones solo se pueden eliminar si están asociadas a grupos de conectores. Estos cambios no se pueden deshacer. ",
    "DELETE_5G_USER_PLANE": "¿Está seguro de que desea eliminar esta función de plano de usuario? Estos cambios no se pueden deshacer. ",
    "DELETE_ACCOUNT_DESCRIPTION": "Al eliminar la cuenta, Zscaler eliminará toda la información de acceso a su cuenta. Tendrá que volver a proporcionar los permisos si decide añadir esta cuenta nuevamente.",
    "DELETE_ACCOUNT": "Eliminar cuenta",
    "DELETE_ACCOUNTS": "Eliminar cuentas",
    "DELETE_ADMIN_MESSAGE": "¿Desea también eliminar a este administrador de la lista de usuarios?{1}Esta acción {2}no se puede deshacer.{3}",
    "DELETE_API_KEY_CONFIRMATION_MESSAGE": "La eliminación de la clave de API la invalida de inmediato. Esto no puede deshacerse.",
    "DELETE_API_KEY_CONFIRMATION_TITLE": "Eliminar clave de API",
    "DELETE_API_KEY_TOOLTIP": "Eliminar la clave de API",
    "DELETE_APPLICATION": "Eliminar aplicación",
    "DELETE_BC_CONFIRMATION": "¿Está seguro de que desea eliminar el siguiente Branch Connector? Los cambios no se pueden deshacer.",
    "DELETE_BC_GROUP_CONFIRMATION_ALERT": "Esto no elimina la VM. Asegúrese de que elimina los recursos por separado.",
    "DELETE_BC_GROUP_CONFIRMATION": "¿Está seguro de que desea eliminar el siguiente grupo de Branch Connectors? Los cambios no se pueden deshacer.",
    "DELETE_BC_GROUP": "Eliminar grupo de Branch Connectors",
    "DELETE_CC_CONFIRMATION": "¿Está seguro de que desea eliminar el siguiente Cloud Connector? Los cambios no se pueden deshacer.",
    "DELETE_CC_GROUP_CONFIRMATION_ALERT": "Esto no elimina la VM en la nube pública. Asegúrese de que elimina los recursos por separado.",
    "DELETE_CC_GROUP_CONFIRMATION": "¿Está seguro de que desea eliminar el siguiente grupo de Cloud Connectors? Los cambios no se pueden deshacer.",
    "DELETE_CC_GROUP": "Eliminar grupo de Cloud Connectors",
    "DELETE_CONFIRMATION_MESSAGE": "Confirme que desea eliminar el recurso o recursos.",
    "DELETE_CONFIRMATION_MESSAGE1": "¿Está seguro de que desea eliminar este recurso?",
    "DELETE_CONFIRMATION_MESSAGE2": "¿Está seguro de que desea eliminar este recurso?",
    "DELETE_CONFIRMATION": "Confirmación de eliminación",
    "DELETE_DATA_OLLECTION_TEXT": "Al eliminar la cuenta, Zscaler eliminará toda la información de acceso a su cuenta. Tendrá que volver a proporcionar los permisos si decide añadir esta cuenta nuevamente.",
    "DELETE_DATA_OLLECTION": "Eliminar recopilación de datos",
    "DELETE_GROUP_CONFIRMATION": "Confirmación de eliminación de grupo",
    "DELETE_GROUP_MESSAGE_WITH_CC_GROUP": "Este grupo de cuentas está asociado a {1} cuenta(s) y {2} grupo(s) de Cloud Connectors. Si se elimina este grupo de cuentas, el grupo o grupos de Cloud Connectors {2} no podrá(n) utilizar las etiquetas de carga de trabajo de la(s) cuenta(s) {1}.",
    "DELETE_GROUP_MESSAGE": "{1}¿Está seguro de que desea eliminar este grupo?{2}\n\nAl eliminar este grupo, se eliminará de todas las políticas relevantes. Asegúrese de haber revisado el impacto antes de eliminar el grupo.",
    "DELETE_GROUP": "Eliminar grupo",
    "DELETE_INTERFACE_TEXT": "¿Está seguro de que desea eliminar la configuración de la interfaz?\n\nEsta acción no se puede deshacer.",
    "DELETE_INTERFACE": "Eliminar interfaz",
    "DELETE_PORT_TEXT": "¿Está seguro de que desea eliminar el puerto y toda la configuración de su interfaz?\n\nEsta acción no se puede deshacer.",
    "DELETE_PORT": "Eliminar puerto",
    "DELETE_TENANT_DESCRIPTION": "Al eliminar el inquilino, Zscaler eliminará toda la información de acceso a su inquilino. Tendrá que volver a proporcionar los permisos si decide añadir este inquilino nuevamente.",
    "DELETE_TENANT": "Eliminar inquilino",
    "DELETE_THIS_ITEM": "Eliminar este elemento.",
    "DELETE_VDI_GROUP_TEXT": "Al eliminar el grupo de dispositivos, Zscaler eliminará toda la información relacionada con este grupo. Tendrá que volver a crear el grupo si decide añadir esta cuenta de nuevo.",
    "DELETE_VDI_GROUP": "Eliminar grupo de VDI",
    "DELETE_ZERO_TRUST_GATEWAY": "Eliminar pasarela de Zero Trust",
    "DELETE_ZTG_DESCRIPTION": "Al eliminar la pasarela de Zero Trust, Zscaler eliminará toda la información de acceso a su pasarela. Tendrá que volver a proporcionar los permisos si decide añadir esta pasarela nuevamente.",
    "DELETE": "Eliminar",
    "DELETING": "Eliminando",
    "DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA": "África/Kinshasa",
    "DENIED": "Denegado",
    "DENMARK_EUROPE_COPENHAGEN": "Europa/copenhague",
    "DENMARK": "Dinamarca",
    "DEPARTMENT": "Departamento",
    "DEPLOY_AS_GATEWAY": "Implementar como pasarela",
    "DEPLOY_NSS_VIRTUAL_APPLIANCE": "Implementar dispositivo virtual NSS",
    "DEPLOYED_FILTERED": "Implementado (filtrado)",
    "DEPLOYED_OTHER_REGION": "Se han implementado otras regiones",
    "DEPLOYED": "Implementado",
    "DEPLOYMENT_CONFIGURATION_NAME": "Nombre de configuración de implementación",
    "DEPLOYMENT_CONFIGURATION_WITH_ZPA": "Plantilla de despliegue inicial con equilibrador de carga",
    "DEPLOYMENT_CONFIGURATION": "Plantilla de implementación inicial",
    "DEPLOYMENT_DETAILS": "Detalles de implementación",
    "DEPLOYMENT_NAME": "Nombre de implementación",
    "DEPLOYMENT_STATUS": "Estado de implementación",
    "DEPLOYMENT_TEMPLATES_DEPRECATED": "Las plantillas de implementación están disponibles en la página pública de Zscaler {0}GitHub{1} en los enlaces mencionados a continuación. Se realiza un seguimiento de los cambios en esas páginas. Para obtener más información, consulte el {2}Portal de ayuda de Zscaler.{3}",
    "DEPLOYMENT_TEMPLATES": "Plantillas de implementación",
    "DEPLOYMENT_TYPE": "Tipo de implementación",
    "DESCRIPTION_MAX_LIMIT_ERROR": "Este campo no puede contener más de 10240 caracteres",
    "DESCRIPTION_OPTIONAL": "Descripción opcional",
    "DESCRIPTION_PARENTHESIS_OPTIONAL": "Descripción (opcional)",
    "DESCRIPTION": "Descripción ",
    "DESELECT_ALL": "Deseleccionar todo",
    "DESIRED_CAPACITY": "Capacidad deseada",
    "DESTINATION_ADDRESSES": "Direcciones de destino",
    "DESTINATION_COUNTRIES": "Países de destino",
    "DESTINATION_COUNTRY": "País de destino",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Dominios de destino",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Grupo de dominios de destino",
    "DESTINATION_GROUPS": "Grupos de destino",
    "DESTINATION_IP_AND_FQDN_ACCDRESSES": "Dirección IP / FQDN de destino",
    "DESTINATION_IP_AND_FQDN_GROUPS": "Grupo de IP / FQDN de destino",
    "DESTINATION_IP_AND_FQDN": "IP / FQDN de destino",
    "DESTINATION_IP_GROUP": "Grupo de destino",
    "DESTINATION_IP_GROUPS": "Grupos de IP de destino",
    "DESTINATION_IP": "IP de destino",
    "DESTINATION_IPV4_GROUPS": "Grupos de IPv4 de destino",
    "DESTINATION_STATUS": "Estado de destino",
    "DESTINATION": "Destino",
    "DEVICE_APP_VER": "Versión de la aplicación del dispositivo",
    "DEVICE_CRITERIA": "Criterios de dispositivo",
    "DEVICE_DETAILS": "Detalles de dispositivo",
    "DEVICE_GROUP_TYPE": "Tipo de grupo de dispositivos",
    "DEVICE_HOST_NAME": "Nombre de host del dispositivo",
    "DEVICE_ID": "ID de dispositivo ",
    "DEVICE_INFO": "Información del dispositivo",
    "DEVICE_METRICS": "Recurso",
    "DEVICE_MODEL": "Modelo del dispositivo",
    "DEVICE_NAME": "Nombre del dispositivo",
    "DEVICE_OS_TYPE": "Tipo de SO del dispositivo",
    "DEVICE_OS_VER": "Tipo de SO del dispositivo",
    "DEVICE_OWNER": "Propietario del dispositivo",
    "DEVICE_PLATFORM": "Plataforma del dispositivo",
    "DEVICE_PORT": "Puerto de dispositivo",
    "DEVICE_SELECTION": "Selección de dispositivo",
    "DEVICE_SERIAL_NO": "N.º de serie del dispositivo",
    "DEVICE_SERIAL_NUMBER": "Número de serie del dispositivo",
    "DEVICE_TYPE": "Tipo de dispositivo",
    "DEVICES_CRITERIA_TEXT": "Seleccione los criterios que se utilizarán para agrupar los dispositivos VDI.",
    "DEVICES_CRITERIA": "Criterios de dispositivos",
    "DEVICES": "Dispositivos",
    "DEVO": "Devo",
    "DHCP_ADDRESS_RANGE": "Rango de direcciones DHCP",
    "DHCP_DESC": "El protocolo DHCP se utiliza para configurar automáticamente los parámetros de red de una estación",
    "DHCP_MANAGEMENT_IP": "IP de gestión de DHCP",
    "DHCP_OPTIONS": "Opciones de DHCP",
    "DHCP_SERVER": "Servidor DHCP",
    "DHCP_SERVICE_IP": "IP de servicio de DHCP",
    "DHCP": "DHCP",
    "DINING_AND_RESTAURANT": "Comidas & restaurantes",
    "DIRECT_THROUGHPUT_KBPS_SESSION": "Directo (velocidad de kbps / sesión)",
    "DIRECT": "DIRECTO",
    "DIRECTION": "Dirección de TS",
    "DIRECTORY_ID": "ID de directorio",
    "DISABLE_BRANCH_CONNECTOR_CONFIRMATION": "¿Está seguro de que desea desactivar este Branch Connector?",
    "DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "¿Está seguro de que desea desactivar este grupo de Branch Connectors? Esto desactivaría todos los {0} Branch Connectors que pertenecen a este grupo",
    "DISABLE_BRANCH_CONNECTOR_GROUP": "Desactivar grupo de Branch Connectors",
    "DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "¿Está seguro de que desea desactivar los {0} Branch Connectors seleccionados?",
    "DISABLE_BRANCH_CONNECTOR_SELECTED": "Desactivar todos los Branch Connectors seleccionados",
    "DISABLE_BRANCH_CONNECTOR": "Desactivar Branch Connector",
    "DISABLE_CLOUD_CONNECTOR_CONFIRMATION": "¿Está seguro de que desea desactivar este Cloud Connector?",
    "DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "¿Está seguro de que desea desactivar este grupo de Cloud Connectors? Esto desactivaría los {0} Cloud Connectors que pertenecen a este grupo",
    "DISABLE_CLOUD_CONNECTOR_GROUP": "Desactivar grupo de Cloud Connectors",
    "DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "¿Está seguro de que desea desactivar los {0} Cloud Connectors seleccionados?",
    "DISABLE_CLOUD_CONNECTOR_SELECTED": "Desactivar todos los Cloud Connectors seleccionados",
    "DISABLE_CLOUD_CONNECTOR": "Desactivar Cloud Connector",
    "DISABLE_DATA_COLLECTION_DESCRIPTION": "Desactivar la sincronización impedirá que Zscaler obtenga la información de identificador más reciente. Además, excluirá los recursos de esta cuenta para que no se incluyan en grupos de cargas de trabajo.",
    "DISABLE_DATA_COLLECTION": "Desactivar recopilación de datos",
    "DISABLE_POLICY_INFORMATION": "Desactivar información de reenvío",
    "DISABLE_TIPS_MESSAGE": "Mi perfil, para desactivar la información de política.",
    "DISABLE": "Desacticar",
    "DISABLED": "Desactivado",
    "DISABLING": "Desactivando",
    "DISCOVERY_SERVICE_STATUS": "Estado del servicio de detección",
    "DISCUSSION_FORUMS": "Foros de discusión",
    "DISK_STORAGE": "Almacenamiento en disco",
    "DISMISS": "Descartar",
    "DISPLAY": "Mostrar",
    "DJIBOUTI_AFRICA_DJIBOUTI": "Africa/Djibouti",
    "DJIBOUTI": "Djibouti",
    "DNS_ACTIONS": "Acciones",
    "DNS_ACTIVITY": "Actividad de DNS",
    "DNS_APPLICATION_CATEGORIES": "Categorías de túneles de DNS y aplicaciones de red",
    "DNS_APPLICATION_CATEGORY": "Categoría de aplicaciones de DNS",
    "DNS_APPLICATION_GROUP": "Grupo de aplicaciones de DNS",
    "DNS_APPLICATION": "Aplicación de DNS",
    "DNS_BLOCKED_TRAFFIC_OVERVIEW": "Resumen de tráfico de DNS bloqueado",
    "DNS_CACHE": "Caché de DNS",
    "DNS_CONTROL_RECOMMENDED_POLICY": "Política recomendada de control de DNS",
    "DNS_CONTROL_TIPS_DESC": "Puede definir las reglas que controlan las solicitudes y respuestas de DNS.",
    "DNS_CONTROL_TIPS_TITLE": "Configurar política de control de DNS",
    "DNS_CONTROL": "Control de DNS",
    "DNS_DESC": " El protocolo DNS se utiliza para traducir nombres de sitios de internet (www.sitio.com) a direcciones IP y viceversa",
    "DNS_DESTINATION": "Destino",
    "DNS_DETAILS": "Detalles de DNS",
    "DNS_ERROR_CODE": "Código de error de DNS",
    "DNS_ERROR_STATUS": "Estado de error de DNS",
    "DNS_FILTERING_RULE": "Regla de filtrado de DNS",
    "DNS_FILTERING": "Filtrado de DNS",
    "DNS_FILTERS": "Filtros de DNS",
    "DNS_GATEWAY": "Pasarela DNS",
    "DNS_INSIGHTS": "Perspectivas de DNS",
    "DNS_IPV6_CHANGE": "Permitir manejo correcto de peticiones IPv6 a DNS",
    "DNS_MONITOR": "Monitor de DNS",
    "DNS_NETWORK_APPLICATION": "Túneles de DNS y aplicaciones de red",
    "DNS_NW_APP_CATEGORY": "Categorías de túneles de DNS y aplicaciones de red",
    "DNS_NW_APP": "Túneles de DNS y aplicaciones de red",
    "DNS_OVER_HTTPS": "Servicios de DNS sobre HTTPS",
    "DNS_OVERVIEW": "Resumen de DNS",
    "DNS_POLICIES": "Políticas de DNS",
    "DNS_POLICY": "Política de DNS",
    "DNS_REQ_RESP_ACTION": "Acción",
    "DNS_REQ_TYPE": "Tipo de solicitud de DNS",
    "DNS_REQUEST_TYPE": "Tipo de solicitud de DNS",
    "DNS_REQUEST_TYPES": "Tipos de solicitudes de DNS",
    "DNS_RES_TYPE": "Tipo de respuesta de DNS",
    "DNS_RESOLVED_BY_ZPA": "Resuelto por ZPA",
    "DNS_RESOLVER": "Resolución de problemas",
    "DNS_RESPONSE_CODES": "Códigos de respuesta de DNS",
    "DNS_RESPONSE_TYPE": "Tipo de respuesta de DNS",
    "DNS_RESPONSE": "Respuesta de DNS",
    "DNS_RESPONSES": "Respuestas de DNS",
    "DNS_RULE_NAME_DEFAULT": "DNS_{0}",
    "DNS_RULE_NAME": "Nombre de regla de DNS",
    "DNS_RULE": "Nombre de la regla",
    "DNS_SERVER_IP_ADDRESS": "Dirección IP de servidor DNS",
    "DNS_SERVER_IP_ADDRESS1": "Dirección IP 1 de servidor DNS",
    "DNS_SERVER_IP_ADDRESS2": "Dirección IP 2 de servidor DNS",
    "DNS_SERVER_IP_ADDRESSES": "Direcciones IP de servidor DNS",
    "DNS_SERVER_IP_GROUPS": "Grupos de IP de servidor DNS",
    "DNS_SERVER_ONE": "Servidor de DNS 1",
    "DNS_SERVER_TWO_OPTIONAL": "Servidor de DNS 2 (opcional)",
    "DNS_SERVER_TWO": "Servidor de DNS 2",
    "DNS_SERVER": "Servidor de DNS",
    "DNS_SERVICES": "Servicios de DNS",
    "DNS_SOURCE": "Fuente",
    "DNS_TCP_PORTS_DNS_SPECIFIC": "Puertos de DNS (TCP) para reglas específicas de DNS",
    "DNS_TIMEOUT": "Timeout de resolución DNS",
    "DNS_TOP_BLOCKED_BY_LOCATION": "Tráfico bloqueado por ubicación",
    "DNS_TOP_BLOCKED_BY_RULE": "Tráfico bloqueado por regla",
    "DNS_TOP_BLOCKED_BY_USER": "Tráfico bloqueado por usuario",
    "DNS_TRAFFIC_BY_DEPARTMENT": "Tráfico de DNS por departamento",
    "DNS_TRAFFIC_BY_LOCATION": "Tráfico por ubicación",
    "DNS_TRAFFIC_BY_USER": "Tráfico por usuario",
    "DNS_TRAFFIC_OVERVIEW": "Resumen global del tráfico de DNS",
    "DNS_TRANSACTION_POLICY": "Política de transacciones de DNS",
    "DNS_TRANSACTION_RULE": "Regla de transacciones de DNS",
    "DNS_TRANSACTION_TREND": "Tendencia de transacciones de DNS",
    "DNS_TRANSACTION": "Transacción",
    "DNS_UDP_PORTS_DNS_SPECIFIC": "Puertos de DNS (UDP) para reglas específicas de DNS",
    "DNS": "DNS",
    "DNSLOG": "Logs de DNS",
    "DNSREQ_A": "Una dirección de host",
    "DNSREQ_AAAA": "Dirección IP6",
    "DNSREQ_AFSDB": "Para ubicación de base de datos AFS",
    "DNSREQ_CNAME": "El nombre canónico de un alias",
    "DNSREQ_DNSKEY": "Clave pública de DNS",
    "DNSREQ_DS": "Firmante de delegación",
    "DNSREQ_HINFO": "Información de host",
    "DNSREQ_HIP": "Protocolo de identidad de host",
    "DNSREQ_ISDN": "Para dirección ISDN",
    "DNSREQ_LOC": "Información de ubicación",
    "DNSREQ_MB": "Un nombre de dominio de buzón",
    "DNSREQ_MG": "Un miembro del grupo de correo",
    "DNSREQ_MINFO": "Información de buzón o lista de correo",
    "DNSREQ_MR": "Un nombre de dominio de cambio de nombre de correo",
    "DNSREQ_MX": "Intercambio de correo",
    "DNSREQ_NAPTR": "Puntero de autoridad de denominación",
    "DNSREQ_NS": "Un servidor de nombres autorizado",
    "DNSREQ_NSEC": "Extensiones de seguridad de DNS",
    "DNSREQ_PTR": "Un puntero de nombre de dominio",
    "DNSREQ_RP": "Para la persona responsable",
    "DNSREQ_RT": "Para ruta de paso",
    "DNSREQ_SOA": "Marca el comienzo de una zona de autoridad",
    "DNSREQ_SRV": "Selección de servidor",
    "DNSREQ_TXT": "Cadenas de texto",
    "DNSREQ_UNKNOWN": "Tipo de DNS no asignado por firewall ZS",
    "DNSREQ_WKS": "Una descripción de un servicio ampliamente conocido",
    "DNSRES_CNAME": "El tipo de respuesta es CNAME",
    "DNSRES_IPV4": "El tipo de respuesta es IPV4",
    "DNSRES_IPV6": "El tipo de respuesta es IPV6",
    "DNSRES_SRV_CODE": "El tipo de respuesta tiene establecido un código de error del servidor",
    "DNSRES_ZSCODE": "El tipo de respuesta tiene establecido un código personalizado de Zscaler",
    "DOES_NOT_CONTAINS": "No contiene",
    "DOES_NOT_ENDS_WITH": "No termina por",
    "DOES_NOT_STARTS_WITH": "No empieza por",
    "DOHTTPS_RULE": "DNS sobre HTTPS",
    "DOMAIN_CATEGORY": "Categoría de dominio",
    "DOMAIN_NAME": "Nombre de dominio",
    "DOMAIN": "Dominio",
    "DOMAINS": "Dominios",
    "DOMINICA_AMERICA_DOMINICA": "America/Dominica",
    "DOMINICA": "Dominica",
    "DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO": "America/Santo Domingo",
    "DOMINICAN_REPUBLIC": "República Dominicana",
    "DONE": "Hecho",
    "DONT_SHOW_AGAIN": "No volver a mostrar",
    "DOWN": "Abajo",
    "DOWNLOAD_AWS_CLOUDFORMATION_TEMPLATE": "Descargar plantilla de Cloudformation de AWS",
    "DOWNLOAD_CERTIFICATE": "Descargar certificado",
    "DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION": "Descargar plantilla de Cloudformation para su posterior ejecución",
    "DOWNLOAD_CSV": "Descargar (.csv)",
    "DOWNLOAD_ERROR": "No es posible descargar actualización de la nube de Zscaler. Cloud Connector en buen estado",
    "DOWNLOAD_MBPS": "Descarga (Mbps)",
    "DOWNLOAD_MIB_FILES": "Descargar archivos MIB",
    "DOWNLOAD_PROGRESS": "Progreso de descarga",
    "DOWNLOAD": "Descargar ",
    "DPDRCV": "DPD recibido",
    "DR_CONGO": "República Democrática del Congo",
    "DROP": "Tirar",
    "DSTN_DOMAIN": "Dominio comodín",
    "DSTN_FQDN": "FQDN",
    "DSTN_IP": "Dirección IP",
    "DSTN_OTHER": "Otros",
    "DSTN_WILDCARD_FQDN": "Dominio comodín",
    "DUPLICATE_IP_ADDRESS": "Esta dirección IP ya se está utilizando",
    "DUPLICATE_IP_ADDRESSES": "Direcciones IP duplicadas",
    "DUPLICATE_ITEM": "El nombre ya está en uso",
    "DUPLICATE_VLAN_ID": "ID de VLAN duplicado.",
    "DYNAMIC_DNS": "Host DNS dinámico",
    "DYNAMIC_LOCATION_GROUPS": "Grupos de ubicaciones dinámicos",
    "DYNAMIC": "Dinámico",
    "EASTASIA": "(Asia Pacífico) Este de Asia",
    "EASTASIASTAGE": "(Asia Pacífico) Este de Asia (Stage)",
    "EASTUS": "(EE. UU.) Este de EE. UU.",
    "EASTUS2": "(EE. UU.) Este de EE. UU. 2",
    "EASTUS2EUAP": "(EE. UU.) Este de EE. UU. 2 EUAP",
    "EASTUS2STAGE": "(EE. UU.) Este de EE. UU. 2 (Stage)",
    "EASTUSSTAGE": "(EE. UU.) Este de EE. UU. (Stage)",
    "EBS_STORAGE": "Almacenamiento EBS",
    "EC_ACC_ID": "ID de cuenta de AWS",
    "EC_AVAILABILITY_ZONE": "Zona de disponibilidad",
    "EC_AWS_AVAILABILITY_ZONE": "Zona de disponibilidad de AWS",
    "EC_AWS_REGION": "Región de AWS",
    "EC_AZURE_AVAILABILITY_ZONE": "Zona de disponibilidad de Azure",
    "EC_DEVICE_APP_VERSION": "Versión de la aplicación del dispositivo",
    "EC_DEVICE_HOSTNAME": "Nombre de host del dispositivo",
    "EC_DEVICE_ID": "Nombre del dispositivo",
    "EC_DEVICE_OS_TYPE": "Tipo de SO del dispositivo",
    "EC_DEVICE_TYPE": "Tipo de dispositivo",
    "EC_DNS_GW_FLAG": "Indicador de pasarela DNS",
    "EC_DNS_GW_NAME": "Nombre de la pasarela DNS",
    "EC_DNS": "DNS CC",
    "EC_DNSLOG": "Logs de DNS",
    "EC_EVENTLOG": "Evento",
    "EC_FORWARDING_TYPE": "Tipo de reenvío",
    "EC_FW_RULE": "Regla de reenvío",
    "EC_GROUP": "Grupo de conectores de nube",
    "EC_INSTANCE_NAME": "Instancia de conector de nube",
    "EC_INSTANCE": "Instancia de conector de nube",
    "EC_PLATFORM": "Plataforma",
    "EC_PROJECT_ID": "ID del proyecto de GCP",
    "EC_RDRRULESLOT": "Reglas de reenvío de tráfico",
    "EC_SELFRULESLOT": "Reglas de reenvío de registro y control",
    "EC_SOURCE_IP": "IP de origen de Cloud Connector",
    "EC_SOURCE_PORT": "Puerto de origen de Cloud Connector",
    "EC_SUBSCRIPTION_ID": "ID de suscripción a Azure",
    "EC_TRAFFIC_DIRECTION": "Tipo de solicitud",
    "EC_TRAFFIC_TYPE": "Tipo de tráfico",
    "EC_TS_DIRECTION": "Dirección de TS",
    "EC_UI": "Interfaz de usuario de conector de nube",
    "EC_VM": "VM de Cloud Connector",
    "EC_VMNAME": "Nombre de VM de Cloud Connector",
    "EC2_INSTANCE_TYPE": "Tipo de instancia EC2",
    "ECDIRECTSCTPXFORM": "Directo con traducción SCTP",
    "ECHO_DESC": "El protocolo es un servicioen la suite del Protocolo de Internet definida en el RFC 862. Se propuso originalmente para probar y medir tiempos de ida y vuelta en redes IP.",
    "ECHO": "Eco",
    "ECHOREP": "Respuesta de Eco",
    "ECHOREQ": "Solicitud de Eco",
    "ECHOSIGN": "AdobeEchoSign",
    "ECLOG": "Registros de sesiones",
    "ECSELF": "GW para tráfico originado por el conector",
    "ECUADOR_AMERICA_GUAYAQUIL": "America/Guayaquil",
    "ECUADOR_PACIFIC_GALAPAGOS": "Pacific/Galapagos",
    "ECUADOR": "Ecuador",
    "ECZPA": "ZPA",
    "ECZPASCTPXFORM": "ZPA con traducción SCTP",
    "ECZPAXSCTPXFORM": "Túnel a ZPA de conector con transformación SCTP",
    "EDGE_CONNECTOR_ADMIN_MANAGEMENT": "Gestión de Administradores",
    "EDGE_CONNECTOR_ADMIN": "Administrador de conector de nube",
    "EDGE_CONNECTOR_CCA_DEVICE": "CCA",
    "EDGE_CONNECTOR_CLOUD_PROVISIONING": "Aprovisionamiento de conectores de nube",
    "EDGE_CONNECTOR_DASHBOARD": "Cuadro de Mandos",
    "EDGE_CONNECTOR_FORWARDING": "Reenvío (tráfico, DNS y registros)",
    "EDGE_CONNECTOR_LOCATION_MANAGEMENT": "Gestión de ubicación",
    "EDGE_CONNECTOR_NSS_CONFIGURATION": "Registro de NSS",
    "EDGE_CONNECTOR_POLICY_CONFIGURATION": "Configuración de política",
    "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Gestión de configuración de nube pública",
    "EDGE_CONNECTOR_TEMPLATE": "Plantilla (ubicación y aprovisionamiento)",
    "EDGE_CONNECTOR_VM_SIZE": "Tamaño de VM",
    "EDGE_ONLY": "Solo Edge",
    "EDGECONNECTOR_NAME": "Nombre de Cloud Connector",
    "EDIT_5G_DEPLOYMENT": "Editar configuración de implementación",
    "EDIT_API_KEY_TOOLTIP": "Editar la clave de API",
    "EDIT_APPLIANCE": "Editar dispositivo",
    "EDIT_AWS_ACCOUNT": "Editar cuenta de AWS",
    "EDIT_AWS_CLOUD_ACCOUNT": "Editar cuenta de nube de AWS",
    "EDIT_AWS_GROUP": "Editar grupo de AWS",
    "EDIT_AZURE_CLOUD_ACCOUNT": "Editar cuenta de nube de Azure",
    "EDIT_AZURE_TENANT": "Editar inquilino de Azure",
    "EDIT_BC_PROVISIONING_TEMPLATE": "Editar plantilla de aprovisionamiento de conector de sucursal",
    "EDIT_CLOUD_APP_PROVIDER": "Editar proveedor de aplicación de nube",
    "EDIT_CLOUD_CONNECTOR_ADMIN": "Editar administrador de Cloud Connector",
    "EDIT_CLOUD_CONNECTOR_ROLE": "Editar rol de Cloud Connector",
    "EDIT_CLOUD_CONNECTOR": "Editar Cloud Connector",
    "EDIT_CLOUD_CONNECTORS": "Editar conectores",
    "EDIT_CLOUD_SERVICE_API_KEY": "Editar clave de API de servicio de nube",
    "EDIT_CONFIRMATION_DEPLOYED_GATEWAY": "Este dispositivo está en estado Implementado. Cualquier cambio en la configuración puede afectar al tráfico. ¿Está seguro de que desea continuar?",
    "EDIT_CONFIRMATION_PREDEFINED_RULE": "Esta regla predefinida solo es aplicable a grupos/ubicaciones de BC en Modo de pasarela.",
    "EDIT_CONFIRMATION": "Confirmación de edición",
    "EDIT_CONNECTORS": "Editar conectores",
    "EDIT_DESTINATION_IP_GROUP": "Editar grupo de IP de destino",
    "EDIT_DNS_GATEWAY": "Editar pasarela DNS",
    "EDIT_DNS_POLICIES": "Editar regla de filtrado de DNS",
    "EDIT_DYNAMIC_VDI_GROUP": "Editar grupo de VDI dinámico",
    "EDIT_EC_NSS_CLOUD_FEED": "Editar feed de nube NSS",
    "EDIT_EC_NSS_FEED": "Editar feed de NSS",
    "EDIT_EC_NSS_SERVER": "Editar servidor NSS",
    "EDIT_EDGECONNECTOR": "Editar Cloud Connector",
    "EDIT_IP_POOL": "Editar grupo de IP",
    "EDIT_LOCATION_TEMPLATE": "Editar plantilla de ubicación",
    "EDIT_LOCATIONS": "Editar ubicaciones",
    "EDIT_LOG_AND_CONTROL_FORWARDING_RULE": "Editar regla de reenvío de registro y control",
    "EDIT_LOG_AND_CONTROL_GATEWAY": "Editar pasarela de registro y control",
    "EDIT_NETWORK_SERVICE_GROUP": "Añadir grupo de servicios de red",
    "EDIT_NETWORK_SERVICE": "Editar servicio de red",
    "EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE": "La modificación de la clave de API invalida de inmediato la clave existente. Debe sustituir cualquier referencia a la clave antigua por la nueva.",
    "EDIT_PHYSICAL_BRANCH_DEVICE": "Editar dispositivo de sucursal físico",
    "EDIT_PROVISIONING_TEMPLATE": "Editar plantilla de aprovisionamiento de Cloud Connector",
    "EDIT_SOURCE_IP_GROUP": "Editar grupo de IP de origen",
    "EDIT_TRAFFIC_FWD_POLICIES": "Editar reglas de reenvío de tráfico",
    "EDIT_UPF": "Editar función de plano de usuario",
    "EDIT_VDI_AGENT_FORWARDING_PROFILE": "Editar perfil de reenvío de VDI",
    "EDIT_VDI_TEMPLATE": "Editar plantilla VDI",
    "EDIT_VIRTUAL_BRANCH_DEVICE": "Editar dispositivo de sucursal virtual",
    "EDIT_ZERO_TRUST_GATEWAY": "Editar pasarela de Zero Trust",
    "EDIT_ZIA_GATEWAY": "Editar pasarela de ZIA",
    "EDIT_ZT_DEVICE": "Editar dispositivo ZT",
    "EDIT": "Editar",
    "EGRESS_DETAILS": "Detalles de salida",
    "EGYPT_AFRICA_CAIRO": "Africa/Cairo",
    "EGYPT": "Egipto",
    "EITHER_REQ_RESP_BLOCK": "Bloquear",
    "EITHER_REQ_RESP_REDIRECT_NO_BLOCK": "Redirigir ",
    "EL_SALVADOR_AMERICA_EL_SALVADOR": "America/El Salvador",
    "EL_SALVADOR": "El Salvador",
    "EMAIL_HOST": "Webmail",
    "EMAIL": "Correo electrónico",
    "EMPTY_RESP": "Respuesta de DNS sin error pero con la sección de respuesta vacía",
    "ENABLE_AUP": "Activar AUP",
    "ENABLE_BRANCH_CONNECTOR_CONFIRMATION": "¿Está seguro de que desea activar este Branch Connector?",
    "ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "¿Está seguro de que desea activar este grupo de Branch Connectors? Esto activaría todos los {0} Branch Connectors que pertenecen a este grupo",
    "ENABLE_BRANCH_CONNECTOR_GROUP": "Activar grupo de Branch Connectors",
    "ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "¿Está seguro de que desea activar los {0} Branch Connectors seleccionados?",
    "ENABLE_BRANCH_CONNECTOR_SELECTED": "Activar todos los Branch Connectors seleccionados",
    "ENABLE_BRANCH_CONNECTOR": "Activar Branch Connector",
    "ENABLE_CAUTION": "Activar Precaución",
    "ENABLE_CLOUD_CONNECTOR_CONFIRMATION": "¿Está seguro de que desea activar este Cloud Connector?",
    "ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "¿Está seguro de que desea activar este grupo de Cloud Connectors? Esto activaría los {0} Cloud Connectors que pertenecen a este grupo",
    "ENABLE_CLOUD_CONNECTOR_GROUP": "Activar grupo de Cloud Connectors",
    "ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "¿Está seguro de que desea activar los {0} Cloud Connectors seleccionados?",
    "ENABLE_CLOUD_CONNECTOR_SELECTED": "Activar todos los Cloud Connectors seleccionados",
    "ENABLE_CLOUD_CONNECTOR": "Activar Cloud Connector",
    "ENABLE_DATA_COLLECTION_DESCRIPTION": "La activación de la sincronización permitirá que Zscaler recupere la información de identificador más reciente. Además, incluirá los recursos de esta cuenta en grupos de cargas de trabajo.",
    "ENABLE_DATA_COLLECTION": "Activar recopilación de datos",
    "ENABLE_FULL_ACCESS": "Activar acceso completo",
    "ENABLE_GEO_IP_LOOKUP": "Activar búsqueda de IP de GEO",
    "ENABLE_IPS_CONTROL": "Activar control de IPS",
    "ENABLE_MOBILE_APP": "Acceso a la aplicación Executive Insights",
    "ENABLE_POLICY_INFORMATION": "Activar información de reenvío",
    "ENABLE_SSL_INSPECTION": "Activar Inspección SSL",
    "ENABLE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Forzar IP suplente para navegadores conocidos",
    "ENABLE_USER_IP_MAPPING": "Activar IP Surrogate",
    "ENABLE_VIEW_ONLY_ACCESS": "Activar acceso solo de visualización",
    "ENABLE_XFF_FORWARDING": "Activar reenvío XFF ",
    "ENABLE": "Activar",
    "ENABLED": "Activado",
    "ENABLING": "Activando",
    "ENCR_WEB_CONTENT": "Contenido cifrado personalizado",
    "ENCRYPTED_DTLS": "DTLS",
    "END_TIME": "Tiempo de fin",
    "END_USER_AUTHENDICATION": "Autenticación de usuario final",
    "ENDPOINT_ID": "ID de punto de conexión",
    "ENDPOINT_SERVICE_NAME": "Nombre del servicio de punto de conexión",
    "ENDPOINT_SEVICE_NAME": "Nombre del servicio de punto de conexión",
    "ENDPOINTS_SEVICE_NAME": "Nombre del servicio de puntos de conexión",
    "ENDPOINTS": "Puntos de conexión",
    "ENDS_WITH": "Termina con",
    "ENFORCE_AUTHENTICATION": "Aplicar autenticación",
    "ENFORCE_BAND_WIDTH_CONTROL": "Aplicar control de ancho de banda",
    "ENFORCE_FIREWALL_CONTROL": "Aplicar control de cortafueto",
    "ENFORCE_IPS_CONTROL": "Activar control de IPS",
    "ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Forzar IP suplente para navegadores conocidos",
    "ENFORCE_ZAPP_SSL_SETTING": "Aplicar ajuste de SSL de ZApp",
    "ENROLLED_DEVICE_APP_VERSION": "Versión de aplicación de dispositivo inscrita",
    "ENSURE_ALL_INFORMATION_IS_CORRECT": "Asegúrese de que toda la información siguiente sea correcta antes de crear esta plantilla de aprovisionamiento de Cloud Connector.",
    "ENTER_AWS_ACCOUNT_ID": "Introduzca el ID de cuenta de AWS...",
    "ENTER_CC_ROLE_NAME": "Introduzca el nombre del rol de Cloud Connector...",
    "ENTER_CLOUD_WATCH_GROUP_ARN": "Introduzca el ARN de grupo de Cloud Watch",
    "ENTER_CUSTOM_OPTION_CODE": "Introduzca el código. Ej. 42",
    "ENTER_CUSTOM_OPTION_NAME": "Introduzca el nombre de la opción personalizada.",
    "ENTER_DESCRIPTION_HERE": "Introducir descripción (opcional)",
    "ENTER_DESCRIPTION": "Introduzca la descripción...",
    "ENTER_DEVICE_NAME": "Introducir nombre de dispositivo",
    "ENTER_HEADERS_PARAMETERS": "Introducir parámetros de encabezados",
    "ENTER_HOSTNAME_PREFIX": "Introducir prefijo de nombre de host",
    "ENTER_IP_ADDRESS_OR_FQDN": "Introduzca una dirección IP o un FQDN",
    "ENTER_IP_ADDRESS": "Introduzca la dirección IP...",
    "ENTER_LOG_INFO_TYPE": "Introduzca el tipo de información de registro",
    "ENTER_MTU": "Introducir MTU...",
    "ENTER_NAME_HERE": "Introduzca el nombre aquí",
    "ENTER_NAME": "Introduzca el nombre...",
    "ENTER_NAMESPACE": "Introducir espacio de nombres",
    "ENTER_NUMBER": "Introducir número",
    "ENTER_ROLE_NAME": "Introduzca el nombre del rol...",
    "ENTER_TEXT": "Introduzca texto...",
    "ENTER_THE_VALUE": "Introduzca un valor",
    "ENTER_URL": "Introduzca la URL",
    "ENTERTAINMENT": "Entretenimiento",
    "ENTITLEMENT_STATUS_TOOLTIP": "El derecho de pasarela se muestra como el número de zonas de disponibilidad (AZ) a las que tiene derecho la cuenta y el uso actual. Cada pasarela utiliza dos o más AZ. El número de AZ utilizadas por una pasarela se elige al crear una nueva pasarela.",
    "ENTITLEMENT_STATUS": "Estado de derechos",
    "EQUATORIAL_GUINEA_AFRICA_MALABO": "Africa/Malabo",
    "EQUATORIAL_GUINEA": "Guinea Ecuatorial",
    "ERITREA_AFRICA_ASMARA": "Africa/Asmara",
    "ERITREA": "Eritrea",
    "ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER": "El ID de proyecto de Google no se puede utilizar en combinación con el ID de cuenta de AWS",
    "ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "El ID de suscripción de Azure no puede utilizarse en combinación con el ID de cuenta de AWS",
    "ERROR_API_MUST_BE_DIFFERENT": "La nueva clave de API no puede ser la misma que la clave actual",
    "ERROR_BLACKLIST": "Error de IP de lista de denegación",
    "ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR": "Hay un problema con la carga de Cloud y Branch Connectors. Vuelva a intentarlo más tarde.",
    "ERROR_DEFAULT_LEASE_TME_SHOULD_BE_SMALLER_THAN_MAX_LEASE_TIME": "El Tiempo de arrendamiento predeterminado debe ser menor o igual que el Tiempo máximo de arrendamiento.",
    "ERROR_DELETING_ADMIN_MANAGEMENT": "Se ha producido un problema al eliminar la gestión del administrador. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_API_KEY_MANAGEMENT": "Se ha producido un problema al eliminar la gestión de clave de API. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_APPLIANCE": "Se ha producido un problema al eliminar el dispositivo ZT. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_CELLULAR_CONFIGURATION": "Se ha producido un problema al eliminar la configuración de móvil. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_CELLULAR_USER_PLANE": "Se ha producido un problema al eliminar el plano de usuario móvil. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_GATEWAY": "Se ha producido un problema al eliminar las pasarelas. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_LOCATION_TEMPLATE": "Se ha producido un problema al eliminar la plantilla de ubicación. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_LOCATION": "Se ha producido un problema al eliminar la ubicación. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_PARTNER_ACCOUNT": "Se ha producido un problema al eliminar la cuenta de socio. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_ROLE_MANAGEMENT": "Se ha producido un problema al eliminar la gestión de rol. Vuelva a intentarlo más tarde.",
    "ERROR_DELETING_TESTING": "Se ha producido un problema al eliminar la prueba. Vuelva a intentarlo más tarde.",
    "ERROR_DISABLING_PARTNER_ACCOUNT": "Error al desactivar la recopilación de datos de cuenta de socio",
    "ERROR_DUPLICATE_DNS_SERVER": "Seleccione opciones diferentes para el DNS primario y secundario.",
    "ERROR_DUPLICATE_HA_VIRTUAL_ID": "Las ID de virtuales de alta disponibilidad deben ser únicas. Valores duplicados de ID de alta disponibilidad ",
    "ERROR_DUPLICATE_INTERFACE": "La interfaz no puede ser la misma que una existente.",
    "ERROR_DUPLICATE_SUBNETS": "Verifique la subred duplicada",
    "ERROR_EDITING_API_KEY_MANAGEMENT": "Se ha producido un problema al editar la gestión de clave de API. Vuelva a intentarlo más tarde.",
    "ERROR_EDITING_TESTING": "Se ha producido un problema al editar la prueba. Vuelva a intentarlo más tarde.",
    "ERROR_EDITING": "Se ha producido un problema al editar los datos. Vuelva a intentarlo más tarde.",
    "ERROR_ENABLING_PARTNER_ACCOUNT": "Error al activar la recopilación de datos de cuenta de socio ",
    "ERROR_HTTP_REQUEST_FAILURE": "Se ha informado del fallo de la petición HTTP.",
    "ERROR_LIST_DNS_SERVER_HAS_DUPLICATE": "Las direcciones IP del servidor DNS tienen valores duplicados.",
    "ERROR_LIST_DNS_SERVER_LIMIT_4": "El límite son 4 direcciones IP de servidor DNS.",
    "ERROR_LIST_DOMAIN_NAME_HAS_DUPLICATE": "Los nombres de dominio tienen valores duplicados.",
    "ERROR_LIST_DOMAIN_NAME_LIMIT_4": "El límite son 4 nombres de dominio.",
    "ERROR_LOADING_ADMIN_MANAGEMENT": "Se ha producido un problema al cargar los datos de gestión de administrador. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_API_KEY_MANAGEMENT": "Hay un problema con la carga de datos de clave de API. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_DATA": "Se ha producido un problema al cargar los datos. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_DOMAINS": "Se ha producido un problema al cargar los datos de dominio. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_FORWARDING_POLICIES": "Se ha producido un problema al cargar los datos de políticas de reenvío. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_LOCATION_TEMPLATE": "Se ha producido un problema al cargar los datos de la plantilla de ubicación. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_LOCATIONS": "Se ha producido un problema al cargar los datos de ubicación. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING_ROLE_MANAGEMENT": "Se ha producido un problema al cargar los datos de gestión de rol. Inténtelo de nuevo más tarde.",
    "ERROR_LOADING": "Error al cargar los datos.",
    "ERROR_LOOKUP": "Error de URL de búsqueda",
    "ERROR_NO_SCHEDULED_VERSION_AVAILABLE": "No hay versión disponible",
    "ERROR_OCCURRED_WHILE_CREATING_NEW_PASSWORD": "Error al crear la nueva contraseña. Vuelva a intentarlo más tarde",
    "ERROR_OCCURRED_WHILE_VERIFYING_PASSWORD": "Error al verificar la contraseña actual. Vuelva a intentarlo más tarde",
    "ERROR_OPERATIONAL_STATUS_SAVE_ERROR": "Se ha producido un problema al guardar el estado operativo. Vuelva a intentarlo más tarde.",
    "ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "El ID de suscripción de Azure no puede utilizarse en combinación con el ID de proyecto de Google",
    "ERROR_REGENERATE_API_KEY_MANAGEMENT": "Se ha producido un problema al regenerar la gestión de clave de API. Vuelva a intentarlo más tarde.",
    "ERROR_REMOVE_DELETED_SEGMENTS": "La regla no se puede guardar con un segmento eliminado.",
    "ERROR_SAVING_GATEWAY": "Se ha producido un problema al guardar la pasarela. Vuelva a intentarlo más tarde.",
    "ERROR_SAVING_LOCATION": "Se ha producido un problema al guardar la ubicación. Vuelva a intentarlo más tarde.",
    "ERROR_SAVING_NETWORK_SERVICES_GROUPS": "Se ha producido un problema al guardar los grupos de servicios de red. Vuelva a intentarlo más tarde.",
    "ERROR_SAVING_NETWORK_SERVICES": "Se ha producido un problema al guardar los servicios de red. Vuelva a intentarlo más tarde.",
    "ERROR_SAVING_ROLE_MANAGEMENT": "Se ha producido un problema al guardar la gestión de rol. Vuelva a intentarlo más tarde.",
    "ERROR_SAVING": "Se ha producido un problema al guardar los datos. Vuelva a intentarlo más tarde.",
    "ERROR_TESTING": "Se ha producido un error al ejecutar la prueba.",
    "ERROR_UPDATING_PERMISSION_STATUS": "Se ha producido un problema al actualizar el estado de permiso. Vuelva a intentarlo más tarde.",
    "ERROR": "Error",
    "ESP_PROTOCOL_DESC": "Encapsulating Security Payload (ESP) es un protocolo incorporado en IPSec que proporciona autenticación, integridad y confidencialidad de payload de paquetes de red en redes IPv4 y IPv6.",
    "ESP_PROTOCOL": "ESP",
    "ESTABLISH_SUPPORT_TUNNEL": "Establecer túnel de soporte",
    "ESTONIA_EUROPE_TALLINN": "Europa/Tallin",
    "ESTONIA": "Estonia",
    "ETHIOPIA_AFRICA_ADDIS_ABABA": "Africa/Addis Ababa",
    "ETHIOPIA": "Etiopía",
    "EU_CENTRAL_1": "eu-central-1 (Frankfurt)",
    "EU_CENTRAL_1A": "eu-central-1a",
    "EU_CENTRAL_1B": "eu-central-1b",
    "EU_CENTRAL_1C": "eu-central-1c",
    "EU_CENTRAL_2": "Europa (Zúrich)",
    "EU_NORTH_1": "eu-north-1 (Estocolmo)",
    "EU_NORTH_1A": "eu-north-1a",
    "EU_NORTH_1B": "eu-north-1b",
    "EU_NORTH_1C": "eu-north-1c",
    "EU_SOUTH_1": "eu-south-1 (Milán)",
    "EU_SOUTH_1A": "eu-south-1a",
    "EU_SOUTH_1B": "eu-south-1b",
    "EU_SOUTH_1C": "eu-south-1c",
    "EU_SOUTH_2": "Europa (España)",
    "EU_WEST_1": "eu-west-1 (Irlanda)",
    "EU_WEST_1A": "eu-west-1a",
    "EU_WEST_1B": "eu-west-1b",
    "EU_WEST_1C": "eu-west-1c",
    "EU_WEST_2": "eu-west-2 (Londres)",
    "EU_WEST_2A": "eu-west-2a",
    "EU_WEST_2B": "eu-west-2b",
    "EU_WEST_2C": "eu-west-2c",
    "EU_WEST_3": "wu-west-3 (París)",
    "EU_WEST_3A": "eu-west-3a",
    "EU_WEST_3B": "eu-west-3b",
    "EU_WEST_3C": "eu-west-3c",
    "EUROPE_CENTRAL2_A": "europe-central2-a",
    "EUROPE_CENTRAL2_B": "europe-central2-b",
    "EUROPE_CENTRAL2_C": "europe-central2-c",
    "EUROPE_CENTRAL2": "europe-central2",
    "EUROPE_NORTH1_A": "europe-north1-a",
    "EUROPE_NORTH1_B": "europe-north1-b",
    "EUROPE_NORTH1_C": "europe-north1-c",
    "EUROPE_NORTH1": "europe-north1",
    "EUROPE_SOUTHWEST1_A": "europe-southwest1-a",
    "EUROPE_SOUTHWEST1_B": "europe-southwest1-b",
    "EUROPE_SOUTHWEST1_C": "europe-southwest1-c",
    "EUROPE_SOUTHWEST1": "europe-southwest1",
    "EUROPE_WEST1_B": "europe-west1-b",
    "EUROPE_WEST1_C": "europe-west1-c",
    "EUROPE_WEST1_D": "europe-west1-d",
    "EUROPE_WEST1": "europe-west1",
    "EUROPE_WEST10": "europe-west10",
    "EUROPE_WEST12_A": "europe-west12-a",
    "EUROPE_WEST12_B": "europe-west12-b",
    "EUROPE_WEST12_C": "europe-west12-c",
    "EUROPE_WEST12": "europe-west12",
    "EUROPE_WEST2_A": "europe-west2-a",
    "EUROPE_WEST2_B": "europe-west2-b",
    "EUROPE_WEST2_C": "europe-west2-c",
    "EUROPE_WEST2": "europe-west2",
    "EUROPE_WEST3_A": "europe-west3-a",
    "EUROPE_WEST3_B": "europe-west3-b",
    "EUROPE_WEST3_C": "europe-west3-c",
    "EUROPE_WEST3": "europe-west3",
    "EUROPE_WEST4_A": "europe-west4-a",
    "EUROPE_WEST4_B": "europe-west4-b",
    "EUROPE_WEST4_C": "europe-west4-c",
    "EUROPE_WEST4": "europe-west4",
    "EUROPE_WEST6_A": "europe-west6-a",
    "EUROPE_WEST6_B": "europe-west6-b",
    "EUROPE_WEST6_C": "europe-west6-c",
    "EUROPE_WEST6": "europe-west6",
    "EUROPE_WEST8_A": "europe-west8-a",
    "EUROPE_WEST8_B": "europe-west8-b",
    "EUROPE_WEST8_C": "europe-west8-c",
    "EUROPE_WEST8": "europe-west8",
    "EUROPE_WEST9_A": "europe-west9-a",
    "EUROPE_WEST9_B": "europe-west9-b",
    "EUROPE_WEST9_C": "europe-west9-c",
    "EUROPE_WEST9": "europe-west9",
    "EUROPE": "Europa",
    "EUSA_AGREEMENT": "Acuerdo de suscripción de usuario de Zscaler",
    "EVENT_BUS_NAME": "Nombre de bus de eventos",
    "EVENT_GRID_TEXT": "Seleccione las regiones, las suscripciones y los grupos de recursos en los que se crean el tema y el destino del asociado.",
    "EVENT_GRID": "Event Grid",
    "EVENT_TIME": "Hora de evento",
    "EVENT": "Evento",
    "EVENTS": "Eventos",
    "EXACT_MATCH": "Coincidencia exacta",
    "EXCEEDS_UPGRADE_WINDOW": "Ha fallado. La actualización ha superado el tiempo de la ventana de actualización. Cloud Connector ha revertido a buen estado",
    "EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS": "Excluir de grupos de ubicaciones dinámicos",
    "EXCLUDE_FROM_STATIC_LOCATION_GROUPS": "Excluir de grupos de ubicaciones manuales",
    "EXCLUDE_IP_ADDRESSES": "Excluir direcciones IP",
    "EXCLUDE": "EXCLUIR",
    "EXEC_INSIGHT_AND_ORG_ADMIN": "Administrador de Executive Insight y de organización",
    "EXISTING_LOCATION": "Ubicación existente",
    "EXISTING": "Existente",
    "EXPIRES_IN": "Caduca en",
    "EXPIRES": "Caduca el",
    "EXPIRY_DATE": "Fecha de caducidad",
    "EXPORT_TO_CSV": "Exportar a CSV",
    "EXTERNAL_ID": "ID externo",
    "EXTERNAL": "Tráfico externo",
    "FAIL_ALLOW_IGNORE_DNAT": "Reenviar al servidor DNS original",
    "FAIL_CLOSE": "Cierre de fallo",
    "FAIL_CLOSED": "Fallo cerrado",
    "FAIL_OPEN_TEXT": "Omitir motor de reenvío de tráfico",
    "FAIL_OPEN_TOOLTIP": "Activar esta opción permite que todo el tráfico local y destinado a Internet fluya sin validación de políticas o inspección de contenido en caso de que el motor de reenvío de políticas falle o se detenga para actualizaciones. No se permitirá el acceso a las aplicaciones protegidas por ZPA mientras se encuentre en este estado.",
    "FAIL_OPEN": "FAIL OPEN",
    "FAIL_RET_ERR": "Respuesta de error de devolución",
    "FAILED_OTHER": "Otro. Cloud Connector en buen estado",
    "FAILED": "Fallido",
    "FAILURE_BEHAVIOR": "Comportamiento de fallo",
    "FAILURE": "Fallo",
    "FALKLAND_ISLANDS_ATLANTIC_STANLEY": "Atlantic/Stanley",
    "FALKLAND_ISLANDS_MALVINAS": "Falkland Islands (Malvinas)",
    "FALKLAND_ISLANDS": "Falkland Islands",
    "FALLBACK_TO_TLS": "Revertir a TLS",
    "FALSE": "Falso",
    "FAMILY_ISSUES": "Asuntos familiares",
    "FAROE_ISLANDS_ATLANTIC_FAROE": "Atlantic/Faroe",
    "FAROE_ISLANDS": "Faroe Islands",
    "FEDERATED_STATES_OF_MICRONESIA": "Federated States of Micronesia",
    "FETCHING_MORE_LIST_ITEMS": "Buscando más elementos de la lista...",
    "FIJI": "Fiji",
    "FILE_CERTIFICATE_FILTER": "Archivo (.pem, .cer)",
    "FILE_HOST": "FileHost",
    "FILTERING": "Filtrando",
    "FINANCE": "Finanzas",
    "FINISH": "Finalizar",
    "FINLAND_EUROPE_HELSINKI": "Europa/Helsinki",
    "FINLAND": "Finland",
    "FIREWALL_ACCESS_CONTROL": "Control de acceso al firewall",
    "FIREWALL_FORWARDING": "Reenvío de firewall",
    "FIREWALL_LOGS": "Informes del cortafuegos",
    "FIREWALL_RESOURCE": "Recurso de firewall",
    "FIRST_TIME_AUP_BEHAVIOR": "Comportamiento AUP por primera vez",
    "FO_DEST_DROP": "Consulta descartada",
    "FO_DEST_ERR": "Respuesta de error devuelta al cliente",
    "FO_DEST_PASS": "Consulta reenviada al destino",
    "FOOTER_PATENTS_TOOLTIP": "De acuerdo con las disposiciones sobre marcado virtual (Virtual Marking) de la Ley de Inventos de Estados Unidos (America Invents Act), las ofertas de seguridad de Zscaler están protegidas por patentes en Estados Unidos y otros países, conforme a lo descrito en https://www.zscaler.com/patents.",
    "FOR_AUTOMATION": "Para automatización",
    "FORCE_ACTIVATE": "Forzar activación",
    "FORCE_DELETE_VM": "Forzar eliminación de VM",
    "FORCE_SSL_INTERCEPTION": "Forzar inspección de SSL",
    "FORCED_ACTIVATE": "Forzar activación",
    "FORWARD_TO_ORIGINAL_SERVER": "Reenviar al servidor DNS original",
    "FORWARD_TO_PROXY_GATEWAY": "Reenviar a pasarela de proxy",
    "FORWARD_TO_ZPA_GATEWAY": "Reenviar a pasarela de ZPA",
    "FORWARDING_CONTROL": "Control de reenvío",
    "FORWARDING_INFORMATION": "Información de reenvío",
    "FORWARDING_INTERFACE": "Interfaz de reenvío",
    "FORWARDING_IP_ADDRESS": "Dirección IP de reenvío",
    "FORWARDING_METHOD": "Tipo de reenvío",
    "FORWARDING_METHODS": "Métodos de reenvío",
    "FORWARDING_POLICIES": "Políticas de reenvío",
    "FORWARDING_RULE": "Regla de reenvío",
    "FORWARDING": "Reenvío",
    "FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Dominios",
    "FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Grupo de dominios",
    "FRANCE_EUROPE_PARIS": "Europe/Paris",
    "FRANCE": "Francia",
    "FRANCECENTRAL": "(Europa) Centro de Francia",
    "FRANCESOUTH": "(Europa) Sur de Francia",
    "FRENCH_GUIANA_AMERICA_CAYENNE": "America/Cayenne",
    "FRENCH_GUIANA": "French Guiana",
    "FRENCH_POLYNESIA_PACIFIC_GAMBIER": "Pacific/Gambier",
    "FRENCH_POLYNESIA_PACIFIC_MARQUESAS": "Pacific/Marquesas",
    "FRENCH_POLYNESIA_PACIFIC_TAHITI": "Pacific/Tahiti",
    "FRENCH_POLYNESIA": "Polinesia Francesa",
    "FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN": "Indian/Kerguelen",
    "FRENCH_SOUTHERN_TERRITORIES": "Territorios Franceses del Sur",
    "FRIDAY": "Viernes",
    "FROM": "De",
    "FTP_000": "Respuesta de FTP no válida",
    "FTP_110": "110 - Reiniciar reproducción de marcador",
    "FTP_125": "125 - Conexión de datos ya abierta; transferencia iniciada",
    "FTP_150": "150 - Estado del archivo correcto; a punto de abrir la conexión de datos",
    "FTP_200": "200 - Directorio modificado correctamente",
    "FTP_226": "226 - Transferencia completa",
    "FTP_250": "250 - Completado",
    "FTP_421": "421 - Servicio no disponible",
    "FTP_425": "425 - No se puede abrir la conexión de datos",
    "FTP_426": "426 - Transferencia anulada",
    "FTP_450": "450 - Acción de archivo solicitada no realizada",
    "FTP_451": "451 - Error local en el procesamiento",
    "FTP_452": "452 - Almacenamiento insuficiente",
    "FTP_453": "453 - Discrepancia de MD5",
    "FTP_500": "500 - Error de sintaxis",
    "FTP_501": "501 - Error de sintaxis en los parámetros",
    "FTP_502": "502 - Comando no implementado",
    "FTP_530": "530 - No se ha iniciado sesión",
    "FTP_532": "532 - Se necesita una cuenta para almacenar archivos",
    "FTP_550": "550 - Archivo no disponible",
    "FTP_551": "551 - Tipo de página desconocido",
    "FTP_552": "552 - Excedida la asignación de almacenamiento",
    "FTP_553": "553 - Nombre de archivo no permitido",
    "FTP_554": "554 - El archivo está infectado",
    "FTP_555": "555 - Bloqueado por política de tipo de archivo",
    "FTP_556": "556 - Bloqueado por DLP",
    "FTP_557": "557 - Bloqueado por BA",
    "FTP_558": "558 - Bloqueado por BWCTL",
    "FTP_559": "559 - Bloqueado por categoría de URL",
    "FTP_560": "560 - Bloqueado por ATP",
    "FTP_561": "561 - Bloqueado por Bloquear acceso a Internet",
    "FTP_ALLOW_OVER_HTTP": "Permitir FTP sobre HTTP",
    "FTP_ALLOWED_URL_CATEGORIES": "Categorías de URL permitidas",
    "FTP_ALLOWED_URLS": "URL permitidas",
    "FTP_APPE": "appe",
    "FTP_CONNECT_CMD": "CONNECT",
    "FTP_CONNECT": "Convertir el tráfico FTP (mediante el método connect de HTTP) de bridge a FTP nativo",
    "FTP_CONTROL_RECOMMENDED_POLICY": "Política recomendada de control de FTP",
    "FTP_CONTROL_TIPS_DESC": "De forma predeterminada, el servicio Zscaler no permite que los usuarios de una ubicación carguen o descarguen archivos de sitios FTP. Puede configurar la política de Control de FTP para permitir el acceso a sitios específicos.",
    "FTP_CONTROL_TIPS_TITLE": "Configurar política de control de FTP",
    "FTP_CONTROL": "Control de FTP",
    "FTP_CWD": "cwd",
    "FTP_DATA_DESC": "  Este protocolo se utiliza para transportar datos en conexiones de datos de comunicaciones FTP ",
    "FTP_DATA": "FTP-Data",
    "FTP_DENIED": "No se permite el acceso a sitios FTP",
    "FTP_DESC": " el protocolo FTP se utiliza para transportar datos de forma fiable entre un cliente y un servidor",
    "FTP_INVALID": "INVALIDO",
    "FTP_LIST": "lista",
    "FTP_NATIVE_TRAFFIC": "Tráfico FTP nativo",
    "FTP_OVER_HTTP_TRAFFIC": "Tráfico FTP sobre HTTP",
    "FTP_PROXY_PORT": "Puerto de proxy de FTP",
    "FTP_PROXY": "Proxy de FTP",
    "FTP_RETR": "retr",
    "FTP_RULE": "FTP nativo",
    "FTP_SECURITY": "La seguridad de FTP incluye AV, DLP, BA, FT, etc.",
    "FTP_SERVICES": "Servicios FTP",
    "FTP_STOR": "stor",
    "FTP_UPLOAD_DENIED": "No se permite usar FTP a través de HTTP para cargar",
    "FTP": "FTP",
    "FTPOVERHTTP": "FTP sobre HTTP",
    "FTPRULESLOT": "Control de tipo de archivo (FTP)",
    "FTPS_DATA_DESC": " Este protocolo se utiliza para transportar datos en conexiones de datos de comunicaciones Secure FTP (FTP seguro)",
    "FTPS_DATA": "ftps_data",
    "FTPS_DESC": " Versión segura del protocolo FTP",
    "FTPS_IMPLICIT_DESC": "FTPS implícito inicia automáticamente una conexión SSL/TLS con el servidor inmediatamente después de que el cliente de FTP se conecte a un servidor de FTP.",
    "FTPS_IMPLICIT": "FTPS implícito",
    "FTPS": "FTPS",
    "FULL_ACCESS_ENABLED_UNTIL": "Acceso completo activado hasta",
    "FULL_ACCESS": "Acceso completo",
    "FULL_SESSION_LOGS": "Registros de sesión completos",
    "FULL": "Completo",
    "FUNCTIONAL_SCOPE": "Alcance funcional",
    "FWD_METHOD": "Tipo de reenvío",
    "FWD_RULE": "Regla de reenvío",
    "FWD_TRAFFIC_DIRECTION": "Tipo de solicitud",
    "FWD_TYPE": "Tipo de reenvío",
    "FWD_TYPES": "Tipos de Fwd",
    "GABON_AFRICA_LIBREVILLE": "Africa/Libreville",
    "GABON": "Gabon",
    "GAMBIA_AFRICA_BANJUL": "Africa/Banjul",
    "GAMBIA": "Gambia",
    "GAMBLING": "Juegos de azar",
    "GATEWAY_DEST_IP": "IP de destino de pasarela",
    "GATEWAY_DEST_PORT": "Puerto de destino de pasarela",
    "GATEWAY_DETAILS": "Detalles de pasarela",
    "GATEWAY_IP_ADDRESS": "Dirección IP de pasarela",
    "GATEWAY_NAME": "Nombre de pasarela",
    "GATEWAY_OPTIONS": "Opciones de pasarela",
    "GATEWAY": "Pasarela",
    "GATEWAYS": "Pasarelas",
    "GCP_AVAILABILITY_ZONE": "Zona de disponibilidad de GCP",
    "GCP_REGION": "Región del GCP",
    "GENERAL_AVAILABILITY": "Disponibilidad general",
    "GENERAL_INFORMATION": "Información general",
    "GENERAL": "General",
    "GENERATE_NEW_CERTIFICATE": "Generar nuevo certificado",
    "GENERATE_TOKEN": "Generar testigo",
    "GEO_LOCATION": "Geolocalización",
    "GEO_VIEW": "Vista geográfica",
    "GEORGIA_ASIA_TBILISI": "Asia/Tbilisi",
    "GEORGIA": "Georgia",
    "GERMANY_EUROPE_BERLIN": "Europe/Berlin",
    "GERMANY": "Germany",
    "GERMANYNORTH": "(Europa) Norte de Alemania",
    "GERMANYWESTCENTRAL": "(Europa) Centro-Oeste de Alemania",
    "GHANA_AFRICA_ACCRA": "Africa/Accra",
    "GHANA": "Ghana",
    "GIBRALTAR_EUROPE_GIBRALTAR": "Europe/Gibraltar",
    "GIBRALTAR": "Gibraltar",
    "GLOBAL": "Global",
    "GMT_01_00_AZORES": "GMT-01:00",
    "GMT_01_00_WESTERN_EUROPE_GMT_01_00": "GMT+01:00",
    "GMT_02_00_EASTERN_EUROPE_GMT_02_00": "GMT+02:00",
    "GMT_02_00_EGYPT_GMT_02_00": "GMT+02:00",
    "GMT_02_00_ISRAEL_GMT_02_00": "GMT+02:00",
    "GMT_02_00_MID_ATLANTIC": "GMT-02:00",
    "GMT_03_00_ARGENTINA": "GMT+03:00",
    "GMT_03_00_BRAZIL": "GMT+03:00",
    "GMT_03_00_RUSSIA_GMT_03_00": "GMT+03:00",
    "GMT_03_00_SAUDI_ARABIA_GMT_03_00": "GMT+03:00",
    "GMT_03_30_IRAN_GMT_03_30": "GMT+03:30",
    "GMT_03_30_NEWFOUNDLAND_CANADA": "GMT-03:30",
    "GMT_04_00_ARABIAN_GMT_04_00": "GMT+04:00",
    "GMT_04_00_ATLANTIC_TIME": "GMT-04:00",
    "GMT_04_30_AFGHANISTAN_GMT_04_30": "GMT+04:30",
    "GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA": "GMT-05:00",
    "GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00": "GMT+05:00",
    "GMT_05_00_US_EASTERN_TIME_INDIANA": "GMT-05:00",
    "GMT_05_00_US_EASTERN_TIME": "GMT-05:00",
    "GMT_05_30_INDIA_GMT_05_30": "GMT+05:30",
    "GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00": "GMT+06:00",
    "GMT_06_00_MEXICO": "GMT-06:00",
    "GMT_06_00_US_CENTRAL_TIME": "GMT-06:00",
    "GMT_06_30_BURMA_GMT_06_30": "GMT+06:30",
    "GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00": "GMT+07:00",
    "GMT_07_00_US_MOUNTAIN_TIME_ARIZONA": "GMT-07:00",
    "GMT_07_00_US_MOUNTAIN_TIME": "GMT-07:00",
    "GMT_08_00_AUSTRALIA_WT_GMT_08_00": "GMT+08:00",
    "GMT_08_00_CHINA_TAIWAN_GMT_08_00": "GMT+08:00",
    "GMT_08_00_PACIFIC_TIME": "GMT-08:00",
    "GMT_08_00_SINGAPORE_GMT_08_00": "GMT+08:00",
    "GMT_08_30_PITCARN": "GMT-08:30",
    "GMT_09_00_JAPAN_GMT_09_00": "GMT+09:00",
    "GMT_09_00_KOREA_GMT_09_00": "GMT+09:00",
    "GMT_09_00_US_ALASKA_TIME": "GMT-09:00",
    "GMT_09_30_AUSTRALIA_CT_GMT_09_30": "GMT+09:30",
    "GMT_09_30_MARQUESAS": "GMT-09:30",
    "GMT_10_00_AUSTRALIA_ET_GMT_10_00": "GMT+10:00",
    "GMT_10_00_US_HAWAIIAN_TIME": "GMT-10:00",
    "GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30": "GMT+10:30",
    "GMT_11_00_CENTRAL_PACIFIC_GMT_11_00": "GMT+11:00",
    "GMT_11_00_SAMOA": "GMT-11:00",
    "GMT_11_30_NORFOLK_ISLANDS_GMT_11_30": "GMT+11:30",
    "GMT_12_00_DATELINE": "GMT-12:00",
    "GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00": "GMT+12:00",
    "GMT_UK_SPAIN": "GMT",
    "GMT": "GMT",
    "GMTP": "",
    "GNUTELLA_DESC": " Gnutella es un protocolo peer-to-peer",
    "GNUTELLA": "Gnutella",
    "GO_BACK": "Volver",
    "GOOD": "Bien",
    "GOVERNMENT": "Otros Gobierno y política",
    "GRE_PROTOCOL_DESC": "Generic Routing Encapsulation (GRE) es un protocolo de túnel capaz de encapsular gran variedad de protocolos de capa de red dentro de enlaces de punto a punto virtuales o enlaces de punto a multipunto a través de una red IP.",
    "GRE_PROTOCOL": "GRE",
    "GRE_TUNNEL_INFO": "Información de túnel GRE",
    "GRE": "GRE",
    "GREECE_EUROPE_ATHENS": "Europa/Atenas",
    "GREECE": "Grecia",
    "GREENLAND_AMERICA_DANMARKSHAVN": "America/Danmarkshavn",
    "GREENLAND_AMERICA_GODTHAB": "America/Godthab",
    "GREENLAND_AMERICA_SCORESBYSUND": "America/Scoresbysund",
    "GREENLAND_AMERICA_THULE": "America/Thule",
    "GREENLAND": "Groenlandia",
    "GRENADA_AMERICA_GRENADA": "America/Grenada",
    "GRENADA": "Grenada",
    "GROUP_INFORMATION": "Información de grupo",
    "GROUP_NAME": "Nombre del grupo",
    "GROUP_ONLY": "Grupo",
    "GROUP": "Grupo",
    "GROUPS": "Grupos",
    "GUADELOUPE_AMERICA_GUADELOUPE": "America/Guadalupe",
    "GUADELOUPE": "Guadalupe",
    "GUAM_PACIFIC_GUAM": "Pacific/Guam",
    "GUAM": "Guam",
    "GUATEMALA_AMERICA_GUATEMALA": "America/Guatemala",
    "GUATEMALA": "Guatemala",
    "GUERNSEY_EUROPE_GUERNSEY": "Europa/Guernsey",
    "GUERNSEY": "Guernsey",
    "GUESTWIFI": "Tipo de tráfico de Wi-Fi de invitados",
    "GUINEA_AFRICA_CONAKRY": "Africa/Conakry",
    "GUINEA_BISSAU_AFRICA_BISSAU": "Africa/Bissau",
    "GUINEA_BISSAU": "Guinea-Bissau",
    "GUINEA": "Guinea",
    "GUYANA_AMERICA_GUYANA": "America/Guyana",
    "GUYANA": "Guyana",
    "GW_CONNECT_FAILED": "Fallo al establecer conexión con la pasarela.",
    "GW_CONNECTION_CLOSE": "La conexión con la pasarela se ha cerrado con EOF.",
    "GW_CONNECTION_FAIL": "Fallo en la conexión con la pasarela.",
    "GW_KEEPALIVE_FAIL": "Se ha agotado el tiempo de espera para sondeo keepalive de la pasarela.",
    "GW_RESOLVE_FAIL": "Error en la resolución de pasarela.",
    "GW_RESOLVE_NOIP": "PAC no ha devuelto ninguna IP para resolución de pasarela.",
    "GW_UNHEALTHY": "Alguna pasarela no está en buen estado.",
    "H_323_DESC": "H.323 es un estándar aprobado por la International Telecommunication Union (ITU) que define cómo se transmiten los datos de conferencia audiovisual entre redes",
    "H_323": "H.323",
    "HA_DEPLOYMENT_STATUS": "Estado de implementación de HA",
    "HA_DEPLOYMENT": "Implementación de alta disponibilidad",
    "HA_STATE": "Estado de alta disponibilidad (HA)",
    "HA_STATUS": "Estado de HA",
    "HAITI_AMERICA_PORT_AU_PRINCE": "America/Port-au-Prince",
    "HAITI": "Haiti",
    "HARDWARE_DEVICE": "Dispositivo de hardware",
    "HARDWARE_MANAGEMENT": "Gestión de hardware",
    "HEALTH_MONITORING_CC": "Supervisión de estado de Cloud Connectors",
    "HEALTH_STATUS_TOOLTIP": "El estado de salud muestra el estado de cada pasarela implementada. El número de pasarelas aquí mostrado será menor que el número mostrado en los derechos, ya que cada pasarela puede contener 2 o más zonas de disponibilidad (AZ).",
    "HEALTH_STATUS": "Estado de salud",
    "HEALTH": "Salud",
    "HEALTHY": "Buen estado",
    "HELP": "Ayuda",
    "HIGH_AVAILABILITY_STATUS": "Estado de alta disponibilidad",
    "HIGH_AVAILABILITY": "Alta disponibilidad",
    "HISTORY": "Historia",
    "HOBBIES_AND_LEISURE": "Hobbies / tiempo libre",
    "HOLY_SEE_VATICAN_CITY_STATE": "Santa Sede (Estado de la Ciudad del Vaticano) ",
    "HONDURAS_AMERICA_TEGUCIGALPA": "America/Tegucigalpa",
    "HONDURAS": "Honduras",
    "HONG_KONG_ASIA_HONG_KONG": "Asia/Hong Kong",
    "HONG_KONG": "Hong Kong",
    "HOP_COUNT": "Recuento de saltos",
    "HOSTED_DB": "Base de datos alojada",
    "HOSTNAME_PREFIX": "Prefijo de nombre de host",
    "HOSTNAME": "Nombre de Host",
    "HOURS": "Horas",
    "HTTP_0_0": "malo",
    "HTTP_000": "Respuesta HTTP no válida",
    "HTTP_1_0": "1",
    "HTTP_1_1": "1.1",
    "HTTP_100": "100 - Continuar",
    "HTTP_101": "101 - Protocolos de conmutación",
    "HTTP_102": "102 - Procesamiento",
    "HTTP_150": "150 - Otros errores 1XX",
    "HTTP_2_0": "2.0",
    "HTTP_200": "200 - OK",
    "HTTP_201": "201 - Creado",
    "HTTP_202": "202 - Aceptado",
    "HTTP_203": "203 - Información no autoritativa",
    "HTTP_204": "204 - Sin contenido",
    "HTTP_205": "205 - Resetear contenido",
    "HTTP_206": "206 - Contenido parcial  ",
    "HTTP_207": "207 - Estado múltiple",
    "HTTP_226": "226 - IM utilizado",
    "HTTP_250": "250 - Otros errores 2XX",
    "HTTP_300": "300 - Múltiples opciones ",
    "HTTP_301": "301 - Desplazado Permanentemente",
    "HTTP_302": "302 - Encontrados",
    "HTTP_303": "303 - Ver otros",
    "HTTP_304": "304 - No modificado",
    "HTTP_305": "305 - Use Proxy",
    "HTTP_306": "306 - No usado",
    "HTTP_307": "307 - ReDirección temporal",
    "HTTP_308": "308 - Redirección permanente",
    "HTTP_400": "400 - Mala Petición",
    "HTTP_401": "401 - No autorizado",
    "HTTP_402": "402 - Pago requerido",
    "HTTP_403": "403 - Prohibido",
    "HTTP_404": "404 - No encontrado",
    "HTTP_405": "405 - Método no permitido",
    "HTTP_406": "406 - No aceptable",
    "HTTP_407": "407 - Requiere autenticación Proxy ",
    "HTTP_408": "408 - Tiempo de petición excedido ",
    "HTTP_409": "409 - Conflicto",
    "HTTP_410": "410 - Ausente",
    "HTTP_411": "411 - Longitud requerida",
    "HTTP_412": "412 - Fallo de pre-condición",
    "HTTP_413": "413 - Entidad de petición demasiado grande",
    "HTTP_414": "414 - Petición de URL demasiado larga ",
    "HTTP_415": "415 - Tipo de medio no soportado",
    "HTTP_416": "416 - Rango solicitado no puede ser satisfecho",
    "HTTP_417": "417 - Fallo de expectación ",
    "HTTP_421": "421 - Petición mal dirigida",
    "HTTP_422": "422 - Entidad no procesable",
    "HTTP_423": "423 - Bloqueado",
    "HTTP_424": "424 - Dependencia fallida",
    "HTTP_426": "426 - Actualización requerida",
    "HTTP_428": "428 - Condición previa requerida",
    "HTTP_429": "429 - Demasiadas solicitudes",
    "HTTP_450": "450 - Otros errores 4XX",
    "HTTP_500": "500 - Error interno de servidor ",
    "HTTP_501": "501 - No implementado ",
    "HTTP_502": "502 - Pasarela incorrecta",
    "HTTP_503": "503 - Servicio no disponible",
    "HTTP_504": "504 - Tiempo de pasarela excedido ",
    "HTTP_505": "505 - Versión no soportada ",
    "HTTP_506": "506 - La variante también negocia",
    "HTTP_507": "507 - Almacenamiento insuficiente",
    "HTTP_508": "508 - Bucle detectado",
    "HTTP_510": "510 - No extendido",
    "HTTP_550": "550 - Otros errores 5XX",
    "HTTP_BASELINECONTROL": "Baselinecontrol",
    "HTTP_BCOPY": "Bcopy",
    "HTTP_BDELETE": "Bdelete",
    "HTTP_BMOVE": "Bmove",
    "HTTP_BPROPFIND": "Bpropfind",
    "HTTP_BPROPPATCH": "Bproppatch",
    "HTTP_CHECKIN": "Registrar entrada",
    "HTTP_CHECKOUT": "Registrar salida",
    "HTTP_CONNECT_DENIED": "No se permite el uso de túnel HTTP",
    "HTTP_CONNECT": "CONNECT",
    "HTTP_COPY": "Copiar",
    "HTTP_DELETE": "Eliminar",
    "HTTP_DESC": " El protocolo Hypertext Transfer Protocol (HTTP) se usa para navegar en la red",
    "HTTP_DNS_PORT_SETTINGS": "Configuración de puertos HTTP y DNS",
    "HTTP_DPI_DISABLED": "SME DPI: determina el tráfico http del proxy que debe enviarse o no a DPI; por defecto el bit de esta funcionalidad está desactivado y enviamos flujo a DPI si no puede determinarse el appid desde ZURLDB",
    "HTTP_GET": "Obtener",
    "HTTP_HEAD": "HEAD",
    "HTTP_LABEL": "Etiqueta",
    "HTTP_LOCK": "Bloquear",
    "HTTP_MAILPOST": "MAILPOST",
    "HTTP_MERGE": "Fusionar",
    "HTTP_MKACTIVITY": "Mkactivity",
    "HTTP_MKCOL": "Mkcol",
    "HTTP_MKWORKSPACE": "Mkworkspace",
    "HTTP_MOVE": "MOVE",
    "HTTP_NOTIFY": "Notificar",
    "HTTP_OPTIONS": "Opciones",
    "HTTP_POLL": "Sondeo",
    "HTTP_PORTS_FORWARDED_TO_WEB_PROXY": "Puertos HTTP reenviados a proxy web",
    "HTTP_POST": "POST",
    "HTTP_PROPFIND": "Propfind",
    "HTTP_PROPPATCH": "Proppatch",
    "HTTP_PROXY_DESC": "HTTP tunneling es una técnica de encapsulación de comunicaciones de diversos protocolos de red utilizando el protocolo HTTP , estos protocolos de red normalmente pertenecen a la familia de protocolos TCP/IP ",
    "HTTP_PROXY_PORT": "Puerto de proxy de HTTP",
    "HTTP_PROXY": "Proxy de HTTP",
    "HTTP_PUT": "Colocar",
    "HTTP_REPORT": "Informe",
    "HTTP_REQMOD": "Reqmod",
    "HTTP_REQUEST": "Solicitud HTTP",
    "HTTP_REQUESTS": "Solicitudes HTTP",
    "HTTP_RESPMOD": "Respmod",
    "HTTP_RESPONSE": "Respuesta HTTP",
    "HTTP_RULE": "HTTP",
    "HTTP_SEARCH": "Búsqueda",
    "HTTP_SECURITY_HEADERS": "Encabezado de seguridad HTTP",
    "HTTP_SERVICES": "Servicios HTTP",
    "HTTP_SUBSCRIBE": "Suscribirse",
    "HTTP_TRACE": "Rastrear",
    "HTTP_TUNNEL_CONTROL": "Control de túnel HTTP",
    "HTTP_TUNNEL": "Túnel HTTP",
    "HTTP_UNCHECKOUT": "Anular registro de salida",
    "HTTP_UNKNOWN_DESC": " Identifica tráfico de proxy/firewall HTTP para el que no se puede determinar ingún detalle adicional de aplicación",
    "HTTP_UNKNOWN": "HTTP desconocido",
    "HTTP_UNLOCK": "Cancelar bloqueo",
    "HTTP_UNSUBSCRIBE": "Anula suscripción",
    "HTTP_UPDATE": "Actualizar",
    "HTTP_VERSIONCONTROL": "Versioncontrol",
    "HTTP_VS_HTTPS": "HTTP frente a HTTPS",
    "HTTP": "HTTP",
    "HTTP2_DESC": " El protocolo Hypertext Transfer Protocol (HTTP 2.0) se usa para navegar en la red",
    "HTTP2": "HTTPv2",
    "HTTPS_DESC": " HTTPS es la versión segura de HTTP",
    "HTTPS_PORTS_FORWARDED_TO_WEB_PROXY": "Puertos HTTPS reenviados a proxy web",
    "HTTPS_PROXY_PORT": "Puerto de proxy de HTTPS",
    "HTTPS_PROXY": "Proxy de HTTPS",
    "HTTPS_RULE": "HTTPS",
    "HTTPS_SERVICES": "Servicios HTTPS",
    "HTTPS_SSL_TRAFFIC_TREND": "TENDENCIA DE TRÁFICO HTTPS Y SSL",
    "HTTPS_SSL_TRAFFIC": "TRÁFICO HTTPS Y SSL",
    "HTTPS_UNKNOWN_DESC": " Identifica tráfico de proxy/firewall HTTPS para el que no se puede determinar ingún detalle adicional de aplicación",
    "HTTPS_UNKNOWN": "Desconocido HTTPS",
    "HTTPS": "HTTPS",
    "HTTPTUNNEL_DESC": "  HTTP tunneling es una técnica de encapsulación de comunicaciones de diversos protocolos de red utilizando el protocolo HTTP , estos protocolos de red normalmente pertenecen a la familia de protocolos TCP/IP ",
    "HTTPTUNNEL": "HttpTunnel",
    "HUNGARY_EUROPE_BUDAPEST": "Europa/Budapest",
    "HUNGARY": "Hungría",
    "HYPERVISOR_OS": "Hipervisor",
    "HYPERVISOR_VERSION": "Versión de hipervisor",
    "I_AGREE": "De acuerdo",
    "I_GAMER_DESC": " Sitio web de juegos y manga online",
    "I_GAMER": "i-gamer",
    "I_PART_DESC": " Sitio taiwanés de citas online",
    "I_PART": "i-part.com",
    "I_UNDERSTAND_THE_CONSEQUENCE_AND_WANT_TO_PROCEED": "Entiendo las consecuencias y quiero continuar",
    "I_UNDERSTAND_THE_CONSEQUENCE": "Entiendo las consecuencias y quiero continuar",
    "ICELAND_ATLANTIC_REYKJAVIK": "Atlantic/Reykjavik",
    "ICELAND": "Islandia",
    "ICMP_ANY_DESC": "ICMP es uno de los principales protocolos de la Suite de protocolos de Internet, utilizada por dispositivos de red como routers para enviar mensajes de error indicando que un servicio solicitado no está disponible o que un host o router no pudieron ser alcanzados. ICMP también puede usarse para reenviar mensajes de consulta ",
    "ICMP_ANY": "ICMP",
    "ICMP_DESC": " El Protocolo Internet Control Message Protocol (ICMP) es uno de los protocolos principals de la suite Internet Protocol",
    "ICMP_UNKNOWN_DESC": " Identifica tráfico UDP de proxy/cortafuegos para el que no se puede determinar más información de la aplicación",
    "ICMP_UNKNOWN": "ICMP desconocido",
    "IDENT_DESC": " El Protocolo de Identificación ofrece mecanismos para determinar la identidad del usuario de un conexión TCP específica",
    "IDENT": "Ident",
    "IDLE_TIME_DISASSOCIATION": "Tiempo inactivo hasta disociación",
    "IDP_NAME": "Nombre de IdP",
    "IDP": "IdP",
    "IGNORE_INSECURE_KEY": "Inseguro",
    "IKE_ALG": "This LB support ike alg",
    "IKE_DESC": "IKE es un protocolo para obtener material de claves autenticadas para usar en ISAKMP para IPSEC",
    "IKE_NAT_DESC": "IKE-NAT permite Network Address Translation para paquetes ISAKMP y ESP ",
    "IKE_NAT": "IKE-NAT",
    "IKE": "IKE",
    "IKEA_DESC": " Este plug-in de protocolo clasifica el tráfico http al host ikea.com",
    "IKEA": "ikea",
    "IKEV1_PHASE1": "IKE Version 1",
    "IKEV1_PHASE2": "IKE Version 2",
    "IKEV1": "IKE Version 1",
    "IKEV2_ALL_PHASES": "IKE Version 2",
    "IKEV2": "IKE Version 2",
    "IL_CENTRAL_1": "Israel (Tel Aviv)",
    "ILOVEIM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host iloveim.com",
    "ILOVEIM": "ILoveIM",
    "ILS_DESC": "Internet Locator Service (ILS) incluye LDAP, User Locator Service y LDAP sobre TLS/SSL",
    "ILS": "ILS",
    "IMAGE_HOST_DESC": " Sitios que proporcionan almacenamiento, enlaces y/o intercambio de vídeo o imágenes",
    "IMAGE_HOST": "Host de imágenes",
    "IMAGE_ID": "ID de imagen",
    "IMAGES": "Imágenes",
    "IMAGESHACK_DESC": " Servicio gratuito de compartición de imágenes online",
    "IMAGESHACK": "ImageShack",
    "IMAP_DESC": "Internet Message Access Protocol es un protocolo utilizado para recuperar mensajes de correo electrónico",
    "IMAP": "IMAP",
    "IMDB_DESC": "  Base de datos online con información relacionada con películas y programas de TV ",
    "IMDB": "IMDb",
    "IMEEM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host imeem.com",
    "IMEEM": "imeem",
    "IMEET_DESC": "  Servicio de videoconferencia online que utiliza tecnología basada en nube ",
    "IMEET": "i-Meet",
    "IMESH_DESC": " iMesh es un protocolo peer-to-peer",
    "IMESH": "iMesh",
    "IMFRULESLOT": "Control de aplicación de mensajería instantánea",
    "IMGUR_DESC": " Servicio gratuito de alojamiento de imágenes online  ",
    "IMGUR": "imgur",
    "IMO": "imo",
    "IMOIM_DESC": " imo.im",
    "IMOIM": "imo.im",
    "IMP_DESC": "  IMP es el webmail IMAP del proyecto Horde ",
    "IMP": "IMP",
    "IMPERSONATION": "Suplantación de identidad",
    "IMPLUS_DESC": " IM+",
    "IMPLUS": "IM+",
    "IMPORT_ACCOUNT_ID": "Importar ID de cuenta",
    "IMPORT_PROJECT_ID": "Importar ID de proyecto",
    "IMPORT_SUBSCRIPTION_ID": "Importar ID de suscripción",
    "IMPORT": "Importar",
    "IMPRESS_DESC": " Sitio web japonés de noticias de TI ",
    "IMPRESS": "impresionar",
    "IMVU_DESC": " Este plug-in de protocolo clasifica el tráfico http al host imvu.com",
    "IMVU": "IMVU",
    "INACTIVE": "Inactivo",
    "INBOUND": "Entrante",
    "INBOX_DESC": " El portal inbox.com proporciona servicio de correo gratuito",
    "INBOX": "Inbox",
    "INBYTES": "Bytes entrantes",
    "INCLUDE_ADDRESS_RANGE": "Incluir rango de direcciones",
    "INCLUDE_IP_ADDRESSES": "Incluir direcciones IP",
    "INCLUDE": "Incluir",
    "INCOMPLETE_DESC": " Incompleto se usa cuando la firma de protocolo es demasiado larga",
    "INCOMPLETE": "Incompleto",
    "INDABA_MUSIC_DESC": " Este plug-in de protocolo clasifica el tráfico http al host indabamusic.com",
    "INDABA_MUSIC": "Indaba Music",
    "INDIA_ASIA_CALCUTTA": "Asia/Calcuta",
    "INDIA_ASIA_KOLKATA": "Asia/Kolkata",
    "INDIA": "India",
    "INDIATIMES_DESC": "  Esta firma detecta indiatimes y un gran número de sus subdominios, que es uno de los portales de internet y servicios de valor añadido para móviles más populares de India ",
    "INDIATIMES": "timesofindia",
    "INDONESIA_ASIA_JAKARTA": "Asia/Jakarta",
    "INDONESIA_ASIA_JAYAPURA": "Asia/Jayapura",
    "INDONESIA_ASIA_MAKASSAR": "Asia/Makassar",
    "INDONESIA_ASIA_PONTIANAK": "Asia/Pontianak",
    "INDONESIA": "Indonesia",
    "INDONETWORK_DESC": " Este plug-in de protocolo clasifica el tráfico http al host indonetwork.co.id",
    "INDONETWORK": "Indonetwork",
    "INDOWEBSTER_DESC": " Este plug-in de protocolo clasifica el tráfico http al host indowebster.com",
    "INDOWEBSTER": "Indowebster",
    "INFO": "Info",
    "INFOARMOR_DESC": " InfoArmor garantiza soluciones líderes del sector para la protección de identidades de empleados y utiliza inteligencia avanzada contra amenazas",
    "INFOARMOR": "InfoArmor",
    "INFORMIX_DESC": " Informix es una familia de sistemas de gestión de bases de datos relacionales desarrollado por IBM. IBM adquirió la tecnología Informix en 2001 pero se remonta a 1981. Corre sobre mainframes IBM y también está disponible para Linux/Unix/Windows",
    "INFORMIX": "Informix",
    "INGRESS_DETAILS": "Detalles de entrada",
    "INILAH_DESC": " Este plug-in de protocolo clasifica el tráfico http al host inilah.com",
    "INILAH": "inilah",
    "INSIGHTS": "Insights",
    "INST_GBL_METRICS": "Instancia",
    "INSTAGRAM_DESC": " Este plug-in de protocolo clasifica el tráfico http a los hosts instagr.am y instagram.com. También clasifica el tráfico SSL al Common Name instagram.com",
    "INSTAGRAM": "Instagram",
    "INSTANCE_ROLE": "Rol de instancia",
    "INTALKING_DESC": " Portal taiwanés de belleza y maquillaje",
    "INTALKING": "intalking.com",
    "INTEGER_REQUIRED": "Introduzca un número.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_LAN": "La LAN necesita un mínimo de una interfaz o subinterfaz habilitada.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_WAN": "WAN necesita un mínimo de una interfaz o subinterfaz activada y un máximo de dos interfaces o subinterfaces activadas.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE": "La interfaz debe tener al menos una subinterfaz.",
    "INTERFACE_NAME": "Nombre de la interfaz",
    "INTERFACE_SHUTDOWN": "Cierre de la interfaz",
    "INTERFACE": "Interfaz",
    "INTERNAL_EXTERNAL_TRAFFIC": "Tráfico interno/externo",
    "INTERNAL_GATEWAY_IP_ADDRESS": "Dirección IP de pasarela interna",
    "INTERNAL": "Tráfico interno",
    "INTERNATIONS_DESC": " Este plug-in de protocolo clasifica el tráfico http al host internations.org. También clasifica el tráfico SSL al Common Name .internations.org",
    "INTERNATIONS": "InterNations",
    "INTERNET_ACCESS": "Acceso a Internet",
    "INTERNET_COMMUNICATION": "Comunicación en Internet",
    "INTERNET_SERVICES_DESC": " Sitios relacionados con servicios suministrados a través de Internet",
    "INTERNET_SERVICES": "Servicios de Internet",
    "INTERNET_USAGE_TREND": "Tendencia de uso de Internet",
    "INTERNET": "Internet",
    "INTERPARK_DESC": " Este plug-in de protocolo clasifica el tráfico http al host www.interpark.com. También clasifica el tráfico SSL al Common Name .interpark.com",
    "INTERPARK": "Interpark",
    "INTUIT_DESC": " Este plug-in de protocolo clasifica el tráfico http al host intuit.com",
    "INTUIT": "Intuit",
    "INVALID_API_KEY": "Clave de API no válida",
    "INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE": "Unidad de tamaño máximo de lote no válida para el tipo SIEM. Este número debe estar entre 16 y 512 KB.",
    "INVALID_NAME": "Introduzca un nombre válido",
    "INVALID_USERNAME_OR_PASSWORD": "ID o contraseña de inicio de sesión no válidos",
    "IOS_APPSTORE_DESC": " La AppStore de Apple es una plataforma de distribución de aplicaciones digitales para iOS desarrollada y mantenida por Apple Inc",
    "IOS_APPSTORE": "Tienda de aplicaciones iOS",
    "IOS_OTA_UPDATE_DESC": " iOS OTA Update es el protocolo utilizado para actualizaciones de iOS en la red / Over The Air",
    "IOS_OTA_UPDATE": "iOS OTA Update",
    "IOS_OTHERS": "iOS (otro)",
    "IOS_TUNES": "Aplicación IOS iTunes",
    "IOT": "Tráfico IoT",
    "IP_ABUSE_CHECK_DESCRIPTION": "Comprobar las IP que pueden estar abusando de los proxies",
    "IP_ABUSE_CHECK": "Comprobación de abuso de IP",
    "IP_ADDRESS_FROM": "Dirección IP de origen",
    "IP_ADDRESS_HA_DEVICE": "Introduzca la dirección IP virtual del dispositivo de alta disponibilidad.",
    "IP_ADDRESS_LAN_SECTION": "Introduzca una dirección IP para la sección LAN de su dispositivo.",
    "IP_ADDRESS_OPTIONAL": "Dirección IP (opcional)",
    "IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN": "Dirección IP o FQDN o FQDN comodín",
    "IP_ADDRESS_OR_FQDN": "Dirección IP o FQDN",
    "IP_ADDRESS_OR_WILDCARD_FQDN": "Dirección IP o FQDN comodín",
    "IP_ADDRESS_RANCE_CIDR": "Rango de direcciones IP/CIDR",
    "IP_ADDRESS_SHOULD_NOT_BE_PART_OF_ADDRESS_RANGES_POOL": "La dirección IP no debe formar parte del grupo de direcciones DHCP.",
    "IP_ADDRESS_TO": "Dirección IP de destino",
    "IP_ADDRESS_WAN_SECTION": "Introduzca una dirección IP para la sección WAN de su dispositivo.",
    "IP_ADDRESS": "Dirección IP",
    "IP_ADDRESSES": "Direcciones IP",
    "IP_ADDRESSESS": "Dirección IP",
    "IP_BASED_COUNTRIES": "Países basados en IP",
    "IP_CAT_LOOKUP": "Activación búsqueda dinámica cat IP",
    "IP_CATEGORIES": "Categorías de IP",
    "IP_CONNECT_TRANSPARENT": "cambiar CONNECT a IP:puerto a modo transparente",
    "IP_DESC": " El protocolo de Internet (IP) es el principal protocolo de comunicaciones para entregar datagramas a través de fronteras de red en la suite Internet Protocol ",
    "IP_EXAMPLE_WITH_RANGE_CIDR": "Por ejemplo: ********, ********, ********-********, ********/24",
    "IP_EXAMPLE": "P. ej.: ********,********",
    "IP_FQDN_GROUPS": "Grupos de IP y FQDN",
    "IP_INFO": "Información de IP",
    "IP_POOL": "Grupo de IP",
    "IP_UNKNOWN_DESC": " Identifica tráfico IP para el que no se puede determinar mayor detalle de aplicación",
    "IP_UNKNOWN": "IP desconocida",
    "IP": "IP",
    "IP6_DESC": " La versión 6 del protocolo de Internet (IPv6) es la versión más reciente del Protocolo de Internet, el protocolo de comunicaciones que permite identificar y localizar sistemas de ordenadores en redes y encaminar tráfico a través de Internet",
    "IP6": "IP6",
    "IPASS_DESC": " iPass es la compañía pionera del sector en conectividad móvil global, y garantiza un acceso ilimitado a contenido ilimitado en un número ilimitado de dispositivos",
    "IPASS": "iPass",
    "IPERF_DESC": "  El protocolo iperf es utilizado por la herramienta del mismo nombre para medidas de rendimiento de red ",
    "IPERF": "Iperf",
    "IPLAYER_DESC": " iPlayer",
    "IPLAYER": "iPlayer",
    "IPSEC_DESC": " El protocolo IPSec proporciona servicios para securizar comunicaciones de host. Ipsec proporciona dos cabeceras de autenticación de servicios de seguridad Authentication Header (AH)",
    "IPSEC": "IPSEC",
    "IPV4_ALL_DESTINATION_GROUP_NAME": "Nombre de grupo de todos los destinos de IP V4",
    "IPV4_DNS_RESOLUTION_ONLY": "Solo resolución de DNS IPv4",
    "IPV4": "Encapsulado IPv4",
    "IPV6_HERE": "IPv6 i-am-here",
    "IPV6_WHERE": "IPv6 where-are-you",
    "IPV6": "Encabezado IP6",
    "IPV6CP_DESC": " Este protocolo se utiliza para establecer y configurar IPv6 sobre PPP",
    "IPV6CP": "IPV6CP",
    "IPXRIP_DESC": " RIPIPX es el equivalente al protocolo RIP para redes Novell",
    "IPXRIP": "RIPIPX",
    "IQIYI_DESC": " Este plug-in de protocolo clasifica el tráfico http al host iqiyi.com. También clasifica el tráfico SSL al Common Name .iqiyi.com",
    "IQIYI": "iqiyi.com",
    "IRAN_ASIA_TEHRAN": "Asia/Tehran",
    "IRAN": "Iran",
    "IRAQ_ASIA_BAGHDAD": "Asia/Baghdad",
    "IRAQ": "Iraq",
    "IRC_DESC": "  IRC (Internet Relay Chat) es un protocolo de mensajería instantánea ",
    "IRC_GALLERIA_DESC": " Este plug-in de protocolo clasifica el tráfico http al host irc-galleria.net",
    "IRC_GALLERIA": "IRC-Galleria",
    "IRC_TRANSFER_DESC": " Este protocolo se usa para transportar datos en una transferencia de archivos IRC ",
    "IRC_TRANSFER": "Transferencia de Archivos IRC",
    "IRC": "IRC",
    "IRCS_DESC": "  IRCs es la versión segura del protocolo IRC ",
    "IRCS": "Secure IRC",
    "IRELAND_EUROPE_DUBLIN": "Europa/Dublin",
    "IRELAND": "Irlanda",
    "IS_NULL": "Es nulo",
    "ISAE_3402": "ISAE 3402",
    "ISAKMP_DESC": "  El protocolo Internet Security Association and Key Management Protocol (ISAKMP) define procedimientos y formatos de paquete para establecer, negociar, modificar y borrar Asociaciones de Seguridad ",
    "ISAKMP": "ISAKMP",
    "ISLE_OF_MAN_EUROPE_ISLE_OF_MAN": "Europa/Isla de Man",
    "ISLE_OF_MAN": "Isle of Man",
    "ISRAEL_ASIA_JERUSALEM": "Asia/Jerusalem",
    "ISRAEL": "Israel",
    "ISSUER": "Emisor",
    "ITALKI_DESC": " Este plug-in de protocolo clasifica el tráfico http al host italki.com. También clasifica el tráfico SSL al Common Name .italki.com",
    "ITALKI": "italki",
    "ITALY_EUROPE_ROME": "Europa/Roma",
    "ITALY": "Italia",
    "ITALYNORTH": "(Europa) Norte de Italia",
    "ITEMS_TOTAL": "Total de elementos",
    "ITSMY_DESC": " Este plug-in de protocolo clasifica el tráfico http al host mobile.itsmy.com",
    "ITSMY": "GameCloud (itsmy.com)",
    "ITUNES_DESC": "  iTunes es una aplicación de medios digitales propietaria de Apple, se utiliza para reproducir y organizar archivos digitales de música y vídeo ",
    "ITUNES": "iTunes",
    "ITUNESU": "iTunes U",
    "IVORY_COAST": "Costa de Marfil",
    "IWF": "IWF",
    "IWIW_DESC": " Este plug-in de protocolo clasifica el tráfico http al host iwiw.hu",
    "IWIW": "Iwiw",
    "JABBER_DESC": " Jabber es un sistema de estándar abierto de mensajería y presencia que utiliza el protocolo XMPP",
    "JABBER_TRANSFER_DESC": " Jabber transfer es un estándar abierto para transferir archivos entre dos clientes Jabber",
    "JABBER_TRANSFER": "Jabber File Transfer",
    "JABBER": "Jabber",
    "JAIKU_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jaiku.com",
    "JAIKU": "jaiku.com",
    "JAILBREAK": "Jailbreak activado",
    "JAILBROKEN_ROOTED": "Jailbreak/Root realizado",
    "JAJAH_DESC": " Jajah es un suministrador de VoIP propiedad de Telefonica Europa",
    "JAJAH": "Jajah",
    "JAMAICA_AMERICA_JAMAICA": "America/Jamaica",
    "JAMAICA": "Jamaica",
    "JAMMERDIRECT_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jammerdirect.com",
    "JAMMERDIRECT": "Jammer Direct",
    "JANGO_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jango.com",
    "JANGO": "Jango",
    "JANUS_END": "Fin de los códigos de error de Janus.",
    "JAPAN_ASIA_TOKYO": "Asia/Tokyo",
    "JAPAN": "Japón",
    "JAPANEAST": "(Asia Pacífico) Este de Japón",
    "JAPANWEST": "(Asia Pacífico) Oeste de Japón",
    "JAVA_UPDATE_DESC": "  Java Update es el protocolo para la actualización de máquinas virtuales Java, también llamado JVM ",
    "JAVA_UPDATE": "Java Update",
    "JEDI_DESC": "  JEDI es el nombre del protocolo de conexión de streaming de CITRIX ",
    "JEDI": "JEDI",
    "JERSEY_EUROPE_JERSEY": "Europa/Jersey",
    "JERSEY": "Jersey",
    "JINGDONG_DESC": " Tienda de alta tecnología online china muy popular ",
    "JINGDONG": "JingDong",
    "JIOINDIACENTRAL": "(Asia Pacífico) Jio India Central",
    "JIOINDIAWEST": "(Asia Pacífico) Jio India Oeste",
    "JIRA_DESC": " Este plug-in de protocolo clasifica el tráfico HTTP al host airaim.com. También clasifica el tráfico SSL al Common Name onjira.com",
    "JIRA": "JIRA",
    "JIVE_DESC": " Jive Software es una compañía de la industria de software social de negocios con sede en Palo Alto, California",
    "JIVE": "Jive",
    "JNE_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jne.co.id",
    "JNE": "JNE",
    "JOB_EMPLOYMENT_SEARCH_DESC": " Sitios relacionados con ofertas de trabajo o con oportunidades de empleo",
    "JOB_EMPLOYMENT_SEARCH": "Búsqueda de Trabajo / Empleo",
    "JOB_SEARCH_DESC": " Sitios relacionados con ofertas de trabajo o con oportunidades de empleo",
    "JOB_SEARCH": "Búsqueda de Trabajo / Empleo",
    "JOBSTREET_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jobstreet.co.id",
    "JOBSTREET": "JobStreet",
    "JOONGANG_DAILY_DESC": " Este plug-in de protocolo clasifica el tráfico http a los hosts www.joins.com y www.joinsmsn.com",
    "JOONGANG_DAILY": "Joongang Daily",
    "JOOST_DESC": " Joost",
    "JOOST": "Joost",
    "JORDAN_ASIA_AMMAN": "Asia/Amman",
    "JORDAN": "Jordania",
    "JPEG": "Ficheros jpeg",
    "JS_VIEW": "Vista de JS",
    "JSON_CLOUDINFO_STAGGER_SIZE": "Número de instancias cuya cloudinfo se envía de CA en la nube a CA de FCC",
    "JSON_CLOUDINFO": "Enviar información de nube de CA de nube a CA de FCC como JSON",
    "JSONNOTFOUND": "Archivo Json no encontrado",
    "JUBII_DESC": " Este plug-in de protocolo clasifica el tráfico http al host jubii.dk",
    "JUBII": "Jubii",
    "JUSTIN_TV_DESC": " Este plug-in de protocolo clasifica el tráfico http al host justin.tv. También clasifica el tráfico SSL al Common Name .justin.tv",
    "JUSTIN_TV": "Justin.tv",
    "K_12_SEX_EDUCATION": "K-12 Sex Education",
    "K_12": "K-12",
    "KAIOO_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kaioo.com",
    "KAIOO": "kaioo.com",
    "KAIXIN_CHAT_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kaixin001.com",
    "KAIXIN_CHAT": "kaixin",
    "KAKAKU_DESC": " Sitio web dedicado a comparativas de precio y pruebas de producto",
    "KAKAKU": "kakaku.com",
    "KAKAOTALK_DESC": "  KakaoTalk es una plataforma de mensajería instantánea para dispositivos móviles ",
    "KAKAOTALK": "KakaoTalk",
    "KANKAN_DESC": " Sitio chino de streaming de vídeo",
    "KANKAN": "kankan.com",
    "KAPANLAGI_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kapanlagi.com",
    "KAPANLAGI": "KapanLagi",
    "KAROSGAME_DESC": " Este plug-in de protocolo clasifica el tráfico http al host karosgame.ru",
    "KAROSGAME": "Karos (karosgame.ru)",
    "KASKUS_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kaskus.co.id",
    "KASKUS": "Kaskus",
    "KASPERSKY_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kaspersky.com",
    "KASPERSKY_UPDATE_DESC": "  Kaspersky_update es el protocolo utilizado para actualizaciones de software Kaspersky ",
    "KASPERSKY_UPDATE": "Kaspersky Update",
    "KASPERSKY": "Kaspersky",
    "KAZAA_DESC": " Kazaa es un protocolo peer-to-peer",
    "KAZAA": "Kazaa",
    "KAZAKHSTAN_ASIA_ALMATY": "Asia/Almaty",
    "KAZAKHSTAN_ASIA_AQTAU": "Asia/Aqtau",
    "KAZAKHSTAN_ASIA_AQTOBE": "Asia/Aqtobe",
    "KAZAKHSTAN_ASIA_ORAL": "Asia/Oral",
    "KAZAKHSTAN_ASIA_QYZYLORDA": "Asia/Qyzylorda",
    "KAZAKHSTAN": "Kazakhstan",
    "KB_BANK_DESC": " Este plug-in de protocolo clasifica el tráfico HTTP al host .kbstar.com. También clasifica el tráfico SSL al Common Name .kbstar.com",
    "KB_BANK": "KB Bank (kbstar.com)",
    "KBS_DESC": " Este plug-in de protocolo clasifica el tráfico http al host www.kbs.co.kr",
    "KBSTAR_DESC": " Este servicio web está cerrado",
    "KBSTAR": "kbstar (cerrado)",
    "KEEPLIVE": "Enviar registros keepalive para mantener activa la conexión TCP si no hay flujo de datos",
    "KEEZMOVIES_DESC": " Este plug-in de protocolo clasifica el tráfico http al host keezmovies.com",
    "KEEZMOVIES": "KeezMovies",
    "KEK_ROTATION": "Activar rotación de clave en sme ",
    "KEMENKUMHAM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kemenkumham.go.id",
    "KEMENKUMHAM": "kemenkumham.go.id",
    "KENYA_AFRICA_NAIROBI": "Africa/Nairobi",
    "KENYA": "Kenya",
    "KERBEROS_SEC_DESC": "Kerberos es un protocolo de autenticación de redes de ordenadores que funciona sobre la base de \"tickets\" para permitir a los nodos comunicar sobre una red no segura para demostrar su identidad a otros de forma segura",
    "KERBEROS_SEC": "Kerberos",
    "KERBEROS_SHARED_KEY": "Contraseña de confianza de dominio",
    "KEY": "Clave",
    "KHAN_DESC": " Este plug-in de protocolo clasifica el tráfico http al host khan.co.kr",
    "KHAN": "Khan (khan.co.kr)",
    "KHANACADEMY": "Khan Academy",
    "KICKASSTORRENTS_DESC": " Kickass Torrents es un motor de búsqueda de torrents y magnets",
    "KICKASSTORRENTS": "KickAssTorrents",
    "KIK_DESC": "  Kik Messenger es un servicio chino de Mensajería instantánea  ",
    "KIK": "Kik Messenger",
    "KINDLE": "Kindle",
    "KINO_DESC": " Este plug-in de protocolo clasifica el tráfico HTTP al host kino.to",
    "KINO": "Kino",
    "KIRIBATI_PACIFIC_ENDERBURY": "Pacific/Enderbury",
    "KIRIBATI_PACIFIC_KIRITIMATI": "Pacific/Kiritimati",
    "KIRIBATI_PACIFIC_TARAWA": "Pacific/Tarawa",
    "KIRIBATI": "Kiribati",
    "KIWIBOX_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kiwibox.com",
    "KIWIBOX": "Kiwibox",
    "KLIKBCA_DESC": " Este plug-in de protocolo clasifica el tráfico HTTP al host klikbca.com",
    "KLIKBCA": "KlikBCA",
    "KOMPAS_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kompas.com",
    "KOMPAS": "Kompas",
    "KOMPASIANA_DESC": " Este plug-in de protocolo clasifica el tráfico http al host kompasiana.com",
    "KOMPASIANA": "Kompasiana",
    "KONAMINET_DESC": " Este plug-in de protocolo clasifica el tráfico http al host konaminet.jp. También clasifica el tráfico SSL al Common Name konaminet.jp",
    "KONAMINET": "KONAMI",
    "KOOLIM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host koolim.com",
    "KOOLIM": "KoolIm",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG": "Asia/Pyongyang",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF": "Korea (Democratic People's Republic of)",
    "KOREA_REPUBLIC_OF_ASIA_SEOUL": "Asia/Seoul",
    "KOREA_REPUBLIC_OF": "Korea (Republic of)",
    "KOREA": "Corea",
    "KOREACENTRAL": "(Asia Pacífico) Centro de Corea",
    "KOREASOUTH": "(Asia Pacífico) Sur de Corea",
    "KUWAIT_ASIA_KUWAIT": "Asia/Kuwait",
    "KUWAIT": "Kuwait",
    "KYRGYZSTAN_ASIA_BISHKEK": "Asia/Bishkek",
    "KYRGYZSTAN": "Kyrgyzstan",
    "L2TP_DESC": " Layer Two Tunneling Protocol (L2TP) es una extensión del Point-to-Point Tunneling Protocol (PPTP) utilizada por un proveedor de internet (ISP) para permitir la operación de una red privada virtual (VPN) sobre internet",
    "L2TP": "L2TP",
    "LAN_DESTINATIONS_GROUP": "Grupo de destinos de LAN",
    "LAN_DNS": "DNS de LAN",
    "LAN_IP_GROUP": "Grupo de IP de LAN",
    "LAN_PRI_DNS": "Servidor DNS primario de LAN",
    "LAN_RX": "LAN Rx",
    "LAN_SEC_DNS": "Servidor DNS secundario de LAN",
    "LAN_TX": "LAN Tx",
    "LAN": "LAN",
    "LANGUAGE": "Idioma",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE": "Asia/Vientiane",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC": "Lao People's Democratic Republic",
    "LAOS": "Laos",
    "LARGE": "Grande",
    "last_1_hour": "Última hora",
    "LAST_1_HOUR": "Última hora",
    "last_1_min": "Último minuto",
    "LAST_1_MIN": "Último minuto",
    "last_1_month": "Último mes",
    "LAST_1_MONTH": "Último mes",
    "last_1_week": "Última semana",
    "LAST_1_WEEK": "Última semana",
    "last_10_hours": "Últimas 10 horas",
    "LAST_10_HOURS": "Últimas 10 horas",
    "last_15_mins": "Últimos 15 minutos",
    "LAST_15_MINS": "Últimos 15 minutos",
    "last_2_hours": "Últimas 2 horas",
    "LAST_2_HOURS": "Últimas 2 horas",
    "last_2_mins": "Últimos 2 minutos",
    "LAST_2_MINS": "Últimos 2 minutos",
    "LAST_24_HOURS": "Últimas 24 horas",
    "last_24_hrs": "Últimas 24 horas",
    "last_30_mins": "Últimos 30 minutos",
    "LAST_30_MINS": "Últimos 30 minutos",
    "last_5_hours": "Últimas 5 horas",
    "LAST_5_HOURS": "Últimas 5 horas",
    "last_5_mins": "Últimos 5 minutos",
    "LAST_5_MINS": "Últimos 5 minutos",
    "LAST_ACTIVE": "Última actividad",
    "LAST_CONFIG_TEMPLATE_PUSH_FAILED": "Error en la última inserción de la plantilla de configuración",
    "LAST_CONNECTIVITY_TEST": "Última prueba de conectividad",
    "LAST_HEARTBEAT_RECEIVED_ON": "Último heartbeat recibido el",
    "LAST_KNOW_IP": "Última IP conocida",
    "LAST_KNOWN_IP": "Última IP conocida",
    "LAST_MODIFIED_BY": "Última modificación realizada por",
    "LAST_MODIFIED_ON": "Modificado por última vez el",
    "LAST_SYNC": "Última sincronización",
    "LAST_UPDATE": "Última actualización",
    "LAST_UPDATES": "Últimas actualizaciones",
    "LAST_UPGRADE_ON": "Actualizado por última vez el",
    "LASTEST_SYNC": "Última sincronización",
    "LATITUDE": "Latitud",
    "LATVIA_EUROPE_RIGA": "Europa/Riga",
    "LATVIA": "Latvia",
    "LAUNCH_CLOUDFORMATION_TEMPLATE_AWS_CONSOLE": "Iniciar plantilla de Cloudformation en la consola de AWS",
    "LAUNCH_CLOUDFORMATION": "Iniciar Cloudformation",
    "LDAP_CONNECTION_DOWN": "Conexión LDAP caída ",
    "LDAP_DESC": "  LDAP (Lightweight Directory Access Protocol) es un protocolo utilizado para acceder a servicios de directorio.  En entornos Windows se utiliza este protocolo para enviar consultas al Directorio Activo ",
    "LDAP_FAILURE": "Fallo LDAP",
    "LDAP_SETTINGS": "Configuración de LDAP",
    "LDAP_SUCCESS": "Exito LDAP",
    "LDAP": "LDAP",
    "LDAPS_DESC": " Secure LDAP es la versión segura del protocolo LDAP",
    "LDAPS": "LDAPS",
    "LEARN_TO_SETUP_EC2_INSTANCE": "Aprenda a configurar la instancia EC2",
    "LEBANON_ASIA_BEIRUT": "Asia/Beirut",
    "LEBANON": "Líbano",
    "LESOTHO_AFRICA_MASERU": "Africa/Maseru",
    "LESOTHO": "Lesotho",
    "LESS_THAN": "Menos de",
    "LIBERIA_AFRICA_MONROVIA": "Africa/Monrovia",
    "LIBERIA": "Liberia",
    "LIBYA": "Libia",
    "LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI": "Africa/Tripoli",
    "LIBYAN_ARAB_JAMAHIRIYA": "Libyan Arab Jamahiriya",
    "LIECHTENSTEIN_EUROPE_VADUZ": "Europa/Vaduz",
    "LIECHTENSTEIN": "Liechtenstein",
    "LIMITED_AVAILABILITY": "Disponibilidad limitada",
    "LIMITED": "Limitado",
    "LINGERIE_BIKINI": "Lencería/Bikini",
    "LINK_SCORE": "Puntuación de enlace",
    "LINUX_OS": "Sistema operativo Linux",
    "LINUX": "Linux",
    "LITHUANIA_EUROPE_VILNIUS": "Europa/Vilnius",
    "LITHUANIA": "Lithuania",
    "LOAD_BALANCER_IP_ADDRESS": "Dirección IP del equilibrador de carga",
    "LOAD_BALANCER": "Balanceador de carga",
    "LOAD_MORE": "Cargar más",
    "LOC_DEFAULT": "Usuario Itinerante",
    "LOCAL_EGRESS": "Salida local",
    "LOCAL_TIME_ZONE_CC_GROUP": "Zona horaria local del grupo de conectores de nube",
    "LOCATION_ALREADY_IN_USE_PLEASE_ENTER_A_NEW_LOCATION": "La ubicación no es válida. Introduzca una nueva.",
    "LOCATION_CREATION": "Creación de ubicación",
    "LOCATION_DETAILS_OPTIONAL": "Detalles de ubicación (opcional)",
    "LOCATION_DETAILS": "Detalles de ubicación",
    "LOCATION_GROUP_TYPE": "Tipo de ubicación",
    "LOCATION_GROUP": "Grupo de ubicaciones",
    "LOCATION_GROUPS": "Grupos de ubicaciones",
    "LOCATION_INFORMATION": "Información de ubicación",
    "LOCATION_MANAGEMENT": "Gestión de ubicación",
    "LOCATION_NAME": "Nombre de ubicación",
    "LOCATION_SUBLOCATION": "Ubicación/Sububicación",
    "LOCATION_TEMPLATE": "Plantilla de ubicación",
    "LOCATION_TEMPLATES": "Plantillas de ubicación",
    "LOCATION_TYPE": "Tipo de ubicación",
    "LOCATION_UNAUTHENTICATED_AUP_FREQUENCY": "Frecuencia de UAP personalizada (días)",
    "LOCATION": "Ubicación",
    "LOCATIONS": "Ubicaciones",
    "LOG_AND_CONTROL_FORWARDING": "Reenvío de registro y control",
    "LOG_AND_CONTROL_GATEWAY": "Pasarela de registro y control",
    "LOG_AND_CONTROL_GW": "GW de registro y control",
    "LOG_AND_CONTROL": "Registro y control",
    "LOG_GW_CONN_CLOSE": "Conexión activa de pasarela de registro cerrada.",
    "LOG_GW_CONN_SETUP_FAIL": "Fallo en la configuración de conexión de pasarela de registro (error interno).",
    "LOG_GW_CONNECT_FAIL": "Fallo en la conexión de pasarela de registro (error de red).",
    "LOG_GW_DNS_RESOLVE_FAIL": "Fallo en la resolución de DNS de pasarela de registro.",
    "LOG_GW_KA_FAIL": "Fallo en keepalive de conexión de pasarela de registro.",
    "LOG_GW_NO_CONN": "Conexión de pasarela de registro aún no iniciada por el cliente.",
    "LOG_GW_PAC_RESOLVE_FAIL": "Fallo en la resolución de PAC de pasarela de registro.",
    "LOG_GW_PAC_RESOLVE_NOIP": "La resolución de PAC de pasarela de registro no ha devuelto ningún IPS.",
    "LOG_GW_PROTO_MSG_ERROR": "Error de formato de mensaje en la respuesta de la pasarela de registro.",
    "LOG_GW_SRV_ERR_RESPONSE": "La pasarela de registro ha recibido del servidor una respuesta de error de HTTP.",
    "LOG_GW_UNHEALTHY": "La pasarela de registro no está en buen estado (estado transitorio).",
    "LOG_INFO_TYPE": "Tipo de información de registro",
    "LOG_STREAMING": "Streaming de registros",
    "LOG_TIME": "Tiempo de registro",
    "LOG_TYPE": "TIPO DE REGISTRO",
    "LOGGED_TIME": "Tiempo registrado",
    "LOGGING": "Registrando",
    "LOGIN_ID": "Identificador de inicio de sesión",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_GROUP": "Seleccione un nombre de grupo de App Connectors en el menú desplegable.",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_KEY": "Seleccione una clave de aprovisionamiento del menú desplegable",
    "LOGIN_TYPE": "Tipo de inicio de sesión",
    "LOGIN": "Inicio de sesión",
    "LOGS": "Registros",
    "LONGITUDE": "Longitud",
    "LOOKUP_URL_CATEGORY": "Clasificaciones de URL de búsqueda",
    "LOOKUP": "Búsqueda de URL",
    "LUXEMBOURG_EUROPE_LUXEMBOURG": "Europa/Luxemburgo",
    "LUXEMBOURG": "Luxemburgo",
    "MAC_ADDRESS": "Dirección MAC",
    "MAC_OS": "Mac",
    "MACAO_ASIA_MACAU": "Asia/Macau",
    "MACAO": "Macao",
    "MACAU": "Macao",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE": "Europa/Skopje",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF": "Macedonia (The Former Yugoslav Republic of)",
    "MACEDONIA": "Macedonia",
    "MADAGASCAR_INDIAN_ANTANANARIVO": "Indian/Antananarivo",
    "MADAGASCAR": "Madagascar",
    "MALAWI_AFRICA_BLANTYRE": "Africa/Blantyre",
    "MALAWI": "Malawi",
    "MALAYSIA_ASIA_KUALA_LUMPUR": "Asia/Kuala Lumpur",
    "MALAYSIA_ASIA_KUCHING": "Asia/Kuching",
    "MALAYSIA": "Malasia",
    "MALDIVES_INDIAN_MALDIVES": "Indian/Maldives",
    "MALDIVES": "Maldivas",
    "MALI_AFRICA_BAMAKO": "Africa/Bamako",
    "MALI": "Mali",
    "MALICIOUS_TLD": "TLD maliciosos",
    "MALTA_EUROPE_MALTA": "Europa/Malta",
    "MALTA": "Malta",
    "MALWARE_SITE": "Contenido Malicioso",
    "MANAGED_APP_DEF_ZPA": "Definición de aplicación administrada con ZPA",
    "MANAGED_APP_DEF": "Definición de aplicación administrada",
    "Management IP": "IP de gestión",
    "MANAGEMENT_DEFAULT_GATEWAY": "Pasarela predeterminada de gestión",
    "MANAGEMENT_DETAILS": "Detalles de gestión",
    "MANAGEMENT_DNS_SERVER": "Servidor DNS de gestión",
    "MANAGEMENT_INFORMATION": "Información de gestión",
    "MANAGEMENT_INTERFACE": "Interfaz de gestión",
    "MANAGEMENT_IP_ADDRESS_POOL": "Grupo de direcciones IP de gestión",
    "MANAGEMENT_IP_ADDRESS": "Dirección IP de gestión",
    "MANAGEMENT_IP": "IP de gestión",
    "MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS": "Dirección IP de pasarela saliente de gestión",
    "MANAGEMENT": "Gestión",
    "MANUAL_MANAGEMENT_IP": "IP de gestión manual",
    "MANUAL_SERVICE_IP": "IP de servicio manual",
    "MANUAL": "Manual",
    "MARIJUANA": "Marihuana",
    "MARSHALL_ISLANDS_PACIFIC_KWAJALEIN": "Pacific/Kwajalein",
    "MARSHALL_ISLANDS_PACIFIC_MAJURO": "Pacific/Majuro",
    "MARSHALL_ISLANDS": "Marshall Islands",
    "MARTINIQUE_AMERICA_MARTINIQUE": "America/Martinique",
    "MARTINIQUE": "Martinique",
    "MATURE_HUMOR": "Humor adulto",
    "MAURITANIA_AFRICA_NOUAKCHOTT": "Africa/Nouakchott",
    "MAURITANIA": "Mauritania",
    "MAURITIUS_INDIAN_MAURITIUS": "Indian/Mauritius",
    "MAURITIUS": "Mauritius",
    "MAX_AMF_NUMBER": "Se pueden añadir un máximo de 5 AMF.",
    "MAX_CAPACITY": "Capacidad máxima",
    "MAX_CHARACTER_LIMIT_EXCEEDED": "Superado el límite máximo de caracteres",
    "MAX_EC_COUNT": "Recuento máximo",
    "MAX_INTERFACES_NUMBER": "Se ha añadido el número máximo de interfaces.",
    "MAX_LEASE_TIME": "Tiempo máximo de arrendamiento (s)",
    "MAX_NUM_DESINATION_ADRESS_IS_1000": "El número máximo de direcciones de destino permitidas por regla es 1000.",
    "MAX_REUSE_PROVISIONING_KEY": "Reutilización máxima de clave de aprovisionamiento",
    "MAX_STATIC_ROUTES_NUMBER": "El número máximo de rutas estáticas es 32.",
    "MAX_SUB_INTERFACES_NUMBER": "Se ha alcanzado el número máximo de interfaces.",
    "MAX_SUBINTERFACE_STATIC_LEASES": "El número máximo de arrendamientos estáticos es 32.",
    "MAX_USER_TUNNELS_PER_CC": "Número máximo de túneles de usuario por conector",
    "MAX_VALUE_LIMIT_ERROR": "El valor supera el límite de",
    "MAX_WAN_INTERFACES_NUMBER": "Se puede añadir un máximo de 2 interfaces de WAN.",
    "MAX": "Max",
    "MAYOTTE_INDIAN_MAYOTTE": "Indian/Mayotte",
    "MAYOTTE": "Mayotte",
    "ME_CENTRAL_1": "Oriente Medio (EAU)",
    "ME_CENTRAL1_A": "me-central1-a",
    "ME_CENTRAL1_B": "me-central1-b",
    "ME_CENTRAL1_C": "me-central1-c",
    "ME_CENTRAL1": "me-central1",
    "ME_SOUTH_1": "Oriente Medio (Baréin)",
    "ME_SOUTH_1A": "me-south-1a",
    "ME_SOUTH_1B": "me-south-1b",
    "ME_SOUTH_1C": "me-south-1c",
    "ME_WEST1_A": "me-west1-a",
    "ME_WEST1_B": "me-west1-b",
    "ME_WEST1_C": "me-west1-c",
    "ME_WEST1": "me-west1",
    "MEDIUM": "Medio",
    "MEMORY": "Memoria",
    "MEXICO_AMERICA_CANCUN": "America/Cancun",
    "MEXICO_AMERICA_CHIHUAHUA": "America/Chihuahua",
    "MEXICO_AMERICA_HERMOSILLO": "America/Hermosillo",
    "MEXICO_AMERICA_MAZATLAN": "America/Mazatlan",
    "MEXICO_AMERICA_MERIDA": "America/Merida",
    "MEXICO_AMERICA_MEXICO_CITY": "America/Mexico City",
    "MEXICO_AMERICA_MONTERREY": "America/Monterrey",
    "MEXICO_AMERICA_TIJUANA": "America/Tijuana",
    "MEXICO": "Méjico",
    "MGCP_CA_DESC": "Media Gateway Control Protocol CA Service",
    "MGCP_CA": "Agente de llamadas MGCP",
    "MGCP_DESC": " El protocolo MGCP protocol se utiliza como protocolo de señalización en aplicaciones de voz IP  ",
    "MGCP_UA_DESC": "Media Gateway Control Protocol UA Service",
    "MGCP_UA": "Agente de usuarios MGCP",
    "MGCP": "MGCP",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE": "Pacific/Kosrae",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE": "Pacific/Ponape",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK": "Pacific/Truk",
    "MICRONESIA_FEDERATED_STATES_OF": "Micronesia (Federated States of)",
    "MICRONESIA": "Micronesia",
    "MICROSOFT_AZURE": "Microsoft Azure",
    "MICROSOFT_HYPER_V": "Microsoft Hyper V",
    "MILITANCY_HATE_AND_EXTREMISM": "Militancia/Odio y Extremismo",
    "MILITARY": "Militar",
    "MIN_CAPACITY": "Capacidad mínima",
    "MIN_VALUE_LIMIT_ERROR": "El valor es inferior al límite de",
    "MINUTES": "Minutos",
    "MISCELLANEOUS_OR_UNKNOWN": "Varios o desconocido",
    "MODEL_NUMBER": "Número de modelo",
    "MODEL": "Modelo",
    "MOLDOVA_EUROPE_CHISINAU": "Europa/Chisinau",
    "MOLDOVA": "Moldova",
    "MONACO_EUROPE_MONACO": "Europa/Monaco",
    "MONACO": "Monaco",
    "MONDAY": "Lunes",
    "MONGOLIA_ASIA_CHOIBALSAN": "Asia/Choibalsan",
    "MONGOLIA_ASIA_HOVD": "Asia/Hovd",
    "MONGOLIA_ASIA_ULAANBAATAR": "Asia/Ulaanbaatar",
    "MONGOLIA": "Mongolia",
    "MONTENEGRO_EUROPE_PODGORICA": "Europa/Podgorica",
    "MONTENEGRO": "Montenegro",
    "MONTSERRAT_AMERICA_MONTSERRAT": "America/Montserrat",
    "MONTSERRAT": "Montserrat",
    "MORE_ITEMS_SELECTED": "Más elementos seleccionados",
    "MOROCCO_AFRICA_CASABLANCA": "Africa/Casablanca",
    "MOROCCO": "Marruecos",
    "MOZAMBIQUE_AFRICA_MAPUTO": "Africa/Maputo",
    "MOZAMBIQUE": "Mozambique",
    "MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS": "Guía de implementación de Microsoft Azure para NSS",
    "MSI_URL_32BITS": "URL MSI (32 bits)",
    "MSI_URL_64BITS": "URL MSI (64 bits)",
    "MSN_DESC": "  El protocolo MSN permite el intercambio de mensajes instantáneos. El protocolo MSN lo utiliza el software Microsoft Messenger hecho por Microsoft ",
    "MSN_EXPLORER": "MSN Explorer",
    "MSN_GROUPS_DEPRECATED": "Grupos MSN",
    "MSN_GROUPS_DESC": " MSN Groups era un sitio web parte de la red MSN que alojaba comunidades online, y que contenía páginas web, imágenes alojadas, y un boletín de noticias. MSN Groups fue cerrado en on February 2009 como parte de una migración de aplicaciones y servicios online a la marca Windows Live, más tarde renovada como Windows Live Groups",
    "MSN_GROUPS": "Grupos MSN",
    "MSN_MESSENGER": "MSN Messenger",
    "MSN_MSDW": "MSN MSDW",
    "MSN_SEARCH_DESC": " Este protocolo se utiliza para enviar peticiones de usuario al motor de búsqueda MSN Live",
    "MSN_SEARCH": "MSN Search",
    "MSN_VIDEO_DESC": " Este protocolo se utiliza por MSN Messenger para conversaciones de video (no se utiliza más desde la versión 8.X de MSN)",
    "MSN_VIDEO": "MSN Video",
    "MSN_WEB_MESSENGER": "MSN Messenger",
    "MSN": "MSN",
    "MSNMOBILE_DESC": "  MSN Mobile es la mensajería instantánea de MSN para móviles ",
    "MSNMOBILE": "MSN Mobile",
    "MTS_ERROR": "Error de servidor de aplicación MTS.",
    "MTU_TITLE": "MTU",
    "MULTIFEEDLOG": "Métricas",
    "MULTIPLE_APPLIANCES_ADDED_INFO": "Puede ver los nuevos dispositivos en la página Dispositivos. Para obtener más información, consulte el {1}Portal de ayuda{2} de conectores.",
    "MULTIPLE_APPLIANCES_ADDED": "nuevos dispositivos han sido añadidos a su inquilino.",
    "MULTIPLE": "Múltiple",
    "MUSIC": "Música",
    "MY_ACTIVATION_STATUS": "MI ESTADO DE ACTIVACIÓN",
    "MY_PROFILE": "Mi perfil",
    "MYANMAR_ASIA_RANGOON": "Asia/Rangoon",
    "MYANMAR": "Myanmar",
    "NA": "N/A",
    "NAME_MAX_LIMIT_ERROR": "Este campo no puede contener más de 255 caracteres",
    "NAME_VALUE_PAIRS": "Pares de nombre y valor",
    "NAME": "Nombre",
    "NAMESPACE_OPTIONAL": "Espacio de nombres (opcional)",
    "NAMESPACE": "Espacio de nombres",
    "NAMIBIA_AFRICA_WINDHOEK": "Africa/Windhoek",
    "NAMIBIA": "Namibia",
    "NANOLOG_STREAMING_SERVICES": "Nanolog Streaming Service",
    "NAT_IP_ADDRESS": "Dirección IP de NAT",
    "NAURU_PACIFIC_NAURU": "Pacific/Nauru",
    "NAURU": "Nauru",
    "NAVIGATE_TO_ADMINISTRATION": "Navegar a administración",
    "NEPAL_ASIA_KATMANDU": "Asia/Katmandu",
    "NEPAL": "Nepal",
    "NET_MASK": "Máscara de red",
    "NETBIOS_DESC": "NetBIOS Name/Datagram/Session Service",
    "NETBIOS": "NetBIOS",
    "NETHERLANDS_ANTILLES_AMERICA_CURACAO": "America/Curacao",
    "NETHERLANDS_ANTILLES": "Netherlands Antilles",
    "NETHERLANDS_EUROPE_AMSTERDAM": "Europa/Amsterdam",
    "NETHERLANDS": "Países Bajos",
    "NETMEETING_DESC": "Microsoft NetMeeting permite a los usuarios hacer teleconferencia usando internet como medio de transmisión ",
    "NETMEETING_ILS_DESC": "  NetMeeting ILS es el protocolo utilizado entre Netmeeting y los Internet Locator Servers (ILS). Netmeeting es un cliente de VoIP y videoconferencia multipunto incluido en múltiples versiones de Microsoft Windows ",
    "NETMEETING_ILS": "NetMeeting ILS",
    "NETMEETING": "NetMeeting",
    "NETWORK_INTERFACE_ID": "ID de interfaz de red",
    "NETWORK_PROTOCOL_ADFS": "Cualquier FS distribuido",
    "NETWORK_PROTOCOL_AH": "Cabecera de autenticación de IP6",
    "NETWORK_PROTOCOL_AHIP": "cualquier protocolo interno del host",
    "NETWORK_PROTOCOL_APES": "cualquier cifr. privado esquema",
    "NETWORK_PROTOCOL_ARGUS": "Argus",
    "NETWORK_PROTOCOL_AX25": "Marcos AX.25",
    "NETWORK_PROTOCOL_BHA": "BHA",
    "NETWORK_PROTOCOL_BLT": "Transferencia masiva de datos",
    "NETWORK_PROTOCOL_BRSATMON": "Supervisión de SATNET de BackRoom",
    "NETWORK_PROTOCOL_CARP": "CARP",
    "NETWORK_PROTOCOL_CFTP": "CFTP",
    "NETWORK_PROTOCOL_CHAOS": "Chaos",
    "NETWORK_PROTOCOL_CMTP": "Transporte de mensajes de control",
    "NETWORK_PROTOCOL_CPHB": "Comp. Prot. HeartBeat",
    "NETWORK_PROTOCOL_CPNX": "Comp. Prot. Net. Executive",
    "NETWORK_PROTOCOL_DDP": "Entrega de datagramas",
    "NETWORK_PROTOCOL_DGP": "distinto prot. de puerta de enlace",
    "NETWORK_PROTOCOL_DSTOPTS": "Opción de destino IP6",
    "NETWORK_PROTOCOL_EGP": "protocolo de puerta de enlace exterior",
    "NETWORK_PROTOCOL_EMCON": "EMCON",
    "NETWORK_PROTOCOL_ENCAP": "encabezado de encapsulación",
    "NETWORK_PROTOCOL_EON": "ISO cnlp",
    "NETWORK_PROTOCOL_ESP": "Contenido de encapsulado de seguridad de IP6",
    "NETWORK_PROTOCOL_ETHERIP": "Encapsulación IP Ethernet",
    "NETWORK_PROTOCOL_FRAGMENT": "Encabezado de fragmentación IP6",
    "NETWORK_PROTOCOL_GGP": "puerta de enlace^2 (obsoleta)",
    "NETWORK_PROTOCOL_GMTP": "GMTP",
    "NETWORK_PROTOCOL_GRE": "Encap. de enrutamiento general",
    "NETWORK_PROTOCOL_HELLO": "protocolo de enrutamiento de hello",
    "NETWORK_PROTOCOL_HMP": "Supervisión de host",
    "NETWORK_PROTOCOL_ICMP": "protocolo de mensajes de control",
    "NETWORK_PROTOCOL_ICMPV6": "ICMP6",
    "NETWORK_PROTOCOL_IDP": "xns idp",
    "NETWORK_PROTOCOL_IDPR": "Enrutamiento de políticas de InterDomain",
    "NETWORK_PROTOCOL_IDRP": "InterDomain",
    "NETWORK_PROTOCOL_IGMP": "protocolo de gestión de grupos",
    "NETWORK_PROTOCOL_IGP": "NSFNET-IGP",
    "NETWORK_PROTOCOL_IGRP": "Cisco/GXS IGRP",
    "NETWORK_PROTOCOL_IL": "Protocolo de transporte IL",
    "NETWORK_PROTOCOL_INLSP": "Seguridad de capa de red de integ.",
    "NETWORK_PROTOCOL_INP": "Merit Internodal",
    "NETWORK_PROTOCOL_IP": "ficticio para IP",
    "NETWORK_PROTOCOL_IPCOMP": "compresión de carga útil (IPComp)",
    "NETWORK_PROTOCOL_IPCV": "Utilidad básica de paquetes",
    "NETWORK_PROTOCOL_IPEIP": "IP encapsulada en IP",
    "NETWORK_PROTOCOL_IPPC": "Base de paquetes de Pluribus",
    "NETWORK_PROTOCOL_IPV4": "Encapsulado IPv4",
    "NETWORK_PROTOCOL_IPV6": "Encabezado IP6",
    "NETWORK_PROTOCOL_IRTP": "Transacción fiable",
    "NETWORK_PROTOCOL_KRYPTOLAN": "Kryptolan",
    "NETWORK_PROTOCOL_LARP": "Resolución de dirección Locus",
    "NETWORK_PROTOCOL_LEAF1": "Leaf-1",
    "NETWORK_PROTOCOL_LEAF2": "Leaf-2",
    "NETWORK_PROTOCOL_MEAS": "Subsistemas de medición DCN",
    "NETWORK_PROTOCOL_MHRP": "Enrutamiento de hosts móviles",
    "NETWORK_PROTOCOL_MICP": "Control Int.ing móvil",
    "NETWORK_PROTOCOL_MOBILE": "Movilidad de IP",
    "NETWORK_PROTOCOL_MTP": "Transporte multicast",
    "NETWORK_PROTOCOL_MUX": "Multiplexación",
    "NETWORK_PROTOCOL_ND": "Sun net disk proto (temp.)",
    "NETWORK_PROTOCOL_NHRP": "Resolución del siguiente salto",
    "NETWORK_PROTOCOL_NO_NEXT_HDR": "IP6 sin siguiente encabezado",
    "NETWORK_PROTOCOL_NSP": "Servicios de red",
    "NETWORK_PROTOCOL_NVPII": "voz de red",
    "NETWORK_PROTOCOL_OLD_DIVERT": "Pseudo-proto de desvío antiguo",
    "NETWORK_PROTOCOL_OSPFIGP": "OSPFIGP",
    "NETWORK_PROTOCOL_PFSYNC": "PFSYNC",
    "NETWORK_PROTOCOL_PGM": "PGM",
    "NETWORK_PROTOCOL_PIGP": "puerta de enlace interior privada",
    "NETWORK_PROTOCOL_PIM": "Mcast independiente de protocolos",
    "NETWORK_PROTOCOL_PRM": "Medición de radio de paquetes",
    "NETWORK_PROTOCOL_PUP": "pup",
    "NETWORK_PROTOCOL_PVP": "Protocolo de vídeo de paquetes",
    "NETWORK_PROTOCOL_RAW": "paquete IP sin procesar",
    "NETWORK_PROTOCOL_RCCMON": "Supervisión de RCC de BBN",
    "NETWORK_PROTOCOL_RDP": "Datos fiables",
    "NETWORK_PROTOCOL_ROUTING": "Encabezado de enrutamiento IP6",
    "NETWORK_PROTOCOL_RSVP": "reserva de recurso",
    "NETWORK_PROTOCOL_RVD": "Disco virtual remoto",
    "NETWORK_PROTOCOL_SATEXPAK": "SATNET/Backroom EXPAK",
    "NETWORK_PROTOCOL_SATMON": "Supervisión de Satnet",
    "NETWORK_PROTOCOL_SCCSP": "Seguridad de comunicaciones de semáforo",
    "NETWORK_PROTOCOL_SCTP": "SCTP",
    "NETWORK_PROTOCOL_SDRP": "Enrutamiento de demanda de origen",
    "NETWORK_PROTOCOL_SEP": "Intercambio secuencial",
    "NETWORK_PROTOCOL_SKIP": "SKIP",
    "NETWORK_PROTOCOL_SRPC": "Protocolo Strite RPC",
    "NETWORK_PROTOCOL_ST": "Stream protocol II",
    "NETWORK_PROTOCOL_SVMTP": "VMTP seguro",
    "NETWORK_PROTOCOL_SWIPE": "IP con cifrado",
    "NETWORK_PROTOCOL_TCF": "TCF",
    "NETWORK_PROTOCOL_TCP": "TCP",
    "NETWORK_PROTOCOL_TLSP": "Seguridad de la capa de transporte",
    "NETWORK_PROTOCOL_TP": "Negociación con tp-4 w/ class",
    "NETWORK_PROTOCOL_TPC": "Conexión de terceros",
    "NETWORK_PROTOCOL_TPXX": "Transporte TP++",
    "NETWORK_PROTOCOL_TRUNK1": "Trunk-1",
    "NETWORK_PROTOCOL_TRUNK2": "Trunk-2",
    "NETWORK_PROTOCOL_TTP": "TTP",
    "NETWORK_PROTOCOL_UDP": "UDP - protocolo de datagramas de usuario",
    "NETWORK_PROTOCOL_VINES": "Banyon VINES",
    "NETWORK_PROTOCOL_VISA": "Protocolo VISA",
    "NETWORK_PROTOCOL_VMTP": "VMTP",
    "NETWORK_PROTOCOL_WBEXPAK": "WIDEBAND EXPAK",
    "NETWORK_PROTOCOL_WBMON": "Supervisión WIDEBAND",
    "NETWORK_PROTOCOL_WSN": "Red Wang Span",
    "NETWORK_PROTOCOL_XNET": "Cross Net Debugger",
    "NETWORK_PROTOCOL_XTP": "XTP",
    "NETWORK_SERVICE_GROUP": "Grupo de servicios de red",
    "NETWORK_SERVICE_GROUPS": "Grupos de servicios de red",
    "NETWORK_SERVICE": "Servicio de red",
    "NETWORK_SERVICES_GROUP": "Grupo de servicios de red",
    "NETWORK_SERVICES": "Servicios de red",
    "NETWORK_TRAFFIC": "Tráfico de red",
    "NEW_API_KEY": "Nueva clave de API",
    "NEW_CALEDONIA_PACIFIC_NOUMEA": "Pacific/Noumea",
    "NEW_CALEDONIA": "New Caledonia",
    "NEW_PASSWORD_EQUALITY": "La nueva contraseña y la contraseña actual deben ser diferentes",
    "NEW_PASSWORD_PLACEHOLDER": "Debe tener al menos 8 caracteres y contener 1 dígito, 1 letra mayúscula y 1 carácter especial",
    "NEW_PASSWORD": "Nueva contraseña",
    "NEW_ZEALAND_PACIFIC_AUCKLAND": "Pacific/Auckland",
    "NEW_ZEALAND_PACIFIC_CHATHAM": "Pacific/Chatham",
    "NEW_ZEALAND": "Nueva Zelanda",
    "NEW": "Nuevo",
    "NEWLY_REG_DOMAINS": "Dominios registrados recientemente",
    "NEWS_AND_MEDIA": "Noticias y medios",
    "NEXT_PERIODIC_UPDATE": "La próxima actualización periódica se realiza según la programación establecida para máquinas virtuales en",
    "NEXT_UPDATE": "La próxima actualización periódica de software será el ",
    "NEXT": "Siguiente",
    "NFL_DESC": " Este plug-in de protocolo clasifica el tráfico http al host nfl.com",
    "NFL": "NFL",
    "NFLMOBILE": "NFL Mobile",
    "NFS_DESC": " El protocolo NFS ofrece acceso remoto transparente a sistemas de archivos compartidos a través de redes según se describe en el RFC 1813",
    "NFS": "NFS",
    "NGAP_SCTP_DESC": "El protocolo de aplicación NG (NGAP) proporciona la señalización del plano de control entre el nodo NG-RAN y la función de gestión de acceso y movilidad (AMF), y proporciona señalización NAS para el equipo de usuario (UE) y AMF",
    "NGAP_SCTP": "NGAP-SCTP",
    "NGAP_UDP_DESC": "El protocolo de aplicación NG (NGAP) está encapsulado como UDP para admitir el transporte a través de redes que no admiten SCTP",
    "NGAP_UDP": "NGAP-UDP",
    "NICARAGUA_AMERICA_MANAGUA": "America/Managua",
    "NICARAGUA": "Nicaragua",
    "NIGER_AFRICA_NIAMEY": "Africa/Niamey",
    "NIGER": "Niger",
    "NIGERIA_AFRICA_LAGOS": "Africa/Lagos",
    "NIGERIA": "Nigeria",
    "NIUE_PACIFIC_NIUE": "Pacific/Niue",
    "NIUE": "Niue",
    "NLOCKMGR_DESC": " El network lock manager es una utilidad que trabaja junto con el Network File System (NFS) para proveer un bloqueo de archivos y registros estilo System V sobre la red",
    "NLOCKMGR": "nlockmgr",
    "NLSP_DESC": " NetWare Link Services Protocol (NLSP) proporciona un enrutamiento de estado de enlace para redes Internetwork Packet Exchange",
    "NLSP": "NLSP",
    "NMAP_DESC": "Nmap (Network Mapper) es un escáner de seguridad utiliizado para descubrir hosts y servicios en una red de computadores, creando un mapa de la red ",
    "NMAP": "Nmap",
    "NNTP_DESC": "  El Network News Transport Protocol (NNTP) se usa para la distribución, búsqueda, recuperación y publicación de artículos de noticias de la red utilizando utilizando un mecanismo fiable basado en flujos ",
    "NNTP": "NNTP",
    "NNTPS_DESC": "  Versión segura del protocolo NNTP ",
    "NNTPS": "SecureNNTP",
    "NO_ACTIVATION_PENDING": "No hay ninguna activación pendiente",
    "NO_BC_GROUPS_AVAILABLE_FOR_SELECTED_LOCATION": "Los grupos de conectores de sucursales no están disponibles para la ubicación seleccionada.",
    "NO_DATA_AVAILABLE_AWS_ACCOUNT_GROUP": "No hay datos disponibles\n\nPara crear un grupo de cuentas de AWS\n\nvaya a\n\nAdmin > Integraciones de socios\n\n\n",
    "NO_DATA": "No se han encontrado elementos coincidentes",
    "NO_DESCRIPTION": "Sin descripción",
    "NO_GROUPING": "Tráfico global",
    "NO_ITEMS_AVAILABLE": "No se han encontrados datos",
    "NO_MATCHING_ITEMS_FOUND": "No se han encontrado elementos coincidentes",
    "NO_MORE_DHCP_OPTIONS_AVAILABLE": "No hay más opciones de DHCP disponibles",
    "NO_OF_CLOUD_CONNECTOR_GROUPS": "Número de grupos de Cloud Connectors",
    "NO_OF_CLOUD_CONNECTORS": "Número de Cloud Connectors",
    "NO_OF_DUPLICATES_IP": "N.º de IP duplicadas",
    "NO_OF_EDGE_CONNECTOR_GROUPS": "N.º de grupos de Cloud Connectors",
    "NO_OF_PRIVATE_IP_ADDRESSES": "N.º de direcciones IP privadas",
    "NO_PENDING_UPGRADES": "No hay actualizaciones pendientes",
    "NO_PRESIGNED_URL_WAS_GENERATED": "No se ha generado ninguna dirección URL prefirmada.",
    "NO_REGION_WAS_PREVIOUS_SELECTED_TEXT": "No se ha seleccionado ninguna región en Event Grid para usarla en la cuenta de almacenamiento.",
    "NO_REGION_WAS_PREVIOUS_SELECTED": "No se ha seleccionado ninguna región",
    "NO_STATIC_LEASE_CONFIGURED": "No se ha configurado ningún arrendamiento estático",
    "NO_SUBSCRIPTION_AVAILABLE": "No hay ninguna suscripción disponible.",
    "NO_VALUE_SELECTED": "No se ha seleccionado ningún valor",
    "NO_WIDGET_DATA": "No hay datos para el rango de tiempo seleccionado",
    "NO": "No",
    "NON_CATEGORIZABLE": "No categorizable",
    "NON_NUMERIC_VALUE": "Este campo solo debería contener números",
    "NONE": "Ninguno",
    "NORFOLK_ISLAND_PACIFIC_NORFOLK": "Pacific/Norfolk",
    "NORFOLK_ISLAND": "Norfolk Island",
    "NORTH_KOREA": "Korea del Norte",
    "NORTH_MACEDONIA": "Macedonia del Norte",
    "NORTHAMERICA_NORTHEAST1_A": "northamerica-northeast1a",
    "NORTHAMERICA_NORTHEAST1_B": "northamerica-northeast1b",
    "NORTHAMERICA_NORTHEAST1_C": "northamerica-northeast1c",
    "NORTHAMERICA_NORTHEAST1": "northamerica-northeast1",
    "NORTHAMERICA_NORTHEAST2_A": "northamerica-northeast2a",
    "NORTHAMERICA_NORTHEAST2_B": "northamerica-northeast2b",
    "NORTHAMERICA_NORTHEAST2_C": "northamerica-northeast2c",
    "NORTHAMERICA_NORTHEAST2": "northamerica-northeast2",
    "NORTHCENTRALUS": "(EE. UU.) Centro-Norte de EE. UU.",
    "NORTHCENTRALUSSTAGE": "(EE. UU.) Centro-Norte de EE. UU. (Stage)",
    "NORTHERN_EUROPE": "Europa septentrional",
    "NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN": "Pacific/Saipan",
    "NORTHERN_MARIANA_ISLANDS": "Northern Mariana Islands",
    "NORTHEUROPE": "(Europa) Norte de Europa",
    "NORWAY_EUROPE_OSLO": "Europa/Oslo",
    "NORWAY": "Noruega",
    "NORWAYEAST": "(Europa) Este de Noruega",
    "NORWAYWEST": "(Europa) Oeste de Noruega",
    "NOT_AVAILABLE": "No disponible",
    "NOT_DEPLOYED": "Listo para implementar",
    "NOT_NULL": "No es nulo",
    "NOT_SPECIFIED": "No especificado",
    "NSS_CLOUD_FEED_API_URL": "URL de la API",
    "NSS_CLOUD_FEED_AUTHENTICATION_URL": "URL de autorización",
    "NSS_CLOUD_FEED_AWS_ACCESS_ID": "ID de acceso de AWS",
    "NSS_CLOUD_FEED_AWS_SECRET_KEY": "Clave secreta de AWS",
    "NSS_CLOUD_FEED_CLIENT_ID": "ID de cliente",
    "NSS_CLOUD_FEED_CLIENT_SECRET": "Secreto de cliente",
    "NSS_CLOUD_FEED_GENERAL": "General",
    "NSS_CLOUD_FEED_GRANT_TYPE": "Tipo de subvención",
    "NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Notación de matriz JSON",
    "NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Tamaño máximo de lote",
    "NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Autenticación OAuth 2.0",
    "NSS_CLOUD_FEED_SCOPE": "Alcance",
    "NSS_CLOUD_FEED_SIEM_TYPE": "Tipo de SIEM",
    "NSS_CLOUD_FEEDS_API_URL": "URL de la API",
    "NSS_CLOUD_FEEDS_FEED_NAME": "Nombre del feed",
    "NSS_CLOUD_FEEDS_FEED_OVERVIEW": "Resumen de feed",
    "NSS_CLOUD_FEEDS_FEED_TYPE": "TIPO DE FEED",
    "NSS_CLOUD_FEEDS_LOG_TYPE": "TIPO DE REGISTRO",
    "NSS_CLOUD_FEEDS_S3_FOLDER_URL": "URL de carpeta S3",
    "NSS_CLOUD_FEEDS_SIEM_TYPE": "Tipo de SIEM",
    "NSS_CLOUD_FEEDS_STATUS": "Estado",
    "NSS_CLOUD_FEEDS": "Feeds NSS de nube",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Grupos de Cloud/Branch Connectors",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Conector de sucursal/nube",
    "NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Direcciones IP de cliente",
    "NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Tipos de solicitudes de DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Códigos de respuesta de DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Tipos de respuesta de DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Respuestas de DNS",
    "NSS_FEED_DNS_FILTERS_DOMAINS": "Dominios",
    "NSS_FEED_DNS_FILTERS_DURATIONS": "Duraciones",
    "NSS_FEED_DNS_FILTERS_LOCATIONS": "Ubicaciones",
    "NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Acción de política",
    "NSS_FEED_DNS_FILTERS_RULE_NAME": "Nombre de la regla",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_ADDRESS": "Direcciones IP del servidor",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_PORTS": "Puertos del servidor",
    "NSS_FEED_DUPLICATE_LOGS": "Duplicar registros",
    "NSS_FEED_EC_METRICS_RECORD_TYPE": "Tipo de registro de métricas",
    "NSS_FEED_ESCAPE_CHARACTER": "Carácter de escape de feed",
    "NSS_FEED_FILTERS": "Filtros",
    "NSS_FEED_FORMATTING": "APLICANDO FORMATO",
    "NSS_FEED_GENERAL": "General",
    "NSS_FEED_LOG_TYPE": "TIPO DE REGISTRO",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Máquina virtual de Cloud/Branch Connector",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Máquina virtual de Cloud/Branch Connector",
    "NSS_FEED_NAME": "Nombre del feed",
    "NSS_FEED_OUTPUT_FORMAT": "Formato de salida de feed",
    "NSS_FEED_OUTPUT_TYPE": "TIPO DE SALIDA DE FEED",
    "NSS_FEED_SERVER": "Servidor NSS",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Grupos de Cloud/Branch Connectors",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Conector de sucursal/nube",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Direcciones IP de cliente",
    "NSS_FEED_SESSION_FILTERS_FIREWALL_LOG_TYPE": "Tipo de registro de firewall",
    "NSS_FEED_SESSION_FILTERS_GATEWAY": "Pasarela",
    "NSS_FEED_SESSION_FILTERS_LOCATIONS": "Ubicaciones",
    "NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Servicios de red",
    "NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Acción de política",
    "NSS_FEED_SESSION_FILTERS_RULE_NAME": "Nombre de la regla",
    "NSS_FEED_SESSION_LOG_TYPE": "Tipo de registro de sesión",
    "NSS_FEED_SIEM_CONNECTIVITY": "Conectividad SIEM",
    "NSS_FEED_SIEM_DESTINATION_TYPE": "Tipo de destino SIEM",
    "NSS_FEED_SIEM_FQDN": "FQDN DE SIEM",
    "NSS_FEED_SIEM_IP_ADDRESS": "Dirección IP de SIEM",
    "NSS_FEED_SIEM_RATE_LIMIT": "Límite de velocidad de SIEM (eventos por segundo)",
    "NSS_FEED_SIEM_RATE": "Velocidad de SIEM",
    "NSS_FEED_SIEM_TCP_PORT": "Puerto TPC de SIEM",
    "NSS_FEED_STATUS": "Estado",
    "NSS_FEED_TIMEZONE": "Zona horaria",
    "NSS_FEED": "FEED DE NSS",
    "NSS_FEEDS_AGGREGATE_LOGS": "Agregar registros",
    "NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS": "Registros de sesión y agregación",
    "NSS_FEEDS_DUPLICATE_LOG": "Duplicar registros",
    "NSS_FEEDS_FEED_ATTRIBUTES": "Atributos de feed",
    "NSS_FEEDS_FEED_NAME": "Nombre del feed",
    "NSS_FEEDS_FEED_OUTPUT_FORMAT": "Formato de salida de feed",
    "NSS_FEEDS_FEED_OVERVIEW": "Resumen de feed",
    "NSS_FEEDS_FEED_TYPE": "TIPO DE FEED",
    "NSS_FEEDS_FULL_SESSION_LOGS": "Registros de sesión completos",
    "NSS_FEEDS_LOG_FILTER": "Filtro de registro",
    "NSS_FEEDS_LOG_TYPE": "TIPO DE REGISTRO",
    "NSS_FEEDS_NSS_SERVER_TEXT": "Servidor NSS",
    "NSS_FEEDS_OUTPUT_DESTINATION": "DESTINO DE SALIDA",
    "NSS_FEEDS_SIEM_RATE": "Velocidad de SIEM",
    "NSS_FEEDS_STATUS": "Estado",
    "NSS_FEEDS_TIMEZONE": "Zona horaria",
    "NSS_FEEDS_USER_OBFUSCATION": "Ofuscación de usuarios",
    "NSS_FEEDS": "Feeds de NSS",
    "NSS_FOR_FIREWALL_AND_EC": "NSS para firewall, conector de nube y de sucursal",
    "NSS_FOR_FIREWALL": "NSS para cortafuegos",
    "NSS_GCP_DEPLOYMENT_GUIDE": "Guía de implementación de Google Cloud Platform para NSS",
    "NSS_LOGGING": "Registro de NSS",
    "NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Número de usuarios",
    "NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Máximo de solicitudes de DNS por hora",
    "NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR": "Máximo de sesiones por hora",
    "NSS_SERVER_DEPLOYMENT_PLATFORM": "Plataforma",
    "NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE": "Descargar dispositivo virtual NSS",
    "NSS_SERVER": "Servidor NSS",
    "NSS_SERVERS": "Servidores NSS",
    "NSS_TYPE": "Tipo de NSS",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC": "Implementación de dispositivo virtual NSS",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT": "IMPLEMENTACIÓN DE DISPOSITIVO VIRTUAL NSS",
    "NSS_VIRTUAL_MACHINE": "Máquina virtual NSS",
    "NTP_DESC": "Network Time Protocol es un protocolo de red para sincronizar relojes entre sistemas de computadores sobre redes de conmutación de paquetes de latencia variable",
    "NTP": "NTP",
    "NTV_DESC": " Este plug-in de protocolo clasifica el tráfico http al host ntv.co.jp",
    "NTV": "NTV",
    "NUDITY_DESC": " Sitios que proporcionan imágenes de desnudos tanto artísticas como no artísticas en cual quier soporte (escultura, fotografías, pinturas, etc.)",
    "NUDITY": "Desnudos",
    "NUMBER_ABBR": "N.º",
    "NUMBER_OF_ACCOUNTS": "Número de cuentas",
    "NUMBER_OF_CONNECTORS": "N.º de conectores",
    "NUMBER_OF_CORES": "Número de núcleos",
    "NUMBER_OF_RECORDS_DISPLAYED": "Número de registros mostrados",
    "NUMBER_OF_RECORDS_FETCHED_SO_FAR": "Número de registros recuperados hasta el momento:",
    "NUMBER_OF_SELECTED_ITEM_PLURAL": "{{count}} elementos seleccionados",
    "NUMBER_OF_SELECTED_ITEM": "{{count}} elemento seleccionado",
    "NUMBER_OF_SELECTED_ITEMS": "N.º de elementos seleccionados",
    "OBFUSCATED": "Ofuscado",
    "OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA": "Asia/Gaza",
    "OCCUPIED_PALESTINIAN_TERRITORY": "Territorios Palestinos ocupados",
    "OFF": "Desactivado",
    "OKAY": "De acuerdo",
    "OMAN_ASIA_MUSCAT": "Asia/Muscat",
    "OMAN": "Oman",
    "ON_PREMISE": "En las instalaciones",
    "ON": "El",
    "ONE_OR_MORE_CC_FAILED": "Una o varias actualizaciones de Cloud Connector han fallado",
    "ONLINE_AUCTIONS": "Subastas en línea",
    "ONLINE_CHAT": "Chat en línea",
    "ONLY_ONE_PHISICAL_INTERFACE_PER_PORT": "Solo una interfaz etiquetada por puerto.",
    "OPEN_A_NEW_TAB": "Abrir en una nueva pestaña",
    "OPENVPN_DESC": "OpenVPN es una aplicación de software de código abierto que implementa técnicas de redes privadas virtuales (VPN) para crear conexiones seguras punto a punto o ubicación a ubicación en configuraciones enrutadas o puenteadas y en instalaciones de acceso remoto ",
    "OPENVPN": "OpenVPN",
    "OPERATIONAL_STATUS": "Estado operativo",
    "OPTION_NAME": "Nombre de opción",
    "OPTIONAL_PARENTHESIS": "(opcional)",
    "OPTIONAL": "Opcional",
    "OPTIONS_COLON": "Opciones:",
    "OPTIONS": "Opciones",
    "ORACLE_LINUX": "ORACLE LINUX",
    "ORDER_DEFAULT": "Defecto",
    "ORG_ADMIN": "Administración de la organización",
    "ORG_ID": "ID de la organización",
    "ORGANIZATION": "Organización",
    "OS_TYPE": "Tipo de sistema operativo",
    "OS_VERSION": "Versión del sistema operativo",
    "OSS_UPDATES": "Actualizaciones del sistema operativo y del software",
    "OTHER_ADULT_MATERIAL": "Otro Material para adultos",
    "OTHER_BUSINESS_AND_ECONOMY": "Otros Negocios y Economíaa",
    "OTHER_CLOUDS": "Otras nubes",
    "OTHER_DRUGS": "Otros medicamentos/drogas",
    "OTHER_EDUCATION": "Otro Educación",
    "OTHER_ENTERTAINMENT_AND_RECREATION": "Otro entretenimiento/recreación",
    "OTHER_GAMES": "Juegos online y otros juegos",
    "OTHER_GOVERNMENT_AND_POLITICS": "",
    "OTHER_ILLEGAL_OR_QUESTIONABLE": "Otros Ilegal y Cuestionable",
    "OTHER_INFORMATION_TECHNOLOGY": "",
    "OTHER_INTERNET_COMMUNICATION": "Otras comunicaciones de Internet",
    "OTHER_MISCELLANEOUS": "Otros varios",
    "OTHER_OS": "Otro sistema operativo",
    "OTHER_RELIGION": "Otra religión",
    "OTHER_SECURITY": "Otro seguridad",
    "OTHER_SHOPPING_AND_AUCTIONS": "Otras compras y subastas",
    "OTHER_SOCIAL_AND_FAMILY_ISSUES": "Otros problemas sociales y familia",
    "OTHER_SOCIETY_AND_LIFESTYLE": "Otros sociedad y estilo de vida",
    "OTHER_THREAT": "Otras amenazas",
    "OTHER": "Otros",
    "out of": "de",
    "OUT_OF": "de",
    "OUTBOUND": "Saliente",
    "OUTBYTES": "Bytes de salida",
    "OUTGOING_GATEWAY_IP_ADDRESS": "Dirección IP de pasarela saliente",
    "OVER_ALL_TRAFFIC": "Tráfico global",
    "OVERRIDE": "Anular",
    "P2P_COMMUNICATION": "Sitio peer-to-peer",
    "P2P": "Peer-to-Peer",
    "PACKET_LOSS": "Pérdida de paquetes",
    "PAGE_OF": "Página {1} de {2}",
    "PAGE_RISK_INDEX": "Riesgo de página",
    "PAGE": "Página",
    "PAKISTAN_ASIA_KARACHI": "Asia/Karachi",
    "PAKISTAN": "Pakistan",
    "PALAU_PACIFIC_PALAU": "Pacific/Palau",
    "PALAU": "Palau",
    "PALESTINE": "Palestina",
    "PALESTINIAN_TERRITORY": "Palestinian Territory",
    "PANAMA_AMERICA_PANAMA": "America/Panama",
    "PANAMA": "Panama",
    "PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY": "Pacific/Port Moresby",
    "PAPUA_NEW_GUINEA": "Papua New Guinea",
    "PARAGUAY_AMERICA_ASUNCION": "America/Asuncion",
    "PARAGUAY": "Paraguay",
    "PARTNER_INTEGRATIONS": "Integraciones de socios",
    "PASSPHRASE": "Frase de contraseña",
    "PASSWORD_CHANGE_REMINDER_NEW_PASSWORD_INPUT_PLACEHOLDER": "Debe tener al menos 8 caracteres y contener 1 dígito, 1 letra mayúscula y 1 carácter especial...",
    "PASSWORD_DONT_MATCH": "Las contraseñas no coinciden",
    "PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE": "Este cambio también se aplicará a los administradores que existan en el Portal de administración para otro servicio. ¿Continuar?",
    "PASSWORD_EXPIRATION_DAYS_RANGE": "La fecha de caducidad de la contraseña debe ser de entre 15 y 365 días",
    "PASSWORD_EXPIRATION": "Caducidad de la contraseña",
    "PASSWORD_EXPIRED_ALREADY": "Tu contraseña ya ha caducado",
    "PASSWORD_EXPIRED": "Contraseña caducada",
    "PASSWORD_EXPIRES_AFTER": "La contraseña caduca después de",
    "PASSWORD_EXPIRY": "Contraseña caducada",
    "PASSWORD_MANAGEMENT": "GESTIÓN DE CONTRASEÑAS",
    "PASSWORD_MESSAGE_WARNING_MESSAGE": "Este cambio también se aplicará a los administradores que existan en el Portal de administración para otro servicio. ¿Continuar?",
    "PASSWORD_STRENGTH_REQUIRED": "La contraseña debe contener al menos ocho caracteres y al menos un dígito, una letra mayúscula y un carácter especial.",
    "PASSWORD_STRENGTH": "Debe tener al menos 8 caracteres y contener 1 dígito, 1 letra mayúscula y 1 carácter especial",
    "PASSWORD_UPDATE_SUCCESSFULLY": "Contraseña actualizada correctamente",
    "PASSWORD": "contraseña",
    "PATCH": "Parche",
    "PATENTS": "Patentes",
    "PC_ANYWHERE_DESC": " PCAnywhere es un software de control remoto y de transferencia de archivos.",
    "PC_ANYWHERE": "pcAnywhere",
    "PCANYWHERE_DESC": " PCAnywhere es una solución de control remoto. Puede gestionar sistemas Windows y Linux. El rendimiento de vídeo mejorado y el cifrado AES de 256 bits incorporado contribuyen a que las comunicaciones sean rápidas y seguras. PCAnywhere también cuenta con potentes funciones de transferencia de archivos",
    "PCANYWHERE": "pcanywhere",
    "PEER_DHCP": "DHCP de par (opcional)",
    "PENDING": "Pendiente",
    "PERMISSION_REQUIRED_MESSAGE": "Esta función requiere un permiso que usted no tiene actualmente.",
    "PERMISSION_REQUIRED": "Permiso requerido",
    "PERMISSION": "Permiso",
    "PERMISSIONS": "Permisos",
    "PERSIST_LOCAL_VERSION_PROFILE": "Persistencia de perfil de versión local",
    "PERU_AMERICA_LIMA": "America/Lima",
    "PERU": "Peru",
    "PFCP_DESC": "Un protocolo 3GPP utilizado en la interfaz N4 entre el plano de control y la función de plano de usuario (UPF)",
    "PFCP_PORT": "Puerto PFCP",
    "PHILIPPINES_ASIA_MANILA": "Asia/Manila",
    "PHILIPPINES": "Philippines",
    "PHISHING": "Phishing",
    "PHYSICAL": "Físico",
    "PITCAIRN_ISLANDS": "Pitcairn Islands",
    "PITCAIRN_PACIFIC_PITCAIRN": "Pacific/Pitcairn",
    "PITCAIRN": "Pitcairn",
    "PLACEHOLDER_NETWORK_SERVICE_GROUP_NAME": "Escriba aquí el nombre del grupo de servicios de red",
    "PLACEHOLDER_NETWORK_SERVICE_NAME": "Escriba aquí el nombre del servicio de red",
    "PLAIN_UDP": "UDP sin cifrar",
    "PLATFORM": "Plataforma",
    "PLEASE_ADD_BC_GROUP_INFO": "Complete la siguiente información para su grupo de Branch Connectors.",
    "PLEASE_ADD_CLOUD_CONNECTOR_NAME": "Añada el filtro de Cloud Connector.",
    "PLEASE_ADD_DATACENTER_FILTER": "Añada el filtro del centro de datos.",
    "PLEASE_ADD_EC_DEVICE_APP_VERSION": "Añada el filtro de versión de la aplicación del dispositivo.",
    "PLEASE_ADD_EC_DEVICE_HOSTNAME": "Añada el filtro de nombre de host del dispositivo.",
    "PLEASE_ADD_EC_DEVICE_ID": "Añada el filtro de nombre del dispositivo.",
    "PLEASE_CONFIGURE_THE_IP_ADDRESS": "Configure la dirección IP",
    "PLEASE_CONFIGURE_THE_MAC_ADDRESS": "Configure la dirección MAC",
    "PLEASE_CONFIGURE_THE_ROUTE_ADDRESS": "Configure la dirección de la ruta.",
    "PLEASE_ENTER_BELOW_VALUES": "Introduzca los siguientes valores",
    "PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW": "Introduzca la siguiente información.",
    "PLEASE_ENTER_VALID_EMAIL_ADDRESS": "Introduzca una dirección de correo electrónico válida.",
    "PLEASE_FILL_BRANCH_INFO": "Rellene la siguiente información para su Branch Connector individual.",
    "PLEASE_FILL_DEVICE_INFO": "Seleccione un dispositivo para el aprovisionamiento de la configuración de Branch Connector.",
    "PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM": "Póngase en contacto con su equipo de cuenta para habilitar el cifrado de túnel",
    "PLEASE_REMOVE_DELETED_LOCATION": "Quite la ubicación o ubicaciones eliminadas.",
    "PLEASE_SELECT_A_STORAGE_ACCOUNT": "Seleccione una cuenta de almacenamiento.",
    "PLEASE_VERIFY_THE_DHCP_CUSTOM_CONFIGURTION_FOR_DUPLICATES": "Verifique la configuración de opciones personalizadas de DHCP para determinar si hay duplicados.",
    "POLAND_EUROPE_WARSAW": "Europa/Varsovia",
    "POLAND": "Polonia",
    "POLICY_CONFIGURATION": "Configuración de política",
    "POLICY_INFORMATION": "Información de reenvío",
    "POLICY_MANAGEMENT": "Gestión de políticas",
    "POLICY_SYNC": "Sincronización de políticas",
    "Policy": "Política ",
    "POLICY": "Política ",
    "POLITICS": "Política",
    "POOR": "Baja",
    "POP3_DESC": "Post Office Protocol es un protocolo utilizado para recuperar correo",
    "POP3": "POP3",
    "PORNOGRAPHY": "Pornografía",
    "PORT_DETAILS": "Detalles del puerto",
    "PORT_NAME": "[Nombre del puerto]",
    "PORT_NO": "N.º de puerto  ",
    "PORT_STATUS": "Estado del puerto",
    "PORT": "Puerto",
    "PORTALS": "Portales",
    "PORTS": "Puertos proxy",
    "PORTUGAL_ATLANTIC_AZORES": "Atlantic/Azores",
    "PORTUGAL_ATLANTIC_MADEIRA": "Atlantic/Madeira",
    "PORTUGAL_EUROPE_LISBON": "Europa/Lisboa",
    "PORTUGAL": "Portugal",
    "PPP_DESC": "  PPP (Point-to-Point Protocol) es un protocolo de nivel de enlace usado para transferir datos en un enlace punto a punto. Permite direccionamiento IP dinámico, soporte a contraseñas, comprobación de errores, y transmisión de múltiples protocolos en el mismo enlace ",
    "PPP": "PPP",
    "PPPOE_DESC": " PPP over Ethernet (PPPoE) permite conectar una red de hosts conectados en un dispositivo bridge simple a un concentrador de acceso remoto",
    "PPPOE": "PPPoE",
    "PPS_DESC": " Este plug-in de protocolo clasifica el tráfico http al host pps.tv",
    "PPS": "PPS (pps.tv)",
    "PPSTREAM_DESC": " El protocolo PPStream permite streaming de audio y vídeo. Está basado en tecnología bittorrent (peer-to-peer). Se usa principalmente en China",
    "PPSTREAM": "PPStream",
    "PPTP_DATA_DESC": " El protocolo Point-to-Point Tunneling Protocol permite al protocolo Point to Point Protocol (PPP) ser tunelizado a través de una red IP",
    "PPTP_DATA": "PPTP data channel. PPTP",
    "PPTP_DESC": " El protocolo Point-to-Point Tunneling Protocol permite al protocolo Point to Point Protocol (PPP) ser tunelizado a través de una red IP",
    "PPTP_SERVICES": "Servicios PPTP",
    "PPTP": "PPTP",
    "PPTV_DESC": " Este plug-in de protocolo clasifica el tráfico http al host pptv.com",
    "PPTV": "pptv",
    "PRE_SIGNED_URL": "Dirección URL prefirmada",
    "PREDEFINED_RULE_CONFIRMATION": "Confirmación de regla predefinida",
    "PREDEFINED": "Predefinido",
    "PREFERRED_COLLON": "Preferido:",
    "PREFERRED_TEXT": "Seleccione Sí para permitir que el dispositivo preferido se anticipe y tome el control cuando esté activo.",
    "PREFERRED": "Preferido",
    "PREFIX": "Prefijo",
    "PREV_DAY": "Día anterior",
    "PREV_MONTH": "Mes anterior",
    "PREV_WEEK": "Semana anterior",
    "PREVIOUS": "Anterior",
    "PRIMARY_DNS_IS_MADATORY_BEFORE_SECONDARY_DNS": "El DNS primario es obligatorio antes que un DNS secundario.",
    "PRIMARY_DNS_SERVER_IP_ADDRESS": "Dirección IP del servidor DNS principal",
    "PRIMARY_DNS_SERVER": "Servidor DNS primario",
    "PRIMARY_DNS": "DNS primario",
    "PRIMARY_PROXY": "Proxy principal",
    "PRIMARY_SERVER_RESPONSE_PASS": "Intento de servidor primario",
    "PRIMARY": "Primario",
    "PRINT_VIEW": "Vista de impresión",
    "PRINT": "Imprimir",
    "PRIVATE_APLICATIONS": "Aplicaciones privadas",
    "PRIVATE_IP_ADDRESS": "Dirección IP privada",
    "PRIVATE_IP_ADDRESSES": "Direcciones IP privadas",
    "PROCEED": "Continuar",
    "PROCESSED_BYTES": "Bytes procesados",
    "PROFANITY": "Blasfemia",
    "PROFESSIONAL_SERVICES": "Servicios profesionales",
    "PROTOCOL_TYPE": "Tipo de protocolo",
    "PROTOCOL": "Protocolo",
    "PROVISION_KEY_NAME": "Nombre de clave de aprovisionamiento",
    "PROVISION_KEY": "Clave de aprovisionamiento",
    "PROVISIONED": "Aprovisionado",
    "Provisioning URL": "URL de aprovisionamiento",
    "PROVISIONING_AND_CONFIGUATION": "Aprovisionamiento y configuración",
    "PROVISIONING_CONTROL": "Control de aprovisionamiento",
    "PROVISIONING_KEY": "Clave de aprovisionamiento",
    "PROVISIONING_MANAGEMENT": "Gestión del aprovisionamiento",
    "PROVISIONING_TEMPLATE_IS_BROKEN": "Esta plantilla de aprovisionamiento no es válida. Elimínela y cree una nueva.",
    "PROVISIONING_TEMPLATE": "Plantilla de aprovisionamiento",
    "PROVISIONING_TEMPLATES": "Plantillas de aprovisionamiento",
    "PROVISIONING_URL": "URL de aprovisionamiento",
    "PROXY_TEST": "Prueba de proxy",
    "PUBLIC_CLOUD_COFIGURATION": "Configuración de nube pública",
    "PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Gestión de configuración de nube pública",
    "PUBLIC_IP_FOR_DIRECT_FORWARDING": "IP pública para reenvío directo",
    "PUBLIC_IP": "IP pública",
    "PUBLIC_IPS": "IP públicas",
    "PUERTO_RICO_AMERICA_PUERTO_RICO": "America/Puerto Rico",
    "PUERTO_RICO": "Puerto Rico",
    "PZEN": "Private Service Edge",
    "QATAR_ASIA_QATAR": "Asia/Qatar",
    "QATAR": "Qatar",
    "QUESTIONABLE": "Cuestionable",
    "QUEUED_ACTIVATIONS": "ACTIVACIONES EN COLA",
    "QUIC_DESC": "QUIC (Quick UDP Internet Connections) es un nuevo protocolo de transporte para Internet desarrollado por Google",
    "QUIC": "QUIC",
    "QUICK_LINKS": "Enlaces rápidos",
    "QUICKOFFICE": "Quickoffice",
    "QUICKSEC": "QUICKSEC",
    "QUICKTIME_VIDEO": "QuickTime Video (mov, qt)",
    "QUICKTIME": "QuickTime",
    "RADIO_STATIONS": "Radio",
    "RADIUS_DESC": " RADIUS (Remote Authentication Dial-In User Service) es un procolo cliente-servidor que permite a servidores de acceso remoto comunicar con un servidor central para autenticar a usuarios llamantes y autorizar su acceso al servicio o sistema requerido",
    "RADIUS": "RADIUS",
    "RADIUSIM_DESC": " Este plug-in de protocolo clasifica el tráfico http al host radiusim.com",
    "RADIUSIM": "RadiusIM",
    "RANGE_ERROR": "Rango no válido. El número debe estar entre {{min}} y {{max}}",
    "RANGE_FROM_BIGGER_THAN_TO": "La IP inicial del rango debe ser igual o menor que la IP final.",
    "RBA_LIMITED": "Acceso restringido",
    "REAL_ESTATE": "Construcción e Inmobiliaria",
    "REAL_MEDIA_DESC": "Real Media es una tecnología de streaming de vídeo y audio ",
    "REAL_MEDIA": "RealMedia",
    "REASON": "Motivo",
    "RECEIVE_COUNT": "Recuento de recepciones",
    "RECEIVED_BYTES": "Bytes recibidos",
    "RECEIVED_MESSAGES": "Mensajes recibidos",
    "RECOMMENDED_HYPERVISOR_SPECS": "ESPECIFICACIONES RECOMENDADAS DE HIPERVISOR",
    "RECOMMENDED_VM_SPECS": "ESPECIFICACIONES RECOMENDADAS DE VM",
    "REDHAT_LINUX": "Red Hat Linux",
    "REDIR_ZPA": "Redirigir a ZPA",
    "REDIRECT_REQUEST": "Solicitud de redireccionamiento",
    "REFERENCE_SITES": "Sitios de referencias",
    "REFRESH": "Actualizar",
    "REGENARATE": "Regenerar",
    "REGENERATE_API_KEY_CONFIRMATION_MESSAGE": "La regeneración de la clave de API invalida de inmediato la clave existente. La nueva clave conserva el alcance, los permisos y el nombre de la clave existente. Esto no puede deshacerse.",
    "REGENERATE_API_KEY_CONFIRMATION_TITLE": "Regenerar clave de API",
    "REGENERATE_API_KEY_TOOLTIP": "Regenerar la clave de API",
    "REGENERATE_API_KEY": "Regenerar clave de API",
    "REGION_TEXT": "Seleccione las regiones donde desea que Zscaler detecte identificadores en su cuenta de AWS. El menú desplegable muestra la lista de regiones admitidas por el servicio de detección de identificadores de Zscaler. Encontrará más información sobre las regiones admitidas {1}aquí{2}.",
    "REGION": "Región",
    "REGIONS_AND_SUBSCRIPTIONS": "Regiones y suscripciones",
    "REGIONS_SUBSCRIPTION_TEXT": "Seleccione las regiones y las suscripciones. Zscaler leerá las etiquetas definidas por el usuario para cargas de trabajo ubicadas en estas regiones y suscripciones.",
    "REGIONS_SUBSCRIPTION": "Regiones y suscripciones",
    "REGIONS_WORKLOAD_INVENTORY": "Inventario de cargas de trabajo de regiones",
    "REGIONS": "Regiones",
    "REGISTERED": "Registrado",
    "RELEASES_NOTES": "Notas de la versión",
    "REMOTE_ACCESS": "Herramientas de acceso remoto",
    "REMOTE_ASSISTANCE_MANAGEMENT": "Gestión de asistencia remota",
    "REMOTE_ASSISTANCE": "Asistencia remota",
    "REMOVE_ALL": "Quitar todo",
    "RENEW": "Renovar",
    "REPORT": "Informe",
    "REQ_ACTION": "Solicitar acción",
    "REQ_DURATION": "Duración de la solicitud",
    "REQ_RULE_NAME": "Nombre de regla de solicitud",
    "REQUESTED_DOMAIN": "Dominio solicitado",
    "REQUIRED": "Este campo no puede estar vacío.",
    "RES_ACTION": "Acción de respuesta",
    "RES_RULE_NAME": "Nombre de regla de respuesta",
    "RESEARCH_BLOG": "ThreatLabz | Investigación en seguridad",
    "RESET_COUNT": "Restablecer recuento",
    "RESET": "Restablecer",
    "RESOLVE_BY_ZPA": "Resolver por ZPA",
    "RESOLVED_BY_ZPA": "Resuelto",
    "RESOLVED_IP_OR_NAME": "IP o nombre resuelto",
    "RESOLVED_IP": "IP resuelta",
    "RESOLVER_IP_OR_NAME": "Resolución de problemas de IP o nombre",
    "RESOLVER": "Resolución de problemas",
    "RESOURCE_GROUP": "Grupo de recursos",
    "RESOURCE_GROUPS": "Grupos de recursos",
    "RESOURCE_NAME": "Recurso",
    "RESOURCE_NOT_FOUND": "Recurso no encontrado",
    "RESOURCE_TYPE": "Tipo de recurso",
    "RESOURCE": "Recurso",
    "RESPONSE_ACTION": "Acción de respuesta",
    "REST": "Rest",
    "RETRIEVING_FOR": "Recuperando para",
    "RETURN_ERROR": "Error de devolución",
    "REUNION_INDIAN_REUNION": "Indian/Reunion",
    "REUNION": "Reunion",
    "REVIEW_ENSURE_INFORMATION": "Asegúrese de que toda la información que figura a continuación sea correcta antes de crear esta plantilla de aprovisionamiento de conector de sucursal.",
    "REVIEW_TENANT": "Asegúrese de que toda la información siguiente sea correcta antes de añadir esta cuenta.",
    "REVIEW_TEXT": "Asegúrese de que toda la información siguiente sea correcta antes de añadir esta cuenta.",
    "REVIEW_YOUR_CHANGES": "Revise los cambios",
    "REVIEW": "Revisar",
    "RMA": "RMA solicitada",
    "ROLE_MANAGEMENT": "Gestión de roles",
    "ROLE_NAME": "Nombre de rol",
    "ROLE": "Rol",
    "ROMANIA_EUROPE_BUCHAREST": "Europa/Bucarest",
    "ROMANIA": "Rumanía",
    "ROUTE_HAS_DUPLICATE_VALUES": "La ruta tiene valores duplicados.",
    "ROUTE": "Ruta",
    "ROUTING": "Enrutamiento",
    "ROWS_PER_PAGE": "Filas por página",
    "RSH_DESC": " El protocolo RSH permite a un usuario establecer una conexión segura con un host remoto y obener un shell que permite enviar a una máquina remota comandos para ejecutar.  ",
    "RSH": "rsh",
    "RSLTS_READFAILED": "Error al leer el volumen de resultados.",
    "RSS_DESC": " RSS es una familia de formatos de alimentación de páginas web usados para publicar trabajos frecuentemente actualizados de una forma estandarizada",
    "RSS": "rss",
    "RSTAT_DESC": " El protocolo RStat se usa en la familia Sun NFS para intercambiar estadísticas de actividad de red",
    "RSTAT": "RStat",
    "RSVP_DESC": " El protocolo RSVP (Resource reSerVation setup Protocol) está diseñado para internet de servicios integrados. RSVP permite al receptor la configuración de reserva de recursos para flujos de datos multicast o unicast, con buenas propiedades de escalado y robustez. El protocolo RSVP protocol es usado por un host para pedir calidades de servicio específicas de la red para flujos de aplicaciones de datos. RSVP también lo utilizan los routers para proveer peticiones de calidad de servicio (QoS) a todos los nodos en el camino o caminos de los flujos y a establecer y mantener el estado para el servicio ",
    "RSVP": "RSVP",
    "RSYNC_DESC": "rsync es un programa de sincronización y transferencia de archivos para sistemas similares a Unix que minimiza la transferencia de datos por la red usando una forma de codificación delta llamada algoritmo rsync",
    "RSYNC": "Rsync",
    "RTCP_DESC": " El protocolo de transporte en tiempo real RTP permite monitorizar la entrega de datos de forma escalable a grandes redes multicast y permite una funcionalidad mínima de control e identificación",
    "RTCP": "RTCP",
    "RTL_DESC": " Este plug-in de protocolo clasifica el tráfico http al host rtl.de",
    "RTL": "RTL",
    "RTMP_DESC": "El protocolo de mensajería en tiempo real (RTMP) es un protocolo propietario desarrollado por Adobe Systems para la transmisión en streaming de sonido, vídeo y datos por Internet, entre reproductores y servidores Flash",
    "RTMP": "RTMP",
    "RTP_DESC": "  RTP es el protocolo de transporte en tiempo real utilizado para transmitir datos en tiempo real, como audio, vídeo datos de simulación, sobre servicios de red multicast o unicast ",
    "RTP": "RTP",
    "RTSP_DESC": " El protocolo Real Time Streaming Protocol (RTSP) es un procolo a nivel de aplicación para controlar la entrega de datos en tiempo real. RTSP permite un entorno extensible para permitir entrega controlada bajo demanda de datos en tiempo real, como audio y vídeo",
    "RTSP_SERVICES": "Servicios RTSP",
    "RTSP": "RTSP",
    "RULE_CRITERIA": "Criterios",
    "RULE_NAME": "Nombre de la regla",
    "RULE_ORDER": "Orden de regla",
    "RULE_STATUS": "Estado de regla",
    "RULES": "Reglas",
    "RUN_TEST": "Ejecutar prueba",
    "RUSSIA": "Rusia",
    "RUSSIAN_FEDERATION_ASIA_ANADYR": "Asia/Anadyr",
    "RUSSIAN_FEDERATION_ASIA_IRKUTSK": "Asia/Irkutsk",
    "RUSSIAN_FEDERATION_ASIA_KAMCHATKA": "Asia/Kamchatka",
    "RUSSIAN_FEDERATION_ASIA_KRASNOYARSK": "Asia/Krasnoyarsk",
    "RUSSIAN_FEDERATION_ASIA_MAGADAN": "Asia/Magadan",
    "RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK": "Asia/Novosibirsk",
    "RUSSIAN_FEDERATION_ASIA_OMSK": "Asia/Omsk",
    "RUSSIAN_FEDERATION_ASIA_SAKHALIN": "Asia/Sakhalin",
    "RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK": "Asia/Vladivostok",
    "RUSSIAN_FEDERATION_ASIA_YAKUTSK": "Asia/Yakutsk",
    "RUSSIAN_FEDERATION_ASIA_YEKATERINBURG": "Asia/Yekaterinburg",
    "RUSSIAN_FEDERATION_EUROPE_KALININGRAD": "Europa/Kaliningrado",
    "RUSSIAN_FEDERATION_EUROPE_MOSCOW": "Europa/Moscu",
    "RUSSIAN_FEDERATION_EUROPE_SAMARA": "Europa/Samara",
    "RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD": "Europa/Volgogrado",
    "RUSSIAN_FEDERATION": "Federación Rusa",
    "RWANDA_AFRICA_KIGALI": "Africa/Kigali",
    "RWANDA": "Rwanda",
    "RX_BYTES": "Bytes recibidos",
    "RX_PACKETS": "Paquetes recibidos",
    "SA_EAST_1": "sa-east-1 (Sao Paulo)",
    "SA_EAST_1A": "sa-east-1a",
    "SA_EAST_1B": "sa-east-1b",
    "SA_EAST_1C": "sa-east-1c",
    "SAFE_SEARCH_ENGINE": "Motor de Búsqueda segura",
    "SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY": "America/St. Barthelemy",
    "SAINT_BARTHELEMY": "Saint Barthelemy",
    "SAINT_HELENA": "Saint Helena",
    "SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS": "America/St. Kitts",
    "SAINT_KITTS_AND_NEVIS": "Saint Kitts and Nevis",
    "SAINT_LUCIA_AMERICA_ST_LUCIA": "America/St. Lucia",
    "SAINT_LUCIA": "Saint Lucia",
    "SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT": "America/Marigot",
    "SAINT_MARTIN_FRENCH_PART": "Saint Martin (French part)",
    "SAINT_MARTIN": "Saint Martin",
    "SAINT_PIERRE_AND_MIQUELON": "Saint Pierre and Miquelon",
    "SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT": "America/St. Vincent",
    "SAINT_VINCENT_AND_THE_GRENADINES": "Saint Vincent and the Grenadines",
    "SAML_CERTIFICATE_FILENAME": "Certificado SAML de IdP",
    "SAMOA_PACIFIC_APIA": "Pacific/Apia",
    "SAMOA": "Samoa",
    "SAN_MARINO_EUROPE_SAN_MARINO": "Europa/San Marino",
    "SAN_MARINO": "San Marino",
    "SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME": "Africa/Sao Tome",
    "SAO_TOME_AND_PRINCIPE": "Sao Tome and Principe",
    "SATURDAY": "Sábado",
    "SAUDI_ARABIA_ASIA_RIYADH": "Asia/Riyadh",
    "SAUDI_ARABIA": "Saudi Arabia",
    "SAVE_AND_NEXT": "Guardar y siguiente",
    "SAVE_ERROR_MESSAGE": "Error de validación. Vuelva a verificar todos los campos antes de guardar.",
    "SAVE_SUCCESS_MESSAGE": "Se han guardado todos los cambios",
    "SAVE": "Guardar",
    "SCCP_DESC": " Skinny Client Control Protocol (SCCP) es un protocolo propietario de Cisco utilizado entre el Cisco Call Manager y teléfonos VOIP de Cisco VOIP. Es soportado por algún otro fabricante",
    "SCCP": "SCCP",
    "SCHEDULE_UPGRADE": "Programar actualización",
    "SCHEDULED_VERSION": "Versión programada",
    "SCHEDULED": "Programado",
    "SCIENCE_AND_TECHNOLOGY": "Ciencia/Tecnologia",
    "SCOPE": "Alcance",
    "SCTP_ANY_DESC": "El protocolo de transmisión de control de flujo (SCTP) es un protocolo de la capa de transporte del conjunto de protocolos de Internet (IP). Originalmente diseñado para el transporte de mensajes del Sistema de señalización 7 (SS7) en telecomunicaciones, el protocolo proporciona la característica orientada a mensajes del Protocolo de datagramas de usuario (UDP), al tiempo que garantiza un transporte fiable y secuencial de mensajes con control de congestión como el Protocolo de control de transmisión (TCP).",
    "SCTP_ANY": "SCTP",
    "SCTP_DEST_PORTS": "Puertos de destino SCTP",
    "SCTP_PORT": "Puerto SCTP",
    "SCTP_PORTS": "Puertos SCTP",
    "SCTP_SRC_PORTS": "Puertos de origen SCTP",
    "SCTP_UDP_Translation": "Traducción SCTP/UDP",
    "SEARCH_BY": "Buscar por:",
    "SEARCH_ELLIPSIS": "Buscar...",
    "SEARCH_LOCATION": "Buscar ubicación",
    "SEARCH_RESULT": "Resultado de la búsqueda",
    "SEARCH_TO_SEE_MORE": "Busque para ver más elementos",
    "SEARCH": "Búsqueda",
    "SECONDARY_DNS_OPTIONAL": "DNS secundario (opcional)",
    "SECONDARY_DNS_SERVER_IP_ADDRESS": "Dirección IP del servidor DNS secundario",
    "SECONDARY_DNS_SERVER": "Servidor DNS secundario",
    "SECONDARY_DNS": "DNS secundario",
    "SECONDARY_PROXY": "Proxy secundario",
    "SECONDARY_SERVER_RESPONSE_PASS": "Intento de servidor secundario",
    "SECONDARY": "Secundario",
    "SECURITY_GROUP_ID": "ID del grupo de seguridad",
    "SECURITY_GROUP_NAME": "Nombre del grupo de seguridad",
    "SEGMENT_GROUPS": "Grupos de segmentos",
    "SELECT_A_LOCATION": "Seleccionar una ubicación",
    "SELECT_A_TEST": "Seleccione una prueba",
    "SELECT_ALL": "Seleccionar todos",
    "SELECT_AN_EXISTING_LOCATION": "Seleccionar una ubicación existente",
    "SELECT_BRANCH_PROVISIONING_LOCATION": "Seleccione una ubicación para su plantilla de aprovisionamiento de Branch Connector.",
    "SELECT_CC_GROUP": "Seleccionar grupo de Cloud Connectors",
    "SELECT_CC_LOCATION": "Seleccionar ubicación de Cloud Connector",
    "SELECT_CC_VERSION": "Seleccionar versión de Cloud Connector",
    "SELECT_CHART_TYPE": "Seleccionar tipo de gráfico",
    "SELECT_DESTINATION_IP": "Seleccionar IP de destino",
    "SELECT_EVENT_TIME": "Seleccionar hora de evento",
    "SELECT_FILTERS": "Seleccionar filtros",
    "SELECT_HYPERVISOR_VERSION": "Seleccionar versión del hipervisor",
    "SELECT_RESOURCE_GROUP_NAME": "Seleccionar grupo de recursos",
    "SELECT_STORAGE_ACCOUNT": "Seleccionar cuenta de almacenamiento",
    "SELECT_SUBSCRIPTION_GROUP_NAME": "Seleccionar nombre de grupo de suscripciones",
    "SELECT_SUBSCRIPTION": "Seleccionar suscripción",
    "SELECT_TWO_VERSIONS_TO_COMPARE": "Seleccione dos versiones para comparar.",
    "SELECT_UPGRADE_WINDOW": "Seleccionar ventana de actualización",
    "SELECT_ZSCALER_IP": "Seleccionar IP de Zscaler",
    "SELECT": "Seleccionar",
    "SELECTED_ITEMS": "Elementos seleccionados ({{count}})",
    "SELECTED": "Seleccionado",
    "SENEGAL_AFRICA_DAKAR": "Africa/Dakar",
    "SENEGAL": "Senegal",
    "SENT_BYTES": "Bytes enviados",
    "SENT_COUNT": "Recuento de envíos",
    "SENT_MESSAGES": "Mensajes enviados",
    "SERBIA_EUROPE_BELGRADE": "Europa/Belgrado",
    "SERBIA": "Serbia",
    "SERIAL_NUMBER": "Número de serie",
    "SERVER_DESTINATION_IP": "IP de destino del servidor",
    "SERVER_DESTINATION_PORT": "Puerto de destino del servidor",
    "SERVER_IP_CATEGORY": "Categoría IP del servidor",
    "SERVER_IP": "IP del servidor",
    "SERVER_NAME": "Nombre de servidor",
    "SERVER_NETWORK_PROTOCOL": "Protocolo NW del servidor",
    "SERVER_PORT": "Puerto del servidor",
    "SERVER_SOURCE_IP": "IP de origen del servidor",
    "SERVER_SOURCE_PORT": "Puerto de origen del servidor",
    "SERVER": "Tráfico de servidor",
    "SERVERS": "Servidores",
    "SERVFAIL": "Fallo del servidor",
    "Service IP": "IP de servicio",
    "SERVICE_GATEWAY_IP_ADDRESS": "Dirección IP de pasarela de servicio",
    "SERVICE_GROUPS": "Grupos de servicios",
    "SERVICE_INFORMATION": "Información de servicio",
    "SERVICE_INTERFACE": "Interfaz de servicio",
    "SERVICE_IP_ADDRESS_ONE": "Dirección IP de servicio 1",
    "SERVICE_IP_ADDRESS_POOL": "Grupo de direcciones IP del servidor",
    "SERVICE_IP_ADDRESS_THREE": "Dirección IP de servicio 3",
    "SERVICE_IP_ADDRESS_TWO": "Dirección IP de servicio 2",
    "SERVICE_IP_ADDRESS": "Dirección IP de servicio",
    "SERVICE_IP": "IP de servicio",
    "SERVICE_STATUS": "Estado del servicio",
    "SERVICE_VIRTUAL_IP_ADDRESS": "Dirección IP virtual de servicio",
    "SERVICE": "Servicio",
    "SERVICES": "Servicios",
    "SESSION_COUNT_TREND": "Tendencia del recuento de sesiones",
    "SESSION_COUNT": "Recuento de sesiones",
    "SESSION_DURATION": "Duración de sesión",
    "SESSION_ID": "ID de sesión",
    "SESSION_INSIGHTS": "Perspectivas de sesión",
    "SESSION_LOGS": "Registros de sesiones",
    "SESSION_TIMED_OUT": "Su sesión ha caducado. Inicie sesión de nuevo para continuar.",
    "SESSION": "Sesión",
    "SESSIONS_ACROSS_SERVICES": "Sesiones en todos los servicios",
    "SESSIONS": "Sesiones",
    "SET_PASSWORD": "ESTABLECER CONTRASEÑA",
    "SEXUALITY": "Sexualidad",
    "SEYCHELLES_INDIAN_MAHE": "Indian/Mahe",
    "SEYCHELLES": "Seychelles",
    "SHAREWARE_DOWNLOAD": "Descargar Shareware",
    "SHOW_DETAILS": "Mostrar detalles",
    "SHOW_LESS_ELLIPSIS": "Mostrar menos...",
    "SHOW_MORE_ELLIPSIS": "Mostrar más...",
    "SHOW": "Mostrar",
    "SHUTDOWN": "Cierre",
    "SIERRA_LEONE_AFRICA_FREETOWN": "Africa/Freetown",
    "SIERRA_LEONE": "Sierra Leona",
    "SIGN_IN": "Iniciar sesión",
    "SIGN_OUT": "Cerrar sesión",
    "SIGNING_CERTIFICATE": "Certificado de firma",
    "SINGAPORE": "Singapur",
    "SINGLE_APPLIANCE_ADDED_INFO": "Puede ver el nuevo dispositivo en la página Dispositivos. Para obtener más información, consulte el {1}Portal de ayuda{2} de conectores.",
    "SINGLE_APPLIANCE_ADDED": "Se ha añadido 1 nuevo dispositivo a su inquilino.",
    "SIP_DESC": " Session Initiation Protocol (SIP) es el estándar de la Internet Engineering Task Force's (IETF's) para conferencias multimedia sobre IP. Como otros protocolos VoIP, SIP se diseña para gestionar las funciones de señalización y gestión de sesión dentro de una red de telefonía de paquetes",
    "SIP": "SIP",
    "SIZE_MUST_BE_EXACT_LENGTH": "Este campo debería tener un tamaño de ",
    "SLOVAKIA_EUROPE_BRATISLAVA": "Europa/Bratislava",
    "SLOVAKIA": "Eslovaquia",
    "SLOVENIA_EUROPE_LJUBLJANA": "Europa/Ljubljana",
    "SLOVENIA": "Eslovenia",
    "SMALL": "Pequeña",
    "SMB_DESC": " El protocolo Server Message Block (SMB/SMB2) permite a las aplicaciones de cliente leer y escribir en archivos y solicitar servicios de los programas de los servidores en una red de computadores",
    "SMB": "SMB",
    "SMBA": "SMBA",
    "SMBAC": "Controlador de Sandbox",
    "SMBAUI": "Interfaz de usuario de Sandbox",
    "SMEDGE_BOOTING": "SMEDGE: Arrancando.",
    "SMEDGE_END": "Fin de los códigos de error de smedge. No utilizar.",
    "SMEDGE_INIT": "SMEDGE: Iniciando.",
    "SMEDGE_NOT_RUNNING": "SMEDGE: El proceso no se está ejecutando.",
    "SMEDGE_PKG_DOWNLOAD": "SMEDGE: Descarga de paquete en curso.",
    "SMEDGE_PKG_INSTALL": "SMEDGE: Instalación de paquete en curso.",
    "SMEDGE_START": "Inicio de los códigos de error de smedge. No utilizar.",
    "SMEDGE_UNKNOWN_ERROR": "SMEDGE: Código de error de pasarela desconocido.",
    "SMEDGE_UPDATING": "SMEDGE: Actualizando.",
    "SMEDGE_UPGRADING": "SMEDGE: Actualizando a versión superior.",
    "SMEDGE_ZIA_BRINGUP": "SMEDGE: Trayendo túneles de ZIA.",
    "SMEDGE_ZIA_ZPA_BRINGUP": "SMEDGE: Trayendo túneles de ZIA y estableciendo conexión ZPA.",
    "SMRES_ERROR": "La aplicación de servidor SMRES ha devuelto una respuesta de error HTTP.",
    "SMTP_AV_ENCRYPTED_ALLOW": "Permitido adjunto cifrado",
    "SMTP_AV_ENCRYPTED_ATTACH_DROP": "Adjunto cifrado con contraseñass, adjunto descartado",
    "SMTP_AV_ENCRYPTED_DROP": "Adjunto cifrado con contraseña, mensaje rechazado",
    "SMTP_AV_UNSCANNABLE_ALLOW": "Permitido adjunto no escaneable",
    "SMTP_AV_UNZIPPABLE_ATTACH_DROP": "Adjunto no escaneable, adjunto descartado",
    "SMTP_AV_UNZIPPABLE_DROP": "Adjunto no escaneable, mensaje rechazado",
    "SMTP_AV_UWL": "Virus UWL,",
    "SMTP_AV_VIRUS_ATTACH_DROP": "Virus encontrado, adjunto descartado",
    "SMTP_AV_VIRUS_DROP": "Virus encontrado, mensaje rechazado",
    "SMTP_CMD_TIMEOUT_LIMIT": "SMTP/SMTQTN: timeout de comando (en segundos)",
    "SMTP_CONCURRENT_CLIENT_CONN_LIMIT": "SMTP/SMQTN: límite conexiones cliente concurrentes",
    "SMTP_CONCURRENT_SERVER_CONN_LIMIT": "SMTP/SMQTN: límite conexiones servidor concurrentes",
    "SMTP_DATA_TIMEOUT_LIMIT": "SMQTN: timeout en comando DATA (en segundos)",
    "SMTP_DESC": "Simple Mail Transfer Protocol es un protocolo para enviar mensajes de correo electrónico entre servidores",
    "SMTP_DLP_ALLOW": "Acierto DLP, mensaje permitido",
    "SMTP_DLP_DROP": "Acierto DLP, mensaje rechazado",
    "SMTP_DLP_QTN": "Acierto DLP, mensaje en cuarentena",
    "SMTP_DLP_SIGN_REQUIRED": "Acierto DLP, mensaje no firmado, mensaje rechazado",
    "SMTP_DLP_TLS_REQUIRED": "Acierto DLP, la conexión no es TLS, mensaje rechazado",
    "SMTP_DLP": "SMTP - Cumplimiento",
    "SMTP_EODT_TIMEOUT_LIMIT": "SMQTN: timeout en comando EODT (en segundos)",
    "SMTP_ERRINJECTION_DELAY_LIMIT": "GULPER: retraso inyección error (en segundos)",
    "SMTP_FLOWCONTROL": "Control de flujo en entrega de mensaje (aplicable solo en SMQTN)",
    "SMTP_INCOMPL_TRANS": "Transacción SMTP terminada por interlocutor",
    "SMTP_INSPOL": "Política de seguro SMTP",
    "SMTP_MAILPERCONN_LIMIT": "SMTP/SMQTN: límite mensajes por conexión",
    "SMTP_MAILSIZE_LIMIT": "SMTP/SMQTN: límite tamaño mensaje",
    "SMTP_MF_ATTACHBLK_ATTACH_DROP": "Acierto en bloque de adjunto, adjunto descartado",
    "SMTP_MF_ATTACHBLK_MSG_DROP": "Acierto en bloque de adjunto, mensaje rechazado",
    "SMTP_MF_RCPT_DROP": "Receptor rechazado",
    "SMTP_MF_SIGN_REQUIRED": "Receptor rechazado, mensaje no firmado",
    "SMTP_MF_TLS_REQUIRED": "Receptor rechazado, conexión no es TLS",
    "SMTP_MIN_MSBC_DENSITY": "SMTP/SMQTN: compactar si la densidad de msbs que llegan en data_in es inferior a este valor porcentual mínimo",
    "SMTP_NOCA_BYPASS_CONFIG": "Política omitida",
    "SMTP_NOCA_BYPASS": "SMTP/SMQTN: usar configuración SMTP por defecto si CA no está disponible o si la respuesta de configuración contiene errores",
    "SMTP_OUTBD_DROP_SUSPECTED_SPAM": "Eliminar los mensajes salientes sospechosos de ser spam",
    "SMTP_PLATFORM_1": "SMTP - Plataforma",
    "SMTP_PLATFORM_2": "SMTP - Plataforma II (Saliente)",
    "SMTP_PRETRY": "Reintentar desde proxy (solo aplicable en nodo SMTP)",
    "SMTP_PROXY": "Cluster proxy SMTP",
    "SMTP_RCPT_COPY": "El receptor es una copia",
    "SMTP_RCPT_REDIRECT": "El receptor es una redirección",
    "SMTP_RCPT_UNDELIVERABLE": "Receptor rechazado, no es posible entregarlo",
    "SMTP_RCPTS_LIMIT": "SMTP/SMQTN: límite destinatarios por transacción SMTP",
    "SMTP_RESERVED2": "SMTP: reservado2",
    "SMTP_RESERVED3": "SMTP: reservado3",
    "SMTP_RESERVED4": "SMTP: reservado4",
    "SMTP_RESERVED5": "SMTP: reservado5",
    "SMTP_REUSE_TIMEOUT_LIMIT": "SMTP/SMQTN: timeout pool de reutilización de conexión",
    "SMTP_SECURE": "SMTP - Seguro",
    "SMTP_SENDER_MASQ": "Remitente enmascarado",
    "SMTP_SMQTN": "Cluster cuarentena SMTP",
    "SMTP_SPAM_DROP": "Spam detectado, mensaje rechazado  ",
    "SMTP_SPAM_IPWL": "Spam IPWL,",
    "SMTP_SPAM_SUSPECT_ALLOW": "Sospecha de spam, mensaje permitido",
    "SMTP_SPAM_SUSPECT_DROP": "Sospecha de spam, mensaje rechazado",
    "SMTP_SPAM_SUSPECT_MARKSUBJ": "Sospecha de spam, asunto antepuesto",
    "SMTP_SPAM_SUSPECT_QTN": "Sospecha de spam, mensaje en cuarentena",
    "SMTP_SPAM_UBL_DROP": "Spam UBL detectado, mensaje rechazado",
    "SMTP_SPAM_UWL": "Spam UWL,",
    "SMTP_SPAM": "SMTP - Ajustes Anti-Spam ",
    "SMTP_SPF_ENABLED": "La búsqueda SPF está habilitada desde esta empresa",
    "SMTP_TRANS_TIMEOUT_LIMIT": "SMTP/SMQTN: timeout de transacción (en segundos)",
    "SMTP_USERLIST_LIMIT": "valor LIMIT consulta lista de usuarios SMTP",
    "SMTP": "SMTP",
    "SMTPEVT_VERBOSE": "Registrar eventos smtp marcados como detallados",
    "SMTPTDL": "TDL - Límite de datos de transacción",
    "SMUI_PROCESS_TIMEOUT": "Valor de timeout de proceso SMUI",
    "SMUI": "SMUI",
    "SN_POSTING_CAUTIONED": "Precaución al publicar mensajes en este sitio",
    "SN_POSTING_DENIED": "No se permite publicar mensajes en este sitio",
    "SN_WEBUSE_CAUTIONED": "Precaución sobre el uso de esta red social/sitio de blogs",
    "SN_WEBUSE_DENIED": "No se permite el uso de esta red social/sitio de blogs",
    "SNAGFILMS_DESC": " SnagFilms",
    "SNAGFILMS": "SnagFilms",
    "SNMP_DESC": "  Aplicaciones SNMP (también llamados gestores SNMP) y agentes SNMP ",
    "SNMP": "SNMP: SNMP es un protocolo de pregunta / respuesta que comunica información de gestión entre dos tipos de entidades de software SNMP ",
    "SNMPTRAP_DESC": "Las trampas SNMP permiten a un agente notificar a la estación de gestión de eventos significativos por medio de un mensaje SNMP no solicitado",
    "SNMPTRAP": "Trampa SNMP",
    "SOA": "SOA",
    "SOAP_DESC": "  SOAP es un protocolo ligero para intercambios de información estructurada en un entorno descentralizado, distribuido.  Define, usando tecnología XML, un marco de mensajería extensible  que contiene una conttrucción de mensajes que puede ser intercambiado mediante una variedad de protocolos subyacentes ",
    "SOAP": "SOAP",
    "SOC1": "SOC1",
    "SOC2": "SOC2",
    "SOC3": "SOC3",
    "SOCIAL_ACTIVITY": "Actividad de redes sociales",
    "SOCIAL_ADULT_DESC": "Sitios que proporcionan redes sociales para adultos, como sitios de citas.",
    "SOCIAL_ADULT": "Redes sociales de adultos",
    "SOCIAL_ISSUES": "Cuestiones sociales",
    "SOCIAL_NETWORKING_GAMES": "Juegos de redes sociales",
    "SOCIAL": "Redes sociales",
    "SOCIALBAKERS": "SocialBakers",
    "SOCIALTV_DESC": " Este plug-in de protocolo clasifica el tráfico http al host srv.sixdegs.com",
    "SOCIALTV": "TV social",
    "SOCIALVIBE_DESC": " Este plug-in de protocolo clasifica el tráfico HTTP al host socialvibe.com",
    "SOCIALVIBE": "SocialVibe",
    "SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP": "La hora de actualización programada es la correspondiente a la zona horaria de su conector. Las actualizaciones deben programarse al menos con una hora de antelación.",
    "SOFTWARE_UPGRADE_SCHEDULE": "Programación de actualización de software",
    "SOLOMON_ISLANDS_PACIFIC_GUADALCANAL": "Pacific/Guadalcanal",
    "SOLOMON_ISLANDS": "Solomon Islands",
    "SOMALIA_AFRICA_MOGADISHU": "Africa/Mogadishu",
    "SOMALIA": "Somalia",
    "SORRY_THIS_CODE_CAN_NOT_BE_USED": "Lo sentimos, pero esto no se puede utilizar.",
    "SOURCE_IP_ADDRESSES": "Direcciones IP de origen",
    "SOURCE_IP_GROUP": "Grupo de IP de origen",
    "SOURCE_IP_GROUPS": "Grupos de IP de origen",
    "SOURCE_IP": "Dirección IP de origen",
    "SOURCE_PORT": "Puerto de origen",
    "SOURCE": "Fuente",
    "SOUTH_AFRICA_AFRICA_JOHANNESBURG": "Africa/Johannesburg",
    "SOUTH_AFRICA": "Sudáfrica",
    "SOUTH_AMERICA": "Sudamérica",
    "SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS": "Islas Georgias del Sur y Sandwich del Sur",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA": "Atlantic/South Georgia",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS": "South Georgia and the South Sandwich Islands",
    "SOUTH_KOREA": "Korea del Sur",
    "SOUTHAFRICA": "Sudáfrica",
    "SOUTHAFRICANORTH": "(África) Norte de Sudáfrica",
    "SOUTHAFRICAWEST": "(África) Oeste de Sudáfrica",
    "SOUTHAMERICA_EAST1_A": "southamerica-east1-a",
    "SOUTHAMERICA_EAST1_B": "southamerica-east1-b",
    "SOUTHAMERICA_EAST1_C": "southamerica-east1-c",
    "SOUTHAMERICA_EAST1": "southamerica-east1",
    "SOUTHAMERICA_WEST1_A": "southamerica-west1-a",
    "SOUTHAMERICA_WEST1_B": "southamerica-west1-b",
    "SOUTHAMERICA_WEST1_C": "southamerica-west1-c",
    "SOUTHAMERICA_WEST1": "southamerica-west1",
    "SOUTHCENTRALUS": "(EE. UU.) Centro-Sur de EE. UU.",
    "SOUTHCENTRALUSSTAGE": "(EE. UU.) Centro-Sur de EE. UU. (Stage)",
    "SOUTHEASTASIA": "(Asia Pacífico) Sureste de Asia",
    "SOUTHEASTASIASTAGE": "(Asia Pacífico) Sureste de Asia (Stage)",
    "SOUTHINDIA": "(Asia Pacífico) Sur de India",
    "SPAIN_AFRICA_CEUTA": "Africa/Ceuta",
    "SPAIN_ATLANTIC_CANARY": "Atlantic/Canary",
    "SPAIN_EUROPE_MADRID": "Europa/Madrid",
    "SPAIN": "España",
    "SPECIAL_INTERESTS": "Intereses especiales / Organizaciones sociales",
    "SPECIALIZED_SHOPPING": "Compras en línea",
    "SPLIT_DEPLOY_CORE": "Implementación dividida - Core",
    "SPLIT_DEPLOY_EDGE": "Implementación dividida - Edge",
    "SPLUNK": "Splunk",
    "SPORTS": "Deportes",
    "SPYWARE_OR_ADWARE": "Software espía o de anuncios",
    "SRI_LANKA_ASIA_COLOMBO": "Asia/Colombo",
    "SRI_LANKA": "Sri Lanka",
    "SRV_RX_BYTES": "Bytes recibidos por el servidor",
    "SRV_TIMEOUT": "Se ha agotado el tiempo de espera a la transacción de DNS porque el servidor no ha respondido",
    "SRV_TX_BYTES": "Bytes enviados por el servidor",
    "SRV_TX_DROPS": "Bytes eliminados por el servidor",
    "SSDP_DESC": "  Simple Service Discovery Protocol (SSDP) proporciona un mecanismo donde los clientes de red pueden descubrir los servicios de red deseados ",
    "SSDP": "SSDP",
    "SSH_DESC": "  Secure Shell SSH, algunas veces conocido como Secure Socket Shell, es una interfaz de comandos basada en UNIX y un protocolo para obtener acceso seguro a un computador remoto ",
    "SSH": "SSH",
    "SSHFP": "SSHFP",
    "SSL_CERTIFICATE": "Certificado SSL",
    "SSO_LOGOUT_MESSAGE": "Ha cerrado correctamente la sesión en el Portal de Cloud Connector",
    "ST_HELENA_ATLANTIC_ST_HELENA": "Atlantic/St. Helena",
    "ST_HELENA": "St. Helena",
    "ST_KITTS_AND_NEVIS": "San Cristóbal y Nieves",
    "ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON": "America/Miquelon",
    "ST_PIERRE_AND_MIQUELON": "St. Pierre and Miquelon",
    "ST_VINCENT_AND_THE_GRENADINES": "San Vicente y las Granadinas",
    "STAGED": "Preparado",
    "STANDBY": "Standby",
    "START_OVER": "Volver a empezar",
    "START_TIME": "Hora de arranque",
    "STARTS_WITH": "Comienza con",
    "STAT": "Stat",
    "STATE_PROVINCE": "Ciudad/Estado/Provincia",
    "STATE": "Estado",
    "STATIC_IP_ADDRESS": "Dirección IP estática",
    "STATIC_IP_ADDRESSES": "Direcciones IP estáticas y túneles GRE",
    "STATIC_IP_CONFLICT_WITH_SUBINTERFACE_IP": "La IP de arrendamiento estático entra en conflicto con la IP de la subinterfaz",
    "STATIC_IP_HAS_DUPLICATES_IPs": "El arrendamiento estático tiene IP duplicadas",
    "STATIC_IP_HAS_DUPLICATES_MACS": "El arrendamiento estático tiene MAC duplicadas",
    "STATIC_LEASE": "Arrendamiento estático",
    "STATIC_LOCATION_GROUPS": "Grupos de ubicaciones manuales",
    "STATIC_MANAGEMENT_IP": "IP de gestión estática",
    "STATIC_ROUTE_OPTIONAL": "Ruta estática (opcional)",
    "STATIC_ROUTE": "Ruta estática",
    "STATIC_SERVICE_IP": "IP de servicio estático",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1": "¿Está seguro de que desea actualizar el estado de la plantilla?",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED": "El elemento actualizado se puede editar/eliminar una vez que se mueve al estado Preparado.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1": "La configuración se aplicará al dispositivo cuando esté online.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED": "La plantilla no se podrá editar una vez que se actualice a Listo para implementar.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE": "¿Está seguro de que desea actualizar este recurso?",
    "STATUS_UPDATE_CONFIRMATION": "¡Confirmación de actualización de estado!",
    "STATUS": "Estado",
    "STORAGE_ACCOUNT_TEXT": "Seleccione las regiones, las suscripciones y los grupos de cuentas de almacenamiento en los que se crean el tema y el destino del asociado.",
    "STORAGE_ACCOUNT": "Cuenta de almacenamiento",
    "STREAMING_MEDIA": "Streaming de vídeo",
    "STRING": "Cadena",
    "SUB_CATEGORIES": "Subcategorías",
    "SUB_CATEGORY": "Subcategoría",
    "SUB_INTERFACE_SHUTDOWN": "Cierre de la subinterfaz",
    "SUB_INTERFACE_VLAN": "VLAN de subinterfaz",
    "SUB_INTERFACE": "Subinterfaz",
    "SUBCLOUDS": "Subnubes",
    "SUBLOCATIONS": "Sububicaciones",
    "SUBMIT_A_TICKET": "Enviar un ticket",
    "SUBMIT_TICKET": "Enviar un ticket",
    "SUBMIT": "Enviar",
    "SUBMITTED_ON": "Enviado el",
    "SUBNET_ID": "ID de subred",
    "SUBSCRIPTION_GROUP_NAME": "Nombre del grupo de suscripciones",
    "SUBSCRIPTION_GROUP": "Grupo de suscripciones",
    "SUBSCRIPTION_GROUPS_TEXT": "Configure las regiones y las suscripciones de su cuenta de Azure.",
    "SUBSCRIPTION_GROUPS": "Grupos de suscripciones",
    "SUBSCRIPTION_ID": "ID de suscripción a Azure",
    "SUBSCRIPTION_REQUIRED_MESSAGE": "Esta función requiere una suscripción que su organización no tiene actualmente.  Para obtener más información sobre esta función, póngase en contacto con su representante de ventas.",
    "SUBSCRIPTION_REQUIRED": "Se requiere suscripción",
    "SUBSCRIPTIONS": "Suscripciones",
    "SUCCESS": "Correcto",
    "SUCCESSFULLY_DELETED": "Eliminado correctamente",
    "SUCCESSFULLY_DISABLED": "Desactivado correctamente",
    "SUCCESSFULLY_ENABLED": "Activado correctamente",
    "SUCCESSFULLY_REGENERATED": "Regenerado correctamente",
    "SUCCESSFULLY_SAVED": "Guardado correctamente",
    "SUCCESSFULLY_UPDATED": "Actualizado correctamente",
    "SUDAN_AFRICA_KHARTOUM": "Africa/Khartoum",
    "SUDAN": "Sudan",
    "SUM": "SUM",
    "SUMO_LOGIC": "Sumo Logic",
    "SUNDAY": "Domingo",
    "SUPPORT_INFORMATION": "Información de soporte",
    "SUPPORT_TUNNEL": "Túnel de soporte",
    "SUPPORT": "Soporte",
    "SURINAME_AMERICA_PARAMARIBO": "America/Paramaribo",
    "SURINAME": "Surinam",
    "SURROGATE_IP_REFRESH_RATE": "Tiempo de actualización para revalidación de suplencia",
    "SUSPICIOUS_DESTINATION": "Destinos sospechosos",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN": "Arctic/Longyearbyen",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS": "Svalbard and Jan Mayen Islands",
    "SVALBARD_AND_JAN_MAYEN": "Svalbard and Jan Mayen",
    "SVPN": "Z-Tunnel 2.0",
    "SWAZILAND_AFRICA_MBABANE": "Africa/Mbabane",
    "SWAZILAND": "Swaziland",
    "SWEDEN_EUROPE_STOCKHOLM": "Europa/Estocolmo",
    "SWEDEN": "Suecia",
    "SWEDENCENTRAL": "(Europa) Suecia Central",
    "SWITZERLAND_EUROPE_ZURICH": "Europa/Zurich",
    "SWITZERLAND": "Suiza",
    "SWITZERLANDNORTH": "(Europa) Norte de Suiza",
    "SWITZERLANDWEST": "(Europa) Oeste de Suiza",
    "SYRIA": "Siria",
    "SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS": "Asia/Damasco",
    "SYRIAN_ARAB_REPUBLIC": "República Arabe Siria",
    "SYSLOG_DESC": " El protocolo Syslog se usa para la transmisión de mensajes de notificación de eventos a través de redes entre un cliente y un servidor ",
    "SYSLOG": "Syslog",
    "SYSTEM_IN_READ_ONLY_MODE_ONLY": "La interfaz de usuario actualmente se encuentra en modo de solo lectura.",
    "SYSTEM_IN_READ_ONLY_MODE": "La interfaz de usuario se encuentra en mantenimiento de actualización a una versión superior. Actualmente se encuentra en modo de solo lectura.",
    "SYSTEM_SETTINGS": "Configuración del sistema",
    "SYSTEM_USER": "Usuario del sistema",
    "SYSTEM": "Sistema",
    "TAB_SEPARATED": "Separado por tabuladores",
    "TABLE_OPTIONS": "Opciones de tabla",
    "TACACS_DESC": "Terminal Access Controller Access-Control System se refiere a una familia de protocolos que manejan autenticación remota y servicios relacionados para el control de acceso a red a través de un servidor centralizado",
    "TACACS_PLUS_DESC": "  TACACS+ (Terminal Access Controller Access-Control System Plus) es un protocolo propietario de Cisco Systems que permite control de acceso para enrutadores, servidores de acceso a red y otros dispositivos de computación en red a través de uno o mas servidores centralizados ",
    "TACACS_PLUS": "TACACS+",
    "TACACS": "TACACS",
    "TAGGED": "Tagged",
    "TAGS": "Etiquetas",
    "TAIWAN_ASIA_TAIPEI": "Asia/Taipei",
    "TAIWAN": "Taiwan",
    "TAJIKISTAN_ASIA_DUSHANBE": "Asia/Dushanbe",
    "TAJIKISTAN": "Tajikistan",
    "TANZANIA_AFRICA_DAR_ES_SALAAM": "Africa/Dar es Salaam",
    "TANZANIA": "Tanzania",
    "TARGET_ORG_ID": "OrgID de destino",
    "TARINGA_DESC": " Este plug-in de protocolo clasifica el tráfico http al host taringa.net",
    "TARINGA": "Taringa",
    "TASTELESS_DESC": " Sitios relacionados con tortura, degradación animal y humana, y otro comportamiento generalmente considerado inapropiado para el público en general",
    "TASTELESS": "De mal gusto",
    "TATTOODESIGNS": "Diseños de tatuajes",
    "TB": "TB",
    "TCF": "TCF",
    "TCHATCHE_DESC": "Tchatche es un sitio de mensajería instantánea",
    "TCHATCHE": "Tchatche",
    "TCP_ANY_DESC": "El Transmission Control Protocol (TCP) es uno de los protocolos básicos de la suite de protocolos internet (IP) y es tan común que la suite completa a menudo se denomina TCP/IP ",
    "TCP_ANY": "TCP",
    "TCP_DESC": " El protocolo de control de transmisión (TCP) es uno de los protocolos clave de la suite del protocolo de Internet (IP) y es tan común que la suite se suele llamar TCP/IP",
    "TCP_DEST_PORTS": "Puertos de destino TCP",
    "TCP_OVER_DNS_DESC": " Tcp-over-dns contiene un servidor especial dns y un cliente especial dns. El cliente y el servidor trabajan en tándem para proveer un túnel TCP y UDP mediante el protocolo estándar DNS",
    "TCP_OVER_DNS": "TCP Over DNS",
    "TCP_PORT": "Puerto TCP",
    "TCP_PORTS": "Puertos TCP",
    "TCP_SRC_PORTS": "Puertos de origen TCP",
    "TCP_STATS_COUNTER_INTERVAL": "Capturar contadores de estado tcp",
    "TCP_UNKNOWN_DESC": " Identifica tráfico TCP de proxy/cortafuegos para el que no se puede determinar más información de la aplicación",
    "TCP_UNKNOWN": "TCP desconocido",
    "TCP": "TCP",
    "TDS_DESC": "Protocolo para el sistema de administración de bases de datos relacionales Microsoft SQL",
    "TDS": "TDS",
    "TEACHERTUBE_DESC": " Este plug-in de protocolo clasifica el tráfico http a los hosts teachertube.com y teachertube.biz",
    "TEACHERTUBE": "TeacherTube",
    "TEACHSTREET_DESC": " Este plug-in de protocolo clasifica el tráfico http al host teachstreet.com",
    "TEACHSTREET": "TeachStreet",
    "TEAMSPEAK_DESC": " El protocolo propietario TeamSpeak2 se usa por jugadores y software de VoIP TeamSpeak2 ",
    "TEAMSPEAK_V3_DESC": " TeamSpeak 3 continúa el legado del sistema de comunicación TeamSpeak original. TeamSpeak 3 no es solo una extensión de sus predecesores sino una reescritura completa en C++ de su protocolo propietario y tecnología de base",
    "TEAMSPEAK_V3": "TeamSpeak 3",
    "TEAMSPEAK": "TeamSpeak",
    "TEAMVIEWER_DESC": " TeamViewer es una aplicación que permite una conexión a un computador remoto parar realizar operaciones de mantenimiento. También permite visualizar la pantalla en un computador remoto, transferir archivos, y crear un túnel VPN ",
    "TEAMVIEWER": "TeamViewer",
    "TECHINLINE_DESC": " Este plug-in de protocolo clasifica el tráfico http al host techinline.com. También clasifica el tráfico SSL al Common Name techinline.com",
    "TECHINLINE": "Techinline",
    "TECHNICAL_PRIMARY": "Contacto técnico principal",
    "TECHNICAL_SECONDARY": "Contacto técnico secundario",
    "TECHNOLOGY_COMMUNICATION": "Tecnología y comunicaciones",
    "TECHNOLOGY": "Tecnología",
    "TED": "TED",
    "TELECOMMUNICATION": "Telecomunicaciones",
    "TELEGRAM_DESC": " Telegram es un protocolo de mensajería instantánea como WhatsApp",
    "TELEGRAM": "Telegram",
    "TELEVISION_AND_MOVIES_DESC": " Sitios relacionados con programación de televisión o con películas, independientemente de la capacidad de hacer streaming o descarga de archivos de medios",
    "TELEVISION_AND_MOVIES": "Televisión/Películas",
    "TELEVISION_MOVIES_DESC": " Sitios relacionados con programación de televisión o con películas, independientemente de la capacidad de hacer streaming o descarga de archivos de medios",
    "TELEVISION_MOVIES": "Televisión/Películas",
    "TELNET_DESC": " Telnet es un mecanismo de comunicaciones bastante general, bidireccional, orientado a bytes de 8 bits. Su principal objetivo es proveer un método estándar de interfaz entre equipos terminales y procesos orientados a terminal",
    "TELNET": "Telnet",
    "TELNETS_DESC": " Versión segura de Telnet",
    "TELNETS": "Telnet seguro",
    "TEMPLATE_NAME": "Nombre de la plantilla",
    "TEMPLATE_NOT_FOUND": "Plantilla no encontrada",
    "TEMPLATE_PREFIX": "Prefijo de la plantilla",
    "TEMPLATE": "Plantilla",
    "TENANT_ID": "ID de inquilino",
    "TENANT_NAME": "Nombre del inquilino",
    "TERRA_FORMATION": "Terraform",
    "TEST_CONNECTIVITY_FAILED": "Prueba de conectividad fallida",
    "TEST_CONNECTIVITY_SUCCESSFUL": "Prueba de conectividad correcta",
    "TEST_ENVIRONMENT_TEXT": "Las pruebas de tráfico son solicitudes HTTP/HTTPS simuladas ejecutadas desde un entorno de prueba creado por Zscaler. Este entorno de prueba se conecta a la pasarela mediante un punto de conexión de VPC. Se asocia un único entorno de prueba a un inquilino de Zscaler. Todas las pruebas se ejecutan desde el mismo entorno.",
    "TEST_ENVIRONMENT": "Entorno de prueba",
    "TEST_EXECUTED": "Se ha ejecutado la prueba",
    "TEST_NAME": "Nombre de la prueba",
    "TEST_PROTOCOL": "Protocolo de prueba",
    "TESTS": "Pruebas",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_R53": "Plantilla de implementación inicial con ZPA y alta disponibilidad",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_TEMPLATE": "Plantilla de implementación inicial con alta disponibilidad",
    "TF_DEFAULT_DEPLOYMENT_TEMPLATE": "Plantilla de implementación inicial",
    "TF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Plantilla de implementación inicial con ZPA",
    "TF_STARTER_DEPLOYMENT_GWLB_TEMPLATE": "Plantilla de despliegue inicial con equilibrador de carga de pasarela (GWLB)",
    "TFTP_DESC": "Trivial File Transfer Protocol (TFTP) es un protocolo de transferencia de archivos notable por su sencillez",
    "TFTP": "TFTP",
    "THAILAND_ASIA_BANGKOK": "Asia/Bangkok",
    "THAILAND": "Tailandia",
    "THE_BASE_URL_FOR_YOUR_API": "La URL base para su API es",
    "THE_FOLLOW_REGIONS_ARE_PENDING": "Las siguientes regiones están pendientes ",
    "THE_FOLLOWING_STATIC_LEASE_IP_IS_NOT_INCLUDED_ON_THE_ADDRESS_RANGE": "La siguiente IP de arrendamiento estático no está incluida en el rango de direcciones.",
    "THE_GAMBIA": "Gambia",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_SUBNET": " La IP de la pasarela debe estar en una subred LAN.",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_WAN_SUBNET": " La IP de la pasarela debe estar en una subred LAN o WAN.",
    "THE_NETHERLANDS": "Países Bajos",
    "THERE_IS_A_PROBLEM_SAVING_PROVISIONING_TEMPLATE": "Se ha producido un problema al guardar la plantilla de aprovisionamiento. Inténtelo de nuevo más tarde.",
    "THERE_IS_A_PROBLEM_SAVING_VDI_TEMPLATE": "Se ha producido un problema al guardar la plantilla VDI. Inténtelo de nuevo más tarde.",
    "THREAT_LIBRARY": "Biblioteca de amenazas",
    "THROUGHPUT_ACROSS_SERVICES": "Rendimiento en todos los servicios",
    "THROUGHPUT_KBPS_PER_SESSION": "Rendimiento (kbps) / Sesión",
    "THROUGHPUT_SESSION": "[Rendimiento | Sesión]",
    "THURSDAY": "Jueves",
    "TIME_FRAME": "Periodo de tiempo",
    "TIME_ZONE": "Zona horaria",
    "TIMESTAMP": "Marca de tiempo",
    "TIMEZONE": "Zona horaria",
    "TIMOR_LESTE_ASIA_DILI": "Asia/Dili",
    "TIMOR_LESTE": "Timor-Leste",
    "TLS": "TLS",
    "TO": "Para",
    "TOGO_AFRICA_LOME": "Africa/Lome",
    "TOGO": "Togo",
    "TOKELAU_PACIFIC_FAKAOFO": "Pacific/Fakaofo",
    "TOKELAU": "Tokelau",
    "TOKEN_VALUE": "Valor de token",
    "TOKEN": "Token",
    "TONGA_PACIFIC_TONGATAPU": "Pacific/Tongatapu",
    "TONGA": "Tonga",
    "TOOLS": "Herramientas",
    "TOOLTIP_ACCOUNT_GROUP_DESCRIPTION": "Introduzca información descriptiva sobre el grupo",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_AUTH_DEVICES": "Muestra los dispositivos del administrador que están autorizados a utilizar la aplicación Executive Insights. Los administradores pueden registrar hasta 5 dispositivos.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_COMMENTS": "(Opcional) Introduzca notas o información adicional. Los comentarios no pueden tener más de 10240 caracteres.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_CONFIRM_PASSWORD": "Vuelva a introducir la contraseña para confirmar.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL": "Introduzca la dirección de correo electrónico profesional válida del administrador.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EXEC_MOBILE_APP_ENABLE": "Permite al administrador acceder a la aplicación Executive Insights.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_LOGIN_ID": "Introduzca el ID de inicio de sesión que utiliza el administrador para iniciar sesión desde el portal del proveedor de SSO. Seleccione el nombre de dominio apropiado. (Los nombres de dominio que ha proporcionado a Zscaler aparecen en el menú desplegable.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_MOBILE_APP_ENABLE": "Active esta opción para permitir el acceso de un administrador a la aplicación Executive Insights. Para habilitar esta configuración, el administrador requiere un ámbito de <b>Organización</b> y un <b>rol de administrador</b> con <b>Habilitar permisos para la aplicación Executive Insights</b> seleccionado.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_NAME": "Introduzca el nombre del administrador",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD_BASED_LOGIN": "Habilite esta opción si desea dar al administrador la opción de iniciar sesión directamente en el portal de administración. Esto puede ser adicional a habilitar el <b>SSO SAML para administradores</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD": "Introduzca una contraseña para el administrador. Puede tener entre 8 y 100 caracteres y debe contener al menos un número, un carácter especial y una letra mayúscula.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PRODUCT_UPDATES": "Habilite esta opción si desea que el administrador reciba comunicaciones por correo electrónico relacionadas con cambios importantes y actualizaciones en nuestro servicio.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_ROLE": "Seleccione un {0}rol{1} para especificar el nivel de acceso del administrador al portal de administración. Los roles que ha configurado aparecen en el menú desplegable. También puede buscar roles o hacer clic en el icono {2}Añadir{3} para añadir un nuevo rol. Si ha activado {4}Rango de administrador{5}, el rango de administrador asignado determina los roles que puede seleccionar.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_DEPARTMENTS": "Elija los departamentos que puede gestionar el administrador en el portal de administración. ",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATION_GROUPS": "Elija los grupos de ubicaciones que puede gestionar el administrador en el portal de administración.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATIONS": "Elija las ubicaciones que puede gestionar el administrador en el portal de administración.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE": "Seleccione un {0}ámbito{1} para especificar qué áreas de la organización puede gestionar un administrador en el portal de administración. El ámbito asignado determina los ámbitos que puede seleccionar.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SECURITY_UPDATES": "Active esta opción si desea que el administrador reciba comunicaciones por correo electrónico sobre las vulnerabilidades y amenazas que pueden afectar a su organización.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SERVICE_UPDATES": "Active esta opción si desea que el administrador reciba comunicaciones por correo electrónico sobre nuevos servicios y mejoras en los productos, incluidas notificaciones de nuevos centros de datos e información sobre el lanzamiento de la nube.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_STATUS": "Activar o desactivar el administrador",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_UNAUTH_DEVICE": "No autorizar",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ALLOW_TO_CREATE_NEW_LOCATION": "Habilite al usuario para crear nuevas ubicaciones.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_COMMENTS": "(Opcional) Introduzca notas o información adicional. Los comentarios no pueden superar los 10.240 caracteres.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_CONFIRM_PASSWORD": "Vuelva a introducir la contraseña para confirmar",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_LOGIN_ID": "Introduzca el ID de inicio de sesión del <b>auditor</b> y seleccione el nombre de dominio apropiado. (Los nombres de dominio que ha proporcionado a Zscaler aparecen en el menú desplegable.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NAME": "Introduzca el nombre del <b>auditor</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NEW_PASSWORD": "Introduzca una contraseña para el auditor. Puede tener entre 8 y 100 caracteres y debe contener al menos un número, un carácter especial y una letra mayúscula.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_EMAIL": "Introduzca la dirección de correo electrónico del administrador del socio y seleccione el nombre de dominio adecuado. Los nombres de dominio que ha proporcionado a Zscaler aparecen en el menú desplegable.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_LOGIN_ID": "Introduzca el ID de inicio de sesión que utiliza el administrador para iniciar sesión desde el portal del proveedor de SSO y seleccione el nombre de dominio adecuado. Los nombres de dominio que ha proporcionado a Zscaler aparecen en el menú desplegable.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_NAME": "Introduzca el nombre del administrador del socio",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_PASSWORD": "Si desea dar al administrador del socio la opción de iniciar sesión directamente en el Portal de administración, introduzca una contraseña. Puede tener entre 8 y 100 caracteres y debe contener al menos un número, un carácter especial y una letra mayúscula. Esto puede ser adicional a habilitar el inicio de sesión único SAML para administradores de socios.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_ROLE": "Seleccione un <b>rol de socio</b> para especificar el nivel de acceso del administrador del socio al Portal de administración. Los roles de los socios que ha configurado aparecen en el menú desplegable. También puede buscar roles o hacer clic en el icono <b>Añadir</b> para añadir un nuevo rol de socio.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_DOWNLOAD_XML_METADATA": "Haga clic en {0}Descargar {1} para exportar los metadatos XML del servicio Zscaler. Los metadatos detallan las capacidades SAML de Zscaler y se utilizan para configuración automática. Algunos {2}IdP{3} requieren los metadatos para configurar proveedores de servicios.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ENABLE_SAML_AUTH": "Active esta opción para permitir que los administradores inicien sesión en el portal de administración directamente desde su {0}portal de proveedores de SSO.{1} Un {2}IdP{3} (por ejemplo, ADFS u Okta) debe estar ya configurado para su organización y debe {4}añadir la cuenta de administrador{5} en el portal de administración (en lugar de hacerlo mediante aprovisionamiento automático).",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ISSUERS": "Opcionalmente, introduzca el emisor IdP asociado con el servicio Zscaler.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_UPLOAD_SSL_CERTIFICATE": "Haga clic en {0}Cargar{1} para cargar el certificado público SSL que se utiliza para verificar la firma digital del IdP. Este es el formato PEM codificado con Base64 que descargó del IdP. La extensión del archivo debe ser .pem o .cer y no incluir ningún otro punto (.) en el nombre del archivo.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML": "Active esta opción para permitir que el administrador inicie sesión en el Portal de Administración de Cloud Connector directamente utilizando una contraseña. Puede utilizar este método de autenticación con SSO de SAML.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_ADMIN_EDGE_CONNECTOR_TRAFFIC_FORWARDING_DNS": "Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Reenvío (tráfico, DNS y registros)",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a la gestión de claves de API.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT": " Elija esta opción para dar a los administradores acceso completo o de solo lectura a la Gestión de asistencia remota.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Controles de administración.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING": " Elija esta opción para conceder a los administradores acceso completo, de solo lectura o ningún acceso al aprovisionamiento de Cloud Connector.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD": " Elija esta opción para dar a los administradores acceso de solo lectura o ningún acceso a Paneles de control.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_LOCATION_MANAGEMENT": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Gestión de ubicación.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION": " Elija esta opción para dar a los administradores acceso completo o ningún acceso a NSS.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_POLICY_CONFIGURATION": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Política y Administración.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Gestión de configuración de nube pública.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE": " Elija esta opción para dar a los administradores acceso completo, de solo lectura o ningún acceso a Ubicación y Plantillas de aprovisionamiento.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME": "Introduzca un nombre para el {0}rol{1}.",
    "TOOLTIP_ALL_APP_SEGMENTS_ONLY": "ZPA Edge App Segment solo está disponible si está activada Aplicar a todos los App Segments.",
    "TOOLTIP_ALLOW_AWS_ACCOUNT_ID": "Activar esta opción permitiría a Zscaler importar el ID de cuenta de AWS y mostrarlo en el portal",
    "TOOLTIP_ALLOW_AZURE_SUBSCRIPTION_ID": "Activar esta opción permitiría a Zscaler importar el ID de suscripción de Azure y mostrarlo en el portal",
    "TOOLTIP_ALLOW_GCP_PROJECT_ID": "Activar esta opción permitiría a Zscaler importar el ID del proyecto GCP y mostrarlo en el portal.",
    "TOOLTIP_AWS_CONFIG_NAME": "El nombre de la cuenta de AWS.",
    "TOOLTIP_AWS_GROUP_NAME": "Introduzca el nombre que se utilizará para este grupo",
    "TOOLTIP_AWS_ROLE_NAME": "El nombre del rol de AWS en la cuenta de AWS introducida anteriormente que Zscaler asume.",
    "TOOLTIP_BC_BC_GROUP": "Seleccione un Grupo de conectores de sucursal existente para su plantilla de aprovisionamiento de sucursales.",
    "TOOLTIP_BC_COUNTRY": "Seleccione un país para la nueva ubicación.",
    "TOOLTIP_BC_DNS_SERVER": "Introduzca la dirección IP del servidor DNS.",
    "TOOLTIP_BC_FORWARDING_NET_MASK": "Introduzca la máscara de red para la dirección IP de la pasarela interna.",
    "TOOLTIP_BC_GROUP_5G": "Este grupo de conectores se asociará con esta configuración de implementación.",
    "TOOLTIP_BC_GROUP_NAME": "Introduzca un nombre para el nuevo grupo de conectores de sucursal que desea añadir.",
    "TOOLTIP_BC_HARDWARE_DEVICE": "Elija el dispositivo de hardware para su Branch Connector.",
    "TOOLTIP_BC_HYPERVISOR": "Elija el hipervisor para su Conector de sucursal.",
    "TOOLTIP_BC_INTERNAL_GATEWAY_IP_ADDRESS": "Introduzca la dirección IP de la pasarela interna.",
    "TOOLTIP_BC_IP_ADDRESS": "Introduzca la dirección IP del Conector de sucursal.",
    "TOOLTIP_BC_LOAD_BALANCER_IP_ADDRESS": "Introduzca la dirección IP del equilibrador de carga.",
    "TOOLTIP_BC_LOCATION_NAME": "Introduzca el nombre de la nueva ubicación que desea añadir.",
    "TOOLTIP_BC_LOCATION_TEMPLATE": "Seleccione una plantilla de ubicación predeterminada o configurada para su URL de aprovisionamiento.",
    "TOOLTIP_BC_LOCATION": "Seleccione una ubicación existente para su URL de aprovisionamiento.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_1": "Introduzca la dirección IP del servidor DNS principal. Este es uno de los dos servidores DNS utilizados para el equilibrio de carga.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_2": "Introduzca la dirección IP del servidor DNS secundario. Este es uno de los dos servidores DNS utilizados para el equilibrio de carga.",
    "TOOLTIP_BC_MANAGEMENT_NET_MASK": "Introduzca la máscara de red para la dirección IP del conector de sucursal.",
    "TOOLTIP_BC_PROVISIONING_NAME": "Introduzca un nombre para la plantilla de aprovisionamiento de sucursales.",
    "TOOLTIP_BC_SERVER_IP_ADDRESS": "Introduzca la dirección IP del servicio.",
    "TOOLTIP_BC_VM_SIZE": "Este campo está configurado con el valor Pequeño de forma predeterminada",
    "TOOLTIP_BLOCK_INTERNET_ACCESS": "Active esta opción para desactivar todo el acceso a Internet, incluido el tráfico no HTTP, hasta que el usuario acepte la Política de uso aceptable",
    "TOOLTIP_BW_DOWNLOAD": "Especifique el límite máximo de ancho de banda de descarga (Mbps).",
    "TOOLTIP_BW_UPLOAD": "Especifique el límite máximo de ancho de banda de carga (Mbps).",
    "TOOLTIP_CC_ROLE_NAME": "Texto de información sobre herramientas para Cloud Connector: este nombre debe estar asociado con todos los Cloud Connectors de esa cuenta",
    "TOOLTIP_CLOUD_COONECTOR_GROUP": "Seleccione los grupos de Cloud Connectors que se asociarán a este grupo de cuentas",
    "TOOLTIP_CLOUD_NSS_HTTP_HEADERS": "",
    "TOOLTIP_CLOUD_PROVIDER": "Elija el proveedor de nube para su Cloud Connector.",
    "TOOLTIP_CPU": "La CPU recomendada para el hipervisor.",
    "TOOLTIP_CUSTOM_AUP_FREQUENCY": "",
    "TOOLTIP_DEDICATED_BANDWIDTH": "Este es el ancho de banda máximo necesario para descargar los registros de Nanolog en la nube de Zscaler. Si el NSS no tiene asignado el ancho de banda que necesita, los registros podrían acumularse en el Nanolog. Esto puede dar lugar a reinicios frecuentes de la conexión y a que los registros no se transmitan al NSS.",
    "TOOLTIP_DEFAULT_GATEWAY_IP_ADDRESS": "Introduzca la dirección IP de la pasarela predeterminada.",
    "TOOLTIP_DEFAULT_GATEWAY": "Introduzca una dirección IP de pasarela predeterminada válida.",
    "TOOLTIP_DEFAULT_LEASE_TIME": "Introduzca el tiempo predeterminado de arrendamiento en segundos.",
    "TOOLTIP_DEPLOY_AS_GATEWAY": "Seleccione Sí o No para decidir si el dispositivo de hardware se implementa como pasarela.",
    "TOOLTIP_DESCRIPTION": "(Opcional) Introduzca notas o información adicional.",
    "TOOLTIP_DESTINATION_IP_ADDRESS": "Introduzca las direcciones IP. Puede introducir direcciones IP individuales, subredes o rangos de direcciones. Si va a añadir varios elementos, pulse Intro después de cada entrada.",
    "TOOLTIP_DESTINATION_IP_COUNTRIES": "Para identificar destinos en función de la ubicación de un servidor, seleccione Cualquiera para incluir todos los países del grupo o seleccione países específicos.",
    "TOOLTIP_DESTINATION_IP_DOMAIN": "Introduzca nombres de dominio completos (FQDN) o FQDN comodín. Utilice el punto ('.') como carácter comodín. Para añadir varios elementos, pulse Intro después de cada entrada.",
    "TOOLTIP_DESTINATION_IP_FQDN": "Introduzca nombres de dominio completos (FQDN). Si va a añadir varios elementos, pulse Intro después de cada entrada.",
    "TOOLTIP_DESTINATION_IP_NAME": "Agrupe los destinos que desee controlar en una regla de firewall, especificando las direcciones IP, los países donde se encuentran los servidores y las categorías de URL.",
    "TOOLTIP_DESTINATION_TYPE": "Seleccione el tipo de grupo de destino",
    "TOOLTIP_DEVICE_SN": "Seleccione un número de serie de dispositivo.",
    "TOOLTIP_DHCP_OPTIONS": "Puede crear pasarelas y nombres de dominio predeterminados como criterios para DHCP.",
    "TOOLTIP_DHCP": "Seleccione Activado para introducir los detalles del servidor DNS o Desactivado para desactivar el Protocolo de configuración dinámica de host (DHCP).",
    "TOOLTIP_DISK_STORAGE": "Muestra el almacenamiento en disco recomendado para la carga de trabajo de su organización.",
    "TOOLTIP_DNS_SERVER_IP_1": "Introduzca la dirección IP del servidor DNS primario.",
    "TOOLTIP_DNS_SERVER_IP_2": "Introduzca la dirección IP del servidor DNS secundario.",
    "TOOLTIP_DNS_SERVER": "Introduzca una dirección IP de servidor DNS válida.",
    "TOOLTIP_DOMAIN_NAME": "Introduzca un nombre de dominio válido.",
    "TOOLTIP_EBS_STORAGE": "Almacenamiento recomendado",
    "TOOLTIP_EC2_INSTANCE_TYPE": "Tipo de instancia EC2 recomendado",
    "TOOLTIP_EDIT_ORGANIZATION_API_KEY_NEW_KEY": "La nueva clave de API puede ser alfanumérica (A-Z, a-z, 0-9) y tener exactamente 12 caracteres de longitud.",
    "TOOLTIP_ENABLE_AUP": "Active esta opción para mostrar una Política de uso aceptable para el tráfico no autenticado y exigir a los usuarios que la acepten",
    "TOOLTIP_ENABLE_CAUTION": "Active esta opción para aplicar la acción de la política de precaución y mostrar una notificación al usuario final para el tráfico no autenticado. Si está desactivada, la acción se trata como política de autorización.",
    "TOOLTIP_ENABLE_IPS_CONTROL": "Active esta opción para permitir que un administrador acceda a Controlar IPS.",
    "TOOLTIP_ENABLE_SURROGATE_BROWSER": "Si se activa y la asignación IP-usuario existe, se utiliza la identidad de usuario Suplente también para el tráfico de los navegadores conocidos. Si se desactiva, el tráfico de los navegadores conocidos siempre se cuestionará utilizando el mecanismo de autenticación configurado y se ignorará la identidad de usuario Suplente",
    "TOOLTIP_ENABLE_SURROGATE_REFRESH_TIME": "Este es el tiempo durante el que se puede utilizar la identidad de usuario suplente para el tráfico de navegadores conocidos antes de que deba actualizar y volver a validar la identidad de usuario suplente mediante el mecanismo de autenticación configurado.{0}{1} NOTA: El tiempo de actualización para la revalidación de la suplencia de IP debe ser inferior al tiempo de arrendamiento de DHCP. De lo contrario, podrían aplicarse políticas de usuario incorrectas.",
    "TOOLTIP_ENABLE_SURROGATE": "Permite la asignación de usuario a dispositivo cuando la dirección IP interna se puede distinguir de la dirección IP pública. Esto se utiliza para aplicar políticas de usuario en el tráfico incompatible con cookies. Para obtener más información, consulte la página de Ayuda.",
    "TOOLTIP_ENABLE_XFF_FORWARDING": "Habilite XFF de solicitud de cliente si desea que el servicio Zscaler utilice las cabeceras X-Forwarded-For (XFF) que su servidor proxy local inserta en las solicitudes HTTP salientes. Tenga en cuenta que cuando el servicio reenvíe el tráfico a su destino, eliminará esta cabecera XFF original y la sustituirá por una cabecera XFF que contenga la dirección IP de la pasarela del cliente (la dirección IP pública de la organización). Esto garantiza que las direcciones IP internas de una organización nunca queden expuestas al mundo exterior.",
    "TOOLTIP_ENFORCE_AUTHENTICATION": "Active la autenticación para forzar la identificación del tráfico de usuarios individuales aplicando el mecanismo de autenticación de usuarios configurado.",
    "TOOLTIP_ENFORCE_BAND_WIDTH_CONTROL": "Seleccione Activar para aplicar el control de ancho de banda para la ubicación.",
    "TOOLTIP_ENFORCE_FIREWALL_CONTROL": "Seleccione Aplicar control de firewall para activar el firewall en la ubicación.",
    "TOOLTIP_ENTER_AWS_ACCOUNT_ID": "El ID de la cuenta de AWS donde se implementan las cargas de trabajo.",
    "TOOLTIP_EVENT_BUS_NAME": "Eventbridge Event Bus se utiliza para enviar notificaciones en tiempo real a Zscaler Event Bus. Se puede utilizar una regla en Eventbridge para enviar notificaciones de cambios de recursos en tiempo real a Zscaler. Esto es necesario para permitir actualizaciones en tiempo real de políticas. Zscaler recibe notificación sobre la creación de una VM.",
    "TOOLTIP_EXTERNAL_ID": "Zscaler utiliza esta ID externa en la llamada de API a AWS mientras recupera la información de los identificadores. El ID externo es único para cada cuenta creada. Este ID debe añadirse en la configuración de roles IAM de AWS. Si regenera este ID, actualícelo también en la cuenta de AWS.",
    "TOOLTIP_FAILURE_BEHAVIOR": "Seleccione el comportamiento de fallo.",
    "TOOLTIP_FORCE_SSL_INTERCEPTION": "Active esta opción para que la interceptación SSL aplique una política de uso aceptable para el tráfico HTTPS",
    "TOOLTIP_GATEWAY_FAIL_CLOSE": "Esta opción indica cómo gestionar el tráfico cuando están inaccesibles los proxies primarios y secundarios definidos en esta pasarela. Activar esta opción anula el tráfico y desactivar esta opción permite el tráfico. De forma predeterminada, esta opción está activada.",
    "TOOLTIP_GATEWAY_NAME": "Introduzca un nombre para la pasarela que se va a crear para un servicio proxy de terceros.",
    "TOOLTIP_GENERAL_DESCRIPTION": "(Opcional) Introduzca notas o información adicional. La descripción no puede tener más de 10240 caracteres.",
    "TOOLTIP_HA_ID": "Introduzca el ID del dispositivo de alta disponibilidad.",
    "TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS": "Este campo puede mostrar comentarios sobre la dirección IP introducida. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK": "Puede introducir una dirección IP para comprobar si se ha incluido en la lista de denegación. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS": "El campo indica si la dirección IP introducida ha sido incluida en la lista de denegación. ",
    "TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE": "Permita que los ingenieros de soporte de Zscaler inicien sesión de forma remota en su Portal de administración con plenos privilegios de administrador. No necesita crear cuentas ni compartir contraseñas para activar el acceso.",
    "TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE": "Permita que los empleados autorizados de Zscaler accedan a su Portal de administración con privilegios de solo lectura. El acceso al portal se utilizará para producir contenido e informes de éxito del cliente, proporcionar asistencia remota y ver informes y configuración para contribuir a mejorar el producto y los servicios de Zscaler.",
    "TOOLTIP_HELP_LOOKUP_URL_ENTER_URL": "Para buscar la categoría (o categorías) a la que pertenece una URL, escriba la URL y haga clic en {0}Búsqueda de URL{1}. El servicio muestra la {2}categoría o supercategoría predefinida de la URL{3} e indica si hay una alerta de seguridad asociada a la URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_SECURITY_ALERT": "Este campo indica si hay una alerta de seguridad asociada a la URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL_CLASSIFICATIONS": "Este campo muestra la {0}categoría o supercategoría predefinida{1} a la que pertenece la URL. ",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL": "Este campo muestra la URL que ha buscado.",
    "TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL": "Recomendamos permitir el acceso durante al menos dos días.",
    "TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL": "Recomendamos permitir el acceso durante al menos un año.",
    "TOOLTIP_HW_DEVICE_NAME": "Introduzca un nombre para la plantilla.",
    "TOOLTIP_HW_SUBINTERFACE_SHUTDOWN": "Seleccione Activo o Standby como modo de enlace ascendente.",
    "TOOLTIP_HW_VLAN_ID": "Introduzca el ID de la VLAN.",
    "TOOLTIP_HW_VLAN": "Seleccione Etiquetado o Sin etiquetar para la red de área local virtual (VLAN).",
    "TOOLTIP_INTERFACE_SHUTDOWN": "Seleccione Sí o No para decidir el comportamiento de cierre de la interfaz.",
    "TOOLTIP_IP_ADDRESS_RANGE": "Introduzca el rango de direcciones IP de su dispositivo.",
    "TOOLTIP_IP_ADDRESS_WITH_NETMASK": "Introduzca una dirección IP con el formato a.b.c.d/mask",
    "TOOLTIP_IP_POOL_NAME": "El nombre del grupo de IP.",
    "TOOLTIP_LIST_CUSTOM_OPTION_CODE": "Introduzca un código de opción DHCP válido conforme a lo definido por la IANA. No se permiten opciones predefinidas",
    "TOOLTIP_LIST_CUSTOM_OPTION_NAME": "Introduzca un nombre para esta opción personalizada - No analizada",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_IP": "El tipo de datos ip-address debe introducirse como dirección IP explícita.  Se pueden definir un máximo de cuatro direcciones IP separadas por comas.\nPor ejemplo, *************, ************",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_STRING": "El tipo de datos de cadena especifica un ASCII NVT o una serie de octetos especificados en hexadecimal.\nPor ejemplo, '********:/var/tmp/rootfs' O 43:4c:49:45:54:2d:46:4f:4f",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE": "Seleccione el tipo entre IP o Cadena",
    "TOOLTIP_LIST_CUSTOM_OPTION": "Las opciones de DHCP personalizadas le permiten configurar opciones de DHCP que no están predefinidas en la lista desplegable.",
    "TOOLTIP_LIST_DNS_SERVER": "Introduzca una o una lista de direcciones IP de servidor DNS válidas.",
    "TOOLTIP_LIST_DOMAIN_NAME": "Introduzca uno o una lista de nombres de dominio válidos.",
    "TOOLTIP_LOCATION_CREATION": "Este campo está configurado con el valor Automático de forma predeterminada.",
    "TOOLTIP_LOCATION_TEMPLATE_NAME": "Introduzca el nombre de la plantilla de ubicación que desea añadir.",
    "TOOLTIP_LOCATION_TEMPLATE": "Seleccione una plantilla de ubicación predeterminada o configurada para su URL de aprovisionamiento.",
    "TOOLTIP_LOCATIONS_CUSTOM_AUP_FREQUENCY_TEXT": "Introduzca la frecuencia en días con la que debe mostrarse a los usuarios la Política de uso aceptable",
    "TOOLTIP_LOCATIONS_ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Si se activa y la asignación IP-usuario existe, se utiliza la identidad de usuario Suplente también para el tráfico de los navegadores conocidos. Si se desactiva, el tráfico de los navegadores conocidos siempre se cuestionará utilizando el mecanismo de autenticación configurado y se ignorará la identidad de usuario Suplente",
    "TOOLTIP_LOCATIONS_IDLE_TIME_DISASSOCIATION": "Si ha activado la suplencia de IP (IP Surrogate), en Tiempo inactivo hasta disociación, especifique cuánto tiempo después de que se complete una transacción debe conservar el servicio la asignación de dirección IP a usuario.",
    "TOOLTIP_MAX_LEASE_TIME": "Introduzca el tiempo máximo de arrendamiento en segundos.",
    "TOOLTIP_MTU_1500": "La unidad máxima de transmisión (MTU) para bytes.",
    "TOOLTIP_MTU": "La unidad máxima de transmisión (MTU) para bytes. El valor predeterminado es 1400",
    "TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD": "Si se activa, los paneles de control se actualizarán automáticamente cada 15 minutos.",
    "TOOLTIP_MY_PROFILE_CONFIRM_NEW_PASSWORD": "Vuelva introducir su nueva contraseña. Debe ser la misma que la contraseña introducida en el campo {0}Nueva contraseña{1}.",
    "TOOLTIP_MY_PROFILE_LANGUAGE": "El portal de administración se muestra en inglés de forma predeterminada. También puede seleccionar español, francés, chino tradicional o japonés.",
    "TOOLTIP_MY_PROFILE_NEW_PASSWORD": "Introduzca su nueva contraseña. Debe tener al menos ocho caracteres y un número, una mayúscula y un carácter especial. Solo se permiten caracteres ASCII.",
    "TOOLTIP_MY_PROFILE_OLD_PASSWORD": "Introduzca su contraseña actual.",
    "TOOLTIP_MY_PROFILE_PASSWORD": "Una contraseña debe tener al menos ocho caracteres e incluir un número, una mayúscula y un carácter especial. Solo se permiten caracteres ASCII.",
    "TOOLTIP_MY_PROFILE_POLICY_INFORMATION": "Active esta opción para mostrar la información de la política.",
    "TOOLTIP_MY_PROFILE_TIMEZONE": "Cuando el servicio guarda transacciones, utiliza UTC. Utiliza la zona horaria especificada cuando muestra los registros.",
    "TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME": "El ID de inicio de sesión del administrador asignado al crear la cuenta de administrador.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_DESCRIPTION": "(Opcional) Introduzca notas o información adicional. La descripción no puede tener más de 10240 caracteres.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_NAME": "Introduzca un nombre para el {0}grupo de servicios de red{1}. Puede incluir cualquier carácter y espacios.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_SERVICES": "Elija cualquier número de servicios personalizados y predefinidos que desee incluir en el grupo.",
    "TOOLTIP_NETWORK_SERVICES_DEFINITION": "El servicio muestra {0}Personalizado{1} para indicar que es un servicio definido por el administrador.",
    "TOOLTIP_NETWORK_SERVICES_DESCRIPTION": "(Opcional) Introduzca notas o información adicional. La descripción no puede tener más de 10240 caracteres.",
    "TOOLTIP_NETWORK_SERVICES_NAME": "Introduzca un nombre para el {0}servicio de capa de aplicación{1} que desee controlar. Puede incluir cualquier carácter y espacios.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_DESTINATION_PORTS": "El número de puerto de destino SCTP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_SOURCE_PORTS": "El número de puerto de origen SCTP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NETWORK_SERVICES_TCP_DESTINATION_PORTS": "El número de puerto de destino TCP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NETWORK_SERVICES_TCP_SOURCE_PORTS": "El número de puerto de origen TCP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NETWORK_SERVICES_UDP_DESTINATION_PORTS": "El número de puerto de destino UDP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NETWORK_SERVICES_UDP_SOURCE_PORTS": "El número de puerto de origen UDP (por ejemplo: 50) o el rango de números de puertos (por ejemplo: 1000-1050), si lo hubiera, que utiliza el servicio de red.",
    "TOOLTIP_NSS_CLOUD_FEED_API_URL": "La URL HTTPS del punto de conexión API de recopilación de registros SIEM.",
    "TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Introduzca la URL de autorización con el ID del directorio (inquilino) generado en Azure",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Introduzca el ID de la clave de acceso para el usuario creado en AWS",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Introduzca la clave de acceso secreta para el usuario creado en AWS",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID": "Introduzca el ID de la aplicación (cliente) generado en Azure",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET": "Introduzca el valor de secreto del cliente de la aplicación generado en Azure",
    "TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE": "Introduzca la siguiente cadena: client_credentials",
    "TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Transmitir registros en formato JSON Array (por ejemplo, [{JSON1},{JSON2}])",
    "TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Limite el tamaño de una carga de solicitud HTTP individual a la práctica idónea de SIEM",
    "TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Activado de forma predeterminada y no editable",
    "TOOLTIP_NSS_CLOUD_FEED_SCOPE": "Introduzca la siguiente cadena: https://monitor.azure.com//.default",
    "TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE": "Seleccione su SIEM basado en la nube",
    "TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL": "Introduzca la URL de la carpeta creada en el bucket S3",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Utilice este filtro para limitar los registros a grupos de Cloud/Branch Connectors específicos.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Utilice este filtro para limitar los registros a Cloud/Branch Connectors específicos.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Utilice este filtro para limitar los registros según la dirección IP privada de un cliente. Puede introducir: {0}Una dirección IP, como, por ejemplo, **************{1}Un rango de direcciones IP, como, por ejemplo, *********-*********0{2}Una dirección IP con una máscara de red, como, por ejemplo, ***********/24{3}Haga clic en Intro después de cada entrada.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Utilice este filtro para limitar los registros a sesiones asociadas a tipos específicos de solicitud de DNS.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Utilice este filtro para limitar los registros a sesiones asociadas a códigos específicos de respuesta de DNS.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Utilice este filtro para limitar los registros a sesiones asociadas a códigos específicos de respuesta de DNS.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Limite los registros a sesiones que contuvieran datos específicos en las respuestas de DNS. Puede especificar nombres de dominio y direcciones IPv4 e IPv6. Para direcciones IPv4, puede introducir una dirección IP, un rango de direcciones IP o una dirección IP con una máscara de red.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DOMAINS": "Utilice este filtro para limitar los registros a sesiones asociadas a dominios específicos.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DURATIONS": "Utilice este filtro para limitar los registros en función de la duración, en segundos, de las sesiones.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_LOCATIONS": "Utilice este filtro para limitar los registros a ubicaciones específicas desde las que se generaron transacciones. Puede buscar ubicaciones. No hay límite para el número de ubicaciones que puede seleccionar. Las ubicaciones que se eliminan después de ser seleccionadas aparecen tachadas con una línea.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Limite los registros según acciones de políticas de DNS específicas",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_RULE_NAME": "Utilice este filtro para limitar los registros en función de reglas específicas de la política de Control de DNS. Elija las reglas de la lista.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_ADDRESS": "Utilice este filtro para limitar los registros a direcciones IP de servidor específicas. Puede introducir: {0}Una dirección IP, como, por ejemplo, **************{1}Un rango de direcciones IP, como, por ejemplo, *********-*********0{2}Una dirección IP con una máscara de red, como, por ejemplo, ***********/24{3}Haga clic en Intro después de cada entrada.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_PORTS": "Utilice este filtro para limitar los registros a puertos de servidor específicos. Puede introducir puertos individuales y un rango de puertos.",
    "TOOLTIP_NSS_FEED_DUPLICATE_LOGS": "Para garantizar que no se omita ningún registro durante el tiempo de inactividad, especifique el número de minutos que NSS enviará registros duplicados.",
    "TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE": "Limite los registros según el tipo de registro de métricas",
    "TOOLTIP_NSS_FEED_ESCAPE_CHARACTER": "Opcionalmente, escriba un carácter que desea codificar en hexadecimal cuando aparezca en URL, Host o Referrer. Por ejemplo, escriba una coma, ',' para que se codifique como %2C. Esto es útil si está utilizando este carácter como delimitador y desea asegurarse de que no provoca una delimitación errónea. Si se ha realizado codificación personalizada para un registro, el campo {eedone} será 'SÍ' para ese registro.",
    "TOOLTIP_NSS_FEED_LOG_TYPE": "Elija el tipo de registros que está transmitiendo.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Utilice este filtro para limitar los registros a Cloud/Branch Connectors específicos.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Utilice este filtro para limitar los registros a VM de Branch Connector.",
    "TOOLTIP_NSS_FEED_NAME": "Cada feed es una conexión entre NSS y su SIEM. Introduzca un nombre para el feed.",
    "TOOLTIP_NSS_FEED_OUTPUT_FORMAT": "Estos son los campos que se mostrarán en la salida. Puede editar la lista predeterminada y, si elige Personalizado como Tipo de salida de campo, también puede cambiar el delimitador. Consulte el formato de salida de feed NSS para obtener información sobre los campos disponibles y su sintaxis.",
    "TOOLTIP_NSS_FEED_OUTPUT_TYPE": "Por defecto, la salida es una lista separada por comas (CSV). Elija: Separado por tabulaciones para crear una lista separada por tabulaciones. Personalizado para usar un delimitador diferente, como un guion, e introduzca el delimitador cuando especifique el formato de salida del feed. El tipo de salida del feed de su SIEM, si aparece en la lista",
    "TOOLTIP_NSS_FEED_SERVER": "Elija un NSS de la lista.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Utilice este filtro para limitar los registros a grupos de Cloud/Branch Connectors específicos.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Utilice este filtro para limitar los registros a Cloud/Branch Connectors específicos.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Utilice este filtro para limitar los registros según la dirección IP privada de un cliente. Puede introducir: {0}Una dirección IP, como, por ejemplo, **************{1}Un rango de direcciones IP, como, por ejemplo, *********-*********0{2}Una dirección IP con una máscara de red, como, por ejemplo, ***********/24{3}Haga clic en Intro después de cada entrada.{4}",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_GATEWAY": "Utilice este filtro para limitar los registros a puertas de enlace específicas.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_LOCATIONS": "Utilice este filtro para limitar los registros a ubicaciones específicas desde las que se generaron transacciones. Puede buscar ubicaciones. No hay límite para el número de ubicaciones que puede seleccionar. Las ubicaciones que se eliminan después de ser seleccionadas aparecen tachadas con una línea.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Seleccione Cualquiera para aplicar el feed de NSS a todos los servicios de red, o bien seleccione servicios de red específicos.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Utilice este filtro para limitar los registros en función de la acción realizada por el servicio, de acuerdo con las reglas de la política de Control de reenvío.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME": "Utilice este filtro para limitar los registros en función de reglas específicas de la política de Control de reenvío. Elija las reglas de la lista.",
    "TOOLTIP_NSS_FEED_SESSION_LOG_TYPE": "Elija el tipo de registro de sesión",
    "TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE": "Seleccione la dirección IP o FQDN como el tipo de destino del SIEM al que se transmiten los registros.",
    "TOOLTIP_NSS_FEED_SIEM_FQDN": "Introduzca el FQDN del SIEM al que se emiten los registros. Asegúrese de que el SIEM esté configurado para aceptar el feed del NSS.",
    "TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS": "Introduzca la dirección IP del SIEM al que se emiten los registros. Asegúrese de que el SIEM esté configurado para aceptar el feed del NSS.",
    "TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT": "Introduzca un límite de frecuencia adecuado para los eventos por segundo que desea transmitir a su SIEM. Un límite demasiado bajo para el volumen de tráfico provocará la pérdida de registros.",
    "TOOLTIP_NSS_FEED_SIEM_RATE": "Manténgalo como Ilimitado, a menos que necesite restringir la transmisión de salida debido a licencias SIEM u otras restricciones.",
    "TOOLTIP_NSS_FEED_SIEM_TCP_PORT": "Introduzca el número de puerto del SIEM al que se emiten los registros. Asegúrese de que el SIEM esté configurado para aceptar el feed del NSS.",
    "TOOLTIP_NSS_FEED_STATUS": "El feed NSS está activado por defecto. Elija Desactivado si desea activarlo más adelante.",
    "TOOLTIP_NSS_FEED_TIMEZONE": "Esta es, por defecto, la zona horaria de la organización. La zona horaria que establezca se aplicará al campo de hora en el archivo de salida. La zona horaria se ajusta automáticamente al cambio de hora de la zona horaria específica. La zona horaria configurada puede aparecer en los registros como campo independiente. La lista de zonas horarias se deriva de la base de datos de zonas horarias de IANA. También se pueden especificar desplazamientos directos de GMT.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Opcionalmente, especifique el número de usuarios.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Puede recuperar estos datos accediendo al panel Resumen de DNS. Se recomienda hacer esto para ajustar la especificación de VM a la carga de trabajo de su organización.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR": "Puede recuperar estos datos accediendo al panel Resumen de firewall. Se recomienda hacer esto para ajustar la especificación de VM a la carga de trabajo de su organización.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM": "Indique la plataforma utilizada para desplegar NSS.",
    "TOOLTIP_NSS_SERVER_NAME": "Introduzca un nombre para el servidor NSS.",
    "TOOLTIP_NSS_SERVER_SSL_CERTIFICATE": "",
    "TOOLTIP_NSS_SERVER_STATE": "El estado del servidor NSS.",
    "TOOLTIP_NSS_SERVER_STATUS": "El NSS está activado de forma predeterminada.",
    "TOOLTIP_NSS_TYPE": "Este campo es de solo lectura.",
    "TOOLTIP_NSS_VIRTUAL_MACHINE": "Haga clic para descargar el archivo OVA de NSS.",
    "TOOLTIP_NUMBER_OF_CORES": "El número recomendado de núcleos para el hipervisor.",
    "TOOLTIP_PASSPHRASE": "Introduzca una frase de contraseña para el dispositivo.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRATION_EC": "Active la caducidad de las contraseñas de todos los administradores que inicien sesión en los portales de administración de ZIA, Cloud Connector y ZDX. Si se desactiva esta opción, las contraseñas nunca caducarán.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRES_AFTER": "Introduzca el número de días que desea que las contraseñas sean válidas para los administradores de ZIA y ZDX. Puede ser de 15 a 365.",
    "TOOLTIP_PEER_DHCP": "Introduzca una dirección IP de DHCP de par.",
    "TOOLTIP_POLICY_APP_SEGMENT_GROUP": "Seleccione hasta 600 grupos de segmentos para aplicar la regla de reenvío de tráfico. Si no se selecciona ningún grupo, la regla no se aplica a ningún grupo",
    "TOOLTIP_POLICY_APP_SEGMENT": "Seleccione hasta 50k Application Segments para aplicar a la regla de reenvío de tráfico. Si no se selecciona ningún segmento, la regla no se aplica a ningún segmento.",
    "TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS": "Seleccione cualquier número de grupos de servicios de red predefinidos o personalizados. Si no se selecciona ningún grupo de servicios de red, la regla se aplica a todos los grupos de servicios de red.",
    "TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES": "Introduzca un comodín y nombres de dominio completos (FQDN). Si va a añadir varios elementos, pulse Intro después de cada entrada.",
    "TOOLTIP_POLICY_DNS_DESTINATION_GROUPS": "Seleccione cualquier número de grupos de destino. Si no se selecciona un grupo de destino, la regla se aplica a todos los grupos de destino",
    "TOOLTIP_POLICY_DNS_GATEWAY": "Elija una pasarela DNS.",
    "TOOLTIP_POLICY_DNS_RULE_ORDER": "Las reglas de política se evalúan en orden numérico ascendente (la Regla 1 antes que la Regla 2, y así sucesivamente), y el Orden de Regla refleja el lugar de esta regla en el orden.",
    "TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT": "Active esta configuración para aplicar la regla de reenvío de tráfico a todos App Segments existentes y futuros que se creen. Desactive esta configuración para seleccionar Application Segments o grupos de Application Segments específicos.",
    "TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC": "Seleccione un máximo 32 grupos. Si no se selecciona ningún grupo, la regla se aplica a todos los grupos.",
    "TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES": "Seleccione cualquier número de servicios de red. Si no se selecciona ningún servicio de red, la regla se aplica a todos los servicios de red. El firewall Zscaler tiene servicios predefinidos y puede configurar hasta 1.024 servicios personalizados adicionales.",
    "TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC": "Elija entre las siguientes opciones:{0}Permitir{1}: Permitir las solicitudes y respuestas de DNS.{2}Bloquear{3}: Bloquear silenciosamente todas las solicitudes y respuestas de DNS.{4}Resuelto por ZPA{5}: Solicita a Cloud Connector que resuelva las solicitudes de DNS utilizando el grupo de IP. Para utilizar esta opción debe haber un grupo de IP disponible.{6} Bloquear silenciosamente todas las solicitudes y respuestas de DNS.{4}Resuelto por ZPA{5}: Solicita a Cloud Connector que resuelva las solicitudes de DNS utilizando el grupo de IP. Debe haber un grupo de IP disponible para usar esta opción.{6}Solicitud de redireccionamiento{7}: Redirigir todas las solicitudes y respuestas de DNS a una pasarela DNS. Debe haber una pasarela DNS disponible para usar esta opción.{8}",
    "TOOLTIP_POLICY_FIREWALL_DESCRIPTION": "(Opcional) Introduzca notas o información adicional. La descripción no puede tener más de 10.240 caracteres.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY": "Para identificar destinos en función de la ubicación de un servidor. Seleccione cualquier número de países. Si no se selecciona ningún país, la regla se aplica a todos los países.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES": "Introduzca las direcciones IP y los nombres de dominio completos (FQDN), si el dominio tiene varias direcciones IP de destino o si sus direcciones IP pueden cambiar. Para direcciones IP, puede introducir direcciones IP individuales, subredes o rangos de direcciones. Si va a añadir varios elementos, pulse Intro después de cada entrada.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS": "Seleccione cualquier número de grupos de direcciones IP de destino. Si no se selecciona ningún grupo de direcciones IP de destino, la regla se aplica a todos los grupos de direcciones IP de destino.",
    "TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD": "Seleccione uno de los siguientes métodos de reenvío para esta regla:{0}Directo:{1} omite el acceso a Zscaler Internet Access (ZIA) y/o Zscaler Private Access (ZPA) y reenvía el tráfico directamente al servidor de destino utilizando la dirección IP del servicio Zscaler{2}Directo con traducción SCTP:{3} reenvía el tráfico directamente al destino mientras canaliza el tráfico SCTP a través de UDP y viceversa{4}ZIA{5}: reenvía el tráfico a ZIA a través de la pasarela ZIA{6}ZPA{7}: reenvía el tráfico a ZPA a través de la nube ZPA{8}ZPA con traducción SCTP:{9} reenvía el tráfico a Zscaler Private Access (ZPA) a través de la nube ZPA mientras canaliza el tráfico SCTP a través de UDP y viceversa,{0}Descartar{1}: descarta todos los paquetes que coinciden con la regla de reenvío de tráfico{2}",
    "TOOLTIP_POLICY_FIREWALL_GATEWAY": "Seleccionar una pasarela",
    "TOOLTIP_POLICY_FIREWALL_IPPOOL": "Seleccionar un grupo de IP",
    "TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP": "Seleccione un máximo de 32 grupos de ubicaciones. Si no se selecciona ningún grupo de ubicaciones, la regla se aplica a todos los grupos de ubicaciones.",
    "TOOLTIP_POLICY_FIREWALL_LOCATION": "Seleccione un máximo de 8 ubicaciones. Si no se selecciona ninguna ubicación, la regla se aplica a todas las ubicaciones.",
    "TOOLTIP_POLICY_FIREWALL_RULE_MSFT_OFFICE_365": "Esta regla se crea automáticamente cuando se activa 'Microsoft Recommended One Click configuration for Office 365' para permitir la salida local de todo el tráfico de Office 365 en el producto Cloud Firewall",
    "TOOLTIP_POLICY_FIREWALL_RULE_NAME": "El DNS crea automáticamente un Nombre de regla que puede cambiarse. La longitud máxima es de 31 caracteres.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ORDER": "Las reglas de política se evalúan en orden numérico ascendente (la Regla 1 antes que la Regla 2, y así sucesivamente), y el Orden de Regla refleja el lugar de esta regla en el orden.",
    "TOOLTIP_POLICY_FIREWALL_RULE_STATUS": "Una regla activada se aplica activamente. Una regla desactivada no se aplica activamente, pero no pierde su lugar en el orden de reglas. El servicio la omite y pasa a la siguiente regla.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER_WARNING": "Esta regla se crea automáticamente para redirigir el tráfico a ZPA. Se recomienda mover la regla DNS predefinida para ZPA al orden 1 o 2",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER": "Esta regla se crea automáticamente para redirigir el tráfico a ZPA",
    "TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS": "Seleccione cualquier número de grupos de direcciones IP de origen. Si no se selecciona ningún grupo de direcciones IP de origen, la regla se aplica a todos los grupos de direcciones IP de origen.",
    "TOOLTIP_POLICY_FIREWALL_UCAAS": "Esta regla se crea automáticamente cuando se activan una o todas las aplicaciones UCaaS para permitir el tráfico desde Cloud Firewall",
    "TOOLTIP_PORT_NO": "Seleccione un número de puerto del menú desplegable.",
    "TOOLTIP_PRIMARY_DNS_SERVER": "Introduzca la dirección IP del servidor DNS principal.",
    "TOOLTIP_PRIMARY_PROXY": "Seleccione el proxy principal para la pasarela.",
    "TOOLTIP_PROV_TEMPLATE_NAME": "Introduzca un nombre para la plantilla de aprovisionamiento de nube.",
    "TOOLTIP_REGION": "Seleccione las regiones donde desea que Zscaler detecte identificadores. Zscaler descubre identificadores a nivel regional.",
    "TOOLTIP_SECONDARY_DNS_SERVER": "Introduzca la dirección IP del servidor DNS secundario.",
    "TOOLTIP_SECONDARY_PROXY": "Seleccione el proxy secundario para la pasarela. Esto se utiliza cuando no se puede acceder al proxy principal.",
    "TOOLTIP_SESSIONS_ACROSS_SERVICES": "Número total de sesiones registradas en todos los conectores de nube/sucursal durante las últimas 24 horas",
    "TOOLTIP_SHUTDOWN": "Seleccione Sí o No para decidir el comportamiento de cierre.",
    "TOOLTIP_SOURCE_IP_ADDRESSES": "Introduzca cualquier número de direcciones IP. Puede introducir: {0} Una dirección IP (**************){1}Un rango de direcciones IP *********-*********0{2}Una dirección IP con máscara de red ***********/24{3} Pulse {4}Intro{5} después de cada entrada.",
    "TOOLTIP_SOURCE_IP_GROUP_NAME": "El nombre del grupo de direcciones IP de origen. Por ejemplo, Redes sociales. Agrupar las direcciones IP de origen facilita su referencia en las políticas de firewall.",
    "TOOLTIP_SOURCE_IP_NAME": "Nombre de IP de origen",
    "TOOLTIP_STATIC_LEASE": "Introduzca la dirección MAC y la dirección IP para DHCP.",
    "TOOLTIP_STATIC_ROUTE": "Introduzca los detalles de ruta y pasarela.",
    "TOOLTIP_SUBSCRIPTIONS": "Seleccione las suscripciones que desea agrupar.",
    "TOOLTIP_TEMPLATE_PREFIX": "Introduzca el prefijo para el nombre de la plantilla de ubicación.",
    "TOOLTIP_THROUGHPUT_ACROSS_DIRECT": "Uso medio del rendimiento del tráfico directo en kbps \n\nNúmero total de sesiones registradas por conector de sucursal/nube durante las últimas 24 horas",
    "TOOLTIP_THROUGHPUT_ACROSS_SERVICES": "Uso medio del rendimiento del tráfico en todos los conectores de sucursal/nube durante las últimas 24 horas",
    "TOOLTIP_THROUGHPUT_ACROSS_ZIA": "Uso medio del rendimiento del tráfico ZIA en kbps \n\nNúmero total de sesiones registradas por conector de sucursal/nube durante las últimas 24 horas",
    "TOOLTIP_THROUGHPUT_ACROSS_ZPA": "Uso medio del rendimiento del tráfico ZPA en kbps \n\nNúmero total de sesiones registradas por conector de sucursal/nube durante las últimas 24 horas",
    "TOOLTIP_TRAFFIC_DISTRIBUTION": "Seleccione Equilibrado o Mejor vínculo para determinar cómo debe distribuirse el tráfico.",
    "TOOLTIP_UPGRADE_WINDOW": "Las actualizaciones se realizarán de manera escalonada sin ningún impacto en el servicio",
    "TOOLTIP_USE_WAN_DNS_SERVER": "Seleccione Sí para utilizar el servidor DNS de WAN o No para introducir manualmente los detalles del servidor DNS de LAN. ",
    "TOOLTIP_VDI_AGENT_DESCRIPTION": "Introduzca información descriptiva que facilite la identificación del agente.",
    "TOOLTIP_VDI_AGENT_PROFILE_NAME": "Introduzca un nombre para el perfil de VDI que desea añadir.",
    "TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE": "Seleccione IdP o Base de datos alojada como tipo de autenticación.",
    "TOOLTIP_VDI_FORWRDING_PROFILE_IPS": "Utilice este filtro para limitar los registros según la dirección IP privada de un cliente. Puede introducir: {0}Una dirección IP, como ************** {1}Una dirección IP:rango de puertos, como *********:80 o ********* para todos los rangos {3}Una dirección IP:puerto:protocolo, como *********:80:TCP, *********:80-100:UDP o *********:80 para todos los protocolos {4}Haga clic en Intro después de cada entrada.",
    "TOOLTIP_VDI_GROUP_DESCRIPTION": "Introduzca información descriptiva para facilitar la identificación del grupo y su finalidad",
    "TOOLTIP_VDI_GROUP_NAME": "El nombre del grupo de VDI.",
    "TOOLTIP_VDI_HOSTNAME_PREFIX": "El prefijo del nombre de host utilizado para agrupar los dispositivos VDI. Este es el nombre de host detectado por Zscaler Client Connector for VDI.",
    "TOOLTIP_VDI_LOCATION": "La ubicación de Zscaler que se va a asociar a este grupo de VDI. Esta es la misma ubicación que la nube.",
    "TOOLTIP_VDI_MTU": "Este es el valor de la unidad máxima de transmisión (MTU) para los dispositivos del grupo de VDI. El valor predeterminado es 1400 bytes. Si este valor no se establece correctamente, podría afectar al rendimiento de red del agente VDI. Asegúrese de que agrupa los dispositivos con los mismos valores de MTU en un grupo de VDI.",
    "TOOLTIP_VDI_OS_TYPE": "El tipo de sistema operativo (SO) de los dispositivos de VDI que se van a asociar a este grupo de VDI. El tipo de SO detectado por Zscaler Client Connector for VDI se utilizará para añadir un dispositivo al grupo.",
    "TOOLTIP_VDI_TEMPLATE_IDP_NAME": "Busque o seleccione un nombre de IdP",
    "TOOLTIP_VDI_TEMPLATE_NAME": "Introduzca un nombre para la plantilla VDI.",
    "TOOLTIP_VDI_TEMPLATE_SYSTEM_USER": "Busque o seleccione el usuario del sistema.",
    "TOOLTIP_VDI_ZPA_USER_TUNNEL_FALLBACK": "Los conectores crean un túnel de usuario a ZPA para cada usuario de VDI, hasta el número especificado de túneles de usuario indicado aquí. Si el número de túneles de usuario ZPA de VDI de un conector supera este número, todas las transacciones posteriores se reenviarán a ZPA a través del túnel basado en conector. El usuario se reconocerá como el grupo de Cloud Connectors en este túnel basado en conectores.",
    "TOOLTIP_WAN_SELECTION": "La selección de WAN determina cómo se reenvía el tráfico a través de varios enlaces WAN. Cuando se establece con el valor Equilibrado, el tráfico se distribuye uniformemente. Cuando se establece con el valor Mejor enlace, el tráfico siempre se reenvía a través del enlace WAN de mejor rendimiento.",
    "TOOLTIP_ZIA_TUNNEL_MODEL": "El tipo de cifrado que se utilizará al crear el túnel hacia ZIA.",
    "TOPIC_STATUS": "Estado del tema",
    "TOTAL_CC_DEPLOYED": "Total de Cloud Connectors implementados",
    "TOTAL_DEPLOYED": "Total desplegado",
    "TOTAL_ENTITLED": "Total con derecho",
    "TOTAL_LATENCY": "Latencia total",
    "TOTAL_TRAFFIC": "Tráfico total",
    "TOTAL_TRANSACTIONS": "Total de transacciones",
    "TOTAL": "Total",
    "TRACE": "Traceroute",
    "TRACEROUTE_DESC": "Trace Route es una utilidad que indica el camino para llegar a un host específico",
    "TRACEROUTE": "Traceroute",
    "TRADING_BROKARAGE_INSURANCE": "Comercio online, corretaje, seguros",
    "TRADITIONAL_RELIGION": "Religión tradicional",
    "TRAFFIC_DIRECTON": "Tipo de solicitud",
    "TRAFFIC_DISTRIBUTION": "Distribución del tráfico",
    "TRAFFIC_FLOW": "Flujo de tráfico",
    "TRAFFIC_FORWARDING_METHOD": "Método de reenvío",
    "TRAFFIC_FORWARDING_RESOURCE": "Recurso de reenvío de tráfico",
    "TRAFFIC_FORWARDING": "Reenvio de tráfico",
    "TRAFFIC_FORWRDING_DNS": "Reenvío (tráfico, DNS y registros)",
    "TRAFFIC_MONITORING": "Supervisión del tráfico",
    "TRAFFIC_OVERVIEW": "Resumen del tráfico",
    "TRAFFIC_TEST": "Prueba de tráfico",
    "TRAFFIC_TREND": "Tendencia de tráfico",
    "TRAFFIC_TYPE": "Tipo de tráfico",
    "TRANSACTIONS": "Transacciones",
    "TRANSLATORS": "Otras Tecnologías de la Información",
    "TRAVEL": "Viajes",
    "TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN": "America/Port of Spain",
    "TRINIDAD_AND_TOBAGO": "Trinidad y Tobago",
    "TROUBLESHOOTING_LOGGING": "Solución de problemas de registro",
    "TRUE": "Verdadero",
    "TRUSTED_ACCOUNT_ID": "ID de cuenta de confianza",
    "TRUSTED_ROLE": "Rol de confianza",
    "TS_DIRECTON": "Dirección de TS",
    "TUESDAY": "Martes",
    "TUNISIA_AFRICA_TUNIS": "Africa/Túnez",
    "TUNISIA": "Túnez",
    "TUNNEL_AUTH_ALGORITHM": "Algoritmo de autenticación",
    "TUNNEL_AUTH_TYPE": "Tipo de autenticación",
    "TUNNEL_DEAD_PEER_DETECTION": "Paquetes Keep Alive",
    "TUNNEL_DESTINATION_IP_END": "IP de destino de política P2 - Fin",
    "TUNNEL_DESTINATION_IP_START": "IP de destino de política P2 - Inicio",
    "TUNNEL_DESTINATION_IP": "IP de destino de túnel",
    "TUNNEL_DESTINATION_PORT_END": "Puerto de destino de política P2 - Fin",
    "TUNNEL_ENCRYPTION_ALGORITHM": "Algoritmo de cifrado",
    "TUNNEL_EVENT_REASON": "Motivo del evento",
    "TUNNEL_INFORMATION": "Información del túnel",
    "TUNNEL_INITIATOR_COOKIE": "Cookie de iniciador",
    "TUNNEL_INSIGHTS": "Perspectivas de túnel",
    "TUNNEL_IP": "IP de túnel",
    "TUNNEL_IPSEC_PHASE2_SPI": "SPI de IKE Fase 2",
    "TUNNEL_LIFEBYTES": "Bytes en vivo",
    "TUNNEL_LIFETIME": "Vida útil de túnel",
    "TUNNEL_LOG_TYPE": "TIPO DE REGISTRO",
    "TUNNEL_LOGS": "Registros de túnel",
    "TUNNEL_POLICY_DIRECTION": "Dirección de política",
    "TUNNEL_PROTOCOL_NAME": "Protocolo de política P2",
    "TUNNEL_PROTOCOL": "Protocolo IPSec",
    "TUNNEL_RECEIVED_PACKETS": "Paquetes recibidos",
    "TUNNEL_RESPONDER_COOKIE": "Cookie de respondedor",
    "TUNNEL_SENT_PACKETS": "Paquetes enviados",
    "TUNNEL_SOURCE_IP_END": "IP de origen de política P2 - Fin",
    "TUNNEL_SOURCE_IP_START": "IP de origen de política P2 - Inicio",
    "TUNNEL_SOURCE_IP": "IP de origen de túnel",
    "TUNNEL_SOURCE_PORT_START": "Puerto de origen de política P2 - Inicio",
    "TUNNEL_STATUS": "Estado de túnel",
    "TUNNEL_TYPE": "Tipo de túnel",
    "TUNNEL_VENDOR_ID": "ID de proveedor",
    "TUNNEL_VPN_CREDENTIAL": "Credencial VPN",
    "TURKEY_EUROPE_ISTANBUL": "Europa/Estambul",
    "TURKEY": "Turquía",
    "TURKMENISTAN_ASIA_ASHGABAT": "Asia/Ashgabat",
    "TURKMENISTAN": "Turkmenistan",
    "TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK": "America/Grand Turk",
    "TURKS_AND_CAICOS_ISLANDS": "Turks and Caicos Islands",
    "TUVALU_PACIFIC_FUNAFUTI": "Pacific/Funafuti",
    "TUVALU": "Tuvalu",
    "TWO_CLOUD_CONNECTOR_TEMPLATE": "Plantilla complementaria con alta disponibilidad",
    "TX_BYTES": "Bytes enviados",
    "TX_PACKETS": "Paquetes enviados",
    "TX_RX_BYTES": " Bytes de TX | RX",
    "TX_RX_PACKETS": " Paquetes de TX | RX",
    "TYPE_ACCOUNT_ID": "Escriba el ID de la cuenta",
    "TYPE_ACCOUNT_NAME": "Escriba el nombre de la cuenta",
    "TYPE_APPLICATION_ID": "Escriba el ID de la aplicación",
    "TYPE_APPLICATION_KEY": "Escriba la clave de la aplicación",
    "TYPE_AWS_ACCESS_KEY_ID": "Escriba el ID de clave de acceso a AWS",
    "TYPE_AWS_SECRET_ACCESS_KEY": "Escriba la clave secreta de acceso a AWS",
    "TYPE_BASE_URL": "Escriba la URL base",
    "TYPE_CLIENT_SECRET": "Escriba el secreto de cliente",
    "TYPE_DESCRIPTION_HERE": "Escriba aquí la descripción",
    "TYPE_DOWNLOAD_MBPS": "Escriba la descarga (Mbps)",
    "TYPE_DSTN_IP_NAME": "Escriba el nombre de IP de destino",
    "TYPE_FEED_ESCAPE_CHARACTER": "Introducir texto",
    "TYPE_GATEWAY_NAME": "Escriba aquí el nombre de la pasarela",
    "TYPE_GROUP_NAME_HERE": "Escriba aquí el nombre del grupo",
    "TYPE_IP_ADDRESS_HERE": "Escriba aquí la dirección IP",
    "TYPE_IP_ADDRESSESS_HERE": "Escriba la dirección IP",
    "TYPE_IP_POOL_NAME": "Escriba el nombre del conjunto de IP",
    "TYPE_KEY": "Escribir clave",
    "TYPE_LOCATION_TEMPLATE_NAME": "Escriba el nombre de la plantilla de ubicación",
    "TYPE_NSS_CLOUD_FEED_API_URL": "Introducir URL de API",
    "TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Introduzca la URL de autorización",
    "TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Introduzca la identificación de la clave de acceso",
    "TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Introduzca la clave de acceso secreta",
    "TYPE_NSS_CLOUD_FEED_CLIENT_ID": "Introduzca el ID de cliente de la aplicación",
    "TYPE_NSS_CLOUD_FEED_CLIENT_SECRET": "Introduzca el secreto de cliente de la aplicación",
    "TYPE_NSS_CLOUD_FEED_GRANT_TYPE": "Introduzca client_credentials",
    "TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Introducir texto",
    "TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL": "Introduzca la URL de la carpeta S3",
    "TYPE_NSS_CLOUD_FEED_SCOPE": "Introduzca https://monitor.azure.com//.default",
    "TYPE_NSS_FEED_NAME": "Introducir texto",
    "TYPE_NSS_SERVER_NAME": "Introducir texto",
    "TYPE_SIEM_FQDN": "Introducir texto",
    "TYPE_SIEM_IP_ADDRESS": "Introducir texto",
    "TYPE_SIEM_RATE_LIMIT": "Introducir texto",
    "TYPE_SIEM_TCP_PORT": "Introducir texto",
    "TYPE_SOURCE_IP_NAME": "Escriba el nombre del grupo de IP de origen",
    "TYPE_SUBSCRIPTION_ID": "Escriba el ID de la suscripción",
    "TYPE_TEMPLATE_PREFIX": "Escriba el prefijo de plantilla",
    "TYPE_TENANT_ID": "Escriba el ID de inquilino",
    "TYPE_UPLOAD_MBPS": "Escriba la carga (Mbps)",
    "TYPE_VALUE": "Escribir valor",
    "TYPE_ZS_TAG_OPTIONAL": "Escriba la etiqueta ZS (opcional)",
    "TYPE": "Acción de Política",
    "UAE": "Emiratos Arabes Unidos",
    "UAECENTRAL": "(Oriente Medio) Centro de EAU",
    "UAENORTH": "(Oriente medio) Norte de EAU",
    "UBUNTU_LINUX": "ubuntu LINUX",
    "UDP_ANY_DESC": "El User Datagram Protocol (UDP) es uno de los miembros básicos de la suite de protocolos de Internet (el conjunto de protocolos de red usados en internet) ",
    "UDP_ANY": "UDP",
    "UDP_DESC": " El User Datagram Protocol (UDP) es uno de los miembros básicos de la suite de protocolos de Internet (el conjunto de protocolos de red usados en internet) ",
    "UDP_DEST_PORTS": "Puertos de destino UDP",
    "UDP_PORTS": "Puertos UDP",
    "UDP_SRC_PORTS": "Puertos de origen UDP",
    "UDP_UNKNOWN_DESC": " Identifica tráfico UDP de proxy/cortafuegos para el que no se puede determinar más información de la aplicación",
    "UDP_UNKNOWN": "UDP desconocido",
    "UDP": "UDP",
    "UGANDA_AFRICA_KAMPALA": "África / Kampala",
    "UGANDA": "Uganda",
    "UK": "Reino Unido",
    "UKRAINE_EUROPE_KIEV": "Europa/Kiev",
    "UKRAINE_EUROPE_SIMFEROPOL": "Europa/Simferopol",
    "UKRAINE_EUROPE_UZHGOROD": "Europa/Uzhgorod",
    "UKRAINE_EUROPE_ZAPOROZHYE": "Europa/Zaporozhye",
    "UKRAINE": "Ucrania",
    "UKSOUTH": "(Europa) Sur de Reino Unido",
    "UKWEST": "(Europa) Oeste de Reino Unido",
    "UNABLE_TO_LOGIN_TRY_AGAIN": "No es posible iniciar sesión, vuelva a intentarlo más tarde",
    "UNAUTHORIZED_COMMUNICATION": "Comunicación no autorizada",
    "UNENCRYPTED": "Sin cifrar",
    "UNEXPECTED_ERROR": "Ha ocurrido un error inesperado",
    "UNHEALTHY": "Mal estado",
    "UNITED_ARAB_EMIRATES_ASIA_DUBAI": "Asia/Dubai",
    "UNITED_ARAB_EMIRATES": "Emiratos Arabes Unidos",
    "UNITED_KINGDOM_EUROPE_LONDON": "Europa/Londres",
    "UNITED_KINGDOM": "Reino Unido",
    "UNITED_STATES_AMERICA_ADAK": "America/Adak",
    "UNITED_STATES_AMERICA_ANCHORAGE": "America/Anchorage",
    "UNITED_STATES_AMERICA_BOISE": "America/Boise",
    "UNITED_STATES_AMERICA_CHICAGO": "America/Chicago",
    "UNITED_STATES_AMERICA_DENVER": "America/Denver",
    "UNITED_STATES_AMERICA_DETROIT": "America/Detroit",
    "UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS": "America/Indiana/Indianapolis",
    "UNITED_STATES_AMERICA_INDIANA_KNOX": "America/Indiana/Knox",
    "UNITED_STATES_AMERICA_INDIANA_MARENGO": "America/Indiana/Marengo",
    "UNITED_STATES_AMERICA_INDIANA_PETERSBURG": "America/Indiana/Petersburg",
    "UNITED_STATES_AMERICA_INDIANA_TELL_CITY": "America/Indiana/Tell City",
    "UNITED_STATES_AMERICA_INDIANA_VEVAY": "America/Indiana/Vevay",
    "UNITED_STATES_AMERICA_INDIANA_VINCENNES": "America/Indiana/Vincennes",
    "UNITED_STATES_AMERICA_INDIANA_WINAMAC": "America/Indiana/Winamac",
    "UNITED_STATES_AMERICA_JUNEAU": "America/Juneau",
    "UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE": "America/Kentucky/Louisville",
    "UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO": "America/Kentucky/Monticello",
    "UNITED_STATES_AMERICA_LOS_ANGELES": "America/Los Angeles",
    "UNITED_STATES_AMERICA_MENOMINEE": "America/Menominee",
    "UNITED_STATES_AMERICA_NEW_YORK": "America/New York",
    "UNITED_STATES_AMERICA_NOME": "America/Nome",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER": "America/North Dakota/Center",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM": "America/North Dakota/New Salem",
    "UNITED_STATES_AMERICA_PHOENIX": "America/Phoenix",
    "UNITED_STATES_AMERICA_SHIPROCK": "America/Shiprock",
    "UNITED_STATES_AMERICA_YAKUTAT": "America/Yakutat",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON": "Pacific/Johnston",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY": "Pacific/Midway",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE": "Pacific/Wake",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS": "United States Minor Outlying Islands",
    "UNITED_STATES_PACIFIC_HONOLULU": "Pacific/Honolulu",
    "UNITED_STATES": "United States",
    "UNITEDSTATES": "United States",
    "UNITEDSTATESEUAP": "EUAP de Estados Unidos",
    "UNITS": "Unidades",
    "UNKNOWN_ERROR_CODE": "Código de error desconocido",
    "UNKNOWN_HOPS": "Saltos desconocidos",
    "UNLIMITED": "Ilimitado",
    "UNREGISTERED": "No registrado",
    "UNSELECTED_ITEMS": "Elementos no seleccionados",
    "UNSELECTED": "Sin seleccionar",
    "UNTAGGED": "Sin etiquetar",
    "UP": "Arriba",
    "UPDATE": "Actualizar",
    "UPDATED": "Actualizada",
    "UPF_IP_CIDR": "Función de plano de usuario IP/CIDR",
    "UPF_NAME": "Nombre de la función de plano de usuario",
    "UPGRADE_ON": "Actualizar el",
    "UPGRADE_SCHEDULE": "Programación de actualización",
    "UPGRADE_STATUS": "Estado de actualización",
    "UPGRADE_WILL_BE_SCHEDULED": "Las actualizaciones se programarán conforme a la zona horaria local del conector de nube",
    "UPGRADE_WINDOW": "Ventana de actualización",
    "UPLINK_MODE": "Modo de enlace ascendente",
    "UPLOAD_MBPS": "Subida (Mbps)",
    "UPLOAD": "Cargar",
    "UPTIME": "Tiempo de actividad",
    "URL_LOOKUP": "Búsqueda de URL",
    "URUGUAY_AMERICA_MONTEVIDEO": "America/Montevideo",
    "URUGUAY": "Uruguay",
    "US_CENTRAL1_A": "us-central1-a",
    "US_CENTRAL1_B": "us-central1-b",
    "US_CENTRAL1_C": "us-central1-c",
    "US_CENTRAL1_F": "us-central1-f",
    "US_CENTRAL1": "us-central1",
    "US_EAST_1": "us-east-1 (N. Virginia)",
    "US_EAST_1A": "us-east-1a",
    "US_EAST_1B": "us-east-1b",
    "US_EAST_1C": "us-east-1c",
    "US_EAST_1D": "us-east-1d",
    "US_EAST_1E": "us-east-1e",
    "US_EAST_1F": "us-east-1f",
    "US_EAST_2": "us-east-2 (Ohio)",
    "US_EAST_2A": "us-east-2a",
    "US_EAST_2B": "us-east-2b",
    "US_EAST_2C": "us-east-2c",
    "US_EAST1_B": "us-east1-b",
    "US_EAST1_C": "us-east1-c",
    "US_EAST1_D": "us-east1-d",
    "US_EAST1": "us-east1",
    "US_EAST4_A": "us-east4-a",
    "US_EAST4_B": "us-east4-b",
    "US_EAST4_C": "us-east4-c",
    "US_EAST4": "us-east4",
    "US_EAST5_A": "us-east5-a",
    "US_EAST5_B": "us-east5-b",
    "US_EAST5_C": "us-east5-c",
    "US_EAST5": "us-east5",
    "US_GOV_EAST_1": "AWS GovCloud (Este de EE. UU.)",
    "US_GOV_EAST_1A": "us-gov-east-1a",
    "US_GOV_EAST_1B": "us-gov-east-1b",
    "US_GOV_EAST_1C": "us-gov-east-1c",
    "US_GOV_WEST_1": "AWS GovCloud (Oeste de EE. UU.)",
    "US_GOV_WEST_1A": "us-gov-west-1a",
    "US_GOV_WEST_1B": "us-gov-west-1b",
    "US_GOV_WEST_1C": "us-gov-west-1c",
    "US_OUTLYING_ISLANDS": "Islas Ultramarinas de EE. UU.",
    "US_SOUTH1_A": "us-south1-a",
    "US_SOUTH1_B": "us-south1-b",
    "US_SOUTH1_C": "us-south1-c",
    "US_SOUTH1": "us-south1",
    "US_VIRGIN_ISLANDS": "Islas Vírgenes de EE. UU.",
    "US_WEST_1": "us-west-1 (N. California)",
    "US_WEST_1A": "us-west-1a",
    "US_WEST_1B": "us-west-1b",
    "US_WEST_1C": "us-west-1c",
    "US_WEST_2_LAX_1A": "us-west-2-lax-1a",
    "US_WEST_2": "us-west-2 (Oregón)",
    "US_WEST_2A": "us-west-2a",
    "US_WEST_2B": "us-west-2b",
    "US_WEST_2C": "us-west-2c",
    "US_WEST_2D": "us-west-2d",
    "US_WEST1_A": "us-west1-a",
    "US_WEST1_B": "us-west1-b",
    "US_WEST1_C": "us-west1-c",
    "US_WEST1": "us-west1",
    "US_WEST2_A": "us-west2-a",
    "US_WEST2_B": "us-west2-b",
    "US_WEST2_C": "us-west2-c",
    "US_WEST2": "us-west2",
    "US_WEST3_A": "us-west3-a",
    "US_WEST3_B": "us-west3-b",
    "US_WEST3_C": "us-west3-c",
    "US_WEST3": "us-west3",
    "US_WEST4_A": "us-west4-a",
    "US_WEST4_B": "us-west4-b",
    "US_WEST4_C": "us-west4-c",
    "US_WEST4": "us-west4",
    "USDODCENTRAL": "US DoD Central",
    "USDODEAST": "US DoD East",
    "USE_WAN_DNS_SERVER": "Usar el servidor DNS de WAN",
    "USE_WAN_DNS_SEVER": "Usar el servidor DNS de WAN",
    "USER_ACCOUNT_LOCKED": "Su cuenta se ha bloqueado temporalmente debido a que se han producido demasiados intentos de inicio de sesión fallidos. Vuelva a intentarlo más tarde.",
    "USER_DEFINED_TAGS": "Identificadores definidos por el usuario",
    "USER_DEFINED": "Definido por el usuario",
    "USER_ID": "ID de usuario",
    "USER_MANAGEMENT": "Gestión de usuario",
    "USER_NAME_VISIBILITY": "Visibilidad del nombre del usuario",
    "USER_NAME": "Nombre de usuario mostrado",
    "USER_NAMES": "Nombres de usuario",
    "USER_PLANE_FUNCTION": "Función de plano de usuario",
    "USER": "Usuario",
    "USERNAMES": "Nombres de usuario",
    "USGOVARIZONA": "USGov Arizona",
    "USGOVIOWA": "USGov Iowa",
    "USGOVTEXAS": "USGov Texas",
    "USGOVVIRGINIA": "USGov Virginia",
    "USSECEAST": "Este de USSec",
    "USSECWEST": "Oeste de USSec",
    "USSECWESTCENTRAL": "Centro-oeste de USSec",
    "UZBEKISTAN_ASIA_SAMARKAND": "Asia/Samarkand",
    "UZBEKISTAN_ASIA_TASHKENT": "Asia/Tashkent",
    "UZBEKISTAN": "Uzbekistan",
    "VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE": "Se permiten un máximo de {0} elementos.",
    "VALIDATION_ERROR_EMPTY_PROTOCOL": "Introduzca un protocolo.",
    "VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS": "Uno o más elementos de esta lista no son válidos.",
    "VALIDATION_ERROR_INVALID_AWS_ROLENAME": "Introduzca un nombre de rol de AWS válido. Solo debe contener caracteres alfanuméricos y '+=,.@-' .",
    "VALIDATION_ERROR_INVALID_DOMAIN": "Introduzca un nombre de dominio válido.",
    "VALIDATION_ERROR_INVALID_END_PORT_RANGE": "El puerto final debe estar entre 1 y 65535 y ser mayor que el puerto de inicio.",
    "VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS": "Introduzca un FQDN, una dirección IP, un rango de direcciones IP o un bloque CIDR de IP válidos.",
    "VALIDATION_ERROR_INVALID_IP_ADDRESS": "Introduzca una dirección IP, un rango de direcciones IP o un bloque CIDR de IP válidos.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK_IS_BROADCAST": "Introduzca una IP válida, esta es una IP de difusión para la subred.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK": "Asegúrese de que las direcciones IP estén en la misma subred.",
    "VALIDATION_ERROR_INVALID_IP_PORT": "Introduzca un puerto TCP válido (0-65535)",
    "VALIDATION_ERROR_INVALID_IP_WITH_CIDR": "Introduzca una dirección IP válida con CIDR.",
    "VALIDATION_ERROR_INVALID_IP": "Introduzca una IP válida",
    "VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS": "Introduzca una dirección URL válida sin prefijo http:// o https://. La URL debe tener al menos el patrón host.domain para calificar.",
    "VALIDATION_ERROR_INVALID_MAC_ADDRESS": "Introduzca una dirección MAC válida.",
    "VALIDATION_ERROR_INVALID_NAME": "Introduzca un nombre válido",
    "VALIDATION_ERROR_INVALID_PORT_STRING": "Introduzca un número de puerto o rango de números de puerto válido (por ejemplo, 587, 1-65535).",
    "VALIDATION_ERROR_INVALID_PROTOCOL": "Introduzca un protocolo válido.",
    "VALIDATION_ERROR_INVALID_SECONDARY_FIELD": "¡El campo secundario (Manual) no puede estar vacío!",
    "VALIDATION_ERROR_INVALID_SERVICE_IP_MASK": "Todas las IP de servicio deben tener la misma máscara de subred.",
    "VALIDATION_ERROR_INVALID_START_PORT_RANGE": "Los números de puerto deben estar entre 1 y 65535.",
    "VALIDATION_ERROR_INVALID_URL": "Introduzca una dirección URL válida con prefijo http:// o https://.",
    "VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE": "Esta IP se encuentra dentro de uno de los siguientes rangos no válidos. \n\n [0.0.0.0-*************]\n [**********-***************]\n [*********-***************]\n [***********-***************]\n [240.0.0.0-***************] ",
    "VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE": "Este número debe estar entre 128 KB y 1 MB",
    "VALIDATION_ERROR_SAME_IP": "Las direcciones IP deben ser diferentes.",
    "VALIDATION_ERROR_SAME_SERVICE_IP": "Los IP de servicio deben ser diferentes.",
    "VALIDATION_ERROR_SAME_START_END_PORT_RANGE": "El puerto inicial y el final no pueden ser iguales.",
    "VALIDATION_NETWORK_SERVICE_GROUP_NAME_REQUIRED": "El nombre del grupo de servicios de red no puede estar vacío.",
    "VALIDATION_NETWORK_SERVICE_GROUP_SERIVCE_REQUIRED": "Debe especificarse al menos un tipo de servicio.",
    "VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT": "Introduzca al menos un puerto de destino TCP o UDP. Los puertos de origen requieren un puerto de destino correspondiente.",
    "VALIDATION_NETWORK_SERVICE_MIN_PORT_REQUIRED": "Debe especificarse al menos un tipo de puerto.",
    "VALIDATION_NETWORK_SERVICE_NAME_REQUIRED": "El nombre del servicio de red no puede estar vacío.",
    "VALUE": "Valor",
    "VANUATU_PACIFIC_EFATE": "Pacific/Efate",
    "VANUATU": "Vanuatu",
    "VATICAN_CITY_STATE_EUROPE_VATICAN": "Europa/Vaticano",
    "VATICAN_CITY_STATE": "Estado de la Ciudad del Vaticano",
    "VATICAN_CITY": "Ciudad del Vaticano",
    "VDI_AGENT_APP": "Tienda de aplicaciones de VDI",
    "VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT": "Texto de marcador de posición poco detallado...",
    "VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT": "Zscaler Client Connector for VDI utiliza esta configuración para incluir o excluir tráfico del túnel a Cloud o Branch Connector. En algunos casos, las listas de inclusión y exclusión también deben configurarse en su Cloud o Branch Connector.",
    "VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT": "Introduzca texto separado por un retorno.",
    "VDI_AGENT_FORWARDING_PROFILE": "Perfil de reenvío de VDI",
    "VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT": "Esta configuración se utiliza para la autenticación del usuario final y el ID de usuario asociado al usuario del sistema.",
    "VDI_AGENT_TEMPLATE": "Plantilla de VDI",
    "VDI_AGENT_TEMPLATES": "Plantillas de VDI",
    "VDI_DEVICE_GENERAL_TEXT": "Introduzca información que le ayude a identificar un grupo específico.",
    "VDI_DEVICE_MANAGEMENT": "Gestión de dispositivo VDI",
    "VDI_DEVICES": "Dispositivos VDI",
    "VDI_FORWARDING_PROFILE_TEXT": "El perfil de reenvío que se va a asociar a este grupo de VDI. Las políticas de inclusión o exclusión del perfil de reenvío elegido serán aplicadas por Zscaler Client Connector for VDI e instaladas en los dispositivos incluidos en este grupo de VDI.",
    "VDI_FORWARDING_PROFILE": "Perfil de reenvío de VDI",
    "VDI_GROUPS": "Grupos de VDI",
    "VDI_MANAGEMENT": "Gestión de VDI",
    "VDI_REVIEW_TEXT": "Asegúrese de que toda la información indicada debajo es correcta antes de añadir este grupo.",
    "VDI_ZPA_USER_TUNNEL_FALLBACK": "Reserva de túnel de usuario de ZPA de VDI",
    "VDO_LIVE_DESC": "VDOLive es una tecnología escalable de streaming de vídeo ",
    "VDO_LIVE": "VDOLive",
    "VEHICLES": "Vehículos",
    "VENDOR": "Proveedor",
    "VENEZUELA_AMERICA_CARACAS": "America/Caracas",
    "VENEZUELA": "Venezuela",
    "VERBOSE": "Detalles adicionales",
    "VERIFY_CURRENT_PASSWORD": "Verificar la contraseña actual",
    "VERSION_PROFILE": "Perfil de versión",
    "VERSION": "Versión",
    "VIET_NAM_ASIA_SAIGON": "Asia/Saigon",
    "VIET_NAM": "Viet_nam",
    "VIETNAM": "Vietnam",
    "VIEW_5G_DEPLOYMENT": "Ver configuración de implementación",
    "VIEW_APPLIANCE": "Ver dispositivo",
    "VIEW_AWS_ACCOUNT": "Ver cuenta de AWS",
    "VIEW_AWS_GROUP": "Ver grupo de AWS",
    "VIEW_AZURE_TENANT": "Ver inquilino de Azure",
    "VIEW_BRANCH_PROVISIONING_TEMPLATE": "Ver plantilla de aprovisionamiento de conector de sucursal",
    "VIEW_CLOUD_CONNECTOR_ADMIN": "Ver administrador de Cloud Connector",
    "VIEW_CLOUD_CONNECTOR_ROLE": "Ver rol de Cloud Connector",
    "VIEW_CLOUD_CONNECTORS": "Ver conectores",
    "VIEW_CLOUD_NSS_FEED": "Ver feed de NSS de nube",
    "VIEW_CLOUD_PROVIDER_AWS": "Ver cuenta de nube de AWS",
    "VIEW_CLOUD_PROVIDER_AZURE": "Ver cuenta de nube de Azure",
    "VIEW_CONNECTORS": "Ver conectores",
    "VIEW_DESTINATION_IP_GROUP": "Ver grupo de IP de destino",
    "VIEW_DNS_GATEWAYS": "Ver pasarela DNS",
    "VIEW_DNS_POLICIES": "Ver regla de filtrado de DNS",
    "VIEW_DYNAMIC_VDI_GROUP": "Ver grupo de VDI dinámico",
    "VIEW_GATEWAYS": "Ver pasarelas",
    "VIEW_INFO": "Ver información",
    "VIEW_IP_POOL_GROUP": "Ver grupo de IP",
    "VIEW_LOCATION_TEMPLATE": "Ver plantilla de ubicación",
    "VIEW_LOCATIONS": "Ver ubicaciones",
    "VIEW_LOG_AND_CONTROL_FORWARDING_RULE": "Ver regla de reenvío de registro y control",
    "VIEW_NETWORK_SERVICE_GROUP": "Ver grupo de servicios de red",
    "VIEW_NETWORK_SERVICE": "Ver servicio de red",
    "VIEW_NSS_FEEDS": "Ver feed de NSS",
    "VIEW_NSS_SERVER": "Ver servidor NSS",
    "VIEW_ONLY_ACCESS": "Acceso solo para visualización",
    "VIEW_ONLY_ENABLED_UNTIL": "Acceso solo para visualización activado hasta",
    "VIEW_ONLY": "Sólo Ver",
    "VIEW_PHYSICAL_BRANCH_DEVICE": "Ver dispositivo de sucursal físico",
    "VIEW_PROVISIONING_TEMPLATE": "Ver plantilla de aprovisionamiento de Cloud Connector",
    "VIEW_SOURCE_IP_GROUP": "Ver grupo de IP de origen",
    "VIEW_SUB_LOCATIONS": "Ver sububicaciones",
    "VIEW_SUBLOCATIONS": "Ver sububicaciones",
    "VIEW_TRAFFIC_FWD_POLICIES": "Ver reglas de reenvío de tráfico",
    "VIEW_UPF": "Ver función de plano de usuario",
    "VIEW_VDI_AGENT_FORWARDING_PROFILE": "Ver perfil de reenvío de VDI",
    "VIEW_VDI_TEMPLATE": "Ver plantilla VDI",
    "VIEW_VIRTUAL_BRANCH_DEVICE": "Ver dispositivo de sucursal virtual",
    "VIEW_ZERO_TRUST_GATEWAY": "Ver pasarela de Zero Trust",
    "VIEW_ZT_DEVICE": "Ver dispositivo ZT",
    "VIEW": "Vista",
    "VIOLENCE": "Violencia",
    "VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA": "America/Tortola",
    "VIRGIN_ISLANDS_BRITISH": "Virgin Islands (British)",
    "VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS": "America/St. Thomas",
    "VIRGIN_ISLANDS_US": "Virgin Islands (U.S.)",
    "VIRTUAL_IP_ADDRESS": "Dirección IP virtual",
    "VIRTUAL_IP_AND_LAN_IP_MUST_BE_DIFFERENT": "La IP virtual y la dirección IP de LAN deben ser diferentes.",
    "VIRTUAL_IP_AND_PEER_DHCP_MUST_BE_DIFFERENT": "La IP virtual y la dirección DHCP de par deben ser diferentes.",
    "VIRTUAL_SERVICE_EDGE_ID": "ID de Virtual Service Edge",
    "VIRTUAL": "Virtual",
    "VISIBLE": "Visible",
    "VLAN_ID": "ID de VLAN",
    "VM_GBL_METRICS": "VM",
    "VM_HEALTH_FETCH_API_ERROR": "No se puede recuperar la salud de la VM en estos momentos. Vuelva a intentarlo más tarde.",
    "VM_ID": "ID de VM",
    "VM_NAME": "Nombre de la VM",
    "VM_SIZE": "Tamaño de VM",
    "VMWARE_ESXI": "VMware ESXi",
    "VMWARE": "VMWare",
    "VNC_DESC": "Virtual Network Computing",
    "VNC": "VNC",
    "VPC_ID": "ID de VPC",
    "VPC_NAME": "Nombre de VPC",
    "VPC_VNET_NAME": "Nombre de VPC/VNET",
    "VPN_CREDENTIAL_DROPDOWN": "Lista desplegable de credencial VPN",
    "VPN_CREDENTIAL": "Credencial VPN",
    "VPN_CREDENTIALS": "Credenciales VPN",
    "VSE_CLUSTERS": "Clústeres de perímetros de servicio virtuales",
    "VSE_NODES": "Perímetros de servicio virtuales",
    "WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS": "Pacific/Wallis",
    "WALLIS_AND_FUTUNA_ISLANDS": "Wallis and Futuna Islands",
    "WALLIS_AND_FUTUNA": "Wallis and Futuna",
    "WAN_DESTINATIONS_GROUP": "Grupo de destinos de WAN",
    "WAN_NEED_AT_LEAST_ONE_ACTIVE_INTERFACE": "La WAN necesita al menos una interfaz activa.",
    "WAN_PRI_DNS": "Servidor DNS primario de WAN",
    "WAN_SEC_DNS": "Servidor DNS secundario de WAN",
    "WAN_SELECTION": "Selección de WAN",
    "WAN": "WAN",
    "WEAPONS_AND_BOMBS": "Armas/Bombas",
    "WEB_BANNERS": "Publicidad",
    "WEB_CONFERENCING": "Conferencia Web",
    "WEB_HOST": "Web Host",
    "WEB_SEARCH": "Búsqueda de Web",
    "WEB_SPAM": "Spam de web",
    "WEDNESDAY": "Miércoles",
    "WESTCENTRALUS": "(EE. UU.) Centro Oeste de EE. UU.",
    "WESTERN_SAHARA_AFRICA_EL_AAIUN": "Africa/El Aaiun",
    "WESTERN_SAHARA": "Sáhara Occidental",
    "WESTEUROPE": "(Europa) Oeste de Europa",
    "WESTINDIA": "(Asia Pacífico) Oeste de India",
    "WESTUS": "(EE. UU.) Oeste de EE. UU.",
    "WESTUS2": "(EE. UU.) Oeste de EE. UU. 2",
    "WESTUS2STAGE": "(EE. UU.) Oeste de EE. UU. 2 (Stage)",
    "WESTUS3": "(Estados Unidos) Oeste de EE. UU. 3",
    "WESTUSSTAGE": "(EE. UU.) Oeste de EE. UU. (Stage)",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_PLACEHOLDER": "Introducir ID de cliente/aplicación",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_TOOLTIP": "Introduzca el ID de cliente/aplicación.",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS": "ID de cliente/aplicación",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_PLACEHOLDER": "Introducir secreto de cliente/aplicación",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_TOOLTIP": "Introduzca el secreto de cliente/aplicación.",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET": "Secreto de cliente/aplicación",
    "WHAT_DO_YOU_CALL_THIS_TENANT_PLACEHOLDER": "Introducir nombre para la cuenta de Azure",
    "WHAT_DO_YOU_CALL_THIS_TENANT_TOOLTIP": "Escriba un nombre que se utilice para administrar sus cuentas.",
    "WHAT_DO_YOU_CALL_THIS_TENANT": "Nombre de la cuenta",
    "WHAT_IS_THE_DIRECTORY_ID_PLACEHOLDER": "Introducir ID de directorio",
    "WHAT_IS_THE_DIRECTORY_ID_TOOLTIP": "Escriba el ID de directorio (inquilino) asociado a la entidad de servicio de Azure que se usa para incorporar la cuenta de Azure.",
    "WHAT_IS_THE_DIRECTORY_ID": "ID de directorio (inquilino)",
    "WHITESPACES_ARE_NOT_ALLOWED": "¡No se permiten espacios en blanco!",
    "WHOIS_DESC": "Protocolo de servicio de directorio de red (Network Directory Service Protocol)",
    "WHOIS": "WHOIS",
    "WINDOWS_OS": "Sistema operativo Windows",
    "WORKLOAD_SERVICE_REQUIRED_MESSAGE": "Las integraciones de socios requieren una suscripción que su organización no tiene actualmente. Póngase en contacto con el soporte de Zscaler para activar el plan básico sin coste adicional. Las integraciones de socios permiten que Zscaler ejecute y mantenga un servicio en la nube pública, adjunto a su cuenta o suscripción. Zscaler asignará su cuenta a un servicio en función en nuestra capacidad.",
    "WORKLOAD_SERVICE_REQUIRED": "Es necesario configurar Workload Service",
    "WORKLOAD": "Tipo de tráfico de carga de trabajo",
    "WORKLOADS": "Cargas de trabajo",
    "XSS": "Cross-site Scripting",
    "YEMEN_ASIA_ADEN": "Asia/Aden",
    "YEMEN": "Yemen",
    "YES": "Sí",
    "YESKY_DESC": " Este plug-in de protocolo clasifica el tráfico http al host yesky.com",
    "YESKY": "Yesky",
    "YIHAODIAN_DESC": " Compras online chinas",
    "YIHAODIAN": "Yihaodian",
    "YMAIL_CLASSIC_DESC": " Yahoo Mail Classic fue la interfaz original del Yahoo! Mail",
    "YMAIL_CLASSIC": "Yahoo Mail Classic",
    "YMAIL_MOBILE_DESC": "  (Obsoleto) Yahoo Mail (Mobile) es el webmail de yahoo.com adaptado a móviles ",
    "YMAIL_MOBILE_NEW_DESC": " Yahoo Mail (Mobile) es el webmail de yahoo.com adaptado a móviles",
    "YMAIL_MOBILE_NEW": "Yahoo Mail (Mobile)",
    "YMAIL_MOBILE": "Ymail (Mobile)",
    "YMAIL2_DESC": " Este protocolo es la versión basada en ajax de Webmail Yahoo",
    "YMAIL2": "Ymail2",
    "YMSG_CONF_DESC": " Este protocolo se utiliza en la parte de señalización de una conferencia",
    "YMSG_CONF": "Conferencia de Yahoo Messenger",
    "YMSG_DESC": "  Yahoo Messenger se utiliza por la aplicación Yahoo Instant Messenger para enviar mensajes instantáneos, archivos y correos entre usuarios ",
    "YMSG_TRANSFER_DESC": " Este protocolo se usa para transferencia de archivos sobre ymsg",
    "YMSG_TRANSFER": "Transferencia de Archivos de Yahoo Messenger",
    "YMSG_VIDEO_DESC": "  (versiones anteriores a 10.0.0.270) Este protocolo se usa por Yahoo Messenger para conversaciones de video ",
    "YMSG_VIDEO": "Video de Yahoo Messenger",
    "YMSG_WEBMESSENGER_DESC": " Yahoo Messenger para la web",
    "YMSG_WEBMESSENGER": "Yahoo Messenger para la web",
    "YMSG": "Yahoo Messenger",
    "YOU_DO_NOT_HAVE_THE_NECESSARY_PERMISSION": "No tiene los permisos necesarios para ver esta página",
    "YOUR_ACCOUNT_INFORMATION_WAS_SAVED_BUT_SOME_REGIONS_FAILED": "Se ha guardado la información de su cuenta, pero las siguientes regiones no se han guardado:",
    "ZAMBIA_AFRICA_LUSAKA": "Africa/Lusaka",
    "ZAMBIA": "Zambia",
    "ZDX_UI": "Interfaz de usuario de ZDX",
    "ZERO_TRUST_GATEWAY": "Pasarela de Zero Trust",
    "ZIA_GATEWAY": "Pasarela de ZIA",
    "ZIA_GW_AUTH_FAIL": "No se ha podido autentificar con la pasarela ZIA.",
    "ZIA_GW_CONN_SETUP_FAIL": "Fallo en la configuración de conexión de pasarela de ZIA (error interno).",
    "ZIA_GW_CONNECT_FAIL": "Fallo en la conexión de pasarela de ZIA (error de red).",
    "ZIA_GW_CTL_CONN_CLOSE": "Se ha cerrado la conexión de control activa de pasarela de ZIA.",
    "ZIA_GW_CTL_KA_FAIL": "Fallo en keepalive de conexión de control de pasarela de ZIA.",
    "ZIA_GW_DATA_CONN_CLOSE": "Se ha cerrado la conexión de datos activa de pasarela de ZIA.",
    "ZIA_GW_DATA_KA_FAIL": "Fallo en keepalive de conexión de datos de pasarela de ZIA.",
    "ZIA_GW_DNS_RESOLVE_FAIL": "Fallo en la resolución de DNS de pasarela de ZIA.",
    "ZIA_GW_PAC_RESOLVE_FAIL": "Fallo en la resolución de PAC de pasarela de ZIA.",
    "ZIA_GW_PAC_RESOLVE_NOIP": "La resolución de PAC de pasarela de ZIA no ha devuelto ningún IPS.",
    "ZIA_GW_PROTO_MSG_ERROR": "Error de formato de mensaje en el canal de control/datos de pasarela de ZIA.",
    "ZIA_GW_PROTO_VER_ERROR": "Discrepancia de versión del protocolo ZIA.",
    "ZIA_GW_SSL_ERROR": "Error SSL en el canal de control/datos de pasarela de ZIA.",
    "ZIA_GW_UNHEALTHY": "La pasarela de ZIA no está en buen estado (estado transitorio).",
    "ZIA_THROUGHPUT_KBPS_SESSION": "ZIA (velocidad de kbps / sesión)",
    "ZIA_TUNNEL_MODEL": "Modo de túnel de ZIA",
    "ZIA_TUNNEL": "Túnel ZIA",
    "ZIA": "ZIA",
    "ZIMBABWE_AFRICA_HARARE": "Africa/Harare",
    "ZIMBABWE": "Zimbabwe",
    "ZONE": "Zona",
    "ZPA_BROKER": "Agente de ZPA",
    "ZPA_EDGE_APP_SEGMENT": "Segmento de la aplicación ZPA Edge",
    "ZPA_IP_POOL": "Conjunto de IP de ZPA",
    "ZPA_POLICY_VIOLATION_INDICATOR": "Indicador de infracción de política de ZPA",
    "ZPA_THROUGHPUT_KBPS_SESSION": "ZPA (velocidad de kbps / sesión)",
    "ZPA_TUNNEL": "Túnel ZPA",
    "ZPA": "ZPA",
    "ZS_TAG_OPTIONAL": "Etiqueta ZS (opcional)",
    "ZSCALER_ANALYZER": "Zscaler Analyzer",
    "ZSCALER_CLOUD_ENDPOINTS": "Puntos de conexión de Zscaler Cloud",
    "ZSCALER_DOMAINS": "Dominios de Zscaler",
    "ZSCALER_ESTABLISH_SUPPORT_TUNNEL": "Túnel de soporte bajo demanda iniciado por Zscaler",
    "ZSCALER_GATEWAY_DETAILS": "Detalles de pasarela de Zscaler",
    "ZSCALER_HELP_PORTAL": "Portal de ayuda de Zscaler",
    "ZSCALER_INC_ALL_RIGHTS_RESERVED": "Zscaler Inc. Todos los derechos reservados.",
    "ZSCALER_INTERFACE_NAME": "Nombre de interfaz de Zscaler",
    "ZSCALER_IP": "IP de Zscaler",
    "ZSCALER_IPS": "IP(s) de Zscaler",
    "ZSCALER_PROXY_NW_SERVICES_DESC": "Este servicio de red incluye todos los puertos de proxy web específicos de Zscaler, incluidos los puertos DPPC específicos del cliente.",
    "ZSCALER_PROXY_NW_SERVICES": "Servicios de red de proxy de Zscaler",
    "ZSLOGIN_ADMINISTRATION": "Administración de ZIdentity",
    "ZSPROXY_IPS": "IP de proxy de Zscaler",
    "ZT_DEVICES": "Dispositivos ZT",
    "ZT_GATEWAY": "Pasarela de Zero Trust",
    "ZTG_ACCOUNT_TEXT": "La pasarela acepta solicitudes entrantes de puntos de conexión de la lista de cuentas introducida. Se pueden seleccionar las cuentas y los grupos de cuentas de AWS incorporados en la página Integraciones de partner. En el caso de cuentas no incorporadas mediante la página Integraciones de partner, introduzca manualmente el ID de cuenta de AWS de 12 dígitos. Para obtener más información sobre Integraciones de partner, consulte la {0}documentación de Integraciones de partner de Cloud Connector{1}.",
    "ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP": "Si su cuenta de AWS no se ha incorporado en la página Integraciones de partner, introduzca su ID de cuenta de AWS de 12 dígitos. La pasarela acepta las solicitudes originadas por puntos de conexión de la misma región que la pasarela.",
    "ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP": "Seleccione los grupos de cuentas de AWS que están autorizados a conectar con esta pasarela. La pasarela acepta las solicitudes originadas por puntos de conexión de la misma región que la pasarela. Los grupos de cuentas se crean en la página Integraciones de partner.",
    "ZTG_ALLOWED_ACCOUNTS_TOOLTIP": "Seleccione las cuentas de AWS que están autorizadas a conectar con esta pasarela. La pasarela acepta las solicitudes originadas por puntos de conexión de la misma región que la pasarela. Las cuentas se añaden en la página Integraciones de partner.",
    "ZTG_AVIABILITY_ZONE_TOOLTIP": "Seleccione la zona o zonas de disponibilidad en las que se crean los componentes de la pasarela. Se debe elegir un mínimo de dos zonas de disponibilidad. Tenga en cuenta que estos son los ID de zona de disponibilidad de AWS, no los nombres que se muestran en la cuenta de AWS. Los ID se muestran porque el mismo nombre de zona de disponibilidad se puede asignar a diferentes ID de zona de disponibilidad en diferentes cuentas de AWS. Para obtener más información sobre la asignación de nombres de zonas de disponibilidad a ID y cómo encontrar el ID de zona de disponibilidad de su cuenta, consulte la {0}documentación de AWS{1}.",
    "ZTG_CONFIGURATION_TEXT": "Introduzca la configuración de esta pasarela. Esta configuración controla la región y las zonas de disponibilidad de AWS en las que está disponible el servicio.",
    "ZTG_ID": "ID de pasarela de Zero Trust",
    "ZTG_LOCATION_TEMPLATE_TOOLTIP": "Seleccione la plantilla de ubicación utilizada para crear la ubicación asociada a la pasarela.",
    "ZTG_LOCATION_TOOLTIP": "Introduzca el nombre de la ubicación que se asociará a esta pasarela. Este nombre es visible en todas las políticas en las que hay disponible un objeto de ubicación.",
    "ZTG_NAME_TOOLTIP": "Introduzca un nombre para su pasarela. Este nombre está asociado a la pasarela.",
    "ZTG_REGION_TOOLTIP": "Seleccione la región en la que se crea la pasarela. Las pasarelas son regionales y solo se pueden implementar en una región. La región no se puede modificar después de crear la pasarela.",
    "ZTG_REVIEW_TEXT": "Asegúrese de que toda la información sea correcta antes de crear la pasarela. Una vez creada la pasarela, la implementación de los componentes puede tardar unos minutos. Visite la página de la pasarela de Zero Trust para ver el estado de la pasarela.",
    "ZTGW_GROUP": "Grupo de pasarelas de Zero Trust",
    "ZTGW_VM": "VM de pasarela de Zero Trust",
    "ZULU": "Zulú",
  },
};
