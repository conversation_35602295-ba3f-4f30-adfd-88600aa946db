/* eslint-disable quotes */
/* eslint-disable quote-props */

// Notes: We can add phrases as a key, it does not have to be a key/constant.
// However, use key and constant if the phrase is a long sentence or paragraph.

// Please keep the translations below sorted for easy access and to avoid duplicates

// WIP - will keep this changes until we complete the csv2json conversion

export default {
  translation: {
    "1_HOUR": "1 heure",
    "1_MONTH": "1 mois",
    "1_WEEK": "1 semaine",
    "24_HOURS": "24 heures",
    "4_HOURS": "4 heures",
    "ACCEPT": "Accepter",
    "ACCEPTED_ON": "Accepté le",
    "ACCESS_TOKEN": "Jeton d'accès",
    "ACCOUNT_GROUP": "Groupe de comptes",
    "ACCOUNT_ID_ONLY": "Identifiant de compte",
    "ACCOUNT_ID": "Identifiant de compte AWS",
    "ACCOUNT_LIST": "Liste des comptes",
    "ACCOUNT_NAME": "Nom du compte",
    "ACCOUNT": "Compte",
    "ACCOUNTS": "Comptes",
    "ACTION_CAN_NOT_BE_UNDONE": "This action cannot be undone.",
    "ACTION_CAPS": "Action",
    "ACTION_INTERFACE": "Interface",
    "ACTION_RESULT": "Résultat",
    "ACTION_TYPE": "Action",
    "ACTION": "Action",
    "ACTIONS": "Actions",
    "ACTIVATE": "Activer",
    "ACTIVATION_FAILED": "Echec de l'activation",
    "ACTIVATION": "Activation",
    "ACTIVE_ACTIVE": "Actif-Actif",
    "ACTIVE_CONNECTION": "Connexion active",
    "ACTIVE_STANDBY": "Actif-En attente",
    "ACTIVE_STATUS": "Statut actif",
    "ACTIVE": "Actif",
    "ADD_5G_DEPLOYMENT": "Ajouter une configuration de déploiement",
    "ADD_ACCOUNT": "Ajouter un compte",
    "ADD_API_KEY": "Ajouter une clé API de service cloud",
    "ADD_AWS_ACCOUNT": "Ajouter un compte AWS",
    "ADD_AWS_CLOUD_ACCOUNT": "Ajouter un compte cloud AWS",
    "ADD_AWS_GROUP": "Ajouter un groupe AWS",
    "ADD_AZURE_ACCOUNT": "Ajouter un compte Azure",
    "ADD_AZURE_CLOUD_ACCOUNT": "Ajouter un compte cloud AWS",
    "ADD_BC_PROVISIONING_TEMPLATE": "Ajouter un module de configuration Branch Connector",
    "ADD_BRANCH_CONNECTOR_PROV_TEMPLATE": "Ajouter un module de configuration Branch Connector",
    "ADD_CLOUD_APP_PROVIDER": "Ajouter un fournisseur d'applications cloud",
    "ADD_CLOUD_CONNECTOR_ADMIN": "Ajouter un administrateur",
    "ADD_CLOUD_CONNECTOR_ROLE": "Ajouter un rôle d'administrateur",
    "ADD_CLOUD_CONNECTOR": "Ajouter un connecteur cloud",
    "ADD_CLOUD_NSS_FEED": "Ajouter un flux NSS cloud",
    "ADD_CLOUD_PROVISIONING_TEMPLATE": "Ajouter un modèle de provisionnement de connecteur cloud",
    "ADD_CRITERIA": "Ajouter des critères",
    "ADD_DEPLOYMENT_CONFIGURATION": "Ajouter une configuration de déploiement",
    "ADD_DESTINATION_IP_GROUP": "Ajouter un groupe IP de destination",
    "ADD_DNS_GATEWAY": "Ajouter une passerelle DNS",
    "ADD_DNS_POLICIES": "Ajouter une règle de filtrage DNS",
    "ADD_DYNAMIC_VDI_GROUP": "Ajouter un groupe VDI dynamique",
    "ADD_EC_NSS_CLOUD_FEED": "Ajouter un flux NSS cloud",
    "ADD_EC_NSS_FEED": "Ajouter un flux NSS",
    "ADD_EC_NSS_SERVER": "Ajouter un serveur NSS",
    "ADD_EVENT_GRID": "Ajouter la grille des événements (facultatif)",
    "ADD_FILTER": "Ajouter un filtre",
    "ADD_FILTERS": "Ajouter des filtres",
    "ADD_GROUP": "Ajouter un groupe",
    "ADD_HTTP_HEADER": "Ajouter un en-tête HTTP",
    "ADD_INTERFACE": "Ajouter une interface",
    "ADD_IP_INFO": "Ajouter des informations IP",
    "ADD_IP_POOL": "Ajouter un pool d'adresses IP",
    "ADD_ITEMS": "Ajouter des éléments",
    "ADD_LOCATION_AND_CCG": "Ajouter des connecteurs d’emplacement et de cloud",
    "ADD_LOCATION_TEMPLATE": "Ajouter un modèle d’emplacement",
    "ADD_LOG_AND_CONTROL_FORWARDING_RULE": "Ajouter une règle de transfert des journaux et des contrôles",
    "ADD_LOG_AND_CONTROL_FORWARDING": "Consigner et contrôler les transferts",
    "ADD_LOG_AND_CONTROL_GATEWAY": "Consigner et contrôler les passerelles",
    "ADD_MORE": "Ajouter d'autres éléments",
    "ADD_NETWORK_SERVICE_GROUP": "Ajouter un groupe de services réseau",
    "ADD_NETWORK_SERVICE": "Ajouter un service réseau",
    "ADD_NEW_GATEWAY": "Ajouter une nouvelle passerelle",
    "ADD_NEW": "Ajouter un nouveau",
    "ADD_NSS_FEED": "Ajouter un flux NSS",
    "ADD_NSS_SERVER": "Ajouter un serveur NSS",
    "ADD_PORT": "Ajouter un port",
    "ADD_PROVISIONING_TEMPLATE": "Ajouter un modèle de provisionnement de connecteur cloud",
    "ADD_SOURCE_IP_GROUP": "Ajouter un groupe d’adresses IP source",
    "ADD_STORAGE_ACCOUNT": "Ajouter un compte de stockage",
    "ADD_SUB_INTERFACE": "Ajouter une sous-interface",
    "ADD_TENANT": "Ajouter un locataire",
    "ADD_TO_A_LOCATION": "Ajouter à un emplacement",
    "ADD_TO_AN_EXISTING_GROUP": "Ajouter à un groupe existant",
    "ADD_TRAFFIC_FORWARDING_RULE": "Ajouter une règle de transfert de trafic",
    "ADD_TRAFFIC_FORWARDING": "Ajouter le transfert de trafic",
    "ADD_TRAFFIC_FWD_POLICIES": "Ajouter des règles de transfert de trafic",
    "ADD_UPF": "Ajouter une fonction de l'instance utilisateur",
    "ADD_VDI_AGENT_FORWARDING_PROFILE": "Ajouter un profil de transfert VDI",
    "ADD_VDI_TEMPLATE": "Ajouter un modèle VDI",
    "ADD_ZERO_TRUST_GATEWAY": "Ajouter une passerelle Zero Trust",
    "ADD_ZIA_GATEWAY": "Ajouter une passerelle ZIA",
    "ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128": "La limite supplémentaire de comptes AWS est de 128.",
    "ADDITIONAL_AWS_ACCOUNTS": "Comptes AWS supplémentaires",
    "ADDRESS_RANGES_SHOULD_NOT_OVERLAP": "Les plages d’adresses ne peuvent pas se chevaucher.",
    "ADM_ACTIVATING": "Activation en cours",
    "ADM_ACTV_DONE": "Fin de l'activation de l'administrateur",
    "ADM_ACTV_FAIL": "Échec de l'activation de l'administrateur",
    "ADM_ACTV_QUEUED": "Activation en attente",
    "ADM_EDITING": "Modification",
    "ADM_EXPIRED": "session d'administration expirée",
    "ADM_LOGGED_IN": "administrateur connecté",
    "ADMIN_ID": "Identifiant d'administrateur",
    "ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE": "Cet admin existe également dans l'Admin Portal pour un autre service. Il sera associé au même compte d'administrateur dans l'autre Admin Portal, et le service Zscaler mettra à jour toutes les modifications apportées à l'autre compte admin telles que l'e-mail, le nom, la portée, le mot de passe et les commentaires. Continuer ?",
    "ADMIN_MANAGEMENT": "Gestion Administrateur",
    "ADMIN_ROLE": "Rôle d'Administrateur",
    "ADMIN_SAML_PUBLICCERT_INVALID_EXTENSION": "Le certificat public SAML doit être au format .cer ou .pem.",
    "ADMINISTRATION_CONFIGURATION": "Configuration de l'Administration",
    "ADMINISTRATION_CONTROL": "Contrôle d'Administration",
    "ADMINISTRATION": "Administration",
    "ADMINISTRATOR_ADMIN_USER": "Administrateur",
    "ADMINISTRATOR_AUDITOR": "Auditeur",
    "ADMINISTRATOR_MANAGEMENT": "Gestion Administrateur",
    "ADMINISTRATOR_PASSWORD_BASED_LOGIN": "Connexion basée sur un mot de passe",
    "ADMINISTRATOR_ROLE": "Gestion des rôles",
    "ADMINISTRATOR_SAML_CONFIGURE": "Authentification SAML pour les administrateurs",
    "ADMINISTRATOR_SAML_ENABLED": "Activer l'authentification SAML",
    "ADMINISTRATOR_SAML_METADATA": "Télécharger les métadonnées XML",
    "ADMINISTRATOR_SAML": "SAML",
    "ADMINISTRATORS_MANAGEMENT": "Gestion des Administrateurs",
    "ADMINISTRATORS": "Administrateurs",
    "ADSPYWARE_SITES": "Sites de logiciels espions/logiciels publicitaires",
    "ADULT_SEX_EDUCATION": "Education sexuelle destinée aux adultes",
    "ADULT_THEMES": "Sujets pour adultes",
    "ADVANCED_SETTINGS": "Paramètres avancés",
    "ADWARE_OR_SPYWARE": "Spywares/adwares",
    "AF_SOUTH_1": "Afrique (Le Cap)",
    "AF_SOUTH_1A": "af-south-1a",
    "AF_SOUTH_1B": "af-south-1b",
    "AF_SOUTH_1C": "af-south-1c",
    "AFGHANISTAN_ASIA_KABUL": "Asie/Kaboul",
    "AFGHANISTAN": "Afghanistan",
    "AGENT_STATUS": "Statut de l'agent",
    "AGGREGATE_LOGS": "Agréger les journaux",
    "AIAIGAME_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte aiaigame.com.",
    "AIAIGAME": "AiAi Games",
    "AILI_DESC": " Site Web chinois d'articles de mode",
    "AILI": "Aili",
    "AIM_DESC": " AIM (anciennement AOL Instant Messenger) désigne une application de messagerie instantanée. Le protocole nommé OSCAR (Open System for CommunicAtion in Realtime) est utilisé dans les services ICQ et AIM.",
    "AIM_EXPRESS_DESC": " AOL Instant Messaging Express prend en charge de nombreuses fonctions standard incluses dans AIM, mais ne propose pas de fonctions avancées comme le transfert de fichiers, le chat audio ou la vidéoconférence.",
    "AIM_EXPRESS": "Aim_express",
    "AIM_TRANSFER_DESC": " AIM désigne un protocole de messagerie instantanée.",
    "AIM_TRANSFER": "AIM File Transfer",
    "AIM": "AIM",
    "AIMEXPRESS": "AIM Express",
    "AIMINI_DESC": " Aimini désigne une solution en ligne permettant de stocker, d'envoyer et de partager des fichiers.",
    "AIMINI": "Aimini",
    "AIMS_DESC": " AIMS est la version sécurisée d'AIM.",
    "AIMS": "AIMS",
    "AIOWRITE_THROTTLE": "limite aiowrite. ca-ft est le seul à l’utiliser actuellement",
    "AIRAIM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte airaim.com. Il classifie aussi le trafic SSL vers le nom commun airaim.com.",
    "AIRAIM": "Airaim",
    "ALAND_ISLANDS_EUROPE_MARIEHAMN": "Europe/Mariehamn",
    "ALAND_ISLANDS": "Iles Aland",
    "ALAND": "Aland",
    "ALBANIA_EUROPE_TIRANE": "Europe/Tirana",
    "ALBANIA": "Albanie",
    "ALCOHOL_TOBACCO": "Alcool/Tabac",
    "ALGERIA_AFRICA_ALGIERS": "Afrique/Alger",
    "ALGERIA": "Algérie",
    "ALL_VALUES": "Toutes les valeurs",
    "ALL_ZSCALER_LOCATION_GROUPS": "Tous les groupes d’emplacements Zscaler",
    "ALL_ZSCALER_LOCATION_TYPES": "Tous les types d’emplacements Zscaler",
    "ALL_ZSCALER_LOCATIONS": "Tous les emplacements Zscaler",
    "ALL_ZSCALER_NETWORK_SERVICE": "Tous les services réseau Zscaler",
    "ALL": "Tous",
    "ALLOW_TO_CREATE_NEW_LOCATION": "Permettre la création de nouveaux emplacements",
    "ALLOW": "Autoriser",
    "ALLOWED_ACCOUNT_GROUPS": "Groupes de comptes autorisés",
    "ALLOWED_ACCOUNTS_GROUPS": "Groupes de comptes autorisés",
    "ALLOWED_ACCOUNTS": "Comptes autorisés",
    "ALLOWED": "Autorisé",
    "ALT_NEW_AGE": "Alt/Nouvel An",
    "ALTERNATE_LIFESTYLE": "Mode de vie",
    "AMAZON_WEB_SERVICES_CONSOLE": "Console des services Web d’Amazon",
    "AMAZON_WEB_SERVICES": "Services Web d’Amazon ",
    "AMERICAN_SAMOA_PACIFIC_PAGO_PAGO": "Pacifique/Pago Pago",
    "AMERICAN_SAMOA": "Samoa américaines",
    "AMF_IP_CIDR": "AMF IP/CIDR",
    "AMF_NAME": "Nom de l'AMF",
    "AMI_ID": "ID AMI",
    "ANALYTICS": "Analytique",
    "ANDORRA_EUROPE_ANDORRA": "Europe/Andorre",
    "ANDORRA": "Andorre",
    "ANDROID_OS": "Android",
    "ANGOLA_AFRICA_LUANDA": "Afrique/Luanda",
    "ANGOLA": "Angola",
    "ANGUILLA_AMERICA_ANGUILLA": "Amérique/Anguilla",
    "ANGUILLA": "Anguilla",
    "ANONYMIZER": "P2P et anonymiseur",
    "ANTARCTICA_CASEY": "Antarctique/Casey",
    "ANTARCTICA_DAVIS": "Antarctique/Davis",
    "ANTARCTICA_DUMONTDURVILLE": "Antarctique/DumontDUrville",
    "ANTARCTICA_MAWSON": "Antarctique/Mawson",
    "ANTARCTICA_MCMURDO": "Antarctique/McMurdo",
    "ANTARCTICA_PALMER": "Antarctique/Palmer",
    "ANTARCTICA_ROTHERA": "Antarctique/Rothera",
    "ANTARCTICA_SOUTH_POLE": "Antarctique/Pôle Sud",
    "ANTARCTICA_SYOWA": "Antarctique/Syowa",
    "ANTARCTICA_VOSTOK": "Antarctique/Vostok",
    "ANTARCTICA": "Antarctique",
    "ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA": "Amérique/Antigua",
    "ANTIGUA_AND_BARBUDA": "Antigua-et-Barbuda",
    "ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS": "Toute adresse IP non appariée des pools d’adresses IP ZPA",
    "ANY_RULE": "N'importe quel élément",
    "ANY": "N'importe quel élément",
    "AP_EAST_1": "Asie-Pacifique (Hong Kong)",
    "AP_EAST_1A": "ap-east-1a",
    "AP_EAST_1B": "ap-east-1b",
    "AP_EAST_1C": "ap-east-1c",
    "AP_NORTHEAST_1": "ap-northeast-1 (Tokyo)",
    "AP_NORTHEAST_1A": "ap-northeast-1a",
    "AP_NORTHEAST_1C": "ap-northeast-1c",
    "AP_NORTHEAST_1D": "ap-northeast-1d",
    "AP_NORTHEAST_1E": "ap-northeast-1e",
    "AP_NORTHEAST_2": "ap-northeast-2 (Seoul)",
    "AP_NORTHEAST_2A": "ap-northeast-2a",
    "AP_NORTHEAST_2B": "ap-northeast-2b",
    "AP_NORTHEAST_2C": "ap-northeast-2c",
    "AP_NORTHEAST_3": "ap-northeast-3 (Osaka-Local)",
    "AP_NORTHEAST_3A": "ap-northeast-3a",
    "AP_SOUTH_1": "ap-south-1 (Mumbai)",
    "AP_SOUTH_1A": "ap-south-1a",
    "AP_SOUTH_1B": "ap-south-1b",
    "AP_SOUTH_1C": "ap-south-1c",
    "AP_SOUTH_2": "Asie Pacifique (Hyderabad)",
    "AP_SOUTHEAST_1": "ap-southeast-1 (Singapour)",
    "AP_SOUTHEAST_1A": "ap-southeast-1a",
    "AP_SOUTHEAST_1B": "ap-southeast-1b",
    "AP_SOUTHEAST_1C": "ap-southeast-1c",
    "AP_SOUTHEAST_2": "ap-southeast-2 (Sydney)",
    "AP_SOUTHEAST_2A": "ap-southeast-2a",
    "AP_SOUTHEAST_2B": "ap-southeast-2b",
    "AP_SOUTHEAST_2C": "ap-southeast-2c",
    "AP_SOUTHEAST_3": "ap-southeast-3 (Jakarta)",
    "AP_SOUTHEAST_4": "Asie Pacifique (Melbourne)",
    "API_KEY_MANAGEMENT": "Gestion des clés API",
    "API_KEY": "Clé API",
    "APIKEY_MANAGEMENT": "Gestion des clés API",
    "APP_CONNECTOR_DEPLOYMENT_STATUS": "Détails du déploiement d'App Connector",
    "APP_CONNECTOR_DESCRIPTION": "Les App Connectors peuvent être provisionnés dans le cadre de ce modèle. Veuillez remplir les informations ci-dessous ou passer à l'étape suivante.",
    "APP_CONNECTOR_GROUP_NAME": "Nom du groupe d'App Connectors",
    "APP_CONNECTOR_GROUP_TYPE": "Type de groupe App Connector",
    "APP_CONNECTOR_GROUP": "Groupe d'App Connectors",
    "APP_CONNECTOR_INTERFACE": "Interface App Connector",
    "APP_CONNECTOR": "App Connector",
    "APPLIANCE_MANAGEMENT": "Gestion d'appliance",
    "APPLIANCE_NAME": "Nom de l'appliance",
    "APPLIANCE": "Appliance",
    "APPLIANCES": "Appliances",
    "APPLICABLE_FOR": "Applicable pour",
    "APPLICATION_ID": "Identifiant de l’application",
    "APPLICATION_KEY": "Clé d'application",
    "APPLICATION_SEGMENT": "Segment d'application",
    "APPLICATION_SEGMENTS": "Application Segments",
    "APPLICATION_SERVICE_GROUPS": "Groupes de services d'applications",
    "APPLICATION_VERSION": "Version de l'application",
    "APPLICATIONS": "Applications",
    "APPLY_FILTER": "Appliquer le filtre",
    "APPLY_FILTERS": "Appliquer des filtres",
    "APPLY_TO_ALL_APP_SEGMENTS": "Appliquer à tous les App Segments",
    "APPLY": "Appliquer",
    "ARE_YOU_SURE_YOU_WANT_TO_PROCEED": "Voulez-vous vraiment continuer ?",
    "ARE_YOU_SURE": "Voulez-vous vraiment continuer ?",
    "ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES": "Amérique/Argentine/Buenos Aires",
    "ARGENTINA_AMERICA_ARGENTINA_CATAMARCA": "Amérique/Argentine/Catamarca",
    "ARGENTINA_AMERICA_ARGENTINA_CORDOBA": "Amérique/Argentine/Cordoba",
    "ARGENTINA_AMERICA_ARGENTINA_JUJUY": "Amérique/Argentine/Jujuy",
    "ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA": "Amérique/Argentine/La Rioja",
    "ARGENTINA_AMERICA_ARGENTINA_MENDOZA": "Amérique/Argentine/Mendoza",
    "ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS": "Amérique/Argentine/Rio Gallegos",
    "ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN": "Amérique/Argentine/San Juan",
    "ARGENTINA_AMERICA_ARGENTINA_TUCUMAN": "Amérique/Argentine/Tucuman",
    "ARGENTINA_AMERICA_ARGENTINA_USHUAIA": "Amérique/Argentine/Ushuaïa",
    "ARGENTINA": "Argentine",
    "ARMENIA_ASIA_YEREVAN": "Asie/Erevan",
    "ARMENIA": "Arménie",
    "ART_CULTURE": "Art/Culture",
    "ARUBA_AMERICA_ARUBA": "Amérique/Aruba",
    "ARUBA": "Aruba",
    "ASIA_EAST1_A": "asia-east1-a",
    "ASIA_EAST1_B": "asia-east1-b",
    "ASIA_EAST1_C": "asia-east1-c",
    "ASIA_EAST1": "asia-east1",
    "ASIA_EAST2_A": "asia-east2-a",
    "ASIA_EAST2_B": "asia-east2-b",
    "ASIA_EAST2_C": "asia-east2-c",
    "ASIA_EAST2": "asia-east2",
    "ASIA_NORTHEAST1_A": "asia-northeast1-a",
    "ASIA_NORTHEAST1_B": "asia-northeast1-b",
    "ASIA_NORTHEAST1_C": "asia-northeast1-c",
    "ASIA_NORTHEAST1": "asia-northeast1",
    "ASIA_NORTHEAST2_A": "asia-northeast2-a",
    "ASIA_NORTHEAST2_B": "asia-northeast2-b",
    "ASIA_NORTHEAST2_C": "asia-northeast2-c",
    "ASIA_NORTHEAST2": "asia-northeast2",
    "ASIA_NORTHEAST3_A": "asia-northeast3-a",
    "ASIA_NORTHEAST3_B": "asia-northeast3-b",
    "ASIA_NORTHEAST3_C": "asia-northeast3-c",
    "ASIA_NORTHEAST3": "asia-northeast3",
    "ASIA_SOUTH1_A": "asia-south1-a",
    "ASIA_SOUTH1_B": "asia-south1-b",
    "ASIA_SOUTH1_C": "asia-south1-c",
    "ASIA_SOUTH1": "asia-south1",
    "ASIA_SOUTH2_A": "asia-south2-a",
    "ASIA_SOUTH2_B": "asia-south2-b",
    "ASIA_SOUTH2_C": "asia-south2-c",
    "ASIA_SOUTH2": "asia-south2",
    "ASIA_SOUTHEAST1_A": "asia-southeast1-a",
    "ASIA_SOUTHEAST1_B": "asia-southeast1-b",
    "ASIA_SOUTHEAST1_C": "asia-southeast1-c",
    "ASIA_SOUTHEAST1": "asia-southeast1",
    "ASIA_SOUTHEAST2_A": "asia-southeast2-a",
    "ASIA_SOUTHEAST2_B": "asia-southeast2-b",
    "ASIA_SOUTHEAST2_C": "asia-southeast2-c",
    "ASIA_SOUTHEAST2": "asia-southeast2",
    "ASIA": "Asie",
    "ASIAPACIFIC": "Asie Pacifique",
    "AT": "à",
    "ATTRIBUTES_APPLICABLE_ONLY_TO_KNOWN_HOPS": "Ces attributs ne s'appliquent qu'aux sauts connus.",
    "ATTRIBUTES": "Attributs",
    "AUDIT_LOGS": "Journaux d'audit",
    "AUDIT_OPERATION": "Opération d’audit",
    "AUSTRALIA_ADELAIDE": "Australie/Adélaïde",
    "AUSTRALIA_BRISBANE": "Australie/Brisbane",
    "AUSTRALIA_BROKEN_HILL": "Australie/Broken Hill",
    "AUSTRALIA_CURRIE": "Australie/Currie",
    "AUSTRALIA_DARWIN": "Australie/Darwin",
    "AUSTRALIA_EUCLA": "Australie/Eucla",
    "AUSTRALIA_HOBART": "Australie/Hobart",
    "AUSTRALIA_LINDEMAN": "Australie/Lindeman",
    "AUSTRALIA_LORD_HOWE": "Australie/LordHowe",
    "AUSTRALIA_MELBOURNE": "Australie/Melbourne",
    "AUSTRALIA_NEWZEALAND": "Australie & Nouvelle-Zélande",
    "AUSTRALIA_PERTH": "Australie/Perth",
    "AUSTRALIA_SOUTHEAST1_A": "australia-southeast1-a",
    "AUSTRALIA_SOUTHEAST1_B": "australia-southeast1-b",
    "AUSTRALIA_SOUTHEAST1_C": "australia-southeast1-c",
    "AUSTRALIA_SOUTHEAST1": "australia-southeast1",
    "AUSTRALIA_SOUTHEAST2_A": "australia-southeast2-a",
    "AUSTRALIA_SOUTHEAST2_B": "australia-southeast2-b",
    "AUSTRALIA_SOUTHEAST2_C": "australia-southeast2-c",
    "AUSTRALIA_SOUTHEAST2": "australia-southeast2",
    "AUSTRALIA_SYDNEY": "Australie/Sydney",
    "AUSTRALIA": "Australie",
    "AUSTRALIACENTRAL": "(Asie Pacifique) Australie centrale",
    "AUSTRALIACENTRAL2": "(Asie-Pacifique) Australie centrale 2",
    "AUSTRALIAEAST": "(Asie-Pacifique) Australie orientale",
    "AUSTRALIASOUTHEAST": "(Asie-Pacifique) Australie du Sud-Est",
    "AUSTRIA_EUROPE_VIENNA": "Europe/Vienne",
    "AUSTRIA": "Autriche",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_AUP_ENABLED": "L’authentification requise doit être désactivée lorsque la PUA est activée.",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_CAUTION_ENABLED": "L’authentification requise doit être désactivée lorsque l’option Attention est activée.",
    "AUTH_TYPE": "Type d'authentification",
    "AUTHENTICATION_CONFIGURATION": "Configuration de l'authentification",
    "AUTHENTICATION_FAILED": "Échec de l'authentification",
    "AUTO_POPULATE_DNS_CACHE": "Alimentation automatique du cache DNS",
    "AUTO_REFRESH_DASHBOARD": "Tableau de bord d’actualisation automatique",
    "AUTO_SCALING_OPTIONS": "Options de mise à l'échelle automatique",
    "AUTO_SCALING": "Mise à l'échelle automatique",
    "AUTO": "Auto (Automatique)",
    "AUTOMATIC_MANAGEMENT_IP": "IP de gestion automatique",
    "AUTOMATIC_SERVICE_IP": "IP de service automatique",
    "AUTOMATIC": "Automatique",
    "Availability Zone": "Zone de disponibilité",
    "AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES": "L'identifiant de la zone de disponibilité doit comporter un minimum de 2 zones.",
    "AVAILABILITY_ZONE_ID": "Identifiant de la zone de disponibilité",
    "AVAILABILITY_ZONE": "Zone de disponibilité",
    "AVAILABILITY_ZONES": "Zones de disponibilité",
    "AVAILABLE": "Disponible",
    "AVERAGE": "Moyenne",
    "AWS_ACCESS_KEY_ID": "Identifiant de clé d’accès AWS",
    "AWS_ACCOUNT_ID": "Identifiant de compte AWS",
    "AWS_ACCOUNT": "Compte AWS",
    "AWS_AVAILABILITY_ZONE": "Zone de disponibilité AWS",
    "AWS_CLOUD_FORMATION": "AWS CLOUDFORMATION",
    "AWS_CLOUD": "AWS",
    "AWS_GROUP": "Groupe AWS",
    "AWS_REGION": "Région AWS",
    "AWS_REGIONS": "Régions AWS",
    "AWS_ROLE_NAME": "Nom du rôle AWS",
    "AWS_SECRET_ACCESS_KEY": "Clé d'accès secrète AWS",
    "AWS": "Services Web d’Amazon ",
    "AZERBAIJAN_ASIA_BAKU": "Asie/Bakou",
    "AZERBAIJAN": "Azerbaïdjan",
    "AZURE_ACCOUNT": "Compte Azure",
    "AZURE_AVAILABILITY_ZONE": "Zone de disponibilité Azure",
    "AZURE_CLOUD": "Azure",
    "AZURE_REGION": "Région Azure",
    "AZURE_RESOURCES": "Ressources Azure",
    "AZURE_SENTINEL": "Azure Sentinel",
    "AZURE": "Azure",
    "BACK": "Retour",
    "BAHAMAS_AMERICA_NASSAU": "Amérique/Nassau",
    "BAHAMAS": "Bahamas",
    "BAHRAIN_ASIA_BAHRAIN": "Asie/Bahreïn",
    "BAHRAIN": "Bahreïn",
    "BAIDUYUNDNS_DESC": " On a relevé une activité de tunnel DNS sur .baiduyundns.com",
    "BAIDUYUNDNS": "BaiduYunDns",
    "BALANCED_RULE": "Équilibré",
    "BALANCED": "Équilibré",
    "BANDWIDTH_CONTROL_ENABLED": "Les sous-emplacements partagent la valeur de bande passante affectée à cet emplacement",
    "BANDWIDTH_CONTROL": "Contrôle de bande passante",
    "BANGLADESH_ASIA_DHAKA": "Asie/Dacca",
    "BANGLADESH": "Bangladesh",
    "BARBADOS_AMERICA_BARBADOS": "Amérique/Barbade",
    "BARBADOS": "Barbade",
    "BASE_URL": "Base URL",
    "BC_APP_CONNECTOR": "Branch Connector & App Connector",
    "BC_CONNECTOR_GROUP": "Groupe Cloud & Branch Connector",
    "BC_CONNECTOR_LOCATION": "Emplacement des Cloud & Branch Connectors",
    "BC_CONNECTOR_VM": "VM Branch Connector",
    "BC_CONNECTOR": "Branch Connector",
    "BC_DESCRIPTION": "Description du connecteur de succursale ",
    "BC_DETAILS": "Détails du connecteur de succursale",
    "BC_DEVICE_GROUP": "Groupe d'appareils Branch Connector",
    "BC_GENERAL_INFORMATION_DESCRIPTION": "Configurez un modèle de provisionnement Branch Connector dans le portail d'administration Zscaler Cloud Connector pour déployer Branch Connector en tant que machine virtuelle dans votre compte de site distant ou de data center. Pour plus d'informations, consultez le document {0}Pour commencer{1}.",
    "BC_GROUP_DETAILS": "Détails du groupe connecteur de succursale",
    "BC_GROUP_NAME": "Nom du groupe connecteur de succursale",
    "BC_GROUP_TYPE": "Type de groupe Branch Connector",
    "BC_GROUP": "Groupe de connecteur de succursales",
    "BC_IMAGES_DESCRIPTION": "Pour déployer Branch Connector ou Branch Connector + App Connector à l'aide de Terraform, consultez {0}GitHub{1}. Pour en savoir plus, consultez le {2}portail d'aide Zscaler.{3}",
    "BC_IMAGES_DETAIL1": "Les Images Branch Connector comprennent des App Connectors qui n'ont pas encore été provisionnés. Pour déployer Branch Connector & App Connector, vous devez sélectionner l'instance combinée et configurer les propriétés de déploiement en conséquence.",
    "BC_IMAGES_DETAIL2": "Pour en savoir plus, reportez-vous à la section {0}Gestion du déploiement pour les périphériques virtuels{1}.",
    "BC_VM_SIZE": "Taille de la machine virtuelle du connecteur de succursale",
    "BELARUS_EUROPE_MINSK": "Europe/Minsk",
    "BELARUS": "Bélarus",
    "BELGIUM_EUROPE_BRUSSELS": "Europe/Bruxelles",
    "BELGIUM": "Belgique",
    "BELIZE_AMERICA_BELIZE": "Amérique/Belize",
    "BELIZE": "Belize",
    "BENIN_AFRICA_PORTO_NOVO": "Afrique/Porto-Novo",
    "BENIN": "Bénin",
    "BERMUDA_ATLANTIC_BERMUDA": "Atlantique/Bermudes",
    "BERMUDA": "Bermudes",
    "BEST_LINK": "Meilleur lien",
    "BEST": "Meilleur lien",
    "BESTLINK_RULE": "Meilleur lien",
    "BHUTAN_ASIA_THIMPHU": "Asie/Thimphou",
    "BHUTAN": "Bhoutan",
    "BLACKLIST_LOOKUP_RESULTS": "Résultats de la recherche de liste de refus",
    "BLACKLISTED_IP_CHECK": "Vérification de l’adresse IP de la liste de refus",
    "BLOCK_INTERNET_ACCESS": "Bloquer l’accès à Internet",
    "BLOCK": "Bloquer",
    "BLOG": "Blogs",
    "BLUEJEANS": "BlueJeans",
    "BOLIVIA_AMERICA_LA_PAZ": "Amérique/La Paz",
    "BOLIVIA": "Bolivie",
    "BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO": "Europe/Sarajevo",
    "BOSNIA_AND_HERZEGOVINA": "Bosnie-Herzégovine",
    "BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO": "Europe/Sarajevo",
    "BOSNIA_AND_HERZEGOWINA": "Bosnie-Herzégovine",
    "BOTH_REQ_RESP_ALLOW": "Autoriser",
    "BOTH_SESSION_AND_AGGREGATE_LOGS": "Journaux des sessions et des agrégats",
    "BOTNET": "Botnet",
    "BOTSWANA_AFRICA_GABORONE": "Afrique/Gaborone",
    "BOTSWANA": "Botswana",
    "BRANCH_AND_CLOUD_CONNECTOR_GROUP_NAME": "Nom du groupe Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_CONNECTOR_MONITORING": "Surveillance du groupe Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_CONNECTOR": "Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_MONITORING": "Surveillance du Cloud & Branch Connector",
    "BRANCH_CLOUD_CONNECTOR_GROUP": "Groupes Cloud & Branch Connector",
    "BRANCH_CONFIGURATION": "Configuration de site distant",
    "BRANCH_CONNECTOR_GROUP": "Groupe de connecteur de succursales",
    "BRANCH_CONNECTOR_GROUPS": "Groupes de Branch Connectors",
    "BRANCH_CONNECTOR_IMAGES": "Imahes de Branch Connectors",
    "BRANCH_CONNECTOR_INFORMATION": "Informations sur le connecteur",
    "BRANCH_CONNECTOR_LOCS": "Emplacements des connecteurs de succursale",
    "BRANCH_CONNECTOR_MONITORING": "Surveillance des connecteurs de succursale",
    "BRANCH_CONNECTOR": "Branch Connector",
    "BRANCH_DEVICES": "Appareils du site distant",
    "BRANCH_MANAGEMENT": "Gestion de site distant",
    "BRANCH_PROVISIONING": "Provisionnement de Succursale",
    "BRANCH_TYPE": "Type de site distant",
    "BRAZIL_AMERICA_ARAGUAINA": "Amérique/Araguaina",
    "BRAZIL_AMERICA_BAHIA": "Amérique/Bahia",
    "BRAZIL_AMERICA_BELEM": "Amérique/Belem",
    "BRAZIL_AMERICA_BOA_VISTA": "Amérique/Boa Vista",
    "BRAZIL_AMERICA_CAMPO_GRANDE": "Amérique/Campo Grande",
    "BRAZIL_AMERICA_CUIABA": "Amérique/Cuiaba",
    "BRAZIL_AMERICA_EIRUNEPE": "Amérique/Eirunepe",
    "BRAZIL_AMERICA_FORTALEZA": "Amérique/Fortaleza",
    "BRAZIL_AMERICA_MACEIO": "Amérique/Maceio",
    "BRAZIL_AMERICA_MANAUS": "Amérique/Manaus",
    "BRAZIL_AMERICA_NORONHA": "Amérique/Noronha",
    "BRAZIL_AMERICA_PORTO_VELHO": "Amérique/Porto Velho",
    "BRAZIL_AMERICA_RECIFE": "Amérique/Recife",
    "BRAZIL_AMERICA_RIO_BRANCO": "Amérique/Rio Branco",
    "BRAZIL_AMERICA_SAO_PAULO": "Amérique/Sao Paulo",
    "BRAZIL": "Brésil",
    "BRAZILSOUTH": "(Amérique du Sud) Brésil du Sud",
    "BRAZILSOUTHEAST": "(Amérique du Sud) Brésil du Sud-Est",
    "BRAZILUS": "(Amérique du Sud) Brésil États-Unis",
    "BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS": "Océan Indien/Chagos",
    "BRITISH_INDIAN_OCEAN_TERRITORY": "Territoire britannique de l'océan Indien",
    "BRITISH_VIRGIN_ISLANDS": "Îles Vierges britanniques",
    "BROWSER_EXPLOIT": "Attaques de navigateur",
    "BRUNEI_DARUSSALAM_ASIA_BRUNEI": "Asie/Brunei",
    "BRUNEI_DARUSSALAM": "Brunei Darussalam",
    "BRUNEI": "Brunei",
    "BULGARIA_EUROPE_SOFIA": "Europe/Sofia",
    "BULGARIA": "Bulgarie",
    "BURKINA_FASO_AFRICA_OUAGADOUGOU": "Afrique/Ouagadougou",
    "BURKINA_FASO": "Burkina Faso",
    "BURUNDI_AFRICA_BUJUMBURA": "Afrique/Bujumbura",
    "BURUNDI": "Burundi",
    "BW_DOWNLOAD": "Téléchargement (Mo/s)",
    "BW_UPLOAD": "Chargement (Mo/s)",
    "BYTES": "Octets",
    "CA_CENTRAL_1": "Canada (centre)",
    "CA_CENTRAL_1A": "ca-central-1a",
    "CA_CENTRAL_1B": "ca-central-1b",
    "CA_CENTRAL_1D": "ca-central-1d",
    "CA_INACTIVE": "Échec de la surveillance de l’intégrité. Le système est en panne trop longtemps.",
    "CABO_VERDE": "Cap-Vert",
    "CAMBODIA_ASIA_PHNOM_PENH": "Asie/Phnom Penh",
    "CAMBODIA": "Cambodge",
    "CAMEROON_AFRICA_DOUALA": "Afrique/Douala",
    "CAMEROON": "Cameroun",
    "CANADA_AMERICA_ATIKOKAN": "Amérique/Atikokan",
    "CANADA_AMERICA_BLANC_SABLON": "Amérique/Blanc-Sablon",
    "CANADA_AMERICA_CAMBRIDGE_BAY": "Amérique/Cambridge Bay",
    "CANADA_AMERICA_DAWSON_CREEK": "Amérique/Dawson Creek",
    "CANADA_AMERICA_DAWSON": "Amérique/Dawson",
    "CANADA_AMERICA_EDMONTON": "Amérique/Edmonton",
    "CANADA_AMERICA_GLACE_BAY": "Amérique/Glace Bay",
    "CANADA_AMERICA_GOOSE_BAY": "Amérique/Goose Bay",
    "CANADA_AMERICA_HALIFAX": "Amérique/Halifax",
    "CANADA_AMERICA_INUVIK": "Amérique/Inuvik",
    "CANADA_AMERICA_IQALUIT": "Amérique/Iqaluit",
    "CANADA_AMERICA_MONCTON": "Amérique/Moncton",
    "CANADA_AMERICA_MONTREAL": "Amérique/Montréal",
    "CANADA_AMERICA_NIPIGON": "Amérique/Nipigon",
    "CANADA_AMERICA_PANGNIRTUNG": "Amérique/Pangnirtung",
    "CANADA_AMERICA_RAINY_RIVER": "Amérique/Rainy River",
    "CANADA_AMERICA_RANKIN_INLET": "Amérique/Rankin Inlet",
    "CANADA_AMERICA_REGINA": "Amérique/Regina",
    "CANADA_AMERICA_RESOLUTE": "Amérique/Resolute",
    "CANADA_AMERICA_ST_JOHNS": "Amérique/Saint- Jean",
    "CANADA_AMERICA_SWIFT_CURRENT": "Amérique/Swift Current",
    "CANADA_AMERICA_THUNDER_BAY": "Amérique/Thunder Bay",
    "CANADA_AMERICA_TORONTO": "Amérique/Toronto",
    "CANADA_AMERICA_VANCOUVER": "Amérique/Vancouver",
    "CANADA_AMERICA_WHITEHORSE": "Amérique/Whitehorse",
    "CANADA_AMERICA_WINNIPEG": "Amérique/Winnipeg",
    "CANADA_AMERICA_YELLOWKNIFE": "Amérique/Yellowknife",
    "CANADA": "Canada",
    "CANADACENTRAL": "(Canada) Centre du Canada",
    "CANADAEAST": "(Canada) Est du Canada",
    "CANCEL_SUCCESS_MESSAGE": "Toutes les modifications ont été annulées.",
    "CANCEL": "Annuler",
    "CAPE_VERDE_ATLANTIC_CAPE_VERDE": "Atlantique/Cap-Vert",
    "CAPE_VERDE": "Cap-Vert",
    "CATEGORIES": "Catégories",
    "CATEGORY": "Catégorie",
    "CAYMAN_ISLANDS_AMERICA_CAYMAN": "Amérique/Caïmans",
    "CAYMAN_ISLANDS": "Iles Caïmans",
    "CC_ADMIN": "Administrateur des Cloud Connectors",
    "CC_BC_DETAILS": "Détails sur le Cloud & Branch Connector",
    "CC_CLOUD_PROVIDER_DESCRIPTION": "Sélectionnez l'un des fournisseurs de cloud suivants.",
    "CC_DETAILS": "Détails du connecteur cloud",
    "CC_ENABLE_XFF_FORWARDING": "Utiliser XFF à partir de la demande client",
    "CC_GENERAL_INFORMATION_DESCRIPTION": "Configurez un modèle de provisionnement cloud dans le portail d'administration Zscaler Cloud & Branch Connector afin de déployer Cloud Connector en tant que machine virtuelle avec Amazon Web Services (AWS), Google Cloud Platform (GCP) ou Microsoft Azure. Pour plus d'informations, voir {0}À propos des modèles de provisionnement cloud{1}.",
    "CC_GROUP_NAME": "Nom du groupe de connecteurs cloud",
    "CC_GROUP": "Groupe CC",
    "CC_INSTANCE": "Instance CC",
    "CC_LOCATION": "Emplacement du connecteur cloud",
    "CC_NW": "RÉSEAU DE CONNECTEURS CLOUD",
    "CC_ROLE_NAME": "Nom du rôle Cloud Connector",
    "CC_SOURCE_IP": "IP source CC",
    "CC_SOURCE_PORT": "Port source CC",
    "CC_STATUS": "Statut du connecteur cloud",
    "CC_VERSION": "Version du connecteur cloud",
    "CC_VM_NAME": "Nom de la machine virtuelle du connecteur cloud",
    "CC_VM": "VM CC",
    "CCA_DEVICE_GROUP": "Groupe d'appareils VDI",
    "CCA_DEVICE": "Appareil VDI",
    "CCA_FWD_PROFILE": "Profil de transfert VDI",
    "CCA_TEMPLATE_APIKEY": "Clé API de modèle VDI",
    "CCA_TEMPLATE_KEY": "Clé du modèle VDI",
    "CCA_TEMPLATE": "Modèle VDI",
    "CDN": "CDN",
    "CELLULAR_CONFIGURATION_MODE_CORE": "Déploiement fractionné - Core",
    "CELLULAR_CONFIGURATION_MODE_EDGE": "Déploiement fractionné - Edge",
    "CELLULAR_CONFIGURATION_MODE_EDGEONLY": "Edge uniquement",
    "CELLULAR_CONFIGURATION_MODE": "Mode de configuration cellulaire",
    "CELLULAR_CONFIGURATION_SELECTION": "Sélection de la configuration cellulaire",
    "CELLULAR_CONFIGURATION": "Configuration cellulaire",
    "CELLULAR_DEPLOYMENT_CONFIGURATION": "Configuration du déploiement",
    "CELLULAR": "Cellulaire",
    "CENTOS": "CentOS",
    "CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI": "Afrique/Bangui",
    "CENTRAL_AFRICAN_REPUBLIC": "République centrafricaine",
    "CENTRALINDIA": "(Asie-Pacifique) Inde centrale",
    "CENTRALUS": "(États-Unis) Centre des États-Unis",
    "CENTRALUSEUAP": "(États-Unis) EUAP du centre des États-Unis",
    "CENTRALUSSTAGE": "(États-Unis) Centre des États-Unis /étape)",
    "CF_ADD_ON_GWLB_TEMPLATE": "Modèle de module complémentaire avec Gateway Load Balancer (GWLB)",
    "CF_CUSTOM_DEPLOYMENT_TEMPLATE": "Modèle de déploiement personnalisé",
    "CF_DEFAULT_DEPLOYMENT_TEMPLATE": "Modèle de déploiement Starter",
    "CF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Modèle complémentaire avec ZPA",
    "CF_PRE_DEPLOYMENT_TEMPLATE": "Modèle de pré-déploiement",
    "CHAD_AFRICA_NDJAMENA": "Afrique/N'Djaména",
    "CHAD": "Tchad",
    "CHANGE_PASSWORD_REMINDER": "Rappel pour le changement de mot de passe",
    "CHANGE_PASSWORD_WINDOW": "Vous devez changer votre mot de passe tous les",
    "CHANGE_PASSWORD": "Changer le mot de passe",
    "CHECK_BLACKLIST": "Vérifier la liste de refus",
    "CHILE_AMERICA_SANTIAGO": "Amérique/Santiago",
    "CHILE_PACIFIC_EASTER": "Pacifique/Ile de Pâques",
    "CHILE": "Chili",
    "CHINA_ASIA_CHONGQING": "Asie/Chongqing",
    "CHINA_ASIA_HARBIN": "Asie/Harbin",
    "CHINA_ASIA_KASHGAR": "Asie/Kashgar",
    "CHINA_ASIA_SHANGHAI": "Asie/Shanghai",
    "CHINA_ASIA_URUMQI": "Asie/Urumqi",
    "CHINA": "Chine",
    "CHINAEAST": "(Asie Pacifique) Chine Est",
    "CHINAEAST2": "(Asie Pacifique) Chine Est 2",
    "CHINAEAST3": "(Asie-Pacifique) Chine orientale 3",
    "CHINANORTH": "(Asie-Pacifique) Chine Nord",
    "CHINANORTH2": "(Asie Pacifique) Chine Nord 2",
    "CHINANORTH3": "(Asie Pacifique) Chine Nord 3",
    "CHOOSE_EXISTING_LOCATION": "Choisir un emplacement existant",
    "CHOOSE_TO_RECEIVE_UPDATES": "CHOISIR DE RECEVOIR DES MISES À JOUR",
    "CHRISTMAS_ISLAND_INDIAN_CHRISTMAS": "Océan Indien/Christmas",
    "CHRISTMAS_ISLAND": "Ile Christmas",
    "CHROME_OS": "Chrome",
    "CIPHER_PROTOCOL": "Protocole de chiffrement",
    "CIPHER": "Chiffre",
    "CITY_STATE_PROVINCE_OPTIONAL": "Ville, État, province (facultatif)",
    "CITY": "Ville",
    "CLASSIFIEDS": "Classifieds",
    "CLEAR_ALL": "Tout effacer",
    "CLEAR_FILTERS": "Effacer les filtres",
    "CLEAR_SEARCH_AND_SORT": "Effacer la recherche et le tri",
    "CLICK_FOR_MORE_INFO": "Cliquer ici pour en savoir plus",
    "CLICK_HERE_TO_ACCEPT_EUSA": "Cliquez ici pour accepter l'accord EUSA en attente",
    "CLIENT_CONNECTOR_FOR_VDI": "Client Connector pour VDI",
    "CLIENT_DEST_NAME": "Nom de la destination du client",
    "CLIENT_DESTINATION_IP": "IP de destination du client",
    "CLIENT_DESTINATION_PORT": "Port de destination du client",
    "CLIENT_ID": "Identifiant du client",
    "CLIENT_IP": "IP du client",
    "CLIENT_NETWORK_PROTOCOL": "Protocole réseau client",
    "CLIENT_SECRET": "Secret du client",
    "CLIENT_SOURCE_IP": "IP de la source du client",
    "CLIENT_SOURCE_PORT": "IP de la source du client",
    "CLOSE": "Fermer",
    "CLOUD_ACCOUNT": "Compte cloud",
    "CLOUD_AUTOMATION_SCRIPTS": "Scripts d'automatisation du cloud",
    "CLOUD_CONFIG_REQUIREMENTS": "Configuration requise pour le cloud",
    "CLOUD_CONFIGURATION": "Configuration du cloud",
    "CLOUD_CONNECTOR_CONFIGURATION_NOT_APPLICABLE": "Ce mode de configuration ne s'applique pas à Cloud Connector.",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_OPTIONAL": "Groupes et espace de noms",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_TEXT": "En option, sélectionnez dans la liste les groupes Cloud Connector que vous souhaitez inclure dans ce groupe d'abonnement. Enrez un espace de noms qui vous permet de réutiliser le même abonnement dans des comptes différents.",
    "CLOUD_CONNECTOR_GROUP_CREATION": "Création du groupe de connecteurs cloud",
    "CLOUD_CONNECTOR_GROUP": "Groupe de Cloud Connectors",
    "CLOUD_CONNECTOR_GROUPS": "Groupes de Cloud Connectors",
    "CLOUD_CONNECTOR_INFORMATION": "Informations sur le connecteur",
    "CLOUD_CONNECTOR_INSTANCE_ROLE_NAME": "Nom du rôle d'instance Cloud Connector ",
    "CLOUD_CONNECTOR_MANAGEMENT": "Gestion des Cloud Connectors",
    "CLOUD_CONNECTOR_MONITORING": "Surveillance des connecteurs de succursale",
    "CLOUD_CONNECTOR_NAME": "Nom du connecteur cloud",
    "CLOUD_CONNECTOR_PROVISIONING": "Provisionnement Cloud Connector",
    "CLOUD_CONNECTOR_TRAFFIC_FLOW": "Flux de trafic du connecteur cloud",
    "CLOUD_CONNECTOR": "Connecteur cloud",
    "CLOUD_CONNECTORS_GROUP_AND_NAMESPACE": "Groupes & Espace de noms",
    "CLOUD_CONNECTORS": "Connecteurs cloud",
    "CLOUD_FORMATION": "CloudFormation",
    "CLOUD_MANAGEMENT": "Gestion du cloud",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_FAILED": "Échec le",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PENDING": "Validation en attente. Cliquez sur l'icône pour tester la connectivité.",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PREFIX": "Dernière validation",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_SUCCESSFUL": "Réussite le",
    "CLOUD_NSS_HTTP_HEADERS": "En-têtes HTTP",
    "CLOUD_OR_ON_PREMISE": "Cloud ou sur site",
    "CLOUD_PROVIDER_TYPE": "Type de fournisseur de cloud",
    "CLOUD_PROVIDER": "Fournisseur de cloud",
    "CLOUD_PROVISIONING": "Provisionnement du Cloud",
    "CLOUD_WATCH_GROUP_ARN": "ARN du groupe de surveillance cloud",
    "CLOUD": "Nuage",
    "CLOUDFORMATION_TEXT": "Les modèles CloudFormation servent à fournir à Zscaler des autorisations d'accès aux informations de comptes. Utilisez l'hyperlien Lancer CloudFormation pour ouvrir un modèle CloudFormation prérempli dans votre compte AWS. {1}Téléchargez le modèle CloudFormation{2} si l'option de lancement ne fonctionne pas.",
    "CLOUDFORMATION": "CloudFormation",
    "CLOUDWATCH_ARN_OPTIONAL": "ARN de Cloudwatch (facultatif)",
    "CLT_RX_BYTES": "Octets reçus par le client",
    "CLT_TX_BYTES": "Octets reçus par le client",
    "CLT_TX_DROPS": "Le client dépose les octets",
    "CLUSTERS": "Clusters",
    "CN_NORTH_1": "Chine (Pékin)",
    "CN_NORTH_1A": "cn-north-1a",
    "CN_NORTH_1B": "cn-north-1b",
    "CN_NORTHWEST_1": "Chine (Ningxia)",
    "CN_NORTHWEST_1A": "cn-northwest-1a",
    "CN_NORTHWEST_1B": "cn-northwest-1b",
    "CN_NORTHWEST_1C": "cn-northwest-1c",
    "COCOS_KEELING_ISLANDS_INDIAN_COCOS": "Océan Indien/Cocos",
    "COCOS_KEELING_ISLANDS": "Iles Cocos (Keeling)",
    "CODE": "CODE",
    "COLOMBIA_AMERICA_BOGOTA": "Amérique/Bogota",
    "COLOMBIA": "Colombie",
    "COMMANDS": "Commandes",
    "COMMENTS": "Commentaires",
    "COMOROS_INDIAN_COMORO": "Océan Indien/Comores",
    "COMOROS": "Comores",
    "COMPARE_VERSIONS": "Comparer les versions",
    "COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE": "TYPE D’INSTANCE EC2 RECOMMANDÉ PAR CALCUL",
    "COMPUTE": "Calculer",
    "COMPUTER_HACKING": "Piratage informatique",
    "CONFIG": "Config.",
    "CONFIGURATION_INFO": "INFORMATIONS DE CONFIGURATION",
    "CONFIGURATION_MODE": "Mode de configuration",
    "CONFIGURATION_NAME": "Nom de la configuration",
    "CONFIGURATION_TEMPLATE_NAME": "Nom du modèle de configuration",
    "CONFIGURATION_TEXT": "Zscaler nécessite des autorisations pour assumer un rôle IAM dans votre compte AWS. Ces autorisations permettent à Zscaler de collecter des métadonnées de configuration en temps réel dans AWS.  Pour plus d'informations, voir {1}Ajout d'un nouveau compte AWS{2}.",
    "CONFIGURATION": "Configuration",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP": "Vous pouvez définir des règles qui contrôlent les demandes et les réponses de la consignation et du contrôle des transferts",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD": "Configurer la stratégie de la consignation et du contrôle des transferts",
    "CONFIGURE_TRAFFIC_FORWARD_TIP": "Vous pouvez définir des règles qui contrôlent les demandes et les réponses du transfert du trafic.",
    "CONFIGURE_TRAFFIC_FORWARD": "Configurer le transfert du trafic",
    "CONFIGURED_MODE": "Mode configuré",
    "CONFIRM_CHANGES": "Confirmer les modifications",
    "CONFIRM_PASSWORD_NON_EQUALITY": "Le nouveau mot de passe et le mot de passe de confirmation doivent être identiques",
    "CONFIRM_PASSWORD_PLACEHOLDER": "Saisir votre nouveau mot de passe pour le confirmer",
    "CONFIRM_PASSWORD": "Confirmer le mot de passe",
    "CONFIRM": "Confirmer",
    "CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE": "Afrique/Brazzaville",
    "CONGO_CONGO_BRAZZAVILLE": "Afrique/Brazzaville",
    "CONGO_DEM_REP_AFRICA_KINSHASA": "Afrique/Kinshasa",
    "CONGO_DEM_REP_AFRICA_LUBUMBASHI": "Africa/Lubumbashi",
    "CONGO_DEM_REP": "Congo (Rép. Dém.)",
    "CONGO_REP_AFRICA_BRAZZAVILLE": "Afrique/Brazzaville",
    "CONGO_REP": "Congo (Rép.)",
    "CONGO_REPUBLIC": "République du Congo",
    "CONNECTOR_GROUP": "Groupe de connecteurs",
    "CONNECTOR_GROUPS": "Groupes de connecteurs",
    "CONNECTOR_INSTANCE": "Instance du connecteur",
    "CONNECTOR_IP": "Adresse IP du connecteur",
    "CONNECTOR_MANAGEMENT": "Gestion des connecteurs",
    "CONNECTOR_NAME": "Nom du connecteur",
    "CONNECTOR_NAMES": "Noms des connecteurs",
    "CONNECTOR_SOURCE_IP": "Adresse IP source du connecteur",
    "CONNECTOR_SOURCE_PORT": "Port source du connecteur",
    "CONNECTOR_VM_SIZE": "Taille de la machine virtuelle du connecteur",
    "CONNECTOR_VM": "VM du connecteur",
    "CONTAINS": "Contient",
    "CONTINUING_EDUCATION_COLLEGES": "Formation continue/supérieure",
    "CONTROL_SLASH_DATA": "Contrôle/Données",
    "COOK_ISLANDS_PACIFIC_RAROTONGA": "Pacifique/Rarotonga",
    "COOK_ISLANDS": "Iles Cook",
    "Copy for provisioning": "Copie pour le provisionnement",
    "COPY_CLOUD_CONNECTOR": "Copier le connecteur cloud",
    "COPY_FOR_PROVISIONING": "Copie pour le provisionnement",
    "COPY_PROVISIONING_URL": "Copier l’URL de provisionnement",
    "COPY_RIGHT": "Copyright ©2007-2020 Zscaler Inc.Tous droits réservés.",
    "COPY": "Copier",
    "COPYRIGHT_INFRINGEMENT": "Violation de droits d'auteur",
    "COPYRIGHT": "Droit d'auteur",
    "CORPORATE_MARKETING": "Marketing d'entreprise",
    "CORPORATE": "Type de trafic des utilisateurs de l'entreprise",
    "COSTA_RICA_AMERICA_COSTA_RICA": "Amérique/Costa Rica",
    "COSTA_RICA": "Costa Rica",
    "COTE_DIVOIRE_AFRICA_ABIDJAN": "Afrique/Abidjan",
    "COTE_DIVOIRE": "Côte d'Ivoire",
    "COUNT": "Nombre",
    "COUNTRIES": "Pays",
    "COUNTRY_A1": "Proxy anonyme",
    "COUNTRY_A2": "Fournisseur de satellite",
    "COUNTRY_AC": "Ile de l'Ascension",
    "COUNTRY_AD": "Andorre",
    "COUNTRY_AE": "Emirats arabes unis",
    "COUNTRY_AF": "Afghanistan",
    "COUNTRY_AG": "Antigua-et-Barbuda",
    "COUNTRY_AI": "Anguilla",
    "COUNTRY_AL": "Albanie",
    "COUNTRY_AM": "Arménie",
    "COUNTRY_AN": "Antilles néerlandaises",
    "COUNTRY_AO": "Angola",
    "COUNTRY_AP": "Région Asie/Pacifique",
    "COUNTRY_AQ": "Antarctique",
    "COUNTRY_AR": "Argentine",
    "COUNTRY_AS": "Samoa américaines",
    "COUNTRY_AT": "Autriche",
    "COUNTRY_AU": "Australie",
    "COUNTRY_AW": "Aruba",
    "COUNTRY_AX": "Iles Aland",
    "COUNTRY_AZ": "Azerbaïdjan",
    "COUNTRY_BA": "Bosnie-Herzégovine",
    "COUNTRY_BB": "Barbade",
    "COUNTRY_BD": "Bangladesh",
    "COUNTRY_BE": "Belgique",
    "COUNTRY_BF": "Burkina Faso",
    "COUNTRY_BG": "Bulgarie",
    "COUNTRY_BH": "Bahreïn",
    "COUNTRY_BI": "Burundi",
    "COUNTRY_BJ": "Bénin",
    "COUNTRY_BL": "Saint-Barthélemy",
    "COUNTRY_BM": "Bermudes",
    "COUNTRY_BN": "Brunei Darussalam",
    "COUNTRY_BO": "Bolivie",
    "COUNTRY_BQ": "Bonaire, Saint-Eustache et Saba",
    "COUNTRY_BR": "Brésil",
    "COUNTRY_BS": "Bahamas",
    "COUNTRY_BT": "Bhoutan",
    "COUNTRY_BU": "Myanmar",
    "COUNTRY_BV": "Ile Bouvet",
    "COUNTRY_BW": "Botswana",
    "COUNTRY_BX": "Bureau des marques et Bureau des dessins du Benelux",
    "COUNTRY_BY": "Bélarus",
    "COUNTRY_BZ": "Belize",
    "COUNTRY_CA": "Canada",
    "COUNTRY_CC": "Iles Cocos (Keeling)",
    "COUNTRY_CD": "République démocratique du Congo (Congo-Kinshasa)",
    "COUNTRY_CF": "République centrafricaine",
    "COUNTRY_CG": "Congo (Congo-Brazzaville)",
    "COUNTRY_CH": "Suisse",
    "COUNTRY_CI": "Côte d'Ivoire",
    "COUNTRY_CK": "Iles Cook",
    "COUNTRY_CL": "Chili",
    "COUNTRY_CM": "Cameroun",
    "COUNTRY_CN": "Chine",
    "COUNTRY_CO": "Colombie",
    "COUNTRY_CODE": "Pays",
    "COUNTRY_CP": "Ile de Clipperton",
    "COUNTRY_CR": "Costa Rica",
    "COUNTRY_CS": "Serbie-et-Monténégro",
    "COUNTRY_CT": "Iles Canton et Enderbury",
    "COUNTRY_CU": "Cuba",
    "COUNTRY_CV": "Cap-Vert",
    "COUNTRY_CW": "Curaçao",
    "COUNTRY_CX": "Ile Christmas",
    "COUNTRY_CY": "Chypre",
    "COUNTRY_CZ": "République tchèque",
    "COUNTRY_DD": "République démocratique allemande",
    "COUNTRY_DE": "Allemagne",
    "COUNTRY_DG": "Diego Garcia",
    "COUNTRY_DJ": "Djibouti",
    "COUNTRY_DK": "Danemark",
    "COUNTRY_DM": "Dominique",
    "COUNTRY_DO": "République dominicaine",
    "COUNTRY_DY": "Bénin",
    "COUNTRY_DZ": "Algérie",
    "COUNTRY_EA": "Ceuta, Melilla",
    "COUNTRY_EC": "Equateur",
    "COUNTRY_EE": "Estonie",
    "COUNTRY_EF": "Union des pays liés par la Convention sur le brevet européen",
    "COUNTRY_EG": "Egypte",
    "COUNTRY_EH": "Sahara occidental",
    "COUNTRY_EM": "Office de l'harmonisation dans le marché intérieur",
    "COUNTRY_EP": "Office européen des brevets",
    "COUNTRY_ER": "Erythrée",
    "COUNTRY_ES": "Espagne",
    "COUNTRY_ET": "Ethiopie",
    "COUNTRY_EU": "Europe",
    "COUNTRY_EV": "Organisation eurasienne des brevets",
    "COUNTRY_EW": "Estonie",
    "COUNTRY_FI": "Finlande",
    "COUNTRY_FJ": "Fidji",
    "COUNTRY_FK": "Iles Falkland (Malouines)",
    "COUNTRY_FL": "Liechtenstein",
    "COUNTRY_FM": "Etats fédérés de Micronésie",
    "COUNTRY_FO": "Iles Féroé",
    "COUNTRY_FQ": "Terres australes et antarctiques françaises",
    "COUNTRY_FR": "France",
    "COUNTRY_FX": "France métropolitaine",
    "COUNTRY_GA": "Gabon",
    "COUNTRY_GB": "Royaume-Uni",
    "COUNTRY_GC": "Office des brevets du Conseil de coopération des États arabes du Golfe (CCG)",
    "COUNTRY_GD": "Grenade",
    "COUNTRY_GE": "Géorgie",
    "COUNTRY_GF": "Guyane française",
    "COUNTRY_GG": "Guernesey",
    "COUNTRY_GH": "Ghana",
    "COUNTRY_GI": "Gibraltar",
    "COUNTRY_GL": "Groenland",
    "COUNTRY_GM": "Gambie",
    "COUNTRY_GN": "Guinée",
    "COUNTRY_GP": "Guadeloupe",
    "COUNTRY_GQ": "Guinée équatoriale",
    "COUNTRY_GR": "Grèce",
    "COUNTRY_GS": "Géorgie du Sud-et-les Iles Sandwich du Sud",
    "COUNTRY_GT": "Guatemala",
    "COUNTRY_GU": "Guam",
    "COUNTRY_GW": "Guinée-Bissau",
    "COUNTRY_GY": "Guyana",
    "COUNTRY_HK": "Hong Kong",
    "COUNTRY_HM": "Ile Heard-et-Iles MacDonald",
    "COUNTRY_HN": "Honduras",
    "COUNTRY_HR": "Croatie",
    "COUNTRY_HT": "Haïti",
    "COUNTRY_HU": "Hongrie",
    "COUNTRY_HV": "Haute-Volta",
    "COUNTRY_IB": "Bureau international de l'OMPI",
    "COUNTRY_IC": "Iles Canaries",
    "COUNTRY_ID": "Indonésie",
    "COUNTRY_IE": "Irlande",
    "COUNTRY_IL": "Israël",
    "COUNTRY_IM": "Ile de Man",
    "COUNTRY_IN": "Inde",
    "COUNTRY_IO": "Territoire britannique de l'océan Indien",
    "COUNTRY_IQ": "Irak",
    "COUNTRY_IR": "Iran",
    "COUNTRY_IS": "Islande",
    "COUNTRY_IT": "Italie",
    "COUNTRY_JA": "Jamaïque",
    "COUNTRY_JE": "Jersey",
    "COUNTRY_JM": "Jamaïque",
    "COUNTRY_JO": "Jordanie",
    "COUNTRY_JP": "Japon",
    "COUNTRY_JT": "Atoll Johnston",
    "COUNTRY_KE": "Kenya",
    "COUNTRY_KG": "Kirghizistan",
    "COUNTRY_KH": "Cambodge",
    "COUNTRY_KI": "Kiribati",
    "COUNTRY_KM": "Comores",
    "COUNTRY_KN": "Saint-Kitts-et-Nevis",
    "COUNTRY_KP": "République démocratique populaire de Corée",
    "COUNTRY_KR": "République de Corée",
    "COUNTRY_KW": "Koweït",
    "COUNTRY_KY": "Iles Caïmans",
    "COUNTRY_KZ": "Kazakhstan",
    "COUNTRY_LA": "Laos",
    "COUNTRY_LB": "Liban",
    "COUNTRY_LC": "Sainte-Lucie",
    "COUNTRY_LF": "Fezzan libyen",
    "COUNTRY_LI": "Liechtenstein",
    "COUNTRY_LK": "Sri Lanka",
    "COUNTRY_LR": "Libéria",
    "COUNTRY_LS": "Lesotho",
    "COUNTRY_LT": "Lituanie",
    "COUNTRY_LU": "Luxembourg",
    "COUNTRY_LV": "Lettonie",
    "COUNTRY_LY": "Libye",
    "COUNTRY_MA": "Maroc",
    "COUNTRY_MC": "Monaco",
    "COUNTRY_MD": "Moldavie",
    "COUNTRY_ME": "Monténégro",
    "COUNTRY_MF": "Saint-Martin",
    "COUNTRY_MG": "Madagascar",
    "COUNTRY_MH": "Iles Marshall",
    "COUNTRY_MI": "Iles Midway",
    "COUNTRY_MK": "Macédoine",
    "COUNTRY_ML": "Mali",
    "COUNTRY_MM": "Myanmar",
    "COUNTRY_MN": "Mongolie",
    "COUNTRY_MO": "Macao",
    "COUNTRY_MP": "Iles Mariannes du Nord",
    "COUNTRY_MQ": "Martinique",
    "COUNTRY_MR": "Mauritanie",
    "COUNTRY_MS": "Montserrat",
    "COUNTRY_MT": "Malte",
    "COUNTRY_MU": "Maurice",
    "COUNTRY_MV": "Maldives",
    "COUNTRY_MW": "Malawi",
    "COUNTRY_MX": "Mexique",
    "COUNTRY_MY": "Malaisie",
    "COUNTRY_MZ": "Mozambique",
    "COUNTRY_NA": "Namibie",
    "COUNTRY_NC": "Nouvelle-Calédonie",
    "COUNTRY_NE": "Niger",
    "COUNTRY_NF": "Ile Norfolk",
    "COUNTRY_NG": "Nigéria",
    "COUNTRY_NH": "Nouvelles-Hébrides",
    "COUNTRY_NI": "Nicaragua",
    "COUNTRY_NL": "Pays-Bas",
    "COUNTRY_NO": "Norvège",
    "COUNTRY_NP": "Népal",
    "COUNTRY_NQ": "Terre de la Reine-Maud",
    "COUNTRY_NR": "Nauru",
    "COUNTRY_NT": "Zone neutre",
    "COUNTRY_NU": "Niue",
    "COUNTRY_NZ": "Nouvelle-Zélande",
    "COUNTRY_O1": "Autre",
    "COUNTRY_OA": "Organisation Africaine de la Propriété Intellectuelle",
    "COUNTRY_OM": "Oman",
    "COUNTRY_OPTIONAL": "Pays (facultatif)",
    "COUNTRY_PA": "Panama",
    "COUNTRY_PC": "Iles du Pacifique, Territoire sous tutelle des",
    "COUNTRY_PE": "Pérou",
    "COUNTRY_PF": "Polynésie française",
    "COUNTRY_PG": "Papouasie-Nouvelle-Guinée",
    "COUNTRY_PH": "Philippines",
    "COUNTRY_PI": "Philippines",
    "COUNTRY_PK": "Pakistan",
    "COUNTRY_PL": "Pologne",
    "COUNTRY_PM": "Saint-Pierre-et-Miquelon",
    "COUNTRY_PN": "Iles Pitcairn",
    "COUNTRY_PR": "Porto Rico",
    "COUNTRY_PS": "Etat de Palestine",
    "COUNTRY_PT": "Portugal",
    "COUNTRY_PU": "Iles mineures américaines du Pacifique",
    "COUNTRY_PW": "Palaos",
    "COUNTRY_PY": "Paraguay",
    "COUNTRY_PZ": "Panama",
    "COUNTRY_QA": "Qatar",
    "COUNTRY_RA": "Argentine",
    "COUNTRY_RB": "Bolivie cf Botswana",
    "COUNTRY_RC": "Chine",
    "COUNTRY_RE": "Réunion",
    "COUNTRY_RH": "Haïti",
    "COUNTRY_RI": "Indonésie",
    "COUNTRY_RL": "Liban",
    "COUNTRY_RM": "Madagascar",
    "COUNTRY_RN": "Niger",
    "COUNTRY_RO": "Roumanie",
    "COUNTRY_RP": "Philippines",
    "COUNTRY_RS": "Serbie",
    "COUNTRY_RU": "Russie",
    "COUNTRY_RW": "Rwanda",
    "COUNTRY_SA": "Arabie saoudite",
    "COUNTRY_SB": "Iles Salomon",
    "COUNTRY_SC": "Seychelles",
    "COUNTRY_SD": "Soudan",
    "COUNTRY_SE": "Suède",
    "COUNTRY_SF": "Finlande",
    "COUNTRY_SG": "Singapour",
    "COUNTRY_SH": "Sainte-Hélène",
    "COUNTRY_SI": "Slovénie",
    "COUNTRY_SJ": "Svalbard et l'Ile Jan Mayen",
    "COUNTRY_SK": "Slovaquie",
    "COUNTRY_SL": "Sierra Leone",
    "COUNTRY_SM": "Saint-Marin",
    "COUNTRY_SN": "Sénégal",
    "COUNTRY_SO": "Somalie",
    "COUNTRY_SR": "Suriname",
    "COUNTRY_SS": "Soudan du Sud",
    "COUNTRY_ST": "Sao Tomé-et-Principe",
    "COUNTRY_SU": "URSS",
    "COUNTRY_SV": "El Salvador",
    "COUNTRY_SX": "Saint-Martin (partie néerlandaise)",
    "COUNTRY_SY": "République arabe syrienne",
    "COUNTRY_SZ": "Swaziland",
    "COUNTRY_TA": "Tristan da Cunha",
    "COUNTRY_TC": "Iles Turks-et-Caïcos",
    "COUNTRY_TD": "Tchad",
    "COUNTRY_TF": "Terres australes françaises",
    "COUNTRY_TG": "Togo",
    "COUNTRY_TH": "Thaïlande",
    "COUNTRY_TJ": "Tadjikistan",
    "COUNTRY_TK": "Tokelau",
    "COUNTRY_TL": "Timor-Leste",
    "COUNTRY_TM": "Turkménistan",
    "COUNTRY_TN": "Tunisie",
    "COUNTRY_TO": "Tonga",
    "COUNTRY_TP": "Timor oriental",
    "COUNTRY_TR": "Turquie",
    "COUNTRY_TT": "Trinité-et-Tobago",
    "COUNTRY_TV": "Tuvalu",
    "COUNTRY_TW": "Taïwan",
    "COUNTRY_TZ": "Tanzanie",
    "COUNTRY_UA": "Ukraine",
    "COUNTRY_UG": "Ouganda",
    "COUNTRY_UK": "Royaume-Uni",
    "COUNTRY_UM": "Iles mineures éloignées des États-Unis",
    "COUNTRY_US": "Etats-Unis",
    "COUNTRY_USA": "É.-U.",
    "COUNTRY_UY": "Uruguay",
    "COUNTRY_UZ": "Ouzbékistan",
    "COUNTRY_VA": "Saint-Siège",
    "COUNTRY_VC": "Saint-Vincent-et-les Grenadines",
    "COUNTRY_VD": "Viet Nam",
    "COUNTRY_VE": "Venezuela",
    "COUNTRY_VG": "Iles Vierges britanniques",
    "COUNTRY_VI": "Iles Vierges des Etats-Unis",
    "COUNTRY_VN": "Viet Nam",
    "COUNTRY_VU": "Vanuatu",
    "COUNTRY_WF": "Wallis-et-Futuna",
    "COUNTRY_WG": "Grenade",
    "COUNTRY_WK": "Wake",
    "COUNTRY_WL": "Sainte-Lucie",
    "COUNTRY_WO": "Organisation Mondiale de la Propriété Intellectuelle",
    "COUNTRY_WS": "Samoa",
    "COUNTRY_WV": "Saint-Vincent",
    "COUNTRY_YD": "Yémen",
    "COUNTRY_YE": "Yémen",
    "COUNTRY_YT": "Mayotte",
    "COUNTRY_YU": "Yougoslavie",
    "COUNTRY_YV": "Venezuela",
    "COUNTRY_ZA": "Afrique du Sud",
    "COUNTRY_ZM": "Zambie",
    "COUNTRY_ZR": "Zaïre",
    "COUNTRY_ZW": "Zimbabwe",
    "COUNTRY": "Pays",
    "CREATE_A_NEW_GROUP": "Créer un groupe",
    "CREATE_A_NEW_TEST": "Créer un test",
    "CREATE_COMPLETE": "Création terminée",
    "CREATE_IN_PROGRESS": "Création en cours",
    "CREATE_TEST_TEXT": "Créez un test en entrant le nom, la description, la destination et le protocole (HTTP/HTTPS) à utiliser pour le test. Une fois créé, le test peut être exécuté à la demande. Lorsqu'il est exécuté, le test simule une commande curl vers la destination en utilisant le protocole saisi. La transaction devrait être visible dans les journaux de transfert, de pare-feu, Web, etc.",
    "CREATE_TEST": "Créer un test",
    "CREATE": "Créer",
    "CREATING": "Création en cours",
    "CREDENTIALS_INFO": "Entrez les informations d'identification du principal de service Azure pour accéder à vos abonnements Azure. Vous pouvez utiliser un seul principal de services pour plusieurs abonnements ou avoir un principal de services pour chaque abonnement. Veillez à ce que le principal de service dispose d'{1}autorisations d'accès{2}.",
    "CREDENTIALS_TEXT": "Entrez les informations d'identification du compte Azure pour accéder à votre compte AZURE.",
    "CREDENTIALS": "Informations d'identification",
    "CRITERIA_TEXT": "Critères",
    "CRITERIA": "CRITÈRES",
    "CROATIA_EUROPE_ZAGREB": "Europe/Zagreb",
    "CROATIA": "Croatie",
    "CRYPTOMINING": "Cryptominage",
    "CTL_CONNECTION_FAIL": "La connexion de contrôle SVPN a échoué.",
    "CTL_GW_CONN_CLOSE": "Connexion active de la passerelle de contrôle fermée.",
    "CTL_GW_CONN_SETUP_FAIL": "La configuration de la connexion à la passerelle de contrôle a échoué (erreur interne).",
    "CTL_GW_CONNECT_FAIL": "La configuration de la connexion à la passerelle de contrôle a échoué (erreur interne).",
    "CTL_GW_DNS_RESOLVE_FAIL": "La résolution DNS de la passerelle de contrôle a échoué.",
    "CTL_GW_KA_FAIL": "La connexion active de la passerelle de contrôle a échoué.",
    "CTL_GW_NO_CONN": "La connexion de la passerelle de contrôle n'a pas encore été établie par le client.",
    "CTL_GW_PAC_RESOLVE_FAIL": "La résolution PAC de la passerelle de contrôle a échoué.",
    "CTL_GW_PAC_RESOLVE_NOIP": "La résolution PAC de la passerelle de contrôle n'a renvoyé aucun IPS.",
    "CTL_GW_PROTO_MSG_ERROR": "Erreur de format de message dans la réponse GW de contrôle.",
    "CTL_GW_SRV_ERR_RESPONSE": "La passerelle de contrôle a reçu une réponse d'erreur HTTP du serveur.",
    "CTL_GW_UNHEALTHY": "L'intégrité de la passerelle de contrôle n'est pas assurée (état transitoire).",
    "CTL_KEEAPLIVE_FAIL": "La connexion active de la passerelle de contrôle a échoué.",
    "CTL_KEEPALIVE_FAIL": "La connexion active de la passerelle de contrôle a échoué.",
    "CUBA_AMERICA_HAVANA": "Amérique/Havane",
    "CUBA": "Cuba",
    "CULT": "Culte",
    "CURRENT_API_KEY": "Clé API actuelle",
    "CURRENT_DAY": "Journée en cours",
    "CURRENT_MODE": "Mode actuel",
    "CURRENT_MONTH": "Mois en cours",
    "CURRENT_PASSWORD_NOT_VALID": "Veuillez saisir un mot de passe valide",
    "CURRENT_PASSWORD": "Mot de passe actuel",
    "CURRENT_VERSION": "Version actuelle",
    "CURRENT_WEEK": "Semaine en cours",
    "CURRENTLY_EDITING": "EN COURS D’ÉDITION",
    "CUSTOM_00": "Catégorie personnalisée 0",
    "CUSTOM_01": "Catégorie personnalisée 1",
    "CUSTOM_02": "Catégorie personnalisée 2",
    "CUSTOM_03": "Catégorie personnalisée 3",
    "CUSTOM_04": "Catégorie personnalisée 4",
    "CUSTOM_05": "Catégorie personnalisée 5",
    "CUSTOM_06": "Catégorie personnalisée 6",
    "CUSTOM_07": "Catégorie personnalisée 7",
    "CUSTOM_08": "Catégorie personnalisée 8",
    "CUSTOM_09": "Catégorie personnalisée 9",
    "CUSTOM_10": "Catégorie personnalisée 0",
    "CUSTOM_100": "Catégorie personnalisée 100",
    "CUSTOM_101": "Catégorie personnalisée 101",
    "CUSTOM_102": "Catégorie personnalisée 102",
    "CUSTOM_103": "Catégorie personnalisée 103",
    "CUSTOM_104": "Catégorie personnalisée 104",
    "CUSTOM_105": "Catégorie personnalisée 105",
    "CUSTOM_106": "Catégorie personnalisée 106",
    "CUSTOM_107": "Catégorie personnalisée 107",
    "CUSTOM_108": "Catégorie personnalisée 108",
    "CUSTOM_109": "Catégorie personnalisée 109",
    "CUSTOM_11": "Catégorie personnalisée 11",
    "CUSTOM_110": "Catégorie personnalisée 110",
    "CUSTOM_111": "Catégorie personnalisée 111",
    "CUSTOM_112": "Catégorie personnalisée 112",
    "CUSTOM_113": "Catégorie personnalisée 113",
    "CUSTOM_114": "Catégorie personnalisée 114",
    "CUSTOM_115": "Catégorie personnalisée 115",
    "CUSTOM_116": "Catégorie personnalisée 116",
    "CUSTOM_117": "Catégorie personnalisée 117",
    "CUSTOM_118": "Catégorie personnalisée 118",
    "CUSTOM_119": "Catégorie personnalisée 119",
    "CUSTOM_12": "Catégorie personnalisée 12",
    "CUSTOM_120": "Catégorie personnalisée 120",
    "CUSTOM_121": "Catégorie personnalisée 121",
    "CUSTOM_122": "Catégorie personnalisée 122",
    "CUSTOM_123": "Catégorie personnalisée 123",
    "CUSTOM_124": "Catégorie personnalisée 124",
    "CUSTOM_125": "Catégorie personnalisée 125",
    "CUSTOM_126": "Catégorie personnalisée 126",
    "CUSTOM_127": "Catégorie personnalisée 127",
    "CUSTOM_128": "Catégorie personnalisée 128",
    "CUSTOM_129": "Catégorie personnalisée 129",
    "CUSTOM_13": "Catégorie personnalisée 13",
    "CUSTOM_130": "Catégorie personnalisée 130",
    "CUSTOM_131": "Catégorie personnalisée 131",
    "CUSTOM_132": "Catégorie personnalisée 132",
    "CUSTOM_133": "Catégorie personnalisée 133",
    "CUSTOM_134": "Catégorie personnalisée 134",
    "CUSTOM_135": "Catégorie personnalisée 135",
    "CUSTOM_136": "Catégorie personnalisée 136",
    "CUSTOM_137": "Catégorie personnalisée 137",
    "CUSTOM_138": "Catégorie personnalisée 138",
    "CUSTOM_139": "Catégorie personnalisée 139",
    "CUSTOM_14": "Catégorie personnalisée 14",
    "CUSTOM_140": "Catégorie personnalisée 140",
    "CUSTOM_141": "Catégorie personnalisée 141",
    "CUSTOM_142": "Catégorie personnalisée 142",
    "CUSTOM_143": "Catégorie personnalisée 143",
    "CUSTOM_144": "Catégorie personnalisée 144",
    "CUSTOM_145": "Catégorie personnalisée 145",
    "CUSTOM_146": "Catégorie personnalisée 146",
    "CUSTOM_147": "Catégorie personnalisée 147",
    "CUSTOM_148": "Catégorie personnalisée 148",
    "CUSTOM_149": "Catégorie personnalisée 149",
    "CUSTOM_15": "Catégorie personnalisée 15",
    "CUSTOM_150": "Catégorie personnalisée 150",
    "CUSTOM_151": "Catégorie personnalisée 151",
    "CUSTOM_152": "Catégorie personnalisée 152",
    "CUSTOM_153": "Catégorie personnalisée 153",
    "CUSTOM_154": "Catégorie personnalisée 154",
    "CUSTOM_155": "Catégorie personnalisée 155",
    "CUSTOM_156": "Catégorie personnalisée 156",
    "CUSTOM_157": "Catégorie personnalisée 157",
    "CUSTOM_158": "Catégorie personnalisée 158",
    "CUSTOM_159": "Catégorie personnalisée 159",
    "CUSTOM_16": "Catégorie personnalisée 16",
    "CUSTOM_160": "Catégorie personnalisée 160",
    "CUSTOM_161": "Catégorie personnalisée 161",
    "CUSTOM_162": "Catégorie personnalisée 162",
    "CUSTOM_163": "Catégorie personnalisée 163",
    "CUSTOM_164": "Catégorie personnalisée 164",
    "CUSTOM_165": "Catégorie personnalisée 165",
    "CUSTOM_166": "Catégorie personnalisée 166",
    "CUSTOM_167": "Catégorie personnalisée 167",
    "CUSTOM_168": "Catégorie personnalisée 168",
    "CUSTOM_169": "Catégorie personnalisée 169",
    "CUSTOM_17": "Catégorie personnalisée 17",
    "CUSTOM_170": "Catégorie personnalisée 170",
    "CUSTOM_171": "Catégorie personnalisée 171",
    "CUSTOM_172": "Catégorie personnalisée 172",
    "CUSTOM_173": "Catégorie personnalisée 173",
    "CUSTOM_174": "Catégorie personnalisée 174",
    "CUSTOM_175": "Catégorie personnalisée 175",
    "CUSTOM_176": "Catégorie personnalisée 176",
    "CUSTOM_177": "Catégorie personnalisée 177",
    "CUSTOM_178": "Catégorie personnalisée 178",
    "CUSTOM_179": "Catégorie personnalisée 179",
    "CUSTOM_18": "Catégorie personnalisée 18",
    "CUSTOM_180": "Catégorie personnalisée 180",
    "CUSTOM_181": "Catégorie personnalisée 181",
    "CUSTOM_182": "Catégorie personnalisée 182",
    "CUSTOM_183": "Catégorie personnalisée 183",
    "CUSTOM_184": "Catégorie personnalisée 184",
    "CUSTOM_185": "Catégorie personnalisée 185",
    "CUSTOM_186": "Catégorie personnalisée 186",
    "CUSTOM_187": "Catégorie personnalisée 187",
    "CUSTOM_188": "Catégorie personnalisée 188",
    "CUSTOM_189": "Catégorie personnalisée 189",
    "CUSTOM_19": "Catégorie personnalisée 19",
    "CUSTOM_190": "Catégorie personnalisée 190",
    "CUSTOM_191": "Catégorie personnalisée 191",
    "CUSTOM_192": "Catégorie personnalisée 192",
    "CUSTOM_193": "Catégorie personnalisée 193",
    "CUSTOM_194": "Catégorie personnalisée 194",
    "CUSTOM_195": "Catégorie personnalisée 195",
    "CUSTOM_196": "Catégorie personnalisée 196",
    "CUSTOM_197": "Catégorie personnalisée 197",
    "CUSTOM_198": "Catégorie personnalisée 198",
    "CUSTOM_199": "Catégorie personnalisée 199",
    "CUSTOM_20": "Catégorie personnalisée 20",
    "CUSTOM_200": "Catégorie personnalisée 200",
    "CUSTOM_201": "Catégorie personnalisée 201",
    "CUSTOM_202": "Catégorie personnalisée 202",
    "CUSTOM_203": "Catégorie personnalisée 203",
    "CUSTOM_204": "Catégorie personnalisée 204",
    "CUSTOM_205": "Catégorie personnalisée 205",
    "CUSTOM_206": "Catégorie personnalisée 206",
    "CUSTOM_207": "Catégorie personnalisée 207",
    "CUSTOM_208": "Catégorie personnalisée 208",
    "CUSTOM_209": "Catégorie personnalisée 209",
    "CUSTOM_21": "Catégorie personnalisée 21",
    "CUSTOM_210": "Catégorie personnalisée 210",
    "CUSTOM_211": "Catégorie personnalisée 211",
    "CUSTOM_212": "Catégorie personnalisée 212",
    "CUSTOM_213": "Catégorie personnalisée 213",
    "CUSTOM_214": "Catégorie personnalisée 214",
    "CUSTOM_215": "Catégorie personnalisée 215",
    "CUSTOM_216": "Catégorie personnalisée 216",
    "CUSTOM_217": "Catégorie personnalisée 217",
    "CUSTOM_218": "Catégorie personnalisée 218",
    "CUSTOM_219": "Catégorie personnalisée 219",
    "CUSTOM_22": "Catégorie personnalisée 22",
    "CUSTOM_220": "Catégorie personnalisée 220",
    "CUSTOM_221": "Catégorie personnalisée 221",
    "CUSTOM_222": "Catégorie personnalisée 222",
    "CUSTOM_223": "Catégorie personnalisée 223",
    "CUSTOM_224": "Catégorie personnalisée 224",
    "CUSTOM_225": "Catégorie personnalisée 225",
    "CUSTOM_226": "Catégorie personnalisée 226",
    "CUSTOM_227": "Catégorie personnalisée 227",
    "CUSTOM_228": "Catégorie personnalisée 228",
    "CUSTOM_229": "Catégorie personnalisée 229",
    "CUSTOM_23": "Catégorie personnalisée 23",
    "CUSTOM_230": "Catégorie personnalisée 230",
    "CUSTOM_231": "Catégorie personnalisée 231",
    "CUSTOM_232": "Catégorie personnalisée 232",
    "CUSTOM_233": "Catégorie personnalisée 233",
    "CUSTOM_234": "Catégorie personnalisée 234",
    "CUSTOM_235": "Catégorie personnalisée 235",
    "CUSTOM_236": "Catégorie personnalisée 236",
    "CUSTOM_237": "Catégorie personnalisée 237",
    "CUSTOM_238": "Catégorie personnalisée 238",
    "CUSTOM_239": "Catégorie personnalisée 239",
    "CUSTOM_24": "Catégorie personnalisée 24",
    "CUSTOM_240": "Catégorie personnalisée 240",
    "CUSTOM_241": "Catégorie personnalisée 241",
    "CUSTOM_242": "Catégorie personnalisée 242",
    "CUSTOM_243": "Catégorie personnalisée 243",
    "CUSTOM_244": "Catégorie personnalisée 244",
    "CUSTOM_245": "Catégorie personnalisée 245",
    "CUSTOM_246": "Catégorie personnalisée 246",
    "CUSTOM_247": "Catégorie personnalisée 247",
    "CUSTOM_248": "Catégorie personnalisée 248",
    "CUSTOM_249": "Catégorie personnalisée 249",
    "CUSTOM_25": "Catégorie personnalisée 25",
    "CUSTOM_250": "Catégorie personnalisée 250",
    "CUSTOM_251": "Catégorie personnalisée 251",
    "CUSTOM_252": "Catégorie personnalisée 252",
    "CUSTOM_253": "Catégorie personnalisée 253",
    "CUSTOM_254": "Catégorie personnalisée 254",
    "CUSTOM_255": "Catégorie personnalisée 255",
    "CUSTOM_256": "Catégorie personnalisée 256",
    "CUSTOM_257": "Catégorie personnalisée 257",
    "CUSTOM_258": "Catégorie personnalisée 258",
    "CUSTOM_259": "Catégorie personnalisée 259",
    "CUSTOM_26": "Catégorie personnalisée 26",
    "CUSTOM_260": "Catégorie personnalisée 260",
    "CUSTOM_261": "Catégorie personnalisée 261",
    "CUSTOM_262": "Catégorie personnalisée 262",
    "CUSTOM_263": "Catégorie personnalisée 263",
    "CUSTOM_264": "Catégorie personnalisée 264",
    "CUSTOM_265": "Catégorie personnalisée 265",
    "CUSTOM_266": "Catégorie personnalisée 266",
    "CUSTOM_267": "Catégorie personnalisée 267",
    "CUSTOM_268": "Catégorie personnalisée 268",
    "CUSTOM_269": "Catégorie personnalisée 269",
    "CUSTOM_27": "Catégorie personnalisée 27",
    "CUSTOM_270": "Catégorie personnalisée 270",
    "CUSTOM_271": "Catégorie personnalisée 271",
    "CUSTOM_272": "Catégorie personnalisée 272",
    "CUSTOM_273": "Catégorie personnalisée 273",
    "CUSTOM_274": "Catégorie personnalisée 274",
    "CUSTOM_275": "Catégorie personnalisée 275",
    "CUSTOM_276": "Catégorie personnalisée 276",
    "CUSTOM_277": "Catégorie personnalisée 277",
    "CUSTOM_278": "Catégorie personnalisée 278",
    "CUSTOM_279": "Catégorie personnalisée 279",
    "CUSTOM_28": "Catégorie personnalisée 28",
    "CUSTOM_280": "Catégorie personnalisée 280",
    "CUSTOM_281": "Catégorie personnalisée 281",
    "CUSTOM_282": "Catégorie personnalisée 282",
    "CUSTOM_283": "Catégorie personnalisée 283",
    "CUSTOM_284": "Catégorie personnalisée 284",
    "CUSTOM_285": "Catégorie personnalisée 285",
    "CUSTOM_286": "Catégorie personnalisée 286",
    "CUSTOM_287": "Catégorie personnalisée 287",
    "CUSTOM_288": "Catégorie personnalisée 288",
    "CUSTOM_289": "Catégorie personnalisée 289",
    "CUSTOM_29": "Catégorie personnalisée 29",
    "CUSTOM_290": "Catégorie personnalisée 290",
    "CUSTOM_291": "Catégorie personnalisée 291",
    "CUSTOM_292": "Catégorie personnalisée 292",
    "CUSTOM_293": "Catégorie personnalisée 293",
    "CUSTOM_294": "Catégorie personnalisée 294",
    "CUSTOM_295": "Catégorie personnalisée 295",
    "CUSTOM_296": "Catégorie personnalisée 296",
    "CUSTOM_297": "Catégorie personnalisée 297",
    "CUSTOM_298": "Catégorie personnalisée 298",
    "CUSTOM_299": "Catégorie personnalisée 299",
    "CUSTOM_30": "Catégorie personnalisée 30",
    "CUSTOM_300": "Catégorie personnalisée 300",
    "CUSTOM_301": "Catégorie personnalisée 301",
    "CUSTOM_302": "Catégorie personnalisée 302",
    "CUSTOM_303": "Catégorie personnalisée 303",
    "CUSTOM_304": "Catégorie personnalisée 304",
    "CUSTOM_305": "Catégorie personnalisée 305",
    "CUSTOM_306": "Catégorie personnalisée 306",
    "CUSTOM_307": "Catégorie personnalisée 307",
    "CUSTOM_308": "Catégorie personnalisée 308",
    "CUSTOM_309": "Catégorie personnalisée 309",
    "CUSTOM_31": "Catégorie personnalisée 31",
    "CUSTOM_310": "Catégorie personnalisée 310",
    "CUSTOM_311": "Catégorie personnalisée 311",
    "CUSTOM_312": "Catégorie personnalisée 312",
    "CUSTOM_313": "Catégorie personnalisée 313",
    "CUSTOM_314": "Catégorie personnalisée 314",
    "CUSTOM_315": "Catégorie personnalisée 315",
    "CUSTOM_316": "Catégorie personnalisée 316",
    "CUSTOM_317": "Catégorie personnalisée 317",
    "CUSTOM_318": "Catégorie personnalisée 318",
    "CUSTOM_319": "Catégorie personnalisée 319",
    "CUSTOM_32": "Catégorie personnalisée 32",
    "CUSTOM_320": "Catégorie personnalisée 320",
    "CUSTOM_321": "Catégorie personnalisée 321",
    "CUSTOM_322": "Catégorie personnalisée 322",
    "CUSTOM_323": "Catégorie personnalisée 323",
    "CUSTOM_324": "Catégorie personnalisée 324",
    "CUSTOM_325": "Catégorie personnalisée 325",
    "CUSTOM_326": "Catégorie personnalisée 326",
    "CUSTOM_327": "Catégorie personnalisée 327",
    "CUSTOM_328": "Catégorie personnalisée 328",
    "CUSTOM_329": "Catégorie personnalisée 329",
    "CUSTOM_33": "Catégorie personnalisée 33",
    "CUSTOM_330": "Catégorie personnalisée 330",
    "CUSTOM_331": "Catégorie personnalisée 331",
    "CUSTOM_332": "Catégorie personnalisée 332",
    "CUSTOM_333": "Catégorie personnalisée 333",
    "CUSTOM_334": "Catégorie personnalisée 334",
    "CUSTOM_335": "Catégorie personnalisée 335",
    "CUSTOM_336": "Catégorie personnalisée 336",
    "CUSTOM_337": "Catégorie personnalisée 337",
    "CUSTOM_338": "Catégorie personnalisée 338",
    "CUSTOM_339": "Catégorie personnalisée 339",
    "CUSTOM_34": "Catégorie personnalisée 34",
    "CUSTOM_340": "Catégorie personnalisée 340",
    "CUSTOM_341": "Catégorie personnalisée 341",
    "CUSTOM_342": "Catégorie personnalisée 342",
    "CUSTOM_343": "Catégorie personnalisée 343",
    "CUSTOM_344": "Catégorie personnalisée 344",
    "CUSTOM_345": "Catégorie personnalisée 345",
    "CUSTOM_346": "Catégorie personnalisée 346",
    "CUSTOM_347": "Catégorie personnalisée 347",
    "CUSTOM_348": "Catégorie personnalisée 348",
    "CUSTOM_349": "Catégorie personnalisée 349",
    "CUSTOM_35": "Catégorie personnalisée 35",
    "CUSTOM_350": "Catégorie personnalisée 350",
    "CUSTOM_351": "Catégorie personnalisée 351",
    "CUSTOM_352": "Catégorie personnalisée 352",
    "CUSTOM_353": "Catégorie personnalisée 353",
    "CUSTOM_354": "Catégorie personnalisée 354",
    "CUSTOM_355": "Catégorie personnalisée 355",
    "CUSTOM_356": "Catégorie personnalisée 356",
    "CUSTOM_357": "Catégorie personnalisée 357",
    "CUSTOM_358": "Catégorie personnalisée 358",
    "CUSTOM_359": "Catégorie personnalisée 359",
    "CUSTOM_36": "Catégorie personnalisée 36",
    "CUSTOM_360": "Catégorie personnalisée 360",
    "CUSTOM_361": "Catégorie personnalisée 361",
    "CUSTOM_362": "Catégorie personnalisée 362",
    "CUSTOM_363": "Catégorie personnalisée 363",
    "CUSTOM_364": "Catégorie personnalisée 364",
    "CUSTOM_365": "Catégorie personnalisée 365",
    "CUSTOM_366": "Catégorie personnalisée 366",
    "CUSTOM_367": "Catégorie personnalisée 367",
    "CUSTOM_368": "Catégorie personnalisée 368",
    "CUSTOM_369": "Catégorie personnalisée 369",
    "CUSTOM_37": "Catégorie personnalisée 37",
    "CUSTOM_370": "Catégorie personnalisée 370",
    "CUSTOM_371": "Catégorie personnalisée 371",
    "CUSTOM_372": "Catégorie personnalisée 372",
    "CUSTOM_373": "Catégorie personnalisée 373",
    "CUSTOM_374": "Catégorie personnalisée 374",
    "CUSTOM_375": "Catégorie personnalisée 375",
    "CUSTOM_376": "Catégorie personnalisée 376",
    "CUSTOM_377": "Catégorie personnalisée 377",
    "CUSTOM_378": "Catégorie personnalisée 378",
    "CUSTOM_379": "Catégorie personnalisée 379",
    "CUSTOM_38": "Catégorie personnalisée 38",
    "CUSTOM_380": "Catégorie personnalisée 380",
    "CUSTOM_381": "Catégorie personnalisée 381",
    "CUSTOM_382": "Catégorie personnalisée 382",
    "CUSTOM_383": "Catégorie personnalisée 383",
    "CUSTOM_384": "Catégorie personnalisée 384",
    "CUSTOM_385": "Catégorie personnalisée 385",
    "CUSTOM_386": "Catégorie personnalisée 386",
    "CUSTOM_387": "Catégorie personnalisée 387",
    "CUSTOM_388": "Catégorie personnalisée 388",
    "CUSTOM_389": "Catégorie personnalisée 389",
    "CUSTOM_39": "Catégorie personnalisée 39",
    "CUSTOM_390": "Catégorie personnalisée 390",
    "CUSTOM_391": "Catégorie personnalisée 391",
    "CUSTOM_392": "Catégorie personnalisée 392",
    "CUSTOM_393": "Catégorie personnalisée 393",
    "CUSTOM_394": "Catégorie personnalisée 394",
    "CUSTOM_395": "Catégorie personnalisée 395",
    "CUSTOM_396": "Catégorie personnalisée 396",
    "CUSTOM_397": "Catégorie personnalisée 397",
    "CUSTOM_398": "Catégorie personnalisée 398",
    "CUSTOM_399": "Catégorie personnalisée 399",
    "CUSTOM_40": "Catégorie personnalisée 40",
    "CUSTOM_400": "Catégorie personnalisée 400",
    "CUSTOM_401": "Catégorie personnalisée 401",
    "CUSTOM_402": "Catégorie personnalisée 402",
    "CUSTOM_403": "Catégorie personnalisée 403",
    "CUSTOM_404": "Catégorie personnalisée 404",
    "CUSTOM_405": "Catégorie personnalisée 405",
    "CUSTOM_406": "Catégorie personnalisée 406",
    "CUSTOM_407": "Catégorie personnalisée 407",
    "CUSTOM_408": "Catégorie personnalisée 408",
    "CUSTOM_409": "Catégorie personnalisée 409",
    "CUSTOM_41": "Catégorie personnalisée 41",
    "CUSTOM_410": "Catégorie personnalisée 410",
    "CUSTOM_411": "Catégorie personnalisée 411",
    "CUSTOM_412": "Catégorie personnalisée 412",
    "CUSTOM_413": "Catégorie personnalisée 413",
    "CUSTOM_414": "Catégorie personnalisée 414",
    "CUSTOM_415": "Catégorie personnalisée 415",
    "CUSTOM_416": "Catégorie personnalisée 416",
    "CUSTOM_417": "Catégorie personnalisée 417",
    "CUSTOM_418": "Catégorie personnalisée 418",
    "CUSTOM_419": "Catégorie personnalisée 419",
    "CUSTOM_42": "Catégorie personnalisée 42",
    "CUSTOM_420": "Catégorie personnalisée 420",
    "CUSTOM_421": "Catégorie personnalisée 421",
    "CUSTOM_422": "Catégorie personnalisée 422",
    "CUSTOM_423": "Catégorie personnalisée 423",
    "CUSTOM_424": "Catégorie personnalisée 424",
    "CUSTOM_425": "Catégorie personnalisée 425",
    "CUSTOM_426": "Catégorie personnalisée 426",
    "CUSTOM_427": "Catégorie personnalisée 427",
    "CUSTOM_428": "Catégorie personnalisée 428",
    "CUSTOM_429": "Catégorie personnalisée 429",
    "CUSTOM_43": "Catégorie personnalisée 43",
    "CUSTOM_430": "Catégorie personnalisée 430",
    "CUSTOM_431": "Catégorie personnalisée 431",
    "CUSTOM_432": "Catégorie personnalisée 432",
    "CUSTOM_433": "Catégorie personnalisée 433",
    "CUSTOM_434": "Catégorie personnalisée 434",
    "CUSTOM_435": "Catégorie personnalisée 435",
    "CUSTOM_436": "Catégorie personnalisée 436",
    "CUSTOM_437": "Catégorie personnalisée 437",
    "CUSTOM_438": "Catégorie personnalisée 438",
    "CUSTOM_439": "Catégorie personnalisée 439",
    "CUSTOM_44": "Catégorie personnalisée 44",
    "CUSTOM_440": "Catégorie personnalisée 440",
    "CUSTOM_441": "Catégorie personnalisée 441",
    "CUSTOM_442": "Catégorie personnalisée 442",
    "CUSTOM_443": "Catégorie personnalisée 443",
    "CUSTOM_444": "Catégorie personnalisée 444",
    "CUSTOM_445": "Catégorie personnalisée 445",
    "CUSTOM_446": "Catégorie personnalisée 446",
    "CUSTOM_447": "Catégorie personnalisée 447",
    "CUSTOM_448": "Catégorie personnalisée 448",
    "CUSTOM_449": "Catégorie personnalisée 449",
    "CUSTOM_45": "Catégorie personnalisée 45",
    "CUSTOM_450": "Catégorie personnalisée 450",
    "CUSTOM_451": "Catégorie personnalisée 451",
    "CUSTOM_452": "Catégorie personnalisée 452",
    "CUSTOM_453": "Catégorie personnalisée 453",
    "CUSTOM_454": "Catégorie personnalisée 454",
    "CUSTOM_455": "Catégorie personnalisée 455",
    "CUSTOM_456": "Catégorie personnalisée 456",
    "CUSTOM_457": "Catégorie personnalisée 457",
    "CUSTOM_458": "Catégorie personnalisée 458",
    "CUSTOM_459": "Catégorie personnalisée 459",
    "CUSTOM_46": "Catégorie personnalisée 46",
    "CUSTOM_460": "Catégorie personnalisée 460",
    "CUSTOM_461": "Catégorie personnalisée 461",
    "CUSTOM_462": "Catégorie personnalisée 462",
    "CUSTOM_463": "Catégorie personnalisée 463",
    "CUSTOM_464": "Catégorie personnalisée 464",
    "CUSTOM_465": "Catégorie personnalisée 465",
    "CUSTOM_466": "Catégorie personnalisée 466",
    "CUSTOM_467": "Catégorie personnalisée 467",
    "CUSTOM_468": "Catégorie personnalisée 468",
    "CUSTOM_469": "Catégorie personnalisée 469",
    "CUSTOM_47": "Catégorie personnalisée 47",
    "CUSTOM_470": "Catégorie personnalisée 470",
    "CUSTOM_471": "Catégorie personnalisée 471",
    "CUSTOM_472": "Catégorie personnalisée 472",
    "CUSTOM_473": "Catégorie personnalisée 473",
    "CUSTOM_474": "Catégorie personnalisée 474",
    "CUSTOM_475": "Catégorie personnalisée 475",
    "CUSTOM_476": "Catégorie personnalisée 476",
    "CUSTOM_477": "Catégorie personnalisée 477",
    "CUSTOM_478": "Catégorie personnalisée 478",
    "CUSTOM_479": "Catégorie personnalisée 479",
    "CUSTOM_48": "Catégorie personnalisée 48",
    "CUSTOM_480": "Catégorie personnalisée 480",
    "CUSTOM_481": "Catégorie personnalisée 481",
    "CUSTOM_482": "Catégorie personnalisée 482",
    "CUSTOM_483": "Catégorie personnalisée 483",
    "CUSTOM_484": "Catégorie personnalisée 484",
    "CUSTOM_485": "Catégorie personnalisée 485",
    "CUSTOM_486": "Catégorie personnalisée 486",
    "CUSTOM_487": "Catégorie personnalisée 487",
    "CUSTOM_488": "Catégorie personnalisée 488",
    "CUSTOM_489": "Catégorie personnalisée 489",
    "CUSTOM_49": "Catégorie personnalisée 49",
    "CUSTOM_490": "Catégorie personnalisée 490",
    "CUSTOM_491": "Catégorie personnalisée 491",
    "CUSTOM_492": "Catégorie personnalisée 492",
    "CUSTOM_493": "Catégorie personnalisée 493",
    "CUSTOM_494": "Catégorie personnalisée 494",
    "CUSTOM_495": "Catégorie personnalisée 495",
    "CUSTOM_496": "Catégorie personnalisée 496",
    "CUSTOM_497": "Catégorie personnalisée 497",
    "CUSTOM_498": "Catégorie personnalisée 498",
    "CUSTOM_499": "Catégorie personnalisée 499",
    "CUSTOM_50": "Catégorie personnalisée 50",
    "CUSTOM_500": "Catégorie personnalisée 500",
    "CUSTOM_501": "Catégorie personnalisée 501",
    "CUSTOM_502": "Catégorie personnalisée 502",
    "CUSTOM_503": "Catégorie personnalisée 503",
    "CUSTOM_504": "Catégorie personnalisée 504",
    "CUSTOM_505": "Catégorie personnalisée 505",
    "CUSTOM_506": "Catégorie personnalisée 506",
    "CUSTOM_507": "Catégorie personnalisée 507",
    "CUSTOM_508": "Catégorie personnalisée 508",
    "CUSTOM_509": "Catégorie personnalisée 509",
    "CUSTOM_51": "Catégorie personnalisée 51",
    "CUSTOM_510": "Catégorie personnalisée 510",
    "CUSTOM_511": "Catégorie personnalisée 511",
    "CUSTOM_512": "Catégorie personnalisée 512",
    "CUSTOM_52": "Catégorie personnalisée 52",
    "CUSTOM_53": "Catégorie personnalisée 53",
    "CUSTOM_54": "Catégorie personnalisée 54",
    "CUSTOM_55": "Catégorie personnalisée 55",
    "CUSTOM_56": "Catégorie personnalisée 56",
    "CUSTOM_57": "Catégorie personnalisée 57",
    "CUSTOM_58": "Catégorie personnalisée 58",
    "CUSTOM_59": "Catégorie personnalisée 59",
    "CUSTOM_60": "Catégorie personnalisée 60",
    "CUSTOM_61": "Catégorie personnalisée 61",
    "CUSTOM_62": "Catégorie personnalisée 62",
    "CUSTOM_63": "Catégorie personnalisée 63",
    "CUSTOM_64": "Catégorie personnalisée 64",
    "CUSTOM_65": "Catégorie personnalisée 65",
    "CUSTOM_66": "Catégorie personnalisée 66",
    "CUSTOM_67": "Catégorie personnalisée 67",
    "CUSTOM_68": "Catégorie personnalisée 68",
    "CUSTOM_69": "Catégorie personnalisée 69",
    "CUSTOM_70": "Catégorie personnalisée 70",
    "CUSTOM_71": "Catégorie personnalisée 71",
    "CUSTOM_72": "Catégorie personnalisée 72",
    "CUSTOM_73": "Catégorie personnalisée 73",
    "CUSTOM_74": "Catégorie personnalisée 74",
    "CUSTOM_75": "Catégorie personnalisée 75",
    "CUSTOM_76": "Catégorie personnalisée 76",
    "CUSTOM_77": "Catégorie personnalisée 77",
    "CUSTOM_78": "Catégorie personnalisée 78",
    "CUSTOM_79": "Catégorie personnalisée 79",
    "CUSTOM_80": "Catégorie personnalisée 80",
    "CUSTOM_81": "Catégorie personnalisée 81",
    "CUSTOM_82": "Catégorie personnalisée 82",
    "CUSTOM_83": "Catégorie personnalisée 83",
    "CUSTOM_84": "Catégorie personnalisée 84",
    "CUSTOM_85": "Catégorie personnalisée 85",
    "CUSTOM_86": "Catégorie personnalisée 86",
    "CUSTOM_87": "Catégorie personnalisée 87",
    "CUSTOM_88": "Catégorie personnalisée 88",
    "CUSTOM_89": "Catégorie personnalisée 89",
    "CUSTOM_90": "Catégorie personnalisée 90",
    "CUSTOM_91": "Catégorie personnalisée 91",
    "CUSTOM_92": "Catégorie personnalisée 92",
    "CUSTOM_93": "Catégorie personnalisée 93",
    "CUSTOM_94": "Catégorie personnalisée 94",
    "CUSTOM_95": "Catégorie personnalisée 95",
    "CUSTOM_96": "Catégorie personnalisée 96",
    "CUSTOM_97": "Catégorie personnalisée 97",
    "CUSTOM_98": "Catégorie personnalisée 98",
    "CUSTOM_99": "Catégorie personnalisée 99",
    "CUSTOM_AUP_FREQUENCY": "Fréquence AUP personnalisée (jours)",
    "CUSTOM_DNS_SERVER": "Serveur DNS personnalisé",
    "CUSTOM_OPTION": "Option personnalisée",
    "CUSTOM": "Personnalisé",
    "CUSTOMIZE_COLS": "Personnaliser les colonnes",
    "CUSTOMIZE_COLUMNS": "Personnaliser les colonnes",
    "CYPRUS_ASIA_NICOSIA": "Asie/Nicosie",
    "CYPRUS": "Chypre",
    "CZECH_REPUBLIC_EUROPE_PRAGUE": "Europe/Prague",
    "CZECH_REPUBLIC": "République tchèque",
    "CZECHIA": "Tchéquie",
    "DASHBOARD": "Tableau de bord",
    "Data Centers": "Centres de données",
    "DATA_CENTER": "Data center",
    "DATA_CENTERS": "Centres de données",
    "DATA_COLLECTION": "Collecte de données",
    "DATA_CONNECTION_FAIL": "Échec de la connexion aux données SVPN.",
    "DATA_TYPE": "Type de données",
    "DATACENTER": "Data center",
    "DAYS": "Jours",
    "DC": "Public Service Edge",
    "DEDICATED_BANDWIDTH": "Bande passante dédiée",
    "DEFAUL_GATEWAY_IP_ADDRESS": "Adresse IP de passerelle par défaut",
    "DEFAULT_AWS_REGIONS": "Toutes les régions",
    "DEFAULT_AZURE_REGIONS": "Aucun",
    "DEFAULT_GATEWAY": "Passerelle par défaut",
    "DEFAULT_GW": "Passerelle par défaut",
    "DEFAULT_LEASE_TIME": "Durée de bail par défaut (s)",
    "DEFAULT_NAMESPACE": "Par défaut",
    "DEFAULT_PREFIX": "Préfixe par défaut",
    "DEFAULT_REGIONS": "Tous les emplacements",
    "DEFAULT_ROUTE_CAN_NOT_BE_SET_AS_STATIC_ROUTE": "La route par défaut n'a pas pu être utilisée comme route statique.",
    "DEFAULT": "Valeur par défaut",
    "DEFINITION": "Définition",
    "DELETE_5G_DEPLOYMENT_CONFIGURATION": "Voulez-vous vraiment supprimer cette configuration du déploiement ? Les déploiements ne peuvent être supprimés que s'ils ne sont associés à aucun groupe de connecteurs. Ces modifications sont irréversibles. ",
    "DELETE_5G_USER_PLANE": "Voulez-vous vraiment supprimer cette fonction d'instance utilisateur ? Ces modifications sont irréversibles. ",
    "DELETE_ACCOUNT_DESCRIPTION": "Si vous supprimez le compte, Zscaler supprimera toutes les informations d'accès à votre compte. Vous devrez à nouveau fournir les autorisations si vous décidez d'ajouter à nouveau ce compte.",
    "DELETE_ACCOUNT": "Supprimer le compte",
    "DELETE_ACCOUNTS": "Supprimer les comptes",
    "DELETE_ADMIN_MESSAGE": "Voulez-vous également supprimer cet administrateur de la liste des utilisateurs ?{1}Cette action {2}ne peut pas être annulée.{3}",
    "DELETE_API_KEY_CONFIRMATION_MESSAGE": "La suppression de la clé API l’invalide immédiatement. Cela ne peut pas être annulé.",
    "DELETE_API_KEY_CONFIRMATION_TITLE": "Supprimer la clé API",
    "DELETE_API_KEY_TOOLTIP": "Supprimer la clé API",
    "DELETE_APPLICATION": "Supprimer l'application",
    "DELETE_BC_CONFIRMATION": "Voulez-vous vraiment supprimer le Branch Connector suivant ? Les modifications ne peuvent être annulées.",
    "DELETE_BC_GROUP_CONFIRMATION_ALERT": "Cela ne supprime pas la machine virtuelle.Veillez à supprimer les ressources séparément.",
    "DELETE_BC_GROUP_CONFIRMATION": "Voulez-vous vraiment supprimer le groupe Branch Connector suivant ? Les modifications ne peuvent être annulées.",
    "DELETE_BC_GROUP": "Supprimer le groupe Branch Connector",
    "DELETE_CC_CONFIRMATION": "Voulez-vous vraiment supprimer le Cloud Connector suivant ? Les modifications ne peuvent être annulées.",
    "DELETE_CC_GROUP_CONFIRMATION_ALERT": "Cela ne supprime pas la machine virtuelle dans le cloud public. Veillez à supprimer les ressources séparément.",
    "DELETE_CC_GROUP_CONFIRMATION": "Voulez-vous vraiment supprimer le groupe de Cloud Connectors suivant ? Les modifications ne peuvent être annulées.",
    "DELETE_CC_GROUP": "Supprimer le groupe de Cloud Connectors",
    "DELETE_CONFIRMATION_MESSAGE": "Veuillez confirmer que vous souhaitez supprimer la ou les ressources.",
    "DELETE_CONFIRMATION_MESSAGE1": "Voulez-vous vraiment supprimer cette ressource ?",
    "DELETE_CONFIRMATION_MESSAGE2": "Voulez-vous vraiment supprimer cette ressource ?",
    "DELETE_CONFIRMATION": "Confirmation de suppression",
    "DELETE_DATA_OLLECTION_TEXT": "Si vous supprimez le compte, Zscaler supprimera toutes les informations d'accès à votre compte. Vous devrez à nouveau fournir les autorisations si vous décidez d'ajouter à nouveau ce compte.",
    "DELETE_DATA_OLLECTION": "Supprimer la collecte de données",
    "DELETE_GROUP_CONFIRMATION": "Confirmation de suppression du groupe",
    "DELETE_GROUP_MESSAGE_WITH_CC_GROUP": "Ce groupe de comptes est associé à {1} compte(s) et à {2} groupe(s) Cloud Connector. La suppression de ce groupe de comptes empêchera le ou les groupes {2} Cloud Connector d’utiliser les balises de charge de travail du ou des comptes {1}.",
    "DELETE_GROUP_MESSAGE": "{1}Voulez-vous vraiment supprimer ce groupe ?{2}\n\nLa suppression de ce groupe supprimera ce groupe de toutes les politiques concernées. Veillez à vérifier l’impact avant de supprimer le groupe.",
    "DELETE_GROUP": "Supprimer le groupe",
    "DELETE_INTERFACE_TEXT": "Voulez-vous vraiment supprimer la configuration d'interface ?Cette action est irréversible.",
    "DELETE_INTERFACE": "Supprimer l’interface",
    "DELETE_PORT_TEXT": "Voulez-vous vraiment supprimer le port et sa configuration d'interface ?Cette action est irréversible.",
    "DELETE_PORT": "Supprimer le port",
    "DELETE_TENANT_DESCRIPTION": "Si vous supprimez le locataire, Zscaler supprime toutes les informations d'accès qui lui sont associées. Si vous décidez de réajouter le locataire, vous devez fournir les autorisations à nouveau.",
    "DELETE_TENANT": "Supprimer le locataire",
    "DELETE_THIS_ITEM": "Supprimez cet élément.",
    "DELETE_VDI_GROUP_TEXT": "Si vous supprimez le compte, Zscaler supprimera toutes les informations d'accès à ce groupe. Vous devrez à nouveau créer le groupe si vous décidez d'ajouter à nouveau ce compte.",
    "DELETE_VDI_GROUP": "Supprimer le groupe VDI",
    "DELETE_ZERO_TRUST_GATEWAY": "Supprimer la Passerelle Zero trust",
    "DELETE_ZTG_DESCRIPTION": "Si vous supprimez la passerelle Zero trust, Zscaler supprime toutes les informations d'accès qui sont associées. Si vous décidez de réajouter la passerelle, vous devez fournir les autorisations à nouveau.",
    "DELETE": "Supprimer",
    "DELETING": "Suppression",
    "DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA": "Afrique/Kinshasa",
    "DENIED": "Refusé",
    "DENMARK_EUROPE_COPENHAGEN": "Europe/Copenhague",
    "DENMARK": "Danemark",
    "DEPARTMENT": "Département",
    "DEPLOY_AS_GATEWAY": "Déployer comme passerelle",
    "DEPLOY_NSS_VIRTUAL_APPLIANCE": "Déployer NSS Virtual Appliance",
    "DEPLOYED_FILTERED": "Déployé (filtré)",
    "DEPLOYED_OTHER_REGION": "Déployé dans d'autres régions",
    "DEPLOYED": "Déployé(e)",
    "DEPLOYMENT_CONFIGURATION_NAME": "Nom de la configuration de déploiement",
    "DEPLOYMENT_CONFIGURATION_WITH_ZPA": "Modèle de déploiement de démarrage avec équilibreur de charge",
    "DEPLOYMENT_CONFIGURATION": "Modèle de déploiement Starter",
    "DEPLOYMENT_DETAILS": "Détails du déploiement",
    "DEPLOYMENT_NAME": "Nom du déploiement",
    "DEPLOYMENT_STATUS": "Détails du déploiement",
    "DEPLOYMENT_TEMPLATES_DEPRECATED": "Les modèles de déploiement sont disponibles sur la page publique {0}GitHub{1} de Zscaler aux liens mentionnés ci-dessous. Les modifications sont suivies dans ces pages. Pour en savoir plus, consultez le {2}portail d'aide Zscaler.{3}",
    "DEPLOYMENT_TEMPLATES": "Modèles de Déploiement",
    "DEPLOYMENT_TYPE": "Type de Déploiement",
    "DESCRIPTION_MAX_LIMIT_ERROR": "Ce champ ne peut pas contenir plus de 10240 caractères",
    "DESCRIPTION_OPTIONAL": "Description facultative",
    "DESCRIPTION_PARENTHESIS_OPTIONAL": "Description (facultatif)",
    "DESCRIPTION": "Description",
    "DESELECT_ALL": "Tout désélectionner",
    "DESIRED_CAPACITY": "Capacité souhaitée",
    "DESTINATION_ADDRESSES": "Adresses de destination",
    "DESTINATION_COUNTRIES": "Pays de destination",
    "DESTINATION_COUNTRY": "Pays de destination",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Domaines de destination",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Groupe de domaines de destination",
    "DESTINATION_GROUPS": "Groupes de destination",
    "DESTINATION_IP_AND_FQDN_ACCDRESSES": "Adresse IP de destination / FQDN",
    "DESTINATION_IP_AND_FQDN_GROUPS": "Groupe IP / FQDN de destination",
    "DESTINATION_IP_AND_FQDN": "Adresse IP de destination / FQDN",
    "DESTINATION_IP_GROUP": "Groupe de destination",
    "DESTINATION_IP_GROUPS": "Groupes IP de destination",
    "DESTINATION_IP": "IP de destination",
    "DESTINATION_IPV4_GROUPS": "Groupes IPv4 de destination",
    "DESTINATION_STATUS": "État de la destination",
    "DESTINATION": "Destination",
    "DEVICE_APP_VER": "Version de l'application de l'appareil",
    "DEVICE_CRITERIA": "Critères de l'appareil",
    "DEVICE_DETAILS": "Détails de l'appareil",
    "DEVICE_GROUP_TYPE": "Type de groupe d'appareils",
    "DEVICE_HOST_NAME": "Nom d'hôte de l'appareil",
    "DEVICE_ID": "ID d'appareil",
    "DEVICE_INFO": "Informations sur l’appareil",
    "DEVICE_METRICS": "Ressource",
    "DEVICE_MODEL": "Modèle d'appareil",
    "DEVICE_NAME": "Nom de l'appareil",
    "DEVICE_OS_TYPE": "Type d'OS de l'appareil",
    "DEVICE_OS_VER": "Version OS de l'appareil",
    "DEVICE_OWNER": "Propriétaire de l'appareil",
    "DEVICE_PLATFORM": "Plate-forme de l'appareil",
    "DEVICE_PORT": "Port de l'appareil",
    "DEVICE_SELECTION": "Sélection de l'appareil",
    "DEVICE_SERIAL_NO": "Numéro de série de l'appareil",
    "DEVICE_SERIAL_NUMBER": "Numéro de série de l'appareil",
    "DEVICE_TYPE": "Type d'appareil",
    "DEVICES_CRITERIA_TEXT": "Sélectionnez les critères qui seront utilisés pour regrouper les appareils VDI.",
    "DEVICES_CRITERIA": "Critères des appareils",
    "DEVICES": "Appareils",
    "DEVO": "Devo",
    "DHCP_ADDRESS_RANGE": "Plage d’adresses DHCP",
    "DHCP_DESC": "Le protocole DHCP permet de configurer automatiquement les paramètres réseau d'une station.",
    "DHCP_MANAGEMENT_IP": "IP de gestion DHCP",
    "DHCP_OPTIONS": "Options DHCP",
    "DHCP_SERVER": "Serveur DHCP",
    "DHCP_SERVICE_IP": "IP du service DHCP",
    "DHCP": "DHCP",
    "DINING_AND_RESTAURANT": "Restauration",
    "DIRECT_THROUGHPUT_KBPS_SESSION": "Direct (Débit kbit/s/session)",
    "DIRECT": "DIRECT",
    "DIRECTION": "TS Direction",
    "DIRECTORY_ID": "Identifiant du répertoire",
    "DISABLE_BRANCH_CONNECTOR_CONFIRMATION": "Voulez-vous vraiment supprimer ce Branch Connector ?",
    "DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Voulez-vous vraiment désactiver ce groupe Branch Connector ? Ceci entraînera la désactivation de tous les {0} Branch Connectors qui appartiennent à ce groupe.",
    "DISABLE_BRANCH_CONNECTOR_GROUP": "Désactiver le groupe Branch Connector",
    "DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Voulez-vous vraiment désactiver tous les {0} Branch Connectors sélectionnés ?",
    "DISABLE_BRANCH_CONNECTOR_SELECTED": "Désactiver tous les Branch Connectors sélectionnés",
    "DISABLE_BRANCH_CONNECTOR": "Désactiver le Branch Connector",
    "DISABLE_CLOUD_CONNECTOR_CONFIRMATION": "Voulez-vous vraiment supprimer ce Cloud Connector ?",
    "DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Voulez-vous vraiment désactiver ce groupe de Cloud Connectors ? Cela désactiverait tous les {0} Cloud Connectors appartenant à ce groupe",
    "DISABLE_CLOUD_CONNECTOR_GROUP": "Désactiver le groupe de Cloud Connectors",
    "DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Voulez-vous vraiment désactiver tous les {0} Cloud Connectors sélectionnés ?",
    "DISABLE_CLOUD_CONNECTOR_SELECTED": "Désactiver tous les Cloud Connectors sélectionnés",
    "DISABLE_CLOUD_CONNECTOR": "Désactiver le Cloud Connector",
    "DISABLE_DATA_COLLECTION_DESCRIPTION": "La désactivation de la synchronisation empêchera Zscaler de récupérer les dernières informations sur les balises. Elle exclura en outre les ressources de ce compte de l'inclusion dans les groupes de charge de travail.",
    "DISABLE_DATA_COLLECTION": "Désactiver la collecte de données",
    "DISABLE_POLICY_INFORMATION": "Désactiver les informations de transfert",
    "DISABLE_TIPS_MESSAGE": "Mon profil, pour désactiver les informations de stratégie.",
    "DISABLE": "Désactiver",
    "DISABLED": "Désactivé",
    "DISABLING": "Désactivation",
    "DISCOVERY_SERVICE_STATUS": "Statut du service de découverte",
    "DISCUSSION_FORUMS": "Forums de discussion",
    "DISK_STORAGE": "Stockage de disque",
    "DISMISS": "Rejeter",
    "DISPLAY": "Affichage",
    "DJIBOUTI_AFRICA_DJIBOUTI": "Afrique/Djibouti",
    "DJIBOUTI": "Djibouti",
    "DNS_ACTIONS": "Actions",
    "DNS_ACTIVITY": "Activité DNS",
    "DNS_APPLICATION_CATEGORIES": "Tunnel DNS et Catégories d'application de réseau",
    "DNS_APPLICATION_CATEGORY": "Catégorie d'application DNS",
    "DNS_APPLICATION_GROUP": "Groupe d'applications DNS",
    "DNS_APPLICATION": "Application DNS",
    "DNS_BLOCKED_TRAFFIC_OVERVIEW": "Vue d’ensemble du trafic DNS bloqué",
    "DNS_CACHE": "Cache DNS",
    "DNS_CONTROL_RECOMMENDED_POLICY": "Politique recommandée de contrôle DNS",
    "DNS_CONTROL_TIPS_DESC": "Vous pouvez définir des règles qui contrôlent les demandes et les réponses du DNS.",
    "DNS_CONTROL_TIPS_TITLE": "Configurer la stratégie de contrôle du DNS",
    "DNS_CONTROL": "Contrôle de DNS",
    "DNS_DESC": " Le protocole DNS permet de traduire les noms Internet (www.site.com) en adresses IP, et inversement.",
    "DNS_DESTINATION": "Destination",
    "DNS_DETAILS": "Détails du DNS",
    "DNS_ERROR_CODE": "Code d'erreur DNS",
    "DNS_ERROR_STATUS": "Statut de l'erreur DNS",
    "DNS_FILTERING_RULE": "Règle de filtrage DNS",
    "DNS_FILTERING": "Filtrage DNS",
    "DNS_FILTERS": "Filtrage DNS",
    "DNS_GATEWAY": "Passerelle DNS",
    "DNS_INSIGHTS": "Informations DNS",
    "DNS_IPV6_CHANGE": "Autoriser le traitement correct des demandes IPv6 à DNS",
    "DNS_MONITOR": "Surveillance des DNS",
    "DNS_NETWORK_APPLICATION": "Tunnels DNS et applications réseau",
    "DNS_NW_APP_CATEGORY": "Tunnel DNS et Catégories d'application de réseau",
    "DNS_NW_APP": "Tunnels DNS et applications réseau",
    "DNS_OVER_HTTPS": "Services DNS via HTTPS",
    "DNS_OVERVIEW": "Vue d’ensemble du DNS",
    "DNS_POLICIES": "Stratégies DNS",
    "DNS_POLICY": "Stratégie DNS",
    "DNS_REQ_RESP_ACTION": "Action",
    "DNS_REQ_TYPE": "Type de demande DNS",
    "DNS_REQUEST_TYPE": "Type de demande DNS",
    "DNS_REQUEST_TYPES": "Types de demande DNS",
    "DNS_RES_TYPE": "Type de réponse DNS",
    "DNS_RESOLVED_BY_ZPA": "Résolu par ZPA",
    "DNS_RESOLVER": "Résolveur",
    "DNS_RESPONSE_CODES": "Codes de réponse DNS",
    "DNS_RESPONSE_TYPE": "Type de réponse DNS",
    "DNS_RESPONSE": "Réponse DNS",
    "DNS_RESPONSES": "Réponses DNS",
    "DNS_RULE_NAME_DEFAULT": "DNS_{0}",
    "DNS_RULE_NAME": "Nom de la règle DNS",
    "DNS_RULE": "Nom de la règle",
    "DNS_SERVER_IP_ADDRESS": "Adresse IP du serveur DNS",
    "DNS_SERVER_IP_ADDRESS1": "Adresse IP du serveur DNS 1",
    "DNS_SERVER_IP_ADDRESS2": "Adresse IP du serveur DNS 2",
    "DNS_SERVER_IP_ADDRESSES": "Adresses IP du serveur DNS",
    "DNS_SERVER_IP_GROUPS": "Groupes d’adresses IP de serveur DNS",
    "DNS_SERVER_ONE": "Serveur DNS 1",
    "DNS_SERVER_TWO_OPTIONAL": "Serveur DNS 2 (facultatif)",
    "DNS_SERVER_TWO": "Serveur DNS 2",
    "DNS_SERVER": "Serveur DNS",
    "DNS_SERVICES": "Services DNS",
    "DNS_SOURCE": "Source",
    "DNS_TCP_PORTS_DNS_SPECIFIC": "Ports DNS (TCP) pour les règles spécifiques au DNS",
    "DNS_TIMEOUT": "Dépassement de délai de résolution DNS",
    "DNS_TOP_BLOCKED_BY_LOCATION": "Trafic bloqué par emplacement",
    "DNS_TOP_BLOCKED_BY_RULE": "Trafic bloqué par règle",
    "DNS_TOP_BLOCKED_BY_USER": "Trafic bloqué par utilisateur",
    "DNS_TRAFFIC_BY_DEPARTMENT": "Trafic DNS par service",
    "DNS_TRAFFIC_BY_LOCATION": "Trafic par emplacement",
    "DNS_TRAFFIC_BY_USER": "Trafic par utilisateur",
    "DNS_TRAFFIC_OVERVIEW": "Présentation générale du trafic DNS",
    "DNS_TRANSACTION_POLICY": "Stratégie de transaction DNS",
    "DNS_TRANSACTION_RULE": "Stratégie de transaction DNS",
    "DNS_TRANSACTION_TREND": "Tendance des transactions DNS",
    "DNS_TRANSACTION": "Transaction",
    "DNS_UDP_PORTS_DNS_SPECIFIC": "Ports DNS (UDP) pour des règles spécifiques au DNS",
    "DNS": "DNS",
    "DNSLOG": "Log Type DNS for NSS",
    "DNSREQ_A": "Une adresse d'hôte",
    "DNSREQ_AAAA": "Adresse IP6",
    "DNSREQ_AFSDB": "Pour l'emplacement de la base de données AFS",
    "DNSREQ_CNAME": "Nom canonique d'un alias",
    "DNSREQ_DNSKEY": "Clé publique DNS",
    "DNSREQ_DS": "Signataire de la délégation",
    "DNSREQ_HINFO": "Informations sur l’hôte",
    "DNSREQ_HIP": "Protocole d’identité de l’hôte",
    "DNSREQ_ISDN": "Pour une adresse RNIS",
    "DNSREQ_LOC": "Informations sur l'emplacement",
    "DNSREQ_MB": "Nom de domaine de la boîte aux lettres",
    "DNSREQ_MG": "Membre de groupe de messagerie",
    "DNSREQ_MINFO": "Informations sur la boîte aux lettres ou la liste de diffusion",
    "DNSREQ_MR": "Nom de domaine de nom de messagerie",
    "DNSREQ_MX": "Échange de courrier",
    "DNSREQ_NAPTR": "Pointeur d'autorité d'attribution de noms",
    "DNSREQ_NS": "Un serveur de noms faisant autorité",
    "DNSREQ_NSEC": "Extensions de sécurité DNS",
    "DNSREQ_PTR": "Pointeur de nom de domaine",
    "DNSREQ_RP": "Pour un(e) responsable",
    "DNSREQ_RT": "Pour Route Through",
    "DNSREQ_SOA": "Marque le début d'une zone d'autorité",
    "DNSREQ_SRV": "Sélection du serveur",
    "DNSREQ_TXT": "Chaînes de texte",
    "DNSREQ_UNKNOWN": "Type DNS non mappé par le pare-feu ZS",
    "DNSREQ_WKS": "Description de service bien connue",
    "DNSRES_CNAME": "Le type de réponse est CNAME",
    "DNSRES_IPV4": "Le type de réponse est IPV4",
    "DNSRES_IPV6": "Le type de réponse est IPV6",
    "DNSRES_SRV_CODE": "Le code d'erreur du serveur est défini pour le type de réponse",
    "DNSRES_ZSCODE": "Le type de réponse a un code personnalisé Zscaler défini",
    "DOES_NOT_CONTAINS": "Ne contient pas",
    "DOES_NOT_ENDS_WITH": "Ne se termine pas par",
    "DOES_NOT_STARTS_WITH": "Ne commence pas par",
    "DOHTTPS_RULE": "DNS via HTTP",
    "DOMAIN_CATEGORY": "Catégorie de domaine",
    "DOMAIN_NAME": "Nom de domaine",
    "DOMAIN": "Domaine",
    "DOMAINS": "Domaines",
    "DOMINICA_AMERICA_DOMINICA": "Amérique/Dominique",
    "DOMINICA": "Dominique",
    "DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO": "Amérique/Saint-Domingue",
    "DOMINICAN_REPUBLIC": "République dominicaine",
    "DONE": "Terminé",
    "DONT_SHOW_AGAIN": "Ne plus montrer",
    "DOWN": "En panne",
    "DOWNLOAD_AWS_CLOUDFORMATION_TEMPLATE": "Télécharger le modèle AWS Cloudformation",
    "DOWNLOAD_CERTIFICATE": "Télécharger le certificat",
    "DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION": "Télécharger le modèle Cloudformation pour une exécution ultérieure",
    "DOWNLOAD_CSV": "Télécharger (.csv)",
    "DOWNLOAD_ERROR": "Impossible de télécharger la mise à niveau depuis le cloud Zscaler. L'intégrité du connecteur cloud est bonne",
    "DOWNLOAD_MBPS": "Téléchargement (Mo/s)",
    "DOWNLOAD_MIB_FILES": "Télécharger les fichiers MIB",
    "DOWNLOAD_PROGRESS": "Progression du téléchargement",
    "DOWNLOAD": "Télécharger",
    "DPDRCV": "DPD reçu",
    "DR_CONGO": "RD du Congo",
    "DROP": "Supprimer",
    "DSTN_DOMAIN": "Domaine générique",
    "DSTN_FQDN": "FQDN",
    "DSTN_IP": "Adresse IP",
    "DSTN_OTHER": "Autre",
    "DSTN_WILDCARD_FQDN": "Domaine générique",
    "DUPLICATE_IP_ADDRESS": "Cette adresse IP est déjà utilisée",
    "DUPLICATE_IP_ADDRESSES": "Adresses IP en doublon",
    "DUPLICATE_ITEM": "Le nom indiqué est déjà utilisé.",
    "DUPLICATE_VLAN_ID": "ID VLAN dupliqué.",
    "DYNAMIC_DNS": "Hôte DNS dynamique",
    "DYNAMIC_LOCATION_GROUPS": "Groupes d'emplacements dynamiques",
    "DYNAMIC": "Dynamique",
    "EASTASIA": "(Asie-Pacifique) Asie de l’Est",
    "EASTASIASTAGE": "(Asie Pacifique) Asie de l'Est (étape)",
    "EASTUS": "(États-Unis) Est des États-Unis",
    "EASTUS2": "(États-Unis) Est des États-Unis 2",
    "EASTUS2EUAP": "(États-Unis) Est des États-Unis 2 EUAP",
    "EASTUS2STAGE": "(États-Unis) Est des États-Unis 2 (étape)",
    "EASTUSSTAGE": "(États-Unis) Est des États-Unis (Stage)",
    "EBS_STORAGE": "Stockage EBS",
    "EC_ACC_ID": "Identifiant de compte AWS",
    "EC_AVAILABILITY_ZONE": "Zone de disponibilité",
    "EC_AWS_AVAILABILITY_ZONE": "Zone de disponibilité AWS",
    "EC_AWS_REGION": "Région AWS",
    "EC_AZURE_AVAILABILITY_ZONE": "Zone de disponibilité Azure",
    "EC_DEVICE_APP_VERSION": "Version de l'application de l'appareil",
    "EC_DEVICE_HOSTNAME": "Nom d'hôte de l'appareil",
    "EC_DEVICE_ID": "Nom de l'appareil",
    "EC_DEVICE_OS_TYPE": "Type d'OS de l'appareil",
    "EC_DEVICE_TYPE": "Type d'appareil",
    "EC_DNS_GW_FLAG": "Indicateur de la passerelle DNS",
    "EC_DNS_GW_NAME": "Nom de la passerelle DNS",
    "EC_DNS": "DNS CC",
    "EC_DNSLOG": "Log Type DNS for NSS",
    "EC_EVENTLOG": "Événement",
    "EC_FORWARDING_TYPE": "Type de transfert",
    "EC_FW_RULE": "Règle de transfert",
    "EC_GROUP": "Groupe de Cloud Connectors",
    "EC_INSTANCE_NAME": "Instance du Cloud Connector",
    "EC_INSTANCE": "Instance du Cloud Connector",
    "EC_PLATFORM": "Plate-forme",
    "EC_PROJECT_ID": "ID de projet GCP",
    "EC_RDRRULESLOT": "Règles de transfert de trafic",
    "EC_SELFRULESLOT": "Règles de transfert des journaux et des contrôles",
    "EC_SOURCE_IP": "IP source du connecteur cloud",
    "EC_SOURCE_PORT": "Port source du connecteur cloud",
    "EC_SUBSCRIPTION_ID": "Identifiant d'abonnement Azure",
    "EC_TRAFFIC_DIRECTION": "Type de demande",
    "EC_TRAFFIC_TYPE": "Type de trafic",
    "EC_TS_DIRECTION": "TS Direction",
    "EC_UI": "Interface utilisateur du Cloud Connector",
    "EC_VM": "Machine virtuelle du connecteur cloud",
    "EC_VMNAME": "Nom de la machine virtuelle du connecteur cloud",
    "EC2_INSTANCE_TYPE": "Type d'instance EC2",
    "ECDIRECTSCTPXFORM": "Direct avec traduction SCTP",
    "ECHO_DESC": "Le protocole Echo désigne un service de la suite IP (Internet Protocol) conformément à la RFC 862. A l'origine, il permettait d'effectuer des tests et des mesures de durée de boucle sur les réseaux IP.",
    "ECHO": "Écho",
    "ECHOREP": "Réponse à l’écho",
    "ECHOREQ": "Demande d’écho",
    "ECHOSIGN": "AdobeEchoSign",
    "ECLOG": "Journaux des sessions",
    "ECSELF": "Passerelle pour le trafic émanant du connecteur",
    "ECUADOR_AMERICA_GUAYAQUIL": "Amérique/Guayaquil",
    "ECUADOR_PACIFIC_GALAPAGOS": "Pacifique/Galapagos",
    "ECUADOR": "Equateur",
    "ECZPA": "ZPA",
    "ECZPASCTPXFORM": "Direct avec traduction SCTP",
    "ECZPAXSCTPXFORM": "Tunnel vers le connecteur ZPA avec transformation SCTP",
    "EDGE_CONNECTOR_ADMIN_MANAGEMENT": "Gestion Administrateur",
    "EDGE_CONNECTOR_ADMIN": "Administrateur des Cloud Connectors",
    "EDGE_CONNECTOR_CCA_DEVICE": "CCA",
    "EDGE_CONNECTOR_CLOUD_PROVISIONING": "Provisionnement Cloud Connector",
    "EDGE_CONNECTOR_DASHBOARD": "Tableau de bord",
    "EDGE_CONNECTOR_FORWARDING": "Transfert (trafic, DNS et journaux)",
    "EDGE_CONNECTOR_LOCATION_MANAGEMENT": "Gestion des emplacements",
    "EDGE_CONNECTOR_NSS_CONFIGURATION": "Consignation NSS",
    "EDGE_CONNECTOR_POLICY_CONFIGURATION": "Configuration de la stratégie",
    "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Gestion de la configuration du cloud public",
    "EDGE_CONNECTOR_TEMPLATE": "Modèle (emplacement et provisionnement)",
    "EDGE_CONNECTOR_VM_SIZE": "Taille de la machine virtuelle",
    "EDGE_ONLY": "Edge uniquement",
    "EDGECONNECTOR_NAME": "Nom du connecteur cloud",
    "EDIT": "Modifier",
    "EDIT_5G_DEPLOYMENT": "Modifier la configuration de déploiement",
    "EDIT_API_KEY_TOOLTIP": "Modifier la clé API",
    "EDIT_APPLIANCE": "Modifier l'appliance",
    "EDIT_AWS_ACCOUNT": "Modifier le compte AWS",
    "EDIT_AWS_CLOUD_ACCOUNT": "Modifier le compte cloud AWS",
    "EDIT_AWS_GROUP": "Modifier le groupe AWS",
    "EDIT_AZURE_CLOUD_ACCOUNT": "Modifier le compte cloud Azure",
    "EDIT_AZURE_TENANT": "Modifier le locataire Azure",
    "EDIT_BC_PROVISIONING_TEMPLATE": "Modifie le modèle de provisionnement des connecteurs de succursale",
    "EDIT_CLOUD_APP_PROVIDER": "Modifier le fournisseur d’applications cloud",
    "EDIT_CLOUD_CONNECTOR_ADMIN": "Modifier l'administrateur du connecteur cloud",
    "EDIT_CLOUD_CONNECTOR_ROLE": "Modifier le rôle du connecteur cloud",
    "EDIT_CLOUD_CONNECTOR": "Modifier le connecteur cloud",
    "EDIT_CLOUD_CONNECTORS": "Modifier les connecteurs",
    "EDIT_CLOUD_SERVICE_API_KEY": "Modifier la clé d’API du service cloud",
    "EDIT_CONFIRMATION_DEPLOYED_GATEWAY": "Cet appareil est en état Déployé. Toute modification de configuration peut avoir un impact sur le trafic. Voulez-vous vraiment continuer ?",
    "EDIT_CONFIRMATION_PREDEFINED_RULE": "Cette règle prédéfinie ne s'applique qu'aux groupes/emplacements BC en mode Passerelle.",
    "EDIT_CONFIRMATION": "Modifier la confirmation",
    "EDIT_CONNECTORS": "Modifier les connecteurs",
    "EDIT_DESTINATION_IP_GROUP": "Modifier le groupe d'adresses IP de destination",
    "EDIT_DNS_GATEWAY": "Modifier la passerelle DNS",
    "EDIT_DNS_POLICIES": "Modifier la règle de filtrage DNS",
    "EDIT_DYNAMIC_VDI_GROUP": "Modifier le groupe VDI dynamique",
    "EDIT_EC_NSS_CLOUD_FEED": "Modifier le flux NSS Cloud",
    "EDIT_EC_NSS_FEED": "Modifier le flux NSS",
    "EDIT_EC_NSS_SERVER": "Modifier le serveur NSS",
    "EDIT_EDGECONNECTOR": "Modifier le connecteur cloud",
    "EDIT_IP_POOL": "Modifier le pool d'adresses IP",
    "EDIT_LOCATION_TEMPLATE": "Modifier le modèle d’emplacement",
    "EDIT_LOCATIONS": "Modifier les emplacements",
    "EDIT_LOG_AND_CONTROL_FORWARDING_RULE": "Modifier la règle de la consignation et du contrôle des transferts",
    "EDIT_LOG_AND_CONTROL_GATEWAY": "Modifier le journal et contrôler la passerelle",
    "EDIT_NETWORK_SERVICE_GROUP": "Ajouter un groupe de services réseau",
    "EDIT_NETWORK_SERVICE": "Modifier le service réseau",
    "EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE": "La modification de la clé API invalide immédiatement la clé actuelle. Vous devez remplacer toute référence à l’ancienne clé par la nouvelle.",
    "EDIT_PHYSICAL_BRANCH_DEVICE": "Modifier l'appareil du site distant physique",
    "EDIT_PROVISIONING_TEMPLATE": "Modifier le modèle d’approvisionnement du connecteur cloud",
    "EDIT_SOURCE_IP_GROUP": "Modifier le groupe d'adresses IP source",
    "EDIT_TRAFFIC_FWD_POLICIES": "Modifier les règles de transfert de trafic",
    "EDIT_UPF": "Modifier la fonction de l'instance utilisateur",
    "EDIT_VDI_AGENT_FORWARDING_PROFILE": "Modifier le profil de transfert VDI",
    "EDIT_VDI_TEMPLATE": "Modifier le modèle VDI",
    "EDIT_VIRTUAL_BRANCH_DEVICE": "Modifier l'appareil du site distant virtuel",
    "EDIT_ZERO_TRUST_GATEWAY": "Modifier la passerelle Zero Trust",
    "EDIT_ZIA_GATEWAY": "Modifier la passerelle ZIA",
    "EDIT_ZT_DEVICE": "Modifier l'appareil ZT",
    "EGRESS_DETAILS": "Détails de la sortie",
    "EGYPT_AFRICA_CAIRO": "Afrique/Caire",
    "EGYPT": "Egypte",
    "EITHER_REQ_RESP_BLOCK": "Bloquer",
    "EITHER_REQ_RESP_REDIRECT_NO_BLOCK": "Rediriger",
    "EL_SALVADOR_AMERICA_EL_SALVADOR": "Amérique/El Salvador",
    "EL_SALVADOR": "El Salvador",
    "EMAIL_HOST": "Messagerie Web",
    "EMAIL": "E-mail",
    "EMPTY_RESP": "Réponse DNS sans erreur mais avec section de réponse vide",
    "ENABLE_AUP": "Activer la PUA",
    "ENABLE_BRANCH_CONNECTOR_CONFIRMATION": "Voulez-vous vraiment activer ce Branch Connector ?",
    "ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Voulez-vous vraiment activer ce groupe Branch Connector ? Ceci entraînera la désactivation de tous les {0} Branch Connectors qui appartiennent à ce groupe.",
    "ENABLE_BRANCH_CONNECTOR_GROUP": "Active le groupe Branch Connector",
    "ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Voulez-vous vraiment activer tous les {0} Branch Connectors sélectionnés ?",
    "ENABLE_BRANCH_CONNECTOR_SELECTED": "Activer tous les Branch Connectors sélectionnés",
    "ENABLE_BRANCH_CONNECTOR": "Activer Branch Connector",
    "ENABLE_CAUTION": "Activer la mise en garde",
    "ENABLE_CLOUD_CONNECTOR_CONFIRMATION": "Voulez-vous vraiment activer ce Cloud Connector ?",
    "ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Voulez-vous vraiment activer ce groupe de Cloud Connectors ? Cela activerait tous les {0} Cloud Connectors appartenant à ce groupe",
    "ENABLE_CLOUD_CONNECTOR_GROUP": "Activer le groupe de Cloud Connectors",
    "ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Voulez-vous vraiment activer tous les {0} Cloud Connectors sélectionnés ?",
    "ENABLE_CLOUD_CONNECTOR_SELECTED": "Activer tous les Cloud Connectors sélectionnés",
    "ENABLE_CLOUD_CONNECTOR": "Activer le Cloud Connector",
    "ENABLE_DATA_COLLECTION_DESCRIPTION": "L'activation de la synchronisation permettra à Zscaler de récupérer les dernières informations sur les balises. Elle inclura en outre les ressources de ce compte de l'inclusion dans les groupes de charge de travail.",
    "ENABLE_DATA_COLLECTION": "Permettre la collecte de données",
    "ENABLE_FULL_ACCESS": "Activer l'accès complet",
    "ENABLE_GEO_IP_LOOKUP": "Activer la recherche IP GEO",
    "ENABLE_IPS_CONTROL": "Activer le contrôle IPS",
    "ENABLE_MOBILE_APP": "Accès à l’application Executive Insights",
    "ENABLE_POLICY_INFORMATION": "Activer les informations sur le transfert",
    "ENABLE_SSL_INSPECTION": "Activer l'Inspection SSL",
    "ENABLE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Appliquer une adresse IP de substitution pour les navigateurs connus",
    "ENABLE_USER_IP_MAPPING": "Activer la substitution d'IP",
    "ENABLE_VIEW_ONLY_ACCESS": "Activer l'affichage en lecture seule",
    "ENABLE_XFF_FORWARDING": "Activer le transfert XFF",
    "ENABLE": "Activer",
    "ENABLED": "Activé",
    "ENABLING": "Activation en cours",
    "ENCR_WEB_CONTENT": "Contenu chiffré sur mesure",
    "ENCRYPTED_DTLS": "DTLS",
    "END_TIME": "Heure de fin",
    "END_USER_AUTHENDICATION": "Authentification de l'utilisateur final",
    "ENDPOINT_ID": "Identifiant du terminal",
    "ENDPOINT_SERVICE_NAME": "Nom du service de terminal",
    "ENDPOINT_SEVICE_NAME": "Nom du service de terminal",
    "ENDPOINTS_SEVICE_NAME": "Nom du service des terminaux",
    "ENDPOINTS": "Terminaux",
    "ENDS_WITH": "Se termine par",
    "ENFORCE_AUTHENTICATION": "Appliquer l'authentification",
    "ENFORCE_BAND_WIDTH_CONTROL": "Appliquer le contrôle de la bande passante",
    "ENFORCE_FIREWALL_CONTROL": "Appliquer le contrôle du pare-feu",
    "ENFORCE_IPS_CONTROL": "Activer le contrôle IPS",
    "ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Appliquer une adresse IP de substitution pour les navigateurs connus",
    "ENFORCE_ZAPP_SSL_SETTING": "Appliquer le paramètre SSL ZApp",
    "ENROLLED_DEVICE_APP_VERSION": "Version de l'application de l'appareil enregistré",
    "ENSURE_ALL_INFORMATION_IS_CORRECT": "Veillez à ce que toutes les informations ci-dessous soient correctes avant de créer ce modèle de provisionnement de Cloud Connector.",
    "ENTER_AWS_ACCOUNT_ID": "Entrez l'ID du compte AWS...",
    "ENTER_CC_ROLE_NAME": "Entrez le nom du rôle Cloud Connector...",
    "ENTER_CLOUD_WATCH_GROUP_ARN": "Entrez l'ARN du groupe de surveillance cloud",
    "ENTER_CUSTOM_OPTION_CODE": "Entrez le code. Par ex. 42",
    "ENTER_CUSTOM_OPTION_NAME": "Entrez le nom de l'option personnalisée.",
    "ENTER_DESCRIPTION_HERE": "Entrer une description (facultatif)",
    "ENTER_DESCRIPTION": "Entrez la description...",
    "ENTER_DEVICE_NAME": "Entrer le nom de l'appareil",
    "ENTER_HEADERS_PARAMETERS": "Entrer les paramètres des en-têtes",
    "ENTER_HOSTNAME_PREFIX": "Entrer le préfixe du nom d'hôte",
    "ENTER_IP_ADDRESS_OR_FQDN": "Entrer une adresse IP ou un FQDN",
    "ENTER_IP_ADDRESS": "Entrez l'adresse IP...",
    "ENTER_LOG_INFO_TYPE": "Entrez le type d'info de journal",
    "ENTER_MTU": "Entrer le MTU...",
    "ENTER_NAME_HERE": "Entrez le nom ici",
    "ENTER_NAME": "Entrez le nom...",
    "ENTER_NAMESPACE": "Entrer l'espace de noms",
    "ENTER_NUMBER": "Entrer le numéro",
    "ENTER_ROLE_NAME": "Entrez le nom du rôle...",
    "ENTER_TEXT": "Entrez le texte...",
    "ENTER_THE_VALUE": "Veuillez entrer une valeur",
    "ENTER_URL": "Entrer l’URL",
    "ENTERTAINMENT": "Divertissement",
    "ENTITLEMENT_STATUS_TOOLTIP": "Le droit d'accès à la passerelle est affiché sous forme du nombre de zones de disponibilité (AZ) auxquelles le compte a droit, ainsi que de l'utilisation actuelle. Chaque passerelle utilise deux ou plusieurs AZ. Le nombre d'AZ utilisés par une passerelle est choisi lors de la création d'une passerelle.",
    "ENTITLEMENT_STATUS": "État des droits d'accès",
    "EQUATORIAL_GUINEA_AFRICA_MALABO": "Afrique/Malabo",
    "EQUATORIAL_GUINEA": "Guinée équatoriale",
    "ERITREA_AFRICA_ASMARA": "Afrique/Asmara",
    "ERITREA": "Erythrée",
    "ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER": "L'ID de projet Google ne peut pas être utilisé en combinaison avec l'ID de compte AWS.",
    "ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "L’identifiant d’abonnement Azure ne peut pas être associé à l’identifiant de compte AWS",
    "ERROR_API_MUST_BE_DIFFERENT": "La nouvelle clé API ne peut pas être identique à la clé actuelle",
    "ERROR_BLACKLIST": "Erreur de l'IP de la liste de refus",
    "ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR": "Une anomalie est survenue au cours du chargement des Cloud and Branch Connectors. Veuillez réessayer plus tard.",
    "ERROR_DEFAULT_LEASE_TME_SHOULD_BE_SMALLER_THAN_MAX_LEASE_TIME": "Le délai de location par défaut doit être inférieur ou égal au délai de location maximal.",
    "ERROR_DELETING_ADMIN_MANAGEMENT": "Une anomalie est survenue au cours de la suppression de la gestion de l’administrateur. Veuillez réessayer plus tard.",
    "ERROR_DELETING_API_KEY_MANAGEMENT": "Une anomalie est survenue au cours de la suppression de la gestion de la clé API. Veuillez réessayer plus tard.",
    "ERROR_DELETING_APPLIANCE": "Une anomalie est survenue au cours de la suppression de l'appareil ZT. Veuillez réessayer plus tard.",
    "ERROR_DELETING_CELLULAR_CONFIGURATION": "Une anomalie est survenue au cours de la suppression de la configuration cellulaire. Veuillez réessayer plus tard.",
    "ERROR_DELETING_CELLULAR_USER_PLANE": "Une anomalie est survenue au cours de la suppression de l'instance de l'utilisateur cellulaire. Veuillez réessayer plus tard.",
    "ERROR_DELETING_GATEWAY": "Une anomalie est survenue lors de la suppression des passerelles. Veuillez réessayer plus tard.",
    "ERROR_DELETING_LOCATION_TEMPLATE": "Une anomalie est survenue lors de la suppression du modèle d’emplacement. Veuillez réessayer plus tard.",
    "ERROR_DELETING_LOCATION": "Une anomalie est survenue lors de la suppression de l’emplacement. Veuillez réessayer plus tard.",
    "ERROR_DELETING_PARTNER_ACCOUNT": "Une anomalie est survenue au cours de la suppression du compte partenaire. Veuillez réessayer plus tard.",
    "ERROR_DELETING_ROLE_MANAGEMENT": "Une anomalie est survenue au cours de la suppression de la gestion du rôle. Veuillez réessayer plus tard.",
    "ERROR_DELETING_TESTING": "Une anomalie est survenue lors de la tentative de suppression du test. Veuillez réessayer plus tard.",
    "ERROR_DISABLING_PARTNER_ACCOUNT": "Erreur de désactivation de la collecte de données du compte partenaire",
    "ERROR_DUPLICATE_DNS_SERVER": "Veuillez sélectionner des options différentes pour le DNS principal et le DNS secondaire.",
    "ERROR_DUPLICATE_HA_VIRTUAL_ID": "Les identifiants virtuels à haute disponibilité doivent être uniques. Valeurs d’identifiants à haute disponibilité dupliquées ",
    "ERROR_DUPLICATE_INTERFACE": "L’interface ne peut pas être la même qu’une interface existante.",
    "ERROR_DUPLICATE_SUBNETS": "Vérifiez le sous-réseau dupliqué",
    "ERROR_EDITING_API_KEY_MANAGEMENT": "Une anomalie est survenue au cours de la modification de la gestion de la clé API. Veuillez réessayer plus tard.",
    "ERROR_EDITING_TESTING": "Une anomalie est survenue au cours de la modification du test. Veuillez réessayer plus tard.",
    "ERROR_EDITING": "Une anomalie est survenue au cours de la modification des données. Veuillez réessayer plus tard.",
    "ERROR_ENABLING_PARTNER_ACCOUNT": "Erreur d'activation de la collecte de données du compte partenaire ",
    "ERROR_HTTP_REQUEST_FAILURE": "Échec de la requête HTTP signalé.",
    "ERROR_LIST_DNS_SERVER_HAS_DUPLICATE": "Les adresses IP du serveur DNS ont des valeurs en doublon.",
    "ERROR_LIST_DNS_SERVER_LIMIT_4": "La limite est de 4 adresses IP de serveur DNS.",
    "ERROR_LIST_DOMAIN_NAME_HAS_DUPLICATE": "Les noms de domaine ont des valeurs en doublon.",
    "ERROR_LIST_DOMAIN_NAME_LIMIT_4": "La limite est de 4 noms de domaine.",
    "ERROR_LOADING_ADMIN_MANAGEMENT": "Une anomalie est survenue au cours du chargement des données de gestion de l'administration. Veuillez réessayer plus tard.",
    "ERROR_LOADING_API_KEY_MANAGEMENT": "Une anomalie est survenue au cours du chargement des données de la clé API. Veuillez réessayer plus tard.",
    "ERROR_LOADING_DATA": "Une anomalie est survenue au cours du chargement des données. Veuillez réessayer plus tard.",
    "ERROR_LOADING_DOMAINS": "Une anomalie est survenue au cours du chargement des données des domaines. Veuillez réessayer plus tard.",
    "ERROR_LOADING_FORWARDING_POLICIES": "Une anomalie est survenue au cours du chargement des données des stratégies de transfert. Veuillez réessayer plus tard.",
    "ERROR_LOADING_LOCATION_TEMPLATE": "Une anomalie est survenue lors du chargement des données du modèle d'emplacement. Veuillez réessayer plus tard.",
    "ERROR_LOADING_LOCATIONS": "Une anomalie est survenue au cours du chargement des données des emplacements. Veuillez réessayer plus tard.",
    "ERROR_LOADING_ROLE_MANAGEMENT": "Une anomalie est survenue au cours du chargement des données de gestion des rôles. Veuillez réessayer plus tard.",
    "ERROR_LOADING": "Erreur de chargement des données.",
    "ERROR_LOOKUP": "Erreur d'URL de recherche",
    "ERROR_NO_SCHEDULED_VERSION_AVAILABLE": "Aucune version disponible",
    "ERROR_OCCURRED_WHILE_CREATING_NEW_PASSWORD": "Une erreur s'est produite lors de la création du nouveau mot de passe. Veuillez réessayer plus tard",
    "ERROR_OCCURRED_WHILE_VERIFYING_PASSWORD": "Une erreur s'est produite lors de la vérification du mot de passe actuel. Veuillez réessayer plus tard",
    "ERROR_OPERATIONAL_STATUS_SAVE_ERROR": "Une anomalie est survenue lors de l’enregistrement de l’état opérationnel. Veuillez réessayer plus tard.",
    "ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "L’identifiant d’abonnement Azure ne peut pas être associé à l’identifiant de projet Google.",
    "ERROR_REGENERATE_API_KEY_MANAGEMENT": "Une anomalie est survenue au cours de la régénération de la gestion de la clé API. Veuillez réessayer plus tard.",
    "ERROR_REMOVE_DELETED_SEGMENTS": "La règle ne peut pas être enregistrée avec un segment supprimé.",
    "ERROR_SAVING_GATEWAY": "Une anomalie est survenue lors de l’enregistrement de la passerelle. Veuillez réessayer plus tard.",
    "ERROR_SAVING_LOCATION": "Une anomalie est survenue au cours de l'enregistrement de l'emplacement. Veuillez réessayer plus tard.",
    "ERROR_SAVING_NETWORK_SERVICES_GROUPS": "Une anomalie est survenue au cours de l’enregistrement des groupes de services réseau. Veuillez réessayer plus tard.",
    "ERROR_SAVING_NETWORK_SERVICES": "Une anomalie est survenue au cours de l'enregistrement des services réseau. Veuillez réessayer plus tard.",
    "ERROR_SAVING_ROLE_MANAGEMENT": "Une anomalie est survenue au cours de l’enregistrement de la gestion des rôles. Veuillez réessayer plus tard.",
    "ERROR_SAVING": "Une anomalie est survenue lors de l'enregistrement des données. Veuillez réessayer plus tard.",
    "ERROR_TESTING": "Une erreur s'est produite lors de l'exécution du test.",
    "ERROR_UPDATING_PERMISSION_STATUS": "Une anomalie est survenue lors de l’enregistrement de l’état des autorisations. Veuillez réessayer plus tard.",
    "ERROR": "Erreur",
    "ESP_PROTOCOL_DESC": "L’ESP (Encapsulating Security Payload) est un protocole au sein de l’IPSec permettant de fournir l’authentification, l’intégrité et la confidentialité de la charge utile des paquets réseau dans les réseaux IPv4 et IPv6.",
    "ESP_PROTOCOL": "ESP",
    "ESTABLISH_SUPPORT_TUNNEL": "Établir un tunnel de support",
    "ESTONIA_EUROPE_TALLINN": "Europe/Tallinn",
    "ESTONIA": "Estonie",
    "ETHIOPIA_AFRICA_ADDIS_ABABA": "Afrique/Addis-Abeba",
    "ETHIOPIA": "Ethiopie",
    "EU_CENTRAL_1": "eu-central-1 (Francfort)",
    "EU_CENTRAL_1A": "eu-central-1a",
    "EU_CENTRAL_1B": "eu-central-1b",
    "EU_CENTRAL_1C": "eu-central-1c",
    "EU_CENTRAL_2": "Europe (Zurich)",
    "EU_NORTH_1": "eu-north-1 (Stockholm)",
    "EU_NORTH_1A": "eu-north-1a",
    "EU_NORTH_1B": "eu-north-1b",
    "EU_NORTH_1C": "eu-north-1c",
    "EU_SOUTH_1": "eu-south-1 (Milan)",
    "EU_SOUTH_1A": "eu-south-1a",
    "EU_SOUTH_1B": "eu-south-1b",
    "EU_SOUTH_1C": "eu-south-1c",
    "EU_SOUTH_2": "Europe (Espagne)",
    "EU_WEST_1": "eu-west-1 (Irlande)",
    "EU_WEST_1A": "eu-west-1a",
    "EU_WEST_1B": "eu-west-1b",
    "EU_WEST_1C": "eu-west-1c",
    "EU_WEST_2": "eu-west-2 (Londres)",
    "EU_WEST_2A": "eu-west-2a",
    "EU_WEST_2B": "eu-west-2b",
    "EU_WEST_2C": "eu-west-2c",
    "EU_WEST_3": "wu-west-3 (Paris)",
    "EU_WEST_3A": "eu-west-3a",
    "EU_WEST_3B": "eu-west-3b",
    "EU_WEST_3C": "eu-west-3c",
    "EUROPE_CENTRAL2_A": "europe-central2-a",
    "EUROPE_CENTRAL2_B": "europe-central2-b",
    "EUROPE_CENTRAL2_C": "europe-central2-c",
    "EUROPE_CENTRAL2": "europe-central2",
    "EUROPE_NORTH1_A": "europe-north1-a",
    "EUROPE_NORTH1_B": "europe-north1-b",
    "EUROPE_NORTH1_C": "europe-north1-c",
    "EUROPE_NORTH1": "europe-north1",
    "EUROPE_SOUTHWEST1_A": "europe-southwest1-a",
    "EUROPE_SOUTHWEST1_B": "europe-southwest1-b",
    "EUROPE_SOUTHWEST1_C": "europe-southwest1-c",
    "EUROPE_SOUTHWEST1": "europe-southwest1",
    "EUROPE_WEST1_B": "europe-west1-b",
    "EUROPE_WEST1_C": "europe-west1-c",
    "EUROPE_WEST1_D": "europe-west1-d",
    "EUROPE_WEST1": "europe-west1",
    "EUROPE_WEST10": "europe-west10",
    "EUROPE_WEST12_A": "europe-west12-a",
    "EUROPE_WEST12_B": "europe-west12-b",
    "EUROPE_WEST12_C": "europe-west12-c",
    "EUROPE_WEST12": "europe-west12",
    "EUROPE_WEST2_A": "europe-west2-a",
    "EUROPE_WEST2_B": "europe-west2-b",
    "EUROPE_WEST2_C": "europe-west2-c",
    "EUROPE_WEST2": "europe-west2",
    "EUROPE_WEST3_A": "europe-west3-a",
    "EUROPE_WEST3_B": "europe-west3-b",
    "EUROPE_WEST3_C": "europe-west3-c",
    "EUROPE_WEST3": "europe-west3",
    "EUROPE_WEST4_A": "europe-west4-a",
    "EUROPE_WEST4_B": "europe-west4-b",
    "EUROPE_WEST4_C": "europe-west4-c",
    "EUROPE_WEST4": "europe-west4",
    "EUROPE_WEST6_A": "europe-west6-a",
    "EUROPE_WEST6_B": "europe-west6-b",
    "EUROPE_WEST6_C": "europe-west6-c",
    "EUROPE_WEST6": "europe-west6",
    "EUROPE_WEST8_A": "europe-west8-a",
    "EUROPE_WEST8_B": "europe-west8-b",
    "EUROPE_WEST8_C": "europe-west8-c",
    "EUROPE_WEST8": "europe-west8",
    "EUROPE_WEST9_A": "europe-west9-a",
    "EUROPE_WEST9_B": "europe-west9-b",
    "EUROPE_WEST9_C": "europe-west9-c",
    "EUROPE_WEST9": "europe-west9",
    "EUROPE": "Europe",
    "EUSA_AGREEMENT": "Contrat d'abonnement de l'utilisateur final de Zscaler",
    "EVENT_BUS_NAME": "Nom du bus d'événements",
    "EVENT_GRID_TEXT": "Sélectionnez les régions, abonnements et groupes de ressources où le sujet partenaire et la destination sont créés.",
    "EVENT_GRID": "Grille des événements",
    "EVENT_TIME": "Heure de l'événement",
    "EVENT": "Événement",
    "EVENTS": "Événements",
    "EXACT_MATCH": "Correspondance exacte",
    "EXCEEDS_UPGRADE_WINDOW": "Échec. La mise à niveau a pris plus de temps que la fenêtre de mise à niveau. L'intégrité du connecteur cloud a été rétablie",
    "EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS": "Exclure des groupes d'emplacements dynamiques",
    "EXCLUDE_FROM_STATIC_LOCATION_GROUPS": "Exclure des groupes d'emplacements manuels",
    "EXCLUDE_IP_ADDRESSES": "Exclure les adresses IP",
    "EXCLUDE": "EXCLURE",
    "EXEC_INSIGHT_AND_ORG_ADMIN": "Executive Insight & Administrateur de l’organisation",
    "EXISTING_LOCATION": "Emplacement actuel",
    "EXISTING": "Existant(e)",
    "EXPIRES_IN": "Expire dans",
    "EXPIRES": "Expiration",
    "EXPIRY_DATE": "Date d'expiration",
    "EXPORT_TO_CSV": "Exporter au format CSV",
    "EXTERNAL_ID": "ID externe",
    "EXTERNAL": "Trafic externe",
    "FAIL_ALLOW_IGNORE_DNAT": "Transférer au serveur DNS d'origine",
    "FAIL_CLOSE": "Échec de la fermeture",
    "FAIL_CLOSED": "Échec de la fermeture",
    "FAIL_OPEN_TEXT": "Contourner le moteur de transfert du trafic",
    "FAIL_OPEN_TOOLTIP": "L'activation de cette option permet à tout le trafic destiné localement et à Internet de circuler sans validation de politique ni inspection de contenu, au cas où le moteur de transfert de politique échouerait ou serait arrêté pour des mises à jour. L'accès aux applications ZPA protégées ne serait pas autorisé dans cet état.",
    "FAIL_OPEN": "OUVERT EN CAS DE PANNE",
    "FAIL_RET_ERR": "Renvoyer une réponse d'erreur",
    "FAILED_OTHER": "Autre. L'intégrité du connecteur cloud est bonne",
    "FAILED": "Échec",
    "FAILURE_BEHAVIOR": "Comportement d'échec",
    "FAILURE": "Échec",
    "FALKLAND_ISLANDS_ATLANTIC_STANLEY": "Atlantique/Stanley",
    "FALKLAND_ISLANDS_MALVINAS": "Iles Falkland (Malouines)",
    "FALKLAND_ISLANDS": "Iles Falkland",
    "FALLBACK_TO_TLS": "Repli sur TLS",
    "FALSE": "Faux",
    "FAMILY_ISSUES": "Problèmes familiaux",
    "FAROE_ISLANDS_ATLANTIC_FAROE": "Atlantique/Féroé",
    "FAROE_ISLANDS": "Iles Féroé",
    "FEDERATED_STATES_OF_MICRONESIA": "Etats fédérés de Micronésie",
    "FETCHING_MORE_LIST_ITEMS": "Récupération de plus d'éléments de la liste...",
    "FIJI": "Fidji",
    "FILE_CERTIFICATE_FILTER": "Fichier (.pem, .cer)",
    "FILE_HOST": "FileHost",
    "FILTERING": "Filtrage",
    "FINANCE": "Finances",
    "FINISH": "Terminer",
    "FINLAND_EUROPE_HELSINKI": "Europe/Helsinki",
    "FINLAND": "Finlande",
    "FIREWALL_ACCESS_CONTROL": "Contrôle d’accès par pare-feu",
    "FIREWALL_FORWARDING": "Transfert de pare-feu",
    "FIREWALL_LOGS": "Log Type FirewallLog for NSS",
    "FIREWALL_RESOURCE": "Ressource de pare-feu",
    "FIRST_TIME_AUP_BEHAVIOR": "Comportement AUP pour la première fois",
    "FO_DEST_DROP": "Requête abandonnée",
    "FO_DEST_ERR": "Réponse d'erreur renvoyée au client",
    "FO_DEST_PASS": "Requête transmise à la destination",
    "FOOTER_PATENTS_TOOLTIP": "Conformément aux dispositions de l’America Invents Act relatives au marquage virtuel, les offres de sécurité de Zscaler sont protégées par des brevets aux États-Unis et ailleurs, tel que décrit plus en détail sur le site https://www.zscaler.com/patents.",
    "FOR_AUTOMATION": "Pour l'automatisation",
    "FORCE_ACTIVATE": "Forcer l’activation",
    "FORCE_DELETE_VM": "Forcer la suppression de la machine virtuelle (VM)",
    "FORCE_SSL_INTERCEPTION": "Forcer l'inspection SSL",
    "FORCED_ACTIVATE": "Forcer l’activation",
    "FORWARD_TO_ORIGINAL_SERVER": "Transférer au serveur DNS d'origine",
    "FORWARD_TO_PROXY_GATEWAY": "Transférer à la passerelle proxy",
    "FORWARD_TO_ZPA_GATEWAY": "Transférer à la passerelle ZPA",
    "FORWARDING_CONTROL": "Transfert de contrôle",
    "FORWARDING_INFORMATION": "Transfert d’informations",
    "FORWARDING_INTERFACE": "Transfert d'interface",
    "FORWARDING_IP_ADDRESS": "Transfert de l’adresse IP",
    "FORWARDING_METHOD": "Type de transfert",
    "FORWARDING_METHODS": "Modes de transfert",
    "FORWARDING_POLICIES": "Transfert de stratégies",
    "FORWARDING_RULE": "Règle de transfert",
    "FORWARDING": "Transfert",
    "FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Domaines",
    "FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Groupe de domaines",
    "FRANCE_EUROPE_PARIS": "Europe/Paris",
    "FRANCE": "France",
    "FRANCECENTRAL": "(Europe) France centre",
    "FRANCESOUTH": "(Europe) France Sud",
    "FRENCH_GUIANA_AMERICA_CAYENNE": "Amérique/Cayenne",
    "FRENCH_GUIANA": "Guyane française",
    "FRENCH_POLYNESIA_PACIFIC_GAMBIER": "Pacifique/Gambier",
    "FRENCH_POLYNESIA_PACIFIC_MARQUESAS": "Pacifique/Marquises",
    "FRENCH_POLYNESIA_PACIFIC_TAHITI": "Pacifique/Tahiti",
    "FRENCH_POLYNESIA": "Polynésie française",
    "FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN": "Océan Indien/Kerguelen",
    "FRENCH_SOUTHERN_TERRITORIES": "Terres australes françaises",
    "FRIDAY": "Vendredi",
    "FROM": "De",
    "FTP_000": "Réponse FTP non valide",
    "FTP_110": "110 - Redémarrer le replay du marqueur",
    "FTP_125": "125 - Connexion des données déjà ouverte ; démarrage du transfert",
    "FTP_150": "150 - État du fichier ok ; sur le point d’ouvrir la connexion des données",
    "FTP_200": "200 - Répertoire modifié",
    "FTP_226": "226 - Transfert terminé",
    "FTP_250": "250 - Terminé",
    "FTP_421": "421 - Service non disponible",
    "FTP_425": "425 - Impossible d'ouvrir la connexion aux données",
    "FTP_426": "426 - Transfert abandonné",
    "FTP_450": "450 - Action de fichier demandée non effectuée",
    "FTP_451": "451 - Erreur locale lors du traitement",
    "FTP_452": "452 - Capacité de stockage insuffisante",
    "FTP_453": "453 - Incompatibilité MD5",
    "FTP_500": "500 - Erreur de syntaxe",
    "FTP_501": "501 - Erreur de syntaxe dans les paramètres",
    "FTP_502": "502 - Commande non intégrée",
    "FTP_530": "530 - Non connecté",
    "FTP_532": "532 - Compte nécessaire pour le stockage des fichiers",
    "FTP_550": "550 - Fichier non disponible",
    "FTP_551": "551 - Type de page inconnu",
    "FTP_552": "552 - Attribution de stockage dépassée",
    "FTP_553": "553 - Nom de fichier non autorisé",
    "FTP_554": "554 - Le fichier est infecté",
    "FTP_555": "555 - Bloqué par la stratégie de type de fichier",
    "FTP_556": "556 - Bloqué par le DLP",
    "FTP_557": "557 - Bloqué par le BA",
    "FTP_558": "558 - Bloqué par le BWCTL",
    "FTP_559": "559 - Bloqué par la catégorie d'URL",
    "FTP_560": "560 - Bloqué par l'ATP",
    "FTP_561": "561 - Bloqué par Bloquer l’accès à Internet",
    "FTP_ALLOW_OVER_HTTP": "Autoriser FTP via HTTP",
    "FTP_ALLOWED_URL_CATEGORIES": "Catégories d'URL autorisées",
    "FTP_ALLOWED_URLS": "URL autorisées",
    "FTP_APPE": "appe",
    "FTP_CONNECT_CMD": "CONNECT",
    "FTP_CONNECT": "Convertir le trafic ftp (en utilisant HTTP connect) du mode pont au mode FTP natif",
    "FTP_CONTROL_RECOMMENDED_POLICY": "Politique recommandée de contrôle FTP",
    "FTP_CONTROL_TIPS_DESC": "Par défaut, le service Zscaler n’autorise pas les utilisateurs d’un emplacement à charger ou à télécharger des fichiers à partir de sites FTP. Vous pouvez configurer la stratégie de contrôle FTP pour autoriser l'accès à des sites spécifiques.",
    "FTP_CONTROL_TIPS_TITLE": "Configurer la stratégie de contrôle du FTP",
    "FTP_CONTROL": "Contrôle FTP",
    "FTP_CWD": "cwd",
    "FTP_DATA_DESC": " Ce protocole permet de transporter les données dans la connexion de données de la communication FTP.",
    "FTP_DATA": "FTP-Data",
    "FTP_DENIED": "Accès non autorisé aux sites FTP",
    "FTP_DESC": " Le protocole FTP permet de transférer des données sécurisées entre un client et un serveur.",
    "FTP_INVALID": "NON VALIDE",
    "FTP_LIST": "liste",
    "FTP_NATIVE_TRAFFIC": "Trafic FTP natif",
    "FTP_OVER_HTTP_TRAFFIC": "Trafic FTP sur HTTP",
    "FTP_PROXY_PORT": "Port proxy FTP",
    "FTP_PROXY": "Proxy FTP",
    "FTP_RETR": "retr",
    "FTP_RULE": "FTP natif",
    "FTP_SECURITY": "La sécurité FTP comprend AV, DLP, BA, FT, etc.",
    "FTP_SERVICES": "Services FTP",
    "FTP_STOR": "stor",
    "FTP_UPLOAD_DENIED": "Non autorisé à utiliser FTP sur HTTP pour le chargement",
    "FTP": "FTP",
    "FTPOVERHTTP": "FTP sur HTTP",
    "FTPRULESLOT": "Contrôle de type de fichier",
    "FTPS_DATA_DESC": " Ce protocole permet de transporter les données dans le cadre de la connexion de données dans une communication FTP sécurisée.",
    "FTPS_DATA": "ftps_data",
    "FTPS_DESC": " Version sécurisée du protocole FTP",
    "FTPS_IMPLICIT_DESC": "Implicit FTPS lance automatiquement une connexion SSL/TLS au serveur dès que le client FTP se connecte à un serveur FTP.",
    "FTPS_IMPLICIT": "FTPS implicite",
    "FTPS": "FTPS",
    "FULL_ACCESS_ENABLED_UNTIL": "L'accès complet est activé jusqu'à",
    "FULL_ACCESS": "Accès complet",
    "FULL_SESSION_LOGS": "Journaux de session complets",
    "FULL": "Complet",
    "FUNCTIONAL_SCOPE": "Périmètre fonctionnel",
    "FWD_METHOD": "Type de transfert",
    "FWD_RULE": "Règle de transfert",
    "FWD_TRAFFIC_DIRECTION": "Type de demande",
    "FWD_TYPE": "Type de transfert",
    "FWD_TYPES": "Types de fwd",
    "GABON_AFRICA_LIBREVILLE": "Afrique/Libreville",
    "GABON": "Gabon",
    "GAMBIA_AFRICA_BANJUL": "Afrique/Banjul",
    "GAMBIA": "Gambie",
    "GAMBLING": "Jeux d'argent",
    "GATEWAY_DEST_IP": "IP de destination de la passerelle",
    "GATEWAY_DEST_PORT": "Port de destination de la passerelle",
    "GATEWAY_DETAILS": "Détails de la passerelle",
    "GATEWAY_IP_ADDRESS": "Adresse IP de la passerelle",
    "GATEWAY_NAME": "Nom de la passerelle",
    "GATEWAY_OPTIONS": "Options de passerelle",
    "GATEWAY": "Passerelle",
    "GATEWAYS": "Passerelles",
    "GCP_AVAILABILITY_ZONE": "Zone de disponibilité GCP",
    "GCP_REGION": "Région GCP",
    "GENERAL_AVAILABILITY": "Disponibilité générale",
    "GENERAL_INFORMATION": "Informations générales",
    "GENERAL": "Général",
    "GENERATE_NEW_CERTIFICATE": "Générer un nouveau certificat",
    "GENERATE_TOKEN": "Générer un jeton",
    "GEO_LOCATION": "Géolocalisation",
    "GEO_VIEW": "Vue géographique",
    "GEORGIA_ASIA_TBILISI": "Asie/Tbilissi",
    "GEORGIA": "Géorgie",
    "GERMANY_EUROPE_BERLIN": "Europe/Berlin",
    "GERMANY": "Allemagne",
    "GERMANYNORTH": "(Europe) Allemagne du Nord",
    "GERMANYWESTCENTRAL": "(Europe) Allemagne Centre-Ouest",
    "GHANA_AFRICA_ACCRA": "Afrique/Accra",
    "GHANA": "Ghana",
    "GIBRALTAR_EUROPE_GIBRALTAR": "Europe/Gibraltar",
    "GIBRALTAR": "Gibraltar",
    "GLOBAL": "Global",
    "GMT_01_00_AZORES": "GMT-01:00",
    "GMT_01_00_WESTERN_EUROPE_GMT_01_00": "GMT+01:00",
    "GMT_02_00_EASTERN_EUROPE_GMT_02_00": "GMT+02:00",
    "GMT_02_00_EGYPT_GMT_02_00": "GMT+02:00",
    "GMT_02_00_ISRAEL_GMT_02_00": "GMT+02:00",
    "GMT_02_00_MID_ATLANTIC": "GMT-02:00",
    "GMT_03_00_ARGENTINA": "GMT-03:00",
    "GMT_03_00_BRAZIL": "GMT-03:00",
    "GMT_03_00_RUSSIA_GMT_03_00": "GMT+03:00",
    "GMT_03_00_SAUDI_ARABIA_GMT_03_00": "GMT+03:00",
    "GMT_03_30_IRAN_GMT_03_30": "GMT+03:30",
    "GMT_03_30_NEWFOUNDLAND_CANADA": "GMT-03:30",
    "GMT_04_00_ARABIAN_GMT_04_00": "GMT+04:00",
    "GMT_04_00_ATLANTIC_TIME": "GMT-04:00",
    "GMT_04_30_AFGHANISTAN_GMT_04_30": "GMT+04:30",
    "GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA": "GMT-05:00",
    "GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00": "GMT+05:00",
    "GMT_05_00_US_EASTERN_TIME_INDIANA": "GMT-05:00",
    "GMT_05_00_US_EASTERN_TIME": "GMT-05:00",
    "GMT_05_30_INDIA_GMT_05_30": "GMT+05:30",
    "GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00": "GMT+06:00",
    "GMT_06_00_MEXICO": "GMT-06:00",
    "GMT_06_00_US_CENTRAL_TIME": "GMT-06:00",
    "GMT_06_30_BURMA_GMT_06_30": "GMT+06:30",
    "GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00": "GMT+07:00",
    "GMT_07_00_US_MOUNTAIN_TIME_ARIZONA": "GMT-07:00",
    "GMT_07_00_US_MOUNTAIN_TIME": "GMT-07:00",
    "GMT_08_00_AUSTRALIA_WT_GMT_08_00": "GMT+08:00",
    "GMT_08_00_CHINA_TAIWAN_GMT_08_00": "GMT+08:00",
    "GMT_08_00_PACIFIC_TIME": "GMT-08:00",
    "GMT_08_00_SINGAPORE_GMT_08_00": "GMT+08:00",
    "GMT_08_30_PITCARN": "GMT-08:30",
    "GMT_09_00_JAPAN_GMT_09_00": "GMT+09:00",
    "GMT_09_00_KOREA_GMT_09_00": "GMT+09:00",
    "GMT_09_00_US_ALASKA_TIME": "GMT-09:00",
    "GMT_09_30_AUSTRALIA_CT_GMT_09_30": "GMT+09:30",
    "GMT_09_30_MARQUESAS": "GMT-09:30",
    "GMT_10_00_AUSTRALIA_ET_GMT_10_00": "GMT+10:00",
    "GMT_10_00_US_HAWAIIAN_TIME": "GMT-10:00",
    "GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30": "GMT+10:30",
    "GMT_11_00_CENTRAL_PACIFIC_GMT_11_00": "GMT+11:00",
    "GMT_11_00_SAMOA": "GMT-11:00",
    "GMT_11_30_NORFOLK_ISLANDS_GMT_11_30": "GMT+11:30",
    "GMT_12_00_DATELINE": "GMT-12:00",
    "GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00": "GMT+12:00",
    "GMT_UK_SPAIN": "GMT",
    "GMT": "GMT",
    "GMTP": "",
    "GNUTELLA_DESC": " Gnutella est un protocole peer-to-peer.",
    "GNUTELLA": "Gnutella",
    "GO_BACK": "Retour",
    "GOOD": "Good",
    "GOVERNMENT": "Gouvernement et politique - autres",
    "GRE_PROTOCOL_DESC": "Le GRE (Generic Routing Encapsulation) est un protocole de tunneling qui peut encapsuler une grande variété de protocoles de couches réseau à l'intérieur de liens virtuels point à point ou de liens point à point sur un réseau de protocole Internet.",
    "GRE_PROTOCOL": "GRE",
    "GRE_TUNNEL_INFO": "Informations sur le tunnel GRE",
    "GRE": "GRE",
    "GREECE_EUROPE_ATHENS": "Europe/Athènes",
    "GREECE": "Grèce",
    "GREENLAND_AMERICA_DANMARKSHAVN": "Amérique/Danmarkshavn",
    "GREENLAND_AMERICA_GODTHAB": "Amérique/Godthab",
    "GREENLAND_AMERICA_SCORESBYSUND": "Amérique/Scoresbysund",
    "GREENLAND_AMERICA_THULE": "Amérique/Thule",
    "GREENLAND": "Groenland",
    "GRENADA_AMERICA_GRENADA": "Amérique/Grenade",
    "GRENADA": "Grenade",
    "GROUP_INFORMATION": "Informations sur le groupe",
    "GROUP_NAME": "Nom du groupe",
    "GROUP_ONLY": "Groupe",
    "GROUP": "Groupe",
    "GROUPS": "Groupes",
    "GUADELOUPE_AMERICA_GUADELOUPE": "Amérique/Guadeloupe",
    "GUADELOUPE": "Guadeloupe",
    "GUAM_PACIFIC_GUAM": "Pacifique/Guam",
    "GUAM": "Guam",
    "GUATEMALA_AMERICA_GUATEMALA": "Amérique/Guatemala",
    "GUATEMALA": "Guatemala",
    "GUERNSEY_EUROPE_GUERNSEY": "Europe/Guernesey",
    "GUERNSEY": "Guernesey",
    "GUESTWIFI": "Type de trafic Wi-Fi invité",
    "GUINEA_AFRICA_CONAKRY": "Afrique/Conakry",
    "GUINEA_BISSAU_AFRICA_BISSAU": "Afrique/Bissau",
    "GUINEA_BISSAU": "Guinée-Bissau",
    "GUINEA": "Guinée",
    "GUYANA_AMERICA_GUYANA": "Amérique/Guyana",
    "GUYANA": "Guyana",
    "GW_CONNECT_FAILED": "L’établissement de la connexion à GW a échoué.",
    "GW_CONNECTION_CLOSE": "La connexion GW a été fermée avec EOF.",
    "GW_CONNECTION_FAIL": "Échec de la connexion à la passerelle.",
    "GW_KEEPALIVE_FAIL": "La sonde GW keepalive a expiré.",
    "GW_RESOLVE_FAIL": "La résolution de la passerelle a échoué.",
    "GW_RESOLVE_NOIP": "PAC n’a renvoyé aucune adresse IP pour la résolution GW.",
    "GW_UNHEALTHY": "L'intégrité de certaines passerelles n'est pas bonne",
    "H_323_DESC": "H.323 désigne une norme approuvée par l'Union internationale des télécommunications (UIT), qui définit la méthode de transmission des données de conférences audiovisuelles sur les réseaux.",
    "H_323": "H.323",
    "HA_DEPLOYMENT_STATUS": "Détails du déploiement GD",
    "HA_DEPLOYMENT": "Déploiement de grande disponibilité",
    "HA_STATE": "État HA",
    "HA_STATUS": "Statut HA",
    "HAITI_AMERICA_PORT_AU_PRINCE": "Amérique/Port-au-Prince",
    "HAITI": "Haïti",
    "HARDWARE_DEVICE": "Dispositif matériel",
    "HARDWARE_MANAGEMENT": "Gestion du matériel",
    "HEALTH_MONITORING_CC": "Surveillance de l'intégrité des connecteurs cloud",
    "HEALTH_STATUS_TOOLTIP": "L'intégrité affiche l'état de santé de chaque passerelle déployée. Le nombre de passerelles affiché ici est inférieur à celui indiqué dans les droits d'accès, car chaque passerelle peut comporter plusieurs zones de disponibilité.",
    "HEALTH_STATUS": "Statut de l'intégrité",
    "HEALTH": "Santé",
    "HEALTHY": "Sain",
    "HELP": "Aide",
    "HIGH_AVAILABILITY_STATUS": "État de grande disponibilité",
    "HIGH_AVAILABILITY": "Grande disponibilité",
    "HISTORY": "Histoire",
    "HOBBIES_AND_LEISURE": "Hobbies/Loisirs",
    "HOLY_SEE_VATICAN_CITY_STATE": "Saint-Siège",
    "HONDURAS_AMERICA_TEGUCIGALPA": "Amérique/Tegucigalpa",
    "HONDURAS": "Honduras",
    "HONG_KONG_ASIA_HONG_KONG": "Asie/Hong Kong",
    "HONG_KONG": "Hong Kong",
    "HOP_COUNT": "Comptage des sauts",
    "HOSTED_DB": "Base de données hébergée",
    "HOSTNAME_PREFIX": "Préfixe du nom d'hôte",
    "HOSTNAME": "Nom d'hôte",
    "HOURS": "Heures",
    "HTTP_0_0": "mauvaise",
    "HTTP_000": "Réponse HTTP non valide",
    "HTTP_1_0": "1",
    "HTTP_1_1": "1.1",
    "HTTP_100": "100 - Continuer",
    "HTTP_101": "101 - Changement de protocole",
    "HTTP_102": "102 - Traitement",
    "HTTP_150": "150 - Autres erreurs 1XX",
    "HTTP_2_0": "2.0",
    "HTTP_200": "200 - OK",
    "HTTP_201": "201 - Créé",
    "HTTP_202": "202 - Accepté",
    "HTTP_203": "203 - Informations ne faisant pas autorité",
    "HTTP_204": "204 - Pas de contenu",
    "HTTP_205": "205 - Rétablir le contenu",
    "HTTP_206": "206 - Contenu partiel",
    "HTTP_207": "207 - Multi-Statut",
    "HTTP_226": "226 MI utilisée",
    "HTTP_250": "250 - Autres erreurs 2XX",
    "HTTP_300": "300 - Choix multiples",
    "HTTP_301": "301 - Déplacement définitif",
    "HTTP_302": "302 - Trouvé",
    "HTTP_303": "303 - Voir un autre",
    "HTTP_304": "304 - Non modifié",
    "HTTP_305": "305 - Utiliser un proxy",
    "HTTP_306": "306 - Inutilisé",
    "HTTP_307": "307 - Redirection temporaire",
    "HTTP_308": "308 - Redirection permanente",
    "HTTP_400": "400 - Mauvaise demande",
    "HTTP_401": "401 - Non autorisé",
    "HTTP_402": "402 - Paiement exigé",
    "HTTP_403": "403 - Interdit",
    "HTTP_404": "404 - Non trouvé",
    "HTTP_405": "405 - Méthode non admise",
    "HTTP_406": "406 - Pas acceptable",
    "HTTP_407": "407 - Authentification du proxy exigée",
    "HTTP_408": "408 - Expiration de la demande",
    "HTTP_409": "409 - Conflit",
    "HTTP_410": "410 - Parti",
    "HTTP_411": "411 - Longueur exigée",
    "HTTP_412": "412 - Echec de précondition",
    "HTTP_413": "413 - Entité de demande trop grande",
    "HTTP_414": "414 - URI de demande trop grande",
    "HTTP_415": "415 - Type de support non pris en charge",
    "HTTP_416": "416 - Gamme demandée non réalisable",
    "HTTP_417": "417 - Echec de l'attente",
    "HTTP_421": "421 - Demande mal dirigée",
    "HTTP_422": "422 - Entité non traitable",
    "HTTP_423": "423 - Verrouillé",
    "HTTP_424": "424 - Échec de la dépendance",
    "HTTP_426": "426 - Mise à niveau requise",
    "HTTP_428": "428 Condition préalable exigée",
    "HTTP_429": "429 - Demandes en surnombre",
    "HTTP_450": "450 - Autres erreurs 4XX",
    "HTTP_500": "500 - Erreur interne du serveur",
    "HTTP_501": "501 - Non mis en œuvre",
    "HTTP_502": "502 - Mauvaise passerelle",
    "HTTP_503": "503 - Service indisponible",
    "HTTP_504": "504 - Expiration de la passerelle",
    "HTTP_505": "505 - Version non prise en charge",
    "HTTP_506": "506 - La variante négocie également",
    "HTTP_507": "507 - Capacité de stockage insuffisante",
    "HTTP_508": "508 - Boucle détectée",
    "HTTP_510": "510 - Sans extension",
    "HTTP_550": "550 - Autres erreurs 5XX",
    "HTTP_BASELINECONTROL": "Baselinecontrol",
    "HTTP_BCOPY": "Bcopy",
    "HTTP_BDELETE": "Bdelete",
    "HTTP_BMOVE": "Bmove",
    "HTTP_BPROPFIND": "Bpropfind",
    "HTTP_BPROPPATCH": "Bproppatch",
    "HTTP_CHECKIN": "Checkin",
    "HTTP_CHECKOUT": "Checkout",
    "HTTP_CONNECT_DENIED": "Non autorisé à utiliser le tunnel HTTP",
    "HTTP_CONNECT": "CONNECT",
    "HTTP_COPY": "Copier",
    "HTTP_DELETE": "Supprimer",
    "HTTP_DESC": " Le protocole HTTP (Hypertext Transfer Protocol) permet de naviguer sur le Web.",
    "HTTP_DNS_PORT_SETTINGS": "Paramètres des ports HTTP et DNS",
    "HTTP_DPI_DISABLED": "SME DPI : Détermine si le trafic HTTP du proxy doit être envoyé à DPI ou non ; par défaut, ce bit de fonctionnalité est désactivé et nous envoyons le flux à DPI si l’appid ne peut pas être déterminé depuis ZURLDB",
    "HTTP_GET": "GET",
    "HTTP_HEAD": "HEAD",
    "HTTP_LABEL": "Étiquette",
    "HTTP_LOCK": "Verrouiller",
    "HTTP_MAILPOST": "MAILPOST",
    "HTTP_MERGE": "Fusionner",
    "HTTP_MKACTIVITY": "Mkactivity",
    "HTTP_MKCOL": "Mkcol",
    "HTTP_MKWORKSPACE": "Mkworkspace",
    "HTTP_MOVE": "MOVE",
    "HTTP_NOTIFY": "Notifier",
    "HTTP_OPTIONS": "Options",
    "HTTP_POLL": "Poll",
    "HTTP_PORTS_FORWARDED_TO_WEB_PROXY": "Ports HTTP transférés vers le proxy Web",
    "HTTP_POST": "POST",
    "HTTP_PROPFIND": "Propfind",
    "HTTP_PROPPATCH": "Proppatch",
    "HTTP_PROXY_DESC": "Le tunneling HTTP désigne la technique permettant d'encapsuler les communications effectuées à l'aide de différents protocoles réseau, généralement de type TCP/IP, et ce, par le biais du protocole HTTP.",
    "HTTP_PROXY_PORT": "HTTP Proxy Port",
    "HTTP_PROXY": "HTTP Proxy",
    "HTTP_PUT": "Put",
    "HTTP_REPORT": "Rapport",
    "HTTP_REQMOD": "Reqmod",
    "HTTP_REQUEST": "Demande HTTP",
    "HTTP_REQUESTS": "Demandes HTTP",
    "HTTP_RESPMOD": "Respmod",
    "HTTP_RESPONSE": "Réponse HTTP",
    "HTTP_RULE": "HTTP",
    "HTTP_SEARCH": "Rechercher",
    "HTTP_SECURITY_HEADERS": "En-têtes de sécurité HTTP",
    "HTTP_SERVICES": "Services HTTP",
    "HTTP_SUBSCRIBE": "S'abonner",
    "HTTP_TRACE": "Trace",
    "HTTP_TUNNEL_CONTROL": "Contrôle HTTP tunnel",
    "HTTP_TUNNEL": "Tunnel HTTP",
    "HTTP_UNCHECKOUT": "Uncheckout",
    "HTTP_UNKNOWN_DESC": " Cela identifie le trafic de proxy/pare-feu HTTP pour lequel il est impossible de déterminer une application plus granulaire.",
    "HTTP_UNKNOWN": "HTTP inconnu",
    "HTTP_UNLOCK": "Déverrouiller",
    "HTTP_UNSUBSCRIBE": "Se désabonner",
    "HTTP_UPDATE": "Mettre à jour",
    "HTTP_VERSIONCONTROL": "Contrôle des versions",
    "HTTP_VS_HTTPS": "HTTP contre HTTPS",
    "HTTP": "HTTP",
    "HTTP2_DESC": " Le protocole HTTP2.0 (Hypertext Transfer Protocol) permet de naviguer sur le Web.",
    "HTTP2": " Le protocole HTTP2.0 (Hypertext Transfer Protocol) permet de naviguer sur le Web.",
    "HTTPS_DESC": " HTTPS est la version sécurisée d'HTTP.",
    "HTTPS_PORTS_FORWARDED_TO_WEB_PROXY": "Ports HTTPS transférés au proxy Web",
    "HTTPS_PROXY_PORT": "HTTPS Proxy Port",
    "HTTPS_PROXY": "HTTPS Proxy",
    "HTTPS_RULE": "HTTPS",
    "HTTPS_SERVICES": "Services HTTPS",
    "HTTPS_SSL_TRAFFIC_TREND": "TENDANCE DU TRAFIC HTTPS et SSL",
    "HTTPS_SSL_TRAFFIC": "TRAFIC HTTP SSL",
    "HTTPS_UNKNOWN_DESC": " Cela identifie le trafic de proxy/pare-feu HTTPS pour lequel il est impossible de déterminer une application plus granulaire.",
    "HTTPS_UNKNOWN": "HTTPS inconnu",
    "HTTPS": "HTTPS",
    "HTTPTUNNEL_DESC": " Le tunneling HTTP désigne la technique permettant d'encapsuler les communications effectuées à l'aide de différents protocoles réseau, généralement de type TCP/IP, et ce, par le biais du protocole HTTP.",
    "HTTPTUNNEL": "HttpTunnel",
    "HUNGARY_EUROPE_BUDAPEST": "Europe/Budapest",
    "HUNGARY": "Hongrie",
    "HYPERVISOR_OS": "Hyperviseur",
    "HYPERVISOR_VERSION": "Version de l’hyperviseur",
    "I_AGREE": "J'approuve",
    "I_GAMER_DESC": " Sites Web dédiés aux mangas et aux jeux en ligne",
    "I_GAMER": "i-gamer",
    "I_PART_DESC": " Site taïwanais de rencontres en ligne",
    "I_PART": "i-part.com",
    "I_UNDERSTAND_THE_CONSEQUENCE_AND_WANT_TO_PROCEED": "Je comprends les conséquences et je veux continuer",
    "I_UNDERSTAND_THE_CONSEQUENCE": "Je comprends les conséquences et je veux continuer",
    "ICELAND_ATLANTIC_REYKJAVIK": "Atlantique/Reykjavik",
    "ICELAND": "Islande",
    "ICMP_ANY_DESC": "ICMP désigne l'un des principaux protocoles de la suite IP (Internet Protocol), utilisé par les appareils réseau, comme les routeurs, pour envoyer des messages d'erreur indiquant que le service demandé n'est pas disponible ou qu'un hôte ou un routeur n'est pas accessible. Le protocole ICMP peut aussi relayer des messages de requête.",
    "ICMP_ANY": "ICMP",
    "ICMP_DESC": " Le protocole ICMP (Internet Control Message Protocol) est l'un des principaux protocoles de la suite IP (Internet Protocol)",
    "ICMP_UNKNOWN_DESC": " Cela identifie le trafic de proxy/pare-feu UDP pour lequel il est impossible de déterminer une application plus granulaire.",
    "ICMP_UNKNOWN": "ICMP inconnu",
    "IDENT_DESC": " Le protocole IP (Identification Protocol) permet de déterminer l'identité de l'utilisateur d'une connexion TCP spécifique.",
    "IDENT": "Ident",
    "IDLE_TIME_DISASSOCIATION": "Durée d'inactivité avant la dissociation",
    "IDP_NAME": "Nom de l'IdP",
    "IDP": "IdP",
    "IGNORE_INSECURE_KEY": "Non sécurisé",
    "IKE_ALG": "Ce support LB ike alg",
    "IKE_DESC": "IKE désigne un protocole permettant d'obtenir du matériel de chiffrement authentifié à utiliser avec ISAKMP pour IPSEC.",
    "IKE_NAT_DESC": "IKE-NAT permet de traduire les adresses réseau pour les paquets ISAKMP et ESP.",
    "IKE_NAT": "IKE-NAT",
    "IKE": "IKE",
    "IKEA_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte ikea.com.",
    "IKEA": "ikea",
    "IKEV1_PHASE1": "IKE Version 1",
    "IKEV1_PHASE2": "IKE Version 2",
    "IKEV1": "IKE Version 1",
    "IKEV2_ALL_PHASES": "IKE Version 2",
    "IKEV2": "IKE Version 2",
    "IL_CENTRAL_1": "Israël (Tel Aviv)",
    "ILOVEIM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte iloveim.com.",
    "ILOVEIM": "ILoveIM",
    "ILS_DESC": "Internet Locator Service inclut LDAP, User Locator Service et LDAP sur TLS/SSL.",
    "ILS": "ILS",
    "IMAGE_HOST_DESC": " Sites proposant un hébergement, une redirection et/ou un partage de vidéos ou d'images.",
    "IMAGE_HOST": "Hébergement d'images",
    "IMAGE_ID": "Identifiant de l'image",
    "IMAGES": "Images",
    "IMAGESHACK_DESC": " Service gratuit de partage d'images en ligne",
    "IMAGESHACK": "ImageShack",
    "IMAP_DESC": "Le protocole IMAP (Internet Message Access Protocol) permet de récupérer les e-mails.",
    "IMAP": "IMAP",
    "IMDB_DESC": " Base de données d'informations en ligne sur les films et les programmes télévisés",
    "IMDB": "IMDb",
    "IMEEM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte imeem.com.",
    "IMEEM": "imeem",
    "IMEET_DESC": " Service de vidéoconférence en ligne basé sur le Cloud",
    "IMEET": "i-Meet",
    "IMESH_DESC": " iMesh désigne un protocole peer-to-peer.",
    "IMESH": "iMesh",
    "IMFRULESLOT": "Contrôle des applications de messagerie instantanée",
    "IMGUR_DESC": " Service gratuit d'hébergement d'images en ligne",
    "IMGUR": "imgur",
    "IMO": "imo",
    "IMOIM_DESC": " imo.im",
    "IMOIM": "imo.im",
    "IMP_DESC": " IMP désigne la messagerie Web IMAP du projet Horde.",
    "IMP": "IMP",
    "IMPERSONATION": "Usurpation d'identité",
    "IMPLUS_DESC": " IM+",
    "IMPLUS": "IM+",
    "IMPORT_ACCOUNT_ID": "Importer l'identifiant du compte",
    "IMPORT_PROJECT_ID": "Importer l'identifiant de projet",
    "IMPORT_SUBSCRIPTION_ID": "Importer l'identifiant de l'abonnement",
    "IMPORT": "Importer",
    "IMPRESS_DESC": " Site Web japonais dédié à l'actualité informatique",
    "IMPRESS": "impress",
    "IMVU_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte imvu.com.",
    "IMVU": "IMVU",
    "INACTIVE": "Inactif(e)",
    "INBOUND": "Entrée",
    "INBOX_DESC": " Portail inbox.com qui fournit un service gratuit de messagerie électronique",
    "INBOX": "Inbox",
    "INBYTES": "En octets",
    "INCLUDE_ADDRESS_RANGE": "Inclure la plage d’adresses",
    "INCLUDE_IP_ADDRESSES": "Inclure les adresses IP",
    "INCLUDE": "Inclure",
    "INCOMPLETE_DESC": " L'option Incomplet signifie que la signature du protocole est trop longue.",
    "INCOMPLETE": "Incomplet",
    "INDABA_MUSIC_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte indabamusic.com.",
    "INDABA_MUSIC": "Indaba Music",
    "INDIA_ASIA_CALCUTTA": "Asie/Calcutta",
    "INDIA_ASIA_KOLKATA": "Asie/Kolkata",
    "INDIA": "Inde",
    "INDIATIMES_DESC": " Cette signature détecte indiatimes et un grand nombre de ses sous-domaines. Il s'agit de l'un des portails Web de services Internet et mobiles à valeur ajoutée les plus populaires en Inde.",
    "INDIATIMES": "timesofindia",
    "INDONESIA_ASIA_JAKARTA": "Asie/Jakarta",
    "INDONESIA_ASIA_JAYAPURA": "Asie/Jayapura",
    "INDONESIA_ASIA_MAKASSAR": "Asie/Makassar",
    "INDONESIA_ASIA_PONTIANAK": "Asie/Pontianak",
    "INDONESIA": "Indonésie",
    "INDONETWORK_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte indonetwork.co.id.",
    "INDONETWORK": "Indonetwork",
    "INDOWEBSTER_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte indowebster.com.",
    "INDOWEBSTER": "Indowebster",
    "INFO": "Informations",
    "INFOARMOR_DESC": " InfoArmor est un leader du secteur pour la protection de l’identité des salariés qui utilise une intelligence de pointe contre les menaces",
    "INFOARMOR": "InfoArmor",
    "INFORMIX_DESC": " Informix est une gamme de systèmes de gestion de bases de données relationnelles développée par IBM. IBM a acquis la technologie Informix en 2001, mais cette dernière est née en 1981. Elle s'exécute sur les mainframes IBM et elle est également disponible pour Linux/Unix/Windows.",
    "INFORMIX": "Informix",
    "INGRESS_DETAILS": "Détails de l’entrée",
    "INILAH_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte inilah.com.",
    "INILAH": "inilah",
    "INSIGHTS": "Aperçus",
    "INST_GBL_METRICS": "Instance",
    "INSTAGRAM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné aux hôtes instagr.am et instagram.com. Il classifie aussi le trafic SSL vers le nom commun instagram.com.",
    "INSTAGRAM": "Instagram",
    "INSTANCE_ROLE": "Rôle de l'instance",
    "INTALKING_DESC": " Portail taïwanais dédié à la beauté et aux cosmétiques",
    "INTALKING": "intalking.com",
    "INTEGER_REQUIRED": "Entrez un nombre.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_LAN": "Le réseau local nécessite au moins une interface ou sous-interface activée.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_WAN": "Le WAN nécessite au moins une interface ou sous-interface activée, ainsi qu'un maximum de deux interfaces ou sous-interfaces activées.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE": "L’interface doit comporter au moins une sous-interface.",
    "INTERFACE_NAME": "Nom de l’interface",
    "INTERFACE_SHUTDOWN": "Arrêt de l’interface",
    "INTERFACE": "Interface",
    "INTERNAL_EXTERNAL_TRAFFIC": "Trafic interne/externe",
    "INTERNAL_GATEWAY_IP_ADDRESS": "Adresse IP de la passerelle interne",
    "INTERNAL": "Trafic interne",
    "INTERNATIONS_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte internations.org. Il classifie aussi le trafic SSL vers le nom commun internations.org.",
    "INTERNATIONS": "InterNations",
    "INTERNET_ACCESS": "Accès à Internet",
    "INTERNET_COMMUNICATION": "Communication Internet",
    "INTERNET_SERVICES_DESC": " Sites dédiés aux services Internet.",
    "INTERNET_SERVICES": "Services Internet",
    "INTERNET_USAGE_TREND": "Tendance de l'utilisation d'Internet",
    "INTERNET": "Internet",
    "INTERPARK_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte www.interpark.com. Il classifie aussi le trafic SSL vers le nom commun interpark.com.",
    "INTERPARK": "Interpark",
    "INTUIT_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte intuit.com.",
    "INTUIT": "Intuit",
    "INVALID_API_KEY": "Clé API non valide",
    "INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE": "Unité de taille maximale du lot non valide pour le type de SIEM. Ce nombre doit être compris entre 16 et 512 Ko.",
    "INVALID_NAME": "Veuillez entrer un nom valide",
    "INVALID_USERNAME_OR_PASSWORD": "identifiant de connexion ou mot de passe non valide",
    "IOS_APPSTORE_DESC": " Apple App Store est une plate-forme de distribution d'applications numériques pour iOS, développée et gérée par Apple Inc.",
    "IOS_APPSTORE": "Boutique d'applications iOS",
    "IOS_OTA_UPDATE_DESC": " iOS OTA Update désigne le protocole utilisé pour les mises à jour iOS Over The Air.",
    "IOS_OTA_UPDATE": "iOS OTA Update",
    "IOS_OTHERS": "iOS (autre)",
    "IOS_TUNES": "Application iTunes iOS",
    "IOT": "Trafic IoT",
    "IP_ABUSE_CHECK_DESCRIPTION": "Vérifier les adresses IP qui peuvent abuser des proxys",
    "IP_ABUSE_CHECK": "Vérification des abus d’IP",
    "IP_ADDRESS_FROM": "Adresse IP de début",
    "IP_ADDRESS_HA_DEVICE": "Entrez l’adresse IP virtuelle de l'appareil HA.",
    "IP_ADDRESS_LAN_SECTION": "Entrez une adresse IP pour la section LAN de votre appareil.",
    "IP_ADDRESS_OPTIONAL": "Adresse IP (facultatif)",
    "IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN": "Adresse IP ou FQDN ou FQDN joker",
    "IP_ADDRESS_OR_FQDN": "Adresse IP ou FQDN",
    "IP_ADDRESS_OR_WILDCARD_FQDN": "Adresse IP ou FQDN générique",
    "IP_ADDRESS_RANCE_CIDR": "Plages d’adresses IP / CIDR",
    "IP_ADDRESS_SHOULD_NOT_BE_PART_OF_ADDRESS_RANGES_POOL": "L’adresse IP ne peut pas faire partie du pool d’adresses DHCP.",
    "IP_ADDRESS_TO": "Adresse IP de fin",
    "IP_ADDRESS_WAN_SECTION": "Entrez une adresse IP pour la section WAN de votre appareil.",
    "IP_ADDRESS": "Adresse IP",
    "IP_ADDRESSES": "Adresses IP",
    "IP_ADDRESSESS": "Adresse IP",
    "IP_BASED_COUNTRIES": "Pays basés sur IP",
    "IP_CAT_LOOKUP": "Activer la consultation dynamique du cat IP",
    "IP_CATEGORIES": "Catégories d'IP",
    "IP_CONNECT_TRANSPARENT": "basculer les CONNECT to IP:port au mode transparent",
    "IP_DESC": " Le protocole IP (Internet Protocol) désigne le principal protocole de communication de la suite IP (Internet Protocol) pour relayer des datagrammes sur les limites du réseau.",
    "IP_EXAMPLE_WITH_RANGE_CIDR": "Par exemple : ********, ********, ******** à ********, ********/24",
    "IP_EXAMPLE": "Par exemple: ********, ********",
    "IP_FQDN_GROUPS": "Groupes IP et FQDN",
    "IP_INFO": "Informations IP",
    "IP_POOL": "Pool d'adresses IP",
    "IP_UNKNOWN_DESC": " Cela identifie le trafic IP pour lequel il est impossible de déterminer une application plus granulaire",
    "IP_UNKNOWN": "IP inconnue",
    "IP": "IP",
    "IP6_DESC": " IPv6 (Internet Protocol version 6) désigne la dernière version de protocole IP (Internet Protocol), qui fournit un système d'identification et de localisation pour les ordinateurs sur réseau et route le trafic sur Internet.",
    "IP6": "IP6",
    "IPASS_DESC": " iPass est le pionnier du secteur dans la connectivité des appareils mobiles à l’échelle mondiale. Elle assure un accès illimité à un contenu illimité sur un nombre illimité d’appareils.",
    "IPASS": " iPass est le pionnier du secteur dans la connectivité des appareils mobiles à l’échelle mondiale. Elle assure un accès illimité à un contenu illimité sur un nombre illimité d’appareils.",
    "IPERF_DESC": " Le protocole iperf permet à l'outil éponyme d'effectuer des mesures de performances réseau.",
    "IPERF": "Iperf",
    "IPLAYER_DESC": " iPlayer",
    "IPLAYER": "iPlayer",
    "IPSEC_DESC": " Le protocole IPsec fournit des services de sécurisation des communications hôtes. IPsec offre deux services de sécurité: AH (Authentication Header), qui permet d'authentifier l'expéditeur, et ESP (Encapsulating Security Payload), qui permet d'authentifier l'expéditeur et de chiffrer les données.",
    "IPSEC": "IPSEC",
    "IPV4_ALL_DESTINATION_GROUP_NAME": "IP V4 Tous les noms de groupe de destination",
    "IPV4_DNS_RESOLUTION_ONLY": "Résolution DNS IPv4 uniquement",
    "IPV4": "IPv4 encapsulation",
    "IPV6_HERE": "IPv6 i-am-here",
    "IPV6_WHERE": "IPv6 where-are-you",
    "IPV6": "En-tête IP6",
    "IPV6CP_DESC": " Ce protocole permet d'établir et de configurer un protocole IPv6 sur PPP.",
    "IPV6CP": "IPV6CP",
    "IPXRIP_DESC": " RIPIPX est l'équivalent du protocole RIP sur les réseaux Novell.",
    "IPXRIP": "RIPIPX",
    "IQIYI_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte iqiyi.com. Il classifie aussi le trafic SSL vers le nom commun iqiyi.com.",
    "IQIYI": "iqiyi.com",
    "IRAN_ASIA_TEHRAN": "Asie/Téhéran",
    "IRAN": "Iran",
    "IRAQ_ASIA_BAGHDAD": "Asie/Bagdad",
    "IRAQ": "Irak",
    "IRC_DESC": " IRC (Internet Relay Chat) désigne un protocole de messagerie instantanée.",
    "IRC_GALLERIA_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte irc-galleria.net.",
    "IRC_GALLERIA": "IRC-Galleria",
    "IRC_TRANSFER_DESC": " Ce protocole permet de transporter les données lors d'un transfert de fichiers IRC.",
    "IRC_TRANSFER": "IRC File Transfer",
    "IRC": "IRC",
    "IRCS_DESC": " IRCs est la version sécurisée du protocole IRC.",
    "IRCS": "IRC sécurisé",
    "IRELAND_EUROPE_DUBLIN": "Europe/Dublin",
    "IRELAND": "Irlande",
    "IS_NULL": "Est nul",
    "ISAE_3402": "ISAE 3402",
    "ISAKMP_DESC": " Le protocole ISAKMP (Internet Security Association and Key Management Protocol) définit les procédures et les formats de paquets afin d'établir, de négocier, de modifier et de supprimer les associations de sécurité (AS).",
    "ISAKMP": "ISAKMP",
    "ISLE_OF_MAN_EUROPE_ISLE_OF_MAN": "Europe/Ile de Man",
    "ISLE_OF_MAN": "Ile de Man",
    "ISRAEL_ASIA_JERUSALEM": "Asie/Jérusalem",
    "ISRAEL": "Israël",
    "ISSUER": "Émetteur",
    "ITALKI_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte italki.com. Il classifie aussi le trafic SSL vers le nom commun italki.com.",
    "ITALKI": "italki",
    "ITALY_EUROPE_ROME": "Europe/Rome",
    "ITALY": "Italie",
    "ITALYNORTH": "(Europe) Italie du Nord",
    "ITEMS_TOTAL": "Total des éléments",
    "ITSMY_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte mobile.itsmy.com.",
    "ITSMY": "GameCloud (itsmy.com)",
    "ITUNES_DESC": " iTunes est une application Apple propriétaire de lecture de médias numériques, qui permet de lire et d'organiser des fichiers audio et vidéo numériques.",
    "ITUNES": "iTunes",
    "ITUNESU": "iTunes U",
    "IVORY_COAST": "Côte d'Ivoire",
    "IWF": "IWF",
    "IWIW_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte iwiw.hu.",
    "IWIW": "Iwiw",
    "JABBER_DESC": " Jabber est un système de présence et de messagerie instantanée standard ouvert, qui utilise le protocole XMPP.",
    "JABBER_TRANSFER_DESC": " Le transfert Jabber est une norme ouverte de transfert de fichiers entre deux clients Jabber.",
    "JABBER_TRANSFER": "Transfert de fichiers Jabber",
    "JABBER": "Jabber",
    "JAIKU_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jaiku.com.",
    "JAIKU": "jaiku.com",
    "JAILBREAK": "Jailbreak activé",
    "JAILBROKEN_ROOTED": "Jailbroken/Rooted",
    "JAJAH_DESC": " Jajah est un fournisseur VoIP détenu par Telefonica Europe.",
    "JAJAH": "Jajah",
    "JAMAICA_AMERICA_JAMAICA": "Amérique/Jamaïque",
    "JAMAICA": "Jamaïque",
    "JAMMERDIRECT_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jammerdirect.com.",
    "JAMMERDIRECT": "Jammer Direct",
    "JANGO_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jango.com.",
    "JANGO": "Jango",
    "JANUS_END": "Fin des codes d’erreur Janus.",
    "JAPAN_ASIA_TOKYO": "Asie/Tokyo",
    "JAPAN": "Japon",
    "JAPANEAST": "(Asie-Pacifique) Est du Japon",
    "JAPANWEST": "(Asie-Pacifique) Ouest du Japon",
    "JAVA_UPDATE_DESC": " Java Update désigne le protocole de mise à jour des machines virtuelles Java, également appelées JVM.",
    "JAVA_UPDATE": "Java Update",
    "JEDI_DESC": " JEDI est le nom du protocole de connexion en streaming CITRIX.",
    "JEDI": "JEDI",
    "JERSEY_EUROPE_JERSEY": "Europe/Jersey",
    "JERSEY": "Jersey",
    "JINGDONG_DESC": " Boutique en ligne chinoise populaire, spécialisée dans le high-tech",
    "JINGDONG": "JingDong",
    "JIOINDIACENTRAL": "(Asie Pacifique) Jio Inde Centre",
    "JIOINDIAWEST": "(Asie Pacifique) Jio Inde Ouest",
    "JIRA_DESC": " Ce plug-in de protocole classe le trafic http vers l’hôte onjira.com. Il classe également le trafic ssl dans le nom commun onjira.com",
    "JIRA": "JIRA",
    "JIVE_DESC": " Jive Software est une société logicielle spécialisée dans les réseaux sociaux professionnels, dont le siège se trouve à Palo Alto (Californie).",
    "JIVE": "Jive",
    "JNE_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jne.co.id.",
    "JNE": "JNE",
    "JOB_EMPLOYMENT_SEARCH_DESC": " Sites dédiés à la recherche d'emploi.",
    "JOB_EMPLOYMENT_SEARCH": "Recherche d'emploi",
    "JOB_SEARCH_DESC": " Sites dédiés à la recherche d'emploi.",
    "JOB_SEARCH": "Recherche d'emploi",
    "JOBSTREET_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jobstreet.co.id.",
    "JOBSTREET": "JobStreet",
    "JOONGANG_DAILY_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné aux hôtes www.joins.com et www.joinsmsn.com.",
    "JOONGANG_DAILY": "Joongang Daily",
    "JOOST_DESC": " Joost",
    "JOOST": "Joost",
    "JORDAN_ASIA_AMMAN": "Asie/Amman",
    "JORDAN": "Jordanie",
    "JPEG": "Fichiers Jpeg",
    "JS_VIEW": "Vue jS",
    "JSON_CLOUDINFO_STAGGER_SIZE": "Nombre d’instances pour lesquelles le cloudinfo est envoyé de l’autorité de certification cloud à l’autorité de certification FCC",
    "JSON_CLOUDINFO": "Envoyer Cloudinfo de cloud CA à FCC CA au format JSON",
    "JSONNOTFOUND": "Impossible de trouver le fichier Json",
    "JUBII_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte jubii.dk.",
    "JUBII": "Jubii",
    "JUSTIN_TV_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte justin.tv. Il classifie aussi le trafic SSL vers le nom commun justin.tv.",
    "JUSTIN_TV": "Justin.tv",
    "K_12_SEX_EDUCATION": "Education sexuelle de la maternelle au lycée",
    "K_12": "Enseignement de la maternelle au lycée",
    "KAIOO_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kaioo.com.",
    "KAIOO": "kaioo.com",
    "KAIXIN_CHAT_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kaixin001.com.",
    "KAIXIN_CHAT": "kaixin",
    "KAKAKU_DESC": " Site Web dédié aux comparatifs de prix et aux tests de produits",
    "KAKAKU": "kakaku.com",
    "KAKAOTALK_DESC": " KakaoTalk est une plate-forme de messagerie instantanée pour les appareils mobiles: les utilisateurs ou groupes d'utilisateurs peuvent envoyer des messages et partager des photos, des vidéos et des coordonnées.",
    "KAKAOTALK": "KakaoTalk",
    "KANKAN_DESC": " Site Web chinois de streaming vidéo",
    "KANKAN": "kankan.com",
    "KAPANLAGI_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kapanlagi.com.",
    "KAPANLAGI": "KapanLagi",
    "KAROSGAME_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte karosgame.ru.",
    "KAROSGAME": "Karos (karosgame.ru)",
    "KASKUS_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kaskus.co.id.",
    "KASKUS": "Kaskus",
    "KASPERSKY_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kaspersky.com.",
    "KASPERSKY_UPDATE_DESC": " Kaspersky_update désigne le protocole de mise à jour des logiciels Kaspersky.",
    "KASPERSKY_UPDATE": "Kaspersky Update",
    "KASPERSKY": "Kaspersky",
    "KAZAA_DESC": " KaZaA désigne un protocole peer-to-peer.",
    "KAZAA": "KaZaA",
    "KAZAKHSTAN_ASIA_ALMATY": "Asie/Almaty",
    "KAZAKHSTAN_ASIA_AQTAU": "Asie/Aktaou",
    "KAZAKHSTAN_ASIA_AQTOBE": "Asie/Aktioubé",
    "KAZAKHSTAN_ASIA_ORAL": "Asie/Oural",
    "KAZAKHSTAN_ASIA_QYZYLORDA": "Asie/Kyzylorda",
    "KAZAKHSTAN": "Kazakhstan",
    "KB_BANK_DESC": " Ce plug-in de protocole classe le trafic HTTP destiné à l'hôte .kbstar.com. Il classe également le trafic SSL vers le nom commun .kbstar.com",
    "KB_BANK": "KB Bank (kbstar.com)",
    "KBS_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte www.kbs.co.kr.",
    "KBSTAR_DESC": " Ce service Web est fermé.",
    "KBSTAR": "kbstar (Fermé)",
    "KEEPLIVE": "Envoyer des enregistrements keepalive pour maintenir la connexion TCP active si les données ne circulent pas",
    "KEEZMOVIES_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte keezmovies.com.",
    "KEEZMOVIES": "KeezMovies",
    "KEK_ROTATION": "activer la rotation de clé sur SME ",
    "KEMENKUMHAM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kemenkumham.go.id.",
    "KEMENKUMHAM": "kemenkumham.go.id",
    "KENYA_AFRICA_NAIROBI": "Afrique/Nairobi",
    "KENYA": "Kenya",
    "KERBEROS_SEC_DESC": "Kerberos désigne un protocole d'authentification réseau qui fonctionne sur la base de tickets: il permet à des nœuds de communiquer sur un réseau non sécurisé pour se prouver mutuellement leur identité de manière sécurisée.",
    "KERBEROS_SEC": "Kerberos",
    "KERBEROS_SHARED_KEY": "Mot de passe de confiance du domaine",
    "KEY": "Clé",
    "KHAN_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte khan.co.kr.",
    "KHAN": "Khan (khan.co.kr)",
    "KHANACADEMY": "Académie Khan",
    "KICKASSTORRENTS_DESC": " KickAssTorrents est un moteur de recherche de torrents et de magnets populaire",
    "KICKASSTORRENTS": " KickAssTorrents est un moteur de recherche de torrents et de magnets populaire",
    "KIK_DESC": " Kik Messenger est un service chinois de messagerie instantanée.",
    "KIK": "Kik Messenger",
    "KINDLE": "Kindle",
    "KINO_DESC": " Ce plug-in de protocole classe le trafic HTTP destiné à l'hôte kino.to",
    "KINO": "Kino",
    "KIRIBATI_PACIFIC_ENDERBURY": "Pacifique/Enderbury",
    "KIRIBATI_PACIFIC_KIRITIMATI": "Pacifique/Christmas",
    "KIRIBATI_PACIFIC_TARAWA": "Pacifique/Tarawa",
    "KIRIBATI": "Kiribati",
    "KIWIBOX_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kiwibox.com.",
    "KIWIBOX": "Kiwibox",
    "KLIKBCA_DESC": " Ce plug-in de protocole classe le trafic HTTP destiné à l'hôte klikbca.com.",
    "KLIKBCA": "KlikBCA",
    "KOMPAS_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kompas.com.",
    "KOMPAS": "Kompas",
    "KOMPASIANA_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte kompasiana.com.",
    "KOMPASIANA": "Kompasiana",
    "KONAMINET_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte konaminet.jp. Il classifie aussi le trafic SSL vers le nom commun konaminet.jp.",
    "KONAMINET": "KONAMI",
    "KOOLIM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte koolim.com.",
    "KOOLIM": "KoolIm",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG": "Asie/Pyongyang",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF": "Corée (République populaire démocratique de)",
    "KOREA_REPUBLIC_OF_ASIA_SEOUL": "Asie/Séoul",
    "KOREA_REPUBLIC_OF": "Corée (République de)",
    "KOREA": "Corée",
    "KOREACENTRAL": "(Asie-Pacifique) Corée centrale",
    "KOREASOUTH": "(Asie-Pacifique) Sud de la Corée",
    "KUWAIT_ASIA_KUWAIT": "Asie/Koweït",
    "KUWAIT": "Koweït",
    "KYRGYZSTAN_ASIA_BISHKEK": "Asie/Bichkek",
    "KYRGYZSTAN": "Kirghizistan",
    "L2TP_DESC": " Le protocole L2TP (Layer Two Tunneling Protocol) désigne une extension du protocole PPTP (Point-to-Point Tunneling Protocol) utilisé par un fournisseur d'accès Internet (FAI) pour faire fonctionner un réseau VPN (Virtual Private Network) sur Internet.",
    "L2TP": "L2TP",
    "LAN_DESTINATIONS_GROUP": "Groupe de destinations LAN",
    "LAN_DNS": "DNS LAN",
    "LAN_IP_GROUP": "Groupe d'adresses IP LAN",
    "LAN_PRI_DNS": "Serveur DNS principal du LAN",
    "LAN_RX": "LAN Rx",
    "LAN_SEC_DNS": "Serveur DNS secondaire du LAN",
    "LAN_TX": "LAN Tx",
    "LAN": "LAN",
    "LANGUAGE": "Langue",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE": "Asie/Vientiane",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC": "République démocratique populaire lao",
    "LAOS": "Laos",
    "LARGE": "Volumineux",
    "last_1_hour": "Dernière heure",
    "LAST_1_HOUR": "Dernière heure",
    "last_1_min": "Dernière min.",
    "LAST_1_MIN": "Dernière minute",
    "last_1_month": "Mois dernier",
    "LAST_1_MONTH": "Mois dernier",
    "last_1_week": "Semaine dernière",
    "LAST_1_WEEK": "Semaine dernière",
    "last_10_hours": "10 dernières heures",
    "LAST_10_HOURS": "10 dernières heures",
    "last_15_mins": "15 dernières min.",
    "LAST_15_MINS": "15 dernières minutes",
    "last_2_hours": "2 dernières heures",
    "LAST_2_HOURS": "2 dernières heures",
    "last_2_mins": "2 dernières min.",
    "LAST_2_MINS": "2 dernières minutes",
    "LAST_24_HOURS": "24 dernières heures",
    "last_24_hrs": "24 dernières heures",
    "last_30_mins": "30 dernières min.",
    "LAST_30_MINS": "30 dernières minutes",
    "last_5_hours": "5 dernières heures",
    "LAST_5_HOURS": "5 dernières heures",
    "last_5_mins": "5 dernières min.",
    "LAST_5_MINS": "5 dernières minutes",
    "LAST_ACTIVE": "Dernière activité",
    "LAST_CONFIG_TEMPLATE_PUSH_FAILED": "La dernière poussée du modèle de configuration a échoué",
    "LAST_CONNECTIVITY_TEST": "Dernier test de connectivité",
    "LAST_HEARTBEAT_RECEIVED_ON": "Dernière pulsation reçue le",
    "LAST_KNOW_IP": "Dernière adresse IP connue",
    "LAST_KNOWN_IP": "Dernière adresse IP connue",
    "LAST_MODIFIED_BY": "Dernière modification par",
    "LAST_MODIFIED_ON": "Dernière modification le",
    "LAST_SYNC": "Dernière synchronisation",
    "LAST_UPDATE": "Dernière mise à jour",
    "LAST_UPDATES": "Dernières mises à jour",
    "LAST_UPGRADE_ON": "Dernière mise à niveau le",
    "LASTEST_SYNC": "Dernière synchronisation",
    "LATITUDE": "Latitude",
    "LATVIA_EUROPE_RIGA": "Europe/Riga",
    "LATVIA": "Lettonie",
    "LAUNCH_CLOUDFORMATION_TEMPLATE_AWS_CONSOLE": "Lancer le modèle Cloudformation dans la console AWS",
    "LAUNCH_CLOUDFORMATION": "Lancer Cloudformation",
    "LDAP_CONNECTION_DOWN": "Connexion LDAP interrompue",
    "LDAP_DESC": " Le protocole LDAP (Lightweight Directory Access Protocol) permet d'accéder aux services d'annuaire. Les environnements Windows utilisent ce protocole pour envoyer des requêtes à Active Directory.",
    "LDAP_FAILURE": "Echec LDAP",
    "LDAP_SETTINGS": "Paramètres LDAP",
    "LDAP_SUCCESS": "Réussite LDAP",
    "LDAP": "LDAP",
    "LDAPS_DESC": " Le protocole LDAP sécurisé est la version sécurisée du protocole LDAP.",
    "LDAPS": "LDAPS",
    "LEARN_TO_SETUP_EC2_INSTANCE": "Découvrez comment configurer l’instance EC2",
    "LEBANON_ASIA_BEIRUT": "Asie/Beyrouth",
    "LEBANON": "Liban",
    "LESOTHO_AFRICA_MASERU": "Afrique/Maseru",
    "LESOTHO": "Lesotho",
    "LESS_THAN": "Inférieur à",
    "LIBERIA_AFRICA_MONROVIA": "Afrique/Monrovia",
    "LIBERIA": "Libéria",
    "LIBYA": "Libye",
    "LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI": "Afrique/Tripoli",
    "LIBYAN_ARAB_JAMAHIRIYA": "Jamahiriya arabe libyenne",
    "LIECHTENSTEIN_EUROPE_VADUZ": "Europe/Vaduz",
    "LIECHTENSTEIN": "Liechtenstein",
    "LIMITED_AVAILABILITY": "Disponibilité limitée",
    "LIMITED": "Limité",
    "LINGERIE_BIKINI": "Lingerie/Bikini",
    "LINK_SCORE": "Score de liaison",
    "LINUX_OS": "Linux OS",
    "LINUX": "Linux",
    "LITHUANIA_EUROPE_VILNIUS": "Europe/Vilnius",
    "LITHUANIA": "Lituanie",
    "LOAD_BALANCER_IP_ADDRESS": "Adresse IP de l’équilibreur de charge",
    "LOAD_BALANCER": "Équilibreur de charge",
    "LOAD_MORE": "Charger plus",
    "LOC_DEFAULT": "Road Warrior",
    "LOCAL_EGRESS": "Sortie locale",
    "LOCAL_TIME_ZONE_CC_GROUP": "Fuseau horaire local du groupe de connecteurs cloud",
    "LOCATION_ALREADY_IN_USE_PLEASE_ENTER_A_NEW_LOCATION": "L'emplacement n'est pas valable. Veuillez en entrer un autre.",
    "LOCATION_CREATION": "Création d'emplacement",
    "LOCATION_DETAILS_OPTIONAL": "Emplacement à emplacement",
    "LOCATION_DETAILS": "Détails de l'emplacement",
    "LOCATION_GROUP_TYPE": "Type d'emplacement",
    "LOCATION_GROUP": "Groupe d'emplacements",
    "LOCATION_GROUPS": "Groupes d’emplacements",
    "LOCATION_INFORMATION": "Informations sur l'emplacement",
    "LOCATION_MANAGEMENT": "Gestion des emplacements",
    "LOCATION_NAME": "Nom de l’emplacement",
    "LOCATION_SUBLOCATION": "Emplacement/Emplacement secondaire",
    "LOCATION_TEMPLATE": "Modèle d'emplacement",
    "LOCATION_TEMPLATES": "Modèles d'emplacement",
    "LOCATION_TYPE": "Type d'emplacement",
    "LOCATION_UNAUTHENTICATED_AUP_FREQUENCY": "Fréquence AUP personnalisée (jours)",
    "LOCATION": "Emplacement",
    "LOCATIONS": "Emplacements",
    "LOG_AND_CONTROL_FORWARDING": "Consigner et contrôler les transferts",
    "LOG_AND_CONTROL_GATEWAY": "Passerelle d'enregistrement et de contrôle",
    "LOG_AND_CONTROL_GW": "Journal et passerelle de contrôle",
    "LOG_AND_CONTROL": "Journal et contrôle",
    "LOG_GW_CONN_CLOSE": "Fermeture de la connexion active de la passerelle de journal.",
    "LOG_GW_CONN_SETUP_FAIL": "Échec de la configuration de la connexion à la passerelle de journal (erreur interne).",
    "LOG_GW_CONNECT_FAIL": "Échec de la connexion à la passerelle de journal (erreur réseau).",
    "LOG_GW_DNS_RESOLVE_FAIL": "Échec de la résolution DNS de la passerelle de journal.",
    "LOG_GW_KA_FAIL": "Échec de la connexion de la passerelle de journal keepalive.",
    "LOG_GW_NO_CONN": "Connexion à la passerelle de journal non encore lancée par le client.",
    "LOG_GW_PAC_RESOLVE_FAIL": "Échec de la résolution PAC de la passerelle de journal.",
    "LOG_GW_PAC_RESOLVE_NOIP": "Consignez que la résolution du PAC de la passerelle n’a renvoyé aucun IPS.",
    "LOG_GW_PROTO_MSG_ERROR": "Erreur de format de message dans la réponse GW du journal.",
    "LOG_GW_SRV_ERR_RESPONSE": "La passerelle de journal a reçu une réponse d'erreur HTTP du serveur.",
    "LOG_GW_UNHEALTHY": "L'intégrité de la passerelle de journal n'est pas bonne (état transitoire).",
    "LOG_INFO_TYPE": "Type d'information de journal",
    "LOG_STREAMING": "Streaming du journal",
    "LOG_TIME": "Heure de consignation",
    "LOG_TYPE": "TYPE DE JOURNAL",
    "LOGGED_TIME": "Durée consignée",
    "LOGGING": "Journalisation",
    "LOGIN_ID": "ID de connexion",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_GROUP": "Sélectionnez un nom de groupe APP Connector dans le menu déroulant.",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_KEY": "Sélectionnez une clé de provisionnement dans le menu déroulant",
    "LOGIN_TYPE": "Type de connexion",
    "LOGIN": "Connexion",
    "LOGS": "Journaux",
    "LONGITUDE": "Longitude",
    "LOOKUP_URL_CATEGORY": "Classements des URL de recherche",
    "LOOKUP": "URL de recherche",
    "LUXEMBOURG_EUROPE_LUXEMBOURG": "Europe/Luxembourg",
    "LUXEMBOURG": "Luxembourg",
    "MAC_ADDRESS": "Adresse MAC",
    "MAC_OS": "Mac",
    "MACAO_ASIA_MACAU": "Asie/Macao",
    "MACAO": "Macao",
    "MACAU": "Macao",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE": "Europe/Skopje",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF": "Macédoine (ex?République yougoslave de)",
    "MACEDONIA": "Macédoine",
    "MADAGASCAR_INDIAN_ANTANANARIVO": "Océan Indien/Antananarivo",
    "MADAGASCAR": "Madagascar",
    "MALAWI_AFRICA_BLANTYRE": "Afrique/Blantyre",
    "MALAWI": "Malawi",
    "MALAYSIA_ASIA_KUALA_LUMPUR": "Asie/Kuala Lumpur",
    "MALAYSIA_ASIA_KUCHING": "Asie/Kuching",
    "MALAYSIA": "Malaisie",
    "MALDIVES_INDIAN_MALDIVES": "Océan Indien/Maldives",
    "MALDIVES": "Maldives",
    "MALI_AFRICA_BAMAKO": "Afrique/Bamako",
    "MALI": "Mali",
    "MALICIOUS_TLD": "TLD malveillants",
    "MALTA_EUROPE_MALTA": "Europe/Malte",
    "MALTA": "Malte",
    "MALWARE_SITE": "Contenu malveillant",
    "MANAGED_APP_DEF_ZPA": "Définition des applications gérées avec ZPA",
    "MANAGED_APP_DEF": "Définition de l'application gérée",
    "Management IP": "IP de gestion",
    "MANAGEMENT_DEFAULT_GATEWAY": "Passerelle de gestion par défaut",
    "MANAGEMENT_DETAILS": "Détails de gestion",
    "MANAGEMENT_DNS_SERVER": "Serveur DNS de gestion",
    "MANAGEMENT_INFORMATION": "Informations de gestion",
    "MANAGEMENT_INTERFACE": "Interface de gestion",
    "MANAGEMENT_IP_ADDRESS_POOL": "Pool d'adresses IP de gestion",
    "MANAGEMENT_IP_ADDRESS": "Adresse IP de gestion",
    "MANAGEMENT_IP": "IP de gestion",
    "MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS": "Adresse IP de la passerelle de gestion sortante",
    "MANAGEMENT": "Gestion",
    "MANUAL_MANAGEMENT_IP": "IP de gestion manuelle",
    "MANUAL_SERVICE_IP": "IP de service manuel",
    "MANUAL": "Manuel",
    "MARIJUANA": "Marijuana",
    "MARSHALL_ISLANDS_PACIFIC_KWAJALEIN": "Pacifique/Kwajalein",
    "MARSHALL_ISLANDS_PACIFIC_MAJURO": "Pacifique/Majuro",
    "MARSHALL_ISLANDS": "Iles Marshall",
    "MARTINIQUE_AMERICA_MARTINIQUE": "Amérique/Martinique",
    "MARTINIQUE": "Martinique",
    "MATURE_HUMOR": "Humour mature",
    "MAURITANIA_AFRICA_NOUAKCHOTT": "Afrique/Nouakchott",
    "MAURITANIA": "Mauritanie",
    "MAURITIUS_INDIAN_MAURITIUS": "Océan Indien/Maurice",
    "MAURITIUS": "Maurice",
    "MAX_AMF_NUMBER": "Un maximum de 5 AMF peut être ajouté.",
    "MAX_CAPACITY": "Capacité maximale",
    "MAX_CHARACTER_LIMIT_EXCEEDED": "Dépassement de la limite maximale de caractères",
    "MAX_EC_COUNT": "Nombre maximal",
    "MAX_INTERFACES_NUMBER": "Le nombre maximum d’interfaces a été ajouté.",
    "MAX_LEASE_TIME": "Durée de bail maximale (s)",
    "MAX_NUM_DESINATION_ADRESS_IS_1000": "Le nombre maximal d’adresses de destination autorisées par règle est de 1000.",
    "MAX_REUSE_PROVISIONING_KEY": "Réutilisation maximale de la clé de provisionnement",
    "MAX_STATIC_ROUTES_NUMBER": "Le nombre maximum de routes statiques est de 32.",
    "MAX_SUB_INTERFACES_NUMBER": "Le nombre maximum d’interfaces a été atteint.",
    "MAX_SUBINTERFACE_STATIC_LEASES": "Le nombre maximum de baux statiques est de 32.",
    "MAX_USER_TUNNELS_PER_CC": "Nombre maximum de tunnels utilisateur par connecteur",
    "MAX_VALUE_LIMIT_ERROR": "La valeur a dépassé la limite de",
    "MAX_WAN_INTERFACES_NUMBER": "Un maximum de 2 interfaces WAN peuvent être ajoutées.",
    "MAX": "Max",
    "MAYOTTE_INDIAN_MAYOTTE": "Océan Indien/Mayotte",
    "MAYOTTE": "Mayotte",
    "ME_CENTRAL_1": "Proche-Orient (EAU)",
    "ME_CENTRAL1_A": "me-central1-a",
    "ME_CENTRAL1_B": "me-central1-b",
    "ME_CENTRAL1_C": "me-central1-c",
    "ME_CENTRAL1": "me-central1",
    "ME_SOUTH_1": "Moyen-Orient (Bahreïn)",
    "ME_SOUTH_1A": "me-south-1a",
    "ME_SOUTH_1B": "me-south-1b",
    "ME_SOUTH_1C": "me-south-1c",
    "ME_WEST1_A": "me-west1-a",
    "ME_WEST1_B": "me-west1-b",
    "ME_WEST1_C": "me-west1-c",
    "ME_WEST1": "me-west1",
    "MEDIUM": "Moyen",
    "MEMORY": "Mémoire",
    "MEXICO_AMERICA_CANCUN": "Amérique/Cancun",
    "MEXICO_AMERICA_CHIHUAHUA": "Amérique/Chihuahua",
    "MEXICO_AMERICA_HERMOSILLO": "Amérique/Hermosillo",
    "MEXICO_AMERICA_MAZATLAN": "Amérique/Mazatlan",
    "MEXICO_AMERICA_MERIDA": "Amérique/Merida",
    "MEXICO_AMERICA_MEXICO_CITY": "Amérique/Mexico",
    "MEXICO_AMERICA_MONTERREY": "Amérique/Monterrey",
    "MEXICO_AMERICA_TIJUANA": "Amérique/Tijuana",
    "MEXICO": "Mexique",
    "MGCP_CA_DESC": "Service AC MGCP (Media Gateway Control Protocol)",
    "MGCP_CA": "Agent d'appel MGCP",
    "MGCP_DESC": " Le protocole MGCP est utilisé comme protocole de signalement pour les applications de VoIP.",
    "MGCP_UA_DESC": "Service d'agent utilisateur MGCP (Media Gateway Control Protocol)",
    "MGCP_UA": "Agent utilisateur MGCP",
    "MGCP": "MGCP",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE": "Pacifique/Kosrae",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE": "Pacifique/Ponape",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK": "Pacifique/Truk",
    "MICRONESIA_FEDERATED_STATES_OF": "Micronésie (Etats fédérés de)",
    "MICRONESIA": "Micronésie",
    "MICROSOFT_AZURE": "Microsoft Azure AD",
    "MICROSOFT_HYPER_V": "Microsoft Hyper V",
    "MILITANCY_HATE_AND_EXTREMISM": "Militantisme/Haine et extrémisme",
    "MILITARY": "Militaire",
    "MIN_CAPACITY": "Capacité minimale",
    "MIN_VALUE_LIMIT_ERROR": "La valeur est inférieure à la limite de",
    "MINUTES": "Minutes",
    "MISCELLANEOUS_OR_UNKNOWN": "Divers ou inconnu",
    "MODEL_NUMBER": "Numéro de modèle",
    "MODEL": "Modèle",
    "MOLDOVA_EUROPE_CHISINAU": "Europe/Chisinau",
    "MOLDOVA": "Moldavie",
    "MONACO_EUROPE_MONACO": "Europe/Monaco",
    "MONACO": "Monaco",
    "MONDAY": "Lundi",
    "MONGOLIA_ASIA_CHOIBALSAN": "Asie/Choybalsan",
    "MONGOLIA_ASIA_HOVD": "Asie/Hovd",
    "MONGOLIA_ASIA_ULAANBAATAR": "Asie/Oulan-Bator",
    "MONGOLIA": "Mongolie",
    "MONTENEGRO_EUROPE_PODGORICA": "Europe/Podgorica",
    "MONTENEGRO": "Monténégro",
    "MONTSERRAT_AMERICA_MONTSERRAT": "Amérique/Montserrat",
    "MONTSERRAT": "Montserrat",
    "MORE_ITEMS_SELECTED": "Plus d’éléments sélectionnés",
    "MOROCCO_AFRICA_CASABLANCA": "Afrique/Casablanca",
    "MOROCCO": "Maroc",
    "MOZAMBIQUE_AFRICA_MAPUTO": "Afrique/Maputo",
    "MOZAMBIQUE": "Mozambique",
    "MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS": "Guide de déploiement Microsoft Azure pour NSS",
    "MSI_URL_32BITS": "URL MSI (32 bits)",
    "MSI_URL_64BITS": "URL MSI (64 bits)",
    "MSN_DESC": " Le protocole MSN permet d'échanger des messages instantanés. Le protocole MSN est utilisé par le logiciel Microsoft Messenger.",
    "MSN_EXPLORER": "Explorateur MSN",
    "MSN_GROUPS_DEPRECATED": "MSN Groups",
    "MSN_GROUPS_DESC": " MSN Groups faisait partie du réseau MSN, qui hébergeait des communautés en ligne, et contenait des pages Web, des images hébergées et un espace pour les messages. MSN Groups a fermé en février 2009 dans le cadre de la migration des applications et services en ligne vers la marque Windows Live, qui est par la suite devenue Windows Live Groups.",
    "MSN_GROUPS": "MSN Groups",
    "MSN_MESSENGER": "MSN Messenger",
    "MSN_MSDW": "MSN MSDW",
    "MSN_SEARCH_DESC": " Ce protocole permet d'envoyer des requêtes utilisateur vers le moteur de recherche MSN Live.",
    "MSN_SEARCH": "MSN Search",
    "MSN_VIDEO_DESC": " Ce protocole est utilisé par MSN Messenger pour les conversations vidéo (obsolète depuis MSN 8.X).",
    "MSN_VIDEO": "MSN Video",
    "MSN_WEB_MESSENGER": "MSN Messenger",
    "MSN": "MSN",
    "MSNMOBILE_DESC": " MSN Mobile désigne la messagerie instantanée mobile de MSN.",
    "MSNMOBILE": "MSN Mobile",
    "MTS_ERROR": "Erreur du serveur d'applications MTS.",
    "MTU_TITLE": "MTU",
    "MULTIFEEDLOG": "Indicateurs",
    "MULTIPLE_APPLIANCES_ADDED_INFO": "Vous pouvez voir les nouvelles appliances sur la page Appliances.Pour plus d'informations, voir le {1}portail d'aide{2} de Connectors.",
    "MULTIPLE_APPLIANCES_ADDED": "de nouvelles appliances ont été ajoutés à votre tenant.",
    "MULTIPLE": "Multiple",
    "MUSIC": "Musique",
    "MY_ACTIVATION_STATUS": "MON STATUT D’ACTIVATION",
    "MY_PROFILE": "Mon profil",
    "MYANMAR_ASIA_RANGOON": "Asie/Rangoun",
    "MYANMAR": "Myanmar",
    "NA": "S/O",
    "NAME_MAX_LIMIT_ERROR": "Ce champ ne peut pas comporter plus de 255 caractères",
    "NAME_VALUE_PAIRS": "Paires de valeurs de nom",
    "NAME": "Nom",
    "NAMESPACE_OPTIONAL": "Espace de noms (facultatif)",
    "NAMESPACE": "Espace de noms",
    "NAMIBIA_AFRICA_WINDHOEK": "Afrique/Windhoek",
    "NAMIBIA": "Namibie",
    "NANOLOG_STREAMING_SERVICES": "Nanolog Streaming Service",
    "NAT_IP_ADDRESS": "Adresse IP NAT",
    "NAURU_PACIFIC_NAURU": "Pacifique/Nauru",
    "NAURU": "Nauru",
    "NAVIGATE_TO_ADMINISTRATION": "Accéder à Administration",
    "NEPAL_ASIA_KATMANDU": "Asie/Katmandou",
    "NEPAL": "Népal",
    "NET_MASK": "Masque de réseau",
    "NETBIOS_DESC": "Service de nom/datagramme/session NetBIOS",
    "NETBIOS": "NetBIOS",
    "NETHERLANDS_ANTILLES_AMERICA_CURACAO": "Amérique/Curaçao",
    "NETHERLANDS_ANTILLES": "Antilles néerlandaises",
    "NETHERLANDS_EUROPE_AMSTERDAM": "Europe/Amsterdam",
    "NETHERLANDS": "Pays-Bas",
    "NETMEETING_DESC": "Microsoft NetMeeting permet aux utilisateurs de participer à des téléconférences par Internet.",
    "NETMEETING_ILS_DESC": " NetMeeting ILS désigne le protocole utilisé entre NetMeeting et Internet Locator Servers (ILS). NetMeeting est un client de vidéoconférence VoIP et multipoint inclus dans de nombreuses versions de Microsoft Windows.",
    "NETMEETING_ILS": "NetMeeting ILS",
    "NETMEETING": "NetMeeting",
    "NETWORK_INTERFACE_ID": "ID de l'interface réseau",
    "NETWORK_PROTOCOL_ADFS": "Tout FS distribué",
    "NETWORK_PROTOCOL_AH": "IP6 Auth Header",
    "NETWORK_PROTOCOL_AHIP": "tout protocole interne de l'hôte",
    "NETWORK_PROTOCOL_APES": "tout encr. privé schéma",
    "NETWORK_PROTOCOL_ARGUS": "Argus",
    "NETWORK_PROTOCOL_AX25": "Cadres AX.25",
    "NETWORK_PROTOCOL_BHA": "BHA",
    "NETWORK_PROTOCOL_BLT": "Transfert de données groupées",
    "NETWORK_PROTOCOL_BRSATMON": "Surveillance BackRoom SATNET",
    "NETWORK_PROTOCOL_CARP": "CARP",
    "NETWORK_PROTOCOL_CFTP": "CFTP",
    "NETWORK_PROTOCOL_CHAOS": "Chaos",
    "NETWORK_PROTOCOL_CMTP": "Contrôle du transport des messages",
    "NETWORK_PROTOCOL_CPHB": "Comp. Prot. HeartBeat",
    "NETWORK_PROTOCOL_CPNX": "Comp. Prot. Net. Executive",
    "NETWORK_PROTOCOL_DDP": "Livraison de datagramme",
    "NETWORK_PROTOCOL_DGP": "prot. de passerelle différente",
    "NETWORK_PROTOCOL_DSTOPTS": "Option de destination IP6",
    "NETWORK_PROTOCOL_EGP": "protocole de passerelle externe",
    "NETWORK_PROTOCOL_EMCON": "EMCON",
    "NETWORK_PROTOCOL_ENCAP": "en-tête d'encapsulation",
    "NETWORK_PROTOCOL_EON": "ISO cnlp",
    "NETWORK_PROTOCOL_ESP": "IP6 Encap Sec. Payload",
    "NETWORK_PROTOCOL_ETHERIP": "Encapsulation Ethernet IP",
    "NETWORK_PROTOCOL_FRAGMENT": "En-tête de fragmentation IP6",
    "NETWORK_PROTOCOL_GGP": "gateway^2 (déprécié)",
    "NETWORK_PROTOCOL_GMTP": "GMTP",
    "NETWORK_PROTOCOL_GRE": "Encap. de routage général",
    "NETWORK_PROTOCOL_HELLO": "protocole de routage hello",
    "NETWORK_PROTOCOL_HMP": "Surveillance des hôtes",
    "NETWORK_PROTOCOL_ICMP": "protocole de messages de contrôle",
    "NETWORK_PROTOCOL_ICMPV6": "ICMP6",
    "NETWORK_PROTOCOL_IDP": "xns idp",
    "NETWORK_PROTOCOL_IDPR": "Routage de la stratégie InterDomain",
    "NETWORK_PROTOCOL_IDRP": "InterDomain",
    "NETWORK_PROTOCOL_IGMP": "protocole de gestion de groupe",
    "NETWORK_PROTOCOL_IGP": "NSFNET-IGP",
    "NETWORK_PROTOCOL_IGRP": "Cisco/GXS IGRP",
    "NETWORK_PROTOCOL_IL": "IL transport protocol",
    "NETWORK_PROTOCOL_INLSP": "Integ. Sécurité de la couche de réseau",
    "NETWORK_PROTOCOL_INP": "Merit Internodal",
    "NETWORK_PROTOCOL_IP": "fictif pour IP",
    "NETWORK_PROTOCOL_IPCOMP": "compression de charge utile (IPComp)",
    "NETWORK_PROTOCOL_IPCV": "Utilitaire Packet Core",
    "NETWORK_PROTOCOL_IPEIP": "IP encapsulé dans IP",
    "NETWORK_PROTOCOL_IPPC": "Noyau du paquet pluribus",
    "NETWORK_PROTOCOL_IPV4": "IPv4 encapsulation",
    "NETWORK_PROTOCOL_IPV6": "En-tête IP6",
    "NETWORK_PROTOCOL_IRTP": "Transaction fiable",
    "NETWORK_PROTOCOL_KRYPTOLAN": "Kryptolan",
    "NETWORK_PROTOCOL_LARP": "Résolution de l'adresse du locus",
    "NETWORK_PROTOCOL_LEAF1": "Leaf-1",
    "NETWORK_PROTOCOL_LEAF2": "Leaf-2",
    "NETWORK_PROTOCOL_MEAS": "Sous-systèmes de mesure DCN",
    "NETWORK_PROTOCOL_MHRP": "Routage de l'hôte mobile",
    "NETWORK_PROTOCOL_MICP": "Contrôle d'Int.ing mobile",
    "NETWORK_PROTOCOL_MOBILE": "Mobilité IP",
    "NETWORK_PROTOCOL_MTP": "Multicast Transport",
    "NETWORK_PROTOCOL_MUX": "Multiplexage",
    "NETWORK_PROTOCOL_ND": "Sun net disk proto (temp.)",
    "NETWORK_PROTOCOL_NHRP": "Résolution du prochain saut",
    "NETWORK_PROTOCOL_NO_NEXT_HDR": "IP6 sans en-tête suivant",
    "NETWORK_PROTOCOL_NSP": "Network Services",
    "NETWORK_PROTOCOL_NVPII": "voix réseau",
    "NETWORK_PROTOCOL_OLD_DIVERT": "Pseudo-proto de détournement OLD",
    "NETWORK_PROTOCOL_OSPFIGP": "OSPFIGP",
    "NETWORK_PROTOCOL_PFSYNC": "PFSYNC",
    "NETWORK_PROTOCOL_PGM": "PGM",
    "NETWORK_PROTOCOL_PIGP": "passerelle intérieure privée",
    "NETWORK_PROTOCOL_PIM": "Mcast indépendant du protocole",
    "NETWORK_PROTOCOL_PRM": "Mesure radio de paquets",
    "NETWORK_PROTOCOL_PUP": "pup",
    "NETWORK_PROTOCOL_PVP": "Protocole de paquet vidéo",
    "NETWORK_PROTOCOL_RAW": "paquet IP brut",
    "NETWORK_PROTOCOL_RCCMON": "Surveillance de RCC BBN",
    "NETWORK_PROTOCOL_RDP": "Données fiables",
    "NETWORK_PROTOCOL_ROUTING": "En-tête de routage IP6",
    "NETWORK_PROTOCOL_RSVP": "réservation de ressources",
    "NETWORK_PROTOCOL_RVD": "Disque virtuel distant",
    "NETWORK_PROTOCOL_SATEXPAK": "SATNET/Backroom EXPAK",
    "NETWORK_PROTOCOL_SATMON": "Surveillance de Satnet",
    "NETWORK_PROTOCOL_SCCSP": "Comm. Semaphore sécurité",
    "NETWORK_PROTOCOL_SCTP": "SCTP",
    "NETWORK_PROTOCOL_SDRP": "Routage de la demande source",
    "NETWORK_PROTOCOL_SEP": "Échange séquentiel",
    "NETWORK_PROTOCOL_SKIP": "SKIP",
    "NETWORK_PROTOCOL_SRPC": "Protocole Strite RPC",
    "NETWORK_PROTOCOL_ST": "Stream protocol II",
    "NETWORK_PROTOCOL_SVMTP": "Sécuriser VMTP",
    "NETWORK_PROTOCOL_SWIPE": "IP avec chiffrement",
    "NETWORK_PROTOCOL_TCF": "TCF",
    "NETWORK_PROTOCOL_TCP": "TCP",
    "NETWORK_PROTOCOL_TLSP": "Sécurité des couches de transport",
    "NETWORK_PROTOCOL_TP": "tp-4 w/ class negotiation",
    "NETWORK_PROTOCOL_TPC": "Connexion de tiers",
    "NETWORK_PROTOCOL_TPXX": "Transport TP++",
    "NETWORK_PROTOCOL_TRUNK1": "Trunk-1",
    "NETWORK_PROTOCOL_TRUNK2": "Trunk-2",
    "NETWORK_PROTOCOL_TTP": "TTP",
    "NETWORK_PROTOCOL_UDP": "UDP - protocole de datagramme utilisateur",
    "NETWORK_PROTOCOL_VINES": "Banyan VINES",
    "NETWORK_PROTOCOL_VISA": "Protocole VISA",
    "NETWORK_PROTOCOL_VMTP": "VMTP",
    "NETWORK_PROTOCOL_WBEXPAK": "WIDEBAND EXPAK",
    "NETWORK_PROTOCOL_WBMON": "Surveillance de la BANDE LARGE",
    "NETWORK_PROTOCOL_WSN": "Réseau Wang Span",
    "NETWORK_PROTOCOL_XNET": "Débogueur Cross Net",
    "NETWORK_PROTOCOL_XTP": "XTP",
    "NETWORK_SERVICE_GROUP": "Groupe de services réseau",
    "NETWORK_SERVICE_GROUPS": "Groupes de services réseau",
    "NETWORK_SERVICE": "Service réseau",
    "NETWORK_SERVICES_GROUP": "Groupe de services réseau",
    "NETWORK_SERVICES": "Network Services",
    "NETWORK_TRAFFIC": "Trafic réseau",
    "NEW_API_KEY": "Nouvelle clé API",
    "NEW_CALEDONIA_PACIFIC_NOUMEA": "Pacifique/Nouméa",
    "NEW_CALEDONIA": "Nouvelle-Calédonie",
    "NEW_PASSWORD_EQUALITY": "Le nouveau mot de passe et le mot de passe actuel ne doivent pas être identiques",
    "NEW_PASSWORD_PLACEHOLDER": "Doit comporter au moins 8 caractères et contenir 1 chiffre, 1 lettre majuscule et 1 caractère spécial",
    "NEW_PASSWORD": "Nouveau mot de passe",
    "NEW_ZEALAND_PACIFIC_AUCKLAND": "Pacifique/Auckland",
    "NEW_ZEALAND_PACIFIC_CHATHAM": "Pacifique/Chatham",
    "NEW_ZEALAND": "Nouvelle-Zélande",
    "NEW": "Nouveau",
    "NEWLY_REG_DOMAINS": "Domaines nouvellement enregistrés",
    "NEWS_AND_MEDIA": "Actualités et médias",
    "NEXT_PERIODIC_UPDATE": "La prochaine mise à jour périodique s'effectuera selon le calendrier défini pour les VM dans",
    "NEXT_UPDATE": "La prochaine mise à niveau périodique du logiciel aura lieu le ",
    "NEXT": "Suivant",
    "NFL_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte nfl.com.",
    "NFL": "NFL",
    "NFLMOBILE": "NFL Mobile",
    "NFS_DESC": " Le protocole NFS fournit un accès distant transparent aux systèmes de fichiers partagés sur les réseaux, conformément à la RFC 1813.",
    "NFS": "NFS",
    "NGAP_SCTP_DESC": "Le protocole NGAP (NG Application Protocol) assure la signalisation du plan de contrôle entre le nœud NG-RAN et la fonction de gestion de l'accès et de la mobilité (AMF). Il assure également la signalisation NAS pour l'équipement de l'utilisateur (UE) et l'AMF.",
    "NGAP_SCTP": "NGAP-SCTP",
    "NGAP_UDP_DESC": "Le protocole NGAP (NG Application Protocol) est encapsulé sous forme d'UDP afin de prendre en charge le transport sur les réseaux non compatibles SCTP.",
    "NGAP_UDP": "NGAP-UDP",
    "NICARAGUA_AMERICA_MANAGUA": "Amérique/Managua",
    "NICARAGUA": "Nicaragua",
    "NIGER_AFRICA_NIAMEY": "Afrique/Niamey",
    "NIGER": "Niger",
    "NIGERIA_AFRICA_LAGOS": "Afrique/Lagos",
    "NIGERIA": "Nigéria",
    "NIUE_PACIFIC_NIUE": "Pacifique/Niue",
    "NIUE": "Niue",
    "NLOCKMGR_DESC": " Le protocole Network Lock Manager est associé au NFS (Network File System) pour fournir un style System V de verrouillage de fichier et d'enregistrement consultatif sur le réseau.",
    "NLOCKMGR": "nlockmgr",
    "NLSP_DESC": " Le protocole NLSP (NetWare Link Services Protocol) fournit un routage de l'état des liaisons pour les réseaux Internetwork Packet Exchange.",
    "NLSP": "NLSP",
    "NMAP_DESC": "Nmap (Network Mapper) est un analyseur de sécurité qui permet de détecter les hôtes et les services sur un réseau informatique, créant ainsi une carte du réseau.",
    "NMAP": "Nmap",
    "NNTP_DESC": " Le protocole NNTP (Network News Transport Protocol) permet de diffuser, d'interroger, de récupérer et de publier des actualités Internet par le biais d'une méthode fiable basée sur le flux.",
    "NNTP": "NNTP",
    "NNTPS_DESC": " Version sécurisée du protocole NNTP.",
    "NNTPS": "SecureNNTP",
    "NO_ACTIVATION_PENDING": "Aucune activation en attente",
    "NO_BC_GROUPS_AVAILABLE_FOR_SELECTED_LOCATION": "Les groupes de connecteurs de succursales ne sont pas disponibles pour l'emplacement sélectionné !",
    "NO_DATA_AVAILABLE_AWS_ACCOUNT_GROUP": "Aucune donnée disponible\n\nPour créer un groupe de comptes AWS\n\naccédez à\n\nAdmin > Intégration des partenaires\n\n\n",
    "NO_DATA": "Impossible de trouver un élément correspondant",
    "NO_DESCRIPTION": "Aucune description",
    "NO_GROUPING": "Trafic global",
    "NO_ITEMS_AVAILABLE": "Impossible de trouver des données",
    "NO_MATCHING_ITEMS_FOUND": "Impossible de trouver un élément correspondant",
    "NO_MORE_DHCP_OPTIONS_AVAILABLE": "Plus aucune option DHCP disponible",
    "NO_OF_CLOUD_CONNECTOR_GROUPS": "Nombre de groupes de connecteurs cloud",
    "NO_OF_CLOUD_CONNECTORS": "Nombre de connecteurs cloud",
    "NO_OF_DUPLICATES_IP": "Nombre d'adresses IP en double",
    "NO_OF_EDGE_CONNECTOR_GROUPS": "Nombre de groupes de connecteurs cloud",
    "NO_OF_PRIVATE_IP_ADDRESSES": "Nombre d'adresses IP privées",
    "NO_PENDING_UPGRADES": "Aucune mise à niveau en attente",
    "NO_PRESIGNED_URL_WAS_GENERATED": "Aucune URL pré-signée n’a été générée.",
    "NO_REGION_WAS_PREVIOUS_SELECTED_TEXT": "Aucune région n'a été sélectionnée sur la grille des événements pour être utilisée sur le compte de stockage.",
    "NO_REGION_WAS_PREVIOUS_SELECTED": "Aucune région n'a été sélectionnée",
    "NO_STATIC_LEASE_CONFIGURED": "Aucun bail statique configuré",
    "NO_SUBSCRIPTION_AVAILABLE": "Aucun abonnement n'est disponible.",
    "NO_VALUE_SELECTED": "Aucune valeur sélectionnée",
    "NO_WIDGET_DATA": "Aucune donnée pour la période sélectionnée",
    "NO": "Non",
    "NON_CATEGORIZABLE": "Non catégorisable",
    "NON_NUMERIC_VALUE": "Ce champ ne doit contenir que des chiffres.",
    "NONE": "Aucun",
    "NORFOLK_ISLAND_PACIFIC_NORFOLK": "Pacifique/Norfolk",
    "NORFOLK_ISLAND": "Ile Norfolk",
    "NORTH_KOREA": "République démocratique populaire de Corée",
    "NORTH_MACEDONIA": "Macédoine du Nord",
    "NORTHAMERICA_NORTHEAST1_A": "northamerica-northeast1a",
    "NORTHAMERICA_NORTHEAST1_B": "northamerica-northeast1b",
    "NORTHAMERICA_NORTHEAST1_C": "northamerica-northeast1c",
    "NORTHAMERICA_NORTHEAST1": "northamerica-northeast1",
    "NORTHAMERICA_NORTHEAST2_A": "northamerica-northeast2a",
    "NORTHAMERICA_NORTHEAST2_B": "northamerica-northeast2b",
    "NORTHAMERICA_NORTHEAST2_C": "northamerica-northeast2c",
    "NORTHAMERICA_NORTHEAST2": "northamerica-northeast2",
    "NORTHCENTRALUS": "(États-Unis) Centre-Nord des États-Unis",
    "NORTHCENTRALUSSTAGE": "(États-Unis) Centre Nord des États-Unis (étape)",
    "NORTHERN_EUROPE": "Europe du nord",
    "NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN": "Pacifique/Saipan",
    "NORTHERN_MARIANA_ISLANDS": "Iles Mariannes du Nord",
    "NORTHEUROPE": "(Europe) Europe du Nord",
    "NORWAY_EUROPE_OSLO": "Europe/Oslo",
    "NORWAY": "Norvège",
    "NORWAYEAST": "(Europe) Est de la Norvège",
    "NORWAYWEST": "(Europe) Ouest de la Norvège",
    "NOT_AVAILABLE": "Non disponible",
    "NOT_DEPLOYED": "Prêt pour le déploiement",
    "NOT_NULL": "Non nul",
    "NOT_SPECIFIED": "Non spécifié",
    "NSS_CLOUD_FEED_API_URL": "URL DE L'API",
    "NSS_CLOUD_FEED_AUTHENTICATION_URL": "URL d'autorisation",
    "NSS_CLOUD_FEED_AWS_ACCESS_ID": "Identifiant d’accès AWS",
    "NSS_CLOUD_FEED_AWS_SECRET_KEY": "Clé secrète AWS",
    "NSS_CLOUD_FEED_CLIENT_ID": "Identifiant du client",
    "NSS_CLOUD_FEED_CLIENT_SECRET": "Secret du client",
    "NSS_CLOUD_FEED_GENERAL": "Général",
    "NSS_CLOUD_FEED_GRANT_TYPE": "Type d'octroi",
    "NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Notation des tableaux JSON",
    "NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Taille maximale du lot",
    "NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Authentification OAuth 2.0",
    "NSS_CLOUD_FEED_SCOPE": "Périmètre",
    "NSS_CLOUD_FEED_SIEM_TYPE": "Type de SIEM",
    "NSS_CLOUD_FEEDS_API_URL": "URL DE L'API",
    "NSS_CLOUD_FEEDS_FEED_NAME": "Nom du flux",
    "NSS_CLOUD_FEEDS_FEED_OVERVIEW": "Aperçu du flux",
    "NSS_CLOUD_FEEDS_FEED_TYPE": "TYPE DE FLUX",
    "NSS_CLOUD_FEEDS_LOG_TYPE": "TYPE DE JOURNAL",
    "NSS_CLOUD_FEEDS_S3_FOLDER_URL": "URL du dossier S3",
    "NSS_CLOUD_FEEDS_SIEM_TYPE": "Type de SIEM",
    "NSS_CLOUD_FEEDS_STATUS": "Statut",
    "NSS_CLOUD_FEEDS": "Flux RSS cloud",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Groupes Cloud/Branch Connector",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Adresses IP du client",
    "NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Types de demande DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Codes de réponse DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Types de réponse DNS",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Réponses DNS",
    "NSS_FEED_DNS_FILTERS_DOMAINS": "Domaines",
    "NSS_FEED_DNS_FILTERS_DURATIONS": "Durées",
    "NSS_FEED_DNS_FILTERS_LOCATIONS": "Emplacements",
    "NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Mesure de la stratégie",
    "NSS_FEED_DNS_FILTERS_RULE_NAME": "Nom de la règle",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_ADDRESS": "Adresse IP des serveurs",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_PORTS": "Ports du serveur",
    "NSS_FEED_DUPLICATE_LOGS": "Journaux en double",
    "NSS_FEED_EC_METRICS_RECORD_TYPE": "Type d'enregistrement d'indicateurs",
    "NSS_FEED_ESCAPE_CHARACTER": "Caractère d’échappement de flux",
    "NSS_FEED_FILTERS": "Filtres",
    "NSS_FEED_FORMATTING": "FORMATAGE",
    "NSS_FEED_GENERAL": "Général",
    "NSS_FEED_LOG_TYPE": "TYPE DE JOURNAL",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "VM Cloud/Branch Connector",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "VM Cloud/Branch Connector",
    "NSS_FEED_NAME": "Nom du flux",
    "NSS_FEED_OUTPUT_FORMAT": "Format de sortie du flux",
    "NSS_FEED_OUTPUT_TYPE": "TYPE DE SORTIE DE FLUX",
    "NSS_FEED_SERVER": "Serveur NSS",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Groupes Cloud/Branch Connector",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Adresses IP du client",
    "NSS_FEED_SESSION_FILTERS_FIREWALL_LOG_TYPE": "Type de journal du pare-feu",
    "NSS_FEED_SESSION_FILTERS_GATEWAY": "Passerelle",
    "NSS_FEED_SESSION_FILTERS_LOCATIONS": "Emplacements",
    "NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Network Services",
    "NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Mesure de la stratégie",
    "NSS_FEED_SESSION_FILTERS_RULE_NAME": "Nom de la règle",
    "NSS_FEED_SESSION_LOG_TYPE": "Type de journal de session",
    "NSS_FEED_SIEM_CONNECTIVITY": "Connectivité SIEM",
    "NSS_FEED_SIEM_DESTINATION_TYPE": "Type de destination SIEM",
    "NSS_FEED_SIEM_FQDN": "Nom de domaine complet SIEM",
    "NSS_FEED_SIEM_IP_ADDRESS": "Adresse IP SIEM",
    "NSS_FEED_SIEM_RATE_LIMIT": "Limite de débit SIEM (événements par seconde)",
    "NSS_FEED_SIEM_RATE": "Taux SIEM",
    "NSS_FEED_SIEM_TCP_PORT": "Port TCP SIEM",
    "NSS_FEED_STATUS": "Statut",
    "NSS_FEED_TIMEZONE": "Fuseau horaire",
    "NSS_FEED": "FLUX DE NSS",
    "NSS_FEEDS_AGGREGATE_LOGS": "Agréger les journaux",
    "NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS": "Journaux des sessions et des agrégats",
    "NSS_FEEDS_DUPLICATE_LOG": "Journaux en double",
    "NSS_FEEDS_FEED_ATTRIBUTES": "Attributs de flux",
    "NSS_FEEDS_FEED_NAME": "Nom du flux",
    "NSS_FEEDS_FEED_OUTPUT_FORMAT": "Format de sortie du flux",
    "NSS_FEEDS_FEED_OVERVIEW": "Aperçu du flux",
    "NSS_FEEDS_FEED_TYPE": "TYPE DE FLUX",
    "NSS_FEEDS_FULL_SESSION_LOGS": "Journaux de session complets",
    "NSS_FEEDS_LOG_FILTER": "Filtre de journal",
    "NSS_FEEDS_LOG_TYPE": "TYPE DE JOURNAL",
    "NSS_FEEDS_NSS_SERVER_TEXT": "Serveur NSS",
    "NSS_FEEDS_OUTPUT_DESTINATION": "DESTINATION DE SORTIE",
    "NSS_FEEDS_SIEM_RATE": "Taux SIEM",
    "NSS_FEEDS_STATUS": "Statut",
    "NSS_FEEDS_TIMEZONE": "Fuseau horaire",
    "NSS_FEEDS_USER_OBFUSCATION": "Obfuscation de l'utilisateur",
    "NSS_FEEDS": "Flux NSS",
    "NSS_FOR_FIREWALL_AND_EC": "NSS pour pare-feu, Branch et Cloud Connector",
    "NSS_FOR_FIREWALL": "NSS pour pare-feu",
    "NSS_GCP_DEPLOYMENT_GUIDE": "Guide de déploiement de la Plateforme Google Cloud pour NSS (Nano Second Service)",
    "NSS_LOGGING": "Consignation NSS",
    "NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Nombre d'utilisateurs",
    "NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Pic de demandes DNS par heure",
    "NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR": "Sessions en période de pointe par heure",
    "NSS_SERVER_DEPLOYMENT_PLATFORM": "Plate-forme",
    "NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE": "Télécharger le NSS Virtual Appliance",
    "NSS_SERVER": "Serveur NSS",
    "NSS_SERVERS": "Serveurs NSS",
    "NSS_TYPE": "Type de NSS",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC": "Déploiement de l’appliance virtuelle NSS",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT": "DÉPLOIEMENT D'APPLIANCE VIRTUELLE NSS",
    "NSS_VIRTUAL_MACHINE": "Machine virtuelle NSS",
    "NTP_DESC": "Le protocole Network Time Protocol désigne un protocole réseau de synchronisation de l'horloge entre les systèmes informatiques sur des réseaux de données à commutation par paquets et à latence variable.",
    "NTP": "NTP",
    "NTV_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte ntv.co.jp.",
    "NTV": "NTV",
    "NUDITY_DESC": " Sites qui proposent des photos artistiques ou non artistiques de nus, sous n'importe quel format (sculpture, photographie, peinture, etc.).",
    "NUDITY": "Nudité",
    "NUMBER_ABBR": "Nº",
    "NUMBER_OF_ACCOUNTS": "Nombre de comptes",
    "NUMBER_OF_CONNECTORS": "Nombre de connecteurs",
    "NUMBER_OF_CORES": "Nombre de cœurs",
    "NUMBER_OF_RECORDS_DISPLAYED": "Nombre d’enregistrements affichés",
    "NUMBER_OF_RECORDS_FETCHED_SO_FAR": "Nombre d'enregistrements récupérés jusqu'ici",
    "NUMBER_OF_SELECTED_ITEM_PLURAL": "{{count}} éléments sélectionnés",
    "NUMBER_OF_SELECTED_ITEM": "{{count}} élément sélectionné",
    "NUMBER_OF_SELECTED_ITEMS": "Nombre d'éléments sélectionnés",
    "OBFUSCATED": "Obscurci",
    "OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA": "Asie/Gaza",
    "OCCUPIED_PALESTINIAN_TERRITORY": "Etat de Palestine",
    "OFF": "Arrêt",
    "OKAY": "D'accord",
    "OMAN_ASIA_MUSCAT": "Asie/Mascate",
    "OMAN": "Oman",
    "ON_PREMISE": "Sur site",
    "ON": "Activé(e)",
    "ONE_OR_MORE_CC_FAILED": "Une ou plusieurs mises à niveau du connecteur cloud ont échoué",
    "ONLINE_AUCTIONS": "Enchères en ligne",
    "ONLINE_CHAT": "Chat en ligne",
    "ONLY_ONE_PHISICAL_INTERFACE_PER_PORT": "Une seule interface balisée par port.",
    "OPEN_A_NEW_TAB": "Ouvrir dans un nouvel onglet",
    "OPENVPN_DESC": "OpenVPN est une application logicielle open source qui implémente des techniques VPN (Virtual Private Network) pour la création de connexions de point à point ou de site à site sécurisées dans des configurations routées ou en pont et des outils d'accès à distance.",
    "OPENVPN": "OpenVPN",
    "OPERATIONAL_STATUS": "Statut opérationnel",
    "OPTION_NAME": "Nom de l'option",
    "OPTIONAL_PARENTHESIS": "(facultatif)",
    "OPTIONAL": "Facultatif :",
    "OPTIONS_COLON": "Options :",
    "OPTIONS": "Options",
    "ORACLE_LINUX": "ORACLE LINUX",
    "ORDER_DEFAULT": "Par défaut",
    "ORG_ADMIN": "Administrateur de l’organisation",
    "ORG_ID": "Identifiant d’organisation",
    "ORGANIZATION": "Organisation",
    "OS_TYPE": "Type de système d’exploitation",
    "OS_VERSION": "Version du système d’exploitation",
    "OSS_UPDATES": "Mises à jour du système d'exploitation et des logiciels",
    "OTHER_ADULT_MATERIAL": "Contenu réservé aux adultes - autres",
    "OTHER_BUSINESS_AND_ECONOMY": "Entreprises et économie - autres",
    "OTHER_CLOUDS": "Autres clouds",
    "OTHER_DRUGS": "Autres drogues",
    "OTHER_EDUCATION": "Education - autres",
    "OTHER_ENTERTAINMENT_AND_RECREATION": "Divertissements/loisirs - autres",
    "OTHER_GAMES": "Jeux en ligne et autres jeux",
    "OTHER_GOVERNMENT_AND_POLITICS": "",
    "OTHER_ILLEGAL_OR_QUESTIONABLE": "Contenu illicite ou contestable - autres",
    "OTHER_INFORMATION_TECHNOLOGY": "",
    "OTHER_INTERNET_COMMUNICATION": "Communication Internet - autres",
    "OTHER_MISCELLANEOUS": "Divers - autres",
    "OTHER_OS": "Autres systèmes d’exploitation",
    "OTHER_RELIGION": "Religion - autres",
    "OTHER_SECURITY": "Sécurité - autres",
    "OTHER_SHOPPING_AND_AUCTIONS": "Shopping et enchères - autres",
    "OTHER_SOCIAL_AND_FAMILY_ISSUES": "Problèmes sociaux et familiaux - autres",
    "OTHER_SOCIETY_AND_LIFESTYLE": "Société et style de vie - autres",
    "OTHER_THREAT": "Menaces - autres",
    "OTHER": "Autre",
    "out of": "sur",
    "OUT_OF": "sur",
    "OUTBOUND": "Sortant(e)",
    "OUTBYTES": "Octets sortants",
    "OUTGOING_GATEWAY_IP_ADDRESS": "Adresse IP de la passerelle sortante",
    "OVER_ALL_TRAFFIC": "Trafic global",
    "OVERRIDE": "Remplacer",
    "P2P_COMMUNICATION": "Site pair à pair",
    "P2P": "Peer-to-Peer",
    "PACKET_LOSS": "Perte de paquets",
    "PAGE_OF": "Page {1} sur {2}",
    "PAGE_RISK_INDEX": "PageRisk",
    "PAGE": "Página",
    "PAKISTAN_ASIA_KARACHI": "Asie/Karachi",
    "PAKISTAN": "Pakistan",
    "PALAU_PACIFIC_PALAU": "Pacifique/Palaos",
    "PALAU": "Palaos",
    "PALESTINE": "Palestine",
    "PALESTINIAN_TERRITORY": "Etat de Palestine",
    "PANAMA_AMERICA_PANAMA": "Amérique/Panama",
    "PANAMA": "Panama",
    "PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY": "Pacifique/Port Moresby",
    "PAPUA_NEW_GUINEA": "Papouasie-Nouvelle-Guinée",
    "PARAGUAY_AMERICA_ASUNCION": "Amérique/Asuncion",
    "PARAGUAY": "Paraguay",
    "PARTNER_INTEGRATIONS": "Intégrations du partenaire",
    "PASSPHRASE": "Phrase d'authentification",
    "PASSWORD_CHANGE_REMINDER_NEW_PASSWORD_INPUT_PLACEHOLDER": "Doit comporter au moins 8 caractères dont 1 chiffre, 1 lettre majuscule et 1 caractère spécial...",
    "PASSWORD_DONT_MATCH": "Les mots de passe ne correspondent pas",
    "PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE": "Cette modification s'appliquera également aux administrateurs qui existent dans le portail d'administration pour un autre service. Continuer ?",
    "PASSWORD_EXPIRATION_DAYS_RANGE": "La date d’expiration du mot de passe doit être comprise entre 15 et 365 jours",
    "PASSWORD_EXPIRATION": "Expiration du mot de passe",
    "PASSWORD_EXPIRED_ALREADY": "Votre mot de passe a déjà expiré",
    "PASSWORD_EXPIRED": "Mot de passe expiré",
    "PASSWORD_EXPIRES_AFTER": "Le mot de passe expire après",
    "PASSWORD_EXPIRY": "Expiration du mot de passe",
    "PASSWORD_MANAGEMENT": "GESTION DES MOTS DE PASSE",
    "PASSWORD_MESSAGE_WARNING_MESSAGE": "Cette modification s'appliquera également aux administrateurs qui existent dans le portail d'administration pour un autre service. Continuer ?",
    "PASSWORD_STRENGTH_REQUIRED": "Le mot de passe doit contenir au moins huit caractères et au moins un chiffre, une majuscule et un caractère spécial.",
    "PASSWORD_STRENGTH": "Doit comporter au moins 8 caractères et contenir 1 chiffre, 1 lettre majuscule et 1 caractère spécial",
    "PASSWORD_UPDATE_SUCCESSFULLY": "Mot de passe mis à jour",
    "PASSWORD": "Mot de passe",
    "PATCH": "Correctif",
    "PATENTS": "Brevets",
    "PC_ANYWHERE_DESC": "pcAnywhere désigne un logiciel de contrôle distant et de transfert de fichiers.",
    "PC_ANYWHERE": "pcAnywhere",
    "PCANYWHERE_DESC": " PCAnywhere est une solution de contrôle à distance. Il peut gérer les systèmes Windows et Linux. Les performances vidéo améliorées et le chiffrement AES 256 bits intégré rendent les communications rapides et sécurisées. PCAnywhere offre également des capacités de transfert de fichiers puissantes",
    "PCANYWHERE": "pcanywhere",
    "PEER_DHCP": "Homologue DHCP (facultatif)",
    "PENDING": "En attente",
    "PERMISSION_REQUIRED_MESSAGE": "Cette fonctionnalité nécessite une autorisation dont vous ne disposez pas actuellement.",
    "PERMISSION_REQUIRED": "Autorisation requise",
    "PERMISSION": "Permission",
    "PERMISSIONS": "Autorisations",
    "PERSIST_LOCAL_VERSION_PROFILE": "Conserver le profil de version locale",
    "PERU_AMERICA_LIMA": "Amérique/Lima",
    "PERU": "Pérou",
    "PFCP_DESC": "Protocole 3GPP utilisé sur l'interface N4 entre le plan de contrôle et la fonction du plan utilisateur (UPF).",
    "PFCP_PORT": "Port PFCP",
    "PHILIPPINES_ASIA_MANILA": "Asie/Manille",
    "PHILIPPINES": "Philippines",
    "PHISHING": "Phishing",
    "PHYSICAL": "Physique",
    "PITCAIRN_ISLANDS": "Iles Pitcairn",
    "PITCAIRN_PACIFIC_PITCAIRN": "Pacifique/Pitcairn",
    "PITCAIRN": "Pitcairn",
    "PLACEHOLDER_NETWORK_SERVICE_GROUP_NAME": "Entrer le nom du groupe de services réseau ici",
    "PLACEHOLDER_NETWORK_SERVICE_NAME": "Entrer le nom du service réseau ici",
    "PLAIN_UDP": "UDP en clair",
    "PLATFORM": "Plate-forme",
    "PLEASE_ADD_BC_GROUP_INFO": "Remplissez les informations ci-dessous pour votre groupe Branch Connector.",
    "PLEASE_ADD_CLOUD_CONNECTOR_NAME": "Veuillez ajouter le filtre du connecteur cloud.",
    "PLEASE_ADD_DATACENTER_FILTER": "Veuillez ajouter le filtre du centre de données.",
    "PLEASE_ADD_EC_DEVICE_APP_VERSION": "Veuillez ajouter le filtre de version de l’application de l’appareil.",
    "PLEASE_ADD_EC_DEVICE_HOSTNAME": "Veuillez ajouter le filtre de nom d’hôte de l’appareil.",
    "PLEASE_ADD_EC_DEVICE_ID": "Veuillez ajouter le filtre de nom de l’appareil.",
    "PLEASE_CONFIGURE_THE_IP_ADDRESS": "Veuillez configurer l’adresse IP",
    "PLEASE_CONFIGURE_THE_MAC_ADDRESS": "Veuillez configurer l’adresse MAC",
    "PLEASE_CONFIGURE_THE_ROUTE_ADDRESS": "Veuillez configurer l’adresse de la route.",
    "PLEASE_ENTER_BELOW_VALUES": "Veuillez entrer les valeurs ci-dessous",
    "PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW": "Entrez les informations suivantes ci-dessous.",
    "PLEASE_ENTER_VALID_EMAIL_ADDRESS": "Entrer une adresse e-mail valide.",
    "PLEASE_FILL_BRANCH_INFO": "Remplissez les informations ci-dessous pour votre Branch Connector individuel.",
    "PLEASE_FILL_DEVICE_INFO": "Sélectionnez un appareil pour le provisionnement de la configuration de votre Branch Connector.",
    "PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM": "Veuillez contacter l'équipe chargée de votre compte pour activer le chiffrement du tunnel",
    "PLEASE_REMOVE_DELETED_LOCATION": "Veuillez supprimer le(s) emplacement(s) supprimé(s).",
    "PLEASE_SELECT_A_STORAGE_ACCOUNT": "Veuillez sélectionner un compte de stockage.",
    "PLEASE_VERIFY_THE_DHCP_CUSTOM_CONFIGURTION_FOR_DUPLICATES": "Veuillez vérifier la configuration des options personnalisées DHCP pour détecter les doublons.",
    "POLAND_EUROPE_WARSAW": "Europe/Varsovie",
    "POLAND": "Pologne",
    "POLICY_CONFIGURATION": "Configuration de la stratégie",
    "POLICY_INFORMATION": "Transfert d’informations",
    "POLICY_MANAGEMENT": "Gestion des stratégies",
    "POLICY_SYNC": "Synchronisation des politiques",
    "Policy": "Politique",
    "POLICY": "Politique",
    "POLITICS": "Politique",
    "POOR": "Mauvaise qualité",
    "POP3_DESC": "Le protocole Post Office Protocol permet de récupérer les e-mails.",
    "POP3": "POP3",
    "PORNOGRAPHY": "Pornographie",
    "PORT_DETAILS": "Détails du port",
    "PORT_NAME": "[Nom du port]",
    "PORT_NO": "N° de port  ",
    "PORT_STATUS": "État du port",
    "PORT": "Port",
    "PORTALS": "Portails",
    "PORTS": "Ports proxy",
    "PORTUGAL_ATLANTIC_AZORES": "Atlantique/Açores",
    "PORTUGAL_ATLANTIC_MADEIRA": "Atlantique/Madère",
    "PORTUGAL_EUROPE_LISBON": "Europe/Lisbonne",
    "PORTUGAL": "Portugal",
    "PPP_DESC": " Le protocole PPP (Point-to-Point Protocol) est un protocole de couche de liaison qui permet de transférer des données sur une liaison de point à point. Il fournit un adressage IP dynamique, une assistance pour les mots de passe, un système de vérification des erreurs et la transmission de plusieurs protocoles sur la même liaison.",
    "PPP": "PPP",
    "PPPOE_DESC": " PPP over Ethernet (PPPoE) permet de connecter un réseau d'hôtes sur un appareil avec accès en mode pont simple à un concentrateur d'accès distant.",
    "PPPOE": "PPPoE",
    "PPS_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte pps.tv.",
    "PPS": "PPS (pps.tv)",
    "PPSTREAM_DESC": " Le protocole PPStream fournit un streaming audio et vidéo. Il repose sur une technologie BitTorrent (peer-to-peer). Il est surtout utilisé en Chine.",
    "PPSTREAM": "PPStream",
    "PPTP_DATA_DESC": " Le protocole PPPTP (Point-to-Point Tunneling Protocol) permet au protocole PPP (Point to Point Protocol) d'être redirigé par tunneling sur un réseau IP.",
    "PPTP_DATA": "PPTP data channel. PPTP",
    "PPTP_DESC": " Le protocole PPPTP (Point-to-Point Tunneling Protocol) permet au protocole PPP (Point to Point Protocol) d'être redirigé par tunneling sur un réseau IP.",
    "PPTP_SERVICES": "Services PPTP",
    "PPTP": "PPTP",
    "PPTV_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte pptv.com.",
    "PPTV": "pptv",
    "PRE_SIGNED_URL": "URL pré-signée",
    "PREDEFINED_RULE_CONFIRMATION": "Confirmation des règles prédéfinies",
    "PREDEFINED": "Prédéfini",
    "PREFERRED_COLLON": "Préféré :",
    "PREFERRED_TEXT": "Sélectionnez Oui pour permettre à l’appareil préféré de devenir prioritaire et de prendre le relais lorsqu’il est actif.",
    "PREFERRED": "Préféré",
    "PREFIX": "Préfixe",
    "PREV_DAY": "Veille",
    "PREV_MONTH": "Mois précédent",
    "PREV_WEEK": "Semaine précédente",
    "PREVIOUS": "Précédent",
    "PRIMARY_DNS_IS_MADATORY_BEFORE_SECONDARY_DNS": "Le DNS principal est obligatoire avant un DNS secondaire.",
    "PRIMARY_DNS_SERVER_IP_ADDRESS": "Adresse IP du serveur DNS principal",
    "PRIMARY_DNS_SERVER": "Serveur DNS principal",
    "PRIMARY_DNS": "DNS principal",
    "PRIMARY_PROXY": "Proxy principal",
    "PRIMARY_SERVER_RESPONSE_PASS": "Tentative sur serveur principal",
    "PRIMARY": "Principal",
    "PRINT_VIEW": "Aperçu de l'impression",
    "PRINT": "Imprimer",
    "PRIVATE_APLICATIONS": "Applications privées",
    "PRIVATE_IP_ADDRESS": "Adresse IP privée",
    "PRIVATE_IP_ADDRESSES": "Adresses IP privées",
    "PROCEED": "Continuer",
    "PROCESSED_BYTES": "Octets traités",
    "PROFANITY": "Blasphème",
    "PROFESSIONAL_SERVICES": "Services professionnels",
    "PROTOCOL_TYPE": "Type de protocole",
    "PROTOCOL": "Protocole",
    "PROVISION_KEY_NAME": "Nom de la clé de provisionnement",
    "PROVISION_KEY": "Clé de provisionnement",
    "PROVISIONED": "Provisionné",
    "Provisioning URL": "URL de Provisionnement",
    "PROVISIONING_AND_CONFIGUATION": "Provisionnement & configuration",
    "PROVISIONING_CONTROL": "Contrôle de provisionnement",
    "PROVISIONING_KEY": "Clé de provisionnement",
    "PROVISIONING_MANAGEMENT": "Gestion des provisionnements",
    "PROVISIONING_TEMPLATE_IS_BROKEN": "Ce modèle de provisionnement n'est pas valide. Veuillez le supprimer et en créer un nouveau.",
    "PROVISIONING_TEMPLATE": "Modèle d'Provisionnement",
    "PROVISIONING_TEMPLATES": "Modèles de Provisionnement",
    "PROVISIONING_URL": "URL de Provisionnement",
    "PROXY_TEST": "Test du proxy",
    "PUBLIC_CLOUD_COFIGURATION": "Configuration du cloud public",
    "PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Gestion de la configuration du cloud public",
    "PUBLIC_IP_FOR_DIRECT_FORWARDING": "Adresse IP publique pour le transfert direct",
    "PUBLIC_IP": "IP public",
    "PUBLIC_IPS": "Adresses IP publiques",
    "PUERTO_RICO_AMERICA_PUERTO_RICO": "Amérique/Porto Rico",
    "PUERTO_RICO": "Porto Rico",
    "PZEN": "Private Service Edge",
    "QATAR_ASIA_QATAR": "Asie/Qatar",
    "QATAR": "Qatar",
    "QUESTIONABLE": "Discutable",
    "QUEUED_ACTIVATIONS": "ACTIVATIONS EN FILE D’ATTENTE",
    "QUIC_DESC": "QUIC (connexions Internet rapide via le protocole UDP) est un nouveau protocole de transport pour Internet, mis au point par Google",
    "QUIC": "QUIC",
    "QUICK_LINKS": "Liens rapides",
    "QUICKOFFICE": "Quickoffice",
    "QUICKSEC": "QUICKSEC",
    "QUICKTIME_VIDEO": "Vidéo QuickTime (mov, qt)",
    "QUICKTIME": "QuickTime",
    "RADIO_STATIONS": "Radio",
    "RADIUS_DESC": " RADIUS (Remote Authentication Dial-In User Service) est un protocole client/serveur qui permet aux serveurs d'accès distant de communiquer avec un serveur central pour authentifier les utilisateurs entrants et autoriser leur accès au système ou au service requis.",
    "RADIUS": "RADIUS",
    "RADIUSIM_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte radiusim.com.",
    "RADIUSIM": "RadiusIM",
    "RANGE_ERROR": "Plage non valide. Le nombre doit être compris entre {{min}} et {{max}}",
    "RANGE_FROM_BIGGER_THAN_TO": "L’adresse IP de début de la plage doit être égale ou inférieure à l’adresse IP de fin.",
    "RBA_LIMITED": "L’accès est restreint",
    "REAL_ESTATE": "Construction et immobilier",
    "REAL_MEDIA_DESC": "RealMedia désigne une technologie de streaming vidéo et audio.",
    "REAL_MEDIA": "RealMedia",
    "REASON": "Raison",
    "RECEIVE_COUNT": "Nombre de réceptions",
    "RECEIVED_BYTES": "Octets reçus",
    "RECEIVED_MESSAGES": "Messages reçus",
    "RECOMMENDED_HYPERVISOR_SPECS": "SPÉCIFICATIONS RECOMMANDÉES DE L’HYPERVISEUR",
    "RECOMMENDED_VM_SPECS": "SPÉCIFICATIONS RECOMMANDÉES DE L’HYPERVISEUR",
    "REDHAT_LINUX": "Red Hat Linux",
    "REDIR_ZPA": "Rediriger vers l'URL",
    "REDIRECT_REQUEST": "Rediriger la demande",
    "REFERENCE_SITES": "Sites de référence",
    "REFRESH": "Rafraîchir",
    "REGENARATE": "Régénérer",
    "REGENERATE_API_KEY_CONFIRMATION_MESSAGE": "La modification de la clé API invalide immédiatement la clé actuelle. La nouvelle clé conserve la portée, les autorisations et le nom de la clé actuelle. Cela ne peut pas être annulé.",
    "REGENERATE_API_KEY_CONFIRMATION_TITLE": "Régénérer la clé API",
    "REGENERATE_API_KEY_TOOLTIP": "Régénérer la clé API",
    "REGENERATE_API_KEY": "Régénérer la clé API",
    "REGION_TEXT": "Veuillez sélectionner les régions dans lesquelles vous souhaitez que Zscaler découvre des balises dans votre compte AWS. Le menu déroulant affiche la liste des régions prises en charge par le service de découverte de balises de Zscaler. Pour plus d'informations sur les régions prises en charge, cliquez {1}ici{2}.",
    "REGION": "Région",
    "REGIONS_AND_SUBSCRIPTIONS": "Régions et abonnements",
    "REGIONS_SUBSCRIPTION_TEXT": "Sélectionnez les régions et les abonnements. Zscaler va lire les balises définies par l'utilisateur pour les charges de travail situées dans ces régions et ces abonnements",
    "REGIONS_SUBSCRIPTION": "Régions et abonnements",
    "REGIONS_WORKLOAD_INVENTORY": "Inventaire des charges de travail par région",
    "REGIONS": "Régions",
    "REGISTERED": "Enregistré(e)",
    "RELEASES_NOTES": "Notes de version",
    "REMOTE_ACCESS": "Outils d'accès distant",
    "REMOTE_ASSISTANCE_MANAGEMENT": "Gestion de l’assistance à distance",
    "REMOTE_ASSISTANCE": "Assistance à distance",
    "REMOVE_ALL": "Tout supprimer",
    "RENEW": "Renouveler",
    "REPORT": "Rapport",
    "REQ_ACTION": "Action de demande",
    "REQ_DURATION": "Durée de la demande",
    "REQ_RULE_NAME": "Nom de la règle de la demande",
    "REQUESTED_DOMAIN": "Domaine demandé",
    "REQUIRED": "Ce champ ne peut pas être vide.",
    "RES_ACTION": "Action de réponse",
    "RES_RULE_NAME": "Nom de la règle de réponse",
    "RESEARCH_BLOG": "ThreatLabz | Recherche sur la sécurité",
    "RESET_COUNT": "Réinitialiser le décompte",
    "RESET": "Réinitialiser",
    "RESOLVE_BY_ZPA": "Résoudre par ZPA",
    "RESOLVED_BY_ZPA": "Résolu(e)",
    "RESOLVED_IP_OR_NAME": "Adresse IP ou nom résolu",
    "RESOLVED_IP": "IP résolue",
    "RESOLVER_IP_OR_NAME": "Adresse IP ou nom du résolveur",
    "RESOLVER": "Résolveur",
    "RESOURCE_GROUP": "Groupe de ressources",
    "RESOURCE_GROUPS": "Groupes de ressources",
    "RESOURCE_NAME": "Ressource",
    "RESOURCE_NOT_FOUND": "Aucune ressource trouvée",
    "RESOURCE_TYPE": "Type de ressource",
    "RESOURCE": "Ressource",
    "RESPONSE_ACTION": "Action de réponse",
    "REST": "Reste(s)",
    "RETRIEVING_FOR": "Récupération pour",
    "RETURN_ERROR": "Erreur de retour",
    "REUNION_INDIAN_REUNION": "Océan Indien/Réunion",
    "REUNION": "Réunion",
    "REVIEW_ENSURE_INFORMATION": "Veillez à ce que toutes les informations ci-dessous soient correctes avant de créer ce modèle de provisionnement de branch connector.",
    "REVIEW_TENANT": "Assurez-vous que toutes les informations ci-dessous sont correctes avant d'ajouter ce compte.",
    "REVIEW_TEXT": "Assurez-vous que toutes les informations ci-dessous sont correctes avant d'ajouter ce compte.",
    "REVIEW_YOUR_CHANGES": "Vérifier vos modifications",
    "REVIEW": "Examiner",
    "RMA": "RMA demandée",
    "ROLE_MANAGEMENT": "Gestion des rôles",
    "ROLE_NAME": "Nom du rôle",
    "ROLE": "Rôle",
    "ROMANIA_EUROPE_BUCHAREST": "Europe/Bucarest",
    "ROMANIA": "Roumanie",
    "ROUTE_HAS_DUPLICATE_VALUES": "Le routage contient des doublons.",
    "ROUTE": "Routage",
    "ROUTING": "Routage",
    "ROWS_PER_PAGE": "Lignes par page",
    "RSH_DESC": " Le protocole RSH permet à un utilisateur d'établir une connexion sécurisée vers un hôte distant et d'obtenir un shell qui envoie des commandes vers la machine distante à exécuter.",
    "RSH": "rsh",
    "RSLTS_READFAILED": "Echec de la lecture du volume de résultats.",
    "RSS_DESC": " RSS désigne un ensemble de formats de flux Web permettant de publier des contenus fréquemment mis à jour dans un format standardisé.",
    "RSS": "rss",
    "RSTAT_DESC": " Le protocole RStat est utilisé dans la gamme Sun NFS pour échanger des statistiques sur l'activité réseau.",
    "RSTAT": "RStat",
    "RSVP_DESC": " Le protocole RSVP (Resource reSerVation setup Protocol) permet de bénéficier de services intégrés sur Internet. Le protocole RSVP permet la configuration initiée par le destinataire de réservations de ressources pour des flux de données multidiffusions ou monodiffusions, avec d'excellentes propriétés de mise à l'échelle et de robustesse. Le protocole RSVP permet à un hôte de demander des qualités de service spécifiques concernant des flux de données d'application particuliers. Le protocole RSVP est également utilisé par les routeurs pour fournir des demandes QoS (qualité de service) à tous les nœuds du ou des chemins de flux, et pour établir et maintenir l'état permettant de fournir de service demandé.",
    "RSVP": "RSVP",
    "RSYNC_DESC": "rsync est un programme de synchronisation et de transfert de fichiers pour les systèmes de type Unix, qui minimise le transfert de données réseau grâce à un encodage delta appelé algorithme rsync.",
    "RSYNC": "Rsync",
    "RTCP_DESC": " Le protocole RTCP (Real-Time Transport Control) permet de surveiller la livraison de données de manière évolutive vers de grands réseaux de multidiffusion, et de fournir un contrôle et une identification de niveau minimal.",
    "RTCP": "RTCP",
    "RTL_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte rtl.de.",
    "RTL": "RTL",
    "RTMP_DESC": "Real Time Messaging Protocol (RTMP) est un protocole propriétaire développé par Adobe Systems pour l’audio, la vidéo et les données en streaming sur Internet entre un Flash player et un serveur",
    "RTMP": "RTMP",
    "RTP_DESC": " RTP désigne le protocole de transport en temps réel permettant de transmettre des données en temps réel, comme des données audio, vidéo ou de simulation, sur des services réseau en monodiffusion ou multidiffusion.",
    "RTP": "RTP",
    "RTSP_DESC": " Le protocole RTSP (Real-Time Streaming Protocol) désigne un protocole applicatif visant à contrôler la livraison des données en temps réel. Il fournit un cadre extensible permettant une livraison contrôlée et à la demande de données en temps réel, comme les données audio et vidéo.",
    "RTSP_SERVICES": "Services RTSP",
    "RTSP": "RTSP",
    "RULE_CRITERIA": "Critères",
    "RULE_NAME": "Nom de la règle",
    "RULE_ORDER": "Ordre de règle",
    "RULE_STATUS": "Statut de règle",
    "RULES": "Règles",
    "RUN_TEST": "Exécuter un test",
    "RUSSIA": "Russie",
    "RUSSIAN_FEDERATION_ASIA_ANADYR": "Asie/Anadyr",
    "RUSSIAN_FEDERATION_ASIA_IRKUTSK": "Asie/Irkoutsk",
    "RUSSIAN_FEDERATION_ASIA_KAMCHATKA": "Asie/Kamtchatka",
    "RUSSIAN_FEDERATION_ASIA_KRASNOYARSK": "Asie/Krasnoïarsk",
    "RUSSIAN_FEDERATION_ASIA_MAGADAN": "Asie/Magadan",
    "RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK": "Asie/Novossibirsk",
    "RUSSIAN_FEDERATION_ASIA_OMSK": "Asie/Omsk",
    "RUSSIAN_FEDERATION_ASIA_SAKHALIN": "Asie/Sakhaline",
    "RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK": "Asie/Vladivostok",
    "RUSSIAN_FEDERATION_ASIA_YAKUTSK": "Asie/Iakoutsk",
    "RUSSIAN_FEDERATION_ASIA_YEKATERINBURG": "Asie/Iekaterinbourg",
    "RUSSIAN_FEDERATION_EUROPE_KALININGRAD": "Europe/Kaliningrad",
    "RUSSIAN_FEDERATION_EUROPE_MOSCOW": "Europe/Moscou",
    "RUSSIAN_FEDERATION_EUROPE_SAMARA": "Europe/Samara",
    "RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD": "Europe/Volgograd",
    "RUSSIAN_FEDERATION": "Fédération russe",
    "RWANDA_AFRICA_KIGALI": "Afrique/Kigali",
    "RWANDA": "Rwanda",
    "RX_BYTES": "Octets reçus",
    "RX_PACKETS": "Paquets reçus",
    "SA_EAST_1": "sa-east-1 (Sao Paulo)",
    "SA_EAST_1A": "sa-east-1a",
    "SA_EAST_1B": "sa-east-1b",
    "SA_EAST_1C": "sa-east-1c",
    "SAFE_SEARCH_ENGINE": "Moteur SafeSearch",
    "SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY": "Amérique/Saint- Barthélémy",
    "SAINT_BARTHELEMY": "Saint-Barthélemy",
    "SAINT_HELENA": "Sainte-Hélène",
    "SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS": "Amérique/Saint- Kitts",
    "SAINT_KITTS_AND_NEVIS": "Saint-Kitts-et-Nevis",
    "SAINT_LUCIA_AMERICA_ST_LUCIA": "Amérique/Sainte- Lucie",
    "SAINT_LUCIA": "Sainte-Lucie",
    "SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT": "Amérique/Marigot",
    "SAINT_MARTIN_FRENCH_PART": "Saint-Martin (partie française)",
    "SAINT_MARTIN": "Saint-Martin",
    "SAINT_PIERRE_AND_MIQUELON": "Saint-Pierre-et-Miquelon",
    "SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT": "Amérique/Saint- Vincent",
    "SAINT_VINCENT_AND_THE_GRENADINES": "Saint-Vincent-et-les Grenadines",
    "SAML_CERTIFICATE_FILENAME": "Certificat SAML d'IdP",
    "SAMOA_PACIFIC_APIA": "Pacifique/Apia",
    "SAMOA": "Samoa",
    "SAN_MARINO_EUROPE_SAN_MARINO": "Europe/Saint-Marin",
    "SAN_MARINO": "Saint-Marin",
    "SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME": "Afrique/Sao Tomé",
    "SAO_TOME_AND_PRINCIPE": "Sao Tomé-et-Principe",
    "SATURDAY": "Samedi",
    "SAUDI_ARABIA_ASIA_RIYADH": "Asie/Riyad",
    "SAUDI_ARABIA": "Arabie saoudite",
    "SAVE_AND_NEXT": "Enregistrer et Suivant",
    "SAVE_ERROR_MESSAGE": "Il y a une erreur de validation. Veuillez revérifier tous les champs avant d'enregistrer.",
    "SAVE_SUCCESS_MESSAGE": "Toutes les modifications ont été enregistrées",
    "SAVE": "Enregistrer",
    "SCCP_DESC": " Le protocole SCCP (Skinny Client Control Protocol) est un protocole propriétaire Cisco utilisé entre Cisco Call Manager et les téléphones VoIP Cisco. Il est également pris en charge par d'autres fournisseurs.",
    "SCCP": "SCCP",
    "SCHEDULE_UPGRADE": "Planifier une mise à niveau",
    "SCHEDULED_VERSION": "Version planifiée",
    "SCHEDULED": "Planifié(e)",
    "SCIENCE_AND_TECHNOLOGY": "Sciences/Technologies",
    "SCOPE": "Périmètre",
    "SCTP_ANY_DESC": "Le protocole SCTP (Stream Control Transmission Protocol ) est utilisé dans la couche de transport de la suite IP (Internet Protocol). Conçu à l'origine pour le transport des messages du système de signalisation 7 (SS7) dans le domaine des télécommunications, le protocole fournit les caractéristiques orientées message du protocole UDP (User Datagram Protocol), tout en assurant un transport fiable et séquentiel des messages avec un contrôle de la congestion comme le fait le protocole TCP (Transmission Control Protocol).",
    "SCTP_ANY": "SCTP",
    "SCTP_DEST_PORTS": "Ports de destination SCTP",
    "SCTP_PORT": "Port SCTP",
    "SCTP_PORTS": "Ports SCTP",
    "SCTP_SRC_PORTS": "Ports source SCTP",
    "SCTP_UDP_Translation": "Traduction de SCTP/UDP",
    "SEARCH_BY": "Recherche par :",
    "SEARCH_ELLIPSIS": "Rechercher...",
    "SEARCH_LOCATION": "Rechercher un emplacement",
    "SEARCH_RESULT": "Résultat de la recherche",
    "SEARCH_TO_SEE_MORE": "Recherchez pour afficher plus d'éléments",
    "SEARCH": "Rechercher",
    "SECONDARY_DNS_OPTIONAL": "DNS secondaire (facultatif)",
    "SECONDARY_DNS_SERVER_IP_ADDRESS": "Adresse IP du serveur DNS secondaire",
    "SECONDARY_DNS_SERVER": "Serveur DNS secondaire",
    "SECONDARY_DNS": "DNS secondaire",
    "SECONDARY_PROXY": "Proxy secondaire",
    "SECONDARY_SERVER_RESPONSE_PASS": "Tentative sur serveur secondaire",
    "SECONDARY": "Secondaire",
    "SECURITY_GROUP_ID": "ID du groupe de sécurité",
    "SECURITY_GROUP_NAME": "Nom du groupe de sécurité",
    "SEGMENT_GROUPS": "Groupes de segments",
    "SELECT_A_LOCATION": "Sélectionnez un emplacement",
    "SELECT_A_TEST": "Sélectionner un test",
    "SELECT_ALL": "Tout sélectionner",
    "SELECT_AN_EXISTING_LOCATION": "Sélectionner un emplacement existant",
    "SELECT_BRANCH_PROVISIONING_LOCATION": "Sélectionnez un emplacement pour votre modèle de provisionnement de Branch Connector.",
    "SELECT_CC_GROUP": "Sélectionner un groupe de connecteurs cloud",
    "SELECT_CC_LOCATION": "Sélectionner l’emplacement du connecteur cloud",
    "SELECT_CC_VERSION": "Sélectionner la version du connecteur cloud",
    "SELECT_CHART_TYPE": "Sélectionner le type de diagramme",
    "SELECT_DESTINATION_IP": "Sélectionner l'adresse IP de destination",
    "SELECT_EVENT_TIME": "Sélectionner l'heure de l'événement",
    "SELECT_FILTERS": "Sélectionner les filtres",
    "SELECT_HYPERVISOR_VERSION": "Sélectionner la version de l'hyperviseur",
    "SELECT_RESOURCE_GROUP_NAME": "Sélectionner un groupe de ressources",
    "SELECT_STORAGE_ACCOUNT": "Sélectionner un compte de stockage",
    "SELECT_SUBSCRIPTION_GROUP_NAME": "Sélectionner un nom de groupe d'abonnements",
    "SELECT_SUBSCRIPTION": "Sélectionner un abonnement",
    "SELECT_TWO_VERSIONS_TO_COMPARE": "Veuillez sélectionner deux versions à comparer.",
    "SELECT_UPGRADE_WINDOW": "Sélectionner la fenêtre de mise à niveau",
    "SELECT_ZSCALER_IP": "Sélectionnez l'IP de Zscaler",
    "SELECT": "Sélectionner",
    "SELECTED_ITEMS": "Éléments sélectionnés ({{count}})",
    "SELECTED": "Sélectionné(e)",
    "SENEGAL_AFRICA_DAKAR": "Afrique/Dakar",
    "SENEGAL": "Sénégal",
    "SENT_BYTES": "Octets envoyés",
    "SENT_COUNT": "Nombre d'envois",
    "SENT_MESSAGES": "Messages envoyés",
    "SERBIA_EUROPE_BELGRADE": "Europe/Belgrade",
    "SERBIA": "Serbie",
    "SERIAL_NUMBER": "Numéro de série",
    "SERVER_DESTINATION_IP": "IP de destination du serveur",
    "SERVER_DESTINATION_PORT": "Port de destination du serveur",
    "SERVER_IP_CATEGORY": "Catégorie d’IP du serveur",
    "SERVER_IP": "IP du serveur",
    "SERVER_NAME": "Nom du serveur",
    "SERVER_NETWORK_PROTOCOL": "Protocole réseau du serveur",
    "SERVER_PORT": "Port du serveur",
    "SERVER_SOURCE_IP": "IP source du serveur",
    "SERVER_SOURCE_PORT": "Port source du serveur",
    "SERVER": "Trafic du serveur",
    "SERVERS": "Serveurs",
    "SERVFAIL": "Défaillance du serveur",
    "Service IP": "IP de service",
    "SERVICE_GATEWAY_IP_ADDRESS": "Adresse IP de la passerelle de service",
    "SERVICE_GROUPS": "Groupes de services",
    "SERVICE_INFORMATION": "Informations sur le service",
    "SERVICE_INTERFACE": "Interface du service",
    "SERVICE_IP_ADDRESS_ONE": "Adresse IP du service 1",
    "SERVICE_IP_ADDRESS_POOL": "Pool d'adresses IP du serveur",
    "SERVICE_IP_ADDRESS_THREE": "Adresse IP du service 3",
    "SERVICE_IP_ADDRESS_TWO": "Adresse IP du service 2",
    "SERVICE_IP_ADDRESS": "Adresse IP du service",
    "SERVICE_IP": "IP de service",
    "SERVICE_STATUS": "Statut de service",
    "SERVICE_VIRTUAL_IP_ADDRESS": "Adresse IP virtuelle de service",
    "SERVICE": "Service",
    "SERVICES": "Services",
    "SESSION_COUNT_TREND": "Tendance du nombre de sessions",
    "SESSION_COUNT": "Nombre de sessions",
    "SESSION_DURATION": "Durée de la session",
    "SESSION_ID": "ID de session",
    "SESSION_INSIGHTS": "Informations sur la session",
    "SESSION_LOGS": "Journaux des sessions",
    "SESSION_TIMED_OUT": "Votre session a expiré. Connectez-vous à nouveau pour continuer.",
    "SESSION": "Session",
    "SESSIONS_ACROSS_SERVICES": "Sessions pour tous les services",
    "SESSIONS": "Sessions",
    "SET_PASSWORD": "DÉFINIR LE MOT DE PASSE",
    "SEXUALITY": "Sexualité",
    "SEYCHELLES_INDIAN_MAHE": "Océan Indien/Mahé",
    "SEYCHELLES": "Seychelles",
    "SHAREWARE_DOWNLOAD": "Téléchargement de sharewares",
    "SHOW_DETAILS": "Afficher les détails",
    "SHOW_LESS_ELLIPSIS": "Montrer moins...",
    "SHOW_MORE_ELLIPSIS": "Montrer plus...",
    "SHOW": "Afficher",
    "SHUTDOWN": "Arrêter",
    "SIERRA_LEONE_AFRICA_FREETOWN": "Afrique/Freetown",
    "SIERRA_LEONE": "Sierra Leone",
    "SIGN_IN": "Connexion",
    "SIGN_OUT": "Se déconnecter",
    "SIGNING_CERTIFICATE": "Certificat de signature",
    "SINGAPORE": "Singapour",
    "SINGLE_APPLIANCE_ADDED_INFO": "Vous pouvez voir la nouvelle appliance sur la page Appliances. Pour plus d'informations, voir le {1}portail d'aide{2} de Connectors.",
    "SINGLE_APPLIANCE_ADDED": "1 nouvelle appliance a été ajoutée à votre tenant.",
    "SIP_DESC": " Le protocole SIP (Session Initiation Protocol) désigne la norme IETF (Internet Engineering Task Force) des conférences multimédias sur IP. Tout comme les autres protocoles VoIP, le protocole SIP traite les fonctions de signalement et de gestion de session sur un réseau de téléphonie par paquets.",
    "SIP": "SIP",
    "SIZE_MUST_BE_EXACT_LENGTH": "Ce champ doit avoir une taille de ",
    "SLOVAKIA_EUROPE_BRATISLAVA": "Europe/Bratislava",
    "SLOVAKIA": "Slovaquie",
    "SLOVENIA_EUROPE_LJUBLJANA": "Europe/Ljubljana",
    "SLOVENIA": "Slovénie",
    "SMALL": "Petit",
    "SMB_DESC": " Le protocole SMB/SMB2 (Server Message Block Protocol) permet aux applications clientes de lire et de modifier des fichiers, mais aussi de recourir à des services provenant de programmes serveur sur un réseau informatique.",
    "SMB": "SMB",
    "SMBA": "SMBA",
    "SMBAC": "Contrôleur sandbox",
    "SMBAUI": "Interface utilisateur Sandbox",
    "SMEDGE_BOOTING": "SMEDGE : démarrage en cours.",
    "SMEDGE_END": "Codes d'erreur de fin de rebord. Ne pas utiliser.",
    "SMEDGE_INIT": "SMEDGE : lancement en cours.",
    "SMEDGE_NOT_RUNNING": "SMEDGE : le processus ne s'exécute pas.",
    "SMEDGE_PKG_DOWNLOAD": "SMEDGE : téléchargement du paquet en cours.",
    "SMEDGE_PKG_INSTALL": "SMEDGE : Installation du package en cours.",
    "SMEDGE_START": "Début des codes d'erreur de smedge. Ne pas utiliser.",
    "SMEDGE_UNKNOWN_ERROR": "SMEDGE : code d'erreur de la passerelle inconnu.",
    "SMEDGE_UPDATING": "SMEDGE : Mise à jour.",
    "SMEDGE_UPGRADING": "SMEDGE : Mise à niveau en cours.",
    "SMEDGE_ZIA_BRINGUP": "SMEDGE : Mise en place des tunnels ZIA.",
    "SMEDGE_ZIA_ZPA_BRINGUP": "SMEDGE : Mise en place des tunnels ZIA et établissement des connexions ZPA.",
    "SMRES_ERROR": "L’application serveur SMRES a renvoyé une réponse d’erreur HTTP.",
    "SMTP_AV_ENCRYPTED_ALLOW": "Pièce jointe chiffrée autorisée",
    "SMTP_AV_ENCRYPTED_ATTACH_DROP": "Pièce jointe chiffrée par mot de passe, pièce jointe supprimée",
    "SMTP_AV_ENCRYPTED_DROP": "Pièce jointe chiffrée par mot de passe, message rejeté",
    "SMTP_AV_UNSCANNABLE_ALLOW": "Pièce jointe non analysable autorisée",
    "SMTP_AV_UNZIPPABLE_ATTACH_DROP": "Pièce jointe non analysable, pièce jointe supprimée",
    "SMTP_AV_UNZIPPABLE_DROP": "Pièce jointe non analysable, message rejeté",
    "SMTP_AV_UWL": "Virus UWL,",
    "SMTP_AV_VIRUS_ATTACH_DROP": "Virus trouvé, pièce jointe supprimée",
    "SMTP_AV_VIRUS_DROP": "Virus trouvé, message rejeté",
    "SMTP_CMD_TIMEOUT_LIMIT": "SMTP/SMTQTN : valeur d’expiration de la commande (en secondes)",
    "SMTP_CONCURRENT_CLIENT_CONN_LIMIT": "SMTP/SMQTN : limite de connexions client simultanées",
    "SMTP_CONCURRENT_SERVER_CONN_LIMIT": "SMTP/SMQTN : limite de connexions serveur simultanées",
    "SMTP_DATA_TIMEOUT_LIMIT": "SMTP/SMQTN : valeur d’expiration dans la commande DATA (en secondes)",
    "SMTP_DESC": "Le protocole SMTP (Simple Mail Transfer Protocol) désigne un protocole d'envoi d'e-mails entre serveurs.",
    "SMTP_DLP_ALLOW": "DLP trouvée, message autorisé",
    "SMTP_DLP_DROP": "DLP trouvée, message rejeté",
    "SMTP_DLP_QTN": "DLP trouvée, message placé en quarantaine",
    "SMTP_DLP_SIGN_REQUIRED": "DLP trouvée, message non signé, message rejeté",
    "SMTP_DLP_TLS_REQUIRED": "DLP trouvée, connexion non TLS, message rejeté",
    "SMTP_DLP": "SMTP - Conformité",
    "SMTP_EODT_TIMEOUT_LIMIT": "SMTP/SMQTN : valeur d’expiration dans la commande EODT (en secondes)",
    "SMTP_ERRINJECTION_DELAY_LIMIT": "GULPER : retard d’injection d’erreur (en secondes)",
    "SMTP_FLOWCONTROL": "Contrôle de flux de remise de messages - uniquement d'application pour SMQTN",
    "SMTP_INCOMPL_TRANS": "Transaction SMTP terminée par un homologue",
    "SMTP_INSPOL": "Politique d'assurance SMTP",
    "SMTP_MAILPERCONN_LIMIT": "SMTP/SMQTN : limite de messages par connexion ",
    "SMTP_MAILSIZE_LIMIT": "SMTP/SMQTN : limite de taille de message",
    "SMTP_MF_ATTACHBLK_ATTACH_DROP": "Pièce jointe bloquée, pièce jointe supprimée",
    "SMTP_MF_ATTACHBLK_MSG_DROP": "Pièce jointe bloquée, message rejeté",
    "SMTP_MF_RCPT_DROP": "Destinataire rejeté",
    "SMTP_MF_SIGN_REQUIRED": "Destinataire rejeté, message non signé",
    "SMTP_MF_TLS_REQUIRED": "Destinataire rejeté, connexion non TLS",
    "SMTP_MIN_MSBC_DENSITY": "SMTP/SMQTN : serrer si la densité de msbs arrivant dans data_in est inférieure à ce pourcentage minimal",
    "SMTP_NOCA_BYPASS_CONFIG": "Stratégie contournée",
    "SMTP_NOCA_BYPASS": "SMTP/SMQTN : utiliser la config SMTP par défaut si la CA est indisponible ou si la réponse de config contient des erreurs",
    "SMTP_OUTBD_DROP_SUSPECTED_SPAM": "Supprimer les messages sortants soupçonnés d’être du spam",
    "SMTP_PLATFORM_1": "SMTP - Plate-forme",
    "SMTP_PLATFORM_2": "SMTP - Plate-forme II (sortie)",
    "SMTP_PRETRY": "Réessayer depuis proxy - uniquement d'application pour le nœud SMTP",
    "SMTP_PROXY": "Cluster de proxy SMTP",
    "SMTP_RCPT_COPY": "Le destinataire est une copie,",
    "SMTP_RCPT_REDIRECT": "Le destinataire est une redirection,",
    "SMTP_RCPT_UNDELIVERABLE": "Destinataire rejeté, livraison impossible",
    "SMTP_RCPTS_LIMIT": "SMTP/SMQTN : limite de destinataires par transaction SMTP",
    "SMTP_RESERVED2": "SMTP: reserved2",
    "SMTP_RESERVED3": "SMTP: reserved3",
    "SMTP_RESERVED4": "SMTP: reserved4",
    "SMTP_RESERVED5": "SMTP: reserved5",
    "SMTP_REUSE_TIMEOUT_LIMIT": "SMTP/SMQTN : valeur d’expiration du pool de réutilisation de connexions",
    "SMTP_SECURE": "SMTP - Sécurisé",
    "SMTP_SENDER_MASQ": "Expéditeur fictif,",
    "SMTP_SMQTN": "Cluster de quarantaine SMTP",
    "SMTP_SPAM_DROP": "Spam détecté, message rejeté",
    "SMTP_SPAM_IPWL": "Spam IPWL,",
    "SMTP_SPAM_SUSPECT_ALLOW": "Suspicion de spam, message autorisé",
    "SMTP_SPAM_SUSPECT_DROP": "Suspicion de spam, message rejeté",
    "SMTP_SPAM_SUSPECT_MARKSUBJ": "Suspicion de spam, début d'objet modifié",
    "SMTP_SPAM_SUSPECT_QTN": "Suspicion de spam, message placé en quarantaine",
    "SMTP_SPAM_UBL_DROP": "Spam UBL détecté, message rejeté",
    "SMTP_SPAM_UWL": "Spam UWL,",
    "SMTP_SPAM": "SMTP - Paramètres antispam",
    "SMTP_SPF_ENABLED": "La recherche SPF est activée par cette entreprise",
    "SMTP_TRANS_TIMEOUT_LIMIT": "SMTP/SMTQTN : valeur d’expiration de transaction (en secondes)",
    "SMTP_USERLIST_LIMIT": "Valeur LIMIT de requête userlist SMTP",
    "SMTP": "SMTP",
    "SMTPEVT_VERBOSE": "Consigner les événements SMTP qui sont marqués comme en clair",
    "SMTPTDL": "TDL - Limite de données de la transaction",
    "SMUI_PROCESS_TIMEOUT": "valeur d'expiration du processus SMUI",
    "SMUI": "SMUI",
    "SN_POSTING_CAUTIONED": "Mis en garde sur la publication de messages sur ce site",
    "SN_POSTING_DENIED": "Non autorisé à publier un message sur ce site",
    "SN_WEBUSE_CAUTIONED": "Mis en garde sur l’utilisation de ce réseau social / site de blogs",
    "SN_WEBUSE_DENIED": "Non autorisé à utiliser ce réseau social /site de blogs",
    "SNAGFILMS_DESC": " SnagFilms",
    "SNAGFILMS": "SnagFilms",
    "SNMP_DESC": " Applications SNMP (également appelées gestionnaires SNMP) et agents SNMP",
    "SNMP": "SNMP: Le protocole SNMP est un protocole de demande/réponse qui communique des informations de gestion entre deux types d'entité logicielle SNMP.",
    "SNMPTRAP_DESC": "Les interruptions SNMP permettent à un agent de notifier la station de gestion d'événements significatifs par le biais d'un message SNMP non sollicité.",
    "SNMPTRAP": "Information (trap) SNMP",
    "SOA": "SOA",
    "SOAP_DESC": " SOAP est un protocole léger permettant d'échanger des informations structurées dans un environnement décentralisé et distribué. A l'aide de technologies XML, il définit un cadre de messagerie extensible comportant une structure de message pouvant être échangée sur de nombreux protocoles sous-jacents.",
    "SOAP": "SOAP",
    "SOC1": "SOC1",
    "SOC2": "SOC2",
    "SOC3": "SOC3",
    "SOCIAL_ACTIVITY": "Activité de réseautage social",
    "SOCIAL_ADULT_DESC": "Sites qui fournissent des réseaux sociaux pour adultes, tels que les sites de rencontres.",
    "SOCIAL_ADULT": "Réseaux sociaux pour adultes",
    "SOCIAL_ISSUES": "Problèmes sociaux",
    "SOCIAL_NETWORKING_GAMES": "Jeux sur réseau social",
    "SOCIAL": "Réseaux sociaux",
    "SOCIALBAKERS": "SocialBakers",
    "SOCIALTV_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte srv.sixdegs.com.",
    "SOCIALTV": "Social TV",
    "SOCIALVIBE_DESC": " Ce plug-in de protocole classe le trafic http vers l’hôte socialvibe.com",
    "SOCIALVIBE": "SocialVibe",
    "SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP": "L'heure de mise à niveau prévue dépend sur le fuseau horaire de votre connecteur. Les mises à niveau doivent être programmées au moins une heure à l'avance.",
    "SOFTWARE_UPGRADE_SCHEDULE": "Calendrier de mise à niveau des logiciels",
    "SOLOMON_ISLANDS_PACIFIC_GUADALCANAL": "Pacifique/Guadalcanal",
    "SOLOMON_ISLANDS": "Iles Salomon",
    "SOMALIA_AFRICA_MOGADISHU": "Afrique/Mogadiscio",
    "SOMALIA": "Somalie",
    "SORRY_THIS_CODE_CAN_NOT_BE_USED": "Désolé, mais cela ne peut pas être utilisé.",
    "SOURCE_IP_ADDRESSES": "Adresses IP source",
    "SOURCE_IP_GROUP": "Groupe d'IP source",
    "SOURCE_IP_GROUPS": "Groupes d’IP sources",
    "SOURCE_IP": "IP source",
    "SOURCE_PORT": "Port source",
    "SOURCE": "Source",
    "SOUTH_AFRICA_AFRICA_JOHANNESBURG": "Afrique/Johannesburg",
    "SOUTH_AFRICA": "Afrique du Sud",
    "SOUTH_AMERICA": "Amérique du Sud",
    "SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS": "Géorgie du Sud et îles Sandwich du Sud",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA": "Atlantique/Géorgie du Sud",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS": "Géorgie du Sud-et-les Iles Sandwich du Sud",
    "SOUTH_KOREA": "République de Corée",
    "SOUTHAFRICA": "Afrique du Sud",
    "SOUTHAFRICANORTH": "(Afrique) Nord de l'Afrique du Sud ",
    "SOUTHAFRICAWEST": "(Afrique) Ouest de l'Afrique du Sud",
    "SOUTHAMERICA_EAST1_A": "southamerica-east1-a",
    "SOUTHAMERICA_EAST1_B": "southamerica-east1-b",
    "SOUTHAMERICA_EAST1_C": "southamerica-east1-c",
    "SOUTHAMERICA_EAST1": "southamerica-east1",
    "SOUTHAMERICA_WEST1_A": "southamerica-west1-a",
    "SOUTHAMERICA_WEST1_B": "southamerica-west1-b",
    "SOUTHAMERICA_WEST1_C": "southamerica-west1-c",
    "SOUTHAMERICA_WEST1": "southamerica-west1",
    "SOUTHCENTRALUS": "(États-Unis) Centre-sud des États-Unis",
    "SOUTHCENTRALUSSTAGE": "(États-Unis) Centre sud des États-Unis (étape)",
    "SOUTHEASTASIA": "(Asie Pacifique) Asie du Sud-Est",
    "SOUTHEASTASIASTAGE": "(Asie Pacifique) Asie du Sud-Est (étape)",
    "SOUTHINDIA": "(Asie-Pacifique) Inde du Sud",
    "SPAIN_AFRICA_CEUTA": "Afrique/Ceuta",
    "SPAIN_ATLANTIC_CANARY": "Atlantique/Canaries",
    "SPAIN_EUROPE_MADRID": "Europe/Madrid",
    "SPAIN": "Espagne",
    "SPECIAL_INTERESTS": "Intérêts spécifiques/organisations sociales",
    "SPECIALIZED_SHOPPING": "Shopping en ligne",
    "SPLIT_DEPLOY_CORE": "Déploiement fractionné - Core",
    "SPLIT_DEPLOY_EDGE": "Déploiement fractionné - Edge",
    "SPLUNK": "Splunk",
    "SPORTS": "Sports",
    "SPYWARE_OR_ADWARE": "Spyware ou adware",
    "SRI_LANKA_ASIA_COLOMBO": "Asie/Colombo",
    "SRI_LANKA": "Sri Lanka",
    "SRV_RX_BYTES": "Octets reçus par le serveur",
    "SRV_TIMEOUT": "La transaction DNS a expiré car le serveur n'a pas répondu",
    "SRV_TX_BYTES": "Octets envoyés par le serveur",
    "SRV_TX_DROPS": "Le serveur supprime des octets",
    "SSDP_DESC": " Le protocole SSDP (Simple Service Discovery Protocol) permet aux clients réseau de détecter les services réseau souhaités.",
    "SSDP": "SSDP",
    "SSH_DESC": " Secure Shell SSH, parfois appelé Secure Socket Shell, désigne une interface de commande UNIX et un protocole permettant d'obtenir un accès sécurisé à un ordinateur distant.",
    "SSH": "SSH",
    "SSHFP": "SSHFP",
    "SSL_CERTIFICATE": "Certificat SSL",
    "SSO_LOGOUT_MESSAGE": "Vous vous êtes déconnecté du portail Cloud Connector",
    "ST_HELENA_ATLANTIC_ST_HELENA": "Atlantique/Sainte- Hélène",
    "ST_HELENA": "Sainte-Hélène",
    "ST_KITTS_AND_NEVIS": "Saint-Kitts-et-Nevis",
    "ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON": "Amérique/Miquelon",
    "ST_PIERRE_AND_MIQUELON": "Saint-Pierre-et-Miquelon",
    "ST_VINCENT_AND_THE_GRENADINES": "Saint-Vincent-et-les-Grenadines",
    "STAGED": "Organisé",
    "STANDBY": "Veille",
    "START_OVER": "Recommencer à zéro",
    "START_TIME": "Heure de début",
    "STARTS_WITH": "Commence par",
    "STAT": "Stats",
    "STATE_PROVINCE": "Ville/État/Province",
    "STATE": "Etat",
    "STATIC_IP_ADDRESS": "Adresse IP statique",
    "STATIC_IP_ADDRESSES": "Adresses IP statiques et tunnels GRE",
    "STATIC_IP_CONFLICT_WITH_SUBINTERFACE_IP": "L’adresse IP de bail statique est en conflit avec l’adresse IP de la sous-interface",
    "STATIC_IP_HAS_DUPLICATES_IPs": "Le bail statique comporte des adresses IP en doublon.",
    "STATIC_IP_HAS_DUPLICATES_MACS": "Le bail statique contient des adresses MAC en doublon.",
    "STATIC_LEASE": "Bail statique",
    "STATIC_LOCATION_GROUPS": "Groupes d'emplacements manuels",
    "STATIC_MANAGEMENT_IP": "IP de gestion statique ",
    "STATIC_ROUTE_OPTIONAL": "Routage statique (facultatif)",
    "STATIC_ROUTE": "Route statique",
    "STATIC_SERVICE_IP": "IP de service statique",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1": "Voulez-vous vraiment mettre à jour l’état du modèle ?",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED": "L'élément mis à jour peut être modifié/supprimé une fois qu'il est passé à l'état Déployé.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1": "La configuration sera appliquée à l’appareil lorsqu’il sera mis en ligne.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED": "Le modèle ne pourra pas être modifié une fois qu’il passera en mode Prêt à être déployé.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE": "Voulez-vous vraiment modifier cette ressource ?",
    "STATUS_UPDATE_CONFIRMATION": "Confirmation de  mise à jour du statut !",
    "STATUS": "Statut",
    "STORAGE_ACCOUNT_TEXT": "Sélectionnez les régions, abonnements et groupes de comptes de stockage où le sujet partenaire et la destination sont créés.",
    "STORAGE_ACCOUNT": "Compte de stockage",
    "STREAMING_MEDIA": "Diffusion de vidéo en streaming",
    "STRING": "Chaîne",
    "SUB_CATEGORIES": "Sous-catégories",
    "SUB_CATEGORY": "Sous-catégorie",
    "SUB_INTERFACE_SHUTDOWN": "Arrêt de la sous-interface",
    "SUB_INTERFACE_VLAN": "VLAN de la sous-interface",
    "SUB_INTERFACE": "Sous-interface",
    "SUBCLOUDS": "Sous-nuages",
    "SUBLOCATIONS": "Sous-emplacements",
    "SUBMIT_A_TICKET": "Envoyer un ticket",
    "SUBMIT_TICKET": "Envoyer un ticket",
    "SUBMIT": "Envoyer",
    "SUBMITTED_ON": "Envoyé(e) le",
    "SUBNET_ID": "ID de sous-réseau",
    "SUBSCRIPTION_GROUP_NAME": "Nom du groupe d'abonnements",
    "SUBSCRIPTION_GROUP": "Groupe d'abonnements",
    "SUBSCRIPTION_GROUPS_TEXT": "Configurez les régions et les abonnements pour votre compte Azure.",
    "SUBSCRIPTION_GROUPS": "Groupes d'abonnements",
    "SUBSCRIPTION_ID": "Identifiant d'abonnement Azure",
    "SUBSCRIPTION_REQUIRED_MESSAGE": "Cette fonctionnalité nécessite un abonnement que votre organisation ne possède pas actuellement.  Pour en savoir plus sur cette fonctionnalité, veuillez contacter votre commercial.",
    "SUBSCRIPTION_REQUIRED": "Abonnement nécessaire",
    "SUBSCRIPTIONS": "Abonnements",
    "SUCCESS": "Réussite",
    "SUCCESSFULLY_DELETED": "Supprimé",
    "SUCCESSFULLY_DISABLED": "Désactivation réussie",
    "SUCCESSFULLY_ENABLED": "Activation réussie",
    "SUCCESSFULLY_REGENERATED": "Regénéré",
    "SUCCESSFULLY_SAVED": "Enregistré",
    "SUCCESSFULLY_UPDATED": "Mise à jour réussie",
    "SUDAN_AFRICA_KHARTOUM": "Afrique/Khartoum",
    "SUDAN": "Soudan",
    "SUM": "SOMME",
    "SUMO_LOGIC": "Sumo Logic",
    "SUNDAY": "Dimanche",
    "SUPPORT_INFORMATION": "Informations sur l'assistance",
    "SUPPORT_TUNNEL": "Tunnel de support",
    "SUPPORT": "Assistance",
    "SURINAME_AMERICA_PARAMARIBO": "Amérique/Paramaribo",
    "SURINAME": "Suriname",
    "SURROGATE_IP_REFRESH_RATE": "Intervalle d’actualisation pour une nouvelle validation de la substitution",
    "SUSPICIOUS_DESTINATION": "Destination suspecte",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN": "Arctique/Longyearbyen",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS": "Svalbard et Ile Jan Mayen",
    "SVALBARD_AND_JAN_MAYEN": "Svalbard et l'Ile Jan Mayen",
    "SVPN": "Z-Tunnel 2.0",
    "SWAZILAND_AFRICA_MBABANE": "Afrique/Mbabane",
    "SWAZILAND": "Swaziland",
    "SWEDEN_EUROPE_STOCKHOLM": "Europe/Stockholm",
    "SWEDEN": "Suède",
    "SWEDENCENTRAL": "(Europe) Suède Centre",
    "SWITZERLAND_EUROPE_ZURICH": "Europe/Zürich",
    "SWITZERLAND": "Suisse",
    "SWITZERLANDNORTH": "(Europe) Nord de la Suisse",
    "SWITZERLANDWEST": "(Europe) Ouest de la Suisse",
    "SYRIA": "République arabe syrienne",
    "SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS": "Asie/Damas",
    "SYRIAN_ARAB_REPUBLIC": "République arabe syrienne",
    "SYSLOG_DESC": " Le protocole Syslog permet de transmettre des notifications d'événement entre un client et un serveur.",
    "SYSLOG": "Syslog",
    "SYSTEM_IN_READ_ONLY_MODE_ONLY": "L'interface utilisateur est actuellement en mode Lecture seule.",
    "SYSTEM_IN_READ_ONLY_MODE": "L’interface utilisateur fait l’objet d’une mise à niveau. Il (elle) est actuellement en mode lecture seule.",
    "SYSTEM_SETTINGS": "Paramètres système",
    "SYSTEM_USER": "Utilisateur système",
    "SYSTEM": "Système",
    "TAB_SEPARATED": "Onglet séparé",
    "TABLE_OPTIONS": "Options de table",
    "TACACS_DESC": "TACACS (Terminal Access Controller Access-Control System) désigne une gamme de protocoles gérant l'authentification à distance et les services connexes pour un contrôle d'accès en réseau par le biais d'un serveur centralisé.",
    "TACACS_PLUS_DESC": " TACACS+ (Terminal Access Controller Access-Control System Plus) est un protocole propriétaire de Cisco Systems, qui fournit un contrôle d'accès aux routeurs, aux serveurs d'accès réseau et à d'autres appareils informatiques en réseau par le biais d'un ou plusieurs serveurs centralisés.",
    "TACACS_PLUS": "TACACS+",
    "TACACS": "TACACS",
    "TAGGED": "Tagged",
    "TAGS": "Balises",
    "TAIWAN_ASIA_TAIPEI": "Asie/Taipei",
    "TAIWAN": "Taïwan",
    "TAJIKISTAN_ASIA_DUSHANBE": "Asie/Douchanbé",
    "TAJIKISTAN": "Tadjikistan",
    "TANZANIA_AFRICA_DAR_ES_SALAAM": "Afrique/Dar es Salam",
    "TANZANIA": "Tanzanie",
    "TARGET_ORG_ID": "Identifiant d'organisation cible",
    "TARINGA_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte taringa.net.",
    "TARINGA": "Taringa",
    "TASTELESS_DESC": " Sites dédiés à la torture, aux mauvais traitements sur les animaux et les êtres humains, et à d'autres comportements généralement considérés comme inappropriés.",
    "TASTELESS": "Mauvais goût",
    "TATTOODESIGNS": "Conceptions de tatouage",
    "TB": "To",
    "TCF": "TCF",
    "TCHATCHE_DESC": "Tchatche est un site Web de messagerie instantanée.",
    "TCHATCHE": "Tchatche",
    "TCP_ANY_DESC": "Le protocole TCP (Transmission Control Protocol) est l'un des protocoles de base de la suite IP (Internet Protocol). Il est si courant que la suite est souvent appelée TCP/IP.",
    "TCP_ANY": "TCP",
    "TCP_DESC": " Le protocole TCP (Transmission Control Protocol) est l'un des protocoles de base de la suite IP (Internet Protocol). Il est si courant que la suite est souvent appelée TCP/IP.",
    "TCP_DEST_PORTS": "Ports de destination TCP",
    "TCP_OVER_DNS_DESC": " Tcp-over-dns comporte un serveur DNS et un client DNS spécifiques. Le client et le serveur fonctionnent de concert pour fournir un tunnel TCP et UDP par le biais du protocole standard DNS.",
    "TCP_OVER_DNS": "TCP sur DNS",
    "TCP_PORT": "Port TCP",
    "TCP_PORTS": "Ports TCP",
    "TCP_SRC_PORTS": "Ports de source TCP",
    "TCP_STATS_COUNTER_INTERVAL": "Capturer les compteurs d’états de tcp",
    "TCP_UNKNOWN_DESC": " Cela identifie le trafic de proxy/pare-feu TCP pour lequel il est impossible de déterminer une application plus granulaire.",
    "TCP_UNKNOWN": "TCP inconnu",
    "TCP": "TCP",
    "TDS_DESC": "Protocole pour le système de gestion de base de données relationnelle Microsoft SQL",
    "TDS": "TDS",
    "TEACHERTUBE_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné aux hôtes teachertube.com et teachertube.biz.",
    "TEACHERTUBE": "TeacherTube",
    "TEACHSTREET_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte teachstreet.com.",
    "TEACHSTREET": "TeachStreet",
    "TEAMSPEAK_DESC": " Le protocole TeamSpeak2 propriétaire est utilisé par les joueurs et il est orienté sur le logiciel de VoIP TeamSpeak2.",
    "TEAMSPEAK_V3_DESC": " TeamSpeak 3 est le successeur du système de communication TeamSpeak d'origine. TeamSpeak 3 ne constitue pas simplement une extension de ses prédécesseurs. Il s'agit d'une refonte totale de la technologie de base et du protocole propriétaire en C++.",
    "TEAMSPEAK_V3": "TeamSpeak 3",
    "TEAMSPEAK": "TeamSpeak",
    "TEAMVIEWER_DESC": " TeamViewer désigne une application qui permet d'établir une connexion avec un ordinateur distant pour des opérations de maintenance. Il est également possible d'afficher l'écran en cours sur un ordinateur distant, de transférer des fichiers et de créer un tunnel VPN.",
    "TEAMVIEWER": "TeamViewer",
    "TECHINLINE_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte techinline.com. Il classifie aussi le trafic SSL vers le nom commun techinline.com.",
    "TECHINLINE": "Techinline",
    "TECHNICAL_PRIMARY": "Contact principal technique",
    "TECHNICAL_SECONDARY": "Contact secondaire technique ",
    "TECHNOLOGY_COMMUNICATION": "Technologie et communication",
    "TECHNOLOGY": "Technologie",
    "TED": "TED",
    "TELECOMMUNICATION": "Télécommunication",
    "TELEGRAM_DESC": " Telegram désigne un protocole de messagerie instantanée comme WhatsApp",
    "TELEGRAM": "Telegram",
    "TELEVISION_AND_MOVIES_DESC": " Sites dédiés aux programmes télévisés et aux films, qu'il s'agisse de téléchargement ou de streaming de fichiers médias.",
    "TELEVISION_AND_MOVIES": "Télévision/Films",
    "TELEVISION_MOVIES_DESC": " Sites dédiés aux programmes télévisés et aux films, qu'il s'agisse de téléchargement ou de streaming de fichiers médias.",
    "TELEVISION_MOVIES": "Télévision/Films",
    "TELNET_DESC": " Telnet offre un outil de communication assez généraliste, bidirectionnel et orienté octet. Il vise principalement à fournir une méthode standard d'interface entre les terminaux et les processus orientés terminaux.",
    "TELNET": "Telnet",
    "TELNETS_DESC": " Version sécurisée de Telnet",
    "TELNETS": "Sécuriser Telnet",
    "TEMPLATE_NAME": "Nom du modèle",
    "TEMPLATE_NOT_FOUND": "Modèle introuvable",
    "TEMPLATE_PREFIX": "Préfixe du modèle",
    "TEMPLATE": "Modèle",
    "TENANT_ID": "Identifiant du locataire",
    "TENANT_NAME": "Nom du locataire",
    "TERRA_FORMATION": "Terraform",
    "TEST_CONNECTIVITY_FAILED": "Échec du test de connectivité",
    "TEST_CONNECTIVITY_SUCCESSFUL": "Test de la connectivité réussi",
    "TEST_ENVIRONMENT_TEXT": "Les tests de trafic sont des demandes HTTP/HTTPS simulées et exécutées à partir d'un environnement de test créé par Zscaler. Cet environnement de test est connecté à la passerelle à l'aide d'un terminal VPC. Un seul environnement de test est associé à un locataire Zscaler. Tous les tests sont exécutés depuis le même environnement.",
    "TEST_ENVIRONMENT": "Environnement de test",
    "TEST_EXECUTED": "Le test a été exécuté",
    "TEST_NAME": "Nom du test",
    "TEST_PROTOCOL": "Protocole du test",
    "TESTS": "Tests",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_R53": "Modèle de déploiement Starter avec ZPA et grande disponibilité",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_TEMPLATE": "Modèle de déploiement Starter avec grande disponibilité",
    "TF_DEFAULT_DEPLOYMENT_TEMPLATE": "Modèle de déploiement Starter",
    "TF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Modèle de déploiement de Starter avec ZPA",
    "TF_STARTER_DEPLOYMENT_GWLB_TEMPLATE": "Modèle de déploiement de Starter avec équilibreur de charge de passerelle (GWLB)",
    "TFTP_DESC": "Le protocole TFTP (Trivial File Transfer Protocol) est un protocole de transfert de fichiers bien connu pour sa simplicité.",
    "TFTP": "TFTP",
    "THAILAND_ASIA_BANGKOK": "Asie/Bangkok",
    "THAILAND": "Thaïlande",
    "THE_BASE_URL_FOR_YOUR_API": "L’URL de base de votre API est",
    "THE_FOLLOW_REGIONS_ARE_PENDING": "Les régions suivantes sont en attente ",
    "THE_FOLLOWING_STATIC_LEASE_IP_IS_NOT_INCLUDED_ON_THE_ADDRESS_RANGE": "L'adresse IP de bail statique suivante n'est pas incluse dans la plage d'adresses.",
    "THE_GAMBIA": "Gambie",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_SUBNET": " L'adresse IP de la passerelle doit se trouver dans un sous-réseau LAN.",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_WAN_SUBNET": " L'adresse IP de la passerelle doit se trouver dans un sous-réseau LAN ou WAN.",
    "THE_NETHERLANDS": "Pays-Bas",
    "THERE_IS_A_PROBLEM_SAVING_PROVISIONING_TEMPLATE": "Une anomalie est survenue lors de la suppression du modèle de provisionnement. Veuillez réessayer plus tard.",
    "THERE_IS_A_PROBLEM_SAVING_VDI_TEMPLATE": "Une anomalie est survenue lors de l'enregistrement du modèle VDI. Veuillez réessayer plus tard.",
    "THREAT_LIBRARY": "Bibliothèque de menaces",
    "THROUGHPUT_ACROSS_SERVICES": "Débit entre les services",
    "THROUGHPUT_KBPS_PER_SESSION": "Débit (kbps) / Session",
    "THROUGHPUT_SESSION": "[Débit | session]",
    "THURSDAY": "Jeudi",
    "TIME_FRAME": "Délai",
    "TIME_ZONE": "Fuseau horaire",
    "TIMESTAMP": "Horodatage",
    "TIMEZONE": "Fuseau horaire",
    "TIMOR_LESTE_ASIA_DILI": "Asie/Dili",
    "TIMOR_LESTE": "Timor-Leste",
    "TLS": "TLS",
    "TO": "A",
    "TOGO_AFRICA_LOME": "Afrique/Lomo",
    "TOGO": "Togo",
    "TOKELAU_PACIFIC_FAKAOFO": "Pacifique/Fakaofo",
    "TOKELAU": "Tokelau",
    "TOKEN_VALUE": "Valeur du jeton",
    "TOKEN": "Jeton",
    "TONGA_PACIFIC_TONGATAPU": "Pacifique/Tongatapu",
    "TONGA": "Tonga",
    "TOOLS": "Outils",
    "TOOLTIP_ACCOUNT_GROUP_DESCRIPTION": "Entrez des informations descriptives sur le groupe",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_AUTH_DEVICES": "Affiche les appareils de l'administrateur autorisés à utiliser l'application Executive Insights. Les administrateurs peuvent enregistrer jusqu'à 5 appareils.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_COMMENTS": "(Facultatif) Entrez des notes ou informations supplémentaires. Les commentaires ne peuvent pas comporter plus de 10 240 caractères.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_CONFIRM_PASSWORD": "Entrez à nouveau le mot de passe pour confirmer.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL": "Entrez l'adresse e-mail professionnelle valide de l'administrateur.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EXEC_MOBILE_APP_ENABLE": "Permet à l’administrateur d’accéder à l’application Executive Insights.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_LOGIN_ID": "Entrez l’identifiant de connexion que l’administrateur utilise pour se connecter à partir de votre portail de fournisseur SSO. Sélectionnez le nom de domaine approprié. (Les noms de domaine que vous avez fournis à Zscaler apparaissent dans le menu déroulant.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_MOBILE_APP_ENABLE": "Activez cette option pour autoriser un administrateur à accéder à l’application Executive Insights. Pour activer ce paramètre, l’administrateur a besoin d’une étendue <b>Organisation</b> et d’un <b>rôle</b> administrateur ainsi que la sélection de <b>Activer les autorisations pour l’application Executive InsightsEntrez</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_NAME": "Entrer le nom de l’administrateur",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD_BASED_LOGIN": "Activez cette option si vous souhaitez donner à l’administrateur la possibilité de se connecter directement au portail d’administration. Cela peut s'ajouter à l'activation de l'<b>authentification unique SAML pour les administrateurs</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD": "Entrez un mot de passe pour l'administrateur. Il peut comporter entre 8 et 100 caractères dont au moins un chiffre, un caractère spécial et une lettre majuscule.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PRODUCT_UPDATES": "Activez cette option si vous souhaitez que l’administrateur reçoive des communications par e-mail concernant les modifications et les mises à jour importantes de notre service.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_ROLE": "Sélectionnez un{1} de rôle {0}pour spécifier le niveau d’accès de l’administrateur au portail d’administration. Les rôles que vous avez configurés apparaissent dans le menu déroulant. Vous pouvez également rechercher des rôles ou cliquer sur l’icône {2}Ajouter{3} pour ajouter un nouveau rôle. Si vous avez activé {4}rang d’administrateur{5}, le rang d’administrateur qui vous est attribué détermine les rôles que vous pouvez sélectionner.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_DEPARTMENTS": "Choisissez les services que l’administrateur peut gérer dans le portail d’administration. ",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATION_GROUPS": "Choisissez les groupes d'emplacement que l'administrateur peut gérer dans le portail d'administration.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATIONS": "Choisissez les emplacements que l’administrateur peut gérer dans le portail d’administration.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE": "Sélectionnez une {0}étendue{1} pour spécifier les zones de l’organisation qu’un administrateur peut gérer dans le portail d’administration. L'étendue que vous avez choisie détermine celles que vous pouvez sélectionner.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SECURITY_UPDATES": "Activez cette option si vous souhaitez que l'administrateur reçoive des communications par e-mail sur les vulnérabilités et les menaces susceptibles d'affecter votre organisation.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SERVICE_UPDATES": "Activez cette option si vous souhaitez que l'administrateur reçoive des communications par e-mail concernant les nouvelles améliorations apportées aux services et aux produits, y compris les nouvelles notifications du centre de données et les informations sur les versions Cloud.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_STATUS": "Activer ou désactiver l'administrateur",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_UNAUTH_DEVICE": "Annuler l'autorisation",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ALLOW_TO_CREATE_NEW_LOCATION": "Autorisez l'utilisateur à créer des emplacements.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_COMMENTS": "(Facultatif) Entrez des notes ou informations supplémentaires. Les commentaires ne peuvent pas comporter plus de 10 240 caractères.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_CONFIRM_PASSWORD": "Entrer à nouveau le mot de passe pour confirmer",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_LOGIN_ID": "Entrez <b>l’identifiant de connexion de l’auditeur</b> et sélectionnez le nom de domaine approprié. (Les noms de domaine que vous avez fournis à Zscaler apparaissent dans le menu déroulant.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NAME": "Entrez le nom de l'<b>auditeur</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NEW_PASSWORD": "Entrez un mot de passe pour l’auditeur. Il peut comporter de 8 à 100 caractères et doit contenir au moins un chiffre, un caractère spécial et une lettre majuscule.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_EMAIL": "Entrez l'adresse e-mail de l’administrateur partenaire et sélectionnez le nom de domaine approprié. Les noms de domaine que vous avez fournis à Zscaler apparaissent dans le menu déroulant.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_LOGIN_ID": "Entrez l’identifiant de connexion que l’administrateur partenaire utilise pour se connecter à partir de votre portail de fournisseur SSO et sélectionnez le nom de domaine approprié. Les noms de domaine que vous avez fournis à Zscaler apparaissent dans le menu déroulant.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_NAME": "Entrer le nom de l’administrateur partenaire",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_PASSWORD": "Si vous souhaitez autoriser l’administrateur partenaire à se connecter directement au portail d'administration, entrez un mot de passe. Il peut comporter entre 8 et 100 caractères dont au moins un chiffre, un caractère spécial et une lettre majuscule. Cela peut s’ajouter à l’activation de l’authentification unique SAML pour les administrateurs partenaires.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_ROLE": "Sélectionnez un <b>rôle de partenaire</b> pour spécifier le niveau d'accès de l'administrateur partenaire au portail d'administration. Les rôles de partenaires que vous avez configurés apparaissent dans le menu déroulant. Vous pouvez également rechercher des rôles ou cliquer sur l’icône <b>Ajouter</b> pour ajouter un nouveau rôle de partenaire.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_DOWNLOAD_XML_METADATA": "Cliquez sur {0}Télécharger{1} pour exporter les métadonnées XML du service Zscaler. Les métadonnées détaillent les fonctionnalités SAML de Zscaler sont utilisées pour la configuration automatique. Certains {2}IdP{3} ont besoin des métadonnées pour configurer les fournisseurs de services.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ENABLE_SAML_AUTH": "Activez cette option pour permettre aux administrateurs de se connecter au portail d'administration directement depuis le {0}.portail de votre fournisseur de SSO{1}. Un {3}IdP{2} (par exemple, ADFS ou Okta) doit déjà être configuré pour votre organisation. Vous devez également {4}ajouter le compte administrateur{5} dans le portail d’administration (plutôt que via le provisionnement automatique).",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ISSUERS": "Vous pouvez éventuellement entrer l'émetteur de l'IdP associé au service Zscaler.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_UPLOAD_SSL_CERTIFICATE": "Cliquez sur {0}Charger{1} afin de charger le certificat public SSL utilisé pour vérifier la signature numérique de l'IdP. Il s’agit du format PEM codé en base 64 que vous avez téléchargé à partir de l’IdP. L'extension de fichier doit être .pem ou .cer et ne doit comporter aucun autre point (.) dans le nom du fichier.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML": "Activez cette option pour permettre à l’administrateur de se connecter directement au portail d’administration du connecteur cloud à l’aide d’un mot de passe. Vous pouvez utiliser ce mode d'authentification à l'aide du SSO SAML.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_ADMIN_EDGE_CONNECTOR_TRAFFIC_FORWARDING_DNS": "Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule au transfert (trafic, DNS et journaux).",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule à la gestion des clés API.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule à la gestion de l’assistance à distance.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule aux contrôles administratifs.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule au provisionnement du connecteur cloud.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD": " Décidez d’accorder ou non aux administrateurs un accès en lecture seule aux tableaux de bord.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_LOCATION_MANAGEMENT": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule à la gestion des emplacements.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION": " Décidez d’accorder ou non aux administrateurs un accès complet à NSS.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_POLICY_CONFIGURATION": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule à la stratégie et à l’administration.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule à la gestion de la configuration du cloud public.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE": " Décidez d’accorder ou non aux administrateurs un accès complet ou en lecture seule aux modèles de localisation et de provisionnement.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME": "Entrez un nom pour le rôle {0}{1}.",
    "TOOLTIP_ALL_APP_SEGMENTS_ONLY": "L'App Segment ZPA Edge est disponible uniquement si l'option Appliquer à tous les App Segments est activée.",
    "TOOLTIP_ALLOW_AWS_ACCOUNT_ID": "L’activation de cette option permettrait à Zscaler d’importer l’identifiant de compte AWS et de l’afficher dans le portail.",
    "TOOLTIP_ALLOW_AZURE_SUBSCRIPTION_ID": "L’activation de cette option permettrait à Zscaler d’importer l’identifiant d’abonnement Azure et de l’afficher dans le portail.",
    "TOOLTIP_ALLOW_GCP_PROJECT_ID": "L'activation de cette option permettrait à Zscaler d'importer l'idetifiant du projet GCP et de l'afficher dans le portail.",
    "TOOLTIP_AWS_CONFIG_NAME": "Le nom du compte AWS.",
    "TOOLTIP_AWS_GROUP_NAME": "Entrez le nom à utiliser pour ce groupe",
    "TOOLTIP_AWS_ROLE_NAME": "Le nom du rôle AWS dans le compte AWS saisi précédemment, qui est supposé par Zscaler.",
    "TOOLTIP_BC_BC_GROUP": "Sélectionnez un groupe de connecteurs de succursale existant pour votre modèle d’approvisionnement de succursale.",
    "TOOLTIP_BC_COUNTRY": "Sélectionnez un pays pour le nouvel emplacement.",
    "TOOLTIP_BC_DNS_SERVER": "Entrez l'adresse IP du serveur DNS.",
    "TOOLTIP_BC_FORWARDING_NET_MASK": "Entrez le masque réseau de l'adresse IP interne de la passerelle.",
    "TOOLTIP_BC_GROUP_5G": "Ce groupe de connecteurs sera associé à cette configuration de déploiement.",
    "TOOLTIP_BC_GROUP_NAME": "Entrez le nom du nouveau groupe de connecteurs de succursales que vous souhaitez ajouter.",
    "TOOLTIP_BC_HARDWARE_DEVICE": "Choisissez le dispositif matériel de votre Branch Connector.",
    "TOOLTIP_BC_HYPERVISOR": "Choisissez l’hyperviseur de votre connecteur de succursale.",
    "TOOLTIP_BC_INTERNAL_GATEWAY_IP_ADDRESS": "Entrez l'adresse IP interne de la passerelle.",
    "TOOLTIP_BC_IP_ADDRESS": "Entrez l'adresse IP du connecteur de succursale.",
    "TOOLTIP_BC_LOAD_BALANCER_IP_ADDRESS": "Entrez l'adresse IP de l'équilibreur de charge.",
    "TOOLTIP_BC_LOCATION_NAME": "Entrez le nom du nouvel emplacement que vous souhaitez ajouter.",
    "TOOLTIP_BC_LOCATION_TEMPLATE": "Sélectionnez un modèle d'emplacement par défaut ou configuré pour votre URL de provisionnement.",
    "TOOLTIP_BC_LOCATION": "Sélectionnez un emplacement actuel pour votre URL de provisionnement.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_1": "Entrez l’adresse IP du serveur DNS principal. Il s'agit de l'un des deux serveurs DNS utilisés pour l'équilibrage de charge.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_2": "Entrez l'adresse IP du serveur DNS secondaire. Il s'agit de l'un des deux serveurs DNS utilisés pour l'équilibrage de charge.",
    "TOOLTIP_BC_MANAGEMENT_NET_MASK": "Entrez le masque de réseau de l’adresse IP du connecteur de succursale.",
    "TOOLTIP_BC_PROVISIONING_NAME": "Entrez un nom pour le modèle de provisionnement de succursale.",
    "TOOLTIP_BC_SERVER_IP_ADDRESS": "Entrez l’adresse IP du service.",
    "TOOLTIP_BC_VM_SIZE": "Ce champ est défini sur Petit par défaut",
    "TOOLTIP_BLOCK_INTERNET_ACCESS": "Activez cette option pour désactiver tous les accès à Internet, y compris le trafic non HTTP, jusqu'à ce que l'utilisateur approuve la Stratégie d'utilisation acceptable.",
    "TOOLTIP_BW_DOWNLOAD": "Indiquez la limite maximale de bande passante du téléchargement (Mbit/s).",
    "TOOLTIP_BW_UPLOAD": "Indiquez la limite de bande passante maximale du chargement (Mbps).",
    "TOOLTIP_CC_ROLE_NAME": "Texte de l'infobulle pour Cloud Connector : ce nom doit être associé à tous les Cloud Connectors de ce compte.",
    "TOOLTIP_CLOUD_COONECTOR_GROUP": "Sélectionnez les groupes Cloud Connector qui seront associés à ce groupe de comptes",
    "TOOLTIP_CLOUD_NSS_HTTP_HEADERS": "",
    "TOOLTIP_CLOUD_PROVIDER": "Choisissez le fournisseur de cloud pour votre connecteur cloud.",
    "TOOLTIP_CPU": "L'unité centrale recommandée pour l'hyperviseur.",
    "TOOLTIP_CUSTOM_AUP_FREQUENCY": "",
    "TOOLTIP_DEDICATED_BANDWIDTH": "Il s'agit de la bande passante maximale nécessaire pour télécharger les journaux depuis le Nanolog dans le cloud Zscaler. Si le NSS ne dispose pas de la bande passante dont il a besoin, les journaux peuvent s’accumuler dans le Nanolog. Cela peut entraîner des réinitialisations fréquentes de la connexion et les journaux ne seront pas diffusés dans le NSS.",
    "TOOLTIP_DEFAULT_GATEWAY_IP_ADDRESS": "Entrez l'adresse IP de la passerelle par défaut.",
    "TOOLTIP_DEFAULT_GATEWAY": "Entrez une adresse IP valide pour la passerelle par défaut.",
    "TOOLTIP_DEFAULT_LEASE_TIME": "Entrez la durée de bail par défaut en secondes.",
    "TOOLTIP_DEPLOY_AS_GATEWAY": "Sélectionnez Oui ou Non pour décider si le périphérique matériel est déployé en tant que passerelle.",
    "TOOLTIP_DESCRIPTION": "(Facultatif) Saisissez des notes ou des informations supplémentaires.",
    "TOOLTIP_DESTINATION_IP_ADDRESS": "Entrez des adresses IP. Vous pouvez entrer des adresses IP, des sous-réseaux ou des plages d’adresses individuels. Si vous ajoutez plusieurs éléments, appuyez sur la touche Entrée après chaque entrée.",
    "TOOLTIP_DESTINATION_IP_COUNTRIES": "Pour identifier les destinations en fonction de l’emplacement d’un serveur, sélectionnez Tous pour inclure tous les pays du groupe ou sélectionnez des pays spécifiques.",
    "TOOLTIP_DESTINATION_IP_DOMAIN": "Entrez des noms de domaine (FQDN) ou des FQDN à caractères génériques. Utilisez le point (.) comme caractère générique. Pour ajouter plusieurs éléments, appuyez sur Entrée après chaque entrée.",
    "TOOLTIP_DESTINATION_IP_FQDN": "Entrez des noms de domaine complets (FQDN). Si vous ajoutez plusieurs éléments, appuyez sur la touche Entrée après chaque entrée.",
    "TOOLTIP_DESTINATION_IP_NAME": "Regroupez les destinations que vous souhaitez contrôler dans une règle de pare-feu, en spécifiant les adresses IP, les pays où se trouvent les serveurs et les catégories d'URL.",
    "TOOLTIP_DESTINATION_TYPE": "Sélectionner le type de groupe de destination",
    "TOOLTIP_DEVICE_SN": "Sélectionnez un numéro de série d'appareil.",
    "TOOLTIP_DHCP_OPTIONS": "Vous pouvez créer des noms de passerelle et de domaine par défaut comme critères pour DHCP.",
    "TOOLTIP_DHCP": "Sélectionnez Activé pour entrer les détails du serveur DNS ou Désactivé pour désactiver le protocole DHCP (Dynamic Host Configuration Protocol).",
    "TOOLTIP_DISK_STORAGE": "Affiche la capacité de stockage sur disque recommandée pour la charge de travail de votre organisation.",
    "TOOLTIP_DNS_SERVER_IP_1": "Entrez l’adresse IP du serveur DNS principal.",
    "TOOLTIP_DNS_SERVER_IP_2": "Entrez l’adresse IP du serveur DNS secondaire.",
    "TOOLTIP_DNS_SERVER": "Entrez une adresse IP valide pour le serveur DNS.",
    "TOOLTIP_DOMAIN_NAME": "Entrer un nom de domaine valide.",
    "TOOLTIP_EBS_STORAGE": "Capacité de stockage recommandée",
    "TOOLTIP_EC2_INSTANCE_TYPE": "Type d’instance EC2 recommandé",
    "TOOLTIP_EDIT_ORGANIZATION_API_KEY_NEW_KEY": "La nouvelle clé API peut être alphanumérique (A à Z, a à z, 0 à 9) et comporter exactement 12 caractères.",
    "TOOLTIP_ENABLE_AUP": "Activer l’affichage d’une stratégie d’utilisation acceptable pour le trafic non authentifié et exiger que les utilisateurs l’approuvent",
    "TOOLTIP_ENABLE_CAUTION": "Activez l'action Appliquer une stratégie de mise en garde et affichez une notification destinée à l'utilisateur final pour le trafic non authentifié. Si cette option est désactivée, l'action est traitée comme une stratégie d'autorisation.",
    "TOOLTIP_ENABLE_IPS_CONTROL": "Activez cette option pour permettre à un administrateur d'accéder au contrôle IPS.",
    "TOOLTIP_ENABLE_SURROGATE_BROWSER": "Si cette option est activée et si le mappage de l'utilisateur IP existe, l'identité de l'utilisateur de remplacement est également utilisée pour le trafic provenant de navigateurs connus. Si cette option est désactivée, le trafic provenant de navigateurs connus est toujours contesté à l'aide du mécanisme d'authentification configuré et l'identité de l'utilisateur Surrogate est ignorée",
    "TOOLTIP_ENABLE_SURROGATE_REFRESH_TIME": "Il s'agit de la durée pendant laquelle l'identité de l'utilisateur de substitution peut être utilisée pour le trafic provenant de navigateurs connus avant qu'il ne soit nécessaire d'actualiser et de revalider l'identité de l'utilisateur de remplacement à l'aide du mécanisme d'authentification configuré.{0}{1} REMARQUE : La durée de l’actualisation pour la revalidation du remplacement de l'IP doit être inférieure à celle du bail du DHCP. Sinon, des stratégies d'utilisateur incorrectes peuvent être appliquées.",
    "TOOLTIP_ENABLE_SURROGATE": "Permet le mappage de l'appareil par l'utilisateur lorsque l'adresse IP interne est distincte de l'adresse IP publique. On utilise cela pour appliquer les stratégies des utilisateurs sur le trafic incompatible avec les cookies. Pour en savoir plus, consultez la page d’aide.",
    "TOOLTIP_ENABLE_XFF_FORWARDING": "Activez XFF à partir de la demande client si vous voulez que le service Zscaler utilise les en-têtes X-Forwarded-For (XFF) que votre serveur proxy sur site insère dans les demandes HTTP sortantes. Notez que lorsque le service transfère le trafic vers sa destination, il supprime cet en-tête XFF d'origine et le remplace par un en-tête XFF contenant l'adresse IP de la passerelle client (l'adresse IP publique de l'organisation). Cela garantit que les adresses IP internes d'une organisation ne sont jamais divulguées au monde extérieur.",
    "TOOLTIP_ENFORCE_AUTHENTICATION": "Activez l’authentification pour forcer l’identification du trafic utilisateur individuel en appliquant le mécanisme d’authentification utilisateur configuré.",
    "TOOLTIP_ENFORCE_BAND_WIDTH_CONTROL": "Sélectionnez Activer pour appliquer le contrôle de la bande passante à l'emplacement.",
    "TOOLTIP_ENFORCE_FIREWALL_CONTROL": "Sélectionnez Appliquer le contrôle du pare-feu pour activer le pare-feu à l'emplacement.",
    "TOOLTIP_ENTER_AWS_ACCOUNT_ID": "L'ID du compte AWS où les charges de travail sont déployées.",
    "TOOLTIP_EVENT_BUS_NAME": "Bus d'événements Eventbridge utilisé pour envoyer des notifications en temps réel au bus d'événements Zscaler. Une règle dans Eventbridge peut être utilisée pour envoyer des notifications de changement de ressources en temps réel à Zscaler. Ceci est nécessaire pour permettre des mises à jour en temps réel des stratégies. Zscaler est informé de la création d'une VM.",
    "TOOLTIP_EXTERNAL_ID": "Zscaler utilisera cet identifiant externe dans l'appel API à AWS lors de la récupération des informations sur les balises. L'identifiant externe est unique pour chaque compte créé.Cet identifiant doit être ajouté dans la configuration du rôle AWS IAM. Si vous régénérez cet identifiant, mettez-le également à jour dans le compte AWS.",
    "TOOLTIP_FAILURE_BEHAVIOR": "Veuillez sélectionner le comportement en cas d'échec.",
    "TOOLTIP_FORCE_SSL_INTERCEPTION": "Activer l'option pour que SSL Interception applique une stratégie d'utilisation acceptable pour le trafic HTTPS",
    "TOOLTIP_GATEWAY_FAIL_CLOSE": "Cette option indique comment gérer le trafic lorsque les proxy principaux et secondaires définis dans cette passerelle sont inaccessibles. L'activation de cette option entraîne une baisse du trafic tandis que sa désactivation autorise le trafic. Cette option est activée par défaut.",
    "TOOLTIP_GATEWAY_NAME": "Entrez un nom pour la passerelle à créer pour un service proxy tiers.",
    "TOOLTIP_GENERAL_DESCRIPTION": "(Facultatif) Entrez des notes ou informations supplémentaires. La description ne peut pas comporter plus de 10 240 caractères.",
    "TOOLTIP_HA_ID": "Entrez l’identifiant du périphérique HA.",
    "TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS": "Ce champ peut afficher des commentaires sur l'adresse IP entrée. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK": "Vous pouvez entrer une adresse IP pour vérifier si elle a été refusée. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS": "Le champ indique si l’adresse IP entrée a été refusée. ",
    "TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE": "Autorisez les ingénieurs de l'assistance Zscaler à se connecter à distance à votre portail d'administration avec des privilèges d'administration complets. Vous n’avez pas besoin de créer des comptes ou de partager des mots de passe pour activer l’accès.",
    "TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE": "Autorisez les employés autorisés de Zscaler à accéder à votre portail d’administration avec des privilèges de lecture seule uniquement. L’accès au portail sera utilisé pour produire du contenu et des rapports sur la réussite des clients, fournir une assistance à distance et afficher des rapports et une configuration pour aider à améliorer le produit et les services Zscaler.",
    "TOOLTIP_HELP_LOOKUP_URL_ENTER_URL": "Pour rechercher la ou les catégories auxquelles une URL appartient, tapez l’URL et cliquez sur {0}URL de recherche{1}. Le service affiche la {2}catégorie prédéfinie ou la super catégorie de l'URL{3} et indique s'il existe une alerte de sécurité associée à l'URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_SECURITY_ALERT": "Ce champ indique si une alerte de sécurité est associée à l’URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL_CLASSIFICATIONS": "Ce champ affiche la {0}catégorie prédéfinie ou supercatégorie{1} à laquelle appartient l’URL. ",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL": "Ce champ affiche l'URL que vous avez recherchée.",
    "TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL": "Nous vous recommandons une autorisation d'accès d'au moins deux jours.",
    "TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL": "Nous vous recommandons une autorisation d'accès d'au moins un an.",
    "TOOLTIP_HW_DEVICE_NAME": "Entrez un nom pour le modèle.",
    "TOOLTIP_HW_SUBINTERFACE_SHUTDOWN": "Sélectionnez Actif ou Veille comme mode de liaison montante.",
    "TOOLTIP_HW_VLAN_ID": "Entrez l’identifiant du VLAN.",
    "TOOLTIP_HW_VLAN": "Sélectionnez Balisé ou Non balisé pour le réseau local virtuel (VLAN).",
    "TOOLTIP_INTERFACE_SHUTDOWN": "Sélectionnez Oui ou Non pour décider du comportement d’arrêt de l’interface.",
    "TOOLTIP_IP_ADDRESS_RANGE": "Entrez la plage d’adresses IP de votre appareil.",
    "TOOLTIP_IP_ADDRESS_WITH_NETMASK": "Entrez une adresse IP au format a.b.c.d/masque",
    "TOOLTIP_IP_POOL_NAME": "Nom du pool d'adresses IP.",
    "TOOLTIP_LIST_CUSTOM_OPTION_CODE": "Veuillez entrer un code d'option DHCP valide tel que défini par l'IANA. Les options prédéfinies sont interdites",
    "TOOLTIP_LIST_CUSTOM_OPTION_NAME": "Veuillez entrer un nom pour cette option personnalisée - Non analysée",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_IP": "Le type de données Adresse IP doit être entré sous forme d'adresse IP explicite.  Vous pouvez définir jusqu'à quatre adresses IP, séparées par des virgules.Par ex. *************, ************",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_STRING": "Le type de chaîne de caractères correspond soit à un ASCII NVT, soit à une série d'octets exprimée en hexadécimal.\nPar ex. '********:/var/tmp/rootfs' ou bien 43:4c:49:45:54:2d:46:4f:4f",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE": "Veuillez sélectionner le type entre IP ou chaîne",
    "TOOLTIP_LIST_CUSTOM_OPTION": "Les options DHCP personnalisées vous permettent de configurer des options DHCP qui ne sont pas prédéfinies dans la liste déroulante.",
    "TOOLTIP_LIST_DNS_SERVER": "Entrez une ou plusieurs listes d’adresses IP de serveur DNS valides.",
    "TOOLTIP_LIST_DOMAIN_NAME": "Entrez un ou plusieurs noms de domaine valides.",
    "TOOLTIP_LOCATION_CREATION": "Ce champ est défini sur Automatique par défaut.",
    "TOOLTIP_LOCATION_TEMPLATE_NAME": "Entrez le nom du modèle d'emplacement que vous souhaitez ajouter.",
    "TOOLTIP_LOCATION_TEMPLATE": "Sélectionnez un modèle d'emplacement par défaut ou configuré pour votre URL de provisionnement.",
    "TOOLTIP_LOCATIONS_CUSTOM_AUP_FREQUENCY_TEXT": "Entrez, en nombre de jours, la fréquence à laquelle la stratégie d’utilisation acceptable est affichée aux utilisateurs",
    "TOOLTIP_LOCATIONS_ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Si cette option est activée et si le mappage de l'utilisateur IP existe, l'identité de l'utilisateur de remplacement est également utilisée pour le trafic provenant de navigateurs connus. Si cette option est désactivée, le trafic provenant de navigateurs connus est toujours contesté à l'aide du mécanisme d'authentification configuré et l'identité de l'utilisateur Surrogate est ignorée",
    "TOOLTIP_LOCATIONS_IDLE_TIME_DISASSOCIATION": "Si vous avez activé Remplacement d'IP, dans Idle Time to Disassociation, indiquez combien de temps après une transaction terminée le service conserve l’adresse IP au mappage utilisateur.",
    "TOOLTIP_MAX_LEASE_TIME": "Entrez la durée maximale du bail en secondes.",
    "TOOLTIP_MTU_1500": "Unité de transmission maximale (MTU) pour les octets.",
    "TOOLTIP_MTU": "Unité de transmission maximale (MTU) pour les octets. La valeur par défaut est 1400",
    "TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD": "S’ils sont activés, les tableaux de bord s’actualisent automatiquement toutes les 15 minutes.",
    "TOOLTIP_MY_PROFILE_CONFIRM_NEW_PASSWORD": "Entrez à nouveau votre nouveau mot de passe. Il doit être identique au mot de passe entré dans le champ {0}Nouveau mot de passe{1}.",
    "TOOLTIP_MY_PROFILE_LANGUAGE": "Le portail d’administration est affiché en anglais par défaut. Vous pouvez également sélectionner espagnol, français, chinois traditionnel ou japonais.",
    "TOOLTIP_MY_PROFILE_NEW_PASSWORD": "Entrez un nouveau mot de passe. Il doit comporter au moins huit caractères dont un chiffre, une lettre majuscule et un caractère spécial. Seuls les caractères ASCII sont autorisés.",
    "TOOLTIP_MY_PROFILE_OLD_PASSWORD": "Entrez votre mot de passe actuel.",
    "TOOLTIP_MY_PROFILE_PASSWORD": "Un mot de passe doit comporter au moins huit caractères dont un chiffre, une lettre majuscule et un caractère spécial. Seuls les caractères ASCII sont autorisés.",
    "TOOLTIP_MY_PROFILE_POLICY_INFORMATION": "Activez cette option pour afficher les informations sur la stratégie.",
    "TOOLTIP_MY_PROFILE_TIMEZONE": "Lorsque le service enregistre des transactions, il utilise UTC. Il utilise le fuseau horaire spécifié lorsqu'il affiche les journaux.",
    "TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME": "L’identifiant de connexion de l’administrateur attribué lors de la création du compte administrateur.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_DESCRIPTION": "(Facultatif) Entrez des notes ou informations supplémentaires. La description ne peut pas comporter plus de 10 240 caractères.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_NAME": "Entrez un nom pour le {0}groupe de services réseau{1}. Il peut comporter des caractères de votre choix et des espaces.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_SERVICES": "Choisissez le nombre de services personnalisés et prédéfinis que vous souhaitez inclure dans le groupe.",
    "TOOLTIP_NETWORK_SERVICES_DEFINITION": "Le service affiche {0}Personnalisé{1} pour indiquer qu’il s’agit d’un service défini par l’administrateur.",
    "TOOLTIP_NETWORK_SERVICES_DESCRIPTION": "(Facultatif) Entrez des notes ou informations supplémentaires. La description ne peut pas comporter plus de 10 240 caractères.",
    "TOOLTIP_NETWORK_SERVICES_NAME": "Entrez un nom pour le {0}service de couche d’application{1} que vous souhaitez contrôler. Il peut comporter des caractères de votre choix et des espaces.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_DESTINATION_PORTS": "Le numéro de port de destination SCTP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, utilisé par le service réseau.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_SOURCE_PORTS": "Le numéro de port source SCTP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, utilisé par le service réseau.",
    "TOOLTIP_NETWORK_SERVICES_TCP_DESTINATION_PORTS": "Le numéro de port de destination TCP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, utilisé par le service réseau.",
    "TOOLTIP_NETWORK_SERVICES_TCP_SOURCE_PORTS": "Le numéro de port source TCP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, qui est utilisé par le service réseau.",
    "TOOLTIP_NETWORK_SERVICES_UDP_DESTINATION_PORTS": "Le numéro de port de destination UDP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, utilisé par le service réseau.",
    "TOOLTIP_NETWORK_SERVICES_UDP_SOURCE_PORTS": "Le numéro de port source UDP (exemple : 50) ou la plage de numéros de port (exemple : 1000-1050), le cas échéant, qui est utilisé par le service réseau.",
    "TOOLTIP_NSS_CLOUD_FEED_API_URL": "L'URL HTTPS du Terminal de l'API de collecte des journaux SIEM.",
    "TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Entrez l'URL d'autorisation avec l'identifiant du répertoire (tenant) généré dans AZURE",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Entrez l'identifiant de la clé d'accès pour l'utilisateur créé dans AWS",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Entrez la clé d'accès secrète pour l'utilisateur créé dans AWS",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID": "Entrez l'identifiant Application (client) généré dans AZURE",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET": "Entrez la valeur du secret client d'application générée dans AZURE",
    "TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE": "Entrez la chaîne suivante : client_credentials",
    "TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Journaux de flux au format JSON Array (par exemple [{JSON1},{JSON2}])",
    "TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Limitez la taille de la charge utile d'une demande HTTP individuelle à la meilleure pratique du SIEM.",
    "TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Activé par défaut et non modifiable",
    "TOOLTIP_NSS_CLOUD_FEED_SCOPE": "Entrez la chaîne suivante : https://monitor.azure.com//.default",
    "TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE": "Sélectionnez votre SIEM basé sur le cloud.",
    "TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL": "Entrez l'URL du dossier créé dans le compartiment S3.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Utilisez ce filtre pour limiter les journaux à des groupes Cloud/Branch Connector spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Utilisez ce filtre pour limiter les journaux à des Cloud/Branch Connectors spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Utilisez ce filtre pour limiter les journaux sur la base de l'adresse IP privée d'un client. Vous pouvez entrer : {0}Une adresse IP, comme **************{1}Une plage d'adresses IP, comme *********-*********0{2}Une adresse IP avec un masque de réseau, comme ***********/24{3}Appuyez sur Entrée après chaque saisie.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Utilisez ce filtre pour limiter les journaux aux sessions associées à des types de requêtes DNS spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Utilisez ce filtre pour limiter les journaux aux sessions associées à des codes de réponse DNS spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Utilisez ce filtre pour limiter les journaux aux sessions associées à des codes de réponse DNS spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Limiter les journaux aux sessions contenant des données spécifiques dans les réponses DNS.Vous pouvez spécifier des noms de domaine, des adresses IPv4 et IPv6. Pour les adresses IPv4, vous pouvez entrer une adresse IP, une plage d'adresses IP ou une adresse IP avec un masque de réseau.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DOMAINS": "Utilisez ce filtre pour limiter les journaux aux sessions associées à des domaines spécifiques.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DURATIONS": "Utilisez ce filtre pour limiter les journaux en fonction de la durée des sessions, exprimée en secondes.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_LOCATIONS": "Utilisez ce filtre pour limiter les journaux à des emplacements spécifiques à partir desquels les transactions ont été générées. Vous pouvez rechercher des emplacements. Le nombre d'emplacements que vous pouvez sélectionner est illimité. Les emplacements qui sont supprimés après avoir été sélectionnés apparaissent barrés.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Limiter les journaux sur la base d'actions de stratégie DNS spécifiques",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_RULE_NAME": "Utilisez ce filtre pour limiter les journaux en fonction des règles spécifiques de la stratégie de contrôle DNS. Choisissez les règles dans la liste.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_ADDRESS": "Utilisez ce filtre pour limiter les journaux à des adresses IP de serveur spécifiques. Vous pouvez entrer : {0}Une adresse IP, comme **************{1}Une plage d'adresses IP, comme *********-*********0{2}Une adresse IP avec un masque de réseau, comme ***********/24{3}Appuyez sur Entrée après chaque saisie.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_PORTS": "Utilisez ce filtre pour limiter les journaux à des ports de serveur spécifiques. Vous pouvez saisir des ports spécifiques et une plage de ports.",
    "TOOLTIP_NSS_FEED_DUPLICATE_LOGS": "Pour vous assurer qu'aucun journal n'est ignoré pendant une période d'arrêt, spécifiez le nombre de minutes que NSS en enverra en double.",
    "TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE": "Limiter les journaux en fonction du type d'enregistrement des indicateurs",
    "TOOLTIP_NSS_FEED_ESCAPE_CHARACTER": "Vous pouvez également saisir un caractère que vous souhaitez coder en hexadécimal lorsqu'il apparaît dans l'URL, l'hôte ou le référent. Par exemple, pour avoir une virgule, (,) tapez %2C. Ceci est utile si vous utilisez ce caractère comme délimiteur et que vous souhaitez vous assurer que la délimitation ne soit pas erronée. Si un enregistrement résulte d'un codage personnalisé, il faut indiquer« OUI » dans le champ {eedone} qui le concerne.",
    "TOOLTIP_NSS_FEED_LOG_TYPE": "Choisissez le type de journaux que vous diffusez.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Utilisez ce filtre pour limiter les journaux à des Cloud/Branch Connectors spécifiques.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Utilisez ce filtre pour limiter les journaux à une VM Branch Connector spécifique.",
    "TOOLTIP_NSS_FEED_NAME": "Chaque flux est une connexion entre NSS et votre SIEM. Saisissez un nom pour le flux.",
    "TOOLTIP_NSS_FEED_OUTPUT_FORMAT": "Il s'agit des champs qui seront affichés dans la sortie. Vous pouvez modifier la liste par défaut et, si vous avez choisi Personnalisé comme type de sortie de champ, modifiez également le délimiteur. Reportez-vous à la section Format de sortie de flux NSS pour en savoir plus sur les champs disponibles et leur syntaxe.",
    "TOOLTIP_NSS_FEED_OUTPUT_TYPE": "La sortie est une liste séparée par des virgules (CSV) par défaut. Choisissez : Séparé par des tabulations pour créer une liste séparée par des tabulations, Personnalisé pour utiliser un délimiteur différent, tel qu'un tiret, et saisissez le délimiteur lorsque vous spécifiez le format de sortie du flux. Le type de sortie du flux de votre SIEM, s'il est répertorié",
    "TOOLTIP_NSS_FEED_SERVER": "Choisissez un NSS de la liste.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Utilisez ce filtre pour limiter les journaux à des groupes Cloud/Branch Connector spécifiques.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Utilisez ce filtre pour limiter les journaux à des Cloud/Branch Connectors spécifiques.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Utilisez ce filtre pour limiter les journaux sur la base de l'adresse IP privée d'un client. Vous pouvez entrer : {0}Une adresse IP, comme **************{1}Une plage d'adresses IP, comme *********-*********0{2}Une adresse IP avec un masque de réseau, comme ***********/24{3}Appuyez sur Entrée après chaque saisie.{4}",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_GATEWAY": "Utilisez ce filtre pour limiter les journaux à des passerelles spécifiques.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_LOCATIONS": "Utilisez ce filtre pour limiter les journaux à des emplacements spécifiques à partir desquels les transactions ont été générées. Vous pouvez rechercher des emplacements. Le nombre d'emplacements que vous pouvez sélectionner est illimité. Les emplacements qui sont supprimés après avoir été sélectionnés apparaissent barrés.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Sélectionnez Tous pour appliquer le flux NSS à tous les services réseau ou sélectionnez des services réseau spécifiques.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Utilisez ce filtre pour limiter les journaux en fonction de l'action entreprise par le service, conformément aux règles de la stratégie de contrôle des transferts.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME": "Utilisez ce filtre pour limiter les journaux en fonction des règles spécifiques de la stratégie de contrôle DNS. Choisissez les règles dans la liste.",
    "TOOLTIP_NSS_FEED_SESSION_LOG_TYPE": "Choisissez le type de journal de session",
    "TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE": "Sélectionnez l'adresse IP ou le FQDN comme type de destination du SIEM vers lequel les journaux sont diffusés en streaming.",
    "TOOLTIP_NSS_FEED_SIEM_FQDN": "Entrez le FQDN du SIEM vers laquelle les journaux sont diffusés en streaming. Vérifiez que le SIEM est configuré pour accepter le flux du NSS.",
    "TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS": "Entrez l'adresse IP du SIEM vers lequel les journaux sont diffusés en streaming. Vérifiez que le SIEM est configuré pour accepter le flux du NSS.",
    "TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT": "Saisissez une limite de débit appropriée pour les événements par seconde que vous voulez transmettre à votre SIEM. Une limite trop basse pour le volume de trafic entraîne une perte de journaux.",
    "TOOLTIP_NSS_FEED_SIEM_RATE": "Laissez cette option sur Sans limites, sauf si vous devez limiter le flux de sortie en raison d'une licence SIEM ou d'autres contraintes.",
    "TOOLTIP_NSS_FEED_SIEM_TCP_PORT": "Entrez le numéro de port du SIEM vers lequel les journaux sont diffusés en streaming. Vérifiez que le SIEM est configuré pour accepter le flux du NSS.",
    "TOOLTIP_NSS_FEED_STATUS": "Le flux NSS est activé par défaut. Choisissez Désactivé si vous souhaitez l'activer ultérieurement.",
    "TOOLTIP_NSS_FEED_TIMEZONE": "Il s'agit du fuseau horaire de l'organisation, par défaut. Le fuseau horaire que vous avez défini s'applique au champ horaire du fichier de sortie. Le fuseau horaire s'adapte automatiquement au passage à l'heure d'été locale. Le fuseau horaire configuré peut être affiché dans les journaux sous la forme d'un champ distinct. La liste des fuseaux horaires est dérivée de la base de données des fuseaux horaires de l'IANA. Des décalages GMT directs peuvent également être spécifiés.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Vous avez la possibilité d'indiquer le nombre d’utilisateurs.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Vous pouvez récupérer ces données en accédant au tableau de bord de l'aperçu DNS. Ceci est recommandé pour ajuster la spécification de machine virtuelle à la charge de travail de votre organisation.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR": "Vous pouvez récupérer ces données en accédant au tableau de bord Vue d’ensemble du pare-feu. Ceci est recommandé pour ajuster la spécification de machine virtuelle à la charge de travail de votre organisation.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM": "Indiquez la plate-forme utilisée pour déployer NSS.",
    "TOOLTIP_NSS_SERVER_NAME": "Entrez un nom pour le serveur NSS.",
    "TOOLTIP_NSS_SERVER_SSL_CERTIFICATE": "",
    "TOOLTIP_NSS_SERVER_STATE": "L'état de santé du serveur NSS.",
    "TOOLTIP_NSS_SERVER_STATUS": "Le NSS est activé par défaut.",
    "TOOLTIP_NSS_TYPE": "Ce champ est en lecture seule.",
    "TOOLTIP_NSS_VIRTUAL_MACHINE": "Cliquez ici pour télécharger le fichier NSS OVA.",
    "TOOLTIP_NUMBER_OF_CORES": "Nombre recommandé de cœurs pour l'hyperviseur.",
    "TOOLTIP_PASSPHRASE": "Entrez la phrase d'authentification pour l'appareil.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRATION_EC": "Activez l'option pour que les mots de passe expirent à terme lorsqu'ils sont destinées à tous les administrateurs qui se connectent aux portails d'administration ZIA, connecteur cloud et ZDX. Si cette option est désactivée, les mots de passe n'expirent jamais.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRES_AFTER": "Saisissez le nombre de jours pendant lesquels vous souhaitez que les mots de passe soient valides pour les administrateurs de ZIA et ZDX. Cela peut aller de 15 à 365.",
    "TOOLTIP_PEER_DHCP": "Entrez une adresse IP DHCP homologue.",
    "TOOLTIP_POLICY_APP_SEGMENT_GROUP": "Sélectionnez jusqu'à 600 groupes de segments à appliquer à la règle de transfert de trafic. Si aucun groupe n'est sélectionné, la règle ne s'applique à aucun groupe.",
    "TOOLTIP_POLICY_APP_SEGMENT": "Sélectionnez jusqu'à 50 000 Application Segments à appliquer à la règle de transfert de trafic. Si aucun segment n'est sélectionné, la règle ne s'applique à aucun segment.",
    "TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS": "Sélectionnez un nombre quelconque de groupes de services réseau prédéfinis ou personnalisés. Si aucun groupe de services réseau n'est sélectionné, la règle s'applique à tous les groupes de services réseau.",
    "TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES": "Entrez un caractère générique et des noms de domaine complets (FQDN). Si vous ajoutez plusieurs éléments, appuyez sur la touche Entrée après chaque entrée.",
    "TOOLTIP_POLICY_DNS_DESTINATION_GROUPS": "Sélectionnez un nombre quelconque de groupes de destination. Si aucun groupe de destination n’est sélectionné, la règle s’applique à tous les groupes de destination.",
    "TOOLTIP_POLICY_DNS_GATEWAY": "Choisissez une passerelle DNS.",
    "TOOLTIP_POLICY_DNS_RULE_ORDER": "Les règles de stratégie sont évaluées par ordre numérique croissant (règle 1 précédant la règle 2, etc.), et l'ordre des règles reflète la position de cette règle dans l'ordre.",
    "TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT": "Activez ce paramètre pour appliquer la règle de transfert de trafic à tous les App Segments existants et futurs qui sont créés. Désactivez ce paramètre pour sélectionner des Application Segments ou des Segment Groups spécifiques.",
    "TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC": "Sélectionnez jusqu’à 32 groupes. Si aucun groupe n'est sélectionné, la règle s'applique à tous les groupes",
    "TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES": "Sélectionnez un nombre quelconque de services réseau. Si aucun service réseau n'est sélectionné, la règle s'applique à tous les services réseau. Le pare-feu Zscaler dispose de services prédéfinis. Vous pouvez configurer jusqu'à 1 024 services personnalisés supplémentaires.",
    "TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC": "Choisissez parmi les options suivantes : {0}Autoriser{1} : autoriser les demandes et les réponses DNS. {2}Bloquer{3} : bloquer silencieusement toutes les demandes et réponses DNS. {4}Résolu par ZPA{5} : demande au connecteur cloud de résoudre les demandes DNS à l’aide du pool d’adresses IP. Un pool d'adresses IP doit être disponible pour pouvoir utiliser cette option.{6}Rediriger la demande{7} : Redirige toutes les demandes et réponses DNS à une passerelle DNS. Une passerelle DNS doit être disponible pour utiliser cette option.{8}",
    "TOOLTIP_POLICY_FIREWALL_DESCRIPTION": "(Facultatif) Saisissez des notes ou des informations supplémentaires. La description ne peut pas comporter plus de 10 240 caractères.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY": "Pour identifier les destinations en fonction de l’emplacement d’un serveur. Sélectionnez un nombre quelconque de pays. Si aucun pays n’est sélectionné, la règle s’applique à tous les pays.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES": "Entrez des adresses IP et des noms de domaine complets (FQDN) si le domaine possède plusieurs adresses IP de destination ou si ses adresses IP peuvent changer. Pour les adresses IP, vous pouvez entrer des adresses IP, des sous-réseaux ou des plages d’adresses individuels. Si vous ajoutez plusieurs éléments, appuyez sur la touche Entrée après chaque entrée.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS": "Sélectionnez un nombre quelconque de groupes d'adresses IP de destination. Si aucun groupe d’adresses IP de destination n’est sélectionné, la règle s’applique à tous les groupes d’adresses IP de destination",
    "TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD": "Sélectionnez l'une des méthodes de transfert suivantes pour cette règle :{0}Direct :{1} Contourne Zscaler Internet Access (ZIA) et/ou Zscaler Private Access (ZPA) et transmet directement le trafic au serveur de destination à l'aide de l'adresse IP du service Zscaler{2}Direct avec traduction SCTP :{3} Transmet directement le trafic à la destination tout en traitant en tunnel le trafic SCTP sur UDP et vice-versa{4}ZIA{5} : Transmet le trafic à ZIA via la passerelle ZIA{6}ZPA{7} : Transmet le trafic à ZPA via le cloud ZPA{8}ZPA avec traduction SCTP :{9} Transmet le trafic à Zscaler Private Access (ZPA) via le cloud ZPA tout en traitant en tunnel le trafic SCTP sur UDP et vice-versa,{0}Abandonner{1} : Rejette tous les paquets qui correspondent à la règle de transfert de trafic{2}",
    "TOOLTIP_POLICY_FIREWALL_GATEWAY": "Sélectionner une passerelle",
    "TOOLTIP_POLICY_FIREWALL_IPPOOL": "Sélectionner un pool d'adresses IP",
    "TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP": "Sélectionnez jusqu'à 32 groupes d'emplacements. Si aucun groupe d’emplacements n’est sélectionné, la règle s’applique à tous les groupes d’emplacements.",
    "TOOLTIP_POLICY_FIREWALL_LOCATION": "Sélectionnez jusqu’à 8 emplacements. Si aucun emplacement n’est sélectionné, la règle s’applique à tous les emplacements.",
    "TOOLTIP_POLICY_FIREWALL_RULE_MSFT_OFFICE_365": "La règle est créée automatiquement lorsque vous activez « Configuration en un clic recommandée par Microsoft pour Office 365 » afin d'autoriser la répartition locale pour tout le trafic Office 365 dans le produit Pare-feu cloud",
    "TOOLTIP_POLICY_FIREWALL_RULE_NAME": "Le DNS crée automatiquement un nom de règle, que vous pouvez modifier. La longueur maximale est de 31 caractères.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ORDER": "Les règles de stratégie sont évaluées par ordre numérique croissant (règle 1 avant règle 2, etc.), et l'ordre des règles reflète la position de cette règle dans l'ordre.",
    "TOOLTIP_POLICY_FIREWALL_RULE_STATUS": "Une règle activée est appliquée activement. Une règle désactivée n'est pas appliquée activement, mais elle ne perd pas sa place dans l'ordre des règles. Le service l'ignore et passe à la règle suivante.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER_WARNING": "Cette règle est créée automatiquement pour rediriger le trafic vers ZPA. On recommande de déplacer la règle DNS prédéfinie pour ZPA vers l'ordre 1 ou 2",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER": "Cette règle est créée automatiquement pour rediriger le trafic vers ZPA",
    "TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS": "Sélectionnez un nombre quelconque de groupes d’adresses IP sources. Si aucun groupe d’adresses IP sources n’est sélectionné, la règle s’applique à tous les groupes d’adresses IP sources.",
    "TOOLTIP_POLICY_FIREWALL_UCAAS": "Cette règle est créée automatiquement lorsque vous permettez à une ou à toutes les applications UCaaS d'autoriser le trafic provenant de Cloud Firewall",
    "TOOLTIP_PORT_NO": "Sélectionnez un numéro de port dans le menu déroulant.",
    "TOOLTIP_PRIMARY_DNS_SERVER": "Entrez l’adresse IP du serveur DNS principal.",
    "TOOLTIP_PRIMARY_PROXY": "Sélectionnez le proxy principal pour la passerelle.",
    "TOOLTIP_PROV_TEMPLATE_NAME": "Entrez un nom pour le modèle Provisionnement de cloud.",
    "TOOLTIP_REGION": "Sélectionnez les régions dans lesquelles vous voulez que Zscaler découvre des balises. Zscaler découvre les balises au niveau régional.",
    "TOOLTIP_SECONDARY_DNS_SERVER": "Entrez l'adresse IP du serveur DNS secondaire.",
    "TOOLTIP_SECONDARY_PROXY": "Sélectionnez le proxy secondaire pour la passerelle. Cette option est utilisée lorsque le proxy principal n'est pas accessible.",
    "TOOLTIP_SESSIONS_ACROSS_SERVICES": "Nombre total de sessions enregistrées sur tous les connecteurs cloud/succursales au cours des 24 dernières heures",
    "TOOLTIP_SHUTDOWN": "Sélectionnez Oui ou Non pour décider du comportement d’arrêt.",
    "TOOLTIP_SOURCE_IP_ADDRESSES": "Entrez un nombre quelconque d'adresses IP. Vous pouvez entrer : {0}une adresse IP (**************){1}Une plage d'adresses IP *********-*********0{2}une adresse IP avec un masque réseau ***********/24{3} Appuyez sur {4}Entrer{5} après chaque entrée.",
    "TOOLTIP_SOURCE_IP_GROUP_NAME": "Nom du groupe d'adresses IP source. Par exemple, les réseaux sociaux. Le regroupement des adresses IP sources facilite leur référencement dans les stratégies de pare-feu.",
    "TOOLTIP_SOURCE_IP_NAME": "Nom d'IP source",
    "TOOLTIP_STATIC_LEASE": "Entrez l’adresse MAC et l’adresse IP pour DHCP.",
    "TOOLTIP_STATIC_ROUTE": "Entrez les détails de la route et de la passerelle.",
    "TOOLTIP_SUBSCRIPTIONS": "Sélectionnez les abonnements que vous voulez regrouper.",
    "TOOLTIP_TEMPLATE_PREFIX": "Entrez le préfixe du nom de votre modèle d’emplacement.",
    "TOOLTIP_THROUGHPUT_ACROSS_DIRECT": "Utilisation moyenne du débit de trafic direct (en kbps) Nombre total de sessions enregistrées par connecteur cloud/succursale au cours des 24 dernières heures",
    "TOOLTIP_THROUGHPUT_ACROSS_SERVICES": "Utilisation moyenne du débit de trafic dans tous les connecteurs cloud/succursales au cours des 24 dernières heures",
    "TOOLTIP_THROUGHPUT_ACROSS_ZIA": "Utilisation moyenne du débit de trafic ZIA (en kbps) \n\nNombre total de sessions enregistrées par connecteur cloud/succursale au cours des 24 dernières heures",
    "TOOLTIP_THROUGHPUT_ACROSS_ZPA": "Utilisation moyenne du débit de trafic ZPA (en kbps) \n\nNombre total de sessions enregistrées par connecteur cloud/succursale au cours des 24 dernières heures",
    "TOOLTIP_TRAFFIC_DISTRIBUTION": "Sélectionnez Équilibré ou Meilleur lien pour déterminer la façon dont le trafic est distribué.",
    "TOOLTIP_UPGRADE_WINDOW": "Les mises à niveau seront échelonnées sans aucune incidence sur le service",
    "TOOLTIP_USE_WAN_DNS_SERVER": "Sélectionnez Oui pour utiliser le serveur DNS WAN ou Non pour saisir manuellement les détails du serveur DNS LAN. ",
    "TOOLTIP_VDI_AGENT_DESCRIPTION": "Entrez des informations descriptives pour aider à identifier l’agent.",
    "TOOLTIP_VDI_AGENT_PROFILE_NAME": "Entrez le nom du profil VDI que vous voulez ajouter.",
    "TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE": "Sélectionnez IdP ou Base de données hébergée comme type d’authentification.",
    "TOOLTIP_VDI_FORWRDING_PROFILE_IPS": "Utilisez ce filtre pour limiter les journaux sur la base de l'adresse IP privée d'un client. Vous pouvez entrer {0}une adresse IP telle que ************** {1}Une adresse IP: plage de ports telle que *********:80 ou ********* pour toutes les plages {3}Une adresse IP: protocole de ports telle que *********:80:TCP, *********:80-100:UDP ou *********:80 pour tous les protocoles {4}Cliquez sur Entrer après chaque entrée.",
    "TOOLTIP_VDI_GROUP_DESCRIPTION": "Entrez des informations descriptives permettant d'identifier le groupe et son objectif",
    "TOOLTIP_VDI_GROUP_NAME": "Le nom du groupe VDI.",
    "TOOLTIP_VDI_HOSTNAME_PREFIX": "Le préfixe du nom d'hôte à utiliser pour regrouper les appareils VDI. Il s’agit du nom d’hôte détecté par Zscaler Client Connector pour VDI.",
    "TOOLTIP_VDI_LOCATION": "L'emplacement Zscaler à associer à ce groupe VDI. Il s'agit du même emplacement que celui du cloud",
    "TOOLTIP_VDI_MTU": "Il s’agit de la valeur de l’unité de transmission maximale (MTU) pour les appareils du groupe VDI. La valeur par défaut est de 1400 octets. Si cette valeur n’est pas définie correctement, les performances réseau de l’agent VDI peuvetn s'en ressentir. Veillez à regrouper les appareils possédant les mêmes valeurs MTU dans un groupe VDI.",
    "TOOLTIP_VDI_OS_TYPE": "Le type de système d’exploitation (OS) des appareils VDI à associer à ce groupe VDI. Le type de système d’exploitation détecté par Zscaler Client Connector pour VDI sera utilisé pour ajouter un appareil au groupe.",
    "TOOLTIP_VDI_TEMPLATE_IDP_NAME": "Rechercher ou sélectionner un nom d’IdP",
    "TOOLTIP_VDI_TEMPLATE_NAME": "Donnez un nom au modèle VDI.",
    "TOOLTIP_VDI_TEMPLATE_SYSTEM_USER": "Rechercher ou sélectionner l’utilisateur système.",
    "TOOLTIP_VDI_ZPA_USER_TUNNEL_FALLBACK": "Les connecteurs créent un tunnel utilisateur vers ZPA pour chaque utilisateur VDI jusqu'au nombre maximal de tunnels utilisateur indiqué ici. Si le nombre de tunnels utilisateur VDI ZPA depuis un connecteur dépasse ce nombre, toutes les transactions suivantes sont transmises à ZPA via le tunnel associé au connecteur. L'utilisateur sera reconnu en tant que groupe de Cloud Connectors dans ce tunnel associé au connecteur.",
    "TOOLTIP_WAN_SELECTION": "La sélection WAN détermine la façon dont le trafic est transféré sur plusieurs liaisons WAN. Lorsqu’elle est réglée sur Équilibrée, le trafic est réparti de manière égale. Lorsqu'elle est réglée sur Meilleure liaison, le trafic est toujours transféré par la liaison WAN la plus performante.",
    "TOOLTIP_ZIA_TUNNEL_MODEL": "Type de cryptage à utiliser lors de la création d’un tunnel vers ZIA.",
    "TOPIC_STATUS": "Statut du sujet",
    "TOTAL_CC_DEPLOYED": "Total des connecteurs cloud déployés",
    "TOTAL_DEPLOYED": "Total déployés",
    "TOTAL_ENTITLED": "Total des ayants droit",
    "TOTAL_LATENCY": "Latence moyenne",
    "TOTAL_TRAFFIC": "Trafic total",
    "TOTAL_TRANSACTIONS": "Total des transactions",
    "TOTAL": "Total",
    "TRACE": "Traceroute",
    "TRACEROUTE_DESC": "Traceroute est un utilitaire qui indique le chemin pour accéder à un hôte spécifique.",
    "TRACEROUTE": "Traceroute",
    "TRADING_BROKARAGE_INSURANCE": "Bourse, courtage, assurances en ligne",
    "TRADITIONAL_RELIGION": "Religion traditionnelle",
    "TRAFFIC_DIRECTON": "Type de demande",
    "TRAFFIC_DISTRIBUTION": "Distribution du trafic",
    "TRAFFIC_FLOW": "Flux du trafic",
    "TRAFFIC_FORWARDING_METHOD": "Mode de transfert",
    "TRAFFIC_FORWARDING_RESOURCE": "Ressource de transfert de trafic",
    "TRAFFIC_FORWARDING": "Transfert de trafic",
    "TRAFFIC_FORWRDING_DNS": "Transfert (trafic, DNS et journaux)",
    "TRAFFIC_MONITORING": "Surveillance du trafic",
    "TRAFFIC_OVERVIEW": "Vue d'ensemble du trafic",
    "TRAFFIC_TEST": "Test du trafic",
    "TRAFFIC_TREND": "Tendance du trafic",
    "TRAFFIC_TYPE": "Type de trafic",
    "TRANSACTIONS": "Transactions",
    "TRANSLATORS": "Informatique - autres",
    "TRAVEL": "Voyages",
    "TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN": "Amérique/Port-d'Espagne",
    "TRINIDAD_AND_TOBAGO": "Trinité-et-Tobago",
    "TROUBLESHOOTING_LOGGING": "Journalisation de dépannage",
    "TRUE": "Vrai",
    "TRUSTED_ACCOUNT_ID": "Identifiant du compte de confiance",
    "TRUSTED_ROLE": "Rôle de confiance",
    "TS_DIRECTON": "TS Direction",
    "TUESDAY": "Mardi",
    "TUNISIA_AFRICA_TUNIS": "Afrique/Tunis",
    "TUNISIA": "Tunisie",
    "TUNNEL_AUTH_ALGORITHM": "Algorithme d'authentification",
    "TUNNEL_AUTH_TYPE": "Type d'authentification",
    "TUNNEL_DEAD_PEER_DETECTION": "Conserver des paquets vivants",
    "TUNNEL_DESTINATION_IP_END": "IP de destination de la stratégie P2 - Fin",
    "TUNNEL_DESTINATION_IP_START": "IP de destination de la stratégie P2 - Star",
    "TUNNEL_DESTINATION_IP": "IP de destination du tunnel",
    "TUNNEL_DESTINATION_PORT_END": "Port de destination de la stratégie P2 - Fin",
    "TUNNEL_ENCRYPTION_ALGORITHM": "Algorithme de chiffrement",
    "TUNNEL_EVENT_REASON": "Motif de l’événement",
    "TUNNEL_INFORMATION": "Informations sur le tunnel",
    "TUNNEL_INITIATOR_COOKIE": "Cookie de lancement",
    "TUNNEL_INSIGHTS": "Informations sur le tunnel",
    "TUNNEL_IP": "IP du tunnel ",
    "TUNNEL_IPSEC_PHASE2_SPI": "IKE Phase 2 SPI",
    "TUNNEL_LIFEBYTES": "Life Bytes",
    "TUNNEL_LIFETIME": "Tunnel Lifetime",
    "TUNNEL_LOG_TYPE": "TYPE DE JOURNAL",
    "TUNNEL_LOGS": "Journaux de tunnel",
    "TUNNEL_POLICY_DIRECTION": "Direction de la stratégie",
    "TUNNEL_PROTOCOL_NAME": "Protocole de stratégie P2",
    "TUNNEL_PROTOCOL": "Protocole IPSec",
    "TUNNEL_RECEIVED_PACKETS": "Paquets reçus",
    "TUNNEL_RESPONDER_COOKIE": "Cookie de répondeur",
    "TUNNEL_SENT_PACKETS": "Paquets envoyés",
    "TUNNEL_SOURCE_IP_END": "IP de source de la stratégie P2 - Fin",
    "TUNNEL_SOURCE_IP_START": "IP de source de la stratégie P2 - Début",
    "TUNNEL_SOURCE_IP": "IP de source du tunnel",
    "TUNNEL_SOURCE_PORT_START": "Port de source de la stratégie P2 - Début",
    "TUNNEL_STATUS": "État du tunnel",
    "TUNNEL_TYPE": "Type de tunnel",
    "TUNNEL_VENDOR_ID": "Identifiant du fournisseur",
    "TUNNEL_VPN_CREDENTIAL": "Informations d'identification VPN",
    "TURKEY_EUROPE_ISTANBUL": "Europe/Istanbul",
    "TURKEY": "Turquie",
    "TURKMENISTAN_ASIA_ASHGABAT": "Asie/Achgabat",
    "TURKMENISTAN": "Turkménistan",
    "TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK": "Amérique/Grand Turk",
    "TURKS_AND_CAICOS_ISLANDS": "Iles Turks-et-Caïcos",
    "TUVALU_PACIFIC_FUNAFUTI": "Pacifique/Funafuti",
    "TUVALU": "Tuvalu",
    "TWO_CLOUD_CONNECTOR_TEMPLATE": "Modèle complémentaire avec grande disponibilité",
    "TX_BYTES": "Octets envoyés",
    "TX_PACKETS": "Paquets envoyés",
    "TX_RX_BYTES": " Octets TX | RX",
    "TX_RX_PACKETS": " Paquets TX | RX",
    "TYPE_ACCOUNT_ID": "Entrer l'identifiant de compte ",
    "TYPE_ACCOUNT_NAME": "Entrer le nom de compte",
    "TYPE_APPLICATION_ID": "Entrer l’identifiant d’application",
    "TYPE_APPLICATION_KEY": "Entrer la clé d’application",
    "TYPE_AWS_ACCESS_KEY_ID": "Entrer l'identifiant de clé d’accès AWS",
    "TYPE_AWS_SECRET_ACCESS_KEY": "Entrer la clé d'accès secrète AWS",
    "TYPE_BASE_URL": "Entrer l’URL de base",
    "TYPE_CLIENT_SECRET": "Entrer la clé secrète client",
    "TYPE_DESCRIPTION_HERE": "Entrer la description ici",
    "TYPE_DOWNLOAD_MBPS": "Entrer le téléchargement (Mbps)",
    "TYPE_DSTN_IP_NAME": "Entrer le nom de l'IP de destination",
    "TYPE_FEED_ESCAPE_CHARACTER": "Saisir le texte",
    "TYPE_GATEWAY_NAME": "Entrer le nom de la passerelle ici",
    "TYPE_GROUP_NAME_HERE": "Tapez le nom du groupe ici",
    "TYPE_IP_ADDRESS_HERE": "Entrer l'adresse IP ici",
    "TYPE_IP_ADDRESSESS_HERE": "Entrer l'adresse IP",
    "TYPE_IP_POOL_NAME": "Entrer le nom du pool d’IP",
    "TYPE_KEY": "Clé de type",
    "TYPE_LOCATION_TEMPLATE_NAME": "Entrer le nom du modèle d’emplacement",
    "TYPE_NSS_CLOUD_FEED_API_URL": "Saisir l'URL de l'API",
    "TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Entrez l'URL d'autorisation",
    "TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Entrez l'identifiant de la clé d'accès",
    "TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Entrez la clé d'accès secrète",
    "TYPE_NSS_CLOUD_FEED_CLIENT_ID": "Entrez l'identifiant du client d'application",
    "TYPE_NSS_CLOUD_FEED_CLIENT_SECRET": "Entrez le secret du client d'application",
    "TYPE_NSS_CLOUD_FEED_GRANT_TYPE": "Entrez les informations d'identification du client",
    "TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Saisir le texte",
    "TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL": "Entrez l'URL du dossier S3",
    "TYPE_NSS_CLOUD_FEED_SCOPE": "Entrez https://monitor.azure.com//.default",
    "TYPE_NSS_FEED_NAME": "Saisir le texte",
    "TYPE_NSS_SERVER_NAME": "Saisir le texte",
    "TYPE_SIEM_FQDN": "Saisir le texte",
    "TYPE_SIEM_IP_ADDRESS": "Saisir le texte",
    "TYPE_SIEM_RATE_LIMIT": "Saisir le texte",
    "TYPE_SIEM_TCP_PORT": "Saisir le texte",
    "TYPE_SOURCE_IP_NAME": "Entrer le nom du groupe IP source",
    "TYPE_SUBSCRIPTION_ID": "Entrer l'identifiant d’abonnement",
    "TYPE_TEMPLATE_PREFIX": "Entrer le préfixe du modèle",
    "TYPE_TENANT_ID": "Entrer l'identifiant du locataire",
    "TYPE_UPLOAD_MBPS": "Entrer le chargement (Mbps)",
    "TYPE_VALUE": "Valeur de type",
    "TYPE_ZS_TAG_OPTIONAL": "Entrer la balise ZS (facultatif)",
    "TYPE": "Action liée à la politique",
    "UAE": "Emirats arabes unis",
    "UAECENTRAL": "(Moyen-Orient) Centre des Émirats arabes unis",
    "UAENORTH": "(Moyen-Orient) Nord des Émirats arabes unis",
    "UBUNTU_LINUX": "ubuntu LINUX",
    "UDP_ANY_DESC": "Le protocole UDP (User Datagram Protocol) est l'un des principaux éléments de la suite IP (Internet Protocol) (ensemble de protocoles réseau utilisés pour Internet).",
    "UDP_ANY": "UDP",
    "UDP_DESC": " Le protocole UDP (User Datagram Protocol) est l'un des principaux éléments de la suite IP (Internet Protocol) (ensemble de protocoles réseau utilisés pour Internet).",
    "UDP_DEST_PORTS": "Ports de destination UDP",
    "UDP_PORTS": "Ports UDP",
    "UDP_SRC_PORTS": "Ports source UDP",
    "UDP_UNKNOWN_DESC": " Cela identifie le trafic de proxy/pare-feu UDP pour lequel il est impossible de déterminer une application plus granulaire.",
    "UDP_UNKNOWN": "UDP inconnu",
    "UDP": "UDP",
    "UGANDA_AFRICA_KAMPALA": "Afrique/Kampala",
    "UGANDA": "Ouganda",
    "UK": "Royaume-Uni",
    "UKRAINE_EUROPE_KIEV": "Europe/Kiev",
    "UKRAINE_EUROPE_SIMFEROPOL": "Europe/Simferopol",
    "UKRAINE_EUROPE_UZHGOROD": "Europe/Ouhjorod",
    "UKRAINE_EUROPE_ZAPOROZHYE": "Europe/Zaporijia",
    "UKRAINE": "Ukraine",
    "UKSOUTH": "(Europe) Sud du Royaume-Uni ",
    "UKWEST": "(Europe) Ouest du Royaume-Uni",
    "UNABLE_TO_LOGIN_TRY_AGAIN": "Impossible de se connecter. Veuillez réessayer plus tard",
    "UNAUTHORIZED_COMMUNICATION": "Communication non autorisée",
    "UNENCRYPTED": "En clair",
    "UNEXPECTED_ERROR": "Une erreur inattendue s'est produite",
    "UNHEALTHY": "Malsain",
    "UNITED_ARAB_EMIRATES_ASIA_DUBAI": "Asie/Dubaï",
    "UNITED_ARAB_EMIRATES": "Emirats arabes unis",
    "UNITED_KINGDOM_EUROPE_LONDON": "Europe/Londres",
    "UNITED_KINGDOM": "Royaume-Uni",
    "UNITED_STATES_AMERICA_ADAK": "Amérique/Adak",
    "UNITED_STATES_AMERICA_ANCHORAGE": "Amérique/Anchorage",
    "UNITED_STATES_AMERICA_BOISE": "Amérique/Boise",
    "UNITED_STATES_AMERICA_CHICAGO": "Amérique/Chicago",
    "UNITED_STATES_AMERICA_DENVER": "Amérique/Denver",
    "UNITED_STATES_AMERICA_DETROIT": "Amérique/Détroit",
    "UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS": "Amérique/Indiana/Indianapolis",
    "UNITED_STATES_AMERICA_INDIANA_KNOX": "Amérique/Indiana/Knox",
    "UNITED_STATES_AMERICA_INDIANA_MARENGO": "Amérique/Indiana/Marengo",
    "UNITED_STATES_AMERICA_INDIANA_PETERSBURG": "Amérique/Indiana/Petersburg",
    "UNITED_STATES_AMERICA_INDIANA_TELL_CITY": "Amérique/Indiana/Tell City",
    "UNITED_STATES_AMERICA_INDIANA_VEVAY": "Amérique/Indiana/Vevay",
    "UNITED_STATES_AMERICA_INDIANA_VINCENNES": "Amérique/Indiana/Vincennes",
    "UNITED_STATES_AMERICA_INDIANA_WINAMAC": "Amérique/Indiana/Winamac",
    "UNITED_STATES_AMERICA_JUNEAU": "Amérique/Juneau",
    "UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE": "Amérique/Kentucky/Louisville",
    "UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO": "Amérique/Kentucky/Monticello",
    "UNITED_STATES_AMERICA_LOS_ANGELES": "Amérique/Los Angeles",
    "UNITED_STATES_AMERICA_MENOMINEE": "Amérique/Menominee",
    "UNITED_STATES_AMERICA_NEW_YORK": "Amérique/New York",
    "UNITED_STATES_AMERICA_NOME": "Amérique/Nome",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER": "Amérique/Dakota du Nord/Center",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM": "Amérique/Dakota du Nord/New Salem",
    "UNITED_STATES_AMERICA_PHOENIX": "Amérique/Phoenix",
    "UNITED_STATES_AMERICA_SHIPROCK": "Amérique/Shiprock",
    "UNITED_STATES_AMERICA_YAKUTAT": "Amérique/Yakutat",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON": "Pacifique/Johnston",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY": "Pacifique/Midway",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE": "Pacifique/Wake",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS": "Iles mineures éloignées des États-Unis",
    "UNITED_STATES_PACIFIC_HONOLULU": "Pacifique/Honolulu",
    "UNITED_STATES": "Etats-Unis",
    "UNITEDSTATES": "Etats-Unis",
    "UNITEDSTATESEUAP": "États-Unis EUAP",
    "UNITS": "Unités",
    "UNKNOWN_ERROR_CODE": "Code d'erreur inconnu",
    "UNKNOWN_HOPS": "Sauts inconnus",
    "UNLIMITED": "Illimité",
    "UNREGISTERED": "Non enregistré",
    "UNSELECTED_ITEMS": "Éléments non sélectionnés",
    "UNSELECTED": "Non sélectionné",
    "UNTAGGED": "Non balisé(e)",
    "UP": "Haut",
    "UPDATE": "Mettre à jour",
    "UPDATED": "Mis à jour",
    "UPF_IP_CIDR": "Fonction IP/CIDR de l'instance utilisateur",
    "UPF_NAME": "Nomde la fonction de l'instance utilisateur",
    "UPGRADE_ON": "Mise à niveau le",
    "UPGRADE_SCHEDULE": "Calendrier de mise à niveau",
    "UPGRADE_STATUS": "État de la mise à niveau",
    "UPGRADE_WILL_BE_SCHEDULED": "Le calendrier des mises à niveau sont établis selon le fuseau horaire local du connecteur cloud",
    "UPGRADE_WINDOW": "Fenêtre de mise à niveau",
    "UPLINK_MODE": "Mode liaison montante",
    "UPLOAD_MBPS": "Chargement (Mo/s)",
    "UPLOAD": "Charger",
    "UPTIME": "Temps d'activité",
    "URL_LOOKUP": "Recherche d'URL",
    "URUGUAY_AMERICA_MONTEVIDEO": "Amérique/Montevideo",
    "URUGUAY": "Uruguay",
    "US_CENTRAL1_A": "us-central1-a",
    "US_CENTRAL1_B": "us-central1-b",
    "US_CENTRAL1_C": "us-central1-c",
    "US_CENTRAL1_F": "us-central1-f",
    "US_CENTRAL1": "us-central1",
    "US_EAST_1": "us-east-1 (Virginie du Nord)",
    "US_EAST_1A": "us-east-1a",
    "US_EAST_1B": "us-east-1b",
    "US_EAST_1C": "us-east-1c",
    "US_EAST_1D": "us-east-1d",
    "US_EAST_1E": "us-east-1e",
    "US_EAST_1F": "us-east-1f",
    "US_EAST_2": "us-east-2 (Ohio)",
    "US_EAST_2A": "us-east-2a",
    "US_EAST_2B": "us-east-2b",
    "US_EAST_2C": "us-east-2c",
    "US_EAST1_B": "us-east1-b",
    "US_EAST1_C": "us-east1-c",
    "US_EAST1_D": "us-east1-d",
    "US_EAST1": "us-east1",
    "US_EAST4_A": "us-east4-a",
    "US_EAST4_B": "us-east4-b",
    "US_EAST4_C": "us-east4-c",
    "US_EAST4": "us-east4",
    "US_EAST5_A": "us-east5-a",
    "US_EAST5_B": "us-east5-b",
    "US_EAST5_C": "us-east5-c",
    "US_EAST5": "us-east5",
    "US_GOV_EAST_1": "AWS GovCloud (États-Unis - EST)",
    "US_GOV_EAST_1A": "us-gov-east-1a",
    "US_GOV_EAST_1B": "us-gov-east-1b",
    "US_GOV_EAST_1C": "us-gov-east-1c",
    "US_GOV_WEST_1": "AWS GovCloud (États-Unis Ouest)",
    "US_GOV_WEST_1A": "us-gov-west-1a",
    "US_GOV_WEST_1B": "us-gov-west-1b",
    "US_GOV_WEST_1C": "us-gov-west-1c",
    "US_OUTLYING_ISLANDS": "Îles périphériques des États-Unis",
    "US_SOUTH1_A": "us-south1-a",
    "US_SOUTH1_B": "us-south1-b",
    "US_SOUTH1_C": "us-south1-c",
    "US_SOUTH1": "us-south1",
    "US_VIRGIN_ISLANDS": "Îles Vierges américaines",
    "US_WEST_1": "us-west-1 (Californie du Nord)",
    "US_WEST_1A": "us-west-1a",
    "US_WEST_1B": "us-west-1b",
    "US_WEST_1C": "us-west-1c",
    "US_WEST_2_LAX_1A": "us-west-2-lax-1a",
    "US_WEST_2": "us-west-2 (Orégon)",
    "US_WEST_2A": "us-west-2a",
    "US_WEST_2B": "us-west-2b",
    "US_WEST_2C": "us-west-2c",
    "US_WEST_2D": "us-west-2d",
    "US_WEST1_A": "us-west1-a",
    "US_WEST1_B": "us-west1-b",
    "US_WEST1_C": "us-west1-c",
    "US_WEST1": "us-west1",
    "US_WEST2_A": "us-west2-a",
    "US_WEST2_B": "us-west2-b",
    "US_WEST2_C": "us-west2-c",
    "US_WEST2": "us-west2",
    "US_WEST3_A": "us-west3-a",
    "US_WEST3_B": "us-west3-b",
    "US_WEST3_C": "us-west3-c",
    "US_WEST3": "us-west3",
    "US_WEST4_A": "us-west4-a",
    "US_WEST4_B": "us-west4-b",
    "US_WEST4_C": "us-west4-c",
    "US_WEST4": "us-west4",
    "USDODCENTRAL": "US DoD Central",
    "USDODEAST": "US DoD East",
    "USE_WAN_DNS_SERVER": "Utiliser le serveur DNS WAN",
    "USE_WAN_DNS_SEVER": "Utiliser le serveur DNS WAN",
    "USER_ACCOUNT_LOCKED": "Votre compte a été verrouillé temporairement en raison d'un trop grand nombre d'échecs de connexion. Veuillez réessayez plus tard.",
    "USER_DEFINED_TAGS": "Balises définies par l'utilisateur",
    "USER_DEFINED": "Défini par l’utilisateur",
    "USER_ID": "Identifiant utilisateur",
    "USER_MANAGEMENT": "Gestion utilisateur",
    "USER_NAME_VISIBILITY": "Visibilité du nom d'utilisateur",
    "USER_NAME": "Nom d'affichage utilisateur",
    "USER_NAMES": "Noms d'utilisateur",
    "USER_PLANE_FUNCTION": "Fonction de l'instance utilisateur",
    "USER": "Utilisateur",
    "USERNAMES": "Noms d'utilisateur",
    "USGOVARIZONA": "USGov Arizona",
    "USGOVIOWA": "USGov Iowa",
    "USGOVTEXAS": "USGov Texas",
    "USGOVVIRGINIA": "USGov Virginie",
    "USSECEAST": "USSec East",
    "USSECWEST": "USSec West",
    "USSECWESTCENTRAL": "USSec West Central",
    "UZBEKISTAN_ASIA_SAMARKAND": "Asie/Samarcande",
    "UZBEKISTAN_ASIA_TASHKENT": "Asie/Tachkent",
    "UZBEKISTAN": "Ouzbékistan",
    "VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE": "Jusqu’à {0} éléments sont autorisés.",
    "VALIDATION_ERROR_EMPTY_PROTOCOL": "Veuillez entrer un protocole.",
    "VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS": "Un ou plusieurs éléments de la liste ne sont pas valides.",
    "VALIDATION_ERROR_INVALID_AWS_ROLENAME": "Veuillez entrer un nom de rôle AWS valide. Il ne doit contenir que des caractères alphanumériques et '+=,.@-' .",
    "VALIDATION_ERROR_INVALID_DOMAIN": "Entrer un nom de domaine valide.",
    "VALIDATION_ERROR_INVALID_END_PORT_RANGE": "Le numéro du port de fin doit être compris entre 1 et 65535 et supérieur à celui de début.",
    "VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS": "Entrez un FQDN, une adresse IP, une plage d'adresses IP ou un bloc CIDR IP valide.",
    "VALIDATION_ERROR_INVALID_IP_ADDRESS": "Entrez une adresse IP, une plage d'adresses IP ou un bloc CIDR IP valide.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK_IS_BROADCAST": "Veuillez entrer une adresse IP valable. Il s'agit d'une adresse IP de diffusion pour le sous-réseau.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK": "Vérifiez que les adresses IP se trouvent dans le même sous-réseau.",
    "VALIDATION_ERROR_INVALID_IP_PORT": "Veuillez entrer un port TCP valable (0 à 65535).",
    "VALIDATION_ERROR_INVALID_IP_WITH_CIDR": "Entrez une adresse IP valide avec CIDR.",
    "VALIDATION_ERROR_INVALID_IP": "Veuillez entrer une adresse IP valide",
    "VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS": "Entrez une adresse URL valide sans préfixe http:// ou https://. L’URL doit avoir au moins le modèle host.domain pour être admissible.",
    "VALIDATION_ERROR_INVALID_MAC_ADDRESS": "Veuillez entrer une adresse MAC valide.",
    "VALIDATION_ERROR_INVALID_NAME": "Veuillez entrer un nom valide",
    "VALIDATION_ERROR_INVALID_PORT_STRING": "Entrez un numéro de port ou une plage de numéros de port valide (par exemple, 587, 1-65535).",
    "VALIDATION_ERROR_INVALID_PROTOCOL": "Veuillez entrer un protocole valable.",
    "VALIDATION_ERROR_INVALID_SECONDARY_FIELD": "Le champ secondaire (manuel) ne peut pas être vide !",
    "VALIDATION_ERROR_INVALID_SERVICE_IP_MASK": "Toutes les adresses IP de service doivent avoir le même masque de sous-réseau.",
    "VALIDATION_ERROR_INVALID_START_PORT_RANGE": "Les numéros de port doivent être compris entre 1 et 65535.",
    "VALIDATION_ERROR_INVALID_URL": "Entrez une adresse URL valide avec préfixe http:// ou https://.",
    "VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE": "Cette adresse IP se trouve dans l’une des plages non valides suivantes. \n\n [0.0.0.0-*************]\n [**********-***************]\n [*********-***************]\n [***********-***************]\n [240.0.0.0-***************] ",
    "VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE": "Ce nombre doit être compris entre 128 Ko et 1 Mo.",
    "VALIDATION_ERROR_SAME_IP": "Les adresses IP doivent être différentes.",
    "VALIDATION_ERROR_SAME_SERVICE_IP": "Les adresses IP de service doivent être différentes.",
    "VALIDATION_ERROR_SAME_START_END_PORT_RANGE": "Les ports de début et de fin ne peuvent pas être identiques.",
    "VALIDATION_NETWORK_SERVICE_GROUP_NAME_REQUIRED": "Le groupe de services réseau doit porter un nom.",
    "VALIDATION_NETWORK_SERVICE_GROUP_SERIVCE_REQUIRED": "Au moins un type de service doit être spécifié.",
    "VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT": "Entrez au moins un port de destination TCP ou UDP. Les ports source nécessitent un port de destination correspondant.",
    "VALIDATION_NETWORK_SERVICE_MIN_PORT_REQUIRED": "Au moins un type de port doit être spécifié.",
    "VALIDATION_NETWORK_SERVICE_NAME_REQUIRED": "Le service réseau doit porter un nom.",
    "VALUE": "Valeur",
    "VANUATU_PACIFIC_EFATE": "Pacifique/Efaté",
    "VANUATU": "Vanuatu",
    "VATICAN_CITY_STATE_EUROPE_VATICAN": "Europe/Vatican",
    "VATICAN_CITY_STATE": "Saint-Siège",
    "VATICAN_CITY": "Cité du Vatican",
    "VDI_AGENT_APP": "Magasin d’applications VDI",
    "VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT": "Texte succinct de créneau réservé détaillé...",
    "VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT": "Ce paramètre est utilisé par Zscaler Client Connector pour VDI afin d’inclure ou d’exclure le trafic du tunnel vers le Cloud Connector ou le Branch Connector. Dans certains cas, des listes d’inclusion et d’exclusion doivent également être configurées dans votre Cloud Connector ou Branch Connector.",
    "VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT": "Entrez le texte séparé par un retour à la ligne.",
    "VDI_AGENT_FORWARDING_PROFILE": "Profil de transfert VDI",
    "VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT": "Cette configuration est utilisée pour l’authentification de l’utilisateur final et l’identifiant utilisateur associé à l’utilisateur système.",
    "VDI_AGENT_TEMPLATE": "Modèle VDI",
    "VDI_AGENT_TEMPLATES": "Modèles VDI",
    "VDI_DEVICE_GENERAL_TEXT": "Entrez des informations qui vous aideront à identifier un groupe spécifique.",
    "VDI_DEVICE_MANAGEMENT": "Gestion de périphérique VDI",
    "VDI_DEVICES": "Appareils VDI",
    "VDI_FORWARDING_PROFILE_TEXT": "Le profil de transfert à associer à ce groupe VDI. Les politiques d’inclusion ou d’exclusion du profil de transfert choisies seront appliquées par Zscaler Client Connector pour VDI et installées sur les appareils inclus dans ce groupe VDI.",
    "VDI_FORWARDING_PROFILE": "Profil de transfert VDI",
    "VDI_GROUPS": "Groupes VDI",
    "VDI_MANAGEMENT": "Gestion VDI",
    "VDI_REVIEW_TEXT": "Assurez-vous que toutes les informations ci-dessous sont correctes avant d'ajouter ce groupe.",
    "VDI_ZPA_USER_TUNNEL_FALLBACK": "Retrait de tunnel utilisateur VDI ZPA",
    "VDO_LIVE_DESC": "VDOLive est une technologie de streaming vidéo évolutive.",
    "VDO_LIVE": "VDOLive",
    "VEHICLES": "Véhicules",
    "VENDOR": "Fournisseur",
    "VENEZUELA_AMERICA_CARACAS": "Amérique/Caracas",
    "VENEZUELA": "Venezuela",
    "VERBOSE": "Détaillé",
    "VERIFY_CURRENT_PASSWORD": "Vérifier le mot de passe actuel",
    "VERSION_PROFILE": "Profil de version",
    "VERSION": "Version",
    "VIET_NAM_ASIA_SAIGON": "Asie/Hô-Chi-Minh-Ville",
    "VIET_NAM": "Viet_nam",
    "VIETNAM": "Viet Nam",
    "VIEW_5G_DEPLOYMENT": "Afficher la configuration de déploiement",
    "VIEW_APPLIANCE": "Afficher l'appliance",
    "VIEW_AWS_ACCOUNT": "Voir le compte AWS",
    "VIEW_AWS_GROUP": "Voir le groupe AWS",
    "VIEW_AZURE_TENANT": "Afficher le locataire Azure",
    "VIEW_BRANCH_PROVISIONING_TEMPLATE": "Afficher le modèle de provisionnement des connecteurs de succursales",
    "VIEW_CLOUD_CONNECTOR_ADMIN": "Afficher l’administrateur du connecteur cloud",
    "VIEW_CLOUD_CONNECTOR_ROLE": "Afficher le rôle du connecteur cloud",
    "VIEW_CLOUD_CONNECTORS": "Afficher les connecteurs",
    "VIEW_CLOUD_NSS_FEED": "Afficher le flux NSS cloud",
    "VIEW_CLOUD_PROVIDER_AWS": "Afficher le compte cloud AWS",
    "VIEW_CLOUD_PROVIDER_AZURE": "Afficher le compte Azure Cloud",
    "VIEW_CONNECTORS": "Afficher les connecteurs",
    "VIEW_DESTINATION_IP_GROUP": "Afficher le groupe d'adresses IP de destination",
    "VIEW_DNS_GATEWAYS": "Voir la passerelle DNS",
    "VIEW_DNS_POLICIES": "Afficher la règle de filtrage DNS",
    "VIEW_DYNAMIC_VDI_GROUP": "Voir le groupe VDI dynamique",
    "VIEW_GATEWAYS": "Afficher les passerelles",
    "VIEW_INFO": "Voir les infos",
    "VIEW_IP_POOL_GROUP": "Afficher le pool IP",
    "VIEW_LOCATION_TEMPLATE": "Afficher le modèle d’emplacement",
    "VIEW_LOCATIONS": "Afficher les emplacements",
    "VIEW_LOG_AND_CONTROL_FORWARDING_RULE": "Afficher le journal et la règle de transfert de contrôle",
    "VIEW_NETWORK_SERVICE_GROUP": "Afficher le groupe de services réseau",
    "VIEW_NETWORK_SERVICE": "Afficher le service réseau",
    "VIEW_NSS_FEEDS": "Afficher le flux NSS",
    "VIEW_NSS_SERVER": "Afficher le serveur NSS",
    "VIEW_ONLY_ACCESS": "Accès en lecture seule",
    "VIEW_ONLY_ENABLED_UNTIL": "Accès en lecture seule jusqu'à",
    "VIEW_ONLY": "À lecture seule",
    "VIEW_PHYSICAL_BRANCH_DEVICE": "Voir l'appareil de site physique distant",
    "VIEW_PROVISIONING_TEMPLATE": "Afficher le modèle de provisionnement des connecteurs cloud",
    "VIEW_SOURCE_IP_GROUP": "Afficher le groupe d’IP source",
    "VIEW_SUB_LOCATIONS": "Voir les sous-emplacements",
    "VIEW_SUBLOCATIONS": "Voir les sous-emplacements",
    "VIEW_TRAFFIC_FWD_POLICIES": "Afficher les règles de transfert de trafic",
    "VIEW_UPF": "Afficher la fonction de l'instance utilisateur",
    "VIEW_VDI_AGENT_FORWARDING_PROFILE": "Voir le profil de transfert VDI",
    "VIEW_VDI_TEMPLATE": "Voir le modèle VDI",
    "VIEW_VIRTUAL_BRANCH_DEVICE": "Voir l'appareil du site distant virtuel",
    "VIEW_ZERO_TRUST_GATEWAY": "Afficher la passerelle Zero Trust",
    "VIEW_ZT_DEVICE": "Afficher l'appareil ZT",
    "VIEW": "Afficher",
    "VIOLENCE": "Violence",
    "VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA": "Amérique/Tortola",
    "VIRGIN_ISLANDS_BRITISH": "Iles Vierges britanniques",
    "VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS": "Amérique/Saint- Thomas",
    "VIRGIN_ISLANDS_US": "Iles Vierges des Etats-Unis",
    "VIRTUAL_IP_ADDRESS": "Adresse IP virtuelle",
    "VIRTUAL_IP_AND_LAN_IP_MUST_BE_DIFFERENT": "L'adresse IP virtuelle et l'adresse IP LAN doivent être différentes.",
    "VIRTUAL_IP_AND_PEER_DHCP_MUST_BE_DIFFERENT": "L'adresse IP virtuelle et l'adresse DHCP de l'homologue doivent être différentes.",
    "VIRTUAL_SERVICE_EDGE_ID": "Identifiant Virtual Service Edge",
    "VIRTUAL": "Virtuel",
    "VISIBLE": "Visible",
    "VLAN_ID": "Identifiant VLAN",
    "VM_GBL_METRICS": "VM",
    "VM_HEALTH_FETCH_API_ERROR": "Impossible de récupérer VM Health pour le moment. Veuillez réessayer plus tard.",
    "VM_ID": "ID VM",
    "VM_NAME": "Nom de la MV",
    "VM_SIZE": "Taille de la machine virtuelle",
    "VMWARE_ESXI": "VMware ESXi",
    "VMWARE": "VMWare",
    "VNC_DESC": "Virtual Network Computing",
    "VNC": "VNC",
    "VPC_ID": "ID VPC",
    "VPC_NAME": "Nom du VPC",
    "VPC_VNET_NAME": "Nom du VPC/VNET",
    "VPN_CREDENTIAL_DROPDOWN": "Liste déroulante de l'accréditation VPN",
    "VPN_CREDENTIAL": "Informations d'identification VPN",
    "VPN_CREDENTIALS": "Informations d'identification VPN",
    "VSE_CLUSTERS": "Clusters de services en périphérie virtuels",
    "VSE_NODES": "Services en périphérie virtuels",
    "WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS": "Pacifique/Wallis",
    "WALLIS_AND_FUTUNA_ISLANDS": "Wallis-et-Futuna",
    "WALLIS_AND_FUTUNA": "Wallis-et-Futuna",
    "WAN_DESTINATIONS_GROUP": "Groupe de destinations WAN",
    "WAN_NEED_AT_LEAST_ONE_ACTIVE_INTERFACE": "Le WAN a besoin d’au moins une interface active.",
    "WAN_PRI_DNS": "Serveur DNS principal du WAN",
    "WAN_SEC_DNS": "Serveur DNS secondaire du WAN",
    "WAN_SELECTION": "Sélection de WAN",
    "WAN": "WAN",
    "WEAPONS_AND_BOMBS": "Armes/bombes",
    "WEB_BANNERS": "Publicité",
    "WEB_CONFERENCING": "Conférences Web",
    "WEB_HOST": "Hôte Web",
    "WEB_SEARCH": "WebSearch",
    "WEB_SPAM": "Webspam",
    "WEDNESDAY": "Mercredi",
    "WESTCENTRALUS": "(États-Unis) Centre des États-Unis",
    "WESTERN_SAHARA_AFRICA_EL_AAIUN": "Afrique/Laâyoune",
    "WESTERN_SAHARA": "Sahara occidental",
    "WESTEUROPE": "(Europe) Europe de l’Ouest",
    "WESTINDIA": "(Asie-Pacifique) Oust de l'Inde",
    "WESTUS": "(États-Unis) Ouest des États-Unis",
    "WESTUS2": "(États-Unis) Ouest des États-Unis 2",
    "WESTUS2STAGE": "(États-Unis) Ouest des États-Unis 2 (Étape)",
    "WESTUS3": "(États-Unis) Ouest des États-Unis 3",
    "WESTUSSTAGE": "(États-Unis) Ouest des États-Unis (Stage)",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_PLACEHOLDER": "Entrer l'identifiant du Client/de l'Application",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_TOOLTIP": "Entrer l'identifiant du client/de l'application",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS": "Identifiant du client/de l'application",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_PLACEHOLDER": "Entrer le Secret du Client/de l'Application",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_TOOLTIP": "Entrer le secret du client/de l'application",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET": "Secret du Client/de l'Application",
    "WHAT_DO_YOU_CALL_THIS_TENANT_PLACEHOLDER": "Entrer un nom pour le compte Azure",
    "WHAT_DO_YOU_CALL_THIS_TENANT_TOOLTIP": "Entrez un nom utilisé pour gérer vos comptes.",
    "WHAT_DO_YOU_CALL_THIS_TENANT": "Nom du compte",
    "WHAT_IS_THE_DIRECTORY_ID_PLACEHOLDER": "Entrer l'identifiant du répertoire",
    "WHAT_IS_THE_DIRECTORY_ID_TOOLTIP": "Entrez l'identifiant du répertoire (locataire) associé au principal de service Azure utilisé pour intégrer le compte Azure.",
    "WHAT_IS_THE_DIRECTORY_ID": "Identifiant du répertoire (locataire)",
    "WHITESPACES_ARE_NOT_ALLOWED": "Les whitespaces ne sont pas autorisés !",
    "WHOIS_DESC": "Network Directory Service Protocol",
    "WHOIS": "QUI EST",
    "WINDOWS_OS": "Système d'exploitation Windows",
    "WORKLOAD_SERVICE_REQUIRED_MESSAGE": "Les intégrations partenaires exigent un abonnement que votre entreprise ne possède pas actuellement. Contactez l'assistance Zscaler pour activer le plan de base sans frais supplémentaires. Les intégrations partenaires permettent à un service de fonctionner et d'être maintenu par Zscaler dans le cloud public, rattaché à votre compte ou à votre abonnement. Zscaler attribuera votre compte à un service en fonction de notre capacité.",
    "WORKLOAD_SERVICE_REQUIRED": "Le service de charge de travail  doit être configuré",
    "WORKLOAD": "Type de trafic de charges de travail",
    "WORKLOADS": "Charges de travail",
    "XSS": "Exécution de scripts de site à site",
    "YEMEN_ASIA_ADEN": "Asie/Aden",
    "YEMEN": "Yémen",
    "YES": "Oui",
    "YESKY_DESC": " Ce plug-in de protocole classifie le trafic HTTP destiné à l'hôte yesky.com.",
    "YESKY": "Yesky",
    "YIHAODIAN_DESC": " Boutique chinoise en ligne",
    "YIHAODIAN": "Yihaodian",
    "YMAIL_CLASSIC_DESC": " Yahoo Mail Classic était l'interface d'origine de Yahoo! Mail",
    "YMAIL_CLASSIC": "Yahoo Mail Classic",
    "YMAIL_MOBILE_DESC": " Yahoo Mail (Mobile) (abandonné) est le service de messagerie yahoo.com pour téléphones mobiles.",
    "YMAIL_MOBILE_NEW_DESC": " Yahoo Mail (Mobile) est le nouveau webmail yahoo.com adapté aux mobiles",
    "YMAIL_MOBILE_NEW": "Yahoo mail (Mobile)",
    "YMAIL_MOBILE": "Ymail (Mobile)",
    "YMAIL2_DESC": " Ce protocole est la version ajax de Webmail Yahoo",
    "YMAIL2": "Ymail2",
    "YMSG_CONF_DESC": " Ce protocole permet de signaler une participation à une conférence.",
    "YMSG_CONF": "Yahoo Messenger Conference",
    "YMSG_DESC": " Yahoo Messenger est utilisé par l'application Yahoo Instant Messenger afin que les utilisateurs s'échangent des messages instantanés, des fichiers et des e-mails.",
    "YMSG_TRANSFER_DESC": " Ce protocole permet de transférer des fichiers sur ymsg.",
    "YMSG_TRANSFER": "Yahoo Messenger File Transfer",
    "YMSG_VIDEO_DESC": " (versions antérieures à 10.0.0.270) Ce protocole est utilisé par Yahoo Messenger pour les conversations vidéo",
    "YMSG_VIDEO": "Yahoo Messenger Video",
    "YMSG_WEBMESSENGER_DESC": " Yahoo Messenger pour le Web",
    "YMSG_WEBMESSENGER": "Yahoo Messenger pour le Web",
    "YMSG": "Yahoo Messenger",
    "YOU_DO_NOT_HAVE_THE_NECESSARY_PERMISSION": "Vous n'avez pas les autorisations nécessaires pour consulter cette page",
    "YOUR_ACCOUNT_INFORMATION_WAS_SAVED_BUT_SOME_REGIONS_FAILED": "Les informations relatives à votre compte ont été enregistrées, mais les régions suivantes n'ont pas été sauvegardées :",
    "ZAMBIA_AFRICA_LUSAKA": "Afrique/Lusaka",
    "ZAMBIA": "Zambie",
    "ZDX_UI": "IU ZDX",
    "ZERO_TRUST_GATEWAY": "Passerelle Zero Trust",
    "ZIA_GATEWAY": "Passerelle ZIA",
    "ZIA_GW_AUTH_FAIL": "Impossible de s’authentifier auprès de la passerelle ZIA.",
    "ZIA_GW_CONN_SETUP_FAIL": "Échec de la configuration de la connexion à la passerelle ZIA (erreur interne).",
    "ZIA_GW_CONNECT_FAIL": "Échec de la connexion à la passerelle de ZIA (erreur réseau).",
    "ZIA_GW_CTL_CONN_CLOSE": "Fermeture de la connexion de contrôle actif de la passerelle ZIA",
    "ZIA_GW_CTL_KA_FAIL": "Échec de la connexion de contrôle de la passerelle ZIA keepalive.",
    "ZIA_GW_DATA_CONN_CLOSE": "Fermeture de la connexion de données active de la passerelle ZIA.",
    "ZIA_GW_DATA_KA_FAIL": "Échec de la la connexion aux données keepalive de la passerelle ZIA.",
    "ZIA_GW_DNS_RESOLVE_FAIL": "Échec de la résolution DNS de la passerelle ZIA.",
    "ZIA_GW_PAC_RESOLVE_FAIL": "Échec de la résolution PAC de la passerelle ZIA.",
    "ZIA_GW_PAC_RESOLVE_NOIP": "La résolution PAC de la passerelle ZIA n'a renvoyé aucun IPS.",
    "ZIA_GW_PROTO_MSG_ERROR": "Erreur de format de message dans la voie de contrôle/données GW ZIA.",
    "ZIA_GW_PROTO_VER_ERROR": "Incompatibilité de version du protocole ZIA.",
    "ZIA_GW_SSL_ERROR": "Erreur SSL dans la voie de contrôle/données GW ZIA.",
    "ZIA_GW_UNHEALTHY": "L'intégrité de la passerelle ZIA n'est pas bonne (état transitoire).",
    "ZIA_THROUGHPUT_KBPS_SESSION": "ZIA (Débit kbps / Session)",
    "ZIA_TUNNEL_MODEL": "Mode tunnel ZIA",
    "ZIA_TUNNEL": "Tunnel ZIA",
    "ZIA": "ZIA",
    "ZIMBABWE_AFRICA_HARARE": "Afrique/Harare",
    "ZIMBABWE": "Zimbabwe",
    "ZONE": "Zone",
    "ZPA_BROKER": "Passerelle ZPA",
    "ZPA_EDGE_APP_SEGMENT": "Segment d’application ZPA périphérie",
    "ZPA_IP_POOL": "Pool IP ZIA",
    "ZPA_POLICY_VIOLATION_INDICATOR": "Indicateur de violation de la stratégie ZPA",
    "ZPA_THROUGHPUT_KBPS_SESSION": "ZPA (Débit kbps/session)",
    "ZPA_TUNNEL": "Tunnel ZPA",
    "ZPA": "ZPA",
    "ZS_TAG_OPTIONAL": "Balise ZS (facultatif)",
    "ZSCALER_ANALYZER": "Analyseur zscaler",
    "ZSCALER_CLOUD_ENDPOINTS": "Terminaux Cloud Zscaler",
    "ZSCALER_DOMAINS": "Domaines Zscaler",
    "ZSCALER_ESTABLISH_SUPPORT_TUNNEL": "Tunnel de support à la demande lancé par Zscaler",
    "ZSCALER_GATEWAY_DETAILS": "Détails de la passerelle Zscaler",
    "ZSCALER_HELP_PORTAL": "Portail d'aide Zscaler",
    "ZSCALER_INC_ALL_RIGHTS_RESERVED": "Zscaler inc. Tous droits réservés.",
    "ZSCALER_INTERFACE_NAME": "Nom de l’interface Zscaler",
    "ZSCALER_IP": "IP Zscaler",
    "ZSCALER_IPS": "Adresses IP Zscaler",
    "ZSCALER_PROXY_NW_SERVICES_DESC": "Ce service réseau inclut tous les ports proxy Web spécifiques à Zscaler, y compris les ports DPPC spécifiques au client.",
    "ZSCALER_PROXY_NW_SERVICES": "Services réseau proxy Zscaler",
    "ZSLOGIN_ADMINISTRATION": "Administration ZIdentity",
    "ZSPROXY_IPS": "IP proxy Zscaler",
    "ZT_DEVICES": "Appareils ZT",
    "ZT_GATEWAY": "Passerelle Zero Trust",
    "ZTG_ACCOUNT_TEXT": "La passerelle accepte les demandes des terminaux entrants provenant de la liste des comptes saisis. Vous pouvez sélectionner les comptes AWS et les groupes de comptes intégrés sur la page des intégrations partenaires. Pour les comptes qui n'ont pas été intégrés via la page des intégrations partenaires, entrez manuellement l'identifiant de compte AWS à 12 chiffres. Pour en savoir plus sur les intégrations de partenaires, consultez la {0}documentation Intégrations partenaires du Cloud Connector{1}.",
    "ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP": "Si votre compte AWS n'a pas été intégré via la page des intégrations partenaires, entrez votre identifiant de compte AWS à 12 chiffres. La passerelle accepte les demandes émanant de terminaux situés dans la même région qu'elle.",
    "ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP": "Sélectionnez les groupes de comptes AWS autorisés à se connecter à cette passerelle. La passerelle accepte les demandes émanant de terminaux situés dans la même région qu'elle. Les groupes de comptes sont créés sur la page des intégrations partenaires.",
    "ZTG_ALLOWED_ACCOUNTS_TOOLTIP": "Sélectionnez les comptes AWS autorisés à se connecter à cette passerelle. La passerelle accepte les demandes émanant de terminaux situés dans la même région qu'elle. Les comptes sont ajoutés sur la page des intégrations partenaires.",
    "ZTG_AVIABILITY_ZONE_TOOLTIP": "Sélectionnez la ou les zones de disponibilité où les composants de la passerelle sont créés. Il faut choisir au moins deux zones de disponibilité. Notez qu'il s'agit des identifiants de zones de disponibilité AWS et non pas des noms affichés dans le compte AWS. Les identifiants sont affichés, car le même nom de zone de disponibilité peut correspondre à différents identifiants de zones de disponibilité dans des comptes AWS distincts. Pour en savoir plus sur la correspondance des noms de zones de disponibilité avec les identifiants et trouver l'identifiant de zone de disponibilité pour votre compte, consultez la {0}documentation AWS{1}.",
    "ZTG_CONFIGURATION_TEXT": "Entrer la configuration de cette passerelle. La configuration détermine la région AWS et les zones de disponibilité où le service est disponible.",
    "ZTG_ID": "Identifiant de la passerelle Zero Trust",
    "ZTG_LOCATION_TEMPLATE_TOOLTIP": "Sélectionnez le modèle d'emplacement utilisé pour la création de celui associé à la passerelle.",
    "ZTG_LOCATION_TOOLTIP": "Entrez le nom de l'emplacement à associer à cette passerelle. Ce nom est visible dans toutes les politiques où un objet emplacement est disponible.",
    "ZTG_NAME_TOOLTIP": "Entrez un nom pour votre passerelle. Ce nom est associé à la passerelle.",
    "ZTG_REGION_TOOLTIP": "Sélectionnez la région où la passerelle est créée. Les passerelles sont régionales et ne peuvent être déployées que dans une seule région. La région ne peut pas être modifiée après la création de la passerelle.",
    "ZTG_REVIEW_TEXT": "Veillez à ce que toutes les informations sont correctes avant de créer la passerelle. Une fois la passerelle créée, le déploiement des composants peut prendre quelques minutes. Consultez la page de la passerelle Zero Trust pour voir son état.",
    "ZTGW_GROUP": "Groupe de passerelles Zero Trust",
    "ZTGW_VM": "VM de passerelles Zero Trust",
    "ZULU": "Zulu",
  },
};
