/* eslint-disable quotes */
/* eslint-disable quote-props */

// Notes: We can add phrases as a key, it does not have to be a key/constant.
// However, use key and constant if the phrase is a long sentence or paragraph.

// Please keep the translations below sorted for easy access and to avoid duplicates

// WIP - will keep this changes until we complete the csv2json conversion

export default {
  translation: {
    "1_HOUR": "1 Hour",
    "1_MONTH": "1 Month",
    "1_WEEK": "1 Week",
    "24_HOURS": "24 Hour",
    "4_HOURS": "4 Hours",
    "ACCEPT": "Accept",
    "ACCEPTED_ON": "Accepted On",
    "ACCESS_TOKEN": "Access Token",
    "ACCOUNT_DETAILS": "Account Details",
    "ACCOUNT_GROUP": "Account Group",
    "ACCOUNT_ID_ONLY": "Account ID",
    "ACCOUNT_ID": "AWS Account ID",
    "ACCOUNT_LIST": "Account List",
    "ACCOUNT_NAME": "Account Name",
    "ACCOUNT": "Account",
    "ACCOUNTS": "Accounts",
    "ACTION_CAN_NOT_BE_UNDONE": "This action cannot be undone.",
    "ACTION_CAPS": "ACTION",
    "ACTION_INTERFACE": "Interface",
    "ACTION_RESULT": "Result",
    "ACTION_TYPE": "Action",
    "ACTION": "Action",
    "ACTIONS_NOT_ALLOWED_ON_ZTG": "Actions not allowed on Zero-trust gateway",
    "ACTIONS": "Actions",
    "ACTIVATE": "Activate",
    "ACTIVATION_FAILED": "Activation Failed!",
    "ACTIVATION": "Activation",
    "ACTIVE_ACTIVE": "Active-Active",
    "ACTIVE_CONNECTION": "Active Connection",
    "ACTIVE_STANDBY": "Active-Standby",
    "ACTIVE_STATUS": "Active Status",
    "ACTIVE": "Active",
    "ADD_5G_DEPLOYMENT": "Add Deployment Configuration",
    "ADD_ACCOUNT": "Add Account",
    "ADD_API_KEY": "Add Cloud Service API Key",
    "ADD_AWS_ACCOUNT": "Add AWS Account",
    "ADD_AWS_CLOUD_ACCOUNT": "Add AWS Cloud Account",
    "ADD_AWS_GROUP": "Add AWS Group",
    "ADD_AZURE_ACCOUNT": "Add Azure Account",
    "ADD_AZURE_CLOUD_ACCOUNT": "Add Azure Cloud Account",
    "ADD_BC_PROVISIONING_TEMPLATE": "Add Branch Connector Configuration Template",
    "ADD_BRANCH_CONNECTOR_PROV_TEMPLATE": "Add Branch Connector Configuration Template",
    "ADD_CLOUD_APP_PROVIDER": "Add Cloud App Provider",
    "ADD_CLOUD_CONNECTOR_ADMIN": "Add Admin",
    "ADD_CLOUD_CONNECTOR_ROLE": "Add Admin Role",
    "ADD_CLOUD_CONNECTOR": "Add Cloud Connector",
    "ADD_CLOUD_NSS_FEED": "Add Cloud NSS Feed",
    "ADD_CLOUD_PROVISIONING_TEMPLATE": "Add Cloud Connector Provisioning Template",
    "ADD_CRITERIA": "Add Criteria",
    "ADD_DEPLOYMENT_CONFIGURATION": "Add Deployment Configuration",
    "ADD_DESTINATION_IP_GROUP": "Add Destination IP Group",
    "ADD_DNS_GATEWAY": "Add DNS Gateway",
    "ADD_DNS_POLICIES": "Add DNS Filtering Rule",
    "ADD_DYNAMIC_VDI_GROUP": "Add Dynamic VDI Group",
    "ADD_EC_NSS_CLOUD_FEED": "Add Cloud NSS Feed",
    "ADD_EC_NSS_FEED": "Add NSS Feed",
    "ADD_EC_NSS_SERVER": "Add NSS Server",
    "ADD_EVENT_GRID": "Add Event Grid",
    "ADD_FILTER": "Add Filter",
    "ADD_FILTERS": "Add Filters",
    "ADD_GCP_ACCOUNT": "Add GCP Account",
    "ADD_GROUP": "Add Group",
    "ADD_HTTP_HEADER": "Add HTTP Header",
    "ADD_INTERFACE": "Add Interface",
    "ADD_INTERNET_ACCESS_GATEWAY": "Add Internet Access Gateway",
    "ADD_IP_INFO": "Add IP Info",
    "ADD_IP_POOL": "Add IP Pool",
    "ADD_ITEMS": "Add Items",
    "ADD_LOCATION_AND_CCG": "Add Location and Cloud Connectors",
    "ADD_LOCATION_TEMPLATE": "Add Location Template",
    "ADD_LOG_AND_CONTROL_FORWARDING_RULE": "Add Log and Control Forwarding Rule",
    "ADD_LOG_AND_CONTROL_FORWARDING": "Add Log and Control Forwarding",
    "ADD_LOG_AND_CONTROL_GATEWAY": "Add Log And Control Gateway",
    "ADD_MORE": "Add More",
    "ADD_NETWORK_SERVICE_GROUP": "Add Network Service Group",
    "ADD_NETWORK_SERVICE": "Add Network Service",
    "ADD_NEW_GATEWAY": "Add New Gateway",
    "ADD_NEW": "Add New",
    "ADD_NSS_FEED": "Add NSS Feed",
    "ADD_NSS_SERVER": "Add NSS Server",
    "ADD_PORT": "Add Port",
    "ADD_PROVISIONING_TEMPLATE": "Add Cloud Connector Provisioning Template",
    "ADD_SOURCE_IP_GROUP": "Add Source IP Group",
    "ADD_STORAGE_ACCOUNT": "Add Storage Account",
    "ADD_SUB_INTERFACE": "Add Sub Interface",
    "ADD_TENANT": "Add Tenant",
    "ADD_TO_A_LOCATION": "Add to a location",
    "ADD_TO_AN_EXISTING_GROUP": "Add to an Existing Group",
    "ADD_TRAFFIC_FORWARDING_RULE": "Add Traffic Forwarding Rule",
    "ADD_TRAFFIC_FORWARDING": "Add Traffic Forwarding",
    "ADD_TRAFFIC_FWD_POLICIES": "Add Traffic Forwarding Rules",
    "ADD_UPF": "Add User Plane Function",
    "ADD_VDI_AGENT_FORWARDING_PROFILE": "Add VDI Forwarding Profile",
    "ADD_VDI_TEMPLATE": "Add VDI Template",
    "ADD_ZERO_TRUST_GATEWAY": "Add Zero Trust Gateway",
    "ADD_ZIA_GATEWAY": "Add ZIA Gateway",
    "ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128": "Additional AWS Accounts limit is 128.",
    "ADDITIONAL_AWS_ACCOUNTS": "Additional AWS Accounts",
    "ADDRESS_RANGES_SHOULD_NOT_OVERLAP": "Address ranges should not overlap.",
    "ADM_ACTIVATING": "Activating",
    "ADM_ACTV_DONE": "admin activate done",
    "ADM_ACTV_FAIL": "admin activate failed",
    "ADM_ACTV_QUEUED": "Activation Queued",
    "ADM_EDITING": "Editing",
    "ADM_EXPIRED": "admin session expired",
    "ADM_LOGGED_IN": "admin logged in",
    "ADMIN_ID": "Admin ID",
    "ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE": "This admin also exists in the Admin Portal for another service. It will be associated with the same admin account in the other Admin Portal, and the Zscaler service will update any changes to the other admin account, such as the email, name, scope, password, status and comments. Continue?",
    "ADMIN_MANAGEMENT": "Administrator Management",
    "ADMIN_ROLE": "Administrator Role",
    "ADMIN_SAML_PUBLICCERT_INVALID_EXTENSION": "Saml public certificate should be of .cer or .pem format only.",
    "ADMINISTRATION_CONFIGURATION": "Administration Configuration",
    "ADMINISTRATION_CONTROL": "Administration Control",
    "ADMINISTRATION": "Administration",
    "ADMINISTRATOR_ADMIN_USER": "Administrator",
    "ADMINISTRATOR_AUDITOR": "Auditor",
    "ADMINISTRATOR_MANAGEMENT": "Administrator Management",
    "ADMINISTRATOR_PASSWORD_BASED_LOGIN": "Password Based Login",
    "ADMINISTRATOR_ROLE": "Role Management",
    "ADMINISTRATOR_SAML_CONFIGURE": "SAML Authentication for Administrators",
    "ADMINISTRATOR_SAML_ENABLED": "Enable SAML Authentication",
    "ADMINISTRATOR_SAML_METADATA": "Download XML Metadata",
    "ADMINISTRATOR_SAML": "SAML",
    "ADMINISTRATORS_MANAGEMENT": "Administrators Management",
    "ADMINISTRATORS": "Administrators",
    "ADSPYWARE_SITES": "Adware/Spyware Sites",
    "ADULT_SEX_EDUCATION": "Adult Sex Education",
    "ADULT_THEMES": "Adult Themes",
    "ADVANCED_SETTINGS": "Advanced Settings",
    "ADWARE_OR_SPYWARE": "Spyware/Adware",
    "AF_SOUTH_1": "Africa (Cape Town)",
    "AF_SOUTH_1A": "af-south-1a",
    "AF_SOUTH_1B": "af-south-1b",
    "AF_SOUTH_1C": "af-south-1c",
    "AFGHANISTAN_ASIA_KABUL": "Asia/Kabul",
    "AFGHANISTAN": "Afghanistan",
    "AGENT_STATUS": "Agent Status",
    "AGGREGATE_LOGS": "Aggregate Logs",
    "AIAIGAME_DESC": " This protocol plug-in classifies the http traffic to the host aiaigame.com",
    "AIAIGAME": "AiAi Games",
    "AILI_DESC": " Chinese fashion shopping website",
    "AILI": "Aili",
    "AIM_DESC": " AIM (originally AOL Instant Messenger) is an instant messaging application. The protocol name is OSCAR (Open System for CommunicAtion in Realtime) and is used in both ICQ and AIM services",
    "AIM_EXPRESS_DESC": " AOL Instant Messaging Express supports many of the standard features included in AIM, but does not provide advanced features like file transfer, audio chat or video conferencing",
    "AIM_EXPRESS": "Aim_express",
    "AIM_TRANSFER_DESC": " AIM is an instant messaging protocol",
    "AIM_TRANSFER": "AIM File Transfer",
    "AIM": "AIM",
    "AIMEXPRESS": "AIM Express",
    "AIMINI_DESC": " Aimini is an online solution to store, send and share files",
    "AIMINI": "Aimini",
    "AIMS_DESC": " AIMS is the secure version of AIM",
    "AIMS": "AIMS",
    "AIOWRITE_THROTTLE": "aiowrite throttle. only ca-ft using it today",
    "AIRAIM_DESC": " This protocol plug-in classifies the http traffic to the host airaim.com. It also classifies the ssl traffic to the Common Name airaim.com",
    "AIRAIM": "Airaim",
    "ALAND_ISLANDS_EUROPE_MARIEHAMN": "Europe/Mariehamn",
    "ALAND_ISLANDS": "Aland Islands",
    "ALAND": "Aland",
    "ALBANIA_EUROPE_TIRANE": "Europe/Tirane",
    "ALBANIA": "Albania",
    "ALCOHOL_TOBACCO": "Alcohol/Tobacco",
    "ALERT_GCP_INFO_CHANGE": "If you change the information for this account, it might cause any associated projects to be lost.",
    "ALGERIA_AFRICA_ALGIERS": "Africa/Algiers",
    "ALGERIA": "Algeria",
    "ALL_VALUES": "All Values",
    "ALL_ZSCALER_LOCATION_GROUPS": "All Zscaler Locations Groups",
    "ALL_ZSCALER_LOCATION_TYPES": "All Zscaler Locations Types",
    "ALL_ZSCALER_LOCATIONS": "All Zscaler Locations",
    "ALL_ZSCALER_NETWORK_SERVICE": "All Zscaler Network Services",
    "ALL": "All",
    "ALLOW_TO_CREATE_NEW_LOCATION": "Allow for Creation of New Locations",
    "ALLOW": "Allow",
    "ALLOWED_ACCOUNT_GROUPS": "Allowed Account Groups",
    "ALLOWED_ACCOUNTS_GROUPS": "Allowed Accounts Groups",
    "ALLOWED_ACCOUNTS": "Allowed Accounts",
    "ALLOWED": "Allowed",
    "ALT_NEW_AGE": "Alt/New Age",
    "ALTERNATE_LIFESTYLE": "Lifestyle",
    "AMAZON_WEB_SERVICES_CONSOLE": "Amazon Web Services Console",
    "AMAZON_WEB_SERVICES": "Amazon Web Services",
    "AMERICAN_SAMOA_PACIFIC_PAGO_PAGO": "Pacific/PagoPago",
    "AMERICAN_SAMOA": "American Samoa",
    "AMF_IP_CIDR": "AMF IP/CIDR",
    "AMF_NAME": "AMF Name",
    "AMI_ID": "AMI ID",
    "ANALYTICS": "Analytics",
    "ANDORRA_EUROPE_ANDORRA": "Europe/Andorra",
    "ANDORRA": "Andorra",
    "ANDROID_OS": "Android",
    "ANGOLA_AFRICA_LUANDA": "Africa/Luanda",
    "ANGOLA": "Angola",
    "ANGUILLA_AMERICA_ANGUILLA": "America/Anguilla",
    "ANGUILLA": "Anguilla",
    "ANONYMIZER": "P2P & Anonymizer",
    "ANTARCTICA_CASEY": "Antarctica/Casey",
    "ANTARCTICA_DAVIS": "Antarctica/Davis",
    "ANTARCTICA_DUMONTDURVILLE": "Antarctica/DumontDUrville",
    "ANTARCTICA_MAWSON": "Antarctica/Mawson",
    "ANTARCTICA_MCMURDO": "Antarctica/McMurdo",
    "ANTARCTICA_PALMER": "Antarctica/Palmer",
    "ANTARCTICA_ROTHERA": "Antarctica/Rothera",
    "ANTARCTICA_SOUTH_POLE": "Antarctica/South Pole",
    "ANTARCTICA_SYOWA": "Antarctica/Syowa",
    "ANTARCTICA_VOSTOK": "Antarctica/Vostok",
    "ANTARCTICA": "Antarctica",
    "ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA": "America/Antigua",
    "ANTIGUA_AND_BARBUDA": "Antigua and Barbuda",
    "ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS": "Any non-matched IP from ZPA IP pools",
    "ANY_RULE": "Any",
    "ANY": "Any",
    "AP_EAST_1": "Asia Pacific (Hong Kong)",
    "AP_EAST_1A": "ap-east-1a",
    "AP_EAST_1B": "ap-east-1b",
    "AP_EAST_1C": "ap-east-1c",
    "AP_NORTHEAST_1": "ap-northeast-1 (Tokyo)",
    "AP_NORTHEAST_1A": "ap-northeast-1a",
    "AP_NORTHEAST_1C": "ap-northeast-1c",
    "AP_NORTHEAST_1D": "ap-northeast-1d",
    "AP_NORTHEAST_1E": "ap-northeast-1e",
    "AP_NORTHEAST_2": "ap-northeast-2 (Seoul)",
    "AP_NORTHEAST_2A": "ap-northeast-2a",
    "AP_NORTHEAST_2B": "ap-northeast-2b",
    "AP_NORTHEAST_2C": "ap-northeast-2c",
    "AP_NORTHEAST_3": "ap-northeast-3 (Osaka-Local)",
    "AP_NORTHEAST_3A": "ap-northeast-3a",
    "AP_SOUTH_1": "ap-south-1 (Mumbai)",
    "AP_SOUTH_1A": "ap-south-1a",
    "AP_SOUTH_1B": "ap-south-1b",
    "AP_SOUTH_1C": "ap-south-1c",
    "AP_SOUTH_2": "Asia Pacific (Hyderabad)",
    "AP_SOUTHEAST_1": "ap-southeast-1 (Singapore)",
    "AP_SOUTHEAST_1A": "ap-southeast-1a",
    "AP_SOUTHEAST_1B": "ap-southeast-1b",
    "AP_SOUTHEAST_1C": "ap-southeast-1c",
    "AP_SOUTHEAST_2": "ap-southeast-2 (Sydney)",
    "AP_SOUTHEAST_2A": "ap-southeast-2a",
    "AP_SOUTHEAST_2B": "ap-southeast-2b",
    "AP_SOUTHEAST_2C": "ap-southeast-2c",
    "AP_SOUTHEAST_3": "ap-southeast-3 (Jakarta)",
    "AP_SOUTHEAST_4": "Asia Pacific (Melbourne)",
    "API_KEY_MANAGEMENT": "API Key Management",
    "API_KEY": "API Key",
    "APIKEY_MANAGEMENT": "API Key Management",
    "APP_CONNECTOR_DEPLOYMENT_STATUS": "App Connector Deployment Status",
    "APP_CONNECTOR_DESCRIPTION": "App Connectors can be provisioned as part of this template. Please fill in the information below or skip to the next step.",
    "APP_CONNECTOR_GROUP_NAME": "App Connector Group Name",
    "APP_CONNECTOR_GROUP_TYPE": "App Connector Group Type",
    "APP_CONNECTOR_GROUP": "App Connector Group",
    "APP_CONNECTOR_INTERFACE": "App Connector Interface",
    "APP_CONNECTOR": "App Connector",
    "APPLIANCE_MANAGEMENT": "Appliance Management",
    "APPLIANCE_NAME": "Appliance Name",
    "APPLIANCE": "Appliance",
    "APPLIANCES": "Appliances",
    "APPLICABLE_FOR": "Applicable For",
    "APPLICATION_ID": "Application ID",
    "APPLICATION_KEY": "Application Key",
    "APPLICATION_SEGMENT": "Application Segment",
    "APPLICATION_SEGMENTS": "Application Segments",
    "APPLICATION_SERVICE_GROUPS_TOOLTIP": "Select any number of application service groups that allow you to configure traffic forwarding rules based on the predefined application service groups that Zscaler provides.",
    "APPLICATION_SERVICE_GROUPS": "Application Service Groups",
    "APPLICATION_VERSION": "Application Version",
    "APPLICATIONS": "Applications",
    "APPLY_FILTER": "Apply Filter",
    "APPLY_FILTERS": "Apply Filters",
    "APPLY_TO_ALL_APP_SEGMENTS": "Apply to all App Segments",
    "APPLY": "Apply",
    "ARE_YOU_SURE_YOU_WANT_TO_PROCEED": "Are you sure you want to proceed?",
    "ARE_YOU_SURE": "Are you sure you want to proceed?",
    "ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES": "America/Argentina/Buenos Aires",
    "ARGENTINA_AMERICA_ARGENTINA_CATAMARCA": "America/Argentina/Catamarca",
    "ARGENTINA_AMERICA_ARGENTINA_CORDOBA": "America/Argentina/Cordoba",
    "ARGENTINA_AMERICA_ARGENTINA_JUJUY": "America/Argentina/Jujuy",
    "ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA": "America/Argentina/La Rioja",
    "ARGENTINA_AMERICA_ARGENTINA_MENDOZA": "America/Argentina/Mendoza",
    "ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS": "America/Argentina/Rio Gallegos",
    "ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN": "America/Argentina/San Juan",
    "ARGENTINA_AMERICA_ARGENTINA_TUCUMAN": "America/Argentina/Tucuman",
    "ARGENTINA_AMERICA_ARGENTINA_USHUAIA": "America/Argentina/Ushuaia",
    "ARGENTINA": "Argentina",
    "ARMENIA_ASIA_YEREVAN": "Asia/Yerevan",
    "ARMENIA": "Armenia",
    "ART_CULTURE": "Art/Culture",
    "ARUBA_AMERICA_ARUBA": "America/Aruba",
    "ARUBA": "Aruba",
    "ASIA_EAST1_A": "asia-east1-a",
    "ASIA_EAST1_B": "asia-east1-b",
    "ASIA_EAST1_C": "asia-east1-c",
    "ASIA_EAST1": "asia-east1",
    "ASIA_EAST2_A": "asia-east2-a",
    "ASIA_EAST2_B": "asia-east2-b",
    "ASIA_EAST2_C": "asia-east2-c",
    "ASIA_EAST2": "asia-east2",
    "ASIA_NORTHEAST1_A": "asia-northeast1-a",
    "ASIA_NORTHEAST1_B": "asia-northeast1-b",
    "ASIA_NORTHEAST1_C": "asia-northeast1-c",
    "ASIA_NORTHEAST1": "asia-northeast1",
    "ASIA_NORTHEAST2_A": "asia-northeast2-a",
    "ASIA_NORTHEAST2_B": "asia-northeast2-b",
    "ASIA_NORTHEAST2_C": "asia-northeast2-c",
    "ASIA_NORTHEAST2": "asia-northeast2",
    "ASIA_NORTHEAST3_A": "asia-northeast3-a",
    "ASIA_NORTHEAST3_B": "asia-northeast3-b",
    "ASIA_NORTHEAST3_C": "asia-northeast3-c",
    "ASIA_NORTHEAST3": "asia-northeast3",
    "ASIA_SOUTH1_A": "asia-south1-a",
    "ASIA_SOUTH1_B": "asia-south1-b",
    "ASIA_SOUTH1_C": "asia-south1-c",
    "ASIA_SOUTH1": "asia-south1",
    "ASIA_SOUTH2_A": "asia-south2-a",
    "ASIA_SOUTH2_B": "asia-south2-b",
    "ASIA_SOUTH2_C": "asia-south2-c",
    "ASIA_SOUTH2": "asia-south2",
    "ASIA_SOUTHEAST1_A": "asia-southeast1-a",
    "ASIA_SOUTHEAST1_B": "asia-southeast1-b",
    "ASIA_SOUTHEAST1_C": "asia-southeast1-c",
    "ASIA_SOUTHEAST1": "asia-southeast1",
    "ASIA_SOUTHEAST2_A": "asia-southeast2-a",
    "ASIA_SOUTHEAST2_B": "asia-southeast2-b",
    "ASIA_SOUTHEAST2_C": "asia-southeast2-c",
    "ASIA_SOUTHEAST2": "asia-southeast2",
    "ASIA": "Asia",
    "ASIAPACIFIC": "Asia Pacific",
    "AT": "at",
    "ATTRIBUTES_APPLICABLE_ONLY_TO_KNOWN_HOPS": "These attributes are applicable only to Known Hops.",
    "ATTRIBUTES": "Attributes",
    "AUDIT_LOGS": "Audit Logs",
    "AUDIT_OPERATION": "Audit Operation",
    "AUSTRALIA_ADELAIDE": "Australia/Adelaide",
    "AUSTRALIA_BRISBANE": "Australia/Brisbane",
    "AUSTRALIA_BROKEN_HILL": "Australia/Broken Hill",
    "AUSTRALIA_CURRIE": "Australia/Currie",
    "AUSTRALIA_DARWIN": "Australia/Darwin",
    "AUSTRALIA_EUCLA": "Australia/Eucla",
    "AUSTRALIA_HOBART": "Australia/Hobart",
    "AUSTRALIA_LINDEMAN": "Australia/Lindeman",
    "AUSTRALIA_LORD_HOWE": "Australia/LordHowe",
    "AUSTRALIA_MELBOURNE": "Australia/Melbourne",
    "AUSTRALIA_NEWZEALAND": "Australia & New Zealand",
    "AUSTRALIA_PERTH": "Australia/Perth",
    "AUSTRALIA_SOUTHEAST1_A": "australia-southeast1-a",
    "AUSTRALIA_SOUTHEAST1_B": "australia-southeast1-b",
    "AUSTRALIA_SOUTHEAST1_C": "australia-southeast1-c",
    "AUSTRALIA_SOUTHEAST1": "australia-southeast1",
    "AUSTRALIA_SOUTHEAST2_A": "australia-southeast2-a",
    "AUSTRALIA_SOUTHEAST2_B": "australia-southeast2-b",
    "AUSTRALIA_SOUTHEAST2_C": "australia-southeast2-c",
    "AUSTRALIA_SOUTHEAST2": "australia-southeast2",
    "AUSTRALIA_SYDNEY": "Australia/Sydney",
    "AUSTRALIA": "Australia",
    "AUSTRALIACENTRAL": "(Asia Pacific) Australia Central",
    "AUSTRALIACENTRAL2": "(Asia Pacific) Australia Central 2",
    "AUSTRALIAEAST": "(Asia Pacific) Australia East",
    "AUSTRALIASOUTHEAST": "(Asia Pacific) Australia Southeast",
    "AUSTRIA_EUROPE_VIENNA": "Europe/Vienna",
    "AUSTRIA": "Austria",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_AUP_ENABLED": "Authentication required must be disabled when AUP is enabled.",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_CAUTION_ENABLED": "Authentication required must be disabled when Caution is enabled.",
    "AUTH_TYPE": "Auth Type",
    "AUTHENTICATION_CONFIGURATION": "Authentication Configuration",
    "AUTHENTICATION_FAILED": "Authentication failed",
    "AUTO_POPULATE_DNS_CACHE_TOOLTIP": "Enabling this option allows you to control if application service FQDNs are resolved ahead of time",
    "AUTO_POPULATE_DNS_CACHE": "Auto Populate DNS Cache",
    "AUTO_REFRESH_DASHBOARD": "Auto Refresh Dashboard",
    "AUTO_SCALING_OPTIONS": "Auto Scaling Options",
    "AUTO_SCALING": "Auto Scaling",
    "AUTO": "Auto",
    "AUTOMATIC_MANAGEMENT_IP": "Automatic Management IP",
    "AUTOMATIC_SERVICE_IP": "Automatic Service IP",
    "AUTOMATIC": "Automatic",
    "Availability Zone": "Availability Zone",
    "AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES": "Availability Zone ID must have a minimum of 2 zones.",
    "AVAILABILITY_ZONE_ID": "Availability Zone ID",
    "AVAILABILITY_ZONE": "Availability Zone",
    "AVAILABILITY_ZONES": "Availability Zones",
    "AVAILABLE": "Availble",
    "AVERAGE": "Average",
    "AWS_ACCESS_KEY_ID": "AWS Access Key ID",
    "AWS_ACCOUNT_ID": "AWS Account ID",
    "AWS_ACCOUNT": "AWS Account",
    "AWS_AVAILABILITY_ZONE": "AWS Availability Zone",
    "AWS_CLOUD_FORMATION": "AWS CloudFormation",
    "AWS_CLOUD": "AWS",
    "AWS_GROUP": "AWS Group",
    "AWS_REGION": "AWS Region",
    "AWS_REGIONS": "AWS Regions",
    "AWS_ROLE_NAME": "AWS Role Name",
    "AWS_SECRET_ACCESS_KEY": "AWS Secret Access Key",
    "AWS": "Amazon Web Services",
    "AZERBAIJAN_ASIA_BAKU": "Asia/Baku",
    "AZERBAIJAN": "Azerbaijan",
    "AZURE_ACCOUNT": "Azure Account",
    "AZURE_AVAILABILITY_ZONE": "Azure Availability Zone",
    "AZURE_CLOUD": "Azure",
    "AZURE_REGION": "Azure Region",
    "AZURE_RESOURCES": "Azure Resources",
    "AZURE_SENTINEL": "Azure Sentinel",
    "AZURE": "Azure",
    "BACK": "Back",
    "BAHAMAS_AMERICA_NASSAU": "America/Nassau",
    "BAHAMAS": "Bahamas",
    "BAHRAIN_ASIA_BAHRAIN": "Asia/Bahrain",
    "BAHRAIN": "Bahrain",
    "BAIDUYUNDNS_DESC": " DNS tunnel activity noticed on .baiduyundns.com",
    "BAIDUYUNDNS": "BaiduYunDns",
    "BALANCED_RULE": "Balanced",
    "BALANCED": "Balanced",
    "BANDWIDTH_CONTROL_ENABLED": "Sub-locations share the bandwidth value assigned to this location",
    "BANDWIDTH_CONTROL": "Bandwidth Control",
    "BANGLADESH_ASIA_DHAKA": "Asia/Dhaka",
    "BANGLADESH": "Bangladesh",
    "BARBADOS_AMERICA_BARBADOS": "America/Barbados",
    "BARBADOS": "Barbados",
    "BASE_URL": "Base URL",
    "BC_APP_CONNECTOR": "Branch Connector & App Connector",
    "BC_CONNECTOR_GROUP": "Cloud & Branch Connector Group",
    "BC_CONNECTOR_LOCATION": "Cloud & Branch Connector Location",
    "BC_CONNECTOR_VM": "Branch Connector VM",
    "BC_CONNECTOR": "Branch Connector",
    "BC_DESCRIPTION": "Branch Connector Description",
    "BC_DETAILS": "Branch Connector Details",
    "BC_DEVICE_GROUP": "Branch Connector Device Group",
    "BC_GENERAL_INFORMATION_DESCRIPTION": "Configure a Branch Connector Provisioning Template to deploy the Branch Connector as a virtual or physical device in your branch account or data center.",
    "BC_GROUP_DETAILS": "Branch Connector Group Details",
    "BC_GROUP_NAME": "Branch Connector Group Name",
    "BC_GROUP_TYPE": "Branch Connector Group Type",
    "BC_GROUP": "Branch Connector Group",
    "BC_IMAGES_DESCRIPTION": "To deploy Branch Connector or Branch Connector + App Connector using Terraform, visit {0}GitHub{1}. To learn more, see the {2}Zscaler Help Portal.{3}",
    "BC_IMAGES_DETAIL1": "Branch Connector Images include App Connectors that have not yet been provisioned. In order to deploy Branch Connector & App Connector, you must select the combined instance and configure the deployment properties accordingly.",
    "BC_IMAGES_DETAIL2": "To learn more, see {0}Deployment Management for Virtual Devices{1}.",
    "BC_VM_SIZE": "Branch Connector VM Size",
    "BELARUS_EUROPE_MINSK": "Europe/Minsk",
    "BELARUS": "Belarus",
    "BELGIUM_EUROPE_BRUSSELS": "Europe/Brussels",
    "BELGIUM": "Belgium",
    "BELIZE_AMERICA_BELIZE": "America/Belize",
    "BELIZE": "Belize",
    "BENIN_AFRICA_PORTO_NOVO": "Africa/Porto-Novo",
    "BENIN": "Benin",
    "BERMUDA_ATLANTIC_BERMUDA": "Atlantic/Bermuda",
    "BERMUDA": "Bermuda",
    "BEST_LINK": "Best Link",
    "BEST": "Best Link",
    "BESTLINK_RULE": "Best Link",
    "BHUTAN_ASIA_THIMPHU": "Asia/Thimphu",
    "BHUTAN": "Bhutan",
    "BLACKLIST_LOOKUP_RESULTS": "Denylist Lookup Results",
    "BLACKLISTED_IP_CHECK": "Denylist IP Check",
    "BLOCK_INTERNET_ACCESS": "Block Internet Access",
    "BLOCK": "Block",
    "BLOG": "Blogs",
    "BLUEJEANS": "BlueJeans",
    "BOLIVIA_AMERICA_LA_PAZ": "America/La Paz",
    "BOLIVIA": "Bolivia",
    "BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO": "Europe/Sarajevo",
    "BOSNIA_AND_HERZEGOVINA": "Bosnia and Herzegovina",
    "BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO": "Europe/Sarajevo",
    "BOSNIA_AND_HERZEGOWINA": "Bosnia and Herzegowina",
    "BOTH_REQ_RESP_ALLOW": "Allow",
    "BOTH_SESSION_AND_AGGREGATE_LOGS": "Both Session and Aggregate Logs",
    "BOTNET": "Botnet Callback",
    "BOTSWANA_AFRICA_GABORONE": "Africa/Gaborone",
    "BOTSWANA": "Botswana",
    "BRANCH_AND_CLOUD_CONNECTOR_GROUP_NAME": "Cloud & Branch Connector Group Name",
    "BRANCH_AND_CLOUD_CONNECTOR_MONITORING": "Cloud & Branch Connector Monitoring",
    "BRANCH_AND_CLOUD_CONNECTOR": "Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_MONITORING": "Cloud & Branch Monitoring",
    "BRANCH_CLOUD_CONNECTOR_GROUP": "Cloud & Branch Connector Groups",
    "BRANCH_CONFIGURATION": "Branch Configuration",
    "BRANCH_CONNECTOR_GROUP": "Branch Connector Group",
    "BRANCH_CONNECTOR_GROUPS": "Branch Connector Groups",
    "BRANCH_CONNECTOR_IMAGES": "Branch Connector Images",
    "BRANCH_CONNECTOR_INFORMATION": "Connector Information",
    "BRANCH_CONNECTOR_LOCS": "Branch Connector Locations",
    "BRANCH_CONNECTOR_MONITORING": "Branch Connector Monitoring",
    "BRANCH_CONNECTOR": "Branch Connector",
    "BRANCH_DEVICES": "Branch Devices",
    "BRANCH_MANAGEMENT": "Branch Management",
    "BRANCH_PROVISIONING": "Branch Provisioning",
    "BRANCH_TYPE": "Branch Type",
    "BRAZIL_AMERICA_ARAGUAINA": "America/Araguaina",
    "BRAZIL_AMERICA_BAHIA": "America/Bahia",
    "BRAZIL_AMERICA_BELEM": "America/Belem",
    "BRAZIL_AMERICA_BOA_VISTA": "America/Boa Vista",
    "BRAZIL_AMERICA_CAMPO_GRANDE": "America/Campo Grande",
    "BRAZIL_AMERICA_CUIABA": "America/Cuiaba",
    "BRAZIL_AMERICA_EIRUNEPE": "America/Eirunepe",
    "BRAZIL_AMERICA_FORTALEZA": "America/Fortaleza",
    "BRAZIL_AMERICA_MACEIO": "America/Maceio",
    "BRAZIL_AMERICA_MANAUS": "America/Manaus",
    "BRAZIL_AMERICA_NORONHA": "America/Noronha",
    "BRAZIL_AMERICA_PORTO_VELHO": "America/Porto Velho",
    "BRAZIL_AMERICA_RECIFE": "America/Recife",
    "BRAZIL_AMERICA_RIO_BRANCO": "America/Rio Branco",
    "BRAZIL_AMERICA_SAO_PAULO": "America/Sao Paulo",
    "BRAZIL": "Brazil",
    "BRAZILSOUTH": "(South America) Brazil South",
    "BRAZILSOUTHEAST": "(South America) Brazil Southeast",
    "BRAZILUS": "(South America) Brazil US",
    "BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS": "Indian/Chagos",
    "BRITISH_INDIAN_OCEAN_TERRITORY": "British Indian Ocean Territory",
    "BRITISH_VIRGIN_ISLANDS": "British Virgin Islands",
    "BROWSER_EXPLOIT": "Browser Exploits",
    "BRUNEI_DARUSSALAM_ASIA_BRUNEI": "Asia/Brunei",
    "BRUNEI_DARUSSALAM": "Brunei Darussalam",
    "BRUNEI": "Brunei",
    "BULGARIA_EUROPE_SOFIA": "Europe/Sofia",
    "BULGARIA": "Bulgaria",
    "BULK_ACTIONS": "Bulk Actions",
    "BURKINA_FASO_AFRICA_OUAGADOUGOU": "Africa/Ouagadougou",
    "BURKINA_FASO": "Burkina Faso",
    "BURUNDI_AFRICA_BUJUMBURA": "Africa/Bujumbura",
    "BURUNDI": "Burundi",
    "BW_DOWNLOAD": "Download (Mbps)",
    "BW_UPLOAD": "Upload (Mbps)",
    "BYTES": "Bytes",
    "CA_CENTRAL_1": "Canada (Central)",
    "CA_CENTRAL_1A": "ca-central-1a",
    "CA_CENTRAL_1B": "ca-central-1b",
    "CA_CENTRAL_1D": "ca-central-1d",
    "CA_INACTIVE": "Health monitoring failure. System is down too long.",
    "CABO_VERDE": "Cabo Verde",
    "CAMBODIA_ASIA_PHNOM_PENH": "Asia/Phnom Penh",
    "CAMBODIA": "Cambodia",
    "CAMEROON_AFRICA_DOUALA": "Africa/Douala",
    "CAMEROON": "Cameroon",
    "CANADA_AMERICA_ATIKOKAN": "America/Atikokan",
    "CANADA_AMERICA_BLANC_SABLON": "America/Blanc-Sablon",
    "CANADA_AMERICA_CAMBRIDGE_BAY": "America/Cambridge Bay",
    "CANADA_AMERICA_DAWSON_CREEK": "America/Dawson Creek",
    "CANADA_AMERICA_DAWSON": "America/Dawson",
    "CANADA_AMERICA_EDMONTON": "America/Edmonton",
    "CANADA_AMERICA_GLACE_BAY": "America/Glace Bay",
    "CANADA_AMERICA_GOOSE_BAY": "America/Goose Bay",
    "CANADA_AMERICA_HALIFAX": "America/Halifax",
    "CANADA_AMERICA_INUVIK": "America/Inuvik",
    "CANADA_AMERICA_IQALUIT": "America/Iqaluit",
    "CANADA_AMERICA_MONCTON": "America/Moncton",
    "CANADA_AMERICA_MONTREAL": "America/Montreal",
    "CANADA_AMERICA_NIPIGON": "America/Nipigon",
    "CANADA_AMERICA_PANGNIRTUNG": "America/Pangnirtung",
    "CANADA_AMERICA_RAINY_RIVER": "America/Rainy River",
    "CANADA_AMERICA_RANKIN_INLET": "America/Rankin Inlet",
    "CANADA_AMERICA_REGINA": "America/Regina",
    "CANADA_AMERICA_RESOLUTE": "America/Resolute",
    "CANADA_AMERICA_ST_JOHNS": "America/St. Johns",
    "CANADA_AMERICA_SWIFT_CURRENT": "America/Swift Current",
    "CANADA_AMERICA_THUNDER_BAY": "America/Thunder Bay",
    "CANADA_AMERICA_TORONTO": "America/Toronto",
    "CANADA_AMERICA_VANCOUVER": "America/Vancouver",
    "CANADA_AMERICA_WHITEHORSE": "America/Whitehorse",
    "CANADA_AMERICA_WINNIPEG": "America/Winnipeg",
    "CANADA_AMERICA_YELLOWKNIFE": "America/Yellowknife",
    "CANADA": "Canada",
    "CANADACENTRAL": "(Canada) Canada Central",
    "CANADAEAST": "(Canada) Canada East",
    "CANCEL_SUCCESS_MESSAGE": "All changes have been canceled.",
    "CANCEL": "Cancel",
    "CAPE_VERDE_ATLANTIC_CAPE_VERDE": "Atlantic/Cape Verde",
    "CAPE_VERDE": "Cape Verde",
    "CATEGORIES": "Categories",
    "CATEGORY": "Category",
    "CAYMAN_ISLANDS_AMERICA_CAYMAN": "America/Cayman",
    "CAYMAN_ISLANDS": "Cayman Islands",
    "CC_ADMIN": "Cloud Connector Admin",
    "CC_BC_DETAILS": "Cloud & Branch Connector Details",
    "CC_CLOUD_PROVIDER_DESCRIPTION": "Select one of the following cloud providers.",
    "CC_DETAILS": "Cloud Connector Details",
    "CC_ENABLE_XFF_FORWARDING": "Use XFF from Client Request",
    "CC_GENERAL_INFORMATION_DESCRIPTION": "Configure a Cloud Provisioning Template within the Zscaler Cloud & Branch Connector Admin Portal for deploying Cloud Connector as a virtual machine with Amazon Web Services (AWS), Google Cloud Platform (GCP) or Microsoft Azure. For more information, see {0}About Cloud Provisioning Templates{1}.",
    "CC_GROUP_NAME": "Cloud Connector Group Name",
    "CC_GROUP": "CC Group",
    "CC_INSTANCE": "CC Instance",
    "CC_LOCATION": "Cloud Connector Location",
    "CC_NW": "CLOUD CONNECTORS NETWORK",
    "CC_ROLE_NAME": "Cloud Connector Role Name",
    "CC_SOURCE_IP": "CC Source IP",
    "CC_SOURCE_PORT": "CC Source Port",
    "CC_STATUS": "Cloud Connector Status",
    "CC_VERSION": "Cloud Connector Version",
    "CC_VM_NAME": "Cloud Connector VM Name",
    "CC_VM": "CC VM",
    "CCA_DEVICE_GROUP": "VDI Device Group",
    "CCA_DEVICE": "VDI Device",
    "CCA_FWD_PROFILE": "VDI Forwarding Profile",
    "CCA_TEMPLATE_APIKEY": "VDI Template API Key",
    "CCA_TEMPLATE_KEY": "VDI Template Key",
    "CCA_TEMPLATE": "VDI Template",
    "CDN": "CDN",
    "CELLULAR_CONFIGURATION_MODE_CORE": "Split Deploy - Core",
    "CELLULAR_CONFIGURATION_MODE_EDGE": "Split Deploy - Edge",
    "CELLULAR_CONFIGURATION_MODE_EDGEONLY": "Edge Only",
    "CELLULAR_CONFIGURATION_MODE": "Cellular Configuration Mode",
    "CELLULAR_CONFIGURATION_SELECTION": "Cellular Configuration Selection",
    "CELLULAR_CONFIGURATION": "Cellular Configuration",
    "CELLULAR_DEPLOYMENT_CONFIGURATION": "Deployment Configuration",
    "CELLULAR": "Cellular",
    "CENTOS": "CentOS",
    "CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI": "Africa/Bangui",
    "CENTRAL_AFRICAN_REPUBLIC": "Central African Republic",
    "CENTRALINDIA": "(Asia Pacific) Central India",
    "CENTRALUS": "(US) Central US",
    "CENTRALUSEUAP": "(US) Central US EUAP",
    "CENTRALUSSTAGE": "(US) Central US (Stage)",
    "CF_ADD_ON_GWLB_TEMPLATE": "Add-on Template with Gateway Load Balancer (GWLB)",
    "CF_CUSTOM_DEPLOYMENT_TEMPLATE": "Custom deployment template",
    "CF_DEFAULT_DEPLOYMENT_TEMPLATE": "Starter Deployment Template",
    "CF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Add-on Template with ZPA",
    "CF_PRE_DEPLOYMENT_TEMPLATE": "Pre-Deployment Template",
    "CHAD_AFRICA_NDJAMENA": "Africa/Ndjamena",
    "CHAD": "Chad",
    "CHANGE_PASSWORD_REMINDER": "Change Password Reminder",
    "CHANGE_PASSWORD_WINDOW": "You should change your password every",
    "CHANGE_PASSWORD": "Change Password",
    "CHECK_BLACKLIST": "Check Denylist",
    "CHILE_AMERICA_SANTIAGO": "America/Santiago",
    "CHILE_PACIFIC_EASTER": "Pacific/Easter",
    "CHILE": "Chile",
    "CHINA_ASIA_CHONGQING": "Asia/Chongqing",
    "CHINA_ASIA_HARBIN": "Asia/Harbin",
    "CHINA_ASIA_KASHGAR": "Asia/Kashgar",
    "CHINA_ASIA_SHANGHAI": "Asia/Shanghai",
    "CHINA_ASIA_URUMQI": "Asia/Urumqi",
    "CHINA": "China",
    "CHINAEAST": "(Asia Pacific) China East",
    "CHINAEAST2": "(Asia Pacific) China East 2",
    "CHINAEAST3": "(Asia Pacific) China East 3",
    "CHINANORTH": "(Asia Pacific) China North",
    "CHINANORTH2": "(Asia Pacific) China North 2",
    "CHINANORTH3": "(Asia Pacific) China North 3",
    "CHOOSE_EXISTING_LOCATION": "Choose Existing Location",
    "CHOOSE_TO_RECEIVE_UPDATES": "CHOOSE TO RECEIVE UPDATES",
    "CHRISTMAS_ISLAND_INDIAN_CHRISTMAS": "Indian/Christmas",
    "CHRISTMAS_ISLAND": "Christmas Island",
    "CHROME_OS": "Chrome",
    "CIPHER_PROTOCOL": "Cipher Protocol",
    "CIPHER": "Cipher",
    "CITY_STATE_PROVINCE_OPTIONAL": "City, State, Province (Optional)",
    "CITY": "City",
    "CLASSIFIEDS": "Classifieds",
    "CLEAR_ALL": "Clear All",
    "CLEAR_FILTERS": "Clear Filters",
    "CLEAR_SEARCH_AND_SORT": "Clear Search & Sorting",
    "CLICK_FOR_MORE_INFO": "Click for more info",
    "CLICK_HERE_TO_ACCEPT_EUSA": "Click here to Accept the Pending EUSA Agreement",
    "CLIENT_CONNECTOR_FOR_VDI": "Client Connector for VDI",
    "CLIENT_DEST_NAME": "Client Destination Name",
    "CLIENT_DESTINATION_IP": "Client Destination IP",
    "CLIENT_DESTINATION_PORT": "Client Destination Port",
    "CLIENT_ID": "Client ID",
    "CLIENT_IP": "Client IP",
    "CLIENT_NETWORK_PROTOCOL": "Client NW Protocol",
    "CLIENT_SECRET": "Client Secret",
    "CLIENT_SOURCE_IP": "Client Source IP",
    "CLIENT_SOURCE_PORT": "Client Source Port",
    "CLOSE": "Close",
    "CLOUD_ACCOUNT": "Cloud Account",
    "CLOUD_AUTOMATION_SCRIPTS": "Cloud Automation Scripts",
    "CLOUD_CONFIG_REQUIREMENTS": "Cloud Configuration Requirements",
    "CLOUD_CONFIGURATION": "Cloud Configuration",
    "CLOUD_CONNECTOR_CONFIGURATION_NOT_APPLICABLE": "This configuration mode is not applicable for Cloud Connector",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_OPTIONAL": "Groups and Namespace",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_TEXT": "Optionally, select the Cloud Connector groups you want to include in this subscription group from the list. Enter a namespace that allows you to reuse the same subscription in different accounts.",
    "CLOUD_CONNECTOR_GROUP_CREATION": "Cloud Connector Group Creation",
    "CLOUD_CONNECTOR_GROUP": "Cloud Connector Group",
    "CLOUD_CONNECTOR_GROUPS_PUB_SUB": "Cloud Connector Groups - Pub/Sub",
    "CLOUD_CONNECTOR_GROUPS": "Cloud Connector Groups",
    "CLOUD_CONNECTOR_INFORMATION": "Connector Information",
    "CLOUD_CONNECTOR_INSTANCE_ROLE_NAME": "Cloud Connector Instance Role Name ",
    "CLOUD_CONNECTOR_MANAGEMENT": "Cloud Connector Management",
    "CLOUD_CONNECTOR_MONITORING": "Cloud Connector Monitoring",
    "CLOUD_CONNECTOR_NAME": "Cloud Connector Name",
    "CLOUD_CONNECTOR_PROVISIONING": "Cloud Connector Provisioning",
    "CLOUD_CONNECTOR_TRAFFIC_FLOW": "Cloud Connector Traffic Flow",
    "CLOUD_CONNECTOR": "Cloud Connector",
    "CLOUD_CONNECTORS_GROUP_AND_NAMESPACE": "Groups & Namespace",
    "CLOUD_CONNECTORS": "Cloud Connectors",
    "CLOUD_FORMATION": "CloudFormation",
    "CLOUD_MANAGEMENT": "Cloud Management",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_FAILED": "Failed on",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PENDING": "Validation pending. Click icon to test the connectivity.",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PREFIX": "Last Validation",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_SUCCESSFUL": "Successful on",
    "CLOUD_NSS_HTTP_HEADERS": "HTTP Headers",
    "CLOUD_OR_ON_PREMISE": "Cloud or On Premise",
    "CLOUD_PROVIDER_TYPE": "Cloud Provider Type",
    "CLOUD_PROVIDER": "Cloud Provider",
    "CLOUD_PROVISIONING": "Cloud Provisioning",
    "CLOUD_WATCH_GROUP_ARN": "Cloud Watch Group ARN",
    "CLOUD": "Cloud",
    "CLOUDFORMATION_TEXT": "CloudFormation templates are used to provide Zscaler with permissions to access account information. Use the Launch CloudFormation hyperlink to open a pre-populated CloudFormation template in your AWS Account. {1}Download the CloudFormation template{2} if launch option does not work.",
    "CLOUDFORMATION": "CloudFormation",
    "CLOUDWATCH_ARN_OPTIONAL": "Cloudwatch ARN (Optional)",
    "CLT_RX_BYTES": "Client Received Bytes",
    "CLT_TX_BYTES": "Client Sent Bytes",
    "CLT_TX_DROPS": "Client Drops Bytes",
    "CLUSTERS": "Clusters",
    "CN_NORTH_1": "China (Beijing)",
    "CN_NORTH_1A": "cn-north-1a",
    "CN_NORTH_1B": "cn-north-1b",
    "CN_NORTHWEST_1": "China (Ningxia)",
    "CN_NORTHWEST_1A": "cn-northwest-1a",
    "CN_NORTHWEST_1B": "cn-northwest-1b",
    "CN_NORTHWEST_1C": "cn-northwest-1c",
    "COCOS_KEELING_ISLANDS_INDIAN_COCOS": "Indian/Cocos",
    "COCOS_KEELING_ISLANDS": "Cocos (Keeling) Islands",
    "CODE": "Code",
    "COLOMBIA_AMERICA_BOGOTA": "America/Bogota",
    "COLOMBIA": "Colombia",
    "COMMANDS": "Commands",
    "COMMENTS": "Comments",
    "COMOROS_INDIAN_COMORO": "Indian/Comoro",
    "COMOROS": "Comoros",
    "COMPARE_VERSIONS": "Compare Versions",
    "COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE": "COMPUTE RECOMMENDED EC2 INSTANCE TYPE",
    "COMPUTE": "Compute",
    "COMPUTER_HACKING": "Computer Hacking",
    "CONFIG": "Config",
    "CONFIGURATION_INFO": "CONFIGURATION INFO",
    "CONFIGURATION_MODE": "Configuration   Mode",
    "CONFIGURATION_NAME": "Configuration Name",
    "CONFIGURATION_TEMPLATE_NAME": "Configuration Template Name",
    "CONFIGURATION_TEXT": "Zscaler requires permissions to assume an IAM role in your AWS account. These permissions enable Zscaler to collect real-time configuration metadata in AWS.  For more information, see {1}Adding a New AWS Account{2}.",
    "CONFIGURATION": "Configuration",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP": "You can define rules that control Log and Control Forwarding requests and responses.",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD": "Configure Log and Control Forwarding Policy",
    "CONFIGURE_TRAFFIC_FORWARD_TIP": "You can define rules that control Traffic Forwarding requests and responses.",
    "CONFIGURE_TRAFFIC_FORWARD": "Configure Traffic Forwarding",
    "CONFIGURED_MODE": "Configured Mode",
    "CONFIRM_CHANGES": "Confirm Changes",
    "CONFIRM_PASSWORD_NON_EQUALITY": "New password and confirm password should be equal",
    "CONFIRM_PASSWORD_PLACEHOLDER": "Enter your new password to confirm it",
    "CONFIRM_PASSWORD": "Confirm Password",
    "CONFIRM": "Confirm",
    "CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE": "Africa/Brazzaville",
    "CONGO_CONGO_BRAZZAVILLE": "Africa/Brazzaville",
    "CONGO_DEM_REP_AFRICA_KINSHASA": "Africa/Kinshasa",
    "CONGO_DEM_REP_AFRICA_LUBUMBASHI": "Africa/Lubumbashi",
    "CONGO_DEM_REP": "Congo (Dem. Rep.)",
    "CONGO_REP_AFRICA_BRAZZAVILLE": "Africa/Brazzaville",
    "CONGO_REP": "Congo (Rep.)",
    "CONGO_REPUBLIC": "Congo Republic",
    "CONNECTOR_GROUP": "Connector Group",
    "CONNECTOR_GROUPS": "Connector Groups",
    "CONNECTOR_INSTANCE": "Connector Instance",
    "CONNECTOR_IP": "Connector IP",
    "CONNECTOR_MANAGEMENT": "Connector Management",
    "CONNECTOR_NAME": "Connector Name",
    "CONNECTOR_NAMES": "Connector Names",
    "CONNECTOR_SOURCE_IP": "Connector Source IP",
    "CONNECTOR_SOURCE_PORT": "Connector Source Port",
    "CONNECTOR_VM_SIZE": "Connector VM Size",
    "CONNECTOR_VM": "Connector VM",
    "CONTAINS": "Contains",
    "CONTINUING_EDUCATION_COLLEGES": "Continuing Education/Colleges",
    "CONTROL_SLASH_DATA": "Control/Data",
    "COOK_ISLANDS_PACIFIC_RAROTONGA": "Pacific/Rarotonga",
    "COOK_ISLANDS": "Cook Islands",
    "Copy for provisioning": "Copy for provisioning",
    "COPY_CLOUD_CONNECTOR": "Copy Cloud Connector",
    "COPY_FOR_PROVISIONING": "Copy for provisioning",
    "COPY_PROVISIONING_URL": "Copy Provisioning URL",
    "COPY_RIGHT": "Copyright ©2007-2020 Zscaler Inc.All rights reserved.",
    "COPY": "Copy",
    "COPYRIGHT_INFRINGEMENT": "Copyright Infringement",
    "COPYRIGHT": "Copyright",
    "CORPORATE_MARKETING": "Corporate Marketing",
    "CORPORATE": "Corporate user traffic type",
    "COSTA_RICA_AMERICA_COSTA_RICA": "America/Costa Rica",
    "COSTA_RICA": "Costa Rica",
    "COTE_DIVOIRE_AFRICA_ABIDJAN": "Africa/Abidjan",
    "COTE_DIVOIRE": "Cote d'Ivoire",
    "COUNT": "Count",
    "COUNTRIES": "Countries",
    "COUNTRY_A1": "Anonymous Proxy",
    "COUNTRY_A2": "Satellite Provider",
    "COUNTRY_AC": "Ascension Island",
    "COUNTRY_AD": "Andorra",
    "COUNTRY_AE": "United Arab Emirates",
    "COUNTRY_AF": "Afghanistan",
    "COUNTRY_AG": "Antigua and Barbuda",
    "COUNTRY_AI": "Anguilla",
    "COUNTRY_AL": "Albania",
    "COUNTRY_AM": "Armenia",
    "COUNTRY_AN": "Netherlands Antilles",
    "COUNTRY_AO": "Angola",
    "COUNTRY_AP": "Asia/Pacific Region",
    "COUNTRY_AQ": "Antarctica",
    "COUNTRY_AR": "Argentina",
    "COUNTRY_AS": "American Samoa",
    "COUNTRY_AT": "Austria",
    "COUNTRY_AU": "Australia",
    "COUNTRY_AW": "Aruba",
    "COUNTRY_AX": "Aland Islands",
    "COUNTRY_AZ": "Azerbaijan",
    "COUNTRY_BA": "Bosnia and Herzegovina",
    "COUNTRY_BB": "Barbados",
    "COUNTRY_BD": "Bangladesh",
    "COUNTRY_BE": "Belgium",
    "COUNTRY_BF": "Burkina Faso",
    "COUNTRY_BG": "Bulgaria",
    "COUNTRY_BH": "Bahrain",
    "COUNTRY_BI": "Burundi",
    "COUNTRY_BJ": "Benin",
    "COUNTRY_BL": "Saint Barthelemy",
    "COUNTRY_BM": "Bermuda",
    "COUNTRY_BN": "Brunei Darussalam",
    "COUNTRY_BO": "Bolivia",
    "COUNTRY_BQ": "Bonaire, Sint Eustatius and Saba",
    "COUNTRY_BR": "Brazil",
    "COUNTRY_BS": "Bahamas",
    "COUNTRY_BT": "Bhutan",
    "COUNTRY_BU": "Burma",
    "COUNTRY_BV": "Bouvet Island",
    "COUNTRY_BW": "Botswana",
    "COUNTRY_BX": "Benelux Trademarks and Design Offices",
    "COUNTRY_BY": "Belarus",
    "COUNTRY_BZ": "Belize",
    "COUNTRY_CA": "Canada",
    "COUNTRY_CC": "Cocos (Keeling) Islands",
    "COUNTRY_CD": "Democratic Republic of Congo (Congo-Kinshasa)",
    "COUNTRY_CF": "Central African Republic",
    "COUNTRY_CG": "Congo (Congo-Brazzaville)",
    "COUNTRY_CH": "Switzerland",
    "COUNTRY_CI": "Cote DIvoire",
    "COUNTRY_CK": "Cook Islands",
    "COUNTRY_CL": "Chile",
    "COUNTRY_CM": "Cameroon",
    "COUNTRY_CN": "China",
    "COUNTRY_CO": "Colombia",
    "COUNTRY_CODE": "Country",
    "COUNTRY_CP": "Clipperton Island",
    "COUNTRY_CR": "Costa Rica",
    "COUNTRY_CS": "Serbia and Montenegro",
    "COUNTRY_CT": "Canton and Enderbury Islands",
    "COUNTRY_CU": "Cuba",
    "COUNTRY_CV": "Cape Verde",
    "COUNTRY_CW": "Curacao",
    "COUNTRY_CX": "Christmas Island",
    "COUNTRY_CY": "Cyprus",
    "COUNTRY_CZ": "Czech Republic",
    "COUNTRY_DD": "German Democratic Republic",
    "COUNTRY_DE": "Germany",
    "COUNTRY_DG": "Diego Garcia",
    "COUNTRY_DJ": "Djibouti",
    "COUNTRY_DK": "Denmark",
    "COUNTRY_DM": "Dominica",
    "COUNTRY_DO": "Dominican Republic",
    "COUNTRY_DY": "Benin",
    "COUNTRY_DZ": "Algeria",
    "COUNTRY_EA": "Ceuta, Melilla",
    "COUNTRY_EC": "Ecuador",
    "COUNTRY_EE": "Estonia",
    "COUNTRY_EF": "Union of Countries under the European Community Patent Convention",
    "COUNTRY_EG": "Egypt",
    "COUNTRY_EH": "Western Sahara",
    "COUNTRY_EM": "European Trademark Office",
    "COUNTRY_EP": "European Patent Organization",
    "COUNTRY_ER": "Eritrea",
    "COUNTRY_ES": "Spain",
    "COUNTRY_ET": "Ethiopia",
    "COUNTRY_EU": "Europe",
    "COUNTRY_EV": "Eurasian Patent Organization",
    "COUNTRY_EW": "Estonia",
    "COUNTRY_FI": "Finland",
    "COUNTRY_FJ": "Fiji",
    "COUNTRY_FK": "Falkland Islands (Malvinas)",
    "COUNTRY_FL": "Liechtenstein",
    "COUNTRY_FM": "Federated States of Micronesia",
    "COUNTRY_FO": "Faroe Islands",
    "COUNTRY_FQ": "French Southern and Antarctic Territories",
    "COUNTRY_FR": "France",
    "COUNTRY_FX": "Metropolitan France",
    "COUNTRY_GA": "Gabon",
    "COUNTRY_GB": "United Kingdom",
    "COUNTRY_GC": "Patent Office of the Cooperation Council for the Arab States of the Gulf (GCC)",
    "COUNTRY_GD": "Grenada",
    "COUNTRY_GE": "Georgia",
    "COUNTRY_GF": "French Guiana",
    "COUNTRY_GG": "Guernsey",
    "COUNTRY_GH": "Ghana",
    "COUNTRY_GI": "Gibraltar",
    "COUNTRY_GL": "Greenland",
    "COUNTRY_GM": "Gambia",
    "COUNTRY_GN": "Guinea",
    "COUNTRY_GP": "Guadeloupe",
    "COUNTRY_GQ": "Equatorial Guinea",
    "COUNTRY_GR": "Greece",
    "COUNTRY_GS": "South Georgia and the South Sandwich Islands",
    "COUNTRY_GT": "Guatemala",
    "COUNTRY_GU": "Guam",
    "COUNTRY_GW": "Guinea-Bissau",
    "COUNTRY_GY": "Guyana",
    "COUNTRY_HK": "Hong Kong",
    "COUNTRY_HM": "Heard Island and McDonald Islands",
    "COUNTRY_HN": "Honduras",
    "COUNTRY_HR": "Croatia",
    "COUNTRY_HT": "Haiti",
    "COUNTRY_HU": "Hungary",
    "COUNTRY_HV": "Upper Volta",
    "COUNTRY_IB": "International Bureau of WIPO",
    "COUNTRY_IC": "Canary Islands",
    "COUNTRY_ID": "Indonesia",
    "COUNTRY_IE": "Ireland",
    "COUNTRY_IL": "Israel",
    "COUNTRY_IM": "Isle of Man",
    "COUNTRY_IN": "India",
    "COUNTRY_IO": "British Indian Ocean Territory",
    "COUNTRY_IQ": "Iraq",
    "COUNTRY_IR": "Iran",
    "COUNTRY_IS": "Iceland",
    "COUNTRY_IT": "Italy",
    "COUNTRY_JA": "Jamaica",
    "COUNTRY_JE": "Jersey",
    "COUNTRY_JM": "Jamaica",
    "COUNTRY_JO": "Jordan",
    "COUNTRY_JP": "Japan",
    "COUNTRY_JT": "Johnston Island",
    "COUNTRY_KE": "Kenya",
    "COUNTRY_KG": "Kyrgyzstan",
    "COUNTRY_KH": "Cambodia",
    "COUNTRY_KI": "Kiribati",
    "COUNTRY_KM": "Comoros",
    "COUNTRY_KN": "Saint Kitts and Nevis",
    "COUNTRY_KP": "North Korea",
    "COUNTRY_KR": "South Korea",
    "COUNTRY_KW": "Kuwait",
    "COUNTRY_KY": "Cayman Islands",
    "COUNTRY_KZ": "Kazakhstan",
    "COUNTRY_LA": "Laos",
    "COUNTRY_LB": "Lebanon",
    "COUNTRY_LC": "Saint Lucia",
    "COUNTRY_LF": "Libya Fezzan",
    "COUNTRY_LI": "Liechtenstein",
    "COUNTRY_LK": "Sri Lanka",
    "COUNTRY_LR": "Liberia",
    "COUNTRY_LS": "Lesotho",
    "COUNTRY_LT": "Lithuania",
    "COUNTRY_LU": "Luxembourg",
    "COUNTRY_LV": "Latvia",
    "COUNTRY_LY": "Libya",
    "COUNTRY_MA": "Morocco",
    "COUNTRY_MC": "Monaco",
    "COUNTRY_MD": "Moldova",
    "COUNTRY_ME": "Montenegro",
    "COUNTRY_MF": "Saint Martin",
    "COUNTRY_MG": "Madagascar",
    "COUNTRY_MH": "Marshall Islands",
    "COUNTRY_MI": "Midway Islands",
    "COUNTRY_MK": "Macedonia",
    "COUNTRY_ML": "Mali",
    "COUNTRY_MM": "Myanmar",
    "COUNTRY_MN": "Mongolia",
    "COUNTRY_MO": "Macau",
    "COUNTRY_MP": "Northern Mariana Islands",
    "COUNTRY_MQ": "Martinique",
    "COUNTRY_MR": "Mauritania",
    "COUNTRY_MS": "Montserrat",
    "COUNTRY_MT": "Malta",
    "COUNTRY_MU": "Mauritius",
    "COUNTRY_MV": "Maldives",
    "COUNTRY_MW": "Malawi",
    "COUNTRY_MX": "Mexico",
    "COUNTRY_MY": "Malaysia",
    "COUNTRY_MZ": "Mozambique",
    "COUNTRY_NA": "Namibia",
    "COUNTRY_NC": "New Caledonia",
    "COUNTRY_NE": "Niger",
    "COUNTRY_NF": "Norfolk Island",
    "COUNTRY_NG": "Nigeria",
    "COUNTRY_NH": "New Hebrides",
    "COUNTRY_NI": "Nicaragua",
    "COUNTRY_NL": "Netherlands",
    "COUNTRY_NO": "Norway",
    "COUNTRY_NP": "Nepal",
    "COUNTRY_NQ": "Dronning Maud Land",
    "COUNTRY_NR": "Nauru",
    "COUNTRY_NT": "Neutral Zone",
    "COUNTRY_NU": "Niue",
    "COUNTRY_NZ": "New Zealand",
    "COUNTRY_O1": "Other",
    "COUNTRY_OA": "African Intellectual Property Organization",
    "COUNTRY_OM": "Oman",
    "COUNTRY_OPTIONAL": "Country (Optional)",
    "COUNTRY_PA": "Panama",
    "COUNTRY_PC": "Pacific Islands, Trust Territory of the",
    "COUNTRY_PE": "Peru",
    "COUNTRY_PF": "French Polynesia",
    "COUNTRY_PG": "Papua New Guinea",
    "COUNTRY_PH": "Philippines",
    "COUNTRY_PI": "Philippines",
    "COUNTRY_PK": "Pakistan",
    "COUNTRY_PL": "Poland",
    "COUNTRY_PM": "Saint Pierre and Miquelon",
    "COUNTRY_PN": "Pitcairn Islands",
    "COUNTRY_PR": "Puerto Rico",
    "COUNTRY_PS": "Palestinian Territory",
    "COUNTRY_PT": "Portugal",
    "COUNTRY_PU": "U.S. Miscellaneous Pacific Islands",
    "COUNTRY_PW": "Palau",
    "COUNTRY_PY": "Paraguay",
    "COUNTRY_PZ": "Panama Canal Zone",
    "COUNTRY_QA": "Qatar",
    "COUNTRY_RA": "Argentina",
    "COUNTRY_RB": "Bolivia cf Botswana",
    "COUNTRY_RC": "China",
    "COUNTRY_RE": "Reunion",
    "COUNTRY_RH": "Haiti",
    "COUNTRY_RI": "Indonesia",
    "COUNTRY_RL": "Lebanon",
    "COUNTRY_RM": "Madagascar",
    "COUNTRY_RN": "Niger",
    "COUNTRY_RO": "Romania",
    "COUNTRY_RP": "Philippines",
    "COUNTRY_RS": "Serbia",
    "COUNTRY_RU": "Russia",
    "COUNTRY_RW": "Rwanda",
    "COUNTRY_SA": "Saudi Arabia",
    "COUNTRY_SB": "Solomon Islands",
    "COUNTRY_SC": "Seychelles",
    "COUNTRY_SD": "Sudan",
    "COUNTRY_SE": "Sweden",
    "COUNTRY_SF": "Finland",
    "COUNTRY_SG": "Singapore",
    "COUNTRY_SH": "Saint Helena",
    "COUNTRY_SI": "Slovenia",
    "COUNTRY_SJ": "Svalbard and Jan Mayen",
    "COUNTRY_SK": "Slovakia",
    "COUNTRY_SL": "Sierra Leone",
    "COUNTRY_SM": "San Marino",
    "COUNTRY_SN": "Senegal",
    "COUNTRY_SO": "Somalia",
    "COUNTRY_SR": "Suriname",
    "COUNTRY_SS": "South Sudan",
    "COUNTRY_ST": "Sao Tome and Principe",
    "COUNTRY_SU": "USSR",
    "COUNTRY_SV": "El Salvador",
    "COUNTRY_SX": "Sint Maarten (Dutch part)",
    "COUNTRY_SY": "Syria",
    "COUNTRY_SZ": "Swaziland",
    "COUNTRY_TA": "Tristan da Cunha",
    "COUNTRY_TC": "Turks and Caicos Islands",
    "COUNTRY_TD": "Chad",
    "COUNTRY_TF": "French Southern Territories",
    "COUNTRY_TG": "Togo",
    "COUNTRY_TH": "Thailand",
    "COUNTRY_TJ": "Tajikistan",
    "COUNTRY_TK": "Tokelau",
    "COUNTRY_TL": "Timor-Leste",
    "COUNTRY_TM": "Turkmenistan",
    "COUNTRY_TN": "Tunisia",
    "COUNTRY_TO": "Tonga",
    "COUNTRY_TP": "East Timor",
    "COUNTRY_TR": "Turkey",
    "COUNTRY_TT": "Trinidad and Tobago",
    "COUNTRY_TV": "Tuvalu",
    "COUNTRY_TW": "Taiwan",
    "COUNTRY_TZ": "Tanzania",
    "COUNTRY_UA": "Ukraine",
    "COUNTRY_UG": "Uganda",
    "COUNTRY_UK": "United Kingdom",
    "COUNTRY_UM": "United States Minor Outlying Islands",
    "COUNTRY_US": "United States",
    "COUNTRY_USA": "USA",
    "COUNTRY_UY": "Uruguay",
    "COUNTRY_UZ": "Uzbekistan",
    "COUNTRY_VA": "Holy See (Vatican City State)",
    "COUNTRY_VC": "Saint Vincent and the Grenadines",
    "COUNTRY_VD": "Viet-Nam, Democratic Republic of",
    "COUNTRY_VE": "Venezuela",
    "COUNTRY_VG": "Virgin Islands (British)",
    "COUNTRY_VI": "Virgin Islands (U.S.)",
    "COUNTRY_VN": "Vietnam",
    "COUNTRY_VU": "Vanuatu",
    "COUNTRY_WF": "Wallis and Futuna",
    "COUNTRY_WG": "Grenada",
    "COUNTRY_WK": "Wake Island",
    "COUNTRY_WL": "Saint Lucia",
    "COUNTRY_WO": "World Intellectual Property Organization",
    "COUNTRY_WS": "Samoa",
    "COUNTRY_WV": "Saint Vincent",
    "COUNTRY_YD": "Yemen, Democratic",
    "COUNTRY_YE": "Yemen",
    "COUNTRY_YT": "Mayotte",
    "COUNTRY_YU": "Yugoslavia",
    "COUNTRY_YV": "Venezuela",
    "COUNTRY_ZA": "South Africa",
    "COUNTRY_ZM": "Zambia",
    "COUNTRY_ZR": "Zaire",
    "COUNTRY_ZW": "Zimbabwe",
    "COUNTRY": "Country",
    "CREATE_A_NEW_GROUP": "Create a New Group",
    "CREATE_A_NEW_TEST": "Create a New Test",
    "CREATE_COMPLETE": "Creation Complete",
    "CREATE_IN_PROGRESS": "Creation in  progress",
    "CREATE_TEST_TEXT": "Create a test by entering the name, description, destination and protocol (HTTP/HTTPS) to be used for the test. Once created, the test can be run on demand. When executed, this test simulates a curl command to the destination using the entered protocol. The transaction should be visible in the forwarding logs, firewall logs, web logs etc.",
    "CREATE_TEST": "Create Test",
    "CREATE": "Create",
    "CREATING": "Creating",
    "CREDENTIAL_DETAILS_TEXT": "Add the Zscaler Principal as a 'Service Account Token Creator' for the service account in your account named below.",
    "CREDENTIAL_DETAILS": "Credential Details",
    "CREDENTIALS_INFO": "Enter the Azure service principal credentials to access your Azure subscriptions. You can either use a single service principal for multiple subscriptions or have a service principal for each subscription. Ensure the service principal has {1}access permissions{2}.",
    "CREDENTIALS_TEXT": "Enter the Azure account credentials to access your Azure account.",
    "CREDENTIALS": "Credentials",
    "CRITERIA_TEXT": "Criteria",
    "CRITERIA": "CRITERIA",
    "CROATIA_EUROPE_ZAGREB": "Europe/Zagreb",
    "CROATIA": "Croatia",
    "CRYPTOMINING": "Cryptomining",
    "CTL_CONNECTION_FAIL": "SVPN control connection failed.",
    "CTL_GW_CONN_CLOSE": "Control gateway active connection closed.",
    "CTL_GW_CONN_SETUP_FAIL": "Control gateway connection setup failed (Internal error).",
    "CTL_GW_CONNECT_FAIL": "Control gateway connection failed (Network error).",
    "CTL_GW_DNS_RESOLVE_FAIL": "Control gateway DNS resolution failed.",
    "CTL_GW_KA_FAIL": "Control gateway connection keepalive failed.",
    "CTL_GW_NO_CONN": "Control gateway connection not yet initiated by client.",
    "CTL_GW_PAC_RESOLVE_FAIL": "Control gateway PAC resolution failed.",
    "CTL_GW_PAC_RESOLVE_NOIP": "Control gateway PAC resolution returned no IPS.",
    "CTL_GW_PROTO_MSG_ERROR": "Message format error in control GW response.",
    "CTL_GW_SRV_ERR_RESPONSE": "Control gateway received HTTP error response from server.",
    "CTL_GW_UNHEALTHY": "Control gateway is unhealthy (Transient state).",
    "CTL_KEEAPLIVE_FAIL": "SVPN control connection keepalive failed.",
    "CTL_KEEPALIVE_FAIL": "SVPN control connection keepalive failed.",
    "CUBA_AMERICA_HAVANA": "America/Havana",
    "CUBA": "Cuba",
    "CULT": "Cult",
    "CURRENT_API_KEY": "Current API Key",
    "CURRENT_DAY": "Current Day",
    "CURRENT_MODE": "Current Mode",
    "CURRENT_MONTH": "Current Month",
    "CURRENT_PASSWORD_NOT_VALID": "Please enter valid current password",
    "CURRENT_PASSWORD": "Current Password",
    "CURRENT_VERSION": "Current Version",
    "CURRENT_WEEK": "Current Week",
    "CURRENTLY_EDITING": "CURRENTLY EDITING",
    "CUSTOM_00": "Custom category 0",
    "CUSTOM_01": "Custom category 1",
    "CUSTOM_02": "Custom category 2",
    "CUSTOM_03": "Custom category 3",
    "CUSTOM_04": "Custom category 4",
    "CUSTOM_05": "Custom category 5",
    "CUSTOM_06": "Custom category 6",
    "CUSTOM_07": "Custom category 7",
    "CUSTOM_08": "Custom category 8",
    "CUSTOM_09": "Custom category 9",
    "CUSTOM_10": "Custom category 0",
    "CUSTOM_100": "Custom category 100",
    "CUSTOM_101": "Custom category 101",
    "CUSTOM_102": "Custom category 102",
    "CUSTOM_103": "Custom category 103",
    "CUSTOM_104": "Custom category 104",
    "CUSTOM_105": "Custom category 105",
    "CUSTOM_106": "Custom category 106",
    "CUSTOM_107": "Custom category 107",
    "CUSTOM_108": "Custom category 108",
    "CUSTOM_109": "Custom category 109",
    "CUSTOM_11": "Custom category 11",
    "CUSTOM_110": "Custom category 110",
    "CUSTOM_111": "Custom category 111",
    "CUSTOM_112": "Custom category 112",
    "CUSTOM_113": "Custom category 113",
    "CUSTOM_114": "Custom category 114",
    "CUSTOM_115": "Custom category 115",
    "CUSTOM_116": "Custom category 116",
    "CUSTOM_117": "Custom category 117",
    "CUSTOM_118": "Custom category 118",
    "CUSTOM_119": "Custom category 119",
    "CUSTOM_12": "Custom category 12",
    "CUSTOM_120": "Custom category 120",
    "CUSTOM_121": "Custom category 121",
    "CUSTOM_122": "Custom category 122",
    "CUSTOM_123": "Custom category 123",
    "CUSTOM_124": "Custom category 124",
    "CUSTOM_125": "Custom category 125",
    "CUSTOM_126": "Custom category 126",
    "CUSTOM_127": "Custom category 127",
    "CUSTOM_128": "Custom category 128",
    "CUSTOM_129": "Custom category 129",
    "CUSTOM_13": "Custom category 13",
    "CUSTOM_130": "Custom category 130",
    "CUSTOM_131": "Custom category 131",
    "CUSTOM_132": "Custom category 132",
    "CUSTOM_133": "Custom category 133",
    "CUSTOM_134": "Custom category 134",
    "CUSTOM_135": "Custom category 135",
    "CUSTOM_136": "Custom category 136",
    "CUSTOM_137": "Custom category 137",
    "CUSTOM_138": "Custom category 138",
    "CUSTOM_139": "Custom category 139",
    "CUSTOM_14": "Custom category 14",
    "CUSTOM_140": "Custom category 140",
    "CUSTOM_141": "Custom category 141",
    "CUSTOM_142": "Custom category 142",
    "CUSTOM_143": "Custom category 143",
    "CUSTOM_144": "Custom category 144",
    "CUSTOM_145": "Custom category 145",
    "CUSTOM_146": "Custom category 146",
    "CUSTOM_147": "Custom category 147",
    "CUSTOM_148": "Custom category 148",
    "CUSTOM_149": "Custom category 149",
    "CUSTOM_15": "Custom category 15",
    "CUSTOM_150": "Custom category 150",
    "CUSTOM_151": "Custom category 151",
    "CUSTOM_152": "Custom category 152",
    "CUSTOM_153": "Custom category 153",
    "CUSTOM_154": "Custom category 154",
    "CUSTOM_155": "Custom category 155",
    "CUSTOM_156": "Custom category 156",
    "CUSTOM_157": "Custom category 157",
    "CUSTOM_158": "Custom category 158",
    "CUSTOM_159": "Custom category 159",
    "CUSTOM_16": "Custom category 16",
    "CUSTOM_160": "Custom category 160",
    "CUSTOM_161": "Custom category 161",
    "CUSTOM_162": "Custom category 162",
    "CUSTOM_163": "Custom category 163",
    "CUSTOM_164": "Custom category 164",
    "CUSTOM_165": "Custom category 165",
    "CUSTOM_166": "Custom category 166",
    "CUSTOM_167": "Custom category 167",
    "CUSTOM_168": "Custom category 168",
    "CUSTOM_169": "Custom category 169",
    "CUSTOM_17": "Custom category 17",
    "CUSTOM_170": "Custom category 170",
    "CUSTOM_171": "Custom category 171",
    "CUSTOM_172": "Custom category 172",
    "CUSTOM_173": "Custom category 173",
    "CUSTOM_174": "Custom category 174",
    "CUSTOM_175": "Custom category 175",
    "CUSTOM_176": "Custom category 176",
    "CUSTOM_177": "Custom category 177",
    "CUSTOM_178": "Custom category 178",
    "CUSTOM_179": "Custom category 179",
    "CUSTOM_18": "Custom category 18",
    "CUSTOM_180": "Custom category 180",
    "CUSTOM_181": "Custom category 181",
    "CUSTOM_182": "Custom category 182",
    "CUSTOM_183": "Custom category 183",
    "CUSTOM_184": "Custom category 184",
    "CUSTOM_185": "Custom category 185",
    "CUSTOM_186": "Custom category 186",
    "CUSTOM_187": "Custom category 187",
    "CUSTOM_188": "Custom category 188",
    "CUSTOM_189": "Custom category 189",
    "CUSTOM_19": "Custom category 19",
    "CUSTOM_190": "Custom category 190",
    "CUSTOM_191": "Custom category 191",
    "CUSTOM_192": "Custom category 192",
    "CUSTOM_193": "Custom category 193",
    "CUSTOM_194": "Custom category 194",
    "CUSTOM_195": "Custom category 195",
    "CUSTOM_196": "Custom category 196",
    "CUSTOM_197": "Custom category 197",
    "CUSTOM_198": "Custom category 198",
    "CUSTOM_199": "Custom category 199",
    "CUSTOM_20": "Custom category 20",
    "CUSTOM_200": "Custom category 200",
    "CUSTOM_201": "Custom category 201",
    "CUSTOM_202": "Custom category 202",
    "CUSTOM_203": "Custom category 203",
    "CUSTOM_204": "Custom category 204",
    "CUSTOM_205": "Custom category 205",
    "CUSTOM_206": "Custom category 206",
    "CUSTOM_207": "Custom category 207",
    "CUSTOM_208": "Custom category 208",
    "CUSTOM_209": "Custom category 209",
    "CUSTOM_21": "Custom category 21",
    "CUSTOM_210": "Custom category 210",
    "CUSTOM_211": "Custom category 211",
    "CUSTOM_212": "Custom category 212",
    "CUSTOM_213": "Custom category 213",
    "CUSTOM_214": "Custom category 214",
    "CUSTOM_215": "Custom category 215",
    "CUSTOM_216": "Custom category 216",
    "CUSTOM_217": "Custom category 217",
    "CUSTOM_218": "Custom category 218",
    "CUSTOM_219": "Custom category 219",
    "CUSTOM_22": "Custom category 22",
    "CUSTOM_220": "Custom category 220",
    "CUSTOM_221": "Custom category 221",
    "CUSTOM_222": "Custom category 222",
    "CUSTOM_223": "Custom category 223",
    "CUSTOM_224": "Custom category 224",
    "CUSTOM_225": "Custom category 225",
    "CUSTOM_226": "Custom category 226",
    "CUSTOM_227": "Custom category 227",
    "CUSTOM_228": "Custom category 228",
    "CUSTOM_229": "Custom category 229",
    "CUSTOM_23": "Custom category 23",
    "CUSTOM_230": "Custom category 230",
    "CUSTOM_231": "Custom category 231",
    "CUSTOM_232": "Custom category 232",
    "CUSTOM_233": "Custom category 233",
    "CUSTOM_234": "Custom category 234",
    "CUSTOM_235": "Custom category 235",
    "CUSTOM_236": "Custom category 236",
    "CUSTOM_237": "Custom category 237",
    "CUSTOM_238": "Custom category 238",
    "CUSTOM_239": "Custom category 239",
    "CUSTOM_24": "Custom category 24",
    "CUSTOM_240": "Custom category 240",
    "CUSTOM_241": "Custom category 241",
    "CUSTOM_242": "Custom category 242",
    "CUSTOM_243": "Custom category 243",
    "CUSTOM_244": "Custom category 244",
    "CUSTOM_245": "Custom category 245",
    "CUSTOM_246": "Custom category 246",
    "CUSTOM_247": "Custom category 247",
    "CUSTOM_248": "Custom category 248",
    "CUSTOM_249": "Custom category 249",
    "CUSTOM_25": "Custom category 25",
    "CUSTOM_250": "Custom category 250",
    "CUSTOM_251": "Custom category 251",
    "CUSTOM_252": "Custom category 252",
    "CUSTOM_253": "Custom category 253",
    "CUSTOM_254": "Custom category 254",
    "CUSTOM_255": "Custom category 255",
    "CUSTOM_256": "Custom category 256",
    "CUSTOM_257": "Custom Category 257",
    "CUSTOM_258": "Custom Category 258",
    "CUSTOM_259": "Custom Category 259",
    "CUSTOM_26": "Custom category 26",
    "CUSTOM_260": "Custom Category 260",
    "CUSTOM_261": "Custom Category 261",
    "CUSTOM_262": "Custom Category 262",
    "CUSTOM_263": "Custom Category 263",
    "CUSTOM_264": "Custom Category 264",
    "CUSTOM_265": "Custom Category 265",
    "CUSTOM_266": "Custom Category 266",
    "CUSTOM_267": "Custom Category 267",
    "CUSTOM_268": "Custom Category 268",
    "CUSTOM_269": "Custom Category 269",
    "CUSTOM_27": "Custom category 27",
    "CUSTOM_270": "Custom Category 270",
    "CUSTOM_271": "Custom Category 271",
    "CUSTOM_272": "Custom Category 272",
    "CUSTOM_273": "Custom Category 273",
    "CUSTOM_274": "Custom Category 274",
    "CUSTOM_275": "Custom Category 275",
    "CUSTOM_276": "Custom Category 276",
    "CUSTOM_277": "Custom Category 277",
    "CUSTOM_278": "Custom Category 278",
    "CUSTOM_279": "Custom Category 279",
    "CUSTOM_28": "Custom category 28",
    "CUSTOM_280": "Custom Category 280",
    "CUSTOM_281": "Custom Category 281",
    "CUSTOM_282": "Custom Category 282",
    "CUSTOM_283": "Custom Category 283",
    "CUSTOM_284": "Custom Category 284",
    "CUSTOM_285": "Custom Category 285",
    "CUSTOM_286": "Custom Category 286",
    "CUSTOM_287": "Custom Category 287",
    "CUSTOM_288": "Custom Category 288",
    "CUSTOM_289": "Custom Category 289",
    "CUSTOM_29": "Custom category 29",
    "CUSTOM_290": "Custom Category 290",
    "CUSTOM_291": "Custom Category 291",
    "CUSTOM_292": "Custom Category 292",
    "CUSTOM_293": "Custom Category 293",
    "CUSTOM_294": "Custom Category 294",
    "CUSTOM_295": "Custom Category 295",
    "CUSTOM_296": "Custom Category 296",
    "CUSTOM_297": "Custom Category 297",
    "CUSTOM_298": "Custom Category 298",
    "CUSTOM_299": "Custom Category 299",
    "CUSTOM_30": "Custom category 30",
    "CUSTOM_300": "Custom Category 300",
    "CUSTOM_301": "Custom Category 301",
    "CUSTOM_302": "Custom Category 302",
    "CUSTOM_303": "Custom Category 303",
    "CUSTOM_304": "Custom Category 304",
    "CUSTOM_305": "Custom Category 305",
    "CUSTOM_306": "Custom Category 306",
    "CUSTOM_307": "Custom Category 307",
    "CUSTOM_308": "Custom Category 308",
    "CUSTOM_309": "Custom Category 309",
    "CUSTOM_31": "Custom category 31",
    "CUSTOM_310": "Custom Category 310",
    "CUSTOM_311": "Custom Category 311",
    "CUSTOM_312": "Custom Category 312",
    "CUSTOM_313": "Custom Category 313",
    "CUSTOM_314": "Custom Category 314",
    "CUSTOM_315": "Custom Category 315",
    "CUSTOM_316": "Custom Category 316",
    "CUSTOM_317": "Custom Category 317",
    "CUSTOM_318": "Custom Category 318",
    "CUSTOM_319": "Custom Category 319",
    "CUSTOM_32": "Custom category 32",
    "CUSTOM_320": "Custom Category 320",
    "CUSTOM_321": "Custom Category 321",
    "CUSTOM_322": "Custom Category 322",
    "CUSTOM_323": "Custom Category 323",
    "CUSTOM_324": "Custom Category 324",
    "CUSTOM_325": "Custom Category 325",
    "CUSTOM_326": "Custom Category 326",
    "CUSTOM_327": "Custom Category 327",
    "CUSTOM_328": "Custom Category 328",
    "CUSTOM_329": "Custom Category 329",
    "CUSTOM_33": "Custom category 33",
    "CUSTOM_330": "Custom Category 330",
    "CUSTOM_331": "Custom Category 331",
    "CUSTOM_332": "Custom Category 332",
    "CUSTOM_333": "Custom Category 333",
    "CUSTOM_334": "Custom Category 334",
    "CUSTOM_335": "Custom Category 335",
    "CUSTOM_336": "Custom Category 336",
    "CUSTOM_337": "Custom Category 337",
    "CUSTOM_338": "Custom Category 338",
    "CUSTOM_339": "Custom Category 339",
    "CUSTOM_34": "Custom category 34",
    "CUSTOM_340": "Custom Category 340",
    "CUSTOM_341": "Custom Category 341",
    "CUSTOM_342": "Custom Category 342",
    "CUSTOM_343": "Custom Category 343",
    "CUSTOM_344": "Custom Category 344",
    "CUSTOM_345": "Custom Category 345",
    "CUSTOM_346": "Custom Category 346",
    "CUSTOM_347": "Custom Category 347",
    "CUSTOM_348": "Custom Category 348",
    "CUSTOM_349": "Custom Category 349",
    "CUSTOM_35": "Custom category 35",
    "CUSTOM_350": "Custom Category 350",
    "CUSTOM_351": "Custom Category 351",
    "CUSTOM_352": "Custom Category 352",
    "CUSTOM_353": "Custom Category 353",
    "CUSTOM_354": "Custom Category 354",
    "CUSTOM_355": "Custom Category 355",
    "CUSTOM_356": "Custom Category 356",
    "CUSTOM_357": "Custom Category 357",
    "CUSTOM_358": "Custom Category 358",
    "CUSTOM_359": "Custom Category 359",
    "CUSTOM_36": "Custom category 36",
    "CUSTOM_360": "Custom Category 360",
    "CUSTOM_361": "Custom Category 361",
    "CUSTOM_362": "Custom Category 362",
    "CUSTOM_363": "Custom Category 363",
    "CUSTOM_364": "Custom Category 364",
    "CUSTOM_365": "Custom Category 365",
    "CUSTOM_366": "Custom Category 366",
    "CUSTOM_367": "Custom Category 367",
    "CUSTOM_368": "Custom Category 368",
    "CUSTOM_369": "Custom Category 369",
    "CUSTOM_37": "Custom category 37",
    "CUSTOM_370": "Custom Category 370",
    "CUSTOM_371": "Custom Category 371",
    "CUSTOM_372": "Custom Category 372",
    "CUSTOM_373": "Custom Category 373",
    "CUSTOM_374": "Custom Category 374",
    "CUSTOM_375": "Custom Category 375",
    "CUSTOM_376": "Custom Category 376",
    "CUSTOM_377": "Custom Category 377",
    "CUSTOM_378": "Custom Category 378",
    "CUSTOM_379": "Custom Category 379",
    "CUSTOM_38": "Custom category 38",
    "CUSTOM_380": "Custom Category 380",
    "CUSTOM_381": "Custom Category 381",
    "CUSTOM_382": "Custom Category 382",
    "CUSTOM_383": "Custom Category 383",
    "CUSTOM_384": "Custom Category 384",
    "CUSTOM_385": "Custom Category 385",
    "CUSTOM_386": "Custom Category 386",
    "CUSTOM_387": "Custom Category 387",
    "CUSTOM_388": "Custom Category 388",
    "CUSTOM_389": "Custom Category 389",
    "CUSTOM_39": "Custom category 39",
    "CUSTOM_390": "Custom Category 390",
    "CUSTOM_391": "Custom Category 391",
    "CUSTOM_392": "Custom Category 392",
    "CUSTOM_393": "Custom Category 393",
    "CUSTOM_394": "Custom Category 394",
    "CUSTOM_395": "Custom Category 395",
    "CUSTOM_396": "Custom Category 396",
    "CUSTOM_397": "Custom Category 397",
    "CUSTOM_398": "Custom Category 398",
    "CUSTOM_399": "Custom Category 399",
    "CUSTOM_40": "Custom category 40",
    "CUSTOM_400": "Custom Category 400",
    "CUSTOM_401": "Custom Category 401",
    "CUSTOM_402": "Custom Category 402",
    "CUSTOM_403": "Custom Category 403",
    "CUSTOM_404": "Custom Category 404",
    "CUSTOM_405": "Custom Category 405",
    "CUSTOM_406": "Custom Category 406",
    "CUSTOM_407": "Custom Category 407",
    "CUSTOM_408": "Custom Category 408",
    "CUSTOM_409": "Custom Category 409",
    "CUSTOM_41": "Custom category 41",
    "CUSTOM_410": "Custom Category 410",
    "CUSTOM_411": "Custom Category 411",
    "CUSTOM_412": "Custom Category 412",
    "CUSTOM_413": "Custom Category 413",
    "CUSTOM_414": "Custom Category 414",
    "CUSTOM_415": "Custom Category 415",
    "CUSTOM_416": "Custom Category 416",
    "CUSTOM_417": "Custom Category 417",
    "CUSTOM_418": "Custom Category 418",
    "CUSTOM_419": "Custom Category 419",
    "CUSTOM_42": "Custom category 42",
    "CUSTOM_420": "Custom Category 420",
    "CUSTOM_421": "Custom Category 421",
    "CUSTOM_422": "Custom Category 422",
    "CUSTOM_423": "Custom Category 423",
    "CUSTOM_424": "Custom Category 424",
    "CUSTOM_425": "Custom Category 425",
    "CUSTOM_426": "Custom Category 426",
    "CUSTOM_427": "Custom Category 427",
    "CUSTOM_428": "Custom Category 428",
    "CUSTOM_429": "Custom Category 429",
    "CUSTOM_43": "Custom category 43",
    "CUSTOM_430": "Custom Category 430",
    "CUSTOM_431": "Custom Category 431",
    "CUSTOM_432": "Custom Category 432",
    "CUSTOM_433": "Custom Category 433",
    "CUSTOM_434": "Custom Category 434",
    "CUSTOM_435": "Custom Category 435",
    "CUSTOM_436": "Custom Category 436",
    "CUSTOM_437": "Custom Category 437",
    "CUSTOM_438": "Custom Category 438",
    "CUSTOM_439": "Custom Category 439",
    "CUSTOM_44": "Custom category 44",
    "CUSTOM_440": "Custom Category 440",
    "CUSTOM_441": "Custom Category 441",
    "CUSTOM_442": "Custom Category 442",
    "CUSTOM_443": "Custom Category 443",
    "CUSTOM_444": "Custom Category 444",
    "CUSTOM_445": "Custom Category 445",
    "CUSTOM_446": "Custom Category 446",
    "CUSTOM_447": "Custom Category 447",
    "CUSTOM_448": "Custom Category 448",
    "CUSTOM_449": "Custom Category 449",
    "CUSTOM_45": "Custom category 45",
    "CUSTOM_450": "Custom Category 450",
    "CUSTOM_451": "Custom Category 451",
    "CUSTOM_452": "Custom Category 452",
    "CUSTOM_453": "Custom Category 453",
    "CUSTOM_454": "Custom Category 454",
    "CUSTOM_455": "Custom Category 455",
    "CUSTOM_456": "Custom Category 456",
    "CUSTOM_457": "Custom Category 457",
    "CUSTOM_458": "Custom Category 458",
    "CUSTOM_459": "Custom Category 459",
    "CUSTOM_46": "Custom category 46",
    "CUSTOM_460": "Custom Category 460",
    "CUSTOM_461": "Custom Category 461",
    "CUSTOM_462": "Custom Category 462",
    "CUSTOM_463": "Custom Category 463",
    "CUSTOM_464": "Custom Category 464",
    "CUSTOM_465": "Custom Category 465",
    "CUSTOM_466": "Custom Category 466",
    "CUSTOM_467": "Custom Category 467",
    "CUSTOM_468": "Custom Category 468",
    "CUSTOM_469": "Custom Category 469",
    "CUSTOM_47": "Custom category 47",
    "CUSTOM_470": "Custom Category 470",
    "CUSTOM_471": "Custom Category 471",
    "CUSTOM_472": "Custom Category 472",
    "CUSTOM_473": "Custom Category 473",
    "CUSTOM_474": "Custom Category 474",
    "CUSTOM_475": "Custom Category 475",
    "CUSTOM_476": "Custom Category 476",
    "CUSTOM_477": "Custom Category 477",
    "CUSTOM_478": "Custom Category 478",
    "CUSTOM_479": "Custom Category 479",
    "CUSTOM_48": "Custom category 48",
    "CUSTOM_480": "Custom Category 480",
    "CUSTOM_481": "Custom Category 481",
    "CUSTOM_482": "Custom Category 482",
    "CUSTOM_483": "Custom Category 483",
    "CUSTOM_484": "Custom Category 484",
    "CUSTOM_485": "Custom Category 485",
    "CUSTOM_486": "Custom Category 486",
    "CUSTOM_487": "Custom Category 487",
    "CUSTOM_488": "Custom Category 488",
    "CUSTOM_489": "Custom Category 489",
    "CUSTOM_49": "Custom category 49",
    "CUSTOM_490": "Custom Category 490",
    "CUSTOM_491": "Custom Category 491",
    "CUSTOM_492": "Custom Category 492",
    "CUSTOM_493": "Custom Category 493",
    "CUSTOM_494": "Custom Category 494",
    "CUSTOM_495": "Custom Category 495",
    "CUSTOM_496": "Custom Category 496",
    "CUSTOM_497": "Custom Category 497",
    "CUSTOM_498": "Custom Category 498",
    "CUSTOM_499": "Custom Category 499",
    "CUSTOM_50": "Custom category 50",
    "CUSTOM_500": "Custom Category 500",
    "CUSTOM_501": "Custom Category 501",
    "CUSTOM_502": "Custom Category 502",
    "CUSTOM_503": "Custom Category 503",
    "CUSTOM_504": "Custom Category 504",
    "CUSTOM_505": "Custom Category 505",
    "CUSTOM_506": "Custom Category 506",
    "CUSTOM_507": "Custom Category 507",
    "CUSTOM_508": "Custom Category 508",
    "CUSTOM_509": "Custom Category 509",
    "CUSTOM_51": "Custom category 51",
    "CUSTOM_510": "Custom Category 510",
    "CUSTOM_511": "Custom Category 511",
    "CUSTOM_512": "Custom Category 512",
    "CUSTOM_52": "Custom category 52",
    "CUSTOM_53": "Custom category 53",
    "CUSTOM_54": "Custom category 54",
    "CUSTOM_55": "Custom category 55",
    "CUSTOM_56": "Custom category 56",
    "CUSTOM_57": "Custom category 57",
    "CUSTOM_58": "Custom category 58",
    "CUSTOM_59": "Custom category 59",
    "CUSTOM_60": "Custom category 60",
    "CUSTOM_61": "Custom category 61",
    "CUSTOM_62": "Custom category 62",
    "CUSTOM_63": "Custom category 63",
    "CUSTOM_64": "Custom category 64",
    "CUSTOM_65": "Custom category 65",
    "CUSTOM_66": "Custom category 66",
    "CUSTOM_67": "Custom category 67",
    "CUSTOM_68": "Custom category 68",
    "CUSTOM_69": "Custom category 69",
    "CUSTOM_70": "Custom category 70",
    "CUSTOM_71": "Custom category 71",
    "CUSTOM_72": "Custom category 72",
    "CUSTOM_73": "Custom category 73",
    "CUSTOM_74": "Custom category 74",
    "CUSTOM_75": "Custom category 75",
    "CUSTOM_76": "Custom category 76",
    "CUSTOM_77": "Custom category 77",
    "CUSTOM_78": "Custom category 78",
    "CUSTOM_79": "Custom category 79",
    "CUSTOM_80": "Custom category 80",
    "CUSTOM_81": "Custom category 81",
    "CUSTOM_82": "Custom category 82",
    "CUSTOM_83": "Custom category 83",
    "CUSTOM_84": "Custom category 84",
    "CUSTOM_85": "Custom category 85",
    "CUSTOM_86": "Custom category 86",
    "CUSTOM_87": "Custom category 87",
    "CUSTOM_88": "Custom category 88",
    "CUSTOM_89": "Custom category 89",
    "CUSTOM_90": "Custom category 90",
    "CUSTOM_91": "Custom category 91",
    "CUSTOM_92": "Custom category 92",
    "CUSTOM_93": "Custom category 93",
    "CUSTOM_94": "Custom category 94",
    "CUSTOM_95": "Custom category 95",
    "CUSTOM_96": "Custom category 96",
    "CUSTOM_97": "Custom category 97",
    "CUSTOM_98": "Custom category 98",
    "CUSTOM_99": "Custom category 99",
    "CUSTOM_AUP_FREQUENCY": "Custom AUP Frequency (Days)",
    "CUSTOM_DNS_SERVER": "Custom DNS Server",
    "CUSTOM_OPTION": "Custom Option",
    "CUSTOM": "Custom",
    "CUSTOMER_SERVICE_ACCOUNT_EMAIL_PLACEHOLDER": "Eg. <customer-sa>@<customer-projectID-containing-sa>.iam.gserviceaccount.com",
    "CUSTOMER_SERVICE_ACCOUNT_EMAIL_TOOLTIP": "",
    "CUSTOMER_SERVICE_ACCOUNT_EMAIL": "Customer Service Account Email",
    "CUSTOMIZE_COLS": "Customize Columns",
    "CUSTOMIZE_COLUMNS": "Customize Columns",
    "CYPRUS_ASIA_NICOSIA": "Asia/Nicosia",
    "CYPRUS": "Cyprus",
    "CZECH_REPUBLIC_EUROPE_PRAGUE": "Europe/Prague",
    "CZECH_REPUBLIC": "Czech Republic",
    "CZECHIA": "Czechia",
    "DASHBOARD": "Dashboard",
    "Data Centers": "Data Centers",
    "DATA_CENTER": "Data Center",
    "DATA_CENTERS": "Data Centers",
    "DATA_COLLECTION": "Data Collection",
    "DATA_CONNECTION_FAIL": "SVPN data connection failed.",
    "DATA_TYPE": "Data Type",
    "DATACENTER": "DataCenter",
    "DAYS": "Days",
    "DC": "Public Service Edge",
    "DEDICATED_BANDWIDTH": "Dedicated Bandwidth",
    "DEFAUL_GATEWAY_IP_ADDRESS": "Default Gateway IP Address",
    "DEFAULT_AWS_REGIONS": "All regions",
    "DEFAULT_AZURE_REGIONS": "None",
    "DEFAULT_GATEWAY": "Default Gateway",
    "DEFAULT_GW": "Default Gateway",
    "DEFAULT_LEASE_TIME": "Default Lease Time (sec)",
    "DEFAULT_NAMESPACE": "DEFAULT",
    "DEFAULT_PREFIX": "Default Prefix",
    "DEFAULT_REGIONS": "All locations",
    "DEFAULT_ROUTE_CAN_NOT_BE_SET_AS_STATIC_ROUTE": "The default route could not be used as a static route.",
    "DEFAULT": "Default value",
    "DEFINITION": "Definition",
    "DELETE_5G_DEPLOYMENT_CONFIGURATION": "Are you sure you want to delete this deployment configuration? Deployments can only be deleted if they are not associated with any connector groups. These changes cannot be undone. ",
    "DELETE_5G_USER_PLANE": "Are you sure you want to delete this User Plane Function? These changes cannot be undone. ",
    "DELETE_ACCOUNT_DESCRIPTION": "Deleting the account will result in Zscaler removing all the access information for your account. You will have to provide the permissions again if you decide to add this account again.",
    "DELETE_ACCOUNT": "Delete Account",
    "DELETE_ACCOUNTS": "Delete Accounts",
    "DELETE_ADMIN_MESSAGE": "Do you want to remove this admin from the user list as well?{1}This action {2}cannot be undone.{3}",
    "DELETE_API_KEY_CONFIRMATION_MESSAGE": "Deleting the API key immediately invalidates it. This cannot be undone.",
    "DELETE_API_KEY_CONFIRMATION_TITLE": "Delete API Key",
    "DELETE_API_KEY_TOOLTIP": "Delete the API key",
    "DELETE_APPLICATION": "Delete Application",
    "DELETE_BC_CONFIRMATION": "Are you sure you want to delete the following Branch Connector? The changes cannot be undone.",
    "DELETE_BC_GROUP_CONFIRMATION_ALERT": "This does not delete the VM. Please make sure you delete the resources separately.",
    "DELETE_BC_GROUP_CONFIRMATION": "Are you sure you want to delete the following Branch Connector Group? The changes cannot be undone.",
    "DELETE_BC_GROUP": "Delete Branch Connector Group",
    "DELETE_CC_CONFIRMATION": "Are you sure you want to delete the following Cloud Connector? The changes cannot be undone.",
    "DELETE_CC_GROUP_CONFIRMATION_ALERT": "This does not delete the VM in the public cloud. Please make sure you delete the resources separately.",
    "DELETE_CC_GROUP_CONFIRMATION": "Are you sure you want to delete the following Cloud Connector Group? The changes cannot be undone.",
    "DELETE_CC_GROUP": "Delete Cloud Connector Group",
    "DELETE_CONFIRMATION_MESSAGE": "Please confirm that you want to delete the resource(s).",
    "DELETE_CONFIRMATION_MESSAGE1": "Are you sure you want to delete this resource?",
    "DELETE_CONFIRMATION_MESSAGE2": "Are you sure you want to delete this resources?",
    "DELETE_CONFIRMATION": "Delete Confirmation",
    "DELETE_DATA_OLLECTION_TEXT": "Deleting the account will result in Zscaler removing all the access information for your account. You will have to provide the permissions again if you decide to add this account again.",
    "DELETE_DATA_OLLECTION": "Delete Data Collection",
    "DELETE_GROUP_CONFIRMATION": "Delete Group Confirmation",
    "DELETE_GROUP_MESSAGE_WITH_CC_GROUP": "This account group is associated with {1} account(s) and {2} cloud connector group(s). Deleting this account group will result in the {2} Cloud Connector group(s) to not be able to use workload tags from the {1} account(s).",
    "DELETE_GROUP_MESSAGE": "{1}Are you sure you want to delete this group?{2}\n\nDeleting this group will remove this group from all the relevant policies. Ensure that you have reviewed the impact before deleting the group.",
    "DELETE_GROUP": "Delete Group",
    "DELETE_INTERFACE_TEXT": "Are you sure you want to delete the interface configuration?\n\nThis action cannot be undone.",
    "DELETE_INTERFACE": "Delete Interface",
    "DELETE_PORT_TEXT": "Are you sure you want to delete the port and all it's interface configuration?\n\nThis action cannot be undone.",
    "DELETE_PORT": "Delete Port",
    "DELETE_TENANT_DESCRIPTION": "Deleting the tenant will result in Zscaler removing all the access information for your tenant. You will have to provide the permissions again if you decide to add this tenant again.",
    "DELETE_TENANT": "Delete Tenant",
    "DELETE_THIS_ITEM": "Delete this item.",
    "DELETE_VDI_GROUP_TEXT": "Deleting the device group will result in Zscaler removing all the  information related to this group. You will have to create the group again if you decide to add this account again.",
    "DELETE_VDI_GROUP": "Delete VDI Group",
    "DELETE_ZERO_TRUST_GATEWAY": "Delete Zero Trust Gateway",
    "DELETE_ZTG_DESCRIPTION": "Deleting the Zero Trust Gateway will result in Zscaler removing all the access information for your gateway. You will have to provide the permissions again if you decide to add this gateway again.",
    "DELETE": "Delete",
    "DELETING": "Deleting",
    "DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA": "Africa/Kinshasa",
    "DENIED": "Denied",
    "DENMARK_EUROPE_COPENHAGEN": "Europe/Copenhagen",
    "DENMARK": "Denmark",
    "DEPARTMENT": "Department",
    "DEPLOY_AS_GATEWAY": "Deploy as Gateway",
    "DEPLOY_NSS_VIRTUAL_APPLIANCE": "Deploy NSS Virtual Appliance",
    "DEPLOYED_FILTERED": "Deployed (Filtered)",
    "DEPLOYED_OTHER_REGION": "Deployed Other Regions",
    "DEPLOYED": "Deployed",
    "DEPLOYMENT_CONFIGURATION_NAME": "Deployment Configuration Name",
    "DEPLOYMENT_CONFIGURATION_WITH_ZPA": "Starter Deployment Template with Load Balancer",
    "DEPLOYMENT_CONFIGURATION": "Starter Deployment Template",
    "DEPLOYMENT_DETAILS": "Deployment Details",
    "DEPLOYMENT_NAME": "Deployment Name",
    "DEPLOYMENT_STATUS": "Deployment Status",
    "DEPLOYMENT_TEMPLATES_DEPRECATED": "The deployment templates are available on public Zscaler {0}GitHub{1} page at the links mentioned below. The changes are tracked in those pages. To learn more, see the {2}Zscaler Help Portal.{3}",
    "DEPLOYMENT_TEMPLATES": "Deployment Templates",
    "DEPLOYMENT_TYPE": "Deployment Type",
    "DESCRIPTION_MAX_LIMIT_ERROR": "This field cannot contain more than 10240 characters",
    "DESCRIPTION_OPTIONAL": "Description Optional",
    "DESCRIPTION_PARENTHESIS_OPTIONAL": "Description (Optional)",
    "DESCRIPTION": "Description",
    "DESELECT_ALL": "Deselect All",
    "DESIRED_CAPACITY": "Desired Capacity",
    "DESTINATION_ADDRESSES": "Destination Addresses",
    "DESTINATION_COUNTRIES": "Destination Countries",
    "DESTINATION_COUNTRY": "Destination Country",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_ACCDRESSES": "Destination FQDN / Domains",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_GROUP": "Destination FQDN / Domains Group",
    "DESTINATION_GROUPS": "DESTINATION GROUPS",
    "DESTINATION_IP_AND_FQDN_ACCDRESSES": "Destination IP Address / FQDN",
    "DESTINATION_IP_AND_FQDN_GROUPS": "Destination IP / FQDN Group",
    "DESTINATION_IP_AND_FQDN": "Destination IP / FQDN",
    "DESTINATION_IP_GROUP": "Destination Group",
    "DESTINATION_IP_GROUPS": "Destination IP Groups",
    "DESTINATION_IP": "Destination IP",
    "DESTINATION_IPV4_GROUPS": "Destination IPv4 Groups",
    "DESTINATION_STATUS": "Destination Status",
    "DESTINATION_WORKLOAD_GROUPS": "Destination Workload Groups",
    "DESTINATION": "Destination",
    "DEVICE_APP_VER": "Device App Version",
    "DEVICE_CRITERIA": "Device Criteria",
    "DEVICE_DETAILS": "Device Details",
    "DEVICE_GROUP_TYPE": "Device Group Type",
    "DEVICE_HOST_NAME": "Device Host Name",
    "DEVICE_ID": "Device Id",
    "DEVICE_INFO": "Device Info",
    "DEVICE_METRICS": "Resource",
    "DEVICE_MODEL": "Device Model",
    "DEVICE_NAME": "Device Name",
    "DEVICE_OS_TYPE": "Device OS Type",
    "DEVICE_OS_VER": "Device OS Version",
    "DEVICE_OWNER": "Device Owner",
    "DEVICE_PLATFORM": "Device Platform",
    "DEVICE_PORT": "Device Port",
    "DEVICE_SELECTION": "Device Selection",
    "DEVICE_SERIAL_NO": "Device Serial No",
    "DEVICE_SERIAL_NUMBER": "Device Serial Number",
    "DEVICE_TYPE": "Device Type",
    "DEVICES_CRITERIA_TEXT": "Select the criteria that will be used to group the VDI devices.",
    "DEVICES_CRITERIA": "Devices Criteria",
    "DEVICES": "Devices",
    "DEVO": "Devo",
    "DHCP_ADDRESS_RANGE": "DHCP Address Range",
    "DHCP_DESC": "The DHCP protocol is used to configure automatically the network parameters of a station",
    "DHCP_MANAGEMENT_IP": "DHCP Management IP",
    "DHCP_OPTIONS": "DHCP Options",
    "DHCP_SERVER": "DHCP Server",
    "DHCP_SERVICE_IP": "DHCP Service IP",
    "DHCP": "DHCP",
    "DINING_AND_RESTAURANT": "Dining/Restaurant",
    "DIRECT_THROUGHPUT_KBPS_SESSION": "Direct (Throughput kbps / Session)",
    "DIRECT": "Direct",
    "DIRECTION": "TS Direction",
    "DIRECTORY_ID": "Directory Id",
    "DISABLE_BRANCH_CONNECTOR_CONFIRMATION": "Are you sure you want to disable this Branch Connector?",
    "DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Are you sure you want to disable this Branch Connector Group? This would disable all {0} Branch Connectors that belong to this group",
    "DISABLE_BRANCH_CONNECTOR_GROUP": "Disable Branch Connector Group",
    "DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Are you sure you want to disable all {0} selected Branch Connectors?",
    "DISABLE_BRANCH_CONNECTOR_SELECTED": "Disable All Selected Branch Connectors",
    "DISABLE_BRANCH_CONNECTOR": "Disable Branch Connector",
    "DISABLE_CLOUD_CONNECTOR_CONFIRMATION": "Are you sure you want to disable this Cloud Connector?",
    "DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Are you sure you want to disable this Cloud Connector Group? This would disable all {0} Cloud Connectors that belong to this group",
    "DISABLE_CLOUD_CONNECTOR_GROUP": "Disable Cloud Connector Group",
    "DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Are you sure you want to disable all {0} selected Cloud Connectors?",
    "DISABLE_CLOUD_CONNECTOR_SELECTED": "Disable All Selected Cloud Connectors",
    "DISABLE_CLOUD_CONNECTOR": "Disable Cloud Connector",
    "DISABLE_DATA_COLLECTION_DESCRIPTION": "Disabling the sync will stop Zscaler from fetching the latest tag information. And will exclude the resources from this account from being included in workload groups.",
    "DISABLE_DATA_COLLECTION": "Disable Data Collection",
    "DISABLE_POLICY_INFORMATION": "Disable Forwarding Information",
    "DISABLE_TIPS_MESSAGE": "My Profile, to disable policy information.",
    "DISABLE": "Disable",
    "DISABLED": "Disabled",
    "DISABLING": "Disabling",
    "DISCOVERY_SERVICE_STATUS": "Discovery Service Status",
    "DISCUSSION_FORUMS": "Discussion Forums",
    "DISK_STORAGE": "Disk Storage",
    "DISMISS": "Dismiss",
    "DISPLAY": "Display",
    "DJIBOUTI_AFRICA_DJIBOUTI": "Africa/Djibouti",
    "DJIBOUTI": "Djibouti",
    "DNS_ACTIONS": "Actions",
    "DNS_ACTIVITY": "DNS Activity",
    "DNS_APPLICATION_CATEGORIES": "DNS Tunnel & Network App Categories",
    "DNS_APPLICATION_CATEGORY": "DNS Application Category",
    "DNS_APPLICATION_GROUP": "DNS Application Group",
    "DNS_APPLICATION": "DNS Application",
    "DNS_BLOCKED_TRAFFIC_OVERVIEW": "Blocked DNS Traffic Overview",
    "DNS_CACHE": "DNS Cache",
    "DNS_CONTROL_RECOMMENDED_POLICY": "Recommended DNS Control Policy",
    "DNS_CONTROL_TIPS_DESC": "You can define rules that control DNS requests and responses.",
    "DNS_CONTROL_TIPS_TITLE": "Configure DNS Control Policy",
    "DNS_CONTROL": "DNS Control",
    "DNS_DESC": " The DNS protocol is used to translate internet names (www.site.com) into IP addresses and vice versa",
    "DNS_DESTINATION": "Destination",
    "DNS_DETAILS": "DNS Details",
    "DNS_ERROR_CODE": "DNS Error Code",
    "DNS_ERROR_STATUS": "DNS Error Status",
    "DNS_FILTERING_RULE": "DNS Filtering Rule",
    "DNS_FILTERING": "DNS Filtering",
    "DNS_FILTERS": "DNS Filters",
    "DNS_GATEWAY": "DNS Gateway",
    "DNS_INSIGHTS": "DNS Insights",
    "DNS_IPV6_CHANGE": "Allow correct handling of ipv6 requests to dns",
    "DNS_MONITOR": "DNS Monitor",
    "DNS_NETWORK_APPLICATION": "DNS Tunnels & Network Apps",
    "DNS_NW_APP_CATEGORY": "DNS Tunnel & Network App Categories",
    "DNS_NW_APP": "DNS Tunnels & Network Apps",
    "DNS_OVER_HTTPS": "DNS Over HTTPS Services",
    "DNS_OVERVIEW": "DNS Overview",
    "DNS_POLICIES": "DNS Policies",
    "DNS_POLICY": "DNS Policy",
    "DNS_REQ_RESP_ACTION": "Action",
    "DNS_REQ_TYPE": "DNS Request Type",
    "DNS_REQUEST_TYPE": "DNS Request Type",
    "DNS_REQUEST_TYPES": "DNS Request Types",
    "DNS_RES_TYPE": "DNS Response Type",
    "DNS_RESOLVED_BY_ZPA": "Resolved by ZPA",
    "DNS_RESOLVER": "Resolver",
    "DNS_RESPONSE_CODES": "DNS Response Codes",
    "DNS_RESPONSE_TYPE": "DNS Response Type",
    "DNS_RESPONSE": "DNS Response",
    "DNS_RESPONSES": "DNS Responses",
    "DNS_RULE_NAME_DEFAULT": "DNS_{0}",
    "DNS_RULE_NAME": "DNS Rule Name",
    "DNS_RULE": "Rule Name",
    "DNS_SERVER_IP_ADDRESS": "DNS Server IP Address",
    "DNS_SERVER_IP_ADDRESS1": "DNS Server IP Address 1",
    "DNS_SERVER_IP_ADDRESS2": "DNS Server IP Address 2",
    "DNS_SERVER_IP_ADDRESSES": "DNS Server IP Addresses",
    "DNS_SERVER_IP_GROUPS": "DNS Server IP Groups",
    "DNS_SERVER_ONE": "DNS Server 1",
    "DNS_SERVER_TWO_OPTIONAL": "DNS Server 2 (optional)",
    "DNS_SERVER_TWO": "DNS Server 2",
    "DNS_SERVER": "DNS Server",
    "DNS_SERVICES": "DNS Services",
    "DNS_SOURCE": "Source",
    "DNS_TCP_PORTS_DNS_SPECIFIC": "DNS (TCP) Ports for DNS-Specific Rules",
    "DNS_TIMEOUT": "DNS resolution timeout",
    "DNS_TOP_BLOCKED_BY_LOCATION": "Blocked Traffic By Location",
    "DNS_TOP_BLOCKED_BY_RULE": "Blocked Traffic By Rule",
    "DNS_TOP_BLOCKED_BY_USER": "Blocked Traffic By User",
    "DNS_TRAFFIC_BY_DEPARTMENT": "DNS Traffic By Department",
    "DNS_TRAFFIC_BY_LOCATION": "Traffic By Location",
    "DNS_TRAFFIC_BY_USER": "Traffic By User",
    "DNS_TRAFFIC_OVERVIEW": "DNS Overall Traffic Overview",
    "DNS_TRANSACTION_POLICY": "DNS Transaction Policy",
    "DNS_TRANSACTION_RULE": "DNS Transaction Rule",
    "DNS_TRANSACTION_TREND": "DNS Transactions Trend",
    "DNS_TRANSACTION": "Transaction",
    "DNS_UDP_PORTS_DNS_SPECIFIC": "DNS (UDP) Ports for DNS-Specific Rules",
    "DNS": "DNS",
    "DNSLOG": "DNS Logs",
    "DNSREQ_A": "A Host Address",
    "DNSREQ_AAAA": "IP6 Address",
    "DNSREQ_AFSDB": "For AFS Data Base location",
    "DNSREQ_CNAME": "The canonical name for an alias",
    "DNSREQ_DNSKEY": "DNS public key",
    "DNSREQ_DS": "Delegation Signer",
    "DNSREQ_HINFO": "Host information",
    "DNSREQ_HIP": "Host Identity Protocol",
    "DNSREQ_ISDN": "For ISDN address",
    "DNSREQ_LOC": "Location Information",
    "DNSREQ_MB": "A mailbox domain name",
    "DNSREQ_MG": "A mail group member",
    "DNSREQ_MINFO": "Mailbox or mail list information",
    "DNSREQ_MR": "A mail rename domain name",
    "DNSREQ_MX": "Mail exchange",
    "DNSREQ_NAPTR": "Naming Authority Pointer",
    "DNSREQ_NS": "An Authoritative Name Server",
    "DNSREQ_NSEC": "DNS Security Extensions",
    "DNSREQ_PTR": "A domain name pointer",
    "DNSREQ_RP": "For Responsible Person",
    "DNSREQ_RT": "For Route Through",
    "DNSREQ_SOA": "Marks the start of a zone of authority",
    "DNSREQ_SRV": "Server Selection",
    "DNSREQ_TXT": "Text strings",
    "DNSREQ_UNKNOWN": "DNS type not mapped by ZS firewall",
    "DNSREQ_WKS": "A well known service description",
    "DNSRES_CNAME": "Response type is CNAME",
    "DNSRES_IPV4": "Response type is IPV4",
    "DNSRES_IPV6": "Response type is IPV6",
    "DNSRES_SRV_CODE": "Response type has server error code set",
    "DNSRES_ZSCODE": "Response type has Zscaler custom code set",
    "DOES_NOT_CONTAINS": "Does Not Contain",
    "DOES_NOT_ENDS_WITH": "Does Not End With",
    "DOES_NOT_STARTS_WITH": "Does Not Start With",
    "DOHTTPS_RULE": "DNS Over HTTPS",
    "DOMAIN_CATEGORY": "Domain Category",
    "DOMAIN_NAME": "Domain Name",
    "DOMAIN": "Domain",
    "DOMAINS": "Domains",
    "DOMINICA_AMERICA_DOMINICA": "America/Dominica",
    "DOMINICA": "Dominica",
    "DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO": "America/Santo Domingo",
    "DOMINICAN_REPUBLIC": "Dominican Republic",
    "DONE": "Done",
    "DONT_SHOW_AGAIN": "Don't show again",
    "DOWN": "Down",
    "DOWNLOAD_AWS_CLOUDFORMATION_TEMPLATE": "Download AWSCloudformation Template",
    "DOWNLOAD_CERTIFICATE": "Download Certificate",
    "DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION": "Download CloudFormation Template for Later Execution",
    "DOWNLOAD_CSV": "Download (.csv)",
    "DOWNLOAD_ERROR": "Not able to download upgrade from Zscaler cloud. Cloud Connector in healthy state",
    "DOWNLOAD_MBPS": "Download (Mbps)",
    "DOWNLOAD_MIB_FILES": "Download MIB Files",
    "DOWNLOAD_PROGRESS": "Download Progress",
    "DOWNLOAD_SCRIPT": "Download Script",
    "DOWNLOAD": "Download",
    "DPDRCV": "DPD Received",
    "DR_CONGO": "DR Congo",
    "DROP": "Drop",
    "DSTN_DOMAIN": "Wildcard Domain",
    "DSTN_FQDN": "FQDN",
    "DSTN_IP": "IP Address",
    "DSTN_OTHER": "Other",
    "DSTN_WILDCARD_FQDN": "Wildcard Domain",
    "DUPLICATE_IP_ADDRESS": "This IP Address is already in use",
    "DUPLICATE_IP_ADDRESSES": "Duplicate IP Addresses",
    "DUPLICATE_ITEM": "The given name is already in use",
    "DUPLICATE_VLAN_ID": "Duplicated VLAN ID.",
    "DYNAMIC_DNS": "Dynamic DNS Host",
    "DYNAMIC_LOCATION_GROUPS": "Dynamic Location Groups",
    "DYNAMIC": "Dynamic",
    "EASTASIA": "(Asia Pacific) East Asia",
    "EASTASIASTAGE": "(Asia Pacific) East Asia (Stage)",
    "EASTUS": "(US) East US",
    "EASTUS2": "(US) East US 2",
    "EASTUS2EUAP": "(US) East US 2 EUAP",
    "EASTUS2STAGE": "(US) East US 2 (Stage)",
    "EASTUSSTAGE": "(US) East US (Stage)",
    "EBS_STORAGE": "EBS Storage",
    "EC_ACC_ID": "AWS Account ID",
    "EC_AVAILABILITY_ZONE": "Availability Zone",
    "EC_AWS_AVAILABILITY_ZONE": "AWS Availability Zone",
    "EC_AWS_REGION": "AWS Region",
    "EC_AZURE_AVAILABILITY_ZONE": "Azure Availability Zone",
    "EC_DEVICE_APP_VERSION": "Device App Version",
    "EC_DEVICE_HOSTNAME": "Device Hostname",
    "EC_DEVICE_ID": "Device Name",
    "EC_DEVICE_OS_TYPE": "Device OS Type",
    "EC_DEVICE_TYPE": "Device Type",
    "EC_DNS_GW_FLAG": "DNS Gateway Flag",
    "EC_DNS_GW_NAME": "DNS Gateway Name",
    "EC_DNS": "CC DNS",
    "EC_DNSLOG": "DNS Logs",
    "EC_EVENTLOG": "Event",
    "EC_FORWARDING_TYPE": "Forwarding Type",
    "EC_FW_RULE": "Forwarding Rule",
    "EC_GROUP": "Cloud Connector Group",
    "EC_INSTANCE_NAME": "Cloud Connector Instance",
    "EC_INSTANCE": "Cloud Connector Instance",
    "EC_PLATFORM": "Platform",
    "EC_PROJECT_ID": "GCP Project ID",
    "EC_RDRRULESLOT": "Traffic Forwarding Rules",
    "EC_SELFRULESLOT": "Log and Control Forwarding Rules",
    "EC_SOURCE_IP": "Cloud Connector Source IP",
    "EC_SOURCE_PORT": "Cloud Connector Source Port",
    "EC_SUBSCRIPTION_ID": "Azure Subscription ID",
    "EC_TRAFFIC_DIRECTION": "Traffic Direction",
    "EC_TRAFFIC_TYPE": "Traffic Type",
    "EC_TS_DIRECTION": "TS Direction",
    "EC_UI": "Cloud Connector UI",
    "EC_VM": "Cloud Connector VM",
    "EC_VMNAME": "Cloud Connector VM Name",
    "EC2_INSTANCE_TYPE": "EC2 Instance Type",
    "ECDIRECTSCTPXFORM": "Direct with SCTP Translation",
    "ECHO_DESC": "Echo Protocol is a service in the Internet Protocol Suite defined in RFC 862. It was originally proposed for testing and measurement of round-trip times in IP networks.",
    "ECHO": "Echo",
    "ECHOREP": "Echo reply",
    "ECHOREQ": "Echo request",
    "ECHOSIGN": "AdobeEchoSign",
    "ECLOG": "Session Logs",
    "ECSELF": "GW for Connector originated traffic",
    "ECUADOR_AMERICA_GUAYAQUIL": "America/Guayaquil",
    "ECUADOR_PACIFIC_GALAPAGOS": "Pacific/Galapagos",
    "ECUADOR": "Ecuador",
    "ECZPA": "ZPA",
    "ECZPASCTPXFORM": "ZPA with SCTP Translation",
    "ECZPAXSCTPXFORM": "Tunnel to Connector ZPA with SCTP transform",
    "EDGE_CONNECTOR_ADMIN_MANAGEMENT": "Administrator Management",
    "EDGE_CONNECTOR_ADMIN": "Cloud Connector Admin",
    "EDGE_CONNECTOR_CCA_DEVICE": "CCA",
    "EDGE_CONNECTOR_CLOUD_PROVISIONING": "Cloud Connector Provisioning",
    "EDGE_CONNECTOR_DASHBOARD": "Dashboard",
    "EDGE_CONNECTOR_FORWARDING": "Forwarding (Traffic, DNS & Logs)",
    "EDGE_CONNECTOR_LOCATION_MANAGEMENT": "Location Management",
    "EDGE_CONNECTOR_NSS_CONFIGURATION": "NSS Logging",
    "EDGE_CONNECTOR_POLICY_CONFIGURATION": "Policy Configuration",
    "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Public Cloud Config Management",
    "EDGE_CONNECTOR_TEMPLATE": "Template (Location & Provisioning)",
    "EDGE_CONNECTOR_VM_SIZE": "VM Size",
    "EDGE_ONLY": "Edge Only",
    "EDGECONNECTOR_NAME": "Cloud Connector Name",
    "EDIT_5G_DEPLOYMENT": "Edit Deployment Configuration",
    "EDIT_API_KEY_TOOLTIP": "Edit the API key",
    "EDIT_APPLIANCE": "Edit Appliance",
    "EDIT_AWS_ACCOUNT": "Edit AWS Account",
    "EDIT_AWS_CLOUD_ACCOUNT": "Edit AWS Cloud Account",
    "EDIT_AWS_GROUP": "Edit AWS Group",
    "EDIT_AZURE_CLOUD_ACCOUNT": "Edit Azure Cloud Account",
    "EDIT_AZURE_TENANT": "Edit Azure Tenant",
    "EDIT_BC_PROVISIONING_TEMPLATE": "Edit Branch Connector Provisioning Template",
    "EDIT_CLOUD_APP_PROVIDER": "Edit Cloud App Provider",
    "EDIT_CLOUD_CONNECTOR_ADMIN": "Edit Cloud Connector Admin",
    "EDIT_CLOUD_CONNECTOR_ROLE": "Edit Cloud Connector Role",
    "EDIT_CLOUD_CONNECTOR": "Edit Cloud Connector",
    "EDIT_CLOUD_CONNECTORS": "Edit Connectors",
    "EDIT_CLOUD_SERVICE_API_KEY": "Edit Cloud Service API Key",
    "EDIT_CONFIRMATION_DEPLOYED_GATEWAY": "This device is in Deployed State. Any config changes may impact traffic. Are you sure you want to proceed?",
    "EDIT_CONFIRMATION_PREDEFINED_RULE": "This pre-defined rule is only applicable for BC Groups/Locations in Gateway Mode.",
    "EDIT_CONFIRMATION": "Edit Confirmation",
    "EDIT_CONNECTORS": "Edit Connectors",
    "EDIT_DESTINATION_IP_GROUP": "Edit Destination IP Group",
    "EDIT_DNS_GATEWAY": "Edit DNS Gateway",
    "EDIT_DNS_POLICIES": "Edit DNS Filtering Rule",
    "EDIT_DYNAMIC_VDI_GROUP": "Edit Dynamic VDI Group",
    "EDIT_EC_NSS_CLOUD_FEED": "Edit NSS Cloud Feed",
    "EDIT_EC_NSS_FEED": "Edit NSS Feed",
    "EDIT_EC_NSS_SERVER": "Edit NSS Server",
    "EDIT_EDGECONNECTOR": "Edit Cloud Connector",
    "EDIT_GCP_TENANT": "Edit Google Cloud Account",
    "EDIT_IP_POOL": "Edit IP Pool",
    "EDIT_LOCATION_TEMPLATE": "Edit Location Template",
    "EDIT_LOCATIONS": "Edit Locations",
    "EDIT_LOG_AND_CONTROL_FORWARDING_RULE": "Edit Log and Control Forwarding Rule",
    "EDIT_LOG_AND_CONTROL_GATEWAY": "Edit Log And Control Gateway",
    "EDIT_NETWORK_SERVICE_GROUP": "Add Network Service Group",
    "EDIT_NETWORK_SERVICE": "Edit Network Service",
    "EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE": "Modifying the API key immediately invalidates the existing key. You must replace any references to the old key with the new one.",
    "EDIT_PHYSICAL_BRANCH_DEVICE": "Edit Physical Branch Device",
    "EDIT_PROVISIONING_TEMPLATE": "Edit Cloud Connector Provisioning Template",
    "EDIT_SOURCE_IP_GROUP": "Edit Source IP Group",
    "EDIT_TRAFFIC_FWD_POLICIES": "Edit Traffic Forwarding Rules",
    "EDIT_UPF": "Edit User Plane Function",
    "EDIT_VDI_AGENT_FORWARDING_PROFILE": "Edit VDI Forwarding Profile",
    "EDIT_VDI_TEMPLATE": "Edit VDI Template",
    "EDIT_VIRTUAL_BRANCH_DEVICE": "Edit Virtual Branch Device",
    "EDIT_ZERO_TRUST_GATEWAY": "Edit Zero Trust Gateway",
    "EDIT_ZIA_GATEWAY": "Edit ZIA Gateway",
    "EDIT_ZT_DEVICE": "Edit ZT Device",
    "EDIT": "Edit",
    "EGRESS_DETAILS": "Egress Details",
    "EGYPT_AFRICA_CAIRO": "Africa/Cairo",
    "EGYPT": "Egypt",
    "EITHER_REQ_RESP_BLOCK": "Block",
    "EITHER_REQ_RESP_REDIRECT_NO_BLOCK": "Redirect",
    "EL_SALVADOR_AMERICA_EL_SALVADOR": "America/El Salvador",
    "EL_SALVADOR": "El Salvador",
    "EMAIL_HOST": "Webmail",
    "EMAIL": "Email",
    "EMPTY_RESP": "DNS response no error but with empty answer section",
    "ENABLE_AUP": "Enable AUP",
    "ENABLE_BRANCH_CONNECTOR_CONFIRMATION": "Are you sure you want to enable this Branch Connector?",
    "ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Are you sure you want to enable this Branch Connector Group? This would enable all {0} Branch Connectors that belong to this group",
    "ENABLE_BRANCH_CONNECTOR_GROUP": "Enable Branch Connector Group",
    "ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Are you sure you want to enable all {0} selected Branch Connectors?",
    "ENABLE_BRANCH_CONNECTOR_SELECTED": "Enable All Selected Branch Connectors",
    "ENABLE_BRANCH_CONNECTOR": "Enable Branch Connector",
    "ENABLE_CAUTION": "Enable Caution",
    "ENABLE_CLOUD_CONNECTOR_CONFIRMATION": "Are you sure you want to enable this Cloud Connector?",
    "ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Are you sure you want to enable this Cloud Connector Group? This would enable all {0} Cloud Connectors that belong to this group",
    "ENABLE_CLOUD_CONNECTOR_GROUP": "Enable Cloud Connector Group",
    "ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Are you sure you want to enable all {0} selected Cloud Connectors?",
    "ENABLE_CLOUD_CONNECTOR_SELECTED": "Enable All Selected Cloud Connectors",
    "ENABLE_CLOUD_CONNECTOR": "Enable Cloud Connector",
    "ENABLE_DATA_COLLECTION_DESCRIPTION": "Enabling the sync will allow Zscaler to fetch the latest tag information. And will include the resources from this account in workload groups.",
    "ENABLE_DATA_COLLECTION": "Enable Data Collection",
    "ENABLE_FULL_ACCESS": "Enable Full Access",
    "ENABLE_GEO_IP_LOOKUP": "Enable GEO IP lookup",
    "ENABLE_IPS_CONTROL": "Enable IPS Control",
    "ENABLE_MOBILE_APP": "Executive Insights App Access",
    "ENABLE_POLICY_INFORMATION": "Enable Forwarding Information",
    "ENABLE_SSL_INSPECTION": "Enable SSL Inspection",
    "ENABLE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Enforce Surrogate IP for Known Browsers",
    "ENABLE_USER_IP_MAPPING": "Enable IP Surrogate",
    "ENABLE_VIEW_ONLY_ACCESS": "Enable View-Only Access",
    "ENABLE_XFF_FORWARDING": "Enable XFF Forwarding",
    "ENABLE": "Enable",
    "ENABLED": "Enabled",
    "ENABLING": "Enabling",
    "ENCR_WEB_CONTENT": "Custom Encrypted Content",
    "ENCRYPTED_DTLS": "DTLS",
    "END_TIME": "End Time",
    "END_USER_AUTHENDICATION": "End User Authentication",
    "ENDPOINT_ID": "Endpoint ID",
    "ENDPOINT_SERVICE_NAME": "Endpoint Service Name",
    "ENDPOINT_SEVICE_NAME": "Endpoint Service Name",
    "ENDPOINTS_SEVICE_NAME": "Endpoints Service Name",
    "ENDPOINTS": "Endpoints",
    "ENDS_WITH": "Ends With",
    "ENFORCE_AUTHENTICATION": "Enforce Authentication",
    "ENFORCE_BAND_WIDTH_CONTROL": "Enforce Bandwidth Control",
    "ENFORCE_FIREWALL_CONTROL": "Enforce Firewall Control",
    "ENFORCE_IPS_CONTROL": "Enable IPS Control",
    "ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Enforce Surrogate IP for Known Browsers",
    "ENFORCE_ZAPP_SSL_SETTING": "Enforce ZApp SSL Setting",
    "ENROLLED_DEVICE_APP_VERSION": "Enrolled Device App Version",
    "ENSURE_ALL_INFORMATION_IS_CORRECT": "Ensure all the information below is correct before creating this Cloud Connector Provisioning Template.",
    "ENTER_AWS_ACCOUNT_ID": "Enter AWS Account ID...",
    "ENTER_CC_ROLE_NAME": "Enter Cloud Connector Role Name...",
    "ENTER_CLOUD_WATCH_GROUP_ARN": "Enter Cloud Watch Group ARN",
    "ENTER_CUSTOM_OPTION_CODE": "Enter code. Eg.42",
    "ENTER_CUSTOM_OPTION_NAME": "Enter custom option name.",
    "ENTER_DESCRIPTION_HERE": "Enter Description (Optional)",
    "ENTER_DESCRIPTION": "Enter Description...",
    "ENTER_DEVICE_NAME": "Enter Device Name",
    "ENTER_HEADERS_PARAMETERS": "Enter headers parameters",
    "ENTER_HOSTNAME_PREFIX": "Enter Hostname Prefix",
    "ENTER_IP_ADDRESS_OR_FQDN": "Enter an IP Address or FQDN",
    "ENTER_IP_ADDRESS": "Enter IP Address...",
    "ENTER_LOG_INFO_TYPE": "Enter Log Info Type",
    "ENTER_MTU": "Enter MTU...",
    "ENTER_NAME_HERE": "Enter Name Here",
    "ENTER_NAME": "Enter Name...",
    "ENTER_NAMESPACE": "Enter Namespace",
    "ENTER_NUMBER": "Enter Number",
    "ENTER_ROLE_NAME": "Enter Role Name...",
    "ENTER_TEXT": "Enter Text...",
    "ENTER_THE_VALUE": "Please enter a value",
    "ENTER_URL": "Enter URL",
    "ENTERTAINMENT": "Entertainment",
    "ENTITLEMENT_STATUS_TOOLTIP": "Gateway entitlement is displayed as the number of Availability Zones (AZ) the account is entitled to and the current usage. Each gateway uses two or more AZs. The number of AZs used by a gateway is chosen while creating a new gateway.",
    "ENTITLEMENT_STATUS": "Entitlement Status",
    "EQUATORIAL_GUINEA_AFRICA_MALABO": "Africa/Malabo",
    "EQUATORIAL_GUINEA": "Equatorial Guinea",
    "ERITREA_AFRICA_ASMARA": "Africa/Asmara",
    "ERITREA": "Eritrea",
    "ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER": "Google Project Id may not be used in combination with AWS Account Id",
    "ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "Azure Subscription Id may not be used in combination with AWS Account Id",
    "ERROR_API_MUST_BE_DIFFERENT": "The new API key cannnot be the same as the current key",
    "ERROR_BLACKLIST": "Denylist IP Error",
    "ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR": "There is a problem with loading Cloud and Branch Connectors. Please try again later.",
    "ERROR_DEFAULT_LEASE_TME_SHOULD_BE_SMALLER_THAN_MAX_LEASE_TIME": "Default Lease Time should be smaller or equal to Max Lease Time.",
    "ERROR_DELETING_ADMIN_MANAGEMENT": "There is a problem with deleting admin management. Please try again later.",
    "ERROR_DELETING_API_KEY_MANAGEMENT": "There is a problem with deleting the API Key management. Please try again later.",
    "ERROR_DELETING_APPLIANCE": "There is a problem with deleting ZT Device. Please try again later.",
    "ERROR_DELETING_CELLULAR_CONFIGURATION": "There is a problem with deleting the Cellular Configuration. Please try again later.",
    "ERROR_DELETING_CELLULAR_USER_PLANE": "There is a problem with deleting the Cellular User Plane. Please try again later.",
    "ERROR_DELETING_GATEWAY": "There is a problem with deleting gateways. Please try again later.",
    "ERROR_DELETING_LOCATION_TEMPLATE": "There is a problem with deleting location template. Please try again later.",
    "ERROR_DELETING_LOCATION": "There is a problem with deleting location. Please try again later.",
    "ERROR_DELETING_PARTNER_ACCOUNT": "There is a problem with deleting Partner Account. Please try again later.",
    "ERROR_DELETING_ROLE_MANAGEMENT": "There is a problem with deleting role management. Please try again later.",
    "ERROR_DELETING_TESTING": "There is a problem with deleting the test. Please try again later.",
    "ERROR_DISABLING_PARTNER_ACCOUNT": "Error Disabling Partner Account Data Collection",
    "ERROR_DUPLICATE_DNS_SERVER": "Please select different options for the primary and secondary DNS.",
    "ERROR_DUPLICATE_HA_VIRTUAL_ID": "High Availability Virtual IDs must be unique. Duplicated High Availability IDs values ",
    "ERROR_DUPLICATE_INTERFACE": "The interface can not be the same as an existing one.",
    "ERROR_DUPLICATE_SUBNETS": "Please verify the duplicate subnet",
    "ERROR_EDITING_API_KEY_MANAGEMENT": "There is a problem with editing the API Key management. Please try again later.",
    "ERROR_EDITING_TESTING": "There is a problem with editing the test. Please try again later.",
    "ERROR_EDITING": "There is a problem with editing the data. Please try again later.",
    "ERROR_ENABLING_PARTNER_ACCOUNT": "Error Enabling Partner Account Data Collection ",
    "ERROR_HTTP_REQUEST_FAILURE": "HTTP Request failure reported.",
    "ERROR_LIST_DNS_SERVER_HAS_DUPLICATE": "The DNS server IP addresses has duplicate values.",
    "ERROR_LIST_DNS_SERVER_LIMIT_4": "The limit is 4 DNS server IP addresses.",
    "ERROR_LIST_DOMAIN_NAME_HAS_DUPLICATE": "The domain names has duplicated values.",
    "ERROR_LIST_DOMAIN_NAME_LIMIT_4": "The limit is 4 domain names.",
    "ERROR_LOADING_ADMIN_MANAGEMENT": "There is a problem with loading administrator management data, Please try again later.",
    "ERROR_LOADING_API_KEY_MANAGEMENT": "There is a problem with loading API key data, Please try again later.",
    "ERROR_LOADING_DATA": "There is a problem loading data, Please try again later.",
    "ERROR_LOADING_DOMAINS": "There is a problem with loading domains data, Please try again later.",
    "ERROR_LOADING_FORWARDING_POLICIES": "There is a problem with loading forwarding policies data, Please try again later.",
    "ERROR_LOADING_LOCATION_TEMPLATE": "There is a problem with loading location template data, Please try again later.",
    "ERROR_LOADING_LOCATIONS": "There is a problem with loading location data, Please try again later.",
    "ERROR_LOADING_ROLE_MANAGEMENT": "There is a problem with loading role management data, Please try again later.",
    "ERROR_LOADING": "Error loading the data.",
    "ERROR_LOCATION_OR_GROUP_IS_MANDATORY_FOR_LOCAL": "The rule can not be saved because a CC Location/Sublocation or Cloud Connector Group is mandatory for Local.",
    "ERROR_LOOKUP": "Lookup URL Error",
    "ERROR_NO_SCHEDULED_VERSION_AVAILABLE": "No version available",
    "ERROR_OCCURRED_WHILE_CREATING_NEW_PASSWORD": "Error occurred while creating new password. Please try again later",
    "ERROR_OCCURRED_WHILE_VERIFYING_PASSWORD": "Error occurred while verifying current password. Please try again later",
    "ERROR_OPERATIONAL_STATUS_SAVE_ERROR": "There is a problem with saving Operational status. Please try again later.",
    "ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "Azure Subscription Id may not be used in combination with Google Project Id",
    "ERROR_REGENERATE_API_KEY_MANAGEMENT": "There is a problem with regenerating the API Key management. Please try again later.",
    "ERROR_REMOVE_DELETED_SEGMENTS": "The rule can not be saved with a deleted segment.",
    "ERROR_SAVING_GATEWAY": "There is a problem with saving gateway. Please try again later.",
    "ERROR_SAVING_LOCATION": "There is a problem with saving location. Please try again later.",
    "ERROR_SAVING_NETWORK_SERVICES_GROUPS": "There is a problem with saving Network service groups. Please try again later.",
    "ERROR_SAVING_NETWORK_SERVICES": "There is a problem with saving Network services. Please try again later.",
    "ERROR_SAVING_ROLE_MANAGEMENT": "There is a problem with saving role management. Please try again later.",
    "ERROR_SAVING": "There is a problem saving the data. Please try again later.",
    "ERROR_TESTING": "There as an error executing test.",
    "ERROR_UPDATING_PERMISSION_STATUS": "There is a problem updating the permission status. Please try again later.",
    "ERROR": "Error",
    "ESP_PROTOCOL_DESC": "The Encapsulating Security Payload (ESP) is a protocol within the IPSec for providing authentication, integrity and confidentially of network packets payload in IPv4 and IPv6 networks.",
    "ESP_PROTOCOL": "ESP",
    "ESTABLISH_SUPPORT_TUNNEL": "Establish Support Tunnel",
    "ESTONIA_EUROPE_TALLINN": "Europe/Tallinn",
    "ESTONIA": "Estonia",
    "ETHIOPIA_AFRICA_ADDIS_ABABA": "Africa/Addis Ababa",
    "ETHIOPIA": "Ethiopia",
    "EU_CENTRAL_1": "eu-central-1 (Frankfurt)",
    "EU_CENTRAL_1A": "eu-central-1a",
    "EU_CENTRAL_1B": "eu-central-1b",
    "EU_CENTRAL_1C": "eu-central-1c",
    "EU_CENTRAL_2": "Europe (Zurich)",
    "EU_NORTH_1": "eu-north-1 (Stockholm)",
    "EU_NORTH_1A": "eu-north-1a",
    "EU_NORTH_1B": "eu-north-1b",
    "EU_NORTH_1C": "eu-north-1c",
    "EU_SOUTH_1": "eu-south-1 (Milan)",
    "EU_SOUTH_1A": "eu-south-1a",
    "EU_SOUTH_1B": "eu-south-1b",
    "EU_SOUTH_1C": "eu-south-1c",
    "EU_SOUTH_2": "Europe (Spain)",
    "EU_WEST_1": "eu-west-1 (Ireland)",
    "EU_WEST_1A": "eu-west-1a",
    "EU_WEST_1B": "eu-west-1b",
    "EU_WEST_1C": "eu-west-1c",
    "EU_WEST_2": "eu-west-2 (London)",
    "EU_WEST_2A": "eu-west-2a",
    "EU_WEST_2B": "eu-west-2b",
    "EU_WEST_2C": "eu-west-2c",
    "EU_WEST_3": "wu-west-3 (Paris)",
    "EU_WEST_3A": "eu-west-3a",
    "EU_WEST_3B": "eu-west-3b",
    "EU_WEST_3C": "eu-west-3c",
    "EUROPE_CENTRAL2_A": "europe-central2-a",
    "EUROPE_CENTRAL2_B": "europe-central2-b",
    "EUROPE_CENTRAL2_C": "europe-central2-c",
    "EUROPE_CENTRAL2": "europe-central2",
    "EUROPE_NORTH1_A": "europe-north1-a",
    "EUROPE_NORTH1_B": "europe-north1-b",
    "EUROPE_NORTH1_C": "europe-north1-c",
    "EUROPE_NORTH1": "europe-north1",
    "EUROPE_SOUTHWEST1_A": "europe-southwest1-a",
    "EUROPE_SOUTHWEST1_B": "europe-southwest1-b",
    "EUROPE_SOUTHWEST1_C": "europe-southwest1-c",
    "EUROPE_SOUTHWEST1": "europe-southwest1",
    "EUROPE_WEST1_B": "europe-west1-b",
    "EUROPE_WEST1_C": "europe-west1-c",
    "EUROPE_WEST1_D": "europe-west1-d",
    "EUROPE_WEST1": "europe-west1",
    "EUROPE_WEST10": "europe-west10",
    "EUROPE_WEST12_A": "europe-west12-a",
    "EUROPE_WEST12_B": "europe-west12-b",
    "EUROPE_WEST12_C": "europe-west12-c",
    "EUROPE_WEST12": "europe-west12",
    "EUROPE_WEST2_A": "europe-west2-a",
    "EUROPE_WEST2_B": "europe-west2-b",
    "EUROPE_WEST2_C": "europe-west2-c",
    "EUROPE_WEST2": "europe-west2",
    "EUROPE_WEST3_A": "europe-west3-a",
    "EUROPE_WEST3_B": "europe-west3-b",
    "EUROPE_WEST3_C": "europe-west3-c",
    "EUROPE_WEST3": "europe-west3",
    "EUROPE_WEST4_A": "europe-west4-a",
    "EUROPE_WEST4_B": "europe-west4-b",
    "EUROPE_WEST4_C": "europe-west4-c",
    "EUROPE_WEST4": "europe-west4",
    "EUROPE_WEST6_A": "europe-west6-a",
    "EUROPE_WEST6_B": "europe-west6-b",
    "EUROPE_WEST6_C": "europe-west6-c",
    "EUROPE_WEST6": "europe-west6",
    "EUROPE_WEST8_A": "europe-west8-a",
    "EUROPE_WEST8_B": "europe-west8-b",
    "EUROPE_WEST8_C": "europe-west8-c",
    "EUROPE_WEST8": "europe-west8",
    "EUROPE_WEST9_A": "europe-west9-a",
    "EUROPE_WEST9_B": "europe-west9-b",
    "EUROPE_WEST9_C": "europe-west9-c",
    "EUROPE_WEST9": "europe-west9",
    "EUROPE": "Europe",
    "EUSA_AGREEMENT": "Zscaler End User Subscription Agreement",
    "EVENT_BUS_NAME": "Event Bus Name",
    "EVENT_GRID_TEXT": "Select the regions, subscriptions, and resource groups where the partner topic and destination are created.",
    "EVENT_GRID": "Event Grid",
    "EVENT_TIME": "Event Time",
    "EVENT": "Event",
    "EVENTS": "Events",
    "EXACT_MATCH": "Exact Match",
    "EXCEEDS_UPGRADE_WINDOW": "Failed. Upgrade took longer than upgrade window. Cloud Connector rolled back to healthy state",
    "EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS": "Exclude from Dynamic Location Groups",
    "EXCLUDE_FROM_STATIC_LOCATION_GROUPS": "Exclude from Manual Location Groups",
    "EXCLUDE_IP_ADDRESSES": "Exclude IP Addresses",
    "EXCLUDE": "Exclude",
    "EXEC_INSIGHT_AND_ORG_ADMIN": "Executive Insight & Organization Admin",
    "EXISTING_LOCATION": "Existing Location",
    "EXISTING": "Existing",
    "EXPIRES_IN": "Expires In",
    "EXPIRES": "Expires",
    "EXPIRY_DATE": "Expiry Date",
    "EXPORT_TO_CSV": "Export to CSV",
    "EXTERNAL_ID": "External ID",
    "EXTERNAL": "External traffic",
    "FAIL_ALLOW_IGNORE_DNAT": "Forward to Original DNS Server",
    "FAIL_CLOSE": "Fail Close",
    "FAIL_CLOSED": "Fail Closed",
    "FAIL_OPEN_TEXT": "Bypass Traffic Forwarding Engine",
    "FAIL_OPEN_TOOLTIP": "Enabling this option allows for all local and Internet destined traffic to flow without policy validation or content inspection in case the policy forwarding engine fails or is stopped for upgrades. Access to ZPA protected applications would not be permitted while in this state.",
    "FAIL_OPEN": "Fail Open",
    "FAIL_RET_ERR": "Return error response",
    "FAILED_OTHER": "Other. Cloud Connector in healthy state",
    "FAILED": "Failed",
    "FAILURE_BEHAVIOR": "Failure Behavior",
    "FAILURE": "Failure",
    "FALKLAND_ISLANDS_ATLANTIC_STANLEY": "Atlantic/Stanley",
    "FALKLAND_ISLANDS_MALVINAS": "Falkland Islands (Malvinas)",
    "FALKLAND_ISLANDS": "Falkland Islands",
    "FALLBACK_TO_TLS": "Fallback To TLS",
    "FALSE": "False",
    "FAMILY_ISSUES": "Family Issues",
    "FAROE_ISLANDS_ATLANTIC_FAROE": "Atlantic/Faroe",
    "FAROE_ISLANDS": "Faroe Islands",
    "FEDERATED_STATES_OF_MICRONESIA": "Federated States of Micronesia",
    "FETCHING_MORE_LIST_ITEMS": "Fetching more list items...",
    "FIJI": "Fiji",
    "FILE_CERTIFICATE_FILTER": "File (.pem, .cer)",
    "FILE_HOST": "FileHost",
    "FILTERING": "Filtering",
    "FINANCE": "Finance",
    "FINISH": "Finish",
    "FINLAND_EUROPE_HELSINKI": "Europe/Helsinki",
    "FINLAND": "Finland",
    "FIREWALL_ACCESS_CONTROL": "Firewall Access Control",
    "FIREWALL_FORWARDING": "Firewall Forwarding",
    "FIREWALL_LOGS": "Firewall Logs",
    "FIREWALL_RESOURCE": "Firewall Resource",
    "FIRST_TIME_AUP_BEHAVIOR": "First Time AUP Behavior",
    "FO_DEST_DROP": "Query Dropped",
    "FO_DEST_ERR": "Error Response Returned to Client",
    "FO_DEST_PASS": "Query Forwarded to Destination",
    "FOOTER_PATENTS_TOOLTIP": "Consistent with the America Invents Act's Virtual Marking provisions, Zscaler's security offerings are protected by patents in the U.S. and elsewhere, as further described at https://www.zscaler.com/patents.",
    "FOR_AUTOMATION": "For Automation",
    "FORCE_ACTIVATE": "Force Activate",
    "FORCE_DELETE_VM": "Force Delete VM",
    "FORCE_SSL_INTERCEPTION": "Force SSL Inspection",
    "FORCED_ACTIVATE": "Force Activate",
    "FORWARD_TO_ORIGINAL_SERVER": "Forward to Original DNS Server",
    "FORWARD_TO_PROXY_GATEWAY": "Forward to Proxy Gateway",
    "FORWARD_TO_ZPA_GATEWAY": "Forward to ZPA Gateway",
    "FORWARDING_CONTROL": "Forwarding Control",
    "FORWARDING_INFORMATION": "Forwarding Information",
    "FORWARDING_INTERFACE": "Forwarding Interface",
    "FORWARDING_IP_ADDRESS": "Forwarding IP Address",
    "FORWARDING_METHOD": "Forwarding Type",
    "FORWARDING_METHODS": "Forward Methods",
    "FORWARDING_POLICIES": "Forwarding Policies",
    "FORWARDING_RULE": "Forwarding Rule",
    "FORWARDING": "Forwarding",
    "FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Domains",
    "FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Domains Group",
    "FRANCE_EUROPE_PARIS": "Europe/Paris",
    "FRANCE": "France",
    "FRANCECENTRAL": "(Europe) France Central",
    "FRANCESOUTH": "(Europe) France South",
    "FRENCH_GUIANA_AMERICA_CAYENNE": "America/Cayenne",
    "FRENCH_GUIANA": "French Guiana",
    "FRENCH_POLYNESIA_PACIFIC_GAMBIER": "Pacific/Gambier",
    "FRENCH_POLYNESIA_PACIFIC_MARQUESAS": "Pacific/Marquesas",
    "FRENCH_POLYNESIA_PACIFIC_TAHITI": "Pacific/Tahiti",
    "FRENCH_POLYNESIA": "French Polynesia",
    "FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN": "Indian/Kerguelen",
    "FRENCH_SOUTHERN_TERRITORIES": "French Southern Territories",
    "FRIDAY": "Friday",
    "FROM": "From",
    "FTP_000": "Invalid FTP response",
    "FTP_110": "110 - Restart marker replay",
    "FTP_125": "125 - Data connection already open; transfer starting",
    "FTP_150": "150 - File status okay;about to open data connection",
    "FTP_200": "200 - Directory successfully changed",
    "FTP_226": "226 - Transfer Complete",
    "FTP_250": "250 - Completed",
    "FTP_421": "421 - Service not available",
    "FTP_425": "425 - Can't open data connection",
    "FTP_426": "426 - Transfer aborted",
    "FTP_450": "450 - Requested file action not taken",
    "FTP_451": "451 - Local error in processing",
    "FTP_452": "452 - Insufficient storage",
    "FTP_453": "453 - MD5 mismatch",
    "FTP_500": "500 - Syntax error",
    "FTP_501": "501 - Syntax error in parameters",
    "FTP_502": "502 - Command not implemented",
    "FTP_530": "530 - Not logged in",
    "FTP_532": "532 - Need account for storing files",
    "FTP_550": "550 - File unavailable",
    "FTP_551": "551 - Page type unknown",
    "FTP_552": "552 - Exceeded storage allocation",
    "FTP_553": "553 - File name not allowed",
    "FTP_554": "554 - File is infected",
    "FTP_555": "555 - Blocked by file type policy",
    "FTP_556": "556 - Blocked by DLP",
    "FTP_557": "557 - Blocked by BA",
    "FTP_558": "558 - Blocked by BWCTL",
    "FTP_559": "559 - Blocked by URL Category",
    "FTP_560": "560 - Blocked by ATP",
    "FTP_561": "561 - Blocked by Block Internet Access",
    "FTP_ALLOW_OVER_HTTP": "Allow FTP over HTTP",
    "FTP_ALLOWED_URL_CATEGORIES": "Allowed URL Categories",
    "FTP_ALLOWED_URLS": "Allowed URLs",
    "FTP_APPE": "appe",
    "FTP_CONNECT_CMD": "connect",
    "FTP_CONNECT": "Convert ftp (using HTTP connect) traffic from bridging to native FTP",
    "FTP_CONTROL_RECOMMENDED_POLICY": "Recommended FTP Control Policy",
    "FTP_CONTROL_TIPS_DESC": "By default, the Zscaler service does not allow users from a location to upload or download files from FTP sites. You can configure the FTP Control policy to allow access to specific sites.",
    "FTP_CONTROL_TIPS_TITLE": "Configure FTP Control Policy",
    "FTP_CONTROL": "FTP Control",
    "FTP_CWD": "cwd",
    "FTP_DATA_DESC": " This protocol is used to transport data in data connection of FTP communication",
    "FTP_DATA": "FTP-Data",
    "FTP_DENIED": "Not allowed to access to FTP sites",
    "FTP_DESC": " The FTP protocol is used for reliable data transfer between a client and a server",
    "FTP_INVALID": "invalid",
    "FTP_LIST": "list",
    "FTP_NATIVE_TRAFFIC": "Native FTP Traffic",
    "FTP_OVER_HTTP_TRAFFIC": "FTP over HTTP Traffic",
    "FTP_PROXY_PORT": "FTP Proxy Port",
    "FTP_PROXY": "FTP Proxy",
    "FTP_RETR": "retr",
    "FTP_RULE": "Native FTP",
    "FTP_SECURITY": "FTP security includes AV, DLP, BA, FT and so on",
    "FTP_SERVICES": "FTP Services",
    "FTP_STOR": "stor",
    "FTP_UPLOAD_DENIED": "Not allowed to use FTP over HTTP for upload",
    "FTP": "FTP",
    "FTPOVERHTTP": "FTP over HTTP",
    "FTPRULESLOT": "File Type Control",
    "FTPS_DATA_DESC": " This protocol is used to transport data in data connection of secure ftp communication",
    "FTPS_DATA": "ftps_data",
    "FTPS_DESC": " Secure version of the FTP protocol",
    "FTPS_IMPLICIT_DESC": "Implicit FTPS automatically starts an SSL/TLS connection to server as soon as the FTP client connects to an FTP server.",
    "FTPS_IMPLICIT": "Implicit FTPS",
    "FTPS": "FTPS",
    "FULL_ACCESS_ENABLED_UNTIL": "Full Access Enabled Until",
    "FULL_ACCESS": "Full Access",
    "FULL_SESSION_LOGS": "Full Session Logs",
    "FULL": "Full",
    "FUNCTIONAL_SCOPE": "Functional Scope",
    "FWD_METHOD": "Forwarding Type",
    "FWD_RULE": "Forwarding Rule",
    "FWD_TRAFFIC_DIRECTION": "Traffic Direction",
    "FWD_TYPE": "Forwarding Type",
    "FWD_TYPES": "Fwd Types",
    "GABON_AFRICA_LIBREVILLE": "Africa/Libreville",
    "GABON": "Gabon",
    "GAMBIA_AFRICA_BANJUL": "Africa/Banjul",
    "GAMBIA": "Gambia",
    "GAMBLING": "Gambling",
    "GATEWAY_DEST_IP": "Gateway Destination IP",
    "GATEWAY_DEST_PORT": "Gateway Destination Port",
    "GATEWAY_DETAILS": "Gateway Details",
    "GATEWAY_IP_ADDRESS": "Gateway IP Address",
    "GATEWAY_NAME": "Gateway Name",
    "GATEWAY_OPTIONS": "Gateway Options",
    "GATEWAY": "Gateway",
    "GATEWAYS": "Gateways",
    "GCP_AVAILABILITY_ZONE": "GCP Availability Zone",
    "GCP_REGION": "GCP Region",
    "GENERAL_AVAILABILITY": "General Availability",
    "GENERAL_INFORMATION": "General Information",
    "GENERAL": "General",
    "GENERATE_NEW_CERTIFICATE": "Generate New Certificate",
    "GENERATE_TOKEN": "Generate Token",
    "GEO_LOCATION": "Geo Location",
    "GEO_VIEW": "Geo View",
    "GEORGIA_ASIA_TBILISI": "Asia/Tbilisi",
    "GEORGIA": "Georgia",
    "GERMANY_EUROPE_BERLIN": "Europe/Berlin",
    "GERMANY": "Germany",
    "GERMANYNORTH": "(Europe) Germany North",
    "GERMANYWESTCENTRAL": "(Europe) Germany West Central",
    "GHANA_AFRICA_ACCRA": "Africa/Accra",
    "GHANA": "Ghana",
    "GIBRALTAR_EUROPE_GIBRALTAR": "Europe/Gibraltar",
    "GIBRALTAR": "Gibraltar",
    "GLOBAL": "Global",
    "GMT_01_00_AZORES": "GMT-01:00",
    "GMT_01_00_WESTERN_EUROPE_GMT_01_00": "GMT+01:00",
    "GMT_02_00_EASTERN_EUROPE_GMT_02_00": "GMT+02:00",
    "GMT_02_00_EGYPT_GMT_02_00": "GMT+02:00",
    "GMT_02_00_ISRAEL_GMT_02_00": "GMT+02:00",
    "GMT_02_00_MID_ATLANTIC": "GMT-02:00",
    "GMT_03_00_ARGENTINA": "GMT-03:00",
    "GMT_03_00_BRAZIL": "GMT-03:00",
    "GMT_03_00_RUSSIA_GMT_03_00": "GMT+03:00",
    "GMT_03_00_SAUDI_ARABIA_GMT_03_00": "GMT+03:00",
    "GMT_03_30_IRAN_GMT_03_30": "GMT+03:30",
    "GMT_03_30_NEWFOUNDLAND_CANADA": "GMT-03:30",
    "GMT_04_00_ARABIAN_GMT_04_00": "GMT+04:00",
    "GMT_04_00_ATLANTIC_TIME": "GMT-04:00",
    "GMT_04_30_AFGHANISTAN_GMT_04_30": "GMT+04:30",
    "GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA": "GMT-05:00",
    "GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00": "GMT+05:00",
    "GMT_05_00_US_EASTERN_TIME_INDIANA": "GMT-05:00",
    "GMT_05_00_US_EASTERN_TIME": "GMT-05:00",
    "GMT_05_30_INDIA_GMT_05_30": "GMT+05:30",
    "GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00": "GMT+06:00",
    "GMT_06_00_MEXICO": "GMT-06:00",
    "GMT_06_00_US_CENTRAL_TIME": "GMT-06:00",
    "GMT_06_30_BURMA_GMT_06_30": "GMT+06:30",
    "GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00": "GMT+07:00",
    "GMT_07_00_US_MOUNTAIN_TIME_ARIZONA": "GMT-07:00",
    "GMT_07_00_US_MOUNTAIN_TIME": "GMT-07:00",
    "GMT_08_00_AUSTRALIA_WT_GMT_08_00": "GMT+08:00",
    "GMT_08_00_CHINA_TAIWAN_GMT_08_00": "GMT+08:00",
    "GMT_08_00_PACIFIC_TIME": "GMT-08:00",
    "GMT_08_00_SINGAPORE_GMT_08_00": "GMT+08:00",
    "GMT_08_30_PITCARN": "GMT-08:30",
    "GMT_09_00_JAPAN_GMT_09_00": "GMT+09:00",
    "GMT_09_00_KOREA_GMT_09_00": "GMT+09:00",
    "GMT_09_00_US_ALASKA_TIME": "GMT-09:00",
    "GMT_09_30_AUSTRALIA_CT_GMT_09_30": "GMT+09:30",
    "GMT_09_30_MARQUESAS": "GMT-09:30",
    "GMT_10_00_AUSTRALIA_ET_GMT_10_00": "GMT+10:00",
    "GMT_10_00_US_HAWAIIAN_TIME": "GMT-10:00",
    "GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30": "GMT+10:30",
    "GMT_11_00_CENTRAL_PACIFIC_GMT_11_00": "GMT+11:00",
    "GMT_11_00_SAMOA": "GMT-11:00",
    "GMT_11_30_NORFOLK_ISLANDS_GMT_11_30": "GMT+11:30",
    "GMT_12_00_DATELINE": "GMT-12:00",
    "GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00": "GMT+12:00",
    "GMT_UK_SPAIN": "GMT",
    "GMT": "GMT",
    "GMTP": "",
    "GNUTELLA_DESC": " Gnutella is a peer-to-peer protocol",
    "GNUTELLA": "Gnutella",
    "GO_BACK": "Go Back",
    "GOOD": "Good",
    "GOOGLE_CLOUD_ACCOUNTS": "Google Cloud Accounts",
    "GOVERNMENT": "Other Government and Politics",
    "GRE_PROTOCOL_DESC": "The Generic Routing Encapsulation (GRE) is a tunneling protocol that can encapsulate a wide variety of network layer protocols inside virtual point-to-point links or point-to-multipoint links over an Internet Protocol network.",
    "GRE_PROTOCOL": "GRE",
    "GRE_TUNNEL_INFO": "GRE Tunnel Information",
    "GRE": "GRE",
    "GREECE_EUROPE_ATHENS": "Europe/Athens",
    "GREECE": "Greece",
    "GREENLAND_AMERICA_DANMARKSHAVN": "America/Danmarkshavn",
    "GREENLAND_AMERICA_GODTHAB": "America/Godthab",
    "GREENLAND_AMERICA_SCORESBYSUND": "America/Scoresbysund",
    "GREENLAND_AMERICA_THULE": "America/Thule",
    "GREENLAND": "Greenland",
    "GRENADA_AMERICA_GRENADA": "America/Grenada",
    "GRENADA": "Grenada",
    "GROUP_INFORMATION": "Group Information",
    "GROUP_NAME": "Group Name",
    "GROUP_ONLY": "Group",
    "GROUP_TYPE": "Group Type",
    "GROUP": "Group",
    "GROUPS": "Groups",
    "GUADELOUPE_AMERICA_GUADELOUPE": "America/Guadeloupe",
    "GUADELOUPE": "Guadeloupe",
    "GUAM_PACIFIC_GUAM": "Pacific/Guam",
    "GUAM": "Guam",
    "GUATEMALA_AMERICA_GUATEMALA": "America/Guatemala",
    "GUATEMALA": "Guatemala",
    "GUERNSEY_EUROPE_GUERNSEY": "Europe/Guernsey",
    "GUERNSEY": "Guernsey",
    "GUESTWIFI": "Guest Wi-Fi traffic type",
    "GUINEA_AFRICA_CONAKRY": "Africa/Conakry",
    "GUINEA_BISSAU_AFRICA_BISSAU": "Africa/Bissau",
    "GUINEA_BISSAU": "Guinea-Bissau",
    "GUINEA": "Guinea",
    "GUYANA_AMERICA_GUYANA": "America/Guyana",
    "GUYANA": "Guyana",
    "GW_CONNECT_FAILED": "Connection establishment to GW failed.",
    "GW_CONNECTION_CLOSE": "GW Connection was closed with EOF.",
    "GW_CONNECTION_FAIL": "Gateway connection failed.",
    "GW_KEEPALIVE_FAIL": "GW keepalive probe timed out.",
    "GW_RESOLVE_FAIL": "Gateway resolution failed.",
    "GW_RESOLVE_NOIP": "PAC returned no IPs for GW resolution.",
    "GW_UNHEALTHY": "Some gateway is unhealthy.",
    "H_323_DESC": "H.323 is a standard approved by the International Telecommunication Union (ITU) that defines how audiovisual conferencing data is transmitted across networks",
    "H_323": "H.323",
    "HA_DEPLOYMENT_STATUS": "HA Deployment Status",
    "HA_DEPLOYMENT": "High Availability Deployment",
    "HA_STATE": "HA State",
    "HA_STATUS": "HA Status",
    "HAITI_AMERICA_PORT_AU_PRINCE": "America/Port-au-Prince",
    "HAITI": "Haiti",
    "HARDWARE_DEVICE": "Hardware Device",
    "HARDWARE_MANAGEMENT": "Hardware Management",
    "HEADERS": "Headers",
    "HEALTH_MONITORING_CC": "Health Monitoring Cloud Connectors",
    "HEALTH_STATUS_TOOLTIP": "Health status displays the health for each gateway deployed. The number of gateways displayed here will be lower than the number shown in entitlement because each gateway can contain 2 or more availability zones (AZ).",
    "HEALTH_STATUS": "Health Status",
    "HEALTH": "Health",
    "HEALTHY": "Healthy",
    "HELP": "Help",
    "HIGH_AVAILABILITY_STATUS": "High Availability Status",
    "HIGH_AVAILABILITY": "High Availability",
    "HISTORY": "History",
    "HOBBIES_AND_LEISURE": "Hobbies/Leisure",
    "HOLY_SEE_VATICAN_CITY_STATE": "Holy See (Vatican City State)",
    "HONDURAS_AMERICA_TEGUCIGALPA": "America/Tegucigalpa",
    "HONDURAS": "Honduras",
    "HONG_KONG_ASIA_HONG_KONG": "Asia/Hong Kong",
    "HONG_KONG": "Hong Kong",
    "HOP_COUNT": "Hop count",
    "HOSTED_DB": "Hosted DB",
    "HOSTNAME_PREFIX": "Hostname Prefix",
    "HOSTNAME": "Hostname",
    "HOURS": "Hours",
    "HTTP_0_0": "bad",
    "HTTP_000": "Invalid HTTP response",
    "HTTP_1_0": "1",
    "HTTP_1_1": "1.1",
    "HTTP_100": "100 - Continue",
    "HTTP_101": "101 - Switching Protocols",
    "HTTP_102": "102 - Processing",
    "HTTP_150": "150 - Other 1XX errors",
    "HTTP_2_0": "2.0",
    "HTTP_200": "200 - OK",
    "HTTP_201": "201 - Created",
    "HTTP_202": "202 - Accepted",
    "HTTP_203": "203 - Non-Authoritative Information",
    "HTTP_204": "204 - No Content",
    "HTTP_205": "205 - Reset Content",
    "HTTP_206": "206 - Partial Content",
    "HTTP_207": "207 - Multi-Status",
    "HTTP_226": "226 - IM Used",
    "HTTP_250": "250 - Other 2XX errors",
    "HTTP_300": "300 - Multiple Choices",
    "HTTP_301": "301 - Moved Permanently",
    "HTTP_302": "302 - Found",
    "HTTP_303": "303 - See Other",
    "HTTP_304": "304 - Not Modified",
    "HTTP_305": "305 - Use Proxy",
    "HTTP_306": "306 - Unused",
    "HTTP_307": "307 - Temporary Redirect",
    "HTTP_308": "308 - Permanent Redirect",
    "HTTP_400": "400 - Bad Request",
    "HTTP_401": "401 - Unauthorized",
    "HTTP_402": "402 - Payment Required",
    "HTTP_403": "403 - Forbidden",
    "HTTP_404": "404 - Not Found",
    "HTTP_405": "405 - Method Not Allowed",
    "HTTP_406": "406 - Not Acceptable",
    "HTTP_407": "407 - Proxy Authentication Required",
    "HTTP_408": "408 - Request Timeout",
    "HTTP_409": "409 - Conflict",
    "HTTP_410": "410 - Gone",
    "HTTP_411": "411 - Length Required",
    "HTTP_412": "412 - Precondition Failed",
    "HTTP_413": "413 - Request Entity Too Large",
    "HTTP_414": "414 - Request-URI Too Long",
    "HTTP_415": "415 - Unsupported Media Type",
    "HTTP_416": "416 - Requested Range Not Satisfiable",
    "HTTP_417": "417 - Expectation Failed",
    "HTTP_421": "421 - Misdirected Request",
    "HTTP_422": "422 - Unprocessable Entity",
    "HTTP_423": "423 - Locked",
    "HTTP_424": "424 - Failed Dependency",
    "HTTP_426": "426 - Upgrade Required",
    "HTTP_428": "428 - Precondition Required",
    "HTTP_429": "429 - Too Many Requests",
    "HTTP_450": "450 - Other 4XX errors",
    "HTTP_500": "500 - Internal Server Error",
    "HTTP_501": "501 - Not Implemented",
    "HTTP_502": "502 - Bad Gateway",
    "HTTP_503": "503 - Service Unavailable",
    "HTTP_504": "504 - Gateway Timeout",
    "HTTP_505": "505 - Version Not Supported",
    "HTTP_506": "506 - Variant Also Negotiates",
    "HTTP_507": "507 - Insufficient Storage",
    "HTTP_508": "508 - Loop Detected",
    "HTTP_510": "510 - Not Extended",
    "HTTP_550": "550 - Other 5XX errors",
    "HTTP_BASELINECONTROL": "Baselinecontrol",
    "HTTP_BCOPY": "Bcopy",
    "HTTP_BDELETE": "Bdelete",
    "HTTP_BMOVE": "Bmove",
    "HTTP_BPROPFIND": "Bpropfind",
    "HTTP_BPROPPATCH": "Bproppatch",
    "HTTP_CHECKIN": "Checkin",
    "HTTP_CHECKOUT": "Checkout",
    "HTTP_CONNECT_DENIED": "Not allowed to use HTTP tunnel",
    "HTTP_CONNECT": "Connect",
    "HTTP_COPY": "Copy",
    "HTTP_DELETE": "Delete",
    "HTTP_DESC": " The Hypertext Transfer Protocol (HTTP) is used for browsing the web",
    "HTTP_DNS_PORT_SETTINGS": "HTTP & DNS Port Settings",
    "HTTP_DPI_DISABLED": "SME DPI: Determines Proxy http traffic should be sent to DPI or not; by default this feature bit is off and we send flow to DPI if appid cannot be determined from ZURLDB",
    "HTTP_GET": "Get",
    "HTTP_HEAD": "Head",
    "HTTP_LABEL": "Label",
    "HTTP_LOCK": "Lock",
    "HTTP_MAILPOST": "Mailpost",
    "HTTP_MERGE": "Merge",
    "HTTP_MKACTIVITY": "Mkactivity",
    "HTTP_MKCOL": "Mkcol",
    "HTTP_MKWORKSPACE": "Mkworkspace",
    "HTTP_MOVE": "Move",
    "HTTP_NOTIFY": "Notify",
    "HTTP_OPTIONS": "Options",
    "HTTP_POLL": "Poll",
    "HTTP_PORTS_FORWARDED_TO_WEB_PROXY": "HTTP Ports Forwarded to Web Proxy",
    "HTTP_POST": "Post",
    "HTTP_PROPFIND": "Propfind",
    "HTTP_PROPPATCH": "Proppatch",
    "HTTP_PROXY_DESC": "HTTP tunneling is a technique by which communications performed using various network protocols are encapsulated using the HTTP protocol, the network protocols in question usually belonging to the TCP/IP family of protocols.",
    "HTTP_PROXY_PORT": "HTTP Proxy Port",
    "HTTP_PROXY": "HTTP Proxy",
    "HTTP_PUT": "Put",
    "HTTP_REPORT": "Report",
    "HTTP_REQMOD": "Reqmod",
    "HTTP_REQUEST": "HTTP Request",
    "HTTP_REQUESTS": "HTTP Requests",
    "HTTP_RESPMOD": "Respmod",
    "HTTP_RESPONSE": "HTTP Response",
    "HTTP_RULE": "HTTP",
    "HTTP_SEARCH": "Search",
    "HTTP_SECURITY_HEADERS": "HTTP Security Headers",
    "HTTP_SERVICES": "HTTP Services",
    "HTTP_SUBSCRIBE": "Subscribe",
    "HTTP_TRACE": "Trace",
    "HTTP_TUNNEL_CONTROL": "HTTP Tunnel Control",
    "HTTP_TUNNEL": "HTTP tunnel",
    "HTTP_UNCHECKOUT": "Uncheckout",
    "HTTP_UNKNOWN_DESC": " This identifies HTTP proxy/firewall traffic for which more granular app cannot be determined",
    "HTTP_UNKNOWN": "HTTP Unknown",
    "HTTP_UNLOCK": "Unlock",
    "HTTP_UNSUBSCRIBE": "Unsubscribe",
    "HTTP_UPDATE": "Update",
    "HTTP_VERSIONCONTROL": "Versioncontrol",
    "HTTP_VS_HTTPS": "HTTP vs. HTTPS",
    "HTTP": "HTTP",
    "HTTP2_DESC": " The Hypertext Transfer Protocol (HTTP2.0) is used for browsing the web",
    "HTTP2": "HTTPv2",
    "HTTPS_DESC": " HTTPS is the secure version of HTTP",
    "HTTPS_PORTS_FORWARDED_TO_WEB_PROXY": "HTTPS Ports Forwarded to Web Proxy",
    "HTTPS_PROXY_PORT": "HTTPS Proxy Port",
    "HTTPS_PROXY": "HTTPS Proxy",
    "HTTPS_RULE": "HTTPS",
    "HTTPS_SERVICES": "HTTPS Services",
    "HTTPS_SSL_TRAFFIC_TREND": "HTTPS & SSL TRAFFIC TREND",
    "HTTPS_SSL_TRAFFIC": "HTTPS & SSL TRAFFIC",
    "HTTPS_UNKNOWN_DESC": " This identifies HTTPS proxy/firewall traffic for which which more granular app cannot be determined",
    "HTTPS_UNKNOWN": "HTTPS Unknown",
    "HTTPS": "HTTPS",
    "HTTPTUNNEL_DESC": " HTTP tunneling is a technique by which communications performed using various network protocols are encapsulated using the HTTP protocol, the network protocols in question usually belonging to the TCP/IP family of protocols",
    "HTTPTUNNEL": "HttpTunnel",
    "HUNGARY_EUROPE_BUDAPEST": "Europe/Budapest",
    "HUNGARY": "Hungary",
    "HYPERVISOR_OS": "Hypervisor",
    "HYPERVISOR_VERSION": "Hypervisor Version",
    "I_AGREE": "I Agree",
    "I_GAMER_DESC": " Online games and manga website",
    "I_GAMER": "i-gamer",
    "I_PART_DESC": " Taiwanese online dating site",
    "I_PART": "i-part.com",
    "I_UNDERSTAND_THE_CONSEQUENCE_AND_WANT_TO_PROCEED": "I understand the consequences and want to proceed",
    "I_UNDERSTAND_THE_CONSEQUENCE": "I understand the consequences and want to proceed",
    "ICELAND_ATLANTIC_REYKJAVIK": "Atlantic/Reykjavik",
    "ICELAND": "Iceland",
    "ICMP_ANY_DESC": "ICMP is one of the main protocols of the Internet Protocol Suite, which is used by network devices, like routers, to send error messages indicating that a requested service is not available or that a host or router could not be reached. ICMP can also be used to relay query messages",
    "ICMP_ANY": "ICMP",
    "ICMP_DESC": " The Internet Control Message Protocol (ICMP) is one of the main protocols of the Internet Protocol Suite",
    "ICMP_UNKNOWN_DESC": " This identifies UDP proxy/firewall traffic for which which more granular app cannot be determined",
    "ICMP_UNKNOWN": "ICMP Unknown",
    "IDENT_DESC": " The Identification Protocol provides a means to determine the identity of a user of a specific TCP connection",
    "IDENT": "Ident",
    "IDLE_TIME_DISASSOCIATION": "Idle Time to Disassociation",
    "IDP_NAME": "IdP Name",
    "IDP": "IdP",
    "IGNORE_INSECURE_KEY": "Insecure",
    "IKE_ALG": "This LB support ike alg",
    "IKE_DESC": "IKE is a protocol to obtain authenticated keying material for use with ISAKMP for IPSEC",
    "IKE_NAT_DESC": "IKE-NAT allows Network Address Translation for ISAKMP and ESP packets",
    "IKE_NAT": "IKE-NAT",
    "IKE": "IKE",
    "IKEA_DESC": " This protocol plug-in classifies the http traffic to the host ikea.com",
    "IKEA": "ikea",
    "IKEV1_PHASE1": "IKE Version 1",
    "IKEV1_PHASE2": "IKE Version 2",
    "IKEV1": "IKE Version 1",
    "IKEV2_ALL_PHASES": "IKE Version 2",
    "IKEV2": "IKE Version 2",
    "IL_CENTRAL_1": "Israel (Tel Aviv)",
    "ILOVEIM_DESC": " This protocol plug-in classifies the http traffic to the host iloveim.com",
    "ILOVEIM": "ILoveIM",
    "ILS_DESC": "Internet Locator Service includes LDAP, User Locator Service and LDAP over TLS/SSL",
    "ILS": "ILS",
    "IMAGE_HOST_DESC": " Sites that provide video or image hosting, linking and/or sharing.",
    "IMAGE_HOST": "Image Host",
    "IMAGE_ID": "Image Id",
    "IMAGES": "Images",
    "IMAGESHACK_DESC": " On-line free image sharing service",
    "IMAGESHACK": "ImageShack",
    "IMAP_DESC": "Internet Message Access Protocol is a protocol used for retrieving email messages",
    "IMAP": "IMAP",
    "IMDB_DESC": " On-line information database related to movies and tv-shows",
    "IMDB": "IMDb",
    "IMEEM_DESC": " This protocol plug-in classifies the http traffic to the host imeem.com",
    "IMEEM": "imeem",
    "IMEET_DESC": " On-line video-conferencing service using cloud-based technology",
    "IMEET": "i-Meet",
    "IMESH_DESC": " iMesh is a peer-to-peer protocol",
    "IMESH": "iMesh",
    "IMFRULESLOT": "Instant Messaging App Control",
    "IMGUR_DESC": " A free online image hosting service",
    "IMGUR": "imgur",
    "IMO": "imo",
    "IMOIM_DESC": " imo.im",
    "IMOIM": "imo.im",
    "IMP_DESC": " IMP is the IMAP webmail of the Horde project",
    "IMP": "IMP",
    "IMPERSONATION": "Impersonation",
    "IMPLUS_DESC": " IM+",
    "IMPLUS": "IM+",
    "IMPORT_ACCOUNT_ID": "Import Account ID",
    "IMPORT_PROJECT_ID": "Import Project ID",
    "IMPORT_SUBSCRIPTION_ID": "Import Subscription ID",
    "IMPORT": "Import",
    "IMPRESS_DESC": " Japanese IT news web site",
    "IMPRESS": "impress",
    "IMVU_DESC": " This protocol plug-in classifies the http traffic to the host imvu.com",
    "IMVU": "IMVU",
    "INACTIVE": "Inactive",
    "INBOUND": "Inbound",
    "INBOX_DESC": " inbox.com portal which provides free email service",
    "INBOX": "Inbox",
    "INBYTES": "In Bytes",
    "INCLUDE_ADDRESS_RANGE": "Include Address Range",
    "INCLUDE_IP_ADDRESSES": "Include IP Addresses",
    "INCLUDE": "Include",
    "INCOMPLETE_DESC": " Incomplete is used when the protocol signature is too long",
    "INCOMPLETE": "Incomplete",
    "INDABA_MUSIC_DESC": " This protocol plug-in classifies the http traffic to the host indabamusic.com",
    "INDABA_MUSIC": "Indaba Music",
    "INDIA_ASIA_CALCUTTA": "Asia/Calcutta",
    "INDIA_ASIA_KOLKATA": "Asia/Kolkata",
    "INDIA": "India",
    "INDIATIMES_DESC": " This signature detects indiatimes and a large number of its subdomains, which is one of the most popular Internet and mobile value-added services web portal in India",
    "INDIATIMES": "timesofindia",
    "INDONESIA_ASIA_JAKARTA": "Asia/Jakarta",
    "INDONESIA_ASIA_JAYAPURA": "Asia/Jayapura",
    "INDONESIA_ASIA_MAKASSAR": "Asia/Makassar",
    "INDONESIA_ASIA_PONTIANAK": "Asia/Pontianak",
    "INDONESIA": "Indonesia",
    "INDONETWORK_DESC": " This protocol plug-in classifies the http traffic to the host indonetwork.co.id",
    "INDONETWORK": "Indonetwork",
    "INDOWEBSTER_DESC": " This protocol plug-in classifies the http traffic to the host indowebster.com",
    "INDOWEBSTER": "Indowebster",
    "INFO": "Info",
    "INFOARMOR_DESC": " InfoArmor ensures industry-leading solutions for employee identity protection and uses advanced threat intelligence",
    "INFOARMOR": "InfoArmor",
    "INFORMIX_DESC": " Informix is a family of relational database management systems developed by IBM. IBM acquired the Informix technology in 2001 but it dates back to 1981.It runs on IBM mainframes and is also available for Linux/Unix/Windows",
    "INFORMIX": "Informix",
    "INGRESS_DETAILS": "Ingress Details",
    "INILAH_DESC": " This protocol plug-in classifies the http traffic to the host inilah.com",
    "INILAH": "inilah",
    "INSIGHTS": "Insights",
    "INST_GBL_METRICS": "Instance",
    "INSTAGRAM_DESC": " This protocol plug-in classifies the http traffic to the hosts instagr.am and instagram.com. It also classifies the ssl traffic to the Common Name instagram.com",
    "INSTAGRAM": "Instagram",
    "INSTANCE_ROLE": "Instance Role",
    "INTALKING_DESC": " Taiwanese beauty, makeup portal",
    "INTALKING": "intalking.com",
    "INTEGER_REQUIRED": "Enter a number.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_LAN": "LAN needs a minimum of one interface or sub-interface enabled.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_WAN": "WAN needs a minimum of one interface or sub-interface enabled, and a maximum of two interfaces or sub-interfaces enabled.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE": "Interface must have at least one sub interface.",
    "INTERFACE_NAME": "Interface Name",
    "INTERFACE_SHUTDOWN": "Interface Shutdown",
    "INTERFACE": "Interface",
    "INTERNAL_EXTERNAL_TRAFFIC": "Internal/External Traffic",
    "INTERNAL_GATEWAY_IP_ADDRESS": "Internal Gateway IP Address",
    "INTERNAL": "Internal traffic",
    "INTERNATIONS_DESC": " This protocol plug-in classifies the http traffic to the host internations.org. It also classifies the ssl traffic to the Common Name .internations.org",
    "INTERNATIONS": "InterNations",
    "INTERNET_ACCESS_GATEWAY": "Internet Access Gateway",
    "INTERNET_ACCESS": "Internet Access",
    "INTERNET_COMMUNICATION": "Internet Communication",
    "INTERNET_SERVICES_DESC": " Sites related to services conducted over the Internet.",
    "INTERNET_SERVICES": "Internet Services",
    "INTERNET_USAGE_TREND": "Internet Usage Trend",
    "INTERNET": "Internet",
    "INTERPARK_DESC": " This protocol plug-in classifies the http traffic to the host www.interpark.com. It also classifies the ssl traffic to the Common Name .interpark.com",
    "INTERPARK": "Interpark",
    "INTUIT_DESC": " This protocol plug-in classifies the http traffic to the host intuit.com",
    "INTUIT": "Intuit",
    "INVALID_API_KEY": "Invalid API Key",
    "INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE": "Invalid Max Batch Size unit for the SIEM type. This number must be between 16 and 512 KB.",
    "INVALID_NAME": "Please enter a valid Name",
    "INVALID_USERNAME_OR_PASSWORD": "Invalid Login ID or Password",
    "IOS_APPSTORE_DESC": " The Apple App Store is a digital application distribution platform for iOS developed and maintained by Apple Inc",
    "IOS_APPSTORE": "IOS Appstore",
    "IOS_OTA_UPDATE_DESC": " iOS OTA Update is the protocol used for iOS updates Over The Air",
    "IOS_OTA_UPDATE": "iOS OTA Update",
    "IOS_OTHERS": "iOS (Other)",
    "IOS_TUNES": "IOS iTunes App",
    "IOT": "IoT traffic",
    "IP_ABUSE_CHECK_DESCRIPTION": "Check IPs that may be abusing proxies",
    "IP_ABUSE_CHECK": "IP Abuse Check",
    "IP_ADDRESS_FROM": "IP Address from",
    "IP_ADDRESS_HA_DEVICE": "Enter the virtual IP address for the HA device.",
    "IP_ADDRESS_LAN_SECTION": "Enter an IP address for the LAN section of your device.",
    "IP_ADDRESS_OPTIONAL": "IP Address (optional)",
    "IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN": "IP Address Or FQDN Or WildCard FQDN",
    "IP_ADDRESS_OR_FQDN": "IP Address or FQDN",
    "IP_ADDRESS_OR_WILDCARD_FQDN": "IP Address Or WildCard FQDN",
    "IP_ADDRESS_RANCE_CIDR": "IP Address Range / CIDR",
    "IP_ADDRESS_SHOULD_NOT_BE_PART_OF_ADDRESS_RANGES_POOL": "IP address should not be part of DHCP address pool.",
    "IP_ADDRESS_TO": "IP Address to",
    "IP_ADDRESS_WAN_SECTION": "Enter an IP address for the WAN section of your device.",
    "IP_ADDRESS": "IP Address",
    "IP_ADDRESSES": "IP Addresses",
    "IP_ADDRESSESS": "IP Address",
    "IP_BASED_COUNTRIES": "IP-Based Countries",
    "IP_CAT_LOOKUP": "IP cat dynamic lookup enable",
    "IP_CATEGORIES": "IP Categories",
    "IP_CONNECT_TRANSPARENT": "switch CONNECTs to IP:port to transparent mode",
    "IP_DESC": " The Internet Protocol (IP) is the principal communications protocol in the Internet protocol suite for relaying datagrams across network boundaries",
    "IP_EXAMPLE_WITH_RANGE_CIDR": "Eg: ********, ********, ********-********, ********/24",
    "IP_EXAMPLE": "Eg: ********,********",
    "IP_FQDN_GROUPS": "IP & FQDN Groups",
    "IP_INFO": "IP Info",
    "IP_POOL": "IP Pool",
    "IP_UNKNOWN_DESC": " This identifies IP traffic for which which more granular app cannot be determined",
    "IP_UNKNOWN": "IP Unknown",
    "IP": "IP",
    "IP6_DESC": " Internet Protocol version 6 (IPv6) is the latest version of the Internet Protocol the communications protocol that provides an identification and location system for computers on networks and routes traffic across the Internet",
    "IP6": "IP6",
    "IPASS_DESC": " iPass is the industry pioneer in global, mobile connectivity, ensuring unlimited access to unlimited content on an unlimited number of devices",
    "IPASS": "Ipass",
    "IPERF_DESC": " The iperf protocol is used by the self-titled tool for network performance measures",
    "IPERF": "Iperf",
    "IPLAYER_DESC": " iPlayer",
    "IPLAYER": "iPlayer",
    "IPSEC_DESC": " IPSec protocol provides services for securing hosts communications. IPsec provides two security services Authentication Header (AH); which allows authentication of the sender and Encapsulating Security Payload (ESP); which allows both authentication of the sender and encryption of data",
    "IPSEC": "IpSec",
    "IPV4_ALL_DESTINATION_GROUP_NAME": "IP V4 All Destination Group Name",
    "IPV4_DNS_RESOLUTION_ONLY": "IPv4 DNS Resolution Only",
    "IPV4": "IPv4 encapsulation",
    "IPV6_HERE": "IPv6 i-am-here",
    "IPV6_WHERE": "IPv6 where-are-you",
    "IPV6": "IP6 header",
    "IPV6CP_DESC": " This  Protocol is used for establishing and configuring IPv6 over PPP",
    "IPV6CP": "IPV6CP",
    "IPXRIP_DESC": " RIPIPX is the equivalent of the RIP protocol in Novell networks",
    "IPXRIP": "RIPIPX",
    "IQIYI_DESC": " This protocol plug-in classifies the http traffic to the host iqiyi.com. It also classifies the ssl traffic to the Common Name .iqiyi.com",
    "IQIYI": "iqiyi.com",
    "IRAN_ASIA_TEHRAN": "Asia/Tehran",
    "IRAN": "Iran",
    "IRAQ_ASIA_BAGHDAD": "Asia/Baghdad",
    "IRAQ": "Iraq",
    "IRC_DESC": " IRC (Internet Relay Chat) is an instant messaging protocol",
    "IRC_GALLERIA_DESC": " This protocol plug-in classifies the http traffic to the host irc-galleria.net",
    "IRC_GALLERIA": "IRC-Galleria",
    "IRC_TRANSFER_DESC": " This protocol is used to transport data in IRC file transfer",
    "IRC_TRANSFER": "IRC File Transfer",
    "IRC": "IRC",
    "IRCS_DESC": " IRCs is the secure version of the IRC protocol",
    "IRCS": "Secure IRC",
    "IRELAND_EUROPE_DUBLIN": "Europe/Dublin",
    "IRELAND": "Ireland",
    "IS_NULL": "Is Null",
    "ISAE_3402": "ISAE 3402",
    "ISAKMP_DESC": " The Internet Security Association and Key Management Protocol (ISAKMP) defines procedures and packet formats to establish, negotiate, modify and delete Security Associations (SA)",
    "ISAKMP": "ISAKMP",
    "ISLE_OF_MAN_EUROPE_ISLE_OF_MAN": "Europe/Isle of Man",
    "ISLE_OF_MAN": "Isle of Man",
    "ISRAEL_ASIA_JERUSALEM": "Asia/Jerusalem",
    "ISRAEL": "Israel",
    "ISRAELCENTRAL": "(Middle East) Israel Central",
    "ISSUER": "Issuer",
    "ITALKI_DESC": " This protocol plug-in classifies the http traffic to the host italki.com. It also classifies the ssl traffic to the Common Name .italki.com",
    "ITALKI": "italki",
    "ITALY_EUROPE_ROME": "Europe/Rome",
    "ITALY": "Italy",
    "ITALYNORTH": "(Europe) Italy North",
    "ITEMS_TOTAL": "Items Total",
    "ITSMY_DESC": " This protocol plug-in classifies the http traffic to the host mobile.itsmy.com",
    "ITSMY": "GameCloud (itsmy.com)",
    "ITUNES_DESC": " iTunes is an Apple proprietary digital media player application, used for playing and organizing digital music and video files",
    "ITUNES": "iTunes",
    "ITUNESU": "iTunes U",
    "IVORY_COAST": "Ivory Coast",
    "IWF": "IWF",
    "IWIW_DESC": " This protocol plug-in classifies the http traffic to the host iwiw.hu",
    "IWIW": "Iwiw",
    "JABBER_DESC": " Jabber is an open standard instant messaging and presence system involving the XMPP protocol",
    "JABBER_TRANSFER_DESC": " Jabber transfer is an open standard to transfer file between two Jabber clients",
    "JABBER_TRANSFER": "Jabber File Transfer",
    "JABBER": "Jabber",
    "JAIKU_DESC": " This protocol plug-in classifies the http traffic to the host jaiku.com",
    "JAIKU": "jaiku.com",
    "JAILBREAK": "Jailbreak Enabled",
    "JAILBROKEN_ROOTED": "Jailbroken/Rooted",
    "JAJAH_DESC": " Jajah is a VoIP provider owned by Telefonica Europe",
    "JAJAH": "Jajah",
    "JAMAICA_AMERICA_JAMAICA": "America/Jamaica",
    "JAMAICA": "Jamaica",
    "JAMMERDIRECT_DESC": " This protocol plug-in classifies the http traffic to the host jammerdirect.com",
    "JAMMERDIRECT": "Jammer Direct",
    "JANGO_DESC": " This protocol plug-in classifies the http traffic to the host jango.com",
    "JANGO": "Jango",
    "JANUS_END": "End of Janus error codes.",
    "JAPAN_ASIA_TOKYO": "Asia/Tokyo",
    "JAPAN": "Japan",
    "JAPANEAST": "(Asia Pacific) Japan East",
    "JAPANWEST": "(Asia Pacific) Japan West",
    "JAVA_UPDATE_DESC": " Java Update is the protocol for the update of the java virtual machines, also called JVM",
    "JAVA_UPDATE": "Java Update",
    "JEDI_DESC": " JEDI is the name of the CITRIX streaming connection protocol",
    "JEDI": "JEDI",
    "JERSEY_EUROPE_JERSEY": "Europe/Jersey",
    "JERSEY": "Jersey",
    "JINGDONG_DESC": " Popular chinese on-line hi-tech shop",
    "JINGDONG": "JingDong",
    "JIOINDIACENTRAL": "(Asia Pacific) Jio India Central",
    "JIOINDIAWEST": "(Asia Pacific) Jio India West",
    "JIRA_DESC": " This protocol plug-in classifies the http traffic to the host onjira.com. It also classifies the ssl traffic to the Common Name onjira.com",
    "JIRA": "JIRA",
    "JIVE_DESC": " Jive Software is a software company in the social business software industry headquartered in Palo Alto, California",
    "JIVE": "Jive",
    "JNE_DESC": " This protocol plug-in classifies the http traffic to the host jne.co.id",
    "JNE": "JNE",
    "JOB_EMPLOYMENT_SEARCH_DESC": " Sites related to job postings or employment opportunities.",
    "JOB_EMPLOYMENT_SEARCH": "Job/Employment Search",
    "JOB_SEARCH_DESC": " Sites related to job postings or employment opportunities.",
    "JOB_SEARCH": "Job/Employment Search",
    "JOBSTREET_DESC": " This protocol plug-in classifies the http traffic to the host jobstreet.co.id",
    "JOBSTREET": "JobStreet",
    "JOONGANG_DAILY_DESC": " This protocol plug-in classifies the http traffic to the hosts www.joins.com and www.joinsmsn.com",
    "JOONGANG_DAILY": "Joongang Daily",
    "JOOST_DESC": " Joost",
    "JOOST": "Joost",
    "JORDAN_ASIA_AMMAN": "Asia/Amman",
    "JORDAN": "Jordan",
    "JPEG": "Jpeg Files",
    "JS_VIEW": "JS View",
    "JSON_CLOUDINFO_STAGGER_SIZE": "No. of instances whose cloudinfo is sent from cloud CA to FCC CA",
    "JSON_CLOUDINFO": "Send Cloudinfo From cloud CA to FCC CA as JSON",
    "JSONNOTFOUND": "Json file not found",
    "JUBII_DESC": " This protocol plug-in classifies the http traffic to the host jubii.dk",
    "JUBII": "Jubii",
    "JUSTIN_TV_DESC": " This protocol plug-in classifies the http traffic to the host justin.tv. It also classifies the ssl traffic to the Common Name .justin.tv",
    "JUSTIN_TV": "Justin.tv",
    "K_12_SEX_EDUCATION": "K-12 Sex Education",
    "K_12": "K-12",
    "KAIOO_DESC": " This protocol plug-in classifies the http traffic to the host kaioo.com",
    "KAIOO": "kaioo.com",
    "KAIXIN_CHAT_DESC": " This protocol plug-in classifies the http traffic to the host kaixin001.com",
    "KAIXIN_CHAT": "kaixin",
    "KAKAKU_DESC": " Website dedicated to price comparisons and product tests",
    "KAKAKU": "kakaku.com",
    "KAKAOTALK_DESC": " KakaoTalk is an instant messenging platform for mobile devices; users or group of users can send messages, share photos, videos and contact information",
    "KAKAOTALK": "KakaoTalk",
    "KANKAN_DESC": " Chinese video streaming website",
    "KANKAN": "kankan.com",
    "KAPANLAGI_DESC": " This protocol plug-in classifies the http traffic to the host kapanlagi.com",
    "KAPANLAGI": "KapanLagi",
    "KAROSGAME_DESC": " This protocol plug-in classifies the http traffic to the host karosgame.ru",
    "KAROSGAME": "Karos (karosgame.ru)",
    "KASKUS_DESC": " This protocol plug-in classifies the http traffic to the host kaskus.co.id",
    "KASKUS": "Kaskus",
    "KASPERSKY_DESC": " This protocol plug-in classifies the http traffic to the host kaspersky.com",
    "KASPERSKY_UPDATE_DESC": " Kaspersky_update is the protocol used for Kaspersky softwares updates",
    "KASPERSKY_UPDATE": "Kaspersky Update",
    "KASPERSKY": "Kaspersky",
    "KAZAA_DESC": " Kazaa is a peer-to-peer protocol",
    "KAZAA": "Kazaa",
    "KAZAKHSTAN_ASIA_ALMATY": "Asia/Almaty",
    "KAZAKHSTAN_ASIA_AQTAU": "Asia/Aqtau",
    "KAZAKHSTAN_ASIA_AQTOBE": "Asia/Aqtobe",
    "KAZAKHSTAN_ASIA_ORAL": "Asia/Oral",
    "KAZAKHSTAN_ASIA_QYZYLORDA": "Asia/Qyzylorda",
    "KAZAKHSTAN": "Kazakhstan",
    "KB_BANK_DESC": " This protocol plug-in classifies the http traffic to the host .kbstar.com. It also classifies the ssl traffic to the Common Name .kbstar.com",
    "KB_BANK": "KB Bank (kbstar.com)",
    "KBS_DESC": " This protocol plug-in classifies the http traffic to the host www.kbs.co.kr",
    "KBSTAR_DESC": " This web service is closed",
    "KBSTAR": "kbstar (Closed)",
    "KEEPLIVE": "Send keepalive records to keep the TCP connection alive if data is not flowing",
    "KEEZMOVIES_DESC": " This protocol plug-in classifies the http traffic to the host keezmovies.com",
    "KEEZMOVIES": "KeezMovies",
    "KEK_ROTATION": "enable key rotation on sme ",
    "KEMENKUMHAM_DESC": " This protocol plug-in classifies the http traffic to the host kemenkumham.go.id",
    "KEMENKUMHAM": "kemenkumham.go.id",
    "KENYA_AFRICA_NAIROBI": "Africa/Nairobi",
    "KENYA": "Kenya",
    "KERBEROS_SEC_DESC": "Kerberos is a computer network authentication protocol which works on the basis of 'tickets' to allow nodes communicating over a non-secure network to prove their identity to one another in a secure manner.",
    "KERBEROS_SEC": "Kerberos",
    "KERBEROS_SHARED_KEY": "Domain Trust Password",
    "KEY": "Key",
    "KHAN_DESC": " This protocol plug-in classifies the http traffic to the host khan.co.kr",
    "KHAN": "Khan (khan.co.kr)",
    "KHANACADEMY": "Khan Academy",
    "KICKASSTORRENTS_DESC": " Kickass Torrents is a popular torrent and magnet search engine",
    "KICKASSTORRENTS": "KickAssTorrents",
    "KIK_DESC": " Kik Messenger is a Chinese Instant Messaging service",
    "KIK": "Kik Messenger",
    "KINDLE": "Kindle",
    "KINO_DESC": " This protocol plug-in classifies the http traffic to the host kino.to",
    "KINO": "Kino",
    "KIRIBATI_PACIFIC_ENDERBURY": "Pacific/Enderbury",
    "KIRIBATI_PACIFIC_KIRITIMATI": "Pacific/Kiritimati",
    "KIRIBATI_PACIFIC_TARAWA": "Pacific/Tarawa",
    "KIRIBATI": "Kiribati",
    "KIWIBOX_DESC": " This protocol plug-in classifies the http traffic to the host kiwibox.com",
    "KIWIBOX": "Kiwibox",
    "KLIKBCA_DESC": " This protocol plug-in classifies the http traffic to the host klikbca.com",
    "KLIKBCA": "KlikBCA",
    "KOMPAS_DESC": " This protocol plug-in classifies the http traffic to the host kompas.com",
    "KOMPAS": "Kompas",
    "KOMPASIANA_DESC": " This protocol plug-in classifies the http traffic to the host kompasiana.com",
    "KOMPASIANA": "Kompasiana",
    "KONAMINET_DESC": " This protocol plug-in classifies the http traffic to the host konaminet.jp. It also classifies the ssl traffic to the Common Name konaminet.jp",
    "KONAMINET": "KONAMI",
    "KOOLIM_DESC": " This protocol plug-in classifies the http traffic to the host koolim.com",
    "KOOLIM": "KoolIm",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG": "Asia/Pyongyang",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF": "Korea (Democratic People's Republic of)",
    "KOREA_REPUBLIC_OF_ASIA_SEOUL": "Asia/Seoul",
    "KOREA_REPUBLIC_OF": "Korea (Republic of)",
    "KOREA": "Korea",
    "KOREACENTRAL": "(Asia Pacific) Korea Central",
    "KOREASOUTH": "(Asia Pacific) Korea South",
    "KUWAIT_ASIA_KUWAIT": "Asia/Kuwait",
    "KUWAIT": "Kuwait",
    "KYRGYZSTAN_ASIA_BISHKEK": "Asia/Bishkek",
    "KYRGYZSTAN": "Kyrgyzstan",
    "L2TP_DESC": " Layer Two Tunneling Protocol (L2TP) is an extension of the Point-to-Point Tunneling Protocol (PPTP) used by an Internet service provider (ISP) to enable the operation of a virtual private network (VPN) over the Internet",
    "L2TP": "L2TP",
    "LAN_DESTINATIONS_GROUP": "LAN Destinations Group",
    "LAN_DNS": "LAN DNS",
    "LAN_IP_GROUP": "LAN IP Group",
    "LAN_PRI_DNS": "LAN Primary DNS Server",
    "LAN_RX": "LAN Rx",
    "LAN_SEC_DNS": "LAN Secondary DNS Server",
    "LAN_TX": "LAN Tx",
    "LAN": "LAN",
    "LANGUAGE": "Language",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE": "Asia/Vientiane",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC": "Lao People's Democratic Republic",
    "LAOS": "Laos",
    "LARGE": "Large",
    "last_1_hour": "Last 1 Hour",
    "LAST_1_HOUR": "Last 1 Hour",
    "last_1_min": "Last 1 Min",
    "LAST_1_MIN": "Last 1 Minute",
    "last_1_month": "Last 1 Month",
    "LAST_1_MONTH": "Last 1 Month",
    "last_1_week": "Last 1 Week",
    "LAST_1_WEEK": "Last 1 Week",
    "last_10_hours": "Last 10 Hours",
    "LAST_10_HOURS": "Last 10 Hours",
    "last_15_mins": "Last 15 Mins",
    "LAST_15_MINS": "Last 15 Minutes",
    "last_2_hours": "Last 2 Hours",
    "LAST_2_HOURS": "Last 2 Hours",
    "last_2_mins": "Last 2 Mins",
    "LAST_2_MINS": "Last 2 Minutes",
    "LAST_24_HOURS": "Last 24 Hours",
    "last_24_hrs": "Last 24 Hours",
    "last_30_mins": "Last 30 Mins",
    "LAST_30_MINS": "Last 30 Minutes",
    "last_5_hours": "Last 5 Hours",
    "LAST_5_HOURS": "Last 5 Hours",
    "last_5_mins": "Last 5 Mins",
    "LAST_5_MINS": "Last 5 Minutes",
    "LAST_ACTIVE": "Last Active",
    "LAST_CONFIG_TEMPLATE_PUSH_FAILED": "Last config template push failed",
    "LAST_CONNECTIVITY_TEST": "Last Connectivity Test",
    "LAST_DISCOVERY": "Last Discovery",
    "LAST_HEARTBEAT_RECEIVED_ON": "Last Heartbeat Received On",
    "LAST_KNOW_IP": "Last Known IP",
    "LAST_KNOWN_IP": "Last Known IP",
    "LAST_MODIFIED_BY": "Last Modified By",
    "LAST_MODIFIED_ON": "Last Modified On",
    "LAST_SYNC": "Last Sync",
    "LAST_UPDATE": "Last Updated",
    "LAST_UPDATES": "Last Updates",
    "LAST_UPGRADE_ON": "Last Upgrade On",
    "LASTEST_SYNC": "Latest Sync",
    "LATITUDE": "Latitude",
    "LATVIA_EUROPE_RIGA": "Europe/Riga",
    "LATVIA": "Latvia",
    "LAUNCH_CLOUDFORMATION_TEMPLATE_AWS_CONSOLE": "Launch Cloudformation template in AWS console",
    "LAUNCH_CLOUDFORMATION": "Launch Cloudformation",
    "LDAP_CONNECTION_DOWN": "LDAP Connection down",
    "LDAP_DESC": " LDAP (Lightweight Directory Access Protocol) is a protocol used for accessing directory services. Windows environments use this protocol to send queries to Active Directory",
    "LDAP_FAILURE": "LDAP Failure",
    "LDAP_SETTINGS": "LDAP Settings",
    "LDAP_SUCCESS": "LDAP Success",
    "LDAP": "LDAP",
    "LDAPS_DESC": " Secure LDAP is the Secure version of the LDAP protocol",
    "LDAPS": "LDAPS",
    "LEARN_TO_SETUP_EC2_INSTANCE": "Learn How To Set Up the EC2 Instance",
    "LEBANON_ASIA_BEIRUT": "Asia/Beirut",
    "LEBANON": "Lebanon",
    "LESOTHO_AFRICA_MASERU": "Africa/Maseru",
    "LESOTHO": "Lesotho",
    "LESS_THAN": "Less than",
    "LIBERIA_AFRICA_MONROVIA": "Africa/Monrovia",
    "LIBERIA": "Liberia",
    "LIBYA": "Libya",
    "LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI": "Africa/Tripoli",
    "LIBYAN_ARAB_JAMAHIRIYA": "Libyan Arab Jamahiriya",
    "LIECHTENSTEIN_EUROPE_VADUZ": "Europe/Vaduz",
    "LIECHTENSTEIN": "Liechtenstein",
    "LIMITED_AVAILABILITY": "Limited Availability",
    "LIMITED": "Limited",
    "LINGERIE_BIKINI": "Lingerie/Bikini",
    "LINK_SCORE": "Link Score",
    "LINUX_OS": "Linux OS",
    "LINUX": "Linux",
    "LITHUANIA_EUROPE_VILNIUS": "Europe/Vilnius",
    "LITHUANIA": "Lithuania",
    "LOAD_BALANCER_IP_ADDRESS": "Load Balancer IP Address",
    "LOAD_BALANCER": "Load Balancer",
    "LOAD_MORE": "Load more",
    "LOC_DEFAULT": "Road Warrior",
    "LOCAL_EGRESS": "Local Egress",
    "LOCAL_SWITCH": "Local",
    "LOCAL_TIME_ZONE_CC_GROUP": "Local time zone of the cloud connector Group",
    "LOCATION_ALREADY_IN_USE_PLEASE_ENTER_A_NEW_LOCATION": "Location is not valid. Please enter a new one.",
    "LOCATION_CREATION": "Location Creation",
    "LOCATION_DETAILS_OPTIONAL": "Location Details (optional)",
    "LOCATION_DETAILS": "Location Details",
    "LOCATION_GROUP_TYPE": "Location Type",
    "LOCATION_GROUP": "Location Group",
    "LOCATION_GROUPS": "Location Groups",
    "LOCATION_INFORMATION": "Location Information",
    "LOCATION_MANAGEMENT": "Location Management",
    "LOCATION_NAME": "Location Name",
    "LOCATION_SUBLOCATION": "Location/Sublocation",
    "LOCATION_TEMPLATE": "Location Template",
    "LOCATION_TEMPLATES": "Location Templates",
    "LOCATION_TYPE": "Location Type",
    "LOCATION_UNAUTHENTICATED_AUP_FREQUENCY": "Custom AUP Frequency (Days)",
    "LOCATION": "Location",
    "LOCATIONS": "Locations",
    "LOG_AND_CONTROL_FORWARDING": "Log and Control Forwarding",
    "LOG_AND_CONTROL_GATEWAY": "Log and Control Gateway",
    "LOG_AND_CONTROL_GW": "Log & Control GW",
    "LOG_AND_CONTROL": "Log & Control",
    "LOG_GW_CONN_CLOSE": "Log gateway active connection closed.",
    "LOG_GW_CONN_SETUP_FAIL": "Log gateway connection setup failed (Internal error).",
    "LOG_GW_CONNECT_FAIL": "Log gateway connection failed (Network error).",
    "LOG_GW_DNS_RESOLVE_FAIL": "Log gateway DNS resolution failed.",
    "LOG_GW_KA_FAIL": "Log gateway connection keepalive failed.",
    "LOG_GW_NO_CONN": "Log gateway connection not yet initiated by client.",
    "LOG_GW_PAC_RESOLVE_FAIL": "Log gateway PAC resolution failed.",
    "LOG_GW_PAC_RESOLVE_NOIP": "Log gateway PAC resolution returned no IPS.",
    "LOG_GW_PROTO_MSG_ERROR": "Message format error in log GW response.",
    "LOG_GW_SRV_ERR_RESPONSE": "Log gateway received HTTP error response from server.",
    "LOG_GW_UNHEALTHY": "Log gateway is unhealthy (Transient state).",
    "LOG_INFO_TYPE": "Log Info Type",
    "LOG_STREAMING": "Log Streaming",
    "LOG_TIME": "Log Time",
    "LOG_TYPE": "Log Type",
    "LOGGED_TIME": "Logged Time",
    "LOGGING": "Logging",
    "LOGIN_ID": "Login ID",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_GROUP": "Select an App Connector group name from the drop-down menu.",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_KEY": "Select a provisioning key from the drop-down menu",
    "LOGIN_TYPE": "Login Type",
    "LOGIN": "Login",
    "LOGS": "Logs",
    "LONGITUDE": "Longitude",
    "LOOKUP_URL_CATEGORY": "Lookup URL Classifications",
    "LOOKUP": "Lookup URL",
    "LUXEMBOURG_EUROPE_LUXEMBOURG": "Europe/Luxembourg",
    "LUXEMBOURG": "Luxembourg",
    "MAC_ADDRESS": "Mac Address",
    "MAC_OS": "Mac",
    "MACAO_ASIA_MACAU": "Asia/Macau",
    "MACAO": "Macao",
    "MACAU": "Macau",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE": "Europe/Skopje",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF": "Macedonia (The Former Yugoslav Republic of)",
    "MACEDONIA": "Macedonia",
    "MADAGASCAR_INDIAN_ANTANANARIVO": "Indian/Antananarivo",
    "MADAGASCAR": "Madagascar",
    "MALAWI_AFRICA_BLANTYRE": "Africa/Blantyre",
    "MALAWI": "Malawi",
    "MALAYSIA_ASIA_KUALA_LUMPUR": "Asia/Kuala Lumpur",
    "MALAYSIA_ASIA_KUCHING": "Asia/Kuching",
    "MALAYSIA": "Malaysia",
    "MALDIVES_INDIAN_MALDIVES": "Indian/Maldives",
    "MALDIVES": "Maldives",
    "MALI_AFRICA_BAMAKO": "Africa/Bamako",
    "MALI": "Mali",
    "MALICIOUS_TLD": "Malicious TLDs",
    "MALTA_EUROPE_MALTA": "Europe/Malta",
    "MALTA": "Malta",
    "MALWARE_SITE": "Malicious Content",
    "MANAGE_ACCOUNT": "Management Account",
    "MANAGE_GCP_ACCOUNT_LOGISTIC_PARTNER": "Manage Google Cloud Account - Logistic Partner",
    "MANAGED_APP_DEF_ZPA": "Managed App Definition with ZPA",
    "MANAGED_APP_DEF": "Managed App Definition",
    "Management IP": "Management IP",
    "MANAGEMENT_DEFAULT_GATEWAY": "Management Default Gateway",
    "MANAGEMENT_DETAILS": "Management Details",
    "MANAGEMENT_DNS_SERVER": "Management DNS Server",
    "MANAGEMENT_INFORMATION": "Management Information",
    "MANAGEMENT_INTERFACE": "Management Interface",
    "MANAGEMENT_IP_ADDRESS_POOL": "Management IP Address Pool",
    "MANAGEMENT_IP_ADDRESS": "Management IP Address",
    "MANAGEMENT_IP": "Management IP",
    "MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS": "Management Outgoing Gateway IP Address",
    "MANAGEMENT": "Management",
    "MANUAL_MANAGEMENT_IP": "Manual Management IP",
    "MANUAL_SERVICE_IP": "Manual Service IP",
    "MANUAL": "Manual",
    "MARIJUANA": "Marijuana",
    "MARSHALL_ISLANDS_PACIFIC_KWAJALEIN": "Pacific/Kwajalein",
    "MARSHALL_ISLANDS_PACIFIC_MAJURO": "Pacific/Majuro",
    "MARSHALL_ISLANDS": "Marshall Islands",
    "MARTINIQUE_AMERICA_MARTINIQUE": "America/Martinique",
    "MARTINIQUE": "Martinique",
    "MATURE_HUMOR": "Mature Humour",
    "MAURITANIA_AFRICA_NOUAKCHOTT": "Africa/Nouakchott",
    "MAURITANIA": "Mauritania",
    "MAURITIUS_INDIAN_MAURITIUS": "Indian/Mauritius",
    "MAURITIUS": "Mauritius",
    "MAX_AMF_NUMBER": "A maximum of 5 AMFs can be added.",
    "MAX_CAPACITY": "Maximum Capacity",
    "MAX_CHARACTER_LIMIT_EXCEEDED": "Max Character Limit Exceeded",
    "MAX_EC_COUNT": "Max Count",
    "MAX_INTERFACES_NUMBER": "The maximum number of Interfaces was added.",
    "MAX_LEASE_TIME": "Max Lease Time (sec)",
    "MAX_NUM_DESINATION_ADRESS_IS_1000": "Max number of destination addresses allowed per rule is 1000.",
    "MAX_REUSE_PROVISIONING_KEY": "Maximum Reuse of Provisioning Key",
    "MAX_STATIC_ROUTES_NUMBER": "The maximum number of static routes is 32.",
    "MAX_SUB_INTERFACES_NUMBER": "The maximum number of interfaces was reached.",
    "MAX_SUBINTERFACE_STATIC_LEASES": "The maximum number of static leases is 32.",
    "MAX_USER_TUNNELS_PER_CC": "Maximum User Tunnels Per Connector",
    "MAX_VALUE_LIMIT_ERROR": "Value exceeded the Limit of",
    "MAX_WAN_INTERFACES_NUMBER": "A maximum of 2 WAN Interfaces can be added.",
    "MAX": "Max",
    "MAYOTTE_INDIAN_MAYOTTE": "Indian/Mayotte",
    "MAYOTTE": "Mayotte",
    "ME_CENTRAL_1": "Middle East (UAE)",
    "ME_CENTRAL1_A": "me-central1-a",
    "ME_CENTRAL1_B": "me-central1-b",
    "ME_CENTRAL1_C": "me-central1-c",
    "ME_CENTRAL1": "me-central1",
    "ME_SOUTH_1": "Middle East (Bahrain)",
    "ME_SOUTH_1A": "me-south-1a",
    "ME_SOUTH_1B": "me-south-1b",
    "ME_SOUTH_1C": "me-south-1c",
    "ME_WEST1_A": "me-west1-a",
    "ME_WEST1_B": "me-west1-b",
    "ME_WEST1_C": "me-west1-c",
    "ME_WEST1": "me-west1",
    "MEDIUM": "Medium",
    "MEMORY": "Memory",
    "MEXICO_AMERICA_CANCUN": "America/Cancun",
    "MEXICO_AMERICA_CHIHUAHUA": "America/Chihuahua",
    "MEXICO_AMERICA_HERMOSILLO": "America/Hermosillo",
    "MEXICO_AMERICA_MAZATLAN": "America/Mazatlan",
    "MEXICO_AMERICA_MERIDA": "America/Merida",
    "MEXICO_AMERICA_MEXICO_CITY": "America/Mexico City",
    "MEXICO_AMERICA_MONTERREY": "America/Monterrey",
    "MEXICO_AMERICA_TIJUANA": "America/Tijuana",
    "MEXICO": "Mexico",
    "MEXICOCENTRAL": "(North Amercia) Mexico Central",
    "MGCP_CA_DESC": "Media Gateway Control Protocol CA Service",
    "MGCP_CA": "MGCP Call Agent",
    "MGCP_DESC": " MGCP protocol is used as signaling protocol for voice IP applications",
    "MGCP_UA_DESC": "Media Gateway Control Protocol UA Service",
    "MGCP_UA": "MGCP User Agent",
    "MGCP": "MGCP",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE": "Pacific/Kosrae",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE": "Pacific/Ponape",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK": "Pacific/Truk",
    "MICRONESIA_FEDERATED_STATES_OF": "Micronesia (Federated States of)",
    "MICRONESIA": "Micronesia",
    "MICROSOFT_AZURE": "Microsoft Azure",
    "MICROSOFT_HYPER_V": "Microsoft Hyper V",
    "MILITANCY_HATE_AND_EXTREMISM": "Militancy/Hate and Extremism",
    "MILITARY": "Military",
    "MIN_CAPACITY": "Minimum Capacity",
    "MIN_VALUE_LIMIT_ERROR": "Value is below the Limit of",
    "MINUTES": "Minutes",
    "MISCELLANEOUS_OR_UNKNOWN": "Miscellaneous or Unknown",
    "MODEL_NUMBER": "Model Number",
    "MODEL": "Model",
    "MOLDOVA_EUROPE_CHISINAU": "Europe/Chisinau",
    "MOLDOVA": "Moldova",
    "MONACO_EUROPE_MONACO": "Europe/Monaco",
    "MONACO": "Monaco",
    "MONDAY": "Monday",
    "MONGOLIA_ASIA_CHOIBALSAN": "Asia/Choibalsan",
    "MONGOLIA_ASIA_HOVD": "Asia/Hovd",
    "MONGOLIA_ASIA_ULAANBAATAR": "Asia/Ulaanbaatar",
    "MONGOLIA": "Mongolia",
    "MONTENEGRO_EUROPE_PODGORICA": "Europe/Podgorica",
    "MONTENEGRO": "Montenegro",
    "MONTSERRAT_AMERICA_MONTSERRAT": "America/Montserrat",
    "MONTSERRAT": "Montserrat",
    "MORE_INFO": "More info.",
    "MORE_ITEMS_SELECTED": "More Items Selected",
    "MOROCCO_AFRICA_CASABLANCA": "Africa/Casablanca",
    "MOROCCO": "Morocco",
    "MOZAMBIQUE_AFRICA_MAPUTO": "Africa/Maputo",
    "MOZAMBIQUE": "Mozambique",
    "MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS": "Microsoft Azure Deployment Guide for NSS",
    "MSI_URL_32BITS": "MSI URL (32 bit)",
    "MSI_URL_64BITS": "MSI URL (64 bit)",
    "MSN_DESC": " The MSN protocol allows the exchange of instant messages. The MSN protocol is used by the Microsoft software Microsoft Messenger",
    "MSN_EXPLORER": "MSN Explorer",
    "MSN_GROUPS_DEPRECATED": "MSN Groups",
    "MSN_GROUPS_DESC": " MSN Groups was a website part of the MSN network which hosted online communities, and which contained Web pages, hosted images, and contained a message board. MSN Groups was shut down on February 2009 as part of a migration of online applications and services to the Windows Live brand, and later renovated as Windows Live Groups",
    "MSN_GROUPS": "MSN Groups",
    "MSN_MESSENGER": "MSN Messenger",
    "MSN_MSDW": "MSN MSDW",
    "MSN_SEARCH_DESC": " This protocol is used for sending user queries to the MSN Live search engine",
    "MSN_SEARCH": "MSN Search",
    "MSN_VIDEO_DESC": " This protocol is used by MSN Messenger for video conversations (is not used anymore since MSN version 8.X)",
    "MSN_VIDEO": "MSN Video",
    "MSN_WEB_MESSENGER": "MSN Messenger",
    "MSN": "MSN",
    "MSNMOBILE_DESC": " MSN Mobile is the MSN instant messenger for mobile",
    "MSNMOBILE": "MSN Mobile",
    "MTS_ERROR": "MTS app server error.",
    "MTU_TITLE": "MTU",
    "MULTIFEEDLOG": "Metrics",
    "MULTIPLE_APPLIANCES_ADDED_INFO": "You can see the new appliances in the Appliances page. For more information view Connectors {1}Help Portal.{2}",
    "MULTIPLE_APPLIANCES_ADDED": "new appliances have been added to your tenant.",
    "MULTIPLE": "Multiple",
    "MUSIC": "Music",
    "MY_ACTIVATION_STATUS": "MY ACTIVATION STATUS",
    "MY_PROFILE": "My Profile",
    "MYANMAR_ASIA_RANGOON": "Asia/Rangoon",
    "MYANMAR": "Myanmar",
    "NA": "N/A",
    "NAME_AND_CREDENTIAL_TEXT": "Enter the Customer Service Account to access your Google Projects . You can use a single service Account for multiple Projects. Ensure the service account has access permissions or add Customer service account in below section and proceed to next step to use Terraform script for creating credentials in GCP.",
    "NAME_AND_CREDENTIAL": "Name And Credential",
    "NAME_MAX_LIMIT_ERROR": "This field cannot contain more than 255 characters",
    "NAME_VALUE_PAIRS": "Name Value Pairs",
    "NAME": "Name",
    "NAMESPACE_OPTIONAL": "Namespace (Optional)",
    "NAMESPACE": "Namespace",
    "NAMIBIA_AFRICA_WINDHOEK": "Africa/Windhoek",
    "NAMIBIA": "Namibia",
    "NANOLOG_STREAMING_SERVICES": "Nanolog Streaming Service",
    "NAT_IP_ADDRESS": "NAT IP Address",
    "NAURU_PACIFIC_NAURU": "Pacific/Nauru",
    "NAURU": "Nauru",
    "NAVIGATE_TO_ADMINISTRATION": "Navigate to Administration",
    "NEPAL_ASIA_KATMANDU": "Asia/Katmandu",
    "NEPAL": "Nepal",
    "NET_MASK": "Net Mask",
    "NETBIOS_DESC": "NetBIOS Name/Datagram/Session Service",
    "NETBIOS": "NetBIOS",
    "NETHERLANDS_ANTILLES_AMERICA_CURACAO": "America/Curacao",
    "NETHERLANDS_ANTILLES": "Netherlands Antilles",
    "NETHERLANDS_EUROPE_AMSTERDAM": "Europe/Amsterdam",
    "NETHERLANDS": "Netherlands",
    "NETMEETING_DESC": "Microsoft NetMeeting allows users to teleconference using the Internet as the transmission medium",
    "NETMEETING_ILS_DESC": " NetMeeting ILS is the protocol used between Netmeeting and Internet Locator Servers (ILS). Netmeeting is a VoIP and multi-point videoconferencing client included in many versions of Microsoft Window",
    "NETMEETING_ILS": "NetMeeting ILS",
    "NETMEETING": "NetMeeting",
    "NETWORK_INTERFACE_ID": "Network Interface ID",
    "NETWORK_PROTOCOL_ADFS": "Any distributed FS",
    "NETWORK_PROTOCOL_AH": "IP6 Auth Header",
    "NETWORK_PROTOCOL_AHIP": "Any Host Internal Protocol",
    "NETWORK_PROTOCOL_APES": "Any Private Encr. Scheme",
    "NETWORK_PROTOCOL_ARGUS": "Argus",
    "NETWORK_PROTOCOL_AX25": "AX.25 Frames",
    "NETWORK_PROTOCOL_BHA": "BHA",
    "NETWORK_PROTOCOL_BLT": "Bulk Data Transfer",
    "NETWORK_PROTOCOL_BRSATMON": "BackRoom SATNET Monitoring",
    "NETWORK_PROTOCOL_CARP": "CARP",
    "NETWORK_PROTOCOL_CFTP": "CFTP",
    "NETWORK_PROTOCOL_CHAOS": "CHAOS",
    "NETWORK_PROTOCOL_CMTP": "Control Message Transport",
    "NETWORK_PROTOCOL_CPHB": "Comp. Prot. HeartBeat",
    "NETWORK_PROTOCOL_CPNX": "Comp. Prot. Net. Executive",
    "NETWORK_PROTOCOL_DDP": "Datagram Delivery",
    "NETWORK_PROTOCOL_DGP": "Dissimilar Gateway Prot.",
    "NETWORK_PROTOCOL_DSTOPTS": "IP6 destination option",
    "NETWORK_PROTOCOL_EGP": "Exterior Gateway Protocol",
    "NETWORK_PROTOCOL_EMCON": "EMCON",
    "NETWORK_PROTOCOL_ENCAP": "Encapsulation Header",
    "NETWORK_PROTOCOL_EON": "ISO cnlp",
    "NETWORK_PROTOCOL_ESP": "IP6 Encap Sec. Payload",
    "NETWORK_PROTOCOL_ETHERIP": "Ethernet IP encapsulation",
    "NETWORK_PROTOCOL_FRAGMENT": "IP6 fragmentation header",
    "NETWORK_PROTOCOL_GGP": "Gateway^2 (deprecated)",
    "NETWORK_PROTOCOL_GMTP": "GMTP",
    "NETWORK_PROTOCOL_GRE": "General Routing Encap.",
    "NETWORK_PROTOCOL_HELLO": "Hello Routing Protocol",
    "NETWORK_PROTOCOL_HMP": "Host Monitoring",
    "NETWORK_PROTOCOL_ICMP": "Control Message Protocol",
    "NETWORK_PROTOCOL_ICMPV6": "ICMP6",
    "NETWORK_PROTOCOL_IDP": "Xns Idp",
    "NETWORK_PROTOCOL_IDPR": "InterDomain Policy Routing",
    "NETWORK_PROTOCOL_IDRP": "InterDomain",
    "NETWORK_PROTOCOL_IGMP": "Group Mgmt Protocol",
    "NETWORK_PROTOCOL_IGP": "NSFNET-IGP",
    "NETWORK_PROTOCOL_IGRP": "Cisco/GXS IGRP",
    "NETWORK_PROTOCOL_IL": "IL transport protocol",
    "NETWORK_PROTOCOL_INLSP": "Integ. Net Layer Security",
    "NETWORK_PROTOCOL_INP": "Merit Internodal",
    "NETWORK_PROTOCOL_IP": "Dummy for IP",
    "NETWORK_PROTOCOL_IPCOMP": "Payload Compression (IPComp)",
    "NETWORK_PROTOCOL_IPCV": "Packet Core Utility",
    "NETWORK_PROTOCOL_IPEIP": "IP encapsulated in IP",
    "NETWORK_PROTOCOL_IPPC": "Pluribus Packet Core",
    "NETWORK_PROTOCOL_IPV4": "IPv4 encapsulation",
    "NETWORK_PROTOCOL_IPV6": "IP6 header",
    "NETWORK_PROTOCOL_IRTP": "Reliable Transaction",
    "NETWORK_PROTOCOL_KRYPTOLAN": "Kryptolan",
    "NETWORK_PROTOCOL_LARP": "Locus Address Resoloution",
    "NETWORK_PROTOCOL_LEAF1": "Leaf-1",
    "NETWORK_PROTOCOL_LEAF2": "Leaf-2",
    "NETWORK_PROTOCOL_MEAS": "DCN Measurement Subsystems",
    "NETWORK_PROTOCOL_MHRP": "Mobile Host Routing",
    "NETWORK_PROTOCOL_MICP": "Mobile Int.ing control",
    "NETWORK_PROTOCOL_MOBILE": "IP Mobility",
    "NETWORK_PROTOCOL_MTP": "Multicast Transport",
    "NETWORK_PROTOCOL_MUX": "Multiplexing",
    "NETWORK_PROTOCOL_ND": "Sun net disk proto (temp.)",
    "NETWORK_PROTOCOL_NHRP": "Next Hop Resolution",
    "NETWORK_PROTOCOL_NO_NEXT_HDR": "IP6 no next header",
    "NETWORK_PROTOCOL_NSP": "Network Services",
    "NETWORK_PROTOCOL_NVPII": "Network Voice",
    "NETWORK_PROTOCOL_OLD_DIVERT": "OLD Divert Pseudo-Proto",
    "NETWORK_PROTOCOL_OSPFIGP": "OSPFIGP",
    "NETWORK_PROTOCOL_PFSYNC": "PFSYNC",
    "NETWORK_PROTOCOL_PGM": "PGM",
    "NETWORK_PROTOCOL_PIGP": "Private Interior Gateway",
    "NETWORK_PROTOCOL_PIM": "Protocol Independent Mcast",
    "NETWORK_PROTOCOL_PRM": "Packet Radio Measurement",
    "NETWORK_PROTOCOL_PUP": "PUP",
    "NETWORK_PROTOCOL_PVP": "Packet Video Protocol",
    "NETWORK_PROTOCOL_RAW": "Raw IP packet",
    "NETWORK_PROTOCOL_RCCMON": "BBN RCC Monitoring",
    "NETWORK_PROTOCOL_RDP": "Reliable Data",
    "NETWORK_PROTOCOL_ROUTING": "IP6 Routing Header",
    "NETWORK_PROTOCOL_RSVP": "Resource Reservation",
    "NETWORK_PROTOCOL_RVD": "Remote Virtual Disk",
    "NETWORK_PROTOCOL_SATEXPAK": "SATNET/Backroom EXPAK",
    "NETWORK_PROTOCOL_SATMON": "Satnet Monitoring",
    "NETWORK_PROTOCOL_SCCSP": "Semaphore Comm. security",
    "NETWORK_PROTOCOL_SCTP": "SCTP",
    "NETWORK_PROTOCOL_SDRP": "Source Demand Routing",
    "NETWORK_PROTOCOL_SEP": "Sequential Exchange",
    "NETWORK_PROTOCOL_SKIP": "SKIP",
    "NETWORK_PROTOCOL_SRPC": "Strite RPC protocol",
    "NETWORK_PROTOCOL_ST": "Stream protocol II",
    "NETWORK_PROTOCOL_SVMTP": "Secure VMTP",
    "NETWORK_PROTOCOL_SWIPE": "IP with encryption",
    "NETWORK_PROTOCOL_TCF": "TCF",
    "NETWORK_PROTOCOL_TCP": "TCP",
    "NETWORK_PROTOCOL_TLSP": "Transport Layer Security",
    "NETWORK_PROTOCOL_TP": "tp-4 w/ class negotiation",
    "NETWORK_PROTOCOL_TPC": "Third Party Connect",
    "NETWORK_PROTOCOL_TPXX": "TP++ Transport",
    "NETWORK_PROTOCOL_TRUNK1": "Trunk-1",
    "NETWORK_PROTOCOL_TRUNK2": "Trunk-2",
    "NETWORK_PROTOCOL_TTP": "TTP",
    "NETWORK_PROTOCOL_UDP": "UDP - user datagram protocol",
    "NETWORK_PROTOCOL_VINES": "Banyon VINES",
    "NETWORK_PROTOCOL_VISA": "VISA Protocol",
    "NETWORK_PROTOCOL_VMTP": "VMTP",
    "NETWORK_PROTOCOL_WBEXPAK": "WIDEBAND EXPAK",
    "NETWORK_PROTOCOL_WBMON": "WIDEBAND Monitoring",
    "NETWORK_PROTOCOL_WSN": "Wang Span Network",
    "NETWORK_PROTOCOL_XNET": "Cross Net Debugger",
    "NETWORK_PROTOCOL_XTP": "XTP",
    "NETWORK_SERVICE_GROUP": "Network Service Group",
    "NETWORK_SERVICE_GROUPS": "Network Service Groups",
    "NETWORK_SERVICE": "Network Service",
    "NETWORK_SERVICES_GROUP": "Network Services Group",
    "NETWORK_SERVICES": "Network Services",
    "NETWORK_TRAFFIC": "Network Traffic",
    "NEW_API_KEY": "New API Key",
    "NEW_CALEDONIA_PACIFIC_NOUMEA": "Pacific/Noumea",
    "NEW_CALEDONIA": "New Caledonia",
    "NEW_PASSWORD_EQUALITY": "New password and current password should not be same",
    "NEW_PASSWORD_PLACEHOLDER": "Should be at least 8 characters long and contain 1 digit, 1 capital letter and 1 special character",
    "NEW_PASSWORD": "New Password",
    "NEW_ZEALAND_PACIFIC_AUCKLAND": "Pacific/Auckland",
    "NEW_ZEALAND_PACIFIC_CHATHAM": "Pacific/Chatham",
    "NEW_ZEALAND": "New Zealand",
    "NEW": "New",
    "NEWLY_REG_DOMAINS": "Newly Registered Domains",
    "NEWS_AND_MEDIA": "News and Media",
    "NEWZEALANDNORTH": "(Asia Pacific) Newzealand North",
    "NEXT_PERIODIC_UPDATE": "Next periodic update is as per schedule set for VMs in",
    "NEXT_UPDATE": "Next periodic software upgrade will be on ",
    "NEXT": "Next",
    "NFL_DESC": " This protocol plug-in classifies the http traffic to the host nfl.com",
    "NFL": "NFL",
    "NFLMOBILE": "NFL Mobile",
    "NFS_DESC": " The NFS protocol provides transparent remote access to shared file systems across networks as described in RFC 1813",
    "NFS": "NFS",
    "NGAP_SCTP_DESC": "The NG Application Protocol (NGAP) provides the control plane signaling between NG-RAN node and the Access and Mobility Management Function (AMF) and provides NAS signaling for User equipment (UE)and AMF",
    "NGAP_SCTP": "NGAP-SCTP",
    "NGAP_UDP_DESC": "The NG Application Protocol (NGAP) is encapsulated as UDP to support transport over networks which do not support SCTP",
    "NGAP_UDP": "NGAP-UDP",
    "NICARAGUA_AMERICA_MANAGUA": "America/Managua",
    "NICARAGUA": "Nicaragua",
    "NIGER_AFRICA_NIAMEY": "Africa/Niamey",
    "NIGER": "Niger",
    "NIGERIA_AFRICA_LAGOS": "Africa/Lagos",
    "NIGERIA": "Nigeria",
    "NIUE_PACIFIC_NIUE": "Pacific/Niue",
    "NIUE": "Niue",
    "NLOCKMGR_DESC": " The network lock manager is a facility that works in cooperation with the Network File System (NFS) to provide a System V style of advisory file and record locking over the network",
    "NLOCKMGR": "nlockmgr",
    "NLSP_DESC": " NetWare Link Services Protocol (NLSP) provides link state routing for Internetwork Packet Exchange networks",
    "NLSP": "NLSP",
    "NMAP_DESC": "Nmap (Network Mapper) is a security scanner used to discover hosts and services on a computer network, thus creating a 'map' of the network.",
    "NMAP": "Nmap",
    "NNTP_DESC": " The Network News Transport Protocol (NNTP) is used for the distribution, inquiry, retrieval and posting of net news articles using a reliable stream-based mechanism",
    "NNTP": "NNTP",
    "NNTPS_DESC": " Secure version of the NNTP protocol",
    "NNTPS": "SecureNNTP",
    "NO_ACTIVATION_PENDING": "No Activation Pending",
    "NO_BC_GROUPS_AVAILABLE_FOR_SELECTED_LOCATION": "Branch Connector Groups is not available for the selected location!",
    "NO_DATA_AVAILABLE_AWS_ACCOUNT_GROUP": "No Data Available\n\nTo create AWS account group\n\ngo to\n\nAdmin > Partner Integration\n\n\n",
    "NO_DATA": "No matching items found",
    "NO_DESCRIPTION": "No description",
    "NO_GROUPING": "Overall Traffic",
    "NO_ITEMS_AVAILABLE": "No Data Found",
    "NO_MATCHING_ITEMS_FOUND": "No matching items found",
    "NO_MORE_DHCP_OPTIONS_AVAILABLE": "No more DHCP options available",
    "NO_OF_CLOUD_CONNECTOR_GROUPS": "Number of Cloud Connector Groups",
    "NO_OF_CLOUD_CONNECTORS": "Number of Cloud Connectors",
    "NO_OF_DUPLICATES_IP": "No of Duplicates IP",
    "NO_OF_EDGE_CONNECTOR_GROUPS": "No. of Cloud Connector Groups",
    "NO_OF_PRIVATE_IP_ADDRESSES": "No of Private IP Addresses",
    "NO_PENDING_UPGRADES": "No Pending Upgrades",
    "NO_PRESIGNED_URL_WAS_GENERATED": "No pre-signed URL was generated.",
    "NO_REGION_WAS_PREVIOUS_SELECTED_TEXT": "No Region was selected on the Event Grid to be used on the Storage account.",
    "NO_REGION_WAS_PREVIOUS_SELECTED": "No Region was selected",
    "NO_STATIC_LEASE_CONFIGURED": "No Static Lease Configured",
    "NO_SUBSCRIPTION_AVAILABLE": "No subscription available.",
    "NO_VALUE_SELECTED": "No Value Selected",
    "NO_WIDGET_DATA": "No data for selected time range",
    "NO": "No",
    "NON_CATEGORIZABLE": "Non Categorizable",
    "NON_NUMERIC_VALUE": "This fiels should contain  only numbers.",
    "NONE": "None",
    "NORFOLK_ISLAND_PACIFIC_NORFOLK": "Pacific/Norfolk",
    "NORFOLK_ISLAND": "Norfolk Island",
    "NORTH_KOREA": "North Korea",
    "NORTH_MACEDONIA": "North Macedonia",
    "NORTHAMERICA_NORTHEAST1_A": "northamerica-northeast1a",
    "NORTHAMERICA_NORTHEAST1_B": "northamerica-northeast1b",
    "NORTHAMERICA_NORTHEAST1_C": "northamerica-northeast1c",
    "NORTHAMERICA_NORTHEAST1": "northamerica-northeast1",
    "NORTHAMERICA_NORTHEAST2_A": "northamerica-northeast2a",
    "NORTHAMERICA_NORTHEAST2_B": "northamerica-northeast2b",
    "NORTHAMERICA_NORTHEAST2_C": "northamerica-northeast2c",
    "NORTHAMERICA_NORTHEAST2": "northamerica-northeast2",
    "NORTHCENTRALUS": "(US) North Central US",
    "NORTHCENTRALUSSTAGE": "(US) North Central US (Stage)",
    "NORTHERN_EUROPE": "Northern Europe",
    "NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN": "Pacific/Saipan",
    "NORTHERN_MARIANA_ISLANDS": "Northern Mariana Islands",
    "NORTHEUROPE": "(Europe) North Europe",
    "NORWAY_EUROPE_OSLO": "Europe/Oslo",
    "NORWAY": "Norway",
    "NORWAYEAST": "(Europe) Norway East",
    "NORWAYWEST": "(Europe) Norway West",
    "NOT_AVAILABLE": "Not Available",
    "NOT_DEPLOYED": "Ready to Deploy",
    "NOT_NULL": "Not Null",
    "NOT_SPECIFIED": "Not Specified",
    "NSS_CLOUD_FEED_API_URL": "API URL",
    "NSS_CLOUD_FEED_AUTHENTICATION_URL": "Authorization URL",
    "NSS_CLOUD_FEED_AWS_ACCESS_ID": "AWS Access Id",
    "NSS_CLOUD_FEED_AWS_SECRET_KEY": "AWS Secret Key",
    "NSS_CLOUD_FEED_CLIENT_ID": "Client Id",
    "NSS_CLOUD_FEED_CLIENT_SECRET": "Client Secret",
    "NSS_CLOUD_FEED_GENERAL": "General",
    "NSS_CLOUD_FEED_GRANT_TYPE": "Grant Type",
    "NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "JSON Array Notation",
    "NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Max Batch Size",
    "NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "OAuth 2.0 Authentication",
    "NSS_CLOUD_FEED_SCOPE": "Scope",
    "NSS_CLOUD_FEED_SIEM_TYPE": "SIEM Type",
    "NSS_CLOUD_FEEDS_API_URL": "API URL",
    "NSS_CLOUD_FEEDS_FEED_NAME": "Feed Name",
    "NSS_CLOUD_FEEDS_FEED_OVERVIEW": "Feed Overview",
    "NSS_CLOUD_FEEDS_FEED_TYPE": "Feed Type",
    "NSS_CLOUD_FEEDS_LOG_TYPE": "Log Type",
    "NSS_CLOUD_FEEDS_S3_FOLDER_URL": "S3 Folder URL",
    "NSS_CLOUD_FEEDS_SIEM_TYPE": "SIEM Type",
    "NSS_CLOUD_FEEDS_STATUS": "Status",
    "NSS_CLOUD_FEEDS": "Cloud NSS Feeds",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connector Groups",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Client IP Addresses",
    "NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "DNS Request Types",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "DNS Response Codes",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "DNS Response Types",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "DNS Responses",
    "NSS_FEED_DNS_FILTERS_DOMAINS": "Domains",
    "NSS_FEED_DNS_FILTERS_DURATIONS": "Durations",
    "NSS_FEED_DNS_FILTERS_LOCATIONS": "Locations",
    "NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Policy Action",
    "NSS_FEED_DNS_FILTERS_RULE_NAME": "Rule Name",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_ADDRESS": "Server IP Addresses",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_PORTS": "Server Ports",
    "NSS_FEED_DUPLICATE_LOGS": "Duplicate Logs",
    "NSS_FEED_EC_METRICS_RECORD_TYPE": "Metrics Record Type",
    "NSS_FEED_ESCAPE_CHARACTER": "Feed Escape Character",
    "NSS_FEED_FILTERS": "Filters",
    "NSS_FEED_FORMATTING": "FORMATTING",
    "NSS_FEED_GENERAL": "General",
    "NSS_FEED_LOG_TYPE": "Log Type",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Cloud/Branch Connector VM",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Cloud/Branch Connector VM",
    "NSS_FEED_NAME": "Feed Name",
    "NSS_FEED_OUTPUT_FORMAT": "Feed Output Format",
    "NSS_FEED_OUTPUT_TYPE": "Feed Output Type",
    "NSS_FEED_SERVER": "NSS Server",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connector Groups",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Client IP Addresses",
    "NSS_FEED_SESSION_FILTERS_FIREWALL_LOG_TYPE": "Firewall Log Type",
    "NSS_FEED_SESSION_FILTERS_GATEWAY": "Gateway",
    "NSS_FEED_SESSION_FILTERS_LOCATIONS": "Locations",
    "NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Network Services",
    "NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Policy Action",
    "NSS_FEED_SESSION_FILTERS_RULE_NAME": "Rule Name",
    "NSS_FEED_SESSION_LOG_TYPE": "Session Log Type",
    "NSS_FEED_SIEM_CONNECTIVITY": "SIEM CONNECTIVITY",
    "NSS_FEED_SIEM_DESTINATION_TYPE": "SIEM Destination Type",
    "NSS_FEED_SIEM_FQDN": "SIEM FQDN",
    "NSS_FEED_SIEM_IP_ADDRESS": "SIEM IP Address",
    "NSS_FEED_SIEM_RATE_LIMIT": "SIEM Rate Limit (Events per Second)",
    "NSS_FEED_SIEM_RATE": "SIEM Rate",
    "NSS_FEED_SIEM_TCP_PORT": "SIEM TCP Port",
    "NSS_FEED_STATUS": "Status",
    "NSS_FEED_TIMEZONE": "Timezone",
    "NSS_FEED": "NSS FEED",
    "NSS_FEEDS_AGGREGATE_LOGS": "Aggregate Logs",
    "NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS": "Both Session and Aggregate Logs",
    "NSS_FEEDS_DUPLICATE_LOG": "Duplicate Logs",
    "NSS_FEEDS_FEED_ATTRIBUTES": "Feed Attributes",
    "NSS_FEEDS_FEED_NAME": "Feed Name",
    "NSS_FEEDS_FEED_OUTPUT_FORMAT": "Feed Output Format",
    "NSS_FEEDS_FEED_OVERVIEW": "Feed Overview",
    "NSS_FEEDS_FEED_TYPE": "Feed Type",
    "NSS_FEEDS_FULL_SESSION_LOGS": "Full Session Logs",
    "NSS_FEEDS_LOG_FILTER": "Log Filter",
    "NSS_FEEDS_LOG_TYPE": "Log Type",
    "NSS_FEEDS_NSS_SERVER_TEXT": "NSS Server",
    "NSS_FEEDS_OUTPUT_DESTINATION": "Output Destination",
    "NSS_FEEDS_SIEM_RATE": "SIEM Rate",
    "NSS_FEEDS_STATUS": "Status",
    "NSS_FEEDS_TIMEZONE": "Timezone",
    "NSS_FEEDS_USER_OBFUSCATION": "User Obfuscation",
    "NSS_FEEDS": "NSS Feeds",
    "NSS_FOR_FIREWALL_AND_EC": "NSS for Firewall, Cloud and Branch Connector",
    "NSS_FOR_FIREWALL": "NSS for Firewall",
    "NSS_GCP_DEPLOYMENT_GUIDE": "Google Cloud Platform Deployment Guide for NSS",
    "NSS_LOGGING": "NSS Logging",
    "NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Number of Users",
    "NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Peak DNS Requests Per Hour",
    "NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR": "Peak Sessions Per Hour",
    "NSS_SERVER_DEPLOYMENT_PLATFORM": "Platform",
    "NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE": "Download NSS Virtual Appliance",
    "NSS_SERVER": "NSS Server",
    "NSS_SERVERS": "NSS Servers",
    "NSS_TYPE": "NSS Type",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC": "NSS Virtual Appliance Deployment",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT": "NSS VIRTUAL APPLIANCE DEPLOYMENT",
    "NSS_VIRTUAL_MACHINE": "NSS Virtual Machine",
    "NTP_DESC": "Network Time Protocol is a networking protocol for clock synchronization between computer systems over packet-switched, variable-latency data networks",
    "NTP": "NTP",
    "NTV_DESC": " This protocol plug-in classifies the http traffic to the host ntv.co.jp",
    "NTV": "NTV",
    "NUDITY_DESC": " Sites that provide either artistic or non-artistic nude images in any medium (sculpture, photographs, paintings, etc.).",
    "NUDITY": "Nudity",
    "NUMBER_ABBR": "No.",
    "NUMBER_OF_ACCOUNTS": "Number of Accounts",
    "NUMBER_OF_CONNECTORS": "No. of Connectors",
    "NUMBER_OF_CORES": "Number of Cores",
    "NUMBER_OF_RECORDS_DISPLAYED": "Number of Records Displayed",
    "NUMBER_OF_RECORDS_FETCHED_SO_FAR": "Number of records fetched so far:",
    "NUMBER_OF_SELECTED_ITEM_PLURAL": "{{count}} items selected",
    "NUMBER_OF_SELECTED_ITEM": "{{count}} item selected",
    "NUMBER_OF_SELECTED_ITEMS": "No. of Selected Items",
    "OBFUSCATED": "Obfuscated",
    "OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA": "Asia/Gaza",
    "OCCUPIED_PALESTINIAN_TERRITORY": "Occupied Palestinian Territory",
    "OFF": "Off",
    "OKAY": "Okay",
    "OMAN_ASIA_MUSCAT": "Asia/Muscat",
    "OMAN": "Oman",
    "ON_PREMISE": "On Premise",
    "ON": "On",
    "ONE_OR_MORE_CC_FAILED": "One or more Cloud Connector Upgrades Failed",
    "ONLINE_AUCTIONS": "Online Auctions",
    "ONLINE_CHAT": "Online Chat",
    "ONLY_ONE_PHISICAL_INTERFACE_PER_PORT": "Only one tagged interface per port.",
    "OPEN_A_NEW_TAB": "Open in a New Tab",
    "OPENVPN_DESC": "OpenVPN is an open source software application that implements virtual private network (VPN) techniques for creating secure point-to-point or site-to-site connections in routed or bridged configurations and remote access facilities",
    "OPENVPN": "OpenVPN",
    "OPERATIONAL_STATUS": "Operational Status",
    "OPTION_NAME": "Option Name",
    "OPTIONAL_PARENTHESIS": "(optional)",
    "OPTIONAL": "Optional",
    "OPTIONS_COLON": "Options:",
    "OPTIONS": "Options",
    "ORACLE_LINUX": "ORACLE LINUX",
    "ORDER_DEFAULT": "Default",
    "ORG_ADMIN": "Organization Admin",
    "ORG_ID": "Org ID",
    "ORGANIZATION": "Organization",
    "OS_TYPE": "OS Type",
    "OS_VERSION": "OS Version",
    "OSS_UPDATES": "Operating System and Software Updates",
    "OTHER_ADULT_MATERIAL": "Other Adult Material",
    "OTHER_BUSINESS_AND_ECONOMY": "Other Business and Economy",
    "OTHER_CLOUDS": "Other Clouds",
    "OTHER_DRUGS": "Other Drugs",
    "OTHER_EDUCATION": "Other Education",
    "OTHER_ENTERTAINMENT_AND_RECREATION": "Other Entertainment/Recreation",
    "OTHER_GAMES": "Online and Other Games",
    "OTHER_GOVERNMENT_AND_POLITICS": "",
    "OTHER_ILLEGAL_OR_QUESTIONABLE": "Other Illegal or Questionable",
    "OTHER_INFORMATION_TECHNOLOGY": "",
    "OTHER_INTERNET_COMMUNICATION": "Other Internet Communication",
    "OTHER_MISCELLANEOUS": "Other Miscellaneous",
    "OTHER_OS": "Other OS",
    "OTHER_RELIGION": "Other Religion",
    "OTHER_SECURITY": "Other Security",
    "OTHER_SHOPPING_AND_AUCTIONS": "Other Shopping and Auctions",
    "OTHER_SOCIAL_AND_FAMILY_ISSUES": "Other Social and Family Issues",
    "OTHER_SOCIETY_AND_LIFESTYLE": "Other Society and Lifestyle",
    "OTHER_THREAT": "Other Threat",
    "OTHER": "Other",
    "out of": "out of",
    "OUT_OF": "out of",
    "OUTBOUND": "Outbound",
    "OUTBYTES": "Out Bytes",
    "OUTGOING_GATEWAY_IP_ADDRESS": "Outgoing Gateway IP Address",
    "OVER_ALL_TRAFFIC": "Overall Traffic",
    "OVERRIDE": "Override",
    "P2P_COMMUNICATION": "Peer-to-Peer Site",
    "P2P": "Peer-to-Peer",
    "PACKET_LOSS": "Packet Loss",
    "PAGE_OF": "Page {1} of {2}",
    "PAGE_RISK_INDEX": "Suspicious Content",
    "PAGE": "Page",
    "PAKISTAN_ASIA_KARACHI": "Asia/Karachi",
    "PAKISTAN": "Pakistan",
    "PALAU_PACIFIC_PALAU": "Pacific/Palau",
    "PALAU": "Palau",
    "PALESTINE": "Palestine",
    "PALESTINIAN_TERRITORY": "Palestinian Territory",
    "PANAMA_AMERICA_PANAMA": "America/Panama",
    "PANAMA": "Panama",
    "PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY": "Pacific/Port Moresby",
    "PAPUA_NEW_GUINEA": "Papua New Guinea",
    "PARAGUAY_AMERICA_ASUNCION": "America/Asuncion",
    "PARAGUAY": "Paraguay",
    "PARTNER_INTEGRATIONS": "Partner Integrations",
    "PASSPHRASE": "Passphrase",
    "PASSWORD_CHANGE_REMINDER_NEW_PASSWORD_INPUT_PLACEHOLDER": "Should be atleast 8 characters long and should contain 1 digit, 1 capital letter, and 1 special character...",
    "PASSWORD_DONT_MATCH": "Passwords do not match",
    "PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE": "This change will also apply to admins that exist in the Admin Portal for another service. Continue?",
    "PASSWORD_EXPIRATION_DAYS_RANGE": "Password Expiry date should be between 15 and 365 days",
    "PASSWORD_EXPIRATION": "Password Expiration",
    "PASSWORD_EXPIRED_ALREADY": "Your password has already expired",
    "PASSWORD_EXPIRED": "Password Expired",
    "PASSWORD_EXPIRES_AFTER": "Password Expires after",
    "PASSWORD_EXPIRY": "Password Expiry",
    "PASSWORD_MANAGEMENT": "PASSWORD MANAGEMENT",
    "PASSWORD_MESSAGE_WARNING_MESSAGE": "This change will also apply to admins that exist in the Admin Portal for another service. Continue?",
    "PASSWORD_STRENGTH_REQUIRED": "The password must contain at least eight characters long and at least one digit, one capital letter, and one special character.",
    "PASSWORD_STRENGTH": "Should be at least 8 characters long and contain 1 digit, 1 capital letter and 1 special character",
    "PASSWORD_UPDATE_SUCCESSFULLY": "Password Updated Successfully",
    "PASSWORD": "Password",
    "PATCH": "Patch",
    "PATENTS": "Patents",
    "PC_ANYWHERE_DESC": "PC-Anywhere is a remote control and file transfer software",
    "PC_ANYWHERE": "pcAnywhere",
    "PCANYWHERE_DESC": " PCAnywhere is a remote control solution. It can manage both Windows and Linux systems. Enhanced video performance and built-in AES 256-bit encryption help make communications fast and secure. PCAnywhere also features powerful file-transfer capabilities",
    "PCANYWHERE": "pcanywhere",
    "PEER_DHCP": "Peer DHCP (Optional)",
    "PENDING": "Pending",
    "PERMISSION_REQUIRED_MESSAGE": "This feature requires a permission that you do not currently have.",
    "PERMISSION_REQUIRED": "Permission Required",
    "PERMISSION": "Permission",
    "PERMISSIONS": "Permissions",
    "PERSIST_LOCAL_VERSION_PROFILE": "Persist Local Version Profile",
    "PERU_AMERICA_LIMA": "America/Lima",
    "PERU": "Peru",
    "PFCP_DESC": "A 3GPP protocol used on the N4 interface between the control plane and the user plane function(UPF)",
    "PFCP_PORT": "PFCP Port",
    "PHILIPPINES_ASIA_MANILA": "Asia/Manila",
    "PHILIPPINES": "Philippines",
    "PHISHING": "Phishing",
    "PHYSICAL": "Physical",
    "PITCAIRN_ISLANDS": "Pitcairn Islands",
    "PITCAIRN_PACIFIC_PITCAIRN": "Pacific/Pitcairn",
    "PITCAIRN": "Pitcairn",
    "PLACEHOLDER_NETWORK_SERVICE_GROUP_NAME": "Type Network Service Group Name here",
    "PLACEHOLDER_NETWORK_SERVICE_NAME": "Type Network Service Name here",
    "PLAIN_UDP": "Unencrypted UDP",
    "PLATFORM": "Platform",
    "PLEASE_ADD_BC_GROUP_INFO": "Fill out the information below for your Branch Connector Group.",
    "PLEASE_ADD_CLOUD_CONNECTOR_NAME": "Please add the Cloud Connector filter.",
    "PLEASE_ADD_DATACENTER_FILTER": "Please add the datacenter filter.",
    "PLEASE_ADD_EC_DEVICE_APP_VERSION": "Please add the device app version filter.",
    "PLEASE_ADD_EC_DEVICE_HOSTNAME": "Please add the device hostname filter.",
    "PLEASE_ADD_EC_DEVICE_ID": "Please add the device name filter.",
    "PLEASE_CONFIGURE_THE_IP_ADDRESS": "Please configure the IP address",
    "PLEASE_CONFIGURE_THE_MAC_ADDRESS": "Please configure the MAC address",
    "PLEASE_CONFIGURE_THE_ROUTE_ADDRESS": "Please configure the route address.",
    "PLEASE_ENTER_BELOW_VALUES": "Please enter the below values",
    "PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW": "Enter the following information below.",
    "PLEASE_ENTER_VALID_EMAIL_ADDRESS": "Enter a valid email address.",
    "PLEASE_FILL_BRANCH_INFO": "Fill out the information below for your individual Branch Connector.",
    "PLEASE_FILL_DEVICE_INFO": "Select a device for your branch connector configuration provisioning.",
    "PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM": "Please reach out to your account team to enable tunnel encryption",
    "PLEASE_REMOVE_DELETED_LOCATION": "Please remove the deleted Location(s).",
    "PLEASE_SELECT_A_STORAGE_ACCOUNT": "Please select a storage account.",
    "PLEASE_VERIFY_THE_DHCP_CUSTOM_CONFIGURTION_FOR_DUPLICATES": "Please verify the DHCP Custom Options configurtion for duplicates.",
    "POLAND_EUROPE_WARSAW": "Europe/Warsaw",
    "POLAND": "Poland",
    "POLANDCENTRAL": "(Europe) Poland Central",
    "POLICY_CONFIGURATION": "Policy Configuration",
    "POLICY_INFORMATION": "Forwarding Information",
    "POLICY_MANAGEMENT": "Policy Management",
    "POLICY_SYNC": "Policy Sync",
    "Policy": "Policy",
    "POLICY": "Policy",
    "POLITICS": "Politics",
    "POOR": "Poor",
    "POP3_DESC": "Post Office Protocol is a protocol used for retrieving email",
    "POP3": "POP3",
    "PORNOGRAPHY": "Pornography",
    "PORT_DETAILS": "Port Details",
    "PORT_NAME": "[Port Name]",
    "PORT_NO": "Port No.  ",
    "PORT_STATUS": "Port Status",
    "PORT": "Port",
    "PORTALS": "Portals",
    "PORTS": "Proxy Ports",
    "PORTUGAL_ATLANTIC_AZORES": "Atlantic/Azores",
    "PORTUGAL_ATLANTIC_MADEIRA": "Atlantic/Madeira",
    "PORTUGAL_EUROPE_LISBON": "Europe/Lisbon",
    "PORTUGAL": "Portugal",
    "PPP_DESC": " PPP (Point-to-Point Protocol) is a link layer protocol used for transferring data along a Point to Point link. It provides dynamic IP addressing, password support, error checking, and multiple protocols transmission on the same link",
    "PPP": "PPP",
    "PPPOE_DESC": " PPP over Ethernet (PPPoE) provides the ability to connect a network of hosts over a simple bridging access device to a remote Access Concentrator",
    "PPPOE": "PPPoE",
    "PPS_DESC": " This protocol plug-in classifies the http traffic to the host pps.tv",
    "PPS": "PPS (pps.tv)",
    "PPSTREAM_DESC": " The PPStream protocol provides audio and video streaming. It is based on bittorent (peer-to-peer) technology. It is mainly used in China",
    "PPSTREAM": "PPStream",
    "PPTP_DATA_DESC": " Point-to-Point Tunneling Protocol allows the Point to Point Protocol (PPP) to be tunnelled through an IP network",
    "PPTP_DATA": "PPTP data channel. PPTP",
    "PPTP_DESC": " Point-to-Point Tunneling Protocol allows the Point to Point Protocol (PPP) to be tunnelled through an IP network",
    "PPTP_SERVICES": "PPTP Services",
    "PPTP": "PPTP",
    "PPTV_DESC": " This protocol plug-in classifies the http traffic to the host pptv.com",
    "PPTV": "pptv",
    "PRE_SIGNED_URL": "Pre Signed URL",
    "PREDEFINED_RULE_CONFIRMATION": "Pre-Defined Rule Confirmation",
    "PREDEFINED": "Predefined",
    "PREFERRED_COLLON": "Preferred:",
    "PREFERRED_TEXT": "Select Yes to allow the preferred device to preempt and take over when active.",
    "PREFERRED": "Preferred",
    "PREFIX": "Prefix",
    "PREV_DAY": "Previous Day",
    "PREV_MONTH": "Previous Month",
    "PREV_WEEK": "Previous Week",
    "PREVIOUS": "Previous",
    "PRIMARY_DNS_IS_MADATORY_BEFORE_SECONDARY_DNS": "Primary DNS is mandatory before a secondary DNS.",
    "PRIMARY_DNS_SERVER_IP_ADDRESS": "Primary DNS Server IP Address",
    "PRIMARY_DNS_SERVER": "Primary DNS Server",
    "PRIMARY_DNS": "Primary DNS",
    "PRIMARY_PROXY": "Primary Proxy",
    "PRIMARY_SERVER_RESPONSE_PASS": "Primary Server Attempted",
    "PRIMARY": "Primary",
    "PRINT_VIEW": "Print View",
    "PRINT": "Print",
    "PRIVATE_APLICATIONS": "Private Applications",
    "PRIVATE_IP_ADDRESS": "Private IP Address",
    "PRIVATE_IP_ADDRESSES": "Private IP Addresses",
    "PROCEED": "Proceed",
    "PROCESSED_BYTES": "Processed Bytes",
    "PROFANITY": "Profanity",
    "PROFESSIONAL_SERVICES": "Professional Services",
    "PROJECT_LIST": "Project List",
    "PROJECTS": "Projects",
    "PROJETCS_AND_REGIONS": "Projects And Regions",
    "PROTOCOL_TYPE": "Protocol Type",
    "PROTOCOL": "Protocol",
    "PROVISION_KEY_NAME": "Provision Key Name",
    "PROVISION_KEY": "Provision Key",
    "PROVISIONED": "Provisioned",
    "Provisioning URL": "Provisioning URL",
    "PROVISIONING_AND_CONFIGUATION": "Provisioning & Configuration",
    "PROVISIONING_CONTROL": "Provisioning Control",
    "PROVISIONING_KEY": "Provisioning Key",
    "PROVISIONING_MANAGEMENT": "Provisioning Management",
    "PROVISIONING_TEMPLATE_IS_BROKEN": "This provisioning template is invalid. Please delete it and create a new one.",
    "PROVISIONING_TEMPLATE": "Provisioning Template",
    "PROVISIONING_TEMPLATES": "Provisioning Templates",
    "PROVISIONING_URL": "Provisioning URL",
    "PROXY_TEST": "Proxy Test",
    "PUB_SUB": "Pub/Sub",
    "PUBLIC_CLOUD_COFIGURATION": "Public Cloud Configuration",
    "PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Public Cloud Config Management",
    "PUBLIC_IP_FOR_DIRECT_FORWARDING": "Public IP for Direct Forwarding",
    "PUBLIC_IP": "Public IP",
    "PUBLIC_IPS": "Public IPs",
    "PUERTO_RICO_AMERICA_PUERTO_RICO": "America/Puerto Rico",
    "PUERTO_RICO": "Puerto Rico",
    "PZEN": "Private Service Edge",
    "QATAR_ASIA_QATAR": "Asia/Qatar",
    "QATAR": "Qatar",
    "QATARCENTRAL": "(Middle East) Qatar Central",
    "QUESTIONABLE": "Questionable",
    "QUEUED_ACTIVATIONS": "QUEUED ACTIVATIONS",
    "QUIC_DESC": "QUIC (Quick UDP Internet Connections) is a new transport protocol for the internet, developed by Google",
    "QUIC": "QUIC",
    "QUICK_LINKS": "Quick Links",
    "QUICKOFFICE": "Quickoffice",
    "QUICKSEC": "QUICKSEC",
    "QUICKTIME_VIDEO": "QuickTime Video (mov, qt)",
    "QUICKTIME": "QuickTime",
    "RADIO_STATIONS": "Radio",
    "RADIUS_DESC": " RADIUS (Remote Authentication Dial-In User Service) is a client/server protocol that enables remote access servers to communicate with a central server to authenticate dial-in users and authorize their access to the requested system or service",
    "RADIUS": "RADIUS",
    "RADIUSIM_DESC": " This protocol plug-in classifies the http traffic to the host radiusim.com",
    "RADIUSIM": "RadiusIM",
    "RANGE_ERROR": "Invalid Range. The number should be between {{min}} and {{max}}",
    "RANGE_FROM_BIGGER_THAN_TO": "The start IP of the range should be equal or smaller than the end IP.",
    "RBA_LIMITED": "Access is restricted",
    "REAL_ESTATE": "Construction & Real Estate",
    "REAL_MEDIA_DESC": "Real Media is a streaming video and audio technology",
    "REAL_MEDIA": "RealMedia",
    "REASON": "Reason",
    "RECEIVE_COUNT": "Receive Count",
    "RECEIVED_BYTES": "Bytes Received",
    "RECEIVED_MESSAGES": "Received Messages",
    "RECOMMENDED_HYPERVISOR_SPECS": "RECOMMENDED HYPERVISOR SPECS",
    "RECOMMENDED_VM_SPECS": "RECOMMENDED VM SPECS",
    "REDHAT_LINUX": "RedHat Linux",
    "REDIR_ZPA": "Redirect to ZPA",
    "REDIRECT_REQUEST": "Redirect Request",
    "REFERENCE_SITES": "Reference Sites",
    "REFRESH": "Refresh",
    "REGENARATE": "Regenerate",
    "REGENERATE_API_KEY_CONFIRMATION_MESSAGE": "Regenerating the API key immediately invalidates the existing key. The new key retains the scope, permissions, and name of the existing key. This cannot be undone.",
    "REGENERATE_API_KEY_CONFIRMATION_TITLE": "Regenerate API Key",
    "REGENERATE_API_KEY_TOOLTIP": "Regenerate the API key",
    "REGENERATE_API_KEY": "Regenerate API Key",
    "REGION_TEXT": "Please select the regions where you want Zscaler to discover tags in your AWS account. The drop-down menu shows the list of regions supported by Zscaler's tag discovery service. More information about supported regions can be found {1}here{2}.",
    "REGION": "Region",
    "REGIONS_AND_PROJECTS": "Regions and Projects ",
    "REGIONS_AND_SUBSCRIPTIONS": "Regions & Subscriptions",
    "REGIONS_SUBSCRIPTION_TEXT": "Select the regions and subscriptions. Zscaler will read the user defined tags for workloads located in these regions and subscriptions.",
    "REGIONS_SUBSCRIPTION": "Regions & Subscriptions",
    "REGIONS_WORKLOAD_INVENTORY": "Regions Workload Inventory",
    "REGIONS": "Regions",
    "REGISTERED": "Registered",
    "RELEASES_NOTES": "Release Notes",
    "REMOTE_ACCESS": "Remote Access Tools",
    "REMOTE_ASSISTANCE_MANAGEMENT": "Remote Assistance Management",
    "REMOTE_ASSISTANCE": "Remote Assistance",
    "REMOVE_ALL": "Remove All",
    "RENEW": "Renew",
    "REPORT": "Report",
    "REQ_ACTION": "Request Action",
    "REQ_DURATION": "Request Duration",
    "REQ_RULE_NAME": "Request Rule Name",
    "REQUESTED_DOMAIN": "Requested Domain",
    "REQUIRED": "This field cannot be empty.",
    "RES_ACTION": "Response Action",
    "RES_RULE_NAME": "Response Rule Name",
    "RESEARCH_BLOG": "ThreatLabz | Security Research",
    "RESET_COUNT": "Reset Count",
    "RESET": "Reset",
    "RESOLVE_BY_ZPA": "Resolve By ZPA",
    "RESOLVED_BY_ZPA": "Resolved",
    "RESOLVED_IP_OR_NAME": "Resolved IP Or Name",
    "RESOLVED_IP": "Resolved IP",
    "RESOLVER_IP_OR_NAME": "Resolver IP Or Name",
    "RESOLVER": "Resolver",
    "RESOURCE_GROUP": "Resource Group",
    "RESOURCE_GROUPS": "Resource Groups",
    "RESOURCE_NAME": "Resource",
    "RESOURCE_NOT_FOUND": "Resource not found",
    "RESOURCE_TYPE": "Resource Type",
    "RESOURCE": "Resource",
    "RESPONSE_ACTION": "Response Action",
    "REST": "Rest",
    "RETRIEVING_FOR": "Retrieving for",
    "RETURN_ERROR": "Return Error",
    "REUNION_INDIAN_REUNION": "Indian/Reunion",
    "REUNION": "Reunion",
    "REVIEW_ENSURE_INFORMATION": "Ensure all the information below is correct before creating this Branch Connector Provisioning Template.",
    "REVIEW_TENANT": "Ensure all the information below is correct before adding this account.",
    "REVIEW_TEXT": "Ensure all the information below is correct before adding this account.",
    "REVIEW_YOUR_CHANGES": "Review your changes",
    "REVIEW": "Review",
    "RMA": "RMA Requested",
    "ROLE_MANAGEMENT": "Role Management",
    "ROLE_NAME": "Role Name",
    "ROLE": "Role",
    "ROMANIA_EUROPE_BUCHAREST": "Europe/Bucharest",
    "ROMANIA": "Romania",
    "ROUTE_HAS_DUPLICATE_VALUES": "Route has duplicate values.",
    "ROUTE": "Route",
    "ROUTING": "Routing",
    "ROWS_PER_PAGE": "Rows per page",
    "RSH_DESC": " The RSH protocol allows a user to establish a secure connection to a remote host and to obtain a shell allowing commands to be sent to the remote machine to be executed",
    "RSH": "rsh",
    "RSLTS_READFAILED": "Results volume read failed.",
    "RSS_DESC": " RSS is a family of web feed formats used to publish frequently updated works in a standardized format",
    "RSS": "rss",
    "RSTAT_DESC": " The RStat protocol is used in the Sun NFS family to exchange statistics on network activity",
    "RSTAT": "RStat",
    "RSVP_DESC": " RSVP is a Resource reSerVation setup Protocol designed for an integrated services Internet. RSVP provides receiver-initiated setup of resource reservations for multicast or unicast data flows, with good scaling and robustness properties. The RSVP protocol is used by a host to request specific qualities of service from the network for particular application data streams or flows. RSVP is also used by routers to deliver quality-of-service (QoS) requests to all nodes along the path(s) of the flows and to establish and maintain state to provide the requested service",
    "RSVP": "RSVP",
    "RSYNC_DESC": "rsync is a file synchronization and file transfer program for Unix-like systems that minimizes network data transfer by using a form of delta encoding called the rsync algorithm",
    "RSYNC": "Rsync",
    "RTCP_DESC": " The real-time transport Control protocol RTP allows monitoring of the data delivery in a manner scalable to large multicast networks, and to provide minimal control and identification functionality",
    "RTCP": "RTCP",
    "RTL_DESC": " This protocol plug-in classifies the http traffic to the host rtl.de",
    "RTL": "RTL",
    "RTMP_DESC": "Real Time Messaging Protocol (RTMP) is a proprietary protocol developed by Adobe Systems for streaming audio, video and data over the Internet, between a Flash player and a server",
    "RTMP": "RTMP",
    "RTP_DESC": " RTP is the real-time transport protocol used to transmit real-time data, such as audio, video or simulation data, over multicast or unicast network services",
    "RTP": "RTP",
    "RTSP_DESC": " The Real Time Streaming Protocol (RTSP) is an application-level protocol for control over the delivery of data with real-time properties. RTSP provides an extensible framework to enable controlled, on-demand delivery of real-time data, such as audio and video",
    "RTSP_SERVICES": "RTSP Services",
    "RTSP": "RTSP",
    "RULE_CRITERIA": "Criteria",
    "RULE_NAME": "Rule Name",
    "RULE_ORDER": "Rule Order",
    "RULE_STATUS": "Rule Status",
    "RULES": "Rules",
    "RUN_TEST": "Run Test",
    "RUSSIA": "Russia",
    "RUSSIAN_FEDERATION_ASIA_ANADYR": "Asia/Anadyr",
    "RUSSIAN_FEDERATION_ASIA_IRKUTSK": "Asia/Irkutsk",
    "RUSSIAN_FEDERATION_ASIA_KAMCHATKA": "Asia/Kamchatka",
    "RUSSIAN_FEDERATION_ASIA_KRASNOYARSK": "Asia/Krasnoyarsk",
    "RUSSIAN_FEDERATION_ASIA_MAGADAN": "Asia/Magadan",
    "RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK": "Asia/Novosibirsk",
    "RUSSIAN_FEDERATION_ASIA_OMSK": "Asia/Omsk",
    "RUSSIAN_FEDERATION_ASIA_SAKHALIN": "Asia/Sakhalin",
    "RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK": "Asia/Vladivostok",
    "RUSSIAN_FEDERATION_ASIA_YAKUTSK": "Asia/Yakutsk",
    "RUSSIAN_FEDERATION_ASIA_YEKATERINBURG": "Asia/Yekaterinburg",
    "RUSSIAN_FEDERATION_EUROPE_KALININGRAD": "Europe/Kaliningrad",
    "RUSSIAN_FEDERATION_EUROPE_MOSCOW": "Europe/Moscow",
    "RUSSIAN_FEDERATION_EUROPE_SAMARA": "Europe/Samara",
    "RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD": "Europe/Volgograd",
    "RUSSIAN_FEDERATION": "Russian Federation",
    "RWANDA_AFRICA_KIGALI": "Africa/Kigali",
    "RWANDA": "Rwanda",
    "RX_BYTES": "Received Bytes",
    "RX_PACKETS": "Received Packets",
    "SA_EAST_1": "sa-east-1 (Sao Paulo)",
    "SA_EAST_1A": "sa-east-1a",
    "SA_EAST_1B": "sa-east-1b",
    "SA_EAST_1C": "sa-east-1c",
    "SAFE_SEARCH_ENGINE": "Safe Search Engine",
    "SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY": "America/St. Barthelemy",
    "SAINT_BARTHELEMY": "Saint Barthelemy",
    "SAINT_HELENA": "Saint Helena",
    "SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS": "America/St. Kitts",
    "SAINT_KITTS_AND_NEVIS": "Saint Kitts and Nevis",
    "SAINT_LUCIA_AMERICA_ST_LUCIA": "America/St. Lucia",
    "SAINT_LUCIA": "Saint Lucia",
    "SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT": "America/Marigot",
    "SAINT_MARTIN_FRENCH_PART": "Saint Martin (French part)",
    "SAINT_MARTIN": "Saint Martin",
    "SAINT_PIERRE_AND_MIQUELON": "Saint Pierre and Miquelon",
    "SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT": "America/St. Vincent",
    "SAINT_VINCENT_AND_THE_GRENADINES": "Saint Vincent and the Grenadines",
    "SAML_CERTIFICATE_FILENAME": "IdP SAML Certificate",
    "SAMOA_PACIFIC_APIA": "Pacific/Apia",
    "SAMOA": "Samoa",
    "SAN_MARINO_EUROPE_SAN_MARINO": "Europe/San Marino",
    "SAN_MARINO": "San Marino",
    "SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME": "Africa/Sao Tome",
    "SAO_TOME_AND_PRINCIPE": "Sao Tome and Principe",
    "SATURDAY": "Saturday",
    "SAUDI_ARABIA_ASIA_RIYADH": "Asia/Riyadh",
    "SAUDI_ARABIA": "Saudi Arabia",
    "SAVE_AND_NEXT": "Save and Next",
    "SAVE_ERROR_MESSAGE": "There is a validation error. Please double check all fields before saving.",
    "SAVE_SUCCESS_MESSAGE": "All changes have been saved",
    "SAVE": "Save",
    "SCCP_DESC": " Skinny Client Control Protocol (SCCP) is a Cisco proprietary protocol used between Cisco Call Manager and Cisco VOIP phones. It is also supported by some other vendors",
    "SCCP": "SCCP",
    "SCHEDULE_UPGRADE": "Schedule Upgrade",
    "SCHEDULED_VERSION": "Scheduled Version",
    "SCHEDULED": "Scheduled",
    "SCIENCE_AND_TECHNOLOGY": "Science/Tech",
    "SCOPE": "Scope",
    "SCTP_ANY_DESC": "The Stream Control Transmission Protocol (SCTP) is a protocol in the transport layer of the Internet protocol suite(IP). Originally intended for Signaling System 7 (SS7) message transport in telecommunication, the protocol provides the message-oriented feature of the User Datagram Protocol (UDP), while ensuring reliable, in-sequence transport of messages with congestion control like the Transmission Control Protocol (TCP).",
    "SCTP_ANY": "SCTP",
    "SCTP_DEST_PORTS": "SCTP Destination Ports",
    "SCTP_PORT": "SCTP Port",
    "SCTP_PORTS": "SCTP Ports",
    "SCTP_SRC_PORTS": "SCTP Source Ports",
    "SCTP_UDP_Translation": "SCTP/UDP Translation",
    "SEARCH_BY": "Search by:",
    "SEARCH_ELLIPSIS": "Search...",
    "SEARCH_LOCATION": "Search Location",
    "SEARCH_RESULT": "Search Result",
    "SEARCH_TO_SEE_MORE": "Search to see more items",
    "SEARCH": "Search",
    "SECONDARY_DNS_OPTIONAL": "Secondary DNS (optional)",
    "SECONDARY_DNS_SERVER_IP_ADDRESS": "Secondary DNS Server IP Address",
    "SECONDARY_DNS_SERVER": "Secondary DNS Server",
    "SECONDARY_DNS": "Secondary DNS",
    "SECONDARY_PROXY": "Secondary Proxy",
    "SECONDARY_SERVER_RESPONSE_PASS": "Secondary Server Attempted",
    "SECONDARY": "Secondary",
    "SECURITY_GROUP_ID": "Security Group ID",
    "SECURITY_GROUP_NAME": "Security Group Name",
    "SEGMENT_GROUPS": "Segment Groups",
    "SELECT_A_LOCATION": "Select a Location",
    "SELECT_A_TEST": "Select a Test",
    "SELECT_ALL": "Select All",
    "SELECT_AN_EXISTING_LOCATION": "Select an existing location",
    "SELECT_BRANCH_PROVISIONING_LOCATION": "Select a location for your Branch Connector Provisioning Template.",
    "SELECT_CC_GROUP": "Select Cloud Connector Group",
    "SELECT_CC_LOCATION": "Select Cloud Connector Location",
    "SELECT_CC_VERSION": "Select Cloud Connector Version",
    "SELECT_CHART_TYPE": "Select Chart Type",
    "SELECT_DESTINATION_IP": "Select Destination IP",
    "SELECT_EVENT_TIME": "Select Event Time",
    "SELECT_FILTERS": "Select Filters",
    "SELECT_HYPERVISOR_VERSION": "Select Hypervisor Version",
    "SELECT_RESOURCE_GROUP_NAME": "Select Resource Group",
    "SELECT_STORAGE_ACCOUNT": "Select Storage Account",
    "SELECT_SUBSCRIPTION_GROUP_NAME": "Select Subscription Group Name",
    "SELECT_SUBSCRIPTION": "Select Subscription",
    "SELECT_TWO_VERSIONS_TO_COMPARE": "Please select two versions to compare.",
    "SELECT_UPGRADE_WINDOW": "Select Upgrade Window",
    "SELECT_ZSCALER_IP": "Select Zscaler IP",
    "SELECT": "Select",
    "SELECTED_ITEMS": "Selected Items ({{count}})",
    "SELECTED": "Selected",
    "SENEGAL_AFRICA_DAKAR": "Africa/Dakar",
    "SENEGAL": "Senegal",
    "SENT_BYTES": "Bytes Sent",
    "SENT_COUNT": "Sent Count",
    "SENT_MESSAGES": "Sent Messages",
    "SERBIA_EUROPE_BELGRADE": "Europe/Belgrade",
    "SERBIA": "Serbia",
    "SERIAL_NUMBER": "Serial Number",
    "SERVER_DESTINATION_IP": "Server Destination IP",
    "SERVER_DESTINATION_PORT": "Server Destination Port",
    "SERVER_IP_CATEGORY": "Server IP Category",
    "SERVER_IP": "Server IP",
    "SERVER_NAME": "Server Name",
    "SERVER_NETWORK_PROTOCOL": "Server NW Protocol",
    "SERVER_PORT": "Server Port",
    "SERVER_SOURCE_IP": "Server Source IP",
    "SERVER_SOURCE_PORT": "Server Source Port",
    "SERVER": "Server Traffic",
    "SERVERS": "Servers",
    "SERVFAIL": "Server Failure",
    "Service IP": "Service IP",
    "SERVICE_GATEWAY_IP_ADDRESS": "Service Gateway IP Address",
    "SERVICE_GROUPS": "Service Groups",
    "SERVICE_INFORMATION": "Service Information",
    "SERVICE_INTERFACE": "Service Interface",
    "SERVICE_IP_ADDRESS_ONE": "Service IP Address 1",
    "SERVICE_IP_ADDRESS_POOL": "Server IP Address Pool",
    "SERVICE_IP_ADDRESS_THREE": "Service IP Address 3",
    "SERVICE_IP_ADDRESS_TWO": "Service IP Address 2",
    "SERVICE_IP_ADDRESS": "Service IP Address",
    "SERVICE_IP": "Service IP",
    "SERVICE_STATUS": "Service Status",
    "SERVICE_VIRTUAL_IP_ADDRESS": "Service Virtual IP Address",
    "SERVICE": "Service",
    "SERVICES": "Services",
    "SESSION_COUNT_TREND": "Session Count Trend",
    "SESSION_COUNT": "Session Count",
    "SESSION_DURATION": "Session Duration",
    "SESSION_ID": "Session ID",
    "SESSION_INSIGHTS": "Session Insights",
    "SESSION_LOGS": "Session Logs",
    "SESSION_TIMED_OUT": "Your session has expired. Log in again to continue.",
    "SESSION": "Session",
    "SESSIONS_ACROSS_SERVICES": "Sessions Across Services",
    "SESSIONS": "Sessions",
    "SET_PASSWORD": "SET PASSWORD",
    "SEXUALITY": "Sexuality",
    "SEYCHELLES_INDIAN_MAHE": "Indian/Mahe",
    "SEYCHELLES": "Seychelles",
    "SHAREWARE_DOWNLOAD": "Shareware Download",
    "SHOW_DETAILS": "Show Details",
    "SHOW_LESS_ELLIPSIS": "Show Less ...",
    "SHOW_MORE_ELLIPSIS": "Show More ...",
    "SHOW": "Show",
    "SHUTDOWN": "Shutdown",
    "SIERRA_LEONE_AFRICA_FREETOWN": "Africa/Freetown",
    "SIERRA_LEONE": "Sierra Leone",
    "SIGN_IN": "Sign In",
    "SIGN_OUT": "Sign Out",
    "SIGNING_CERTIFICATE": "Signing Certificate",
    "SINGAPORE": "Singapore",
    "SINGLE_APPLIANCE_ADDED_INFO": "You can see the new appliance in the Appliances page. For more information view Connectors {1}Help Portal.{2}",
    "SINGLE_APPLIANCE_ADDED": "1 new appliance have been added to your tenant.",
    "SIP_DESC": " Session Initiation Protocol (SIP) is the Internet Engineering Task Force's (IETF's) standard for multimedia conferencing over IP. Like other VoIP protocols, SIP is designed to address the functions of signaling and session management within a packet telephony network",
    "SIP": "SIP",
    "SIZE_MUST_BE_EXACT_LENGTH": "This field should have a size of ",
    "SLOVAKIA_EUROPE_BRATISLAVA": "Europe/Bratislava",
    "SLOVAKIA": "Slovakia",
    "SLOVENIA_EUROPE_LJUBLJANA": "Europe/Ljubljana",
    "SLOVENIA": "Slovenia",
    "SMALL": "Small",
    "SMB_DESC": " The Server Message Block Protocol (SMB/SMB2) provides a method for client applications to read and write to files and to request services from server programs in a computers network",
    "SMB": "SMB",
    "SMBA": "SMBA",
    "SMBAC": "Sandbox Controller",
    "SMBAUI": "Sandbox UI",
    "SMEDGE_BOOTING": "SMEDGE: Booting.",
    "SMEDGE_END": "End of smedge error codes. Don't use.",
    "SMEDGE_INIT": "SMEDGE: Initializing.",
    "SMEDGE_NOT_RUNNING": "SMEDGE: Process is not running.",
    "SMEDGE_PKG_DOWNLOAD": "SMEDGE: Package download in progress.",
    "SMEDGE_PKG_INSTALL": "SMEDGE: Package installation in progress.",
    "SMEDGE_START": "Start of smedge error codes. Don't use.",
    "SMEDGE_UNKNOWN_ERROR": "SMEDGE: Unknown Gateway Error Code.",
    "SMEDGE_UPDATING": "SMEDGE: Updating.",
    "SMEDGE_UPGRADING": "SMEDGE: Upgrading.",
    "SMEDGE_ZIA_BRINGUP": "SMEDGE: Bringing up ZIA Tunnels.",
    "SMEDGE_ZIA_ZPA_BRINGUP": "SMEDGE: Bringing up ZIA Tunnels and Establishing ZPA connection.",
    "SMRES_ERROR": "SMRES server app returned HTTP error response.",
    "SMTP_AV_ENCRYPTED_ALLOW": "Encrypted attachment allowed",
    "SMTP_AV_ENCRYPTED_ATTACH_DROP": "Password Encrypted attachment, attachment dropped",
    "SMTP_AV_ENCRYPTED_DROP": "Password Encrypted attachment, message rejected",
    "SMTP_AV_UNSCANNABLE_ALLOW": "Unscannable attachment allowed",
    "SMTP_AV_UNZIPPABLE_ATTACH_DROP": "Unscannable attachment, attachment dropped",
    "SMTP_AV_UNZIPPABLE_DROP": "Unscannable attachment, message rejected",
    "SMTP_AV_UWL": "Virus UWL,",
    "SMTP_AV_VIRUS_ATTACH_DROP": "Virus found, attachment dropped",
    "SMTP_AV_VIRUS_DROP": "Virus found, message rejected",
    "SMTP_CMD_TIMEOUT_LIMIT": "SMTP/SMTQTN: command timeout (in secs)",
    "SMTP_CONCURRENT_CLIENT_CONN_LIMIT": "SMTP/SMQTN: concurrent client connections limit",
    "SMTP_CONCURRENT_SERVER_CONN_LIMIT": "SMTP/SMQTN: concurrent server connections limit",
    "SMTP_DATA_TIMEOUT_LIMIT": "SMTP/SMQTN: timeout in DATA command (in secs)",
    "SMTP_DESC": "Simple Mail Transfer Protocol is a protocol for sending email messages between servers",
    "SMTP_DLP_ALLOW": "DLP hit, message allowed",
    "SMTP_DLP_DROP": "DLP hit, message rejected",
    "SMTP_DLP_QTN": "DLP hit, message quarantined",
    "SMTP_DLP_SIGN_REQUIRED": "DLP hit, message is not signed, message rejected",
    "SMTP_DLP_TLS_REQUIRED": "DLP hit, connection is not TLS, message rejected",
    "SMTP_DLP": "SMTP - Comply",
    "SMTP_EODT_TIMEOUT_LIMIT": "SMTP/SMQTN: timeout in EODT command (in secs)",
    "SMTP_ERRINJECTION_DELAY_LIMIT": "GULPER: error injection delay (in secs)",
    "SMTP_FLOWCONTROL": "Flow control on message delivery - applicable on SMQTN only",
    "SMTP_INCOMPL_TRANS": "SMTP transaction terminated by peer",
    "SMTP_INSPOL": "SMTP insurance policy",
    "SMTP_MAILPERCONN_LIMIT": "SMTP/SMQTN: messages per connection limit",
    "SMTP_MAILSIZE_LIMIT": "SMTP/SMQTN: message size limit",
    "SMTP_MF_ATTACHBLK_ATTACH_DROP": "Attachment block hit, attachment dropped",
    "SMTP_MF_ATTACHBLK_MSG_DROP": "Attachment block hit, message rejected",
    "SMTP_MF_RCPT_DROP": "Recipient rejected",
    "SMTP_MF_SIGN_REQUIRED": "Recipient rejected, message is not signed",
    "SMTP_MF_TLS_REQUIRED": "Recipient rejected, connection is not TLS",
    "SMTP_MIN_MSBC_DENSITY": "SMTP/SMQTN: squeeze if density of msbs arriving in data_in is below this minimum percentage value",
    "SMTP_NOCA_BYPASS_CONFIG": "Policy bypassed",
    "SMTP_NOCA_BYPASS": "SMTP/SMQTN: use default smtp config if CA is unavailable or config response has errors",
    "SMTP_OUTBD_DROP_SUSPECTED_SPAM": "Drop outbound messages suspected as spam",
    "SMTP_PLATFORM_1": "SMTP - Platform",
    "SMTP_PLATFORM_2": "SMTP - Platform II(Outbound)",
    "SMTP_PRETRY": "Retry from proxy - applicable on SMTP node only",
    "SMTP_PROXY": "SMTP proxy cluster",
    "SMTP_RCPT_COPY": "Recipient is a copy,",
    "SMTP_RCPT_REDIRECT": "Recipient is a redirect,",
    "SMTP_RCPT_UNDELIVERABLE": "Recipient rejected, unable to deliver",
    "SMTP_RCPTS_LIMIT": "SMTP/SMQTN: recipients per smtp transaction limit",
    "SMTP_RESERVED2": "SMTP: reserved2",
    "SMTP_RESERVED3": "SMTP: reserved3",
    "SMTP_RESERVED4": "SMTP: reserved4",
    "SMTP_RESERVED5": "SMTP: reserved5",
    "SMTP_REUSE_TIMEOUT_LIMIT": "SMTP/SMQTN: connection reuse pool timeout",
    "SMTP_SECURE": "SMTP - Secure",
    "SMTP_SENDER_MASQ": "Sender masqueraded,",
    "SMTP_SMQTN": "SMTP Quarantine cluster",
    "SMTP_SPAM_DROP": "Spam detected, message rejected",
    "SMTP_SPAM_IPWL": "Spam IPWL,",
    "SMTP_SPAM_SUSPECT_ALLOW": "Spam suspected, message allowed",
    "SMTP_SPAM_SUSPECT_DROP": "Spam suspected, message rejected",
    "SMTP_SPAM_SUSPECT_MARKSUBJ": "Spam suspected, subject prepended",
    "SMTP_SPAM_SUSPECT_QTN": "Spam suspected, message quarantined",
    "SMTP_SPAM_UBL_DROP": "Spam UBL detected, message rejected",
    "SMTP_SPAM_UWL": "Spam UWL,",
    "SMTP_SPAM": "SMTP - Anti-Spam Settings",
    "SMTP_SPF_ENABLED": "SPF lookup is enabled from this company",
    "SMTP_TRANS_TIMEOUT_LIMIT": "SMTP/SMQTN: transaction timeout (in secs)",
    "SMTP_USERLIST_LIMIT": "SMTP userlist query LIMIT value",
    "SMTP": "SMTP",
    "SMTPEVT_VERBOSE": "Log smtp events that are flagged as verbose",
    "SMTPTDL": "TDL - Transaction Data Limit",
    "SMUI_PROCESS_TIMEOUT": "timeout value of SMUI process",
    "SMUI": "SMUI",
    "SN_POSTING_CAUTIONED": "Cautioned to post message to this site",
    "SN_POSTING_DENIED": "Not allowed to post message to this site",
    "SN_WEBUSE_CAUTIONED": "Cautioned the use of this Social Network / Blogging site",
    "SN_WEBUSE_DENIED": "Not allowed the use of this Social Network / Blogging site",
    "SNAGFILMS_DESC": " SnagFilms",
    "SNAGFILMS": "SnagFilms",
    "SNMP_DESC": " SNMP applications (also called SNMP managers) and SNMP agents",
    "SNMP": "SNMP",
    "SNMPTRAP_DESC": "SNMP traps enable an agent to notify the management station of significant events by way of an unsolicited SNMP message",
    "SNMPTRAP": "SNMP Trap",
    "SOA": "SOA",
    "SOAP_DESC": " SOAP is a lightweight protocol intended for exchanging structured information in a decentralized, distributed environment. It defines, using XML technologies, an extensible messaging framework containing a message construct that can be exchanged over a variety of underlying protocols",
    "SOAP": "SOAP",
    "SOC1": "SOC1",
    "SOC2": "SOC2",
    "SOC3": "SOC3",
    "SOCIAL_ACTIVITY": "Social Networking Activity",
    "SOCIAL_ADULT_DESC": "Sites that provide social networking for adults, such as dating sites.",
    "SOCIAL_ADULT": "Social Networking Adult",
    "SOCIAL_ISSUES": "Social Issues",
    "SOCIAL_NETWORKING_GAMES": "Social Networking Games",
    "SOCIAL": "Social Networking",
    "SOCIALBAKERS": "SocialBakers",
    "SOCIALTV_DESC": " This protocol plug-in classifies the http traffic to the host srv.sixdegs.com",
    "SOCIALTV": "Social TV",
    "SOCIALVIBE_DESC": " This protocol plug-in classifies the http traffic to the host socialvibe.com",
    "SOCIALVIBE": "SocialVibe",
    "SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP": "The scheduled upgrade time is based on the time zone of your connector. Upgrades must be scheduled at least one hour ahead of time.",
    "SOFTWARE_UPGRADE_SCHEDULE": "Software Upgrade Schedule",
    "SOLOMON_ISLANDS_PACIFIC_GUADALCANAL": "Pacific/Guadalcanal",
    "SOLOMON_ISLANDS": "Solomon Islands",
    "SOMALIA_AFRICA_MOGADISHU": "Africa/Mogadishu",
    "SOMALIA": "Somalia",
    "SORRY_THIS_CODE_CAN_NOT_BE_USED": "Sorry, but this can not be used.",
    "SOURCE_IP_ADDRESSES": "Source IP Addresses",
    "SOURCE_IP_GROUP": "Source IP Group",
    "SOURCE_IP_GROUPS": "Source IP Groups",
    "SOURCE_IP": "Source IP",
    "SOURCE_PORT": "Source Port",
    "SOURCE_WORKLOAD_GROUPS": "Source Workload Groups",
    "SOURCE": "Source",
    "SOUTH_AFRICA_AFRICA_JOHANNESBURG": "Africa/Johannesburg",
    "SOUTH_AFRICA": "South Africa",
    "SOUTH_AMERICA": "South America",
    "SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS": "South Georgia and South Sandwich Islands",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA": "Atlantic/South Georgia",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS": "South Georgia and the South Sandwich Islands",
    "SOUTH_KOREA": "South Korea",
    "SOUTHAFRICA": "South Africa",
    "SOUTHAFRICANORTH": "(Africa) South Africa North",
    "SOUTHAFRICAWEST": "(Africa) South Africa West",
    "SOUTHAMERICA_EAST1_A": "southamerica-east1-a",
    "SOUTHAMERICA_EAST1_B": "southamerica-east1-b",
    "SOUTHAMERICA_EAST1_C": "southamerica-east1-c",
    "SOUTHAMERICA_EAST1": "southamerica-east1",
    "SOUTHAMERICA_WEST1_A": "southamerica-west1-a",
    "SOUTHAMERICA_WEST1_B": "southamerica-west1-b",
    "SOUTHAMERICA_WEST1_C": "southamerica-west1-c",
    "SOUTHAMERICA_WEST1": "southamerica-west1",
    "SOUTHCENTRALUS": "(US) South Central US",
    "SOUTHCENTRALUSSTAGE": "(US) South Central US (Stage)",
    "SOUTHEASTASIA": "(Asia Pacific) Southeast Asia",
    "SOUTHEASTASIASTAGE": "(Asia Pacific) Southeast Asia (Stage)",
    "SOUTHINDIA": "(Asia Pacific) South India",
    "SPAIN_AFRICA_CEUTA": "Africa/Ceuta",
    "SPAIN_ATLANTIC_CANARY": "Atlantic/Canary",
    "SPAIN_EUROPE_MADRID": "Europe/Madrid",
    "SPAIN": "Spain",
    "SPAINCENTRAL": "(Europe) Spain Central",
    "SPECIAL_INTERESTS": "Special Interests/Social Organizations",
    "SPECIALIZED_SHOPPING": "Online Shopping",
    "SPLIT_DEPLOY_CORE": "Split Deploy - Core",
    "SPLIT_DEPLOY_EDGE": "Split Deploy - Edge",
    "SPLUNK": "Splunk",
    "SPORTS": "Sports",
    "SPYWARE_OR_ADWARE": "Spyware Callback",
    "SRI_LANKA_ASIA_COLOMBO": "Asia/Colombo",
    "SRI_LANKA": "Sri Lanka",
    "SRV_RX_BYTES": "Server Received Bytes",
    "SRV_TIMEOUT": "DNS transaction timed out as server didn't respond",
    "SRV_TX_BYTES": "Server Sent Bytes",
    "SRV_TX_DROPS": "Server Drops Bytes",
    "SSDP_DESC": " Simple Service Discovery Protocol (SSDP) provides a mechanism whereby network clients can discover desired network services",
    "SSDP": "SSDP",
    "SSH_DESC": " Secure Shell SSH, sometimes known as Secure Socket Shell, is a UNIX-based command interface and a protocol for obtaining secure access to a remote computer",
    "SSH": "SSH",
    "SSHFP": "SSHFP",
    "SSL_CERTIFICATE": "SSL Certificate",
    "SSO_LOGOUT_MESSAGE": "You have successfully logged out of Cloud Connector Portal",
    "ST_HELENA_ATLANTIC_ST_HELENA": "Atlantic/St. Helena",
    "ST_HELENA": "St. Helena",
    "ST_KITTS_AND_NEVIS": "St Kitts and Nevis",
    "ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON": "America/Miquelon",
    "ST_PIERRE_AND_MIQUELON": "St. Pierre and Miquelon",
    "ST_VINCENT_AND_THE_GRENADINES": "St Vincent and the Grenadines",
    "STAGED": "Staged",
    "STANDBY": "Standby",
    "START_OVER": "Start Over",
    "START_TIME": "Start Time",
    "STARTS_WITH": "Starts With",
    "STAT": "Stat",
    "STATE_PROVINCE": "City/State/Province",
    "STATE": "State",
    "STATIC_IP_ADDRESS": "Static IP Address",
    "STATIC_IP_ADDRESSES": "Static IP Addresses and GRE Tunnels",
    "STATIC_IP_CONFLICT_WITH_SUBINTERFACE_IP": "Static lease IP conflicts with Sub interface IP",
    "STATIC_IP_HAS_DUPLICATES_IPs": "Static Lease has duplicates IPs",
    "STATIC_IP_HAS_DUPLICATES_MACS": "Static Lease has duplicates MACs",
    "STATIC_LEASE": "Static Lease",
    "STATIC_LOCATION_GROUPS": "Manual Location Groups",
    "STATIC_MANAGEMENT_IP": "Static Manangement IP",
    "STATIC_ROUTE_OPTIONAL": "Static Route (Optional)",
    "STATIC_ROUTE": "Static Route",
    "STATIC_SERVICE_IP": "Static Service IP",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1": "Are you sure you want to update the template status?",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED": "Updated item can be editable/deletable once moved to Staged state.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1": "The configuration will be applied to the device when it comes online.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED": "Template will not be editable once it is updated to Ready to Deploy.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE": "Are you sure you want to update this resource?",
    "STATUS_UPDATE_CONFIRMATION": "Status Update Confirmation!",
    "STATUS": "Status",
    "STORAGE_ACCOUNT_TEXT": "Select the regions, subscriptions, and storage account groups where the partner topic and destination are created.",
    "STORAGE_ACCOUNT": "Storage Account",
    "STREAMING_MEDIA": "Video Streaming",
    "STRING": "String",
    "SUB_CATEGORIES": "Sub Categories",
    "SUB_CATEGORY": "Sub-Category",
    "SUB_INTERFACE_SHUTDOWN": "Sub Interface Shutdown",
    "SUB_INTERFACE_VLAN": "Sub Interface VLAN",
    "SUB_INTERFACE": "Sub Interface",
    "SUBCLOUDS": "Subclouds",
    "SUBLOCATION_TYPE": "Sublocations Type",
    "SUBLOCATIONS": "Sublocations",
    "SUBMIT_A_TICKET": "Submit a Ticket",
    "SUBMIT_TICKET": "Submit a Ticket",
    "SUBMIT": "Submit",
    "SUBMITTED_ON": "Submitted On",
    "SUBNET_ID": "Subnet ID",
    "SUBSCRIPTION_GROUP_NAME": "Subscription Group Name",
    "SUBSCRIPTION_GROUP": "Subscription Group",
    "SUBSCRIPTION_GROUPS_TEXT": "Configure the regions and subscriptions for your Azure account.",
    "SUBSCRIPTION_GROUPS": "Subscription Groups",
    "SUBSCRIPTION_ID": "Azure Subscription ID",
    "SUBSCRIPTION_REQUIRED_MESSAGE": "This feature requires a subscription that your organization does not currently have.  To learn more about this feature, please contact your sales representative.",
    "SUBSCRIPTION_REQUIRED": "Subscription Required",
    "SUBSCRIPTIONS": "Subscriptions",
    "SUCCESS": "Success",
    "SUCCESSFULLY_DELETED": "Successfully Deleted",
    "SUCCESSFULLY_DISABLED": "Successfully Disabled",
    "SUCCESSFULLY_ENABLED": "Successfully Enabled",
    "SUCCESSFULLY_REGENERATED": "Successfully Regenerated",
    "SUCCESSFULLY_SAVED": "Successfully Saved",
    "SUCCESSFULLY_UPDATED": "Successfully Updated",
    "SUDAN_AFRICA_KHARTOUM": "Africa/Khartoum",
    "SUDAN": "Sudan",
    "SUM": "Sum",
    "SUMMARY": "Summary",
    "SUMO_LOGIC": "Sumo Logic",
    "SUNDAY": "Sunday",
    "SUPPORT_INFORMATION": "Support Information",
    "SUPPORT_TUNNEL": "Support Tunnel",
    "SUPPORT": "Support",
    "SURINAME_AMERICA_PARAMARIBO": "America/Paramaribo",
    "SURINAME": "Suriname",
    "SURROGATE_IP_REFRESH_RATE": "Refresh Time for re-validation of Surrogacy",
    "SUSPICIOUS_DESTINATION": "Suspicious Destination",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN": "Arctic/Longyearbyen",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS": "Svalbard and Jan Mayen Islands",
    "SVALBARD_AND_JAN_MAYEN": "Svalbard and Jan Mayen",
    "SVPN": "Z-Tunnel 2.0",
    "SWAZILAND_AFRICA_MBABANE": "Africa/Mbabane",
    "SWAZILAND": "Swaziland",
    "SWEDEN_EUROPE_STOCKHOLM": "Europe/Stockholm",
    "SWEDEN": "Sweden",
    "SWEDENCENTRAL": "(Europe) Sweden Central",
    "SWEDENSOUTH": "(Europe) Sweden South",
    "SWITZERLAND_EUROPE_ZURICH": "Europe/Zurich",
    "SWITZERLAND": "Switzerland",
    "SWITZERLANDNORTH": "(Europe) Switzerland North",
    "SWITZERLANDWEST": "(Europe) Switzerland West",
    "SYRIA": "Syria",
    "SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS": "Asia/Damascus",
    "SYRIAN_ARAB_REPUBLIC": "Syrian Arab Republic",
    "SYSLOG_DESC": " Syslog protocol is used for the transmission of event notification messages across networks between a client and a server",
    "SYSLOG": "Syslog",
    "SYSTEM_IN_READ_ONLY_MODE_ONLY": "UI is currently in the Read-Only mode.",
    "SYSTEM_IN_READ_ONLY_MODE": "UI is undergoing Upgrade Maintenance. It is currently in the Read-Only mode.",
    "SYSTEM_SETTINGS": "System Settings",
    "SYSTEM_USER": "System User",
    "SYSTEM": "System",
    "TAB_SEPARATED": "Tab Separated",
    "TABLE_OPTIONS": "Table Options",
    "TACACS_DESC": "Terminal Access Controller Access-Control System refers to a family of related protocols handling remote authentication and related services for networked access control through a centralized server",
    "TACACS_PLUS_DESC": " TACACS+ (Terminal Access Controller Access-Control System Plus) is a Cisco Systems proprietary protocol which provides access control for routers, network access servers and other networked computing devices via one or more centralized servers",
    "TACACS_PLUS": "TACACS+",
    "TACACS": "TACACS",
    "TAGGED": "Tagged",
    "TAGS": "Tags",
    "TAIWAN_ASIA_TAIPEI": "Asia/Taipei",
    "TAIWAN": "Taiwan",
    "TAJIKISTAN_ASIA_DUSHANBE": "Asia/Dushanbe",
    "TAJIKISTAN": "Tajikistan",
    "TANZANIA_AFRICA_DAR_ES_SALAAM": "Africa/Dar es Salaam",
    "TANZANIA": "Tanzania",
    "TARGET_ORG_ID": "Target OrgID",
    "TARINGA_DESC": " This protocol plug-in classifies the http traffic to the host taringa.net",
    "TARINGA": "Taringa",
    "TASTELESS_DESC": " Sites related to torture, human and animal degradation, and other behavior generally considered too inappropriate for a public audience.",
    "TASTELESS": "Tasteless",
    "TATTOODESIGNS": "Tattoo Designs",
    "TB": "TB",
    "TCF": "TCF",
    "TCHATCHE_DESC": "Tchatche is an instant messaging website",
    "TCHATCHE": "Tchatche",
    "TCP_ANY_DESC": "The Transmission Control Protocol (TCP) is one of the core protocols of the Internet protocol suite (IP), and is so common that the entire suite is often called TCP/IP",
    "TCP_ANY": "TCP",
    "TCP_DESC": " The Transmission Control Protocol (TCP) is one of the core protocols of the Internet protocol suite and is so common that the entire suite is often called TCP/IP",
    "TCP_DEST_PORTS": "TCP Destination Ports",
    "TCP_OVER_DNS_DESC": " Tcp-over-dns contains a special dns server and a special dns client. The client and server work in tandem to provide a TCP and UDP tunnel through the standard DNS protocol",
    "TCP_OVER_DNS": "TCP Over DNS",
    "TCP_PORT": "TCP Port",
    "TCP_PORTS": "TCP Ports",
    "TCP_SRC_PORTS": "TCP Source Ports",
    "TCP_STATS_COUNTER_INTERVAL": "Capture tcp state counters",
    "TCP_UNKNOWN_DESC": " This identifies TCP proxy/firewall traffic for which which more granular app cannot be determined",
    "TCP_UNKNOWN": "TCP Unknown",
    "TCP": "TCP",
    "TDS_DESC": "Protocol for the Microsoft SQL relational database management system",
    "TDS": "TDS",
    "TEACHERTUBE_DESC": " This protocol plug-in classifies the http traffic to the hosts teachertube.com and teachertube.biz",
    "TEACHERTUBE": "TeacherTube",
    "TEACHSTREET_DESC": " This protocol plug-in classifies the http traffic to the host teachstreet.com",
    "TEACHSTREET": "TeachStreet",
    "TEAMSPEAK_DESC": " The proprietary TeamSpeak2 protocol is used by gamers and oriented TeamSpeak2 VoIP software",
    "TEAMSPEAK_V3_DESC": " TeamSpeak 3 continues the legacy of the original TeamSpeak communication system. TeamSpeak 3 is not merely an extension of its predecessors but rather a complete rewrite in C++ of its proprietary protocol and core technology",
    "TEAMSPEAK_V3": "TeamSpeak 3",
    "TEAMSPEAK": "TeamSpeak",
    "TEAMVIEWER_DESC": " TeamViewer is an application that enables a connection to a remote computer in order to perform maintenance operations. It is also possible to show the current display to a remote computer, to transfer files, and to create a VPN tunnel",
    "TEAMVIEWER": "TeamViewer",
    "TECHINLINE_DESC": " This protocol plug-in classifies the http traffic to the host techinline.com. It also classifies the ssl traffic to the Common Name techinline.com",
    "TECHINLINE": "Techinline",
    "TECHNICAL_PRIMARY": "Technical Primary Contact",
    "TECHNICAL_SECONDARY": "Technical Secondary Contact",
    "TECHNOLOGY_COMMUNICATION": "Technology & Communication",
    "TECHNOLOGY": "Technology",
    "TED": "TED",
    "TELECOMMUNICATION": "Telecommunication",
    "TELEGRAM_DESC": " Telegram is an instant messaging protocol like Whatsapp",
    "TELEGRAM": "Telegram",
    "TELEVISION_AND_MOVIES_DESC": " Sites related to television programming or movies, regardless of the ability to stream or download media files.",
    "TELEVISION_AND_MOVIES": "Television/Movies",
    "TELEVISION_MOVIES_DESC": " Sites related to television programming or movies, regardless of the ability to stream or download media files.",
    "TELEVISION_MOVIES": "Television/Movies",
    "TELNET_DESC": " Telnet provides a fairly general, bi-directional, eight-bit byte oriented communications facility. Its primary aim is to provide a standard method of interfacing between terminal devices and terminal-oriented processes",
    "TELNET": "Telnet",
    "TELNETS_DESC": " Secure version of the Telnet",
    "TELNETS": "Secure Telnet",
    "TEMPLATE_NAME": "Template Name",
    "TEMPLATE_NOT_FOUND": "Template not found",
    "TEMPLATE_PREFIX": "Template Prefix",
    "TEMPLATE": "Template",
    "TENANT_ID": "Tenant ID",
    "TENANT_NAME": "Tenant Name",
    "TERRA_FORMATION": "Terraform",
    "TERRAFORMING": "Terraforming",
    "TERRFORMING_SCRIPT_TEXT": "Use the Below script and run in your GCP account to create Service account and which provides Appropriate Permissions for Zscaler to have read access to your projects.",
    "TERRFORMING_SCRIPT": "Terraforming Script",
    "TEST_CONNECTIVITY_FAILED": "Test Connectivity Failed",
    "TEST_CONNECTIVITY_SUCCESSFUL": "Test Connectivity Successful",
    "TEST_ENVIRONMENT_TEXT": "Traffic tests are simulated HTTP/HTTPS requests executed from a test environment created by Zscaler. This test environment is connected to the gateway using a VPC endpoint. A single test environment is associated with a Zscaler tenant. All the tests are executed from same environment.",
    "TEST_ENVIRONMENT": "Test Environment",
    "TEST_EXECUTED": "Test was executed",
    "TEST_NAME": "Test Name",
    "TEST_PROTOCOL": "Test Protocol",
    "TESTS": "Tests",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_R53": "Starter Deployment Template with ZPA and High-Availability",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_TEMPLATE": "Starter Deployment Template with High-Availability",
    "TF_DEFAULT_DEPLOYMENT_TEMPLATE": "Starter Deployment Template",
    "TF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Starter Deployment Template with ZPA",
    "TF_STARTER_DEPLOYMENT_GWLB_TEMPLATE": "Starter Deployment Template with Gateway Load Balancer (GWLB)",
    "TFTP_DESC": "Trivial File Transfer Protocol (TFTP) is a file transfer protocol notable for its simplicity",
    "TFTP": "TFTP",
    "THAILAND_ASIA_BANGKOK": "Asia/Bangkok",
    "THAILAND": "Thailand",
    "THE_BASE_URL_FOR_YOUR_API": "The base URL for your API is",
    "THE_FOLLOW_REGIONS_ARE_PENDING": "The follow regions are pending ",
    "THE_FOLLOWING_STATIC_LEASE_IP_IS_NOT_INCLUDED_ON_THE_ADDRESS_RANGE": "The following static lease IP is not included in the address range.",
    "THE_GAMBIA": "The Gambia",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_SUBNET": " The gateway IP should be in a LAN subnet.",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_WAN_SUBNET": " The gateway IP should be in a LAN or WAN subnet.",
    "THE_NETHERLANDS": "The Netherlands",
    "THERE_IS_A_PROBLEM_SAVING_PROVISIONING_TEMPLATE": "There is a problem with saving provisioning template, Please try again later.",
    "THERE_IS_A_PROBLEM_SAVING_VDI_TEMPLATE": "There is a problem with saving VDI template, Please try again later.",
    "THREAT_LIBRARY": "Threat Library",
    "THROUGHPUT_ACROSS_SERVICES": "Throughput Across Services",
    "THROUGHPUT_KBPS_PER_SESSION": "Throughput (kbps) / Session",
    "THROUGHPUT_SESSION": "[Throughput | Session]",
    "THURSDAY": "Thursday",
    "TIME_FRAME": "Timeframe",
    "TIME_ZONE": "Time Zone",
    "TIMESTAMP": "Timestamp",
    "TIMEZONE": "Timezone",
    "TIMOR_LESTE_ASIA_DILI": "Asia/Dili",
    "TIMOR_LESTE": "Timor-Leste",
    "TLS": "TLS",
    "TO": "To",
    "TOGO_AFRICA_LOME": "Africa/Lome",
    "TOGO": "Togo",
    "TOKELAU_PACIFIC_FAKAOFO": "Pacific/Fakaofo",
    "TOKELAU": "Tokelau",
    "TOKEN_VALUE": "Token Value",
    "TOKEN": "Token",
    "TONGA_PACIFIC_TONGATAPU": "Pacific/Tongatapu",
    "TONGA": "Tonga",
    "TOOLS": "Tools",
    "TOOLTIP_ACCOUNT_GROUP_DESCRIPTION": "Enter descriptive information about the group",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_AUTH_DEVICES": "Displays the admin's devices that are authorized to use the Executive Insights App. Admins can register up to 5 devices.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_COMMENTS": "(Optional) Enter additional notes or information. The comments cannot exceed 10240 characters.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_CONFIRM_PASSWORD": "Re-enter the password to confirm.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL": "Enter the admin's valid business email address.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EXEC_MOBILE_APP_ENABLE": "Allows the admin access to the Executive Insights App.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_LOGIN_ID": "Enter the login ID the admin uses to log in from your SSO provider portal. Select the appropriate domain name. (The domain names you provided to Zscaler appear in the dropdown menu.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_MOBILE_APP_ENABLE": "Enable to allow an admin access to the Executive Insights App. To enable this setting, the admin requires an <b>Organization</b> scope and an <b>admin role</b> with <b>Enable Permissions for Executive Insights App</b> selected.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_NAME": "Enter the admin name",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD_BASED_LOGIN": "Enable if you want to give the admin the option to log in directly to the admin portal. This can be in addition to enabling <b>SAML SSO for admins</b>.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD": "Enter a password for the admin. It can be 8 to 100 characters and must contain at least one number, one special character, and one upper-case letter.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PRODUCT_UPDATES": "Enable if you want the admin to receive email communications regarding important changes and updates to our service.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_ROLE": "Select a {0}role{1} to specify the admin's level of access to the admin portal. Roles you've configured appear in the dropdown menu. You can also search for roles or click the {2}Add{3} icon to add a new role. If you've enabled {4}Admin Rank{5}, your assigned admin rank determines the roles you can select.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_DEPARTMENTS": "Choose which departments the admin can manage in the admin portal. ",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATION_GROUPS": "Choose which location groups the admin can manage in the admin portal.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATIONS": "Choose which locations the admin can manage in the admin portal.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE": "Select a {0}scope{1} to specify which areas of the organization an admin can manage in the admin portal. Your assigned scope determines the scopes you can select.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SECURITY_UPDATES": "Enable if you want the admin to receive email communications on vulnerabilities and threats that may affect your organization.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SERVICE_UPDATES": "Enable if you want the admin to receive email communications about new service and product enhancements, including new data center notifications and cloud release information.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_STATUS": "Enable or disable the admin",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_UNAUTH_DEVICE": "Unauthorize",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ALLOW_TO_CREATE_NEW_LOCATION": "Enable the user to create new Locations.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_COMMENTS": "(Optional) Enter additional notes or information. Comments cannot exceed 10,240 characters.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_CONFIRM_PASSWORD": "Re-enter the password to confirm",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_LOGIN_ID": "Enter the <b>auditor's</b> login ID and select the appropriate domain name. (The domain names you provided to Zscaler appear in the dropdown menu.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NAME": "Enter the <b>auditor's</b> name.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NEW_PASSWORD": "Enter a password for the auditor. It can be 8 to 100 characters, and must contain at least one number, one special character, and one upper-case letter.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_EMAIL": "Enter the partner admin email address and select the appropriate domain name. The domain names you provided to Zscaler appear in the drop-down menu.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_LOGIN_ID": "Enter the login ID the partner admin uses to log in from your SSO provider portal and select the appropriate domain name. The domain names you provided to Zscaler appear in the drop-down menu.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_NAME": "Enter the partner admin name",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_PASSWORD": "If you want to give the partner admin the option to log in directly to the Admin Portal, enter a password. It can be 8 to 100 characters and must contain at least one number, one special character, and one upper-case letter. This can be in addition to enabling SAML single sign-on for partner admins.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_ROLE": "Select a <b>partner role</b> to specify the partner admin's level of access to the Admin Portal. Partner roles you've configured appear in the drop-down menu. You can also search for roles or click the <b>Add</b> icon to add a new partner role.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_DOWNLOAD_XML_METADATA": "Click {0}Download {1} to export the XML metadata of the Zscaler service. The metadata details Zscaler SAML capabilities and is used for auto-configuration. Some {2}IdPs{3} require the metadata to configure service providers.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ENABLE_SAML_AUTH": "Enable to allow admins to log in to the admin portal directly from your {0}SSO provider portal.{1} An {2}IdP{3} (for example, ADFS or Okta) must already be configured for your organization, and you must {4}add the admin account{5} in the admin portal (rather than through auto-provisioning.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ISSUERS": "Optionally, enter the IdP issuer associated with the Zscaler service.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_UPLOAD_SSL_CERTIFICATE": "Click {0}Upload{1} to upload the SSL public certificate that is used to verify the digital signature of the IdP. This is the base-64 encoded PEM format that you downloaded from the IdP. The file extension must be .pem or .cer and have no other periods (.) in the file name.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML": "Enable to allow the admin to log in to the Cloud Connector Admin Portal directly using a password. You can use this authentication method with SAML SSO.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_ADMIN_EDGE_CONNECTOR_TRAFFIC_FORWARDING_DNS": "Choose to give admins full, view-only, or no access to Forwarding (Traffic, DNS & Logs)",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT": " Choose to give admins full, view-only, or no access to API Key Management.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT": " Choose to give admins full or view-only access to Remote Assistance Management.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT": " Choose to give admins full, view-only, or no access to Administration Controls.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING": " Choose to give admins full, view-only, or no access to Cloud Connector Provisioning.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD": " Choose to give admins view-only or no access to Dashboards.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_LOCATION_MANAGEMENT": " Choose to give admins full, view-only, or no access to  Location Management.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION": " Choose to give admins full or none access to NSS.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_POLICY_CONFIGURATION": " Choose to give admins full, view-only, or no access to Policy and Administration.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": " Choose to give admins full, view-only, or no access to Public Cloud Config Management.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE": " Choose to give admins full, view-only, or no access to Location and Provisioning Templates.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME": "Enter a name for the {0}role{1}.",
    "TOOLTIP_ALL_APP_SEGMENTS_ONLY": "The ZPA Edge App Segment is available only if Apply to All App Segments is enabled.",
    "TOOLTIP_ALLOW_AWS_ACCOUNT_ID": "Enabling this would allow Zscaler to import AWS Account ID and display it in the portal",
    "TOOLTIP_ALLOW_AZURE_SUBSCRIPTION_ID": "Enabling this would allow Zscaler to import Azure Subscription ID and display it in the portal",
    "TOOLTIP_ALLOW_GCP_PROJECT_ID": "Enabling this would allow Zscaler to import GCP Project ID and display it in the portal",
    "TOOLTIP_AWS_CONFIG_NAME": "The name of the AWS account.",
    "TOOLTIP_AWS_GROUP_NAME": "Enter the name to be used for this group",
    "TOOLTIP_AWS_ROLE_NAME": "The name of the AWS role in the AWS account entered previously that is assumed by Zscaler.",
    "TOOLTIP_BC_BC_GROUP": "Select an existing Branch Connector Group for your Branch Provisioning Template.",
    "TOOLTIP_BC_COUNTRY": "Select a country for the new location.",
    "TOOLTIP_BC_DNS_SERVER": "Enter the DNS server IP address.",
    "TOOLTIP_BC_FORWARDING_NET_MASK": "Enter the netmask for the internal gateway IP address.",
    "TOOLTIP_BC_GROUP_5G": "This Connector Group will be associated with this Deployment Configuration.",
    "TOOLTIP_BC_GROUP_NAME": "Enter a name of the new Branch Connector Group you want to add.",
    "TOOLTIP_BC_HARDWARE_DEVICE": "Choose the Hardware Device for your Branch Connector.",
    "TOOLTIP_BC_HYPERVISOR": "Choose the Hypervisor for your Branch Connector.",
    "TOOLTIP_BC_INTERNAL_GATEWAY_IP_ADDRESS": "Enter the internal gateway IP address.",
    "TOOLTIP_BC_IP_ADDRESS": "Enter the IP Address of the Branch Connector.",
    "TOOLTIP_BC_LOAD_BALANCER_IP_ADDRESS": "Enter the load balancer IP address.",
    "TOOLTIP_BC_LOCATION_NAME": "Enter a name of the new location you want to add.",
    "TOOLTIP_BC_LOCATION_TEMPLATE": "Select a default or a configured location template for your provisioning URL.",
    "TOOLTIP_BC_LOCATION": "Select an existing location for your provisioning URL.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_1": "Enter the primary DNS server IP address. This is one of the two DNS server used to load balancing.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_2": "Enter the secondary DNS server IP address. This is one of the two DNS server used to load balancing.",
    "TOOLTIP_BC_MANAGEMENT_NET_MASK": "Enter the netmask for the Branch Connector IP address.",
    "TOOLTIP_BC_PROVISIONING_NAME": "Enter a name for the Branch Provisioning Template.",
    "TOOLTIP_BC_SERVER_IP_ADDRESS": "Enter the service IP address.",
    "TOOLTIP_BC_VM_SIZE": "This field is set to Small by default",
    "TOOLTIP_BLOCK_INTERNET_ACCESS": "Enable in order to disable all access to the internet, including non-HTTP traffic, until the user accepts the Acceptable Use Policy",
    "TOOLTIP_BW_DOWNLOAD": "Specify the maximum bandwidth limit of Download (Mbps).",
    "TOOLTIP_BW_UPLOAD": "Specify the maximum bandwidth limit of Upload (Mbps).",
    "TOOLTIP_CC_ROLE_NAME": "Tooltip text for Cloud connector: this name should be associated with all cloud connectors in that account",
    "TOOLTIP_CLOUD_COONECTOR_GROUP": "Select the cloud connector groups that will be associated with this account group",
    "TOOLTIP_CLOUD_NSS_HTTP_HEADERS": "",
    "TOOLTIP_CLOUD_PROVIDER": "Choose the Cloud Provider for your Cloud Connector.",
    "TOOLTIP_CPU": "The recommended CPU for the hypervisor.",
    "TOOLTIP_CUSTOM_AUP_FREQUENCY": "",
    "TOOLTIP_DEDICATED_BANDWIDTH": "This is the peak bandwidth required to download the logs from the Nanolog in the Zscaler cloud. If the NSS is not allocated the bandwidth it needs, the logs could accumulate in the Nanolog. This can result in frequent connection resets and the logs will not be streamed to the NSS.",
    "TOOLTIP_DEFAULT_GATEWAY_IP_ADDRESS": "Enter the default Gateway IP address.",
    "TOOLTIP_DEFAULT_GATEWAY": "Enter a valid default gateway IP address.",
    "TOOLTIP_DEFAULT_LEASE_TIME": "Enter the default lease time in seconds.",
    "TOOLTIP_DEPLOY_AS_GATEWAY": "Select Yes or No to decide whether the hardware device is deployed as a gateway.",
    "TOOLTIP_DESCRIPTION": "(Optional) Enter additional notes or information.",
    "TOOLTIP_DESTINATION_IP_ADDRESS": "Enter IP addresses. You can enter individual IP addresses, subnets, or address ranges. If adding multiple items, press Enter after each entry.",
    "TOOLTIP_DESTINATION_IP_COUNTRIES": "To identify destinations based on the location of a server, select Any to include all countries in the group or select specific countries.",
    "TOOLTIP_DESTINATION_IP_DOMAIN": "Enter fully qualified domain names (FQDNs) or wildcard FQDNs. Use dot ('.') as the wildcard character. To add multiple items, press Enter after each entry.",
    "TOOLTIP_DESTINATION_IP_FQDN": "Enter fully qualified domain names (FQDNs). If adding multiple items, press Enter after each entry.",
    "TOOLTIP_DESTINATION_IP_NAME": "Group together destinations that you want to control in a firewall rule,by specifying IP addresses, countries where servers are located and URL categories.",
    "TOOLTIP_DESTINATION_TYPE": "Select the Destination Group type",
    "TOOLTIP_DESTINATION_WORKLOAD_GROUPS": "Select any number of destination workload groups that are evaluated in the destination criteria portion of the RDR firewall policy.",
    "TOOLTIP_DEVICE_SN": "Select a device serial number.",
    "TOOLTIP_DHCP_OPTIONS": "You can create default gateways and domain names as criteria for DHCP.",
    "TOOLTIP_DHCP": "Select Enabled to enter DNS server details or Disabled to disable Dynamic Host Configuration Protocol (DHCP).",
    "TOOLTIP_DISK_STORAGE": "Displays the recommended disk storage for your organization's workload.",
    "TOOLTIP_DNS_SERVER_IP_1": "Enter the IP address of the primary DNS server.",
    "TOOLTIP_DNS_SERVER_IP_2": "Enter the IP address of the secondary DNS server.",
    "TOOLTIP_DNS_SERVER": "Enter a valid DNS server IP address.",
    "TOOLTIP_DOMAIN_NAME": "Enter a valid domain name.",
    "TOOLTIP_EBS_STORAGE": "Recommended Storage",
    "TOOLTIP_EC2_INSTANCE_TYPE": "Recommended EC2 Instance Type",
    "TOOLTIP_EDIT_ORGANIZATION_API_KEY_NEW_KEY": "The new API key can be alphanumeric (A-Z, a-z, 0-9) and exactly 12 characters in length.",
    "TOOLTIP_ENABLE_AUP": "Enable to display an Acceptable Use Policy for unauthenticated traffic and require users to accept it",
    "TOOLTIP_ENABLE_CAUTION": "Enable to Enforce Caution Policy action and display an end user notification for unauthenticated traffic. If disabled, the action is treated as an allow policy.",
    "TOOLTIP_ENABLE_IPS_CONTROL": "Enable to allow an admin to access IPS Control.",
    "TOOLTIP_ENABLE_SURROGATE_BROWSER": "If enabled, and if the IP-user mapping exists, then the Surrogate user identity is used for traffic from known browsers too. If disabled, traffic from known browsers will always be challenged using the configured authentication mechanism and Surrogate user identity is ignored",
    "TOOLTIP_ENABLE_SURROGATE_REFRESH_TIME": "This is the length of time that surrogate user identity can be used for traffic from known browsers before it must refresh and re-validate the surrogate user identity using the configured authentication mechanism.{0}{1} NOTE: Refresh time for revalidation of IP surrogacy must be smaller than the DHCP lease time. Otherwise wrong user policies may be applied.",
    "TOOLTIP_ENABLE_SURROGATE": "Enables user-to-device mapping when the internal IP address can be distinguished from the public IP address. This is used to enforce user policies on cookie-incompatible traffic. To learn more, see the Help page.",
    "TOOLTIP_ENABLE_XFF_FORWARDING": "Enable XFF from Client Request if you want the Zscaler service to use the X-Forwarded-For (XFF) headers that your on-premise proxy server inserts in outbound HTTP requests. Note that when the service forwards the traffic to its destination, it will remove this original XFF header and replace it with an XFF header that contains the IP address of the client gateway (the organization's public IP address). This ensures that an organization's internal IP addresses are never exposed to the external world.",
    "TOOLTIP_ENFORCE_AUTHENTICATION": "Enable Authentication to force identification of individual user traffic by applying configured user authentication mechanism.",
    "TOOLTIP_ENFORCE_BAND_WIDTH_CONTROL": "Select Enable to enforce bandwidth control for the location.",
    "TOOLTIP_ENFORCE_FIREWALL_CONTROL": "Select Enforce Firewall Control to enable the firewall at the location.",
    "TOOLTIP_ENTER_AWS_ACCOUNT_ID": "The AWS Account ID where workloads are deployed.",
    "TOOLTIP_EVENT_BUS_NAME": "Eventbridge Event Bus used to send real time notifications to Zscaler Event Bus. A rule in Eventbridge can be used to send real-time resource change notifications to Zscaler. This is necessary to enable real time updates to policies. Zscaler is notified of creation of a VM.",
    "TOOLTIP_EXTERNAL_ID": "Zscaler uses this external ID in the API call to AWS while fetching the tags information. The external ID is unique for each account created. This ID needs to be added in AWS IAM role configuration. If you regenerate this ID, also update it in the AWS account.",
    "TOOLTIP_FAILURE_BEHAVIOR": "Please select the failure behavior.",
    "TOOLTIP_FORCE_SSL_INTERCEPTION": "Enable to make SSL Interception enforce an Acceptable Use Policy for HTTPS traffic",
    "TOOLTIP_GATEWAY_FAIL_CLOSE": "This option indicates how to handle the traffic when both primary and secondary proxies defined in this gateway are unreachable. Enabling this option drops the traffic, and disabling this option allows the traffic. By default, this is enabled.",
    "TOOLTIP_GATEWAY_NAME": "Enter a name for the gateway to be created for a third party proxy service.",
    "TOOLTIP_GENERAL_DESCRIPTION": "(Optional) Enter additional notes or information. The description cannot exceed 10240 characters.",
    "TOOLTIP_HA_ID": "Enter the ID for the HA device.",
    "TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS": "This field may show comments about the entered IP address. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK": "You can enter an IP address to check if it has been denylisted. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS": "The field indicates if the entered IP address has been denylisted. ",
    "TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE": "Allow Zscaler support engineers to remotely log in to your Admin Portal with full admin privileges. You do not need to create accounts or share passwords to enable access.",
    "TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE": "Allow authorized Zscaler employees to access your Admin Portal with view-only privileges. Access to the portal will be used to produce customer success content and reports, provide remote assistance, and view reports and configuration to help improve the Zscaler product and services.",
    "TOOLTIP_HELP_LOOKUP_URL_ENTER_URL": "To look up the category (or categories) to which a URL belongs, type in the URL and click {0}Lookup URL{1}. The service displays the {2}predefined category or super-category of the URL{3}, and indicates if there is a security alert associated with the URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_SECURITY_ALERT": "This field indicates if there is a security alert associated with the URL.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL_CLASSIFICATIONS": "This field shows the {0}predefined category or super-category{1} to which the URL belongs. ",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL": "This field shows the URL you looked up.",
    "TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL": "We recommend allowing access for at least two days.",
    "TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL": "We recommend allowing access for at least one year.",
    "TOOLTIP_HW_DEVICE_NAME": "Enter a name for the template.",
    "TOOLTIP_HW_SUBINTERFACE_SHUTDOWN": "Select Active or Standby as the uplink mode.",
    "TOOLTIP_HW_VLAN_ID": "Enter the ID of the VLAN.",
    "TOOLTIP_HW_VLAN": "Select Tagged or Untagged for the virtual local area network (VLAN).",
    "TOOLTIP_INTERFACE_SHUTDOWN": "Select Yes or No to decide the interface shutdown behavior.",
    "TOOLTIP_IP_ADDRESS_RANGE": "Enter the IP address range for your device.",
    "TOOLTIP_IP_ADDRESS_WITH_NETMASK": "Enter an IP address in the format of a.b.c.d/mask",
    "TOOLTIP_IP_POOL_NAME": "The name of the IP Pool.",
    "TOOLTIP_LIST_CUSTOM_OPTION_CODE": "Please enter a valid DHCP Option Code as defined by IANA. Pre-defined Options are not allowed",
    "TOOLTIP_LIST_CUSTOM_OPTION_NAME": "Please enter a Name for this custom option - Not Parsed",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_IP": "The ip-address data type must be entered as an explicit IP address.  Up to four IP addresses, separated by commas, can be defined.\nE.G. *************, ************",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_STRING": "The string data type specifies either an NVT ASCII, or a series of octets specified in hexadecimal.\nE.G. '********:/var/tmp/rootfs' OR 43:4c:49:45:54:2d:46:4f:4f",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE": "Please select the type between IP or String",
    "TOOLTIP_LIST_CUSTOM_OPTION": "Custom DHCP Options allows you to configure DHCP Options that are not pre-defined in the dropdown list.",
    "TOOLTIP_LIST_DNS_SERVER": "Enter one or a list of valid DNS server IP address.",
    "TOOLTIP_LIST_DOMAIN_NAME": "Enter one or a list of valid domain name.",
    "TOOLTIP_LOCATION_CREATION": "This field is set to Automatic by default.",
    "TOOLTIP_LOCATION_TEMPLATE_NAME": "Enter a name of the location template you want to add.",
    "TOOLTIP_LOCATION_TEMPLATE": "Select a default or a configured location template for your provisioning URL.",
    "TOOLTIP_LOCATIONS_CUSTOM_AUP_FREQUENCY_TEXT": "Enter, in days, how frequently the Acceptable Use Policy is displayed to users",
    "TOOLTIP_LOCATIONS_ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "If enabled, and if the IP-user mapping exists, then the Surrogate user identity is used for traffic from known browsers too. If disabled, traffic from known browsers will always be challenged using the configured authentication mechanism and Surrogate user identity is ignored",
    "TOOLTIP_LOCATIONS_IDLE_TIME_DISASSOCIATION": "If you enabled IP Surrogate, in Idle Time to Disassociation, specify how long after a completed transaction the service retains the IP address to user mapping.",
    "TOOLTIP_MAX_LEASE_TIME": "Enter the maximum lease time in seconds.",
    "TOOLTIP_MTU_1500": "The Maximum Transmission Unit (MTU) for bytes.",
    "TOOLTIP_MTU": "The Maximum Transmission Unit (MTU) for bytes. The default is set to 1400",
    "TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD": "If enabled, dashboards will automatically refresh every 15 minutes.",
    "TOOLTIP_MY_PROFILE_CONFIRM_NEW_PASSWORD": "Reenter your new password. It must be the same as the password entered in the {0}New Password{1} field.",
    "TOOLTIP_MY_PROFILE_LANGUAGE": "The admin portal is displayed in English by default. You can also select Spanish, French, Traditional Chinese, or Japanese.",
    "TOOLTIP_MY_PROFILE_NEW_PASSWORD": "Enter your new password. It must have at least eight characters and one number, one upper-case character, and one special character. Only ASCII characters are allowed.",
    "TOOLTIP_MY_PROFILE_OLD_PASSWORD": "Enter your current password.",
    "TOOLTIP_MY_PROFILE_PASSWORD": "A password requires at least eight characters and include one number, one upper-case character, and one special character. Only ASCII characters are allowed.",
    "TOOLTIP_MY_PROFILE_POLICY_INFORMATION": "Enable to display the policy information.",
    "TOOLTIP_MY_PROFILE_TIMEZONE": "When the service saves transactions, it uses UTC. It uses the specified time zone when it displays the logs.",
    "TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME": "The admin's login ID assigned when the admin account was created.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_DESCRIPTION": "(Optional) Enter additional notes or information. The description cannot exceed 10240 characters.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_NAME": "Enter a name for the {0}network services group{1}. It can include any character and spaces.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_SERVICES": "Choose any number of custom and predefined services that you want to include in the group.",
    "TOOLTIP_NETWORK_SERVICES_DEFINITION": "The service displays {0}Custom{1} to indicate that this is an admin-defined service.",
    "TOOLTIP_NETWORK_SERVICES_DESCRIPTION": "(Optional) Enter additional notes or information. The description cannot exceed 10240 characters.",
    "TOOLTIP_NETWORK_SERVICES_NAME": "Enter a name for the {0}application layer service{1} that you want to control. It can include any character and spaces.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_DESTINATION_PORTS": "The SCTP destination port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_SOURCE_PORTS": "The SCTP source port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NETWORK_SERVICES_TCP_DESTINATION_PORTS": "The TCP destination port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NETWORK_SERVICES_TCP_SOURCE_PORTS": "The TCP source port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NETWORK_SERVICES_UDP_DESTINATION_PORTS": "The UDP destination port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NETWORK_SERVICES_UDP_SOURCE_PORTS": "The UDP source port number (example: 50) or port number range (example: 1000-1050), if any, that is used by the network service.",
    "TOOLTIP_NSS_CLOUD_FEED_API_URL": "The HTTPS URL of the SIEM log collection API endpoint.",
    "TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Enter the authorization URL with the Directory (tenant) ID generated in Azure",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Enter the access key ID for the user created in AWS",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Enter the secret access key for the user created in AWS",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID": "Enter the Application (client) ID generated in Azure",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET": "Enter the application client secret value generated in Azure",
    "TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE": "Enter the following string: client_credentials",
    "TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Stream logs in JSON Array format (e.g., [{JSON1},{JSON2}])",
    "TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Limit the size of an individual HTTP request payload to the SIEM's best practice",
    "TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Enabled by default and not editable",
    "TOOLTIP_NSS_CLOUD_FEED_SCOPE": "Enter the following string: https://monitor.azure.com//.default",
    "TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE": "Select your cloud-based SIEM",
    "TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL": "Enter the URL of the folder created in the S3 bucket",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Use this filter to limit the logs to specific Cloud/Branch Connector groups.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Use this filter to limit the logs to specific Cloud/Branch Connectors.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Use this filter to limit the logs based on a client's private IP address. You can enter: {0}An IP address, such as **************{1}A range of IP addresses, such as *********-**********{2}An IP address with a netmask, such as ***********/24{3}Click Enter after each entry.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Use this filter to limit the logs to sessions associated with specific DNS request types.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Use this filter to limit the logs to sessions associated with specific DNS response codes.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Use this filter to limit the logs to sessions associated with specific DNS response codes.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Limit the logs to sessions that contained specific data in the DNS responses. You can specify domain names and IPv4 and IPv6 addresses. For IPv4 addresses, you can enter an IP address, a range of IP addresses, or an IP address with a netmask.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DOMAINS": "Use this filter to limit the logs to sessions associated with specific domains.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DURATIONS": "Use this filter to limit the logs based on the duration of the sessions, in seconds.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_LOCATIONS": "Use this filter to limit the logs to specific locations from which transactions were generated. You can search for locations. There is no limit to the number of locations that you can select. Locations that are deleted after they are selected appear with a strikethrough line.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Limit the logs based on specific DNS policy actions",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_RULE_NAME": "Use this filter to limit the logs based on specific rules in the DNS Control policy. Choose the rules from the list.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_ADDRESS": "Use this filter to limit the logs to specific server IP addresses. You can enter: {0}An IP address, such as **************{1}A range of IP addresses, such as *********-**********{2}An IP address with a netmask, such as ***********/24{3}Click Enter after each entry.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_PORTS": "Use this filter to limit the logs to specific server ports. You can enter individual ports and a range of ports.",
    "TOOLTIP_NSS_FEED_DUPLICATE_LOGS": "To ensure that no logs are skipped during any down time, specify the number of minutes that NSS will send duplicate logs.",
    "TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE": "Limit the logs based on the metrics record type",
    "TOOLTIP_NSS_FEED_ESCAPE_CHARACTER": "Optionally, type a character that you would like to hex encode when it appears in URL, Host or Referrer. For example, type a comma, ',' to encode as %2C. This is useful if you are using this character as your delimiter and would like to ensure it does not cause erroneous delimitation. If custom encoding was done for a record, the {eedone} field will be 'YES' for that record.",
    "TOOLTIP_NSS_FEED_LOG_TYPE": "Choose the type of logs you are streaming.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Use this filter to limit the logs to specific Cloud/Branch Connectors.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Use this filter to limit the logs to specific Branch Connector VM.",
    "TOOLTIP_NSS_FEED_NAME": "Each feed is a connection between NSS and your SIEM. Enter a name for the feed.",
    "TOOLTIP_NSS_FEED_OUTPUT_FORMAT": "These are the fields that will be displayed in the output. You can edit the default list and if you chose Custom as the Field Output Type, change the delimiter as well. See NSS Feed Output Format for information about the available fields and their syntax.",
    "TOOLTIP_NSS_FEED_OUTPUT_TYPE": "The output is a comma-separated (CSV) list by default. Choose: Tab-separated to create a tab-separated list Custom to use a different delimiter, such as a dash, and enter the delimiter when you specify the feed output format.The feed output type of your SIEM, if listed",
    "TOOLTIP_NSS_FEED_SERVER": "Choose an NSS from the list.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Use this filter to limit the logs to specific Cloud/Branch Connector groups.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Use this filter to limit the logs to specific Cloud/Branch Connectors.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Use this filter to limit the logs based on a client's private IP address. You can enter: {0}An IP address, such as **************{1}A range of IP addresses, such as *********-**********{2}An IP address with a netmask, such as ***********/24{3}Click Enter after each entry.{4}",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_GATEWAY": "Use this filter to limit the logs to specific gateways.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_LOCATIONS": "Use this filter to limit the logs to specific locations from which transactions were generated. You can search for locations. There is no limit to the number of locations that you can select. Locations that are deleted after they are selected appear with a strikethrough line.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Select Any to apply the NSS feed all network services, or select specific network services.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Use this filter to limit the logs based on the action the service took, in accordance with the rules in the Forwarding Control policy.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME": "Use this filter to limit the logs based on specific rules in the Forwarding Control policy. Choose the rules from the list.",
    "TOOLTIP_NSS_FEED_SESSION_LOG_TYPE": "Choose the session log type",
    "TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE": "Select either IP address or FQDN as the destination type of the SIEM to which the logs are streamed.",
    "TOOLTIP_NSS_FEED_SIEM_FQDN": "Enter the FQDN of the SIEM to which the logs are streamed. Ensure that the SIEM is configured to accept the feed from the NSS.",
    "TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS": "Enter the IP address of the SIEM to which the logs are streamed. Ensure that the SIEM is configured to accept the feed from the NSS.",
    "TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT": "Enter an appropriate rate limit for the events per second that you want streamed to your SIEM. A limit that is too low for the traffic volume will cause log loss.",
    "TOOLTIP_NSS_FEED_SIEM_RATE": "Leave as Unlimited, unless you need to throttle the output stream due to SIEM licensing or other constraints.",
    "TOOLTIP_NSS_FEED_SIEM_TCP_PORT": "Enter the port number of the SIEM to which the logs are streamed. Ensure that the SIEM is configured to accept the feed from the NSS.",
    "TOOLTIP_NSS_FEED_STATUS": "The NSS feed is Enabled by default. Choose Disabled if you want to activate it at a later time.",
    "TOOLTIP_NSS_FEED_TIMEZONE": "This is the organization's time zone, by default. The time zone you set applies to the time field in the output file. The time zone automatically adjusts to changes in daylight savings in the specific time zone. The configured time zone can be output to the logs as a separate field. The list of time zones is derived from the IANA Time Zone database. Direct GMT offsets can also be specified.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Optionally, specify the number of users.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "You can retrieve this data by going to the DNS Overview dashboard. This is recommended to fine tune the VM specification to your organization's workload.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR": "You can retrieve this data by going to the Firewall Overview dashboard. This is recommended to fine tune the VM specification to your organization's workload.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM": "Indicate the platform used to deploy NSS.",
    "TOOLTIP_NSS_SERVER_NAME": "Enter a name for the NSS Server.",
    "TOOLTIP_NSS_SERVER_SSL_CERTIFICATE": "",
    "TOOLTIP_NSS_SERVER_STATE": "The health of the NSS server.",
    "TOOLTIP_NSS_SERVER_STATUS": "The NSS is enabled by default.",
    "TOOLTIP_NSS_TYPE": "This field is readonly.",
    "TOOLTIP_NSS_VIRTUAL_MACHINE": "Click to download the NSS OVA file.",
    "TOOLTIP_NUMBER_OF_CORES": "The recommended number of cores for the hypervisor.",
    "TOOLTIP_PASSPHRASE": "Enter a passphrase for the device.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRATION_EC": "Enable passwords to expire for all admins logging in to the ZIA, Cloud Connector, and ZDX Admin Portals. If disabled, passwords will never expire.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRES_AFTER": "Enter the number of days you want passwords to be valid for ZIA and ZDX admins. It can range from 15 to 365.",
    "TOOLTIP_PEER_DHCP": "Enter the peer DHCP server IP address from the other hardware device's corresponding interface. This option is configurable when HA is enabled to synchronize the DHCP leases.",
    "TOOLTIP_POLICY_APP_SEGMENT_GROUP": "Select up to 600 Segment Groups to apply to the traffic forwarding rule. If no groups are selected, the rule applies to no groups",
    "TOOLTIP_POLICY_APP_SEGMENT": "Select up to 50k Application Segments to apply to the traffic forwarding rule. If no segments are selected, the rule applies to no segments.",
    "TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS": "Select any number of predefined or custom network service groups. If a network service group is not selected, the rule applies to all network service groups.",
    "TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES": "Enter wildcard and fully qualified domain names (FQDNs). If adding multiple items, press Enter after each entry.",
    "TOOLTIP_POLICY_DNS_DESTINATION_GROUPS": "Select any number of destination groups. If a destination group is not selected, the rule applies to all destination groups",
    "TOOLTIP_POLICY_DNS_GATEWAY": "Choose a DNS gateway.",
    "TOOLTIP_POLICY_DNS_RULE_ORDER": "Policy rules are evaluated in ascending numerical order (Rule 1 before Rule 2, and so on), and the Rule Order reflects this rule’s place in the order.",
    "TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT": "Enable this setting to apply the traffic forwarding rule to all existing and future App Segments that are created. Disable this setting to select specific Application Segments or Segment Groups.",
    "TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC": "Select up to 32 groups. If a group is not selected, the rule applies to all groups",
    "TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES": "Select any number of network services. If a network service is not selected, the rule applies to all network service. The Zscaler firewall has predefined services and you can configure up to 1,024 additional custom services.",
    "TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC": "Choose from the following:{0}Allow{1}: Allow the DNS requests and responses.{2}Block{3}: Silently block all DNS requests and responses.{4}Resolved by ZPA{5}: Requests the Cloud Connector to resolve DNS requests using the IP Pool. An IP Pool must be available to use this option.{6}Redirect Request{7}: Redirect all DNS requests and responses to a DNS gateway. A DNS gateway must be available to use this option.{8}",
    "TOOLTIP_POLICY_FIREWALL_DESCRIPTION": "(Optional) Enter additional notes or information. The description cannot exceed 10,240 characters.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY": "To identify destinations based on the location of a server. Select any number of countries. If a country is not selected, the rule applies to all countries.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES": "Enter IP addresses and fully qualified domain names (FQDNs), if the domain has multiple destination IP addresses or if its IP addresses may change. For IP addresses, you can enter individual IP addresses, subnets, or address ranges. If adding multiple items, press Enter after each entry.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS": "Select any number of destination IP address groups. If a destination IP address group is not selected, the rule applies to all destination IP address groups",
    "TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD": "Select one of the following forwarding methods for this rule:{0}Direct:{1} Bypasses Zscaler Internet Access (ZIA) and/or Zscaler Private Access (ZPA) and forwards traffic directly to the destination server using the Zscaler service IP address{0}Local:{1} Traffic is sent back to its origin (i.e., load balancer) without NAT. You can only use Local with Cloud Connector.{2}Direct with SCTP Translation:{3} Forwards the traffic directly to the destination while tunneling SCTP traffic over UDP and vice-versa{4}ZIA{5}: Forwards the traffic to ZIA via the ZIA gateway{6}ZPA{7}: Forwards the traffic to ZPA via the ZPA cloud{8}ZPA with SCTP Translation:{9} Forwards the traffic to Zscaler Private Access (ZPA) via the ZPA cloud while tunneling SCTP traffic over UDP and vice-versa,{0}Drop{1}: Discards all packets that match the traffic forwarding rule{2}",
    "TOOLTIP_POLICY_FIREWALL_GATEWAY": "Select an Gateway",
    "TOOLTIP_POLICY_FIREWALL_IPPOOL": "Select an IP Pool",
    "TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP": "Select up to 32 location groups. If a location group is not selected, the rule applies to all location groups.",
    "TOOLTIP_POLICY_FIREWALL_LOCATION": "Select up to 8 locations. If a location is not selected, the rule applies to all locations.",
    "TOOLTIP_POLICY_FIREWALL_RULE_MSFT_OFFICE_365": "This rule is automatically created when you enable 'Microsoft Recommended One Click configuration for Office 365' to allow local break out for all Office 365 traffic in Cloud Firewall Product",
    "TOOLTIP_POLICY_FIREWALL_RULE_NAME": "The DNS automatically creates a Rule Name, which you can change. The maximum length is 31 characters.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ORDER": "Policy rules are evaluated in ascending numerical order (Rule 1 before Rule 2, and so on), and the Rule Order reflects this rule's place in the order.",
    "TOOLTIP_POLICY_FIREWALL_RULE_STATUS": "An enabled rule is actively enforced. A disabled rule is not actively enforced but does not lose its place in the Rule Order. The service skips it and moves to the next rule.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER_WARNING": "This rule is automatically created to redirect traffic to ZPA. It is recommended to move the predefined DNS rule for ZPA to either order 1 or 2",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER": "This rule is automatically created to redirect traffic to ZPA",
    "TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS": "Select any number of source IP address groups. If a source IP address group is not selected, the rule applies to all source IP address groups.",
    "SOURCE_WORKLOAD_GROUPS_TOOLTIP": "Select any number of source workload groups that the small and medium enterprise (SME) firewall rule can include and can be extended to the SMEDGE forwarding policy.",
    "TOOLTIP_POLICY_FIREWALL_UCAAS": "This rule is automatically created when you enable one or all UCaaS apps to allow traffic from Cloud Firewall",
    "TOOLTIP_PORT_NO": "Select a port number from the drop-down menu.",
    "TOOLTIP_PRIMARY_DNS_SERVER": "Enter the primary DNS server IP address.",
    "TOOLTIP_PRIMARY_PROXY": "Select the primary proxy for the gateway.",
    "TOOLTIP_PROV_TEMPLATE_NAME": "Enter a name for the Cloud Provisioning Template.",
    "TOOLTIP_REGION": "Select the regions where you want Zscaler to discover tags. Zscaler discovers tags at a regional level.",
    "TOOLTIP_SECONDARY_DNS_SERVER": "Enter the secondary DNS server IP address.",
    "TOOLTIP_SECONDARY_PROXY": "Select the secondary proxy for the gateway. This is used when the primary proxy is unreachable.",
    "TOOLTIP_SESSIONS_ACROSS_SERVICES": "Total number of sessions logged across all Cloud/Branch Connectors for the past 24 hours",
    "TOOLTIP_SHUTDOWN": "Select Yes or No to decide the shutdown behavior.",
    "TOOLTIP_SOURCE_IP_ADDRESSES": "Enter any number of IP addresses. You can enter: {0} An IP address (**************){1}A range of IP addresses *********-**********{2}An IP address with a netmask ***********/24{3} Press {4}Enter{5} after each entry.",
    "TOOLTIP_SOURCE_IP_GROUP_NAME": "The name of the source IP address group. For example, Social Media. Grouping source IP addresses facilitates referencing them in firewall policies.",
    "TOOLTIP_SOURCE_IP_NAME": "Source IP Name",
    "TOOLTIP_STATIC_LEASE": "Enter the mac address and IP address for DHCP.",
    "TOOLTIP_STATIC_ROUTE": "Enter the Route and Gateway details.",
    "TOOLTIP_SUBSCRIPTIONS": "Select the subscriptions you want to group together.",
    "TOOLTIP_TEMPLATE_PREFIX": "Enter the prefix for your location template name.",
    "TOOLTIP_THROUGHPUT_ACROSS_DIRECT": "Average Direct traffic throughput utilization in kbps \n\nTotal number of sessions logged per Cloud/Branch Connector for the past 24 hours",
    "TOOLTIP_THROUGHPUT_ACROSS_SERVICES": "Average traffic throughput utilization across all Cloud/Branch Connectors for the past 24 hours",
    "TOOLTIP_THROUGHPUT_ACROSS_ZIA": "Average ZIA traffic throughput utilization in kbps \n\nTotal number of sessions logged per Cloud/Branch Connector for the past 24 hours",
    "TOOLTIP_THROUGHPUT_ACROSS_ZPA": "Average ZPA traffic throughput utilization in kbps \n\nTotal number of sessions logged per Cloud/Branch Connector for the past 24 hours",
    "TOOLTIP_TRAFFIC_DISTRIBUTION": "Select Balanced or Best Link to dictate how traffic is distributed.",
    "TOOLTIP_UPGRADE_WINDOW": "The upgrades will be done in a staggered manner without any service impact",
    "TOOLTIP_USE_WAN_DNS_SERVER": "Select Yes to use the WAN DNS server or No to manually enter the LAN DNS server details. ",
    "TOOLTIP_VDI_AGENT_DESCRIPTION": "Enter descriptive information to help identify the agent.",
    "TOOLTIP_VDI_AGENT_PROFILE_NAME": "Enter a name of the VDI Profile you want to add.",
    "TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE": "Select either IdP or Hosted DB as the authentication type.",
    "TOOLTIP_VDI_FORWRDING_PROFILE_IPS": "Use this filter to limit the logs based on a client's private IP address. You can enter: {0}An IP address, such as ************** {1}An IP address:port range, such as *********:80 or ********* for all ranges {3}An IP address:port:protocol, such as *********:80:TCP, *********:80-100:UDP, or *********:80 for all protocols {4}Click Enter after each entry.",
    "TOOLTIP_VDI_GROUP_DESCRIPTION": "Enter descriptive information to help identify the group and it's purpose",
    "TOOLTIP_VDI_GROUP_NAME": "The name of the VDI group.",
    "TOOLTIP_VDI_HOSTNAME_PREFIX": "The hostname prefix used to group the VDI devices. This is the hostname detected by Zscaler Client Connector for VDI.",
    "TOOLTIP_VDI_LOCATION": "The Zscaler location to be associated with this VDI group. This is the same location as the cloud.",
    "TOOLTIP_VDI_MTU": "This is the Maximum Transmission Unit (MTU) value for the devices in the VDI group. The default value is 1400 bytes. If this value is not set correctly then it might impact the network performance of the VDI agent. Please make sure you group the devices with the same MTU values in a VDI group.",
    "TOOLTIP_VDI_OS_TYPE": "The type of Operating System (OS) of the VDI devices to be associated with this VDI group. The OS type detected by Zscaler Client Connector for VDI will be used to add a device to the group.",
    "TOOLTIP_VDI_TEMPLATE_IDP_NAME": "Search for or select an IdP name",
    "TOOLTIP_VDI_TEMPLATE_NAME": "Enter a name for the VDI Template Name.",
    "TOOLTIP_VDI_TEMPLATE_SYSTEM_USER": "Search for or select system user.",
    "TOOLTIP_VDI_ZPA_USER_TUNNEL_FALLBACK": "Connectors create a user tunnel to ZPA for each VDI user, up to the specified number of user tunnels specified here. If the number of VDI ZPA user tunnels from a connector exceeds this number, all subsequent transactions will be forwarded to ZPA via the connector-based tunnel. The user will be recognized as the cloud connector group in this connector-based tunnel.",
    "TOOLTIP_WAN_SELECTION": "WAN selection determines how traffic is forwarded across multiple WAN links. When set to Balanced, the traffic is evenly distributed. When set to Best Link, the traffic is always forwarded across the best-performing WAN link.",
    "TOOLTIP_ZIA_TUNNEL_MODEL": "The encryption type to be used when creating tunnel towards ZIA.",
    "TOPIC_CLOUD_CONNECTOR": "Topic Cloud Connector",
    "TOPIC_DISCOVERY": "Topic Discovery",
    "TOPIC_STATUS": "Topic Status",
    "TOTAL_CC_DEPLOYED": "Total Cloud Connectors Deployed",
    "TOTAL_DEPLOYED": "Total Deployed",
    "TOTAL_ENTITLED": "Total Entitled",
    "TOTAL_LATENCY": "Total Latency",
    "TOTAL_TRAFFIC": "Total Traffic",
    "TOTAL_TRANSACTIONS": "Total Transactions",
    "TOTAL": "Total",
    "TRACE": "Traceroute",
    "TRACEROUTE_DESC": "Trace Route is a utility to indicate the path to access a specific host",
    "TRACEROUTE": "Traceroute",
    "TRADING_BROKARAGE_INSURANCE": "Online Trading, Brokerage, Insurance",
    "TRADITIONAL_RELIGION": "Traditional Religion",
    "TRAFFIC_DIRECTON": "Traffic Direction",
    "TRAFFIC_DISTRIBUTION": "Traffic Distribution",
    "TRAFFIC_FLOW": "Traffic Flow",
    "TRAFFIC_FORWARDING_METHOD": "Forwarding Method",
    "TRAFFIC_FORWARDING_RESOURCE": "Traffic Forwarding Resource",
    "TRAFFIC_FORWARDING": "Traffic Forwarding",
    "TRAFFIC_FORWRDING_DNS": "Forwarding (Traffic, DNS & Logs)",
    "TRAFFIC_MONITORING": "Traffic Monitoring",
    "TRAFFIC_OVERVIEW": "Traffic Overview",
    "TRAFFIC_TEST": "Traffic Test",
    "TRAFFIC_TREND": "Traffic Trend",
    "TRAFFIC_TYPE": "Traffic Type",
    "TRANSACTIONS": "Transactions",
    "TRANSLATORS": "Other Information Technology",
    "TRAVEL": "Travel",
    "TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN": "America/Port of Spain",
    "TRINIDAD_AND_TOBAGO": "Trinidad and Tobago",
    "TROUBLESHOOTING_LOGGING": "Troubleshooting Logging",
    "TRUE": "True",
    "TRUSTED_ACCOUNT_ID": "Trusted Account ID",
    "TRUSTED_ROLE": "Trusted Role",
    "TS_DIRECTON": "TS Direction",
    "TUESDAY": "Tuesday",
    "TUNISIA_AFRICA_TUNIS": "Africa/Tunis",
    "TUNISIA": "Tunisia",
    "TUNNEL_AUTH_ALGORITHM": "Authentication Algorithm",
    "TUNNEL_AUTH_TYPE": "Authentication Type",
    "TUNNEL_DEAD_PEER_DETECTION": "Keep Alive Packets",
    "TUNNEL_DESTINATION_IP_END": "P2 Policy Dest IP - End",
    "TUNNEL_DESTINATION_IP_START": "P2 Policy Dest IP - Star",
    "TUNNEL_DESTINATION_IP": "Tunnel Destination IP",
    "TUNNEL_DESTINATION_PORT_END": "P2 Policy Dest Port - End",
    "TUNNEL_ENCRYPTION_ALGORITHM": "Encryption Algorithm",
    "TUNNEL_EVENT_REASON": "Event Reason",
    "TUNNEL_INFORMATION": "Tunnel Information",
    "TUNNEL_INITIATOR_COOKIE": "Initiator Cookie",
    "TUNNEL_INSIGHTS": "Tunnel Insights",
    "TUNNEL_IP": "Tunnel IP",
    "TUNNEL_IPSEC_PHASE2_SPI": "IKE Phase 2 SPI",
    "TUNNEL_LIFEBYTES": "Life Bytes",
    "TUNNEL_LIFETIME": "Tunnel Lifetime",
    "TUNNEL_LOG_TYPE": "Log Type",
    "TUNNEL_LOGS": "Tunnel Logs",
    "TUNNEL_POLICY_DIRECTION": "Policy Direction",
    "TUNNEL_PROTOCOL_NAME": "P2 Policy Protocol",
    "TUNNEL_PROTOCOL": "IPSec Protocol",
    "TUNNEL_RECEIVED_PACKETS": "Packets Received",
    "TUNNEL_RESPONDER_COOKIE": "Responder Cookie",
    "TUNNEL_SENT_PACKETS": "Packets Sent",
    "TUNNEL_SOURCE_IP_END": "P2 Policy Src IP - End",
    "TUNNEL_SOURCE_IP_START": "P2 Policy Src IP - Start",
    "TUNNEL_SOURCE_IP": "Tunnel Source IP",
    "TUNNEL_SOURCE_PORT_START": "P2 Policy Src Port - Start",
    "TUNNEL_STATUS": "Tunnel Status",
    "TUNNEL_TYPE": "Tunnel Type",
    "TUNNEL_VENDOR_ID": "Vendor ID",
    "TUNNEL_VPN_CREDENTIAL": "VPN Credential",
    "TURKEY_EUROPE_ISTANBUL": "Europe/Istanbul",
    "TURKEY": "Turkey",
    "TURKMENISTAN_ASIA_ASHGABAT": "Asia/Ashgabat",
    "TURKMENISTAN": "Turkmenistan",
    "TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK": "America/Grand Turk",
    "TURKS_AND_CAICOS_ISLANDS": "Turks and Caicos Islands",
    "TUVALU_PACIFIC_FUNAFUTI": "Pacific/Funafuti",
    "TUVALU": "Tuvalu",
    "TWO_CLOUD_CONNECTOR_TEMPLATE": "Add-on Template with High-Availability",
    "TX_BYTES": "Sent Bytes",
    "TX_PACKETS": "Sent Packets",
    "TX_RX_BYTES": " TX | RX Bytes",
    "TX_RX_PACKETS": " TX | RX Packets",
    "TYPE_ACCOUNT_ID": "Type Account ID",
    "TYPE_ACCOUNT_NAME": "Type Account Name",
    "TYPE_APPLICATION_ID": "Type Application ID",
    "TYPE_APPLICATION_KEY": "Type Application Key",
    "TYPE_AWS_ACCESS_KEY_ID": "Type AWS Access Key ID",
    "TYPE_AWS_SECRET_ACCESS_KEY": "Type AWS Secret Access Key",
    "TYPE_BASE_URL": "Type Base URL",
    "TYPE_CLIENT_SECRET": "Type Client Secret",
    "TYPE_DESCRIPTION_HERE": "Type Description here",
    "TYPE_DOWNLOAD_MBPS": "Type Download (Mbps)",
    "TYPE_DSTN_IP_NAME": "Type Destination IP Name",
    "TYPE_FEED_ESCAPE_CHARACTER": "Enter Text",
    "TYPE_GATEWAY_NAME": "Type Gateway Name here",
    "TYPE_GROUP_NAME_HERE": "Type Group Name Here",
    "TYPE_IP_ADDRESS_HERE": "Type IP Address here",
    "TYPE_IP_ADDRESSESS_HERE": "Type IP Address",
    "TYPE_IP_POOL_NAME": "Type IP Pool Name",
    "TYPE_KEY": "Type Key",
    "TYPE_LOCATION_TEMPLATE_NAME": "Type Location Template Name",
    "TYPE_NSS_CLOUD_FEED_API_URL": "Enter API Url",
    "TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Enter Authorization URL",
    "TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Enter Access Key Id",
    "TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Enter Secret Access Key",
    "TYPE_NSS_CLOUD_FEED_CLIENT_ID": "Enter Application Client Id",
    "TYPE_NSS_CLOUD_FEED_CLIENT_SECRET": "Enter Application Client Secret",
    "TYPE_NSS_CLOUD_FEED_GRANT_TYPE": "Enter client_credentials",
    "TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Enter Text",
    "TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL": "Enter S3 Folder URL",
    "TYPE_NSS_CLOUD_FEED_SCOPE": "Enter https://monitor.azure.com//.default",
    "TYPE_NSS_FEED_NAME": "Enter Text",
    "TYPE_NSS_SERVER_NAME": "Enter Text",
    "TYPE_SIEM_FQDN": "Enter Text",
    "TYPE_SIEM_IP_ADDRESS": "Enter Text",
    "TYPE_SIEM_RATE_LIMIT": "Enter Text",
    "TYPE_SIEM_TCP_PORT": "Enter Text",
    "TYPE_SOURCE_IP_NAME": "Type Source IP Group Name",
    "TYPE_SUBSCRIPTION_ID": "Type Subscription ID",
    "TYPE_TEMPLATE_PREFIX": "Type Template Prefix",
    "TYPE_TENANT_ID": "Type Tenant ID",
    "TYPE_UPLOAD_MBPS": "Type Upload (Mbps)",
    "TYPE_VALUE": "Type Value",
    "TYPE_ZS_TAG_OPTIONAL": "Type ZS Tag (Optional)",
    "TYPE": "Type",
    "UAE": "United Arab Emirates",
    "UAECENTRAL": "(Middle East) UAE Central",
    "UAENORTH": "(Middle East) UAE North",
    "UBUNTU_LINUX": "ubuntu LINUX",
    "UDP_ANY_DESC": "The User Datagram Protocol (UDP) is one of the core members of the Internet protocol suite (the set of network protocols used for the Internet)",
    "UDP_ANY": "UDP",
    "UDP_DESC": " The User Datagram Protocol (UDP) is one of the core members of the Internet protocol suite (the set of network protocols used for the Internet",
    "UDP_DEST_PORTS": "UDP Destination Ports",
    "UDP_PORTS": "UDP Ports",
    "UDP_SRC_PORTS": "UDP Source Ports",
    "UDP_UNKNOWN_DESC": " This identifies UDP proxy/firewall traffic for which which more granular app cannot be determined",
    "UDP_UNKNOWN": "UDP Unknown",
    "UDP": "UDP",
    "UGANDA_AFRICA_KAMPALA": "Africa/Kampala",
    "UGANDA": "Uganda",
    "UK": "United Kingdom",
    "UKRAINE_EUROPE_KIEV": "Europe/Kiev",
    "UKRAINE_EUROPE_SIMFEROPOL": "Europe/Simferopol",
    "UKRAINE_EUROPE_UZHGOROD": "Europe/Uzhgorod",
    "UKRAINE_EUROPE_ZAPOROZHYE": "Europe/Zaporozhye",
    "UKRAINE": "Ukraine",
    "UKSOUTH": "(Europe) UK South",
    "UKWEST": "(Europe) UK West",
    "UNABLE_TO_LOGIN_TRY_AGAIN": "Unable to login, please try again later",
    "UNAUTHORIZED_COMMUNICATION": "Unauthorized Communication",
    "UNENCRYPTED": "Unencrypted",
    "UNEXPECTED_ERROR": "An Unexpected Error Has Occurred",
    "UNHEALTHY": "Unhealthy",
    "UNITED_ARAB_EMIRATES_ASIA_DUBAI": "Asia/Dubai",
    "UNITED_ARAB_EMIRATES": "United Arab Emirates",
    "UNITED_KINGDOM_EUROPE_LONDON": "Europe/London",
    "UNITED_KINGDOM": "United Kingdom",
    "UNITED_STATES_AMERICA_ADAK": "America/Adak",
    "UNITED_STATES_AMERICA_ANCHORAGE": "America/Anchorage",
    "UNITED_STATES_AMERICA_BOISE": "America/Boise",
    "UNITED_STATES_AMERICA_CHICAGO": "America/Chicago",
    "UNITED_STATES_AMERICA_DENVER": "America/Denver",
    "UNITED_STATES_AMERICA_DETROIT": "America/Detroit",
    "UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS": "America/Indiana/Indianapolis",
    "UNITED_STATES_AMERICA_INDIANA_KNOX": "America/Indiana/Knox",
    "UNITED_STATES_AMERICA_INDIANA_MARENGO": "America/Indiana/Marengo",
    "UNITED_STATES_AMERICA_INDIANA_PETERSBURG": "America/Indiana/Petersburg",
    "UNITED_STATES_AMERICA_INDIANA_TELL_CITY": "America/Indiana/Tell City",
    "UNITED_STATES_AMERICA_INDIANA_VEVAY": "America/Indiana/Vevay",
    "UNITED_STATES_AMERICA_INDIANA_VINCENNES": "America/Indiana/Vincennes",
    "UNITED_STATES_AMERICA_INDIANA_WINAMAC": "America/Indiana/Winamac",
    "UNITED_STATES_AMERICA_JUNEAU": "America/Juneau",
    "UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE": "America/Kentucky/Louisville",
    "UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO": "America/Kentucky/Monticello",
    "UNITED_STATES_AMERICA_LOS_ANGELES": "America/Los Angeles",
    "UNITED_STATES_AMERICA_MENOMINEE": "America/Menominee",
    "UNITED_STATES_AMERICA_NEW_YORK": "America/New York",
    "UNITED_STATES_AMERICA_NOME": "America/Nome",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER": "America/North Dakota/Center",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM": "America/North Dakota/New Salem",
    "UNITED_STATES_AMERICA_PHOENIX": "America/Phoenix",
    "UNITED_STATES_AMERICA_SHIPROCK": "America/Shiprock",
    "UNITED_STATES_AMERICA_YAKUTAT": "America/Yakutat",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON": "Pacific/Johnston",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY": "Pacific/Midway",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE": "Pacific/Wake",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS": "United States Minor Outlying Islands",
    "UNITED_STATES_PACIFIC_HONOLULU": "Pacific/Honolulu",
    "UNITED_STATES": "United States",
    "UNITEDSTATES": "United States",
    "UNITEDSTATESEUAP": "United States EUAP",
    "UNITS": "Units",
    "UNKNOWN_ERROR_CODE": "Unknown Error Code",
    "UNKNOWN_HOPS": "Unknown Hops",
    "UNLIMITED": "Unlimited",
    "UNREGISTERED": "Unregistered",
    "UNSELECTED_ITEMS": "Unselected Items",
    "UNSELECTED": "Unselected",
    "UNTAGGED": "Untagged",
    "UP": "Up",
    "UPDATE": "Update",
    "UPDATED": "Updated",
    "UPF_IP_CIDR": "User Plane Function IP/CIDR",
    "UPF_NAME": "User Plane Function Name",
    "UPGRADE_ON": "Upgrade On",
    "UPGRADE_SCHEDULE": "Upgrade Schedule",
    "UPGRADE_STATUS": "Upgrade Status",
    "UPGRADE_WILL_BE_SCHEDULED": "Upgrades will be scheduled as per the local time zone of the cloud connector",
    "UPGRADE_WINDOW": "Upgrade Window",
    "UPLINK_MODE": "Uplink Mode",
    "UPLOAD_MBPS": "Upload (Mbps)",
    "UPLOAD": "Upload",
    "UPTIME": "Uptime",
    "URL_LOOKUP": "URL Lookup",
    "URUGUAY_AMERICA_MONTEVIDEO": "America/Montevideo",
    "URUGUAY": "Uruguay",
    "US_CENTRAL1_A": "us-central1-a",
    "US_CENTRAL1_B": "us-central1-b",
    "US_CENTRAL1_C": "us-central1-c",
    "US_CENTRAL1_F": "us-central1-f",
    "US_CENTRAL1": "us-central1",
    "US_EAST_1": "us-east-1 (N. Virginia)",
    "US_EAST_1A": "us-east-1a",
    "US_EAST_1B": "us-east-1b",
    "US_EAST_1C": "us-east-1c",
    "US_EAST_1D": "us-east-1d",
    "US_EAST_1E": "us-east-1e",
    "US_EAST_1F": "us-east-1f",
    "US_EAST_2": "us-east-2 (Ohio)",
    "US_EAST_2A": "us-east-2a",
    "US_EAST_2B": "us-east-2b",
    "US_EAST_2C": "us-east-2c",
    "US_EAST1_B": "us-east1-b",
    "US_EAST1_C": "us-east1-c",
    "US_EAST1_D": "us-east1-d",
    "US_EAST1": "us-east1",
    "US_EAST4_A": "us-east4-a",
    "US_EAST4_B": "us-east4-b",
    "US_EAST4_C": "us-east4-c",
    "US_EAST4": "us-east4",
    "US_EAST5_A": "us-east5-a",
    "US_EAST5_B": "us-east5-b",
    "US_EAST5_C": "us-east5-c",
    "US_EAST5": "us-east5",
    "US_GOV_EAST_1": "AWS GovCloud (US-East)",
    "US_GOV_EAST_1A": "us-gov-east-1a",
    "US_GOV_EAST_1B": "us-gov-east-1b",
    "US_GOV_EAST_1C": "us-gov-east-1c",
    "US_GOV_WEST_1": "AWS GovCloud (US-West)",
    "US_GOV_WEST_1A": "us-gov-west-1a",
    "US_GOV_WEST_1B": "us-gov-west-1b",
    "US_GOV_WEST_1C": "us-gov-west-1c",
    "US_OUTLYING_ISLANDS": "U.S. Outlying Islands",
    "US_SOUTH1_A": "us-south1-a",
    "US_SOUTH1_B": "us-south1-b",
    "US_SOUTH1_C": "us-south1-c",
    "US_SOUTH1": "us-south1",
    "US_VIRGIN_ISLANDS": "U.S. Virgin Islands",
    "US_WEST_1": "us-west-1 (N. California)",
    "US_WEST_1A": "us-west-1a",
    "US_WEST_1B": "us-west-1b",
    "US_WEST_1C": "us-west-1c",
    "US_WEST_2_LAX_1A": "us-west-2-lax-1a",
    "US_WEST_2": "us-west-2 (Oregon)",
    "US_WEST_2A": "us-west-2a",
    "US_WEST_2B": "us-west-2b",
    "US_WEST_2C": "us-west-2c",
    "US_WEST_2D": "us-west-2d",
    "US_WEST1_A": "us-west1-a",
    "US_WEST1_B": "us-west1-b",
    "US_WEST1_C": "us-west1-c",
    "US_WEST1": "us-west1",
    "US_WEST2_A": "us-west2-a",
    "US_WEST2_B": "us-west2-b",
    "US_WEST2_C": "us-west2-c",
    "US_WEST2": "us-west2",
    "US_WEST3_A": "us-west3-a",
    "US_WEST3_B": "us-west3-b",
    "US_WEST3_C": "us-west3-c",
    "US_WEST3": "us-west3",
    "US_WEST4_A": "us-west4-a",
    "US_WEST4_B": "us-west4-b",
    "US_WEST4_C": "us-west4-c",
    "US_WEST4": "us-west4",
    "USDODCENTRAL": "US DoD Central",
    "USDODEAST": "US DoD East",
    "USE_WAN_DNS_SERVER": "Use WAN DNS Server",
    "USE_WAN_DNS_SEVER": "Use WAN DNS Server",
    "USER_ACCOUNT_LOCKED": "Your account has been temporarily locked due to too many login failures. Try again later.",
    "USER_DEFINED_TAGS": "User Defined Tags",
    "USER_DEFINED": "User-Defined",
    "USER_ID": "User ID",
    "USER_MANAGEMENT": "User Management",
    "USER_NAME_VISIBILITY": "User Name Visibility",
    "USER_NAME": "User Display Name",
    "USER_NAMES": "User Names",
    "USER_PLANE_FUNCTION": "User Plane Function",
    "USER": "User",
    "USERNAMES": "User Names",
    "USGOVARIZONA": "USGov Arizona",
    "USGOVIOWA": "USGov Iowa",
    "USGOVTEXAS": "USGov Texas",
    "USGOVVIRGINIA": "USGov Virginia",
    "USSECEAST": "USSec East",
    "USSECWEST": "USSec West",
    "USSECWESTCENTRAL": "USSec West Central",
    "UZBEKISTAN_ASIA_SAMARKAND": "Asia/Samarkand",
    "UZBEKISTAN_ASIA_TASHKENT": "Asia/Tashkent",
    "UZBEKISTAN": "Uzbekistan",
    "VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE": "Up to {0} items are allowed.",
    "VALIDATION_ERROR_EMPTY_PROTOCOL": "Please enter a protocol.",
    "VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS": "One or more items in this list is invalid.",
    "VALIDATION_ERROR_INVALID_AWS_ROLENAME": "Please enter a valid AWS rolename. It should contain only alphanumeric characters and '+=,.@-' .",
    "VALIDATION_ERROR_INVALID_DOMAIN": "Enter a valid domain name.",
    "VALIDATION_ERROR_INVALID_END_PORT_RANGE": "The end port must be between 1 and 65535 and greater than the start port.",
    "VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS": "Enter a valid FQDN, IP address, IP address range, or IP CIDR block.",
    "VALIDATION_ERROR_INVALID_IP_ADDRESS": "Enter a valid IP address, IP address range, or IP CIDR block.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK_IS_BROADCAST": "Please enter a valid IP, this is a Broadcast Ip for the subnet.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK": "Ensure that IP addresses are in the same subnet.",
    "VALIDATION_ERROR_INVALID_IP_PORT": "Please enter a valid TCP Port (0-65535)",
    "VALIDATION_ERROR_INVALID_IP_WITH_CIDR": "Enter a valid IP address with CIDR.",
    "VALIDATION_ERROR_INVALID_IP": "Please enter a valid IP",
    "VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS": "Enter a valid URL address without an http:// or https:// prefix. Url should have at least host.domain pattern to qualify.",
    "VALIDATION_ERROR_INVALID_MAC_ADDRESS": "Please enter a valid MAC address.",
    "VALIDATION_ERROR_INVALID_NAME": "Please enter a valid Name",
    "VALIDATION_ERROR_INVALID_PORT_STRING": "Enter a valid port number or port number range (e.g., 587, 1-65535).",
    "VALIDATION_ERROR_INVALID_PROTOCOL": "Please enter a valid protocol.",
    "VALIDATION_ERROR_INVALID_SECONDARY_FIELD": "Secondary field (Manual) can not be empty!",
    "VALIDATION_ERROR_INVALID_SERVICE_IP_MASK": "All Service IPs should have the same subnet mask.",
    "VALIDATION_ERROR_INVALID_START_PORT_RANGE": "Port numbers must be between 1 and 65535.",
    "VALIDATION_ERROR_INVALID_URL": "Enter a valid URL address with an http:// or https:// prefix.",
    "VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE": "This IP is inside one of the following invalid ranges. \n\n [0.0.0.0-*************]\n [**********-***************]\n [*********-***************]\n [***********-***************]\n [240.0.0.0-***************] ",
    "VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE": "This number must be between 128 KB and 1 MB",
    "VALIDATION_ERROR_SAME_IP": "The IPs should be different.",
    "VALIDATION_ERROR_SAME_SERVICE_IP": "The service IPs should be different.",
    "VALIDATION_ERROR_SAME_START_END_PORT_RANGE": "The start port and end port cannot be same.",
    "VALIDATION_NETWORK_SERVICE_GROUP_NAME_REQUIRED": "The Network Service Group name cannot be empty.",
    "VALIDATION_NETWORK_SERVICE_GROUP_SERIVCE_REQUIRED": "At least one type of service must be specified.",
    "VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT": "Enter at least one TCP or UDP Destination Port. Source Ports require a corresponding Destination Port.",
    "VALIDATION_NETWORK_SERVICE_MIN_PORT_REQUIRED": "At least one type of port must be specified.",
    "VALIDATION_NETWORK_SERVICE_NAME_REQUIRED": "The Network Service name cannot be empty.",
    "VALUE": "Value",
    "VANUATU_PACIFIC_EFATE": "Pacific/Efate",
    "VANUATU": "Vanuatu",
    "VATICAN_CITY_STATE_EUROPE_VATICAN": "Europe/Vatican",
    "VATICAN_CITY_STATE": "Vatican City State",
    "VATICAN_CITY": "Vatican City",
    "VDI_AGENT_APP": "VDI App Store",
    "VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT": "Little detailed placeholder text...",
    "VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT": "This setting is used by Zscaler Client Connector for VDI to include or exclude traffic from the tunnel to the Cloud or Branch Connector. In some cases, inclusion and exclusion lists must also be configured in your Cloud or Branch Connector.",
    "VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT": "Enter text separated by return.",
    "VDI_AGENT_FORWARDING_PROFILE": "VDI Forwarding Profile",
    "VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT": "This configuration is used for end user authentication and the user ID associated with the system user.",
    "VDI_AGENT_TEMPLATE": "VDI Template",
    "VDI_AGENT_TEMPLATES": "VDI Templates",
    "VDI_DEVICE_GENERAL_TEXT": "Enter information that will help you identify a specific group.",
    "VDI_DEVICE_MANAGEMENT": "VDI Device Management",
    "VDI_DEVICES": "VDI Devices",
    "VDI_FORWARDING_PROFILE_TEXT": "The forwarding profile to be associated with this VDI group. The chosen forwarding profile's inclusion or exclusion policies will be applied by Zscaler Client Connector for VDI and installed in the devices included in this VDI group.",
    "VDI_FORWARDING_PROFILE": "VDI Forwarding Profile",
    "VDI_GROUPS": "VDI Groups",
    "VDI_MANAGEMENT": "VDI Management",
    "VDI_REVIEW_TEXT": "Ensure all the information below is correct before adding this group.",
    "VDI_ZPA_USER_TUNNEL_FALLBACK": "VDI ZPA User Tunnel Fallback",
    "VDO_LIVE_DESC": "VDOLive is a scalable, video streaming technology",
    "VDO_LIVE": "VDOLive",
    "VEHICLES": "Vehicles",
    "VENDOR": "Vendor",
    "VENEZUELA_AMERICA_CARACAS": "America/Caracas",
    "VENEZUELA": "Venezuela",
    "VERBOSE": "Verbose",
    "VERIFY_CURRENT_PASSWORD": "Verify Current Password",
    "VERSION_PROFILE": "Version Profile",
    "VERSION": "Version",
    "VIET_NAM_ASIA_SAIGON": "Asia/Saigon",
    "VIET_NAM": "Viet_nam",
    "VIETNAM": "Vietnam",
    "VIEW_5G_DEPLOYMENT": "View Deployment Configuration",
    "VIEW_APPLIANCE": "View Appliance",
    "VIEW_AWS_ACCOUNT": "View AWS Account",
    "VIEW_AWS_GROUP": "View AWS Group",
    "VIEW_AZURE_TENANT": "View Azure Tenant",
    "VIEW_BRANCH_PROVISIONING_TEMPLATE": "View Branch Connector Provisioning Template",
    "VIEW_CLOUD_CONNECTOR_ADMIN": "View Cloud Connector Admin",
    "VIEW_CLOUD_CONNECTOR_ROLE": "View Cloud Connector Role",
    "VIEW_CLOUD_CONNECTORS": "View Connectors",
    "VIEW_CLOUD_NSS_FEED": "View Cloud NSS Feed",
    "VIEW_CLOUD_PROVIDER_AWS": "View AWS Cloud Account",
    "VIEW_CLOUD_PROVIDER_AZURE": "View Azure Cloud Account",
    "VIEW_CONNECTORS": "View Connectors",
    "VIEW_DESTINATION_IP_GROUP": "View Destination IP Group",
    "VIEW_DNS_GATEWAYS": "View DNS Gateway",
    "VIEW_DNS_POLICIES": "View DNS Filtering Rule",
    "VIEW_DYNAMIC_VDI_GROUP": "View Dynamic VDI Group",
    "VIEW_GATEWAYS": "View Gateways",
    "VIEW_INFO": "View info",
    "VIEW_IP_POOL_GROUP": "View IP Pool",
    "VIEW_LOCATION_TEMPLATE": "View Location Template",
    "VIEW_LOCATIONS": "View Locations",
    "VIEW_LOG_AND_CONTROL_FORWARDING_RULE": "View Log and Control Forwarding Rule",
    "VIEW_NETWORK_SERVICE_GROUP": "View Network Service Group",
    "VIEW_NETWORK_SERVICE": "View Network Service",
    "VIEW_NSS_FEEDS": "View NSS Feed",
    "VIEW_NSS_SERVER": "View NSS Server",
    "VIEW_ONLY_ACCESS": "View-Only Access",
    "VIEW_ONLY_ENABLED_UNTIL": "View-Only Access Enabled Until",
    "VIEW_ONLY": "View Only",
    "VIEW_PHYSICAL_BRANCH_DEVICE": "View Physical Branch Device",
    "VIEW_PROVISIONING_TEMPLATE": "View Cloud Connector Provisioning Template",
    "VIEW_SOURCE_IP_GROUP": "View Source IP Group",
    "VIEW_SUB_LOCATIONS": "View Sublocations",
    "VIEW_SUBLOCATIONS": "View Sublocations",
    "VIEW_TRAFFIC_FWD_POLICIES": "View Traffic Forwarding Rules",
    "VIEW_UPF": "View User Plane Function",
    "VIEW_VDI_AGENT_FORWARDING_PROFILE": "View VDI Forwarding Profile",
    "VIEW_VDI_TEMPLATE": "View VDI Template",
    "VIEW_VIRTUAL_BRANCH_DEVICE": "View Virtual Branch Device",
    "VIEW_ZERO_TRUST_GATEWAY": "View Zero Trust Gateway",
    "VIEW_ZT_DEVICE": "View ZT Device",
    "VIEW": "View",
    "VIOLENCE": "Violence",
    "VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA": "America/Tortola",
    "VIRGIN_ISLANDS_BRITISH": "Virgin Islands (British)",
    "VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS": "America/St. Thomas",
    "VIRGIN_ISLANDS_US": "Virgin Islands (U.S.)",
    "VIRTUAL_IP_ADDRESS": "Virtual IP Address",
    "VIRTUAL_IP_AND_LAN_IP_MUST_BE_DIFFERENT": "Virtual IP and LAN IP address must be different.",
    "VIRTUAL_IP_AND_PEER_DHCP_MUST_BE_DIFFERENT": "Virtual IP and Peer DHCP address must be different.",
    "VIRTUAL_SERVICE_EDGE_ID": "Virtual Service Edge ID",
    "VIRTUAL": "Virtual",
    "VISIBLE": "Visible",
    "VLAN_ID": "VLAN ID",
    "VM_GBL_METRICS": "VM",
    "VM_HEALTH_FETCH_API_ERROR": "Unable to fetch VM Health at the moment. Please try again later.",
    "VM_ID": "VM ID",
    "VM_NAME": "VM Name",
    "VM_SIZE": "VM Size",
    "VMWARE_ESXI": "VMware ESXi",
    "VMWARE": "VMware",
    "VNC_DESC": "Virtual Network Computing",
    "VNC": "VNC",
    "VNET_ID": "VNET ID",
    "VPC_ENDPOINT": "VPC Endpoint",
    "VPC_ID": "VPC ID",
    "VPC_NAME": "VPC Name",
    "VPC_VNET_NAME": "VPC/VNET Name",
    "VPN_CREDENTIAL_DROPDOWN": "VPN Credential Dropdown",
    "VPN_CREDENTIAL": "VPN Credential",
    "VPN_CREDENTIALS": "VPN Credentials",
    "VSE_CLUSTERS": "Virtual Service Edge Clusters",
    "VSE_NODES": "Virtual Service Edges",
    "WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS": "Pacific/Wallis",
    "WALLIS_AND_FUTUNA_ISLANDS": "Wallis and Futuna Islands",
    "WALLIS_AND_FUTUNA": "Wallis and Futuna",
    "WAN_DESTINATIONS_GROUP": "WAN Destinations Group",
    "WAN_NEED_AT_LEAST_ONE_ACTIVE_INTERFACE": "WAN need at least one active interface.",
    "WAN_PRI_DNS": "WAN Primary DNS Server",
    "WAN_SEC_DNS": "WAN Secondary DNS Server",
    "WAN_SELECTION": "WAN Selection",
    "WAN": "WAN",
    "WEAPONS_AND_BOMBS": "Weapons/Bombs",
    "WEB_BANNERS": "Advertising",
    "WEB_CONFERENCING": "Web Conferencing",
    "WEB_HOST": "Web Host",
    "WEB_SEARCH": "Web Search",
    "WEB_SPAM": "Web Spam",
    "WEDNESDAY": "Wednesday",
    "WESTCENTRALUS": "(US) West Central US",
    "WESTERN_SAHARA_AFRICA_EL_AAIUN": "Africa/El Aaiun",
    "WESTERN_SAHARA": "Western Sahara",
    "WESTEUROPE": "(Europe) West Europe",
    "WESTINDIA": "(Asia Pacific) West India",
    "WESTUS": "(US) West US",
    "WESTUS2": "(US) West US 2",
    "WESTUS2STAGE": "(US) West US 2 (Stage)",
    "WESTUS3": "(US) West US 3",
    "WESTUSSTAGE": "(US) West US (Stage)",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_PLACEHOLDER": "Enter Client/Application ID",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_TOOLTIP": "Enter the client/application ID.",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS": "Client/Application ID",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_PLACEHOLDER": "Enter Client/Application Secret",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_TOOLTIP": "Enter the client/application secret.",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET": "Client/Application Secret",
    "WHAT_DO_YOU_CALL_THIS_TENANT_PLACEHOLDER": "Enter Name For Azure Account",
    "WHAT_DO_YOU_CALL_THIS_TENANT_TOOLTIP": "Enter a name that is used to manage your accounts.",
    "WHAT_DO_YOU_CALL_THIS_TENANT": "Account Name",
    "WHAT_DO_YOU_WANT_TO_CALL_THIS_ACCOUNT": "What do you want to call this account?",
    "WHAT_IS_THE_DIRECTORY_ID_PLACEHOLDER": "Enter Directory ID",
    "WHAT_IS_THE_DIRECTORY_ID_TOOLTIP": "Enter the Directory (tenant) ID associated with the Azure service principal used to onboard the Azure account.",
    "WHAT_IS_THE_DIRECTORY_ID": "Directory (tenant) ID",
    "WHITESPACES_ARE_NOT_ALLOWED": "Whitespaces Are Not Allowed!",
    "WHOIS_DESC": "Network Directory Service Protocol",
    "WHOIS": "WHOIS",
    "WINDOWS_OS": "Windows OS",
    "WORKLOAD_SERVICE_REQUIRED_MESSAGE": "Partner integrations require a subscription that your organization does not currently have. Contact Zscaler Support to enable the basic plan at no additional cost. Partner integrations enable a service to run and be maintained by Zscaler in the public cloud, attached to your account or subscription. Zscaler will assign your account to a service based on our capacity.",
    "WORKLOAD_SERVICE_REQUIRED": "Workload Service needs to be configured",
    "WORKLOAD": "Workload Traffic Type",
    "WORKLOADS": "Workloads",
    "XSS": "Cross-site Scripting",
    "YEMEN_ASIA_ADEN": "Asia/Aden",
    "YEMEN": "Yemen",
    "YES": "Yes",
    "YESKY_DESC": " This protocol plug-in classifies the http traffic to the host yesky.com",
    "YESKY": "Yesky",
    "YIHAODIAN_DESC": " Chinese Online shopping",
    "YIHAODIAN": "Yihaodian",
    "YMAIL_CLASSIC_DESC": " Yahoo Mail Classic was the original interface for Yahoo! Mail",
    "YMAIL_CLASSIC": "Yahoo Mail Classic",
    "YMAIL_MOBILE_DESC": " (Deprecated) Yahoo Mail (Mobile) is the yahoo.com webmail adapted to mobiles",
    "YMAIL_MOBILE_NEW_DESC": " Yahoo Mail (Mobile) is the new yahoo.com webmail adapted to mobiles",
    "YMAIL_MOBILE_NEW": "Yahoo Mail (Mobile)",
    "YMAIL_MOBILE": "Ymail (Mobile)",
    "YMAIL2_DESC": " This protocol is the ajax based version of Webmail Yahoo",
    "YMAIL2": "Ymail2",
    "YMSG_CONF_DESC": " This protocol is used in signaling part in a conference",
    "YMSG_CONF": "Yahoo Messenger Conference",
    "YMSG_DESC": " Yahoo Messenger is used by the Yahoo Instant Messenger application to send instant messages, files and emails between users",
    "YMSG_TRANSFER_DESC": " This protocol is used for file tranfers over ymsg",
    "YMSG_TRANSFER": "Yahoo Messenger File Transfer",
    "YMSG_VIDEO_DESC": " (versions prior to 10.0.0.270) This protocol is used by Yahoo Messenger for video conversations",
    "YMSG_VIDEO": "Yahoo Messenger Video",
    "YMSG_WEBMESSENGER_DESC": " Yahoo Messenger for the Web",
    "YMSG_WEBMESSENGER": "Yahoo Messenger for the Web",
    "YMSG": "Yahoo Messenger",
    "YOU_DO_NOT_HAVE_THE_NECESSARY_PERMISSION": "You don't have necessary permissions to view this page",
    "YOUR_ACCOUNT_INFORMATION_WAS_SAVED_BUT_SOME_REGIONS_FAILED": "Your account information was saved, but the following regions failed to save:",
    "ZAMBIA_AFRICA_LUSAKA": "Africa/Lusaka",
    "ZAMBIA": "Zambia",
    "ZDX_UI": "ZDX UI",
    "ZERO_TRUST_GATEWAY": "Zero Trust Gateway",
    "ZIA_GATEWAY": "ZIA Gateway",
    "ZIA_GW_AUTH_FAIL": "Failed to authenticate with ZIA gateway.",
    "ZIA_GW_CONN_SETUP_FAIL": "ZIA gateway connection setup failed (Internal error).",
    "ZIA_GW_CONNECT_FAIL": "ZIA gateway connection failed (Network error).",
    "ZIA_GW_CTL_CONN_CLOSE": "ZIA gateway active control connection closed.",
    "ZIA_GW_CTL_KA_FAIL": "ZIA gateway control connection keepalive failed.",
    "ZIA_GW_DATA_CONN_CLOSE": "ZIA gateway active data connection closed.",
    "ZIA_GW_DATA_KA_FAIL": "ZIA gateway data connection keepalive failed.",
    "ZIA_GW_DNS_RESOLVE_FAIL": "ZIA gateway DNS resolution failed.",
    "ZIA_GW_PAC_RESOLVE_FAIL": "ZIA gateway PAC resolution failed.",
    "ZIA_GW_PAC_RESOLVE_NOIP": "ZIA gateway PAC resolution returned no IPS.",
    "ZIA_GW_PROTO_MSG_ERROR": "Message format error in ZIA GW control/data channel.",
    "ZIA_GW_PROTO_VER_ERROR": "ZIA protocol version mismatch.",
    "ZIA_GW_SSL_ERROR": "SSL error in ZIA GW control/data channel.",
    "ZIA_GW_UNHEALTHY": "ZIA gateway is unhealthy (Transient state).",
    "ZIA_THROUGHPUT_KBPS_SESSION": "ZIA (Throughput kbps / Session)",
    "ZIA_TUNNEL_MODEL": "ZIA Tunnel Mode",
    "ZIA_TUNNEL": "ZIA Tunnel",
    "ZIA": "ZIA",
    "ZIMBABWE_AFRICA_HARARE": "Africa/Harare",
    "ZIMBABWE": "Zimbabwe",
    "ZONE": "Zone",
    "ZPA_BROKER": "ZPA Broker",
    "ZPA_EDGE_APP_SEGMENT": "ZPA Edge App Segment",
    "ZPA_IP_POOL": "ZPA IP Pool",
    "ZPA_POLICY_VIOLATION_INDICATOR": "ZPA Policy Violation Indicator",
    "ZPA_THROUGHPUT_KBPS_SESSION": "ZPA (Throughput kbps / Session)",
    "ZPA_TUNNEL": "ZPA Tunnel",
    "ZPA": "ZPA",
    "ZS_TAG_OPTIONAL": "ZS Tag (Optional)",
    "ZSCALER_ANALYZER": "Zscaler Analyzer",
    "ZSCALER_CLOUD_ENDPOINTS": "Zscaler Cloud Endpoints",
    "ZSCALER_DOMAINS": "Zscaler Domains",
    "ZSCALER_ESTABLISH_SUPPORT_TUNNEL": "Zscaler Initiated On-Demand Support Tunnel",
    "ZSCALER_GATEWAY_DETAILS": "Zscaler Gateway Details",
    "ZSCALER_HELP_PORTAL": "Zscaler Help Portal",
    "ZSCALER_INC_ALL_RIGHTS_RESERVED": "Zscaler Inc. All rights reserved.",
    "ZSCALER_INTERFACE_NAME": "Zscaler Interface Name",
    "ZSCALER_IP": "Zscaler IP",
    "ZSCALER_IPS": "Zscaler IPs",
    "ZSCALER_PROXY_NW_SERVICES_DESC": "This network service includes all Zscaler specific web proxy ports including customer specific DPPC ports.",
    "ZSCALER_PROXY_NW_SERVICES": "Zscaler Proxy Network Services",
    "ZSCALER_SERVICE_ACCOUNT_EMAIL_PLACEHOLDER": "<EMAIL><EMAIL>-12345-impersonator@cc-test-5678",
    "ZSCALER_SERVICE_ACCOUNT_EMAIL_TOOLTIP": "",
    "ZSCALER_SERVICE_ACCOUNT_EMAIL": "Zscaler Service Account Email",
    "ZSLOGIN_ADMINISTRATION": "ZIdentity Administration",
    "ZSPROXY_IPS": "Zscaler Proxy IPs",
    "ZT_DEVICES": "ZT Devices",
    "ZT_GATEWAY": "Zero Trust Gateway",
    "ZTG_ACCOUNT_TEXT": "The gateway accepts incoming endpoint requests from the list of accounts entered. AWS accounts and account groups onboarded on the Partner Integrations page can be selected. For accounts not onboarded using the Partner Integrations page, enter the 12 digit AWS account ID manually. To learn more about Partner Integrations, refer to the {0}Cloud Connector Partner Integrations documentation{1}.",
    "ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP": "If your AWS account has not been onboarded on the Partner Integrations page, enter your 12 digit AWS account ID. The gateway accepts the requests originated by endpoints from the same region as the gateway.",
    "ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP": "Select the AWS account groups that are allowed to connect to this gateway. The gateway accepts the requests originated by endpoints from the same region as the gateway. Account groups are created on the Partner Integrations page.",
    "ZTG_ALLOWED_ACCOUNTS_TOOLTIP": "Select the AWS accounts that are allowed to connect to this gateway. The gateway accepts the requests originated by endpoints from the same region as the gateway. Accounts are added on the Partner Integrations page.",
    "ZTG_AVIABILITY_ZONE_TOOLTIP": "Select the Availability Zone(s) where the gateway components are created. A minimum of two Availability Zones must be chosen. Note that these are the AWS Availability Zone IDs and not the names shown in the AWS account. The IDs are displayed because the same Availability Zone name can map to different Availability Zone IDs in different AWS accounts. To learn more about Availability Zone names to ID mapping and finding the Availability Zone ID for your account, refer to the {0}AWS documentation{1}.",
    "ZTG_CONFIGURATION_TEXT": "Enter the configuration for this gateway. This configuration drives the AWS region and availability zones where the service is available.",
    "ZTG_ID": "Zero Trust Gateway ID",
    "ZTG_LOCATION_TEMPLATE_TOOLTIP": "Select the location template used for creating the location associated with the gateway.",
    "ZTG_LOCATION_TOOLTIP": "Enter the name of the location to be associated with this gateway. This name is visible in all the policies where a location object is available.",
    "ZTG_NAME_TOOLTIP": "Enter a name for your gateway. This name is associated with the gateway.",
    "ZTG_NAME": "Zero Trust Gateway Name",
    "ZTG_REGION_TOOLTIP": "Select the region where the gateway is created. Gateways are regional and can only be deployed in one region. The region cannot be modified after the gateway is created.",
    "ZTG_REVIEW_TEXT": "Ensure all of the information is correct before creating the gateway. Once the gateway is created, deployment of the components can take a few minutes. Visit the Zero Trust Gateway page to view the status of the gateway.",
    "ZTGW_GROUP": "Zero Trust Gateway Group",
    "ZTGW_VM": "Zero Trust Gateway VM",
    "ZULU": "Zulu",
  },
};
