/* eslint-disable quotes */
/* eslint-disable quote-props */

// Notes: We can add phrases as a key, it does not have to be a key/constant.
// However, use key and constant if the phrase is a long sentence or paragraph.

// Please keep the translations below sorted for easy access and to avoid duplicates

// WIP - will keep this changes until we complete the csv2json conversion

export default {
  translation: {
    "1_HOUR": "1 Stunde",
    "1_MONTH": "1 Monat",
    "1_WEEK": "1 Woche",
    "24_HOURS": "24 Stunden",
    "4_HOURS": "4 Stunden",
    "ACCEPT": "Akzeptieren",
    "ACCEPTED_ON": "Akzeptiert am",
    "ACCESS_TOKEN": "Zugriffstoken",
    "ACCOUNT_GROUP": "Kontogruppe",
    "ACCOUNT_ID_ONLY": "Konto-ID",
    "ACCOUNT_ID": "AWS-Konto-ID",
    "ACCOUNT_LIST": "Kontenliste",
    "ACCOUNT_NAME": "Kontoname",
    "ACCOUNT": "Konto",
    "ACCOUNTS": "Konten",
    "ACTION_CAN_NOT_BE_UNDONE": "This action cannot be undone.",
    "ACTION_CAPS": "Aktion",
    "ACTION_INTERFACE": "Schnittstelle",
    "ACTION_RESULT": "Ergebnis",
    "ACTION_TYPE": "Aktion",
    "ACTION": "Aktion",
    "ACTIONS": "Aktionen",
    "ACTIVATE": "Aktivieren",
    "ACTIVATION_FAILED": "Aktivierung fehlgeschlagen!",
    "ACTIVATION": "Aktivierung",
    "ACTIVE_ACTIVE": "Aktiv-Aktiv",
    "ACTIVE_CONNECTION": "Aktive Verbindung",
    "ACTIVE_STANDBY": "Aktiv-Standby",
    "ACTIVE_STATUS": "Aktiver Status",
    "ACTIVE": "Aktiv",
    "ADD_5G_DEPLOYMENT": "Deployment-Konfiguration hinzufügen",
    "ADD_ACCOUNT": "Konto hinzufügen",
    "ADD_API_KEY": "Cloud-Dienst-API-Schlüssel hinzufügen",
    "ADD_AWS_ACCOUNT": "AWS-Konto hinzufügen",
    "ADD_AWS_CLOUD_ACCOUNT": "AWS Cloud-Konto hinzufügen",
    "ADD_AWS_GROUP": "AWS-Gruppe hinzufügen",
    "ADD_AZURE_ACCOUNT": "Azure-Konto hinzufügen",
    "ADD_AZURE_CLOUD_ACCOUNT": "Azure Cloud-Konto hinzufügen",
    "ADD_BC_PROVISIONING_TEMPLATE": "Branch Connector-Konfigurationsvorlage hinzufügen",
    "ADD_BRANCH_CONNECTOR_PROV_TEMPLATE": "Branch Connector-Konfigurationsvorlage hinzufügen",
    "ADD_CLOUD_APP_PROVIDER": "Cloud-App-Anbieter hinzufügen",
    "ADD_CLOUD_CONNECTOR_ADMIN": "Administrator hinzufügen",
    "ADD_CLOUD_CONNECTOR_ROLE": "Administratorrolle hinzufügen",
    "ADD_CLOUD_CONNECTOR": "Cloud Connector hinzufügen",
    "ADD_CLOUD_NSS_FEED": "Cloud NSS-Feed hinzufügen",
    "ADD_CLOUD_PROVISIONING_TEMPLATE": "Cloud Connector-Bereitstellungsvorlage hinzufügen",
    "ADD_CRITERIA": "Kriterien hinzufügen",
    "ADD_DEPLOYMENT_CONFIGURATION": "Deployment-Konfiguration hinzufügen",
    "ADD_DESTINATION_IP_GROUP": "Ziel-IP-Gruppe hinzufügen",
    "ADD_DNS_GATEWAY": "DNS-Gateway hinzufügen",
    "ADD_DNS_POLICIES": "DNS-Filterregel hinzufügen",
    "ADD_DYNAMIC_VDI_GROUP": "Dynamische VDI-Gruppe hinzufügen",
    "ADD_EC_NSS_CLOUD_FEED": "Cloud NSS-Feed hinzufügen",
    "ADD_EC_NSS_FEED": "NSS-Feed hinzufügen",
    "ADD_EC_NSS_SERVER": "NSS-Server hinzufügen",
    "ADD_EVENT_GRID": "Ereignisraster hinzufügen (optional)",
    "ADD_FILTER": "Filter hinzufügen",
    "ADD_FILTERS": "Filter hinzufügen",
    "ADD_GROUP": "Gruppe hinzufügen",
    "ADD_HTTP_HEADER": "HTTP-Header hinzufügen",
    "ADD_INTERFACE": "Schnittstelle hinzufügen",
    "ADD_IP_INFO": "IP-Informationen hinzufügen",
    "ADD_IP_POOL": "IP-Pool hinzufügen",
    "ADD_ITEMS": "Elemente hinzufügen",
    "ADD_LOCATION_AND_CCG": "Standort und Cloud Connectors hinzufügen",
    "ADD_LOCATION_TEMPLATE": "Standortvorlage hinzufügen",
    "ADD_LOG_AND_CONTROL_FORWARDING_RULE": "Protokollierungs- und Steuerungsregel für Weiterleitung hinzufügen",
    "ADD_LOG_AND_CONTROL_FORWARDING": "Protokoll- und Steuerungsweiterleitung hinzufügen",
    "ADD_LOG_AND_CONTROL_GATEWAY": "Protokollierungs- und Steuerungs-Gateway hinzufügen",
    "ADD_MORE": "Mehr hinzufügen",
    "ADD_NETWORK_SERVICE_GROUP": "Netzwerkdienstgruppe hinzufügen",
    "ADD_NETWORK_SERVICE": "Netzwerkdienst hinzufügen",
    "ADD_NEW_GATEWAY": "Neues Gateway hinzufügen",
    "ADD_NEW": "Neu hinzufügen",
    "ADD_NSS_FEED": "NSS-Feed hinzufügen",
    "ADD_NSS_SERVER": "NSS-Server hinzufügen",
    "ADD_PORT": "Port hinzufügen",
    "ADD_PROVISIONING_TEMPLATE": "Cloud Connector-Bereitstellungsvorlage hinzufügen",
    "ADD_SOURCE_IP_GROUP": "Quell-IP-Gruppe hinzufügen",
    "ADD_STORAGE_ACCOUNT": "Speicherkonto hinzufügen",
    "ADD_SUB_INTERFACE": "Sub-Schnittstelle hinzufügen",
    "ADD_TENANT": "Instanz hinzufügen",
    "ADD_TO_A_LOCATION": "Zu einem Standort hinzufügen",
    "ADD_TO_AN_EXISTING_GROUP": "Zu einer bestehenden Gruppe hinzufügen",
    "ADD_TRAFFIC_FORWARDING_RULE": "Verkehrsweiterleitungsregel hinzufügen",
    "ADD_TRAFFIC_FORWARDING": "Verkehrsweiterleitung hinzufügen",
    "ADD_TRAFFIC_FWD_POLICIES": "Verkehrsweiterleitungsregeln hinzufügen",
    "ADD_UPF": "Benutzerebenenfunktion hinzufügen",
    "ADD_VDI_AGENT_FORWARDING_PROFILE": "VDI-Weiterleitungsprofil hinzufügen",
    "ADD_VDI_TEMPLATE": "VDI-Vorlage hinzufügen",
    "ADD_ZERO_TRUST_GATEWAY": "Zero-Trust-Gateway hinzufügen",
    "ADD_ZIA_GATEWAY": "ZIA Gateway hinzufügen",
    "ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128": "Das Limit für zusätzliche AWS-Konten beträgt 128.",
    "ADDITIONAL_AWS_ACCOUNTS": "Zusätzliche AWS-Konten",
    "ADDRESS_RANGES_SHOULD_NOT_OVERLAP": "Adressbereiche dürfen sich nicht überschneiden.",
    "ADM_ACTIVATING": "Aktivierung läuft",
    "ADM_ACTV_DONE": "Administratoraktivierung abgeschlossen",
    "ADM_ACTV_FAIL": "Administratoraktivierung fehlgeschlagen",
    "ADM_ACTV_QUEUED": "Aktivierung in Warteschlange gestellt",
    "ADM_EDITING": "Bearbeitung",
    "ADM_EXPIRED": "Admin-Sitzung abgelaufen",
    "ADM_LOGGED_IN": "Administrator angemeldet",
    "ADMIN_ID": "Admin-ID",
    "ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE": "Dieser Administrator existiert auch im Admin-Portal für einen anderen Dienst. Er wird mit dem gleichen Administratorkonto im anderen Admin-Portal verknüpft und der Zscaler-Dienst aktualisiert jegliche Änderungen, die am anderen Administratorkonto vorgenommen werden, wie z. B. E-Mail-Adresse, Name, Bereich, Passwort und Kommentare. Fortfahren?",
    "ADMIN_MANAGEMENT": "Administratorverwaltung",
    "ADMIN_ROLE": "Administratorrolle",
    "ADMIN_SAML_PUBLICCERT_INVALID_EXTENSION": "Öffentliches SAML Zertifikat sollte vom Format .cer oder .pem sein.",
    "ADMINISTRATION_CONFIGURATION": "Verwaltungskonfiguration",
    "ADMINISTRATION_CONTROL": "Verwaltungs-Steuerelement",
    "ADMINISTRATION": "Administration",
    "ADMINISTRATOR_ADMIN_USER": "Administrator",
    "ADMINISTRATOR_AUDITOR": "Auditor",
    "ADMINISTRATOR_MANAGEMENT": "Administratorverwaltung",
    "ADMINISTRATOR_PASSWORD_BASED_LOGIN": "Passwortbasierte Anmeldung",
    "ADMINISTRATOR_ROLE": "Rollen Management",
    "ADMINISTRATOR_SAML_CONFIGURE": "SAML-Authentifizierung für Administratoren",
    "ADMINISTRATOR_SAML_ENABLED": "SAML-Authentifizierung aktivieren",
    "ADMINISTRATOR_SAML_METADATA": "XML-Metadaten herunterladen",
    "ADMINISTRATOR_SAML": "SAML",
    "ADMINISTRATORS_MANAGEMENT": "Administratorverwaltung",
    "ADMINISTRATORS": "Administratoren",
    "ADSPYWARE_SITES": "Adware/Spyware-Seiten",
    "ADULT_SEX_EDUCATION": "Sexualkunde für Erwachsene",
    "ADULT_THEMES": "Themen für Erwachsene",
    "ADVANCED_SETTINGS": "Erweiterte Einstellungen",
    "ADWARE_OR_SPYWARE": "Spyware/Adware",
    "AF_SOUTH_1": "Afrika (Kapstadt)",
    "AF_SOUTH_1A": "af-south-1a",
    "AF_SOUTH_1B": "af-south-1b",
    "AF_SOUTH_1C": "af-south-1c",
    "AFGHANISTAN_ASIA_KABUL": "Asien/Kabul",
    "AFGHANISTAN": "Afghanistan",
    "AGENT_STATUS": "Agentenstatus",
    "AGGREGATE_LOGS": "Protokolle aggregieren",
    "AIAIGAME_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic an den Host aiaigame.com ",
    "AIAIGAME": "AiAi Spiele",
    "AILI_DESC": " Chinesische Mode-Shopping-Website",
    "AILI": "Aili",
    "AIM_DESC": " AIM (ursprünglich AOL Instant Messenger) ist eine Instant-Messaging-Applikation. Der Protokollname ist OSCAR (Open System for CommunicAtion in Realtime) und wird sowohl für ICQ und AIM-Dienste verwendet.",
    "AIM_EXPRESS_DESC": " AOL Instant Messaging Express unterstützt viele der Standard-Features, die in AIM enthalten sind, liefert aber keine erweiterten Funktionen wie Dateiübertragung, Audio-Chat oder Videokonferenz",
    "AIM_EXPRESS": "AIM Express",
    "AIM_TRANSFER_DESC": " AIM ist ein Instant-Messaging-Protokoll",
    "AIM_TRANSFER": "AIM File Transfer",
    "AIM": "AIM",
    "AIMEXPRESS": "AIM Express",
    "AIMINI_DESC": " Aimini ist eine Online-Lösung um die Dateien zu speichern, senden und teilen",
    "AIMINI": "Aimini",
    "AIMS_DESC": " AIMS ist eine sichere Version von AIM",
    "AIMS": "AIMS",
    "AIOWRITE_THROTTLE": "aiowrite drosseln. Wird heute nur von ca-ft genützt.",
    "AIRAIM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic an den Host airaim.com. Es klassifiziert auch den SSL-Traffic zu dem Common Name airaim.com ",
    "AIRAIM": "Airaim",
    "ALAND_ISLANDS_EUROPE_MARIEHAMN": "Europa / Mariehamn",
    "ALAND_ISLANDS": "Aland Inseln",
    "ALAND": "Åland",
    "ALBANIA_EUROPE_TIRANE": "Europa / Bahn",
    "ALBANIA": "Albanien",
    "ALCOHOL_TOBACCO": "Alkohol/Tabak",
    "ALGERIA_AFRICA_ALGIERS": "Afrika / Algier",
    "ALGERIA": "Algerien",
    "ALL_VALUES": "Alle Werte",
    "ALL_ZSCALER_LOCATION_GROUPS": "Alle Zscaler-Standortgruppen",
    "ALL_ZSCALER_LOCATION_TYPES": "Alle Zscaler-Standorttypen",
    "ALL_ZSCALER_LOCATIONS": "Alle Zscaler-Standorte",
    "ALL_ZSCALER_NETWORK_SERVICE": "Alle Zscaler-Netzwerkdienste",
    "ALL": "Alle",
    "ALLOW_TO_CREATE_NEW_LOCATION": "Erstellung neuer Standorte ermöglichen",
    "ALLOW": "Zulassen",
    "ALLOWED_ACCOUNT_GROUPS": "Zulässige Kontogruppen",
    "ALLOWED_ACCOUNTS_GROUPS": "Zulässige Kontengruppen",
    "ALLOWED_ACCOUNTS": "Zulässige Konten",
    "ALLOWED": "Erlaubt",
    "ALT_NEW_AGE": "Alt/New Age",
    "ALTERNATE_LIFESTYLE": "Lebensstil",
    "AMAZON_WEB_SERVICES_CONSOLE": "Amazon Web Services-Konsole",
    "AMAZON_WEB_SERVICES": "Amazon Web Services",
    "AMERICAN_SAMOA_PACIFIC_PAGO_PAGO": "Pacific/PagoPago",
    "AMERICAN_SAMOA": "American Samoa",
    "AMF_IP_CIDR": "AMF-IP/CIDR",
    "AMF_NAME": "AMF-Name",
    "AMI_ID": "AMI-ID",
    "ANALYTICS": "Analytik",
    "ANDORRA_EUROPE_ANDORRA": "Europa / Andorre",
    "ANDORRA": "Andorra",
    "ANDROID_OS": "Android",
    "ANGOLA_AFRICA_LUANDA": "Afrika / Luanda",
    "ANGOLA": "Angola",
    "ANGUILLA_AMERICA_ANGUILLA": "Amerika/Anguilla",
    "ANGUILLA": "Anguilla",
    "ANONYMIZER": "P2P und Anonymizer",
    "ANTARCTICA_CASEY": "Antarktis/Casey",
    "ANTARCTICA_DAVIS": "Antarktis/Davis",
    "ANTARCTICA_DUMONTDURVILLE": "Antarktis/DumontDUrville",
    "ANTARCTICA_MAWSON": "Antarktis/Mawson",
    "ANTARCTICA_MCMURDO": "Antarktis/McMurdo",
    "ANTARCTICA_PALMER": "Antarktis/Palmer",
    "ANTARCTICA_ROTHERA": "Antarktis/Rothera",
    "ANTARCTICA_SOUTH_POLE": "Antarktis/South Pole",
    "ANTARCTICA_SYOWA": "Antarktis/Syowa",
    "ANTARCTICA_VOSTOK": "Antarktis/Vostok",
    "ANTARCTICA": "Antarktis",
    "ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA": "Amerika/Antigua",
    "ANTIGUA_AND_BARBUDA": "Antigua und Barbuda",
    "ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS": "Alle nicht zugeordneten IPs aus ZPA-IP-Pools",
    "ANY_RULE": "Alle",
    "ANY": "Alle",
    "AP_EAST_1": "Asien-Pazifikraum (Hongkong)",
    "AP_EAST_1A": "ap-east-1a",
    "AP_EAST_1B": "ap-east-1b",
    "AP_EAST_1C": "ap-east-1c",
    "AP_NORTHEAST_1": "ap-northeast-1 (Tokio)",
    "AP_NORTHEAST_1A": "ap-northeast-1a",
    "AP_NORTHEAST_1C": "ap-northeast-1c",
    "AP_NORTHEAST_1D": "ap-northeast-1d",
    "AP_NORTHEAST_1E": "ap-northeast-1e",
    "AP_NORTHEAST_2": "ap-northeast-2 (Seoul)",
    "AP_NORTHEAST_2A": "ap-northeast-2a",
    "AP_NORTHEAST_2B": "ap-northeast-2b",
    "AP_NORTHEAST_2C": "ap-northeast-2c",
    "AP_NORTHEAST_3": "ap-northeast-3 (Osaka-Lokal)",
    "AP_NORTHEAST_3A": "ap-northeast-3a",
    "AP_SOUTH_1": "ap-south-1 (Mumbai)",
    "AP_SOUTH_1A": "ap-south-1a",
    "AP_SOUTH_1B": "ap-south-1b",
    "AP_SOUTH_1C": "ap-south-1c",
    "AP_SOUTH_2": "Asien-Pazifikraum (Hyderabad)",
    "AP_SOUTHEAST_1": "ap-southeast-1 (Singapur)",
    "AP_SOUTHEAST_1A": "ap-southeast-1a",
    "AP_SOUTHEAST_1B": "ap-southeast-1b",
    "AP_SOUTHEAST_1C": "ap-southeast-1c",
    "AP_SOUTHEAST_2": "ap-southeast-2 (Sydney)",
    "AP_SOUTHEAST_2A": "ap-southeast-2a",
    "AP_SOUTHEAST_2B": "ap-southeast-2b",
    "AP_SOUTHEAST_2C": "ap-southeast-2c",
    "AP_SOUTHEAST_3": "ap-southeast-3 (Jakarta)",
    "AP_SOUTHEAST_4": "Asien-Pazifikraum (Melbourne)",
    "API_KEY_MANAGEMENT": "API-Schlüsselverwaltung",
    "API_KEY": "API-Schlüssel",
    "APIKEY_MANAGEMENT": "API-Schlüsselverwaltung",
    "APP_CONNECTOR_DEPLOYMENT_STATUS": "App Connector-Bereitstellungsstatus",
    "APP_CONNECTOR_DESCRIPTION": "App Connectors können als Teil dieser Vorlage bereitgestellt werden. Geben Sie die folgenden Informationen ein oder fahren Sie mit dem nächsten Schritt fort.",
    "APP_CONNECTOR_GROUP_NAME": "App Connector-Gruppenname",
    "APP_CONNECTOR_GROUP_TYPE": "App Connector-Gruppentyp",
    "APP_CONNECTOR_GROUP": "App Connector-Gruppe",
    "APP_CONNECTOR_INTERFACE": "App Connector-Schnittstelle",
    "APP_CONNECTOR": "App Connector",
    "APPLIANCE_MANAGEMENT": "Appliance-Verwaltung",
    "APPLIANCE_NAME": "Appliance-Name",
    "APPLIANCE": "Appliance",
    "APPLIANCES": "Appliances",
    "APPLICABLE_FOR": "Anwendbar für",
    "APPLICATION_ID": "Anwendungs-ID",
    "APPLICATION_KEY": "Anwendungsschlüssel",
    "APPLICATION_SEGMENT": "Anwendungssegment",
    "APPLICATION_SEGMENTS": "Application Segments",
    "APPLICATION_SERVICE_GROUPS": "Anwendungsdienstgruppen",
    "APPLICATION_VERSION": "Anwendungsversion",
    "APPLICATIONS": "Applikationen",
    "APPLY_FILTER": "Filter anwenden",
    "APPLY_FILTERS": "Filter anwenden",
    "APPLY_TO_ALL_APP_SEGMENTS": "Auf alle App Segments anwenden",
    "APPLY": "Anwenden",
    "ARE_YOU_SURE_YOU_WANT_TO_PROCEED": "Wollen Sie wirklich fortfahren?",
    "ARE_YOU_SURE": "Wollen Sie wirklich fortfahren?",
    "ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES": "Amerika/Argentina/Buenos Aires",
    "ARGENTINA_AMERICA_ARGENTINA_CATAMARCA": "Amerika/Argentina/Catamarca",
    "ARGENTINA_AMERICA_ARGENTINA_CORDOBA": "Amerika/Argentina/Cordoba",
    "ARGENTINA_AMERICA_ARGENTINA_JUJUY": "Amerika/Argentina/Jujuy",
    "ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA": "Amerika/Argentina/La Rioja",
    "ARGENTINA_AMERICA_ARGENTINA_MENDOZA": "Amerika/Argentina/Mendoza",
    "ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS": "Amerika/Argentina/Rio Gallegos",
    "ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN": "Amerika/Argentina/San Juan",
    "ARGENTINA_AMERICA_ARGENTINA_TUCUMAN": "Amerika/Argentina/Tucuman",
    "ARGENTINA_AMERICA_ARGENTINA_USHUAIA": "Amerika/Argentina/Ushuaia",
    "ARGENTINA": "Argentinien",
    "ARMENIA_ASIA_YEREVAN": "Asien/Eriwan",
    "ARMENIA": "Armenien",
    "ART_CULTURE": "Kunst/Kultur",
    "ARUBA_AMERICA_ARUBA": "Amerika/Aruba",
    "ARUBA": "Aruba",
    "ASIA_EAST1_A": "asia-east1-a",
    "ASIA_EAST1_B": "asia-east1-b",
    "ASIA_EAST1_C": "asia-east1-c",
    "ASIA_EAST1": "asia-east1",
    "ASIA_EAST2_A": "asia-east2-a",
    "ASIA_EAST2_B": "asia-east2-b",
    "ASIA_EAST2_C": "asia-east2-c",
    "ASIA_EAST2": "asia-east2",
    "ASIA_NORTHEAST1_A": "asia-northeast1-a",
    "ASIA_NORTHEAST1_B": "asia-northeast1-b",
    "ASIA_NORTHEAST1_C": "asia-northeast1-c",
    "ASIA_NORTHEAST1": "asia-northeast1",
    "ASIA_NORTHEAST2_A": "asia-northeast2-a",
    "ASIA_NORTHEAST2_B": "asia-northeast2-b",
    "ASIA_NORTHEAST2_C": "asia-northeast2-c",
    "ASIA_NORTHEAST2": "asia-northeast2",
    "ASIA_NORTHEAST3_A": "asia-northeast3-a",
    "ASIA_NORTHEAST3_B": "asia-northeast3-b",
    "ASIA_NORTHEAST3_C": "asia-northeast3-c",
    "ASIA_NORTHEAST3": "asia-northeast3",
    "ASIA_SOUTH1_A": "asia-south1-a",
    "ASIA_SOUTH1_B": "asia-south1-b",
    "ASIA_SOUTH1_C": "asia-south1-c",
    "ASIA_SOUTH1": "asia-south1",
    "ASIA_SOUTH2_A": "asia-south2-a",
    "ASIA_SOUTH2_B": "asia-south2-b",
    "ASIA_SOUTH2_C": "asia-south2-c",
    "ASIA_SOUTH2": "asia-south2",
    "ASIA_SOUTHEAST1_A": "asia-southeast1-a",
    "ASIA_SOUTHEAST1_B": "asia-southeast1-b",
    "ASIA_SOUTHEAST1_C": "asia-southeast1-c",
    "ASIA_SOUTHEAST1": "asia-southeast1",
    "ASIA_SOUTHEAST2_A": "asia-southeast2-a",
    "ASIA_SOUTHEAST2_B": "asia-southeast2-b",
    "ASIA_SOUTHEAST2_C": "asia-southeast2-c",
    "ASIA_SOUTHEAST2": "asia-southeast2",
    "ASIA": "Asien",
    "ASIAPACIFIC": "Asien Pazifik",
    "AT": "auf",
    "ATTRIBUTES_APPLICABLE_ONLY_TO_KNOWN_HOPS": "Diese Attribute gelten nur für bekannte Hops.",
    "ATTRIBUTES": "Attribute",
    "AUDIT_LOGS": "Audit-Logs",
    "AUDIT_OPERATION": "Audit-Betrieb",
    "AUSTRALIA_ADELAIDE": "Australien/Adelaide",
    "AUSTRALIA_BRISBANE": "Australien/Brisbane",
    "AUSTRALIA_BROKEN_HILL": "Australien/Broken Hill",
    "AUSTRALIA_CURRIE": "Australien/Currie",
    "AUSTRALIA_DARWIN": "Australien/Darwin",
    "AUSTRALIA_EUCLA": "Australien/Eucla",
    "AUSTRALIA_HOBART": "Australien/Hobart",
    "AUSTRALIA_LINDEMAN": "Australien/Lindemann",
    "AUSTRALIA_LORD_HOWE": "Australien/LordHowe",
    "AUSTRALIA_MELBOURNE": "Australien/Melbourne",
    "AUSTRALIA_NEWZEALAND": "Australien und Neuseeland",
    "AUSTRALIA_PERTH": "Australien/Perth",
    "AUSTRALIA_SOUTHEAST1_A": "australia-southeast1-a",
    "AUSTRALIA_SOUTHEAST1_B": "australia-southeast1-b",
    "AUSTRALIA_SOUTHEAST1_C": "australia-southeast1-c",
    "AUSTRALIA_SOUTHEAST1": "australia-southeast1",
    "AUSTRALIA_SOUTHEAST2_A": "australia-southeast2-a",
    "AUSTRALIA_SOUTHEAST2_B": "australia-southeast2-b",
    "AUSTRALIA_SOUTHEAST2_C": "australia-southeast2-c",
    "AUSTRALIA_SOUTHEAST2": "australia-southeast2",
    "AUSTRALIA_SYDNEY": "Australien/Sydney",
    "AUSTRALIA": "Australien",
    "AUSTRALIACENTRAL": "Asien-Pazifikraum (Australien Mitte)",
    "AUSTRALIACENTRAL2": "Asien-Pazifikraum (Australien Mitte 2)",
    "AUSTRALIAEAST": "Asien-Pazifikraum (Australien Ost)",
    "AUSTRALIASOUTHEAST": "Asien-Pazifikraum (Australien Südost)",
    "AUSTRIA_EUROPE_VIENNA": "Europa / Wien",
    "AUSTRIA": "Österreich",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_AUP_ENABLED": "Erforderliche Authentifizierung muss deaktiviert werden, wenn AUP aktiviert ist.",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_CAUTION_ENABLED": "Erforderliche Authentifizierung muss deaktiviert werden, wenn Warnung aktiviert ist.",
    "AUTH_TYPE": "Authentifizierungstyp",
    "AUTHENTICATION_CONFIGURATION": "Konfiguration Authentifizierung",
    "AUTHENTICATION_FAILED": "Authentifizierung fehlgeschlagen",
    "AUTO_POPULATE_DNS_CACHE": "DNS-Cache automatisch eintragen",
    "AUTO_REFRESH_DASHBOARD": "Dashboard automatisch aktualisieren",
    "AUTO_SCALING_OPTIONS": "Optionen für automatische Skalierung",
    "AUTO_SCALING": "Automatische Skalierung",
    "AUTO": "Automatisch",
    "AUTOMATIC_MANAGEMENT_IP": "Management-IP automatisch",
    "AUTOMATIC_SERVICE_IP": "Service-IP automatisch",
    "AUTOMATIC": "Automatisch",
    "Availability Zone": "Verfügbarkeitszone",
    "AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES": "Die ID der Verfügbarkeitszone muss mindestens 2 Zonen umfassen.",
    "AVAILABILITY_ZONE_ID": "ID der Verfügbarkeitszone",
    "AVAILABILITY_ZONE": "Verfügbarkeitszone",
    "AVAILABILITY_ZONES": "Verfügbarkeitszonen",
    "AVAILABLE": "Verfügbar",
    "AVERAGE": "Durchschnitt",
    "AWS_ACCESS_KEY_ID": "AWS-Zugriffsschlüssel-ID",
    "AWS_ACCOUNT_ID": "AWS-Konto-ID",
    "AWS_ACCOUNT": "AWS-Konto",
    "AWS_AVAILABILITY_ZONE": "AWS-Verfügbarkeitszone",
    "AWS_CLOUD_FORMATION": "AWS CloudFormation",
    "AWS_CLOUD": "AWS",
    "AWS_GROUP": "AWS-Gruppe",
    "AWS_REGION": "AWS-Region",
    "AWS_REGIONS": "AWS-Regionen",
    "AWS_ROLE_NAME": "AWS-Rollenname",
    "AWS_SECRET_ACCESS_KEY": "AWS-Geheimschlüssel",
    "AWS": "Amazon Web Services",
    "AZERBAIJAN_ASIA_BAKU": "Asien/Baku",
    "AZERBAIJAN": "Aserbaidschan",
    "AZURE_ACCOUNT": "Azure-Konto",
    "AZURE_AVAILABILITY_ZONE": "Azure-Verfügbarkeitszone",
    "AZURE_CLOUD": "Azure",
    "AZURE_REGION": "Azure-Region",
    "AZURE_RESOURCES": "Azure-Ressourcen",
    "AZURE_SENTINEL": "Azure Sentinel",
    "AZURE": "Azure",
    "BACK": "Zurück",
    "BAHAMAS_AMERICA_NASSAU": "Amerika/Nassau",
    "BAHAMAS": "Bahamas",
    "BAHRAIN_ASIA_BAHRAIN": "Asien/Bahrain",
    "BAHRAIN": "Bahrein",
    "BAIDUYUNDNS_DESC": " DNS-Tunnelaktivität bei .baiduyundns.com erkannt",
    "BAIDUYUNDNS": "BaiduYunDns",
    "BALANCED_RULE": "Ausgewogen",
    "BALANCED": "Ausgewogen",
    "BANDWIDTH_CONTROL_ENABLED": "Unterstandorte teilen den Bandbreitenwert, der diesem Standort zugewiesen ist",
    "BANDWIDTH_CONTROL": "Bandbreitenübersicht",
    "BANGLADESH_ASIA_DHAKA": "Asien/Dhaka",
    "BANGLADESH": "Bangladesch",
    "BARBADOS_AMERICA_BARBADOS": "Amerika/Barbados",
    "BARBADOS": "Barbados",
    "BASE_URL": "Basis-URL",
    "BC_APP_CONNECTOR": "Branch Connector und App Connector",
    "BC_CONNECTOR_GROUP": "Cloud & Branch Connector-Gruppe",
    "BC_CONNECTOR_LOCATION": "Cloud & Branch Connector-Standort",
    "BC_CONNECTOR_VM": "Branch Connector-VM",
    "BC_CONNECTOR": "Branch Connector",
    "BC_DESCRIPTION": "Branch Connector-Beschreibung",
    "BC_DETAILS": "Branch Connector-Details",
    "BC_DEVICE_GROUP": "Branch Connector-Gerätegruppe",
    "BC_GENERAL_INFORMATION_DESCRIPTION": "Konfigurieren Sie eine Branch Connector-Bereitstellungsvorlage im Zscaler Cloud & Branch Connector-Verwaltungsportal, um Branch Connector in Ihrem Branch-Konto oder Rechenzentrum als virtuelle Maschine zu implementieren. Weitere Informationen finden Sie im Dokument {0} Erste Schritte {1} .",
    "BC_GROUP_DETAILS": "Branch Connector-Gruppendetails",
    "BC_GROUP_NAME": "Branch Connector-Gruppenname",
    "BC_GROUP_TYPE": "Branch Connector-Gruppentyp",
    "BC_GROUP": "Branch Connector-Gruppe",
    "BC_IMAGES_DESCRIPTION": "Um Branch Connector oder Branch Connector + App Connector mit Terraform bereitzustellen, besuchen Sie {0}GitHub{1}. Nähere Informationen dazu finden Sie im {2}Zscaler-Hilfeportal{3}.",
    "BC_IMAGES_DETAIL1": "Branch Connector-Abbilder enthalten App Connectors, die noch nicht bereitgestellt wurden. Um Branch Connector und App Connector bereitzustellen, müssen Sie die kombinierte Instanz auswählen und die Deployment-Eigenschaften entsprechend konfigurieren.",
    "BC_IMAGES_DETAIL2": "Weitere Informationen finden Sie unter {0}Bereitstellungsverwaltung für virtuelle Geräte{1}.",
    "BC_VM_SIZE": "Branch Connector-VM-Größe",
    "BELARUS_EUROPE_MINSK": "Europa / Minsk",
    "BELARUS": "Weißrussland",
    "BELGIUM_EUROPE_BRUSSELS": "Europa / Brussels",
    "BELGIUM": "Belgien",
    "BELIZE_AMERICA_BELIZE": "Amerika/Belize",
    "BELIZE": "Belize",
    "BENIN_AFRICA_PORTO_NOVO": "Afrika / Porto-Novo",
    "BENIN": "Benin",
    "BERMUDA_ATLANTIC_BERMUDA": "Antlanic/Bermudas",
    "BERMUDA": "Bermuda",
    "BEST_LINK": "Beste Verbindung",
    "BEST": "Beste Verbindung",
    "BESTLINK_RULE": "Beste Verbindung",
    "BHUTAN_ASIA_THIMPHU": "Asien/Thimphu",
    "BHUTAN": "Bhutan",
    "BLACKLIST_LOOKUP_RESULTS": "Lookup-Ergebnisse Abweisungsliste",
    "BLACKLISTED_IP_CHECK": "IP-Prüfung Abweisungsliste",
    "BLOCK_INTERNET_ACCESS": "Internetzugriff blockieren",
    "BLOCK": "Blockieren",
    "BLOG": "Blogs",
    "BLUEJEANS": "BlueJeans",
    "BOLIVIA_AMERICA_LA_PAZ": "Amerika/La Paz",
    "BOLIVIA": "Bolivien",
    "BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO": "Europa / Sarajevo",
    "BOSNIA_AND_HERZEGOVINA": "Bosnien und Herzegowina",
    "BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO": "Europa / Sarajevo",
    "BOSNIA_AND_HERZEGOWINA": "Bosnien und Herzegowina",
    "BOTH_REQ_RESP_ALLOW": "Zulassen",
    "BOTH_SESSION_AND_AGGREGATE_LOGS": "Sitzungs- und Aggregatprotokolle",
    "BOTNET": "Botnet",
    "BOTSWANA_AFRICA_GABORONE": "Afrika / Gaborone",
    "BOTSWANA": "Botswana",
    "BRANCH_AND_CLOUD_CONNECTOR_GROUP_NAME": "Cloud & Branch Connector-Gruppenname",
    "BRANCH_AND_CLOUD_CONNECTOR_MONITORING": "Cloud & Branch Connector-Überwachung",
    "BRANCH_AND_CLOUD_CONNECTOR": "Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_MONITORING": "Cloud & Branch-Überwachung",
    "BRANCH_CLOUD_CONNECTOR_GROUP": "Cloud & Branch Connector-Gruppen",
    "BRANCH_CONFIGURATION": "Branch-Konfiguration",
    "BRANCH_CONNECTOR_GROUP": "Branch Connector-Gruppe",
    "BRANCH_CONNECTOR_GROUPS": "Branch Connector-Gruppen",
    "BRANCH_CONNECTOR_IMAGES": "Branch Connector-Bilder",
    "BRANCH_CONNECTOR_INFORMATION": "Connector-Informationen",
    "BRANCH_CONNECTOR_LOCS": "Branch Connector-Standorte",
    "BRANCH_CONNECTOR_MONITORING": "Branch Connector-Überwachung",
    "BRANCH_CONNECTOR": "Branch Connector",
    "BRANCH_DEVICES": "Branch-Geräte",
    "BRANCH_MANAGEMENT": "Branch-Verwaltung",
    "BRANCH_PROVISIONING": "Niederlassungs-Bereitstellung",
    "BRANCH_TYPE": "Zweigestellentyp",
    "BRAZIL_AMERICA_ARAGUAINA": "Amerika/Araguaina",
    "BRAZIL_AMERICA_BAHIA": "Amerika/Bahia",
    "BRAZIL_AMERICA_BELEM": "Amerika/Belem",
    "BRAZIL_AMERICA_BOA_VISTA": "Amerika/Boa Vista",
    "BRAZIL_AMERICA_CAMPO_GRANDE": "Amerika/Campo Grande",
    "BRAZIL_AMERICA_CUIABA": "Amerika/Cuiaba",
    "BRAZIL_AMERICA_EIRUNEPE": "Amerika/Eirunepe",
    "BRAZIL_AMERICA_FORTALEZA": "Amerika/Fortaleza",
    "BRAZIL_AMERICA_MACEIO": "Amerika/Maceio",
    "BRAZIL_AMERICA_MANAUS": "Amerika/Manaus",
    "BRAZIL_AMERICA_NORONHA": "Amerika/Noronha",
    "BRAZIL_AMERICA_PORTO_VELHO": "Amerika/Porto Velho",
    "BRAZIL_AMERICA_RECIFE": "Amerika/Recife",
    "BRAZIL_AMERICA_RIO_BRANCO": "Amerika/Rio Branco",
    "BRAZIL_AMERICA_SAO_PAULO": "Amerika/Sao Paulo",
    "BRAZIL": "Brasilien",
    "BRAZILSOUTH": "(Südamerika) Brasilien Süd",
    "BRAZILSOUTHEAST": "(Südamerika) Brasilien Südost",
    "BRAZILUS": "(Südamerika) Brasilien US",
    "BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS": "Indisch/Chagos",
    "BRITISH_INDIAN_OCEAN_TERRITORY": "Britisches Territorium im Indischen Ozean",
    "BRITISH_VIRGIN_ISLANDS": "Britische Jungferninseln",
    "BROWSER_EXPLOIT": "Browser-Exploits",
    "BRUNEI_DARUSSALAM_ASIA_BRUNEI": "Asien/Brunei",
    "BRUNEI_DARUSSALAM": "Brunei Darussalam",
    "BRUNEI": "Brunei",
    "BULGARIA_EUROPE_SOFIA": "Europa / Sofia",
    "BULGARIA": "Bulgarien",
    "BURKINA_FASO_AFRICA_OUAGADOUGOU": "Afrika / Ouagadougou",
    "BURKINA_FASO": "Burkina Faso",
    "BURUNDI_AFRICA_BUJUMBURA": "Afrika / Bujumbura",
    "BURUNDI": "Burundi",
    "BW_DOWNLOAD": "Download (Mbps)",
    "BW_UPLOAD": "Upload (Mbps)",
    "BYTES": "Bytes",
    "CA_CENTRAL_1": "Kanada (Mitte)",
    "CA_CENTRAL_1A": "ca-central-1a",
    "CA_CENTRAL_1B": "ca-central-1b",
    "CA_CENTRAL_1D": "ca-central-1d",
    "CA_INACTIVE": "Fehler bei der Gesundheitsüberwachung. System ist zu lange ausgefallen.",
    "CABO_VERDE": "Kap Verde",
    "CAMBODIA_ASIA_PHNOM_PENH": "Asien/Phnom Penh",
    "CAMBODIA": "Kambodscha",
    "CAMEROON_AFRICA_DOUALA": "Afrika / Douala",
    "CAMEROON": "Kamerun",
    "CANADA_AMERICA_ATIKOKAN": "Amerika/Atikokan",
    "CANADA_AMERICA_BLANC_SABLON": "Amerika/Blanc-Sablon",
    "CANADA_AMERICA_CAMBRIDGE_BAY": "Amerika/Cambridge Bay",
    "CANADA_AMERICA_DAWSON_CREEK": "Amerika/Dawson Creek",
    "CANADA_AMERICA_DAWSON": "Amerika/Dawson",
    "CANADA_AMERICA_EDMONTON": "Amerika/Edmonton",
    "CANADA_AMERICA_GLACE_BAY": "Amerika/Glace Bay",
    "CANADA_AMERICA_GOOSE_BAY": "Amerika/Goose Bay",
    "CANADA_AMERICA_HALIFAX": "Amerika/Halifax",
    "CANADA_AMERICA_INUVIK": "Amerika/Inuvik",
    "CANADA_AMERICA_IQALUIT": "Amerika/Iqaluit",
    "CANADA_AMERICA_MONCTON": "Amerika/Moncton",
    "CANADA_AMERICA_MONTREAL": "Amerika/Montreal",
    "CANADA_AMERICA_NIPIGON": "Amerika/Nipigon",
    "CANADA_AMERICA_PANGNIRTUNG": "Amerika/Pangnirtung",
    "CANADA_AMERICA_RAINY_RIVER": "Amerika/Rainy River",
    "CANADA_AMERICA_RANKIN_INLET": "Amerika/Rankin Inlet",
    "CANADA_AMERICA_REGINA": "Amerika/Regina",
    "CANADA_AMERICA_RESOLUTE": "Amerika/Resolute",
    "CANADA_AMERICA_ST_JOHNS": "Amerika/St. Johns",
    "CANADA_AMERICA_SWIFT_CURRENT": "Amerika/Swift Current",
    "CANADA_AMERICA_THUNDER_BAY": "Amerika/Thunder Bay",
    "CANADA_AMERICA_TORONTO": "Amerika/Toronto",
    "CANADA_AMERICA_VANCOUVER": "Amerika/Vancouver",
    "CANADA_AMERICA_WHITEHORSE": "Amerika/Whitehorse",
    "CANADA_AMERICA_WINNIPEG": "Amerika/Winnipeg",
    "CANADA_AMERICA_YELLOWKNIFE": "Amerika/Yellowknife",
    "CANADA": "Kanada",
    "CANADACENTRAL": "(Kanada) Kanada Mitte",
    "CANADAEAST": "(Kanada) Kanada Ost",
    "CANCEL_SUCCESS_MESSAGE": "Alle Änderungen wurden verworfen.",
    "CANCEL": "Abbrechen",
    "CAPE_VERDE_ATLANTIC_CAPE_VERDE": "Atlantik/Kapverdische Inseln",
    "CAPE_VERDE": "Kapverden",
    "CATEGORIES": "Kategorien",
    "CATEGORY": "Kategorie",
    "CAYMAN_ISLANDS_AMERICA_CAYMAN": "Amerika/Cayman",
    "CAYMAN_ISLANDS": "Caymaninseln",
    "CC_ADMIN": "Cloud Connector-Administrator",
    "CC_BC_DETAILS": "Cloud & Branch Connector-Details",
    "CC_CLOUD_PROVIDER_DESCRIPTION": "Wählen Sie einen der folgenden Cloud-Anbieter aus.",
    "CC_DETAILS": "Cloud Connector-Details",
    "CC_ENABLE_XFF_FORWARDING": "XFF aus Clientanfrage verwenden",
    "CC_GENERAL_INFORMATION_DESCRIPTION": "Konfigurieren Sie eine Cloud-Bereitstellungsvorlage im Zscaler Cloud & Branch Connector-Verwaltungsportal für die Implementierung von Cloud Connector als virtuellen Computer mit Amazon Web Services (AWS), Google Cloud Platform (GCP) oder Microsoft AZURE. Nähere Informationen finden Sie unter {0}Informationen zu Cloud-Bereitstellungsvorlagen{1}.",
    "CC_GROUP_NAME": "Cloud Connector-Gruppenname",
    "CC_GROUP": "CC-Gruppe",
    "CC_INSTANCE": "CC-Instanz",
    "CC_LOCATION": "Cloud Connector-Standort",
    "CC_NW": "CLOUD CONNECTORS-NETZWERK",
    "CC_ROLE_NAME": "Cloud Connector-Rollenname",
    "CC_SOURCE_IP": "CC-Quell-IP",
    "CC_SOURCE_PORT": "CC-Quellport",
    "CC_STATUS": "Cloud Connector-Status",
    "CC_VERSION": "Cloud Connector-Version",
    "CC_VM_NAME": "Cloud Connector-VM-Name",
    "CC_VM": "CC-VM",
    "CCA_DEVICE_GROUP": "VDI-Gerätegruppe",
    "CCA_DEVICE": "VDI-Gerät",
    "CCA_FWD_PROFILE": "VDI-Weiterleitungsprofil",
    "CCA_TEMPLATE_APIKEY": "VDI-Vorlagen-API-Schlüssel",
    "CCA_TEMPLATE_KEY": "VDI-Vorlagen-Schlüssel",
    "CCA_TEMPLATE": "VDI-Vorlage",
    "CDN": "CDN",
    "CELLULAR_CONFIGURATION_MODE_CORE": "Geteiltes Deployment – Core",
    "CELLULAR_CONFIGURATION_MODE_EDGE": "Geteiltes Deployment – Edge",
    "CELLULAR_CONFIGURATION_MODE_EDGEONLY": "Nur Edge",
    "CELLULAR_CONFIGURATION_MODE": "Mobilfunk-Konfigurationsmodus",
    "CELLULAR_CONFIGURATION_SELECTION": "Mobilfunk-Konfiguration auswählen",
    "CELLULAR_CONFIGURATION": "Mobilfunk-Konfiguration",
    "CELLULAR_DEPLOYMENT_CONFIGURATION": "Deployment-Konfiguration",
    "CELLULAR": "Mobilfunk",
    "CENTOS": "CentOS",
    "CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI": "Afrika / Bangui",
    "CENTRAL_AFRICAN_REPUBLIC": "Zentralafrikanische Republik",
    "CENTRALINDIA": "Asien-Pazifikraum (Indien Mitte)",
    "CENTRALUS": "(USA) USA Mitte",
    "CENTRALUSEUAP": "(USA) USA Mitte EUAP",
    "CENTRALUSSTAGE": "(USA) USA Mitte (Stufe)",
    "CF_ADD_ON_GWLB_TEMPLATE": "Add-on-Vorlage mit Gateway Load Balancer (GWLB)",
    "CF_CUSTOM_DEPLOYMENT_TEMPLATE": "Benutzerdefinierte Bereitstellungsvorlage",
    "CF_DEFAULT_DEPLOYMENT_TEMPLATE": "Einstiegs-Bereitstellungsvorlage",
    "CF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Add-on-Vorlage mit ZPA",
    "CF_PRE_DEPLOYMENT_TEMPLATE": "Vor-Bereitstellungsvorlage",
    "CHAD_AFRICA_NDJAMENA": "Afrika / Ndjamena",
    "CHAD": "Tschad",
    "CHANGE_PASSWORD_REMINDER": "Passworterinnerung ändern",
    "CHANGE_PASSWORD_WINDOW": "Sie sollten eine Passwortänderung vornehmen alle",
    "CHANGE_PASSWORD": "Passwort ändern",
    "CHECK_BLACKLIST": "Abweisungsliste prüfen",
    "CHILE_AMERICA_SANTIAGO": "Amerika/Santiago",
    "CHILE_PACIFIC_EASTER": "Pacific/Easter",
    "CHILE": "Chile",
    "CHINA_ASIA_CHONGQING": "Asien/Chongqing",
    "CHINA_ASIA_HARBIN": "Asien/Harbin",
    "CHINA_ASIA_KASHGAR": "Asien/Kashgar",
    "CHINA_ASIA_SHANGHAI": "Asien/Shanghai",
    "CHINA_ASIA_URUMQI": "Asien/Urumqi",
    "CHINA": "China",
    "CHINAEAST": "(Asien-Pazifikraum) China Ost",
    "CHINAEAST2": "(Asien-Pazifikraum) China Ost 2",
    "CHINAEAST3": "(Asien-Pazifikraum) China Ost 3",
    "CHINANORTH": "(Asien-Pazifikraum) China Nord",
    "CHINANORTH2": "(Asien-Pazifikraum) China Nord 2",
    "CHINANORTH3": "(Asien-Pazifikraum) China Nord 3",
    "CHOOSE_EXISTING_LOCATION": "Bestehenden Standort auswählen",
    "CHOOSE_TO_RECEIVE_UPDATES": "WÄHLEN, UM UPDATES ZU ERHALTEN",
    "CHRISTMAS_ISLAND_INDIAN_CHRISTMAS": "Indisch/Christmas",
    "CHRISTMAS_ISLAND": "Weihnachtsinsel",
    "CHROME_OS": "Chrome",
    "CIPHER_PROTOCOL": "Cipher-Protokoll",
    "CIPHER": "Cipher",
    "CITY_STATE_PROVINCE_OPTIONAL": "Stadt, Bundesland, Provinz (optional)",
    "CITY": "Stadt",
    "CLASSIFIEDS": "Kleinanzeigen",
    "CLEAR_ALL": "Alle löschen",
    "CLEAR_FILTERS": "Filter löschen",
    "CLEAR_SEARCH_AND_SORT": "Suchen und Sortieren löschen",
    "CLICK_FOR_MORE_INFO": "Für weitere Informationen hier klicken",
    "CLICK_HERE_TO_ACCEPT_EUSA": "Hier klicken, um die ausstehende EUSA-Vereinbarung zu akzeptieren",
    "CLIENT_CONNECTOR_FOR_VDI": "Client Connector für VDI",
    "CLIENT_DEST_NAME": "Zielname Client",
    "CLIENT_DESTINATION_IP": "Ziel-IP Client",
    "CLIENT_DESTINATION_PORT": "Zielport Client",
    "CLIENT_ID": "Client-ID",
    "CLIENT_IP": "Client-IP",
    "CLIENT_NETWORK_PROTOCOL": "Client-NW-Protokoll",
    "CLIENT_SECRET": "Client-Geheimschlüssel",
    "CLIENT_SOURCE_IP": "Client-Quell-IP",
    "CLIENT_SOURCE_PORT": "Quellport Client",
    "CLOSE": "Schließen",
    "CLOUD_ACCOUNT": "Cloud-Konto",
    "CLOUD_AUTOMATION_SCRIPTS": "Cloud-Automatisierungsskripte",
    "CLOUD_CONFIG_REQUIREMENTS": "Cloud-Konfigurationsanforderungen",
    "CLOUD_CONFIGURATION": "Cloud-Konfiguration",
    "CLOUD_CONNECTOR_CONFIGURATION_NOT_APPLICABLE": "Dieser Konfigurationsmodus ist für Cloud Connector nicht gültig",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_OPTIONAL": "Gruppen und Namespace",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_TEXT": "Wählen Sie optional die Cloud Connector-Gruppen aus der Liste aus, die in dieser Abonnementgruppe enthalten sein sollen. Geben Sie einen Namespace ein, der es Ihnen ermöglicht, dasselbe Abonnement in verschiedenen Konten wiederzuverwenden.",
    "CLOUD_CONNECTOR_GROUP_CREATION": "Cloud Connector-Gruppenerstellung",
    "CLOUD_CONNECTOR_GROUP": "Cloud Connector-Gruppe",
    "CLOUD_CONNECTOR_GROUPS": "Cloud Connector-Gruppen",
    "CLOUD_CONNECTOR_INFORMATION": "Connector-Informationen",
    "CLOUD_CONNECTOR_INSTANCE_ROLE_NAME": "Cloud Connector-Instanzrollenname ",
    "CLOUD_CONNECTOR_MANAGEMENT": "Cloud Connector Management",
    "CLOUD_CONNECTOR_MONITORING": "Cloud Connector-Überwachung",
    "CLOUD_CONNECTOR_NAME": "Cloud Connector-Name",
    "CLOUD_CONNECTOR_PROVISIONING": "Cloud Connector-Bereitstellung",
    "CLOUD_CONNECTOR_TRAFFIC_FLOW": "Cloud Connector-Verkehrsfluss",
    "CLOUD_CONNECTOR": "Cloud Connector",
    "CLOUD_CONNECTORS_GROUP_AND_NAMESPACE": "Gruppen und Namespace",
    "CLOUD_CONNECTORS": "Cloud Connectors",
    "CLOUD_FORMATION": "CloudFormation",
    "CLOUD_MANAGEMENT": "Cloud Management",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_FAILED": "Fehlgeschlagen am",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PENDING": "Validierung steht aus. Klicken Sie auf das Symbol, um die Konnektivität zu testen.",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PREFIX": "Letzte Validierung",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_SUCCESSFUL": "Erfolgreich am",
    "CLOUD_NSS_HTTP_HEADERS": "HTTP-Header",
    "CLOUD_OR_ON_PREMISE": "Cloud oder standortbasiert",
    "CLOUD_PROVIDER_TYPE": "Art des Cloud-Anbieters",
    "CLOUD_PROVIDER": "Cloud-Anbieter",
    "CLOUD_PROVISIONING": "Cloud-Bereitstellung",
    "CLOUD_WATCH_GROUP_ARN": "Cloud Watch Group ARN",
    "CLOUD": "Cloud",
    "CLOUDFORMATION_TEXT": "CloudFormation-Vorlagen werden verwendet, um Zscaler Berechtigungen für den Zugriff auf Kontoinformationen zu gewähren. Verwenden Sie den Link „CloudFormation starten“, um eine vorbefüllte CloudFormation-Vorlage in Ihrem AWS-Konto zu öffnen. {1}Laden Sie die CloudFormation-Vorlage herunter{2}, wenn die Startoption nicht funktioniert.",
    "CLOUDFORMATION": "CloudFormation",
    "CLOUDWATCH_ARN_OPTIONAL": "Cloudwatch-ARN (optional)",
    "CLT_RX_BYTES": "Empfangene Bytes Client",
    "CLT_TX_BYTES": "Gesendet Bytes Client",
    "CLT_TX_DROPS": "Verlorene Bytes Client",
    "CLUSTERS": "Cluster",
    "CN_NORTH_1": "China (Beijing)",
    "CN_NORTH_1A": "cn-north-1a",
    "CN_NORTH_1B": "cn-north-1b",
    "CN_NORTHWEST_1": "China (Ningxia)",
    "CN_NORTHWEST_1A": "cn-northwest-1a",
    "CN_NORTHWEST_1B": "cn-northwest-1b",
    "CN_NORTHWEST_1C": "cn-northwest-1c",
    "COCOS_KEELING_ISLANDS_INDIAN_COCOS": "Indisch/Cocos",
    "COCOS_KEELING_ISLANDS": "Kokosinseln",
    "CODE": "CODE",
    "COLOMBIA_AMERICA_BOGOTA": "Amerika/Bogota",
    "COLOMBIA": "Kolumbien",
    "COMMANDS": "Befehle",
    "COMMENTS": "Kommentare",
    "COMOROS_INDIAN_COMORO": "Indisch/Comoro",
    "COMOROS": "Komoren",
    "COMPARE_VERSIONS": "Versionen vergleichen",
    "COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE": "EMPFOHLENEN EC2-INSTANZTYP BERECHNEN",
    "COMPUTE": "Berechnen",
    "COMPUTER_HACKING": "Computer-Hacking",
    "CONFIG": "Konfiguration",
    "CONFIGURATION_INFO": "KONFIGURATIONSINFORMATIONEN",
    "CONFIGURATION_MODE": "Konfigurationsmodus",
    "CONFIGURATION_NAME": "Konfigurationsname",
    "CONFIGURATION_TEMPLATE_NAME": "Name der Konfigurationsvorlage",
    "CONFIGURATION_TEXT": "Zscaler benötigt Berechtigungen, um eine IAM-Rolle in Ihrem AWS-Konto zu übernehmen. Diese Berechtigungen ermöglichen Zscaler, Echtzeit-Konfigurationsmetadaten in AWS zu erfassen.  Nähere Informationen dazu finden Sie unter {1}Ein neues AWS-Konto hinzufügen{2}.",
    "CONFIGURATION": "Konfiguration",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP": "Sie können Regeln für Anfragen und Antworten zur Protokoll- und Steuerungsweiterleitung definieren.",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD": "Richtlinie für die Protokoll- und Weiterleitungssteuerung konfigurieren",
    "CONFIGURE_TRAFFIC_FORWARD_TIP": "Sie können Regeln für Anfragen und Antworten zur Datenverkehrsweiterleitung definieren.",
    "CONFIGURE_TRAFFIC_FORWARD": "Verkehrsweiterleitung konfigurieren",
    "CONFIGURED_MODE": "Konfigurierter Modus",
    "CONFIRM_CHANGES": "Änderungen bestätigen",
    "CONFIRM_PASSWORD_NON_EQUALITY": "Das neue Passwort und die Passwortbestätigungspasswort müssen gleich sein",
    "CONFIRM_PASSWORD_PLACEHOLDER": "Geben Sie Ihr neues Passwort ein, um es zu bestätigen",
    "CONFIRM_PASSWORD": "Passwort bestätigen",
    "CONFIRM": "Bestätigen",
    "CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE": "Afrika/Brazzaville",
    "CONGO_CONGO_BRAZZAVILLE": "Afrika/Brazzaville",
    "CONGO_DEM_REP_AFRICA_KINSHASA": "Afrika/Kinshasa",
    "CONGO_DEM_REP_AFRICA_LUBUMBASHI": "Afrika / Lubumbashi",
    "CONGO_DEM_REP": "Kongo (Dem. Rep.)",
    "CONGO_REP_AFRICA_BRAZZAVILLE": "Afrika/Brazzaville",
    "CONGO_REP": "Kongo (Rep.)",
    "CONGO_REPUBLIC": "Republik Kongo",
    "CONNECTOR_GROUP": "Connector-Gruppe",
    "CONNECTOR_GROUPS": "Connector-Gruppen",
    "CONNECTOR_INSTANCE": "Connector-Instanz",
    "CONNECTOR_IP": "Connector-IP",
    "CONNECTOR_MANAGEMENT": "Connector-Verwaltung",
    "CONNECTOR_NAME": "Connector-Name",
    "CONNECTOR_NAMES": "Connector-Namen",
    "CONNECTOR_SOURCE_IP": "Connector-Quell-IP",
    "CONNECTOR_SOURCE_PORT": "Connector-Quellport",
    "CONNECTOR_VM_SIZE": "Connector-VM-Größe",
    "CONNECTOR_VM": "Connector-VM",
    "CONTAINS": "Enthält",
    "CONTINUING_EDUCATION_COLLEGES": "Weiterbildung / Hochschulen",
    "CONTROL_SLASH_DATA": "Kontrolle/Daten",
    "COOK_ISLANDS_PACIFIC_RAROTONGA": "Pacific/Rarotonga",
    "COOK_ISLANDS": "Cookinseln",
    "Copy for provisioning": "Für Bereitstellung kopieren",
    "COPY_CLOUD_CONNECTOR": "Cloud Connector kopieren",
    "COPY_FOR_PROVISIONING": "Für Bereitstellung kopieren",
    "COPY_PROVISIONING_URL": "Bereitstellungs-URL kopieren",
    "COPY_RIGHT": "Copyright ©2007-2020 Zscaler Inc. Alle Rechte vorbehalten.",
    "COPY": "Kopieren",
    "COPYRIGHT_INFRINGEMENT": "Copyright-Verletzungen",
    "COPYRIGHT": "Copyright",
    "CORPORATE_MARKETING": "Unternehmensmarketing",
    "CORPORATE": "Unternehmensbenutzer-Verkehrstyp",
    "COSTA_RICA_AMERICA_COSTA_RICA": "Amerika/Costa Rica",
    "COSTA_RICA": "Costa Rica",
    "COTE_DIVOIRE_AFRICA_ABIDJAN": "Afrika / Abidjan",
    "COTE_DIVOIRE": "Cote d'Ivoire",
    "COUNT": "Graf",
    "COUNTRIES": "Länder",
    "COUNTRY_A1": "Anonymer Proxyserver",
    "COUNTRY_A2": "Satellitenanbieter",
    "COUNTRY_AC": "Ascension",
    "COUNTRY_AD": "Andorra",
    "COUNTRY_AE": "Vereinigte Arabische Emirate",
    "COUNTRY_AF": "Afghanistan",
    "COUNTRY_AG": "Antigua und Barbuda",
    "COUNTRY_AI": "Anguilla",
    "COUNTRY_AL": "Albanien",
    "COUNTRY_AM": "Armenien",
    "COUNTRY_AN": "Niederländische Antillen",
    "COUNTRY_AO": "Angola",
    "COUNTRY_AP": "Asien/Pazifik-Region",
    "COUNTRY_AQ": "Antarktis",
    "COUNTRY_AR": "Argentinien",
    "COUNTRY_AS": "American Samoa",
    "COUNTRY_AT": "Österreich",
    "COUNTRY_AU": "Australien",
    "COUNTRY_AW": "Aruba",
    "COUNTRY_AX": "Aland Inseln",
    "COUNTRY_AZ": "Aserbaidschan",
    "COUNTRY_BA": "Bosnien und Herzegowina",
    "COUNTRY_BB": "Barbados",
    "COUNTRY_BD": "Bangladesch",
    "COUNTRY_BE": "Belgien",
    "COUNTRY_BF": "Burkina Faso",
    "COUNTRY_BG": "Bulgarien",
    "COUNTRY_BH": "Bahrein",
    "COUNTRY_BI": "Burundi",
    "COUNTRY_BJ": "Benin",
    "COUNTRY_BL": "St. Barthelemy",
    "COUNTRY_BM": "Bermuda",
    "COUNTRY_BN": "Brunei Darussalam",
    "COUNTRY_BO": "Bolivien",
    "COUNTRY_BQ": "Bonaire, Sint Eustatius und Saba",
    "COUNTRY_BR": "Brasilien",
    "COUNTRY_BS": "Bahamas",
    "COUNTRY_BT": "Bhutan",
    "COUNTRY_BU": "Burma",
    "COUNTRY_BV": "Bouvet-Insel",
    "COUNTRY_BW": "Botswana",
    "COUNTRY_BX": "Benelux Warenzeichen und Design-Büros",
    "COUNTRY_BY": "Weißrussland",
    "COUNTRY_BZ": "Belize",
    "COUNTRY_CA": "Kanada",
    "COUNTRY_CC": "Kokosinseln",
    "COUNTRY_CD": "Demokratische Republik Kongo (Kongo-Kinshasa)",
    "COUNTRY_CF": "Zentralafrikanische Republik",
    "COUNTRY_CG": "Kongo (Kongo-Brazzaville)",
    "COUNTRY_CH": "Schweiz",
    "COUNTRY_CI": "Cote d'Ivoire",
    "COUNTRY_CK": "Cookinseln",
    "COUNTRY_CL": "Chile",
    "COUNTRY_CM": "Kamerun",
    "COUNTRY_CN": "China",
    "COUNTRY_CO": "Kolumbien",
    "COUNTRY_CODE": "Land",
    "COUNTRY_CP": "Clipperton",
    "COUNTRY_CR": "Costa Rica",
    "COUNTRY_CS": "Serbien und Montenegro",
    "COUNTRY_CT": "Kanton und Enderbury Inseln",
    "COUNTRY_CU": "Kuba",
    "COUNTRY_CV": "Kapverden",
    "COUNTRY_CW": "Curacao",
    "COUNTRY_CX": "Weihnachtsinsel",
    "COUNTRY_CY": "Zypern",
    "COUNTRY_CZ": "Tschechien",
    "COUNTRY_DD": "DDR",
    "COUNTRY_DE": "Deutschland",
    "COUNTRY_DG": "Diego Garcia",
    "COUNTRY_DJ": "Dschibuti",
    "COUNTRY_DK": "Dänemark",
    "COUNTRY_DM": "Dominica",
    "COUNTRY_DO": "Dominikanische Republik",
    "COUNTRY_DY": "Benin",
    "COUNTRY_DZ": "Algerien",
    "COUNTRY_EA": "Ceuta, Melilla",
    "COUNTRY_EC": "Ecuador",
    "COUNTRY_EE": "Estland",
    "COUNTRY_EF": "Länder des Europäsichen Patentabkommens",
    "COUNTRY_EG": "Ägypten",
    "COUNTRY_EH": "West Sahara",
    "COUNTRY_EM": "Europäisches Markenamt",
    "COUNTRY_EP": "Europäisches Patentamt",
    "COUNTRY_ER": "Eritrea",
    "COUNTRY_ES": "Spanien",
    "COUNTRY_ET": "Äthiopien",
    "COUNTRY_EU": "Europa",
    "COUNTRY_EV": "Eurasische Patentorganisation",
    "COUNTRY_EW": "Estland",
    "COUNTRY_FI": "Finland",
    "COUNTRY_FJ": "Fiji",
    "COUNTRY_FK": "Falkland Inseln (Malvinas)",
    "COUNTRY_FL": "Liechtenstein",
    "COUNTRY_FM": "Mikronesien",
    "COUNTRY_FO": "Färöer Inseln",
    "COUNTRY_FQ": "Südlichen französischen Kolonien und die Antarktis",
    "COUNTRY_FR": "Frankreich",
    "COUNTRY_FX": "Metropolitan-Frankreich",
    "COUNTRY_GA": "Gabon",
    "COUNTRY_GB": "Vereinigtes Königreich",
    "COUNTRY_GC": "Patent Büro des Kooperationsbüros der Golfstaaten (GCC)",
    "COUNTRY_GD": "Grenada",
    "COUNTRY_GE": "Georgien",
    "COUNTRY_GF": "Französisch Guiana",
    "COUNTRY_GG": "Guernsey",
    "COUNTRY_GH": "Ghana",
    "COUNTRY_GI": "Gibraltar",
    "COUNTRY_GL": "Grönland",
    "COUNTRY_GM": "Gambia",
    "COUNTRY_GN": "Guinea",
    "COUNTRY_GP": "Guadeloupe",
    "COUNTRY_GQ": "Äquatorialguinea",
    "COUNTRY_GR": "Griechenland",
    "COUNTRY_GS": "Süd-Georgia und südliche Sandwich Inseln",
    "COUNTRY_GT": "Guatemala",
    "COUNTRY_GU": "Guam",
    "COUNTRY_GW": "Guinea-Bissau",
    "COUNTRY_GY": "Guyana",
    "COUNTRY_HK": "Hong Kong",
    "COUNTRY_HM": "Heard Island und McDonald Inseln",
    "COUNTRY_HN": "Honduras",
    "COUNTRY_HR": "Kroatien",
    "COUNTRY_HT": "Haiti",
    "COUNTRY_HU": "Ungarn",
    "COUNTRY_HV": "Obervolta",
    "COUNTRY_IB": "International Bureau of WIPO",
    "COUNTRY_IC": "Kanarische Inseln",
    "COUNTRY_ID": "Indonesien",
    "COUNTRY_IE": "Irland",
    "COUNTRY_IL": "Israel",
    "COUNTRY_IM": "Isle of Man",
    "COUNTRY_IN": "Indien",
    "COUNTRY_IO": "Britisches Territorium im Indischen Ozean",
    "COUNTRY_IQ": "Irak",
    "COUNTRY_IR": "Iran",
    "COUNTRY_IS": "Island",
    "COUNTRY_IT": "Italien",
    "COUNTRY_JA": "Jamaika",
    "COUNTRY_JE": "Jersey",
    "COUNTRY_JM": "Jamaika",
    "COUNTRY_JO": "Jordan",
    "COUNTRY_JP": "Japan",
    "COUNTRY_JT": "Johnston Inseln",
    "COUNTRY_KE": "Kenia",
    "COUNTRY_KG": "Kirgisistan",
    "COUNTRY_KH": "Kambodscha",
    "COUNTRY_KI": "Kiribati",
    "COUNTRY_KM": "Komoren",
    "COUNTRY_KN": "St. Kitts und Nevis",
    "COUNTRY_KP": "Nord-Korea",
    "COUNTRY_KR": "Süd-Korea",
    "COUNTRY_KW": "Kuwait",
    "COUNTRY_KY": "Caymaninseln",
    "COUNTRY_KZ": "Kasachstan",
    "COUNTRY_LA": "Laos",
    "COUNTRY_LB": "Libanon",
    "COUNTRY_LC": "St. Lucia",
    "COUNTRY_LF": "Libya Fezzan",
    "COUNTRY_LI": "Liechtenstein",
    "COUNTRY_LK": "Sri Lanka",
    "COUNTRY_LR": "Liberia",
    "COUNTRY_LS": "Königreich Lesotho",
    "COUNTRY_LT": "Litauen",
    "COUNTRY_LU": "Luxemburg",
    "COUNTRY_LV": "Lettland",
    "COUNTRY_LY": "Libyen",
    "COUNTRY_MA": "Marokko",
    "COUNTRY_MC": "Monaco",
    "COUNTRY_MD": "Moldawien",
    "COUNTRY_ME": "Montenegro",
    "COUNTRY_MF": "St. Martin",
    "COUNTRY_MG": "Madagaskar",
    "COUNTRY_MH": "Marshallinseln",
    "COUNTRY_MI": "Midwayinseln",
    "COUNTRY_MK": "Mazedonien",
    "COUNTRY_ML": "Mali",
    "COUNTRY_MM": "Myanmar",
    "COUNTRY_MN": "Mongolien",
    "COUNTRY_MO": "Macau",
    "COUNTRY_MP": "Northern Mariana Islands",
    "COUNTRY_MQ": "Martinique",
    "COUNTRY_MR": "Mauretanien",
    "COUNTRY_MS": "Montserrat",
    "COUNTRY_MT": "Malta",
    "COUNTRY_MU": "Mauritius",
    "COUNTRY_MV": "Malediven",
    "COUNTRY_MW": "Malawi",
    "COUNTRY_MX": "Mexiko",
    "COUNTRY_MY": "Malaysien",
    "COUNTRY_MZ": "Mosambik",
    "COUNTRY_NA": "Namibia",
    "COUNTRY_NC": "Neukaledonien",
    "COUNTRY_NE": "Niger",
    "COUNTRY_NF": "Norfolkinsel",
    "COUNTRY_NG": "Nigeria",
    "COUNTRY_NH": "New Hebrides",
    "COUNTRY_NI": "Nicaragua",
    "COUNTRY_NL": "Niederlande",
    "COUNTRY_NO": "Norwegen",
    "COUNTRY_NP": "Nepal",
    "COUNTRY_NQ": "Dronning Maud Land",
    "COUNTRY_NR": "Nauru",
    "COUNTRY_NT": "Neutrale Zone",
    "COUNTRY_NU": "Niue",
    "COUNTRY_NZ": "Neuseeland",
    "COUNTRY_O1": "Sonstiges",
    "COUNTRY_OA": "Afrikanische Organisation für geistiges Eigentum",
    "COUNTRY_OM": "Oman",
    "COUNTRY_OPTIONAL": "Land (optional)",
    "COUNTRY_PA": "Panama",
    "COUNTRY_PC": "Pacific Islands, Trust Territory of the",
    "COUNTRY_PE": "Peru",
    "COUNTRY_PF": "Französisch Polynesien",
    "COUNTRY_PG": "Papua-Neuguinea",
    "COUNTRY_PH": "Philippinen",
    "COUNTRY_PI": "Philippinen",
    "COUNTRY_PK": "Pakistan",
    "COUNTRY_PL": "Polen",
    "COUNTRY_PM": "St. Pierre und Miquelon",
    "COUNTRY_PN": "Pitcairn Inseln",
    "COUNTRY_PR": "Puerto Rico",
    "COUNTRY_PS": "Palästinensische Autonomiegebiete",
    "COUNTRY_PT": "Portugal",
    "COUNTRY_PU": "U.S. verschiedene Pazifische Inseln",
    "COUNTRY_PW": "Palau",
    "COUNTRY_PY": "Paraguay",
    "COUNTRY_PZ": "Panamakanalzone",
    "COUNTRY_QA": "Katar",
    "COUNTRY_RA": "Argentinien",
    "COUNTRY_RB": "Bolivien cf Botswana",
    "COUNTRY_RC": "China",
    "COUNTRY_RE": "Reunion",
    "COUNTRY_RH": "Haiti",
    "COUNTRY_RI": "Indonesien",
    "COUNTRY_RL": "Libanon",
    "COUNTRY_RM": "Madagaskar",
    "COUNTRY_RN": "Niger",
    "COUNTRY_RO": "Rumänien",
    "COUNTRY_RP": "Philippinen",
    "COUNTRY_RS": "Serbien",
    "COUNTRY_RU": "Russland",
    "COUNTRY_RW": "Ruanda",
    "COUNTRY_SA": "Saudi Arabien",
    "COUNTRY_SB": "Solomon Inseln",
    "COUNTRY_SC": "Seychellen",
    "COUNTRY_SD": "Sudan",
    "COUNTRY_SE": "Schweden",
    "COUNTRY_SF": "Finland",
    "COUNTRY_SG": "Singapur",
    "COUNTRY_SH": "St. Helena",
    "COUNTRY_SI": "Slowakei",
    "COUNTRY_SJ": "Spitzbergen und Jan Mayen",
    "COUNTRY_SK": "Slowakei",
    "COUNTRY_SL": "Sierra Leone",
    "COUNTRY_SM": "San Marino",
    "COUNTRY_SN": "Senegal",
    "COUNTRY_SO": "Somalia",
    "COUNTRY_SR": "Surinam",
    "COUNTRY_SS": "Süd-Sudan",
    "COUNTRY_ST": "Sao Tome und Principe",
    "COUNTRY_SU": "USSR",
    "COUNTRY_SV": "El Salvador",
    "COUNTRY_SX": "St. Martin",
    "COUNTRY_SY": "Syrien",
    "COUNTRY_SZ": "Swasiland",
    "COUNTRY_TA": "Tristan da Cunha",
    "COUNTRY_TC": "Turks und Caicos Inseln",
    "COUNTRY_TD": "Tschad",
    "COUNTRY_TF": "Südlichen französischen Kolonien",
    "COUNTRY_TG": "Tokelau",
    "COUNTRY_TH": "Thailand",
    "COUNTRY_TJ": "Tajikistan",
    "COUNTRY_TK": "Tokelau",
    "COUNTRY_TL": "Timor-Leste",
    "COUNTRY_TM": "Turkmenistan",
    "COUNTRY_TN": "Tunesien",
    "COUNTRY_TO": "Tonga",
    "COUNTRY_TP": "Ost-Timor",
    "COUNTRY_TR": "Türkei",
    "COUNTRY_TT": "Trinidad und Tobago",
    "COUNTRY_TV": "Tuvalu",
    "COUNTRY_TW": "Taiwan",
    "COUNTRY_TZ": "Tansania",
    "COUNTRY_UA": "Ukraine",
    "COUNTRY_UG": "Uganda",
    "COUNTRY_UK": "Vereinigtes Königreich",
    "COUNTRY_UM": "Amerikanisch-Ozeanien",
    "COUNTRY_US": "Vereinigte Staaten von Amerika",
    "COUNTRY_USA": "USA",
    "COUNTRY_UY": "Uruguay",
    "COUNTRY_UZ": "Usbekistan",
    "COUNTRY_VA": "Vatikan",
    "COUNTRY_VC": "St. Vincent und die Grenadinen",
    "COUNTRY_VD": "Demokratische Republik Vietnam",
    "COUNTRY_VE": "Venezuela",
    "COUNTRY_VG": "Virgin Islands (Britisch)",
    "COUNTRY_VI": "Virgin Islands (USA)",
    "COUNTRY_VN": "Vietnam",
    "COUNTRY_VU": "Vanuatu",
    "COUNTRY_WF": "Wallis und Futuna",
    "COUNTRY_WG": "Grenada",
    "COUNTRY_WK": "Wake Insel",
    "COUNTRY_WL": "St. Lucia",
    "COUNTRY_WO": "World Intellectual Property Organization",
    "COUNTRY_WS": "Samoa",
    "COUNTRY_WV": "St. Vincent",
    "COUNTRY_YD": "Demokratischer Jemen",
    "COUNTRY_YE": "Jemen",
    "COUNTRY_YT": "Mayotte",
    "COUNTRY_YU": "This country does no longer exist",
    "COUNTRY_YV": "Venezuela",
    "COUNTRY_ZA": "Südafrika",
    "COUNTRY_ZM": "Zambia",
    "COUNTRY_ZR": "Zaire",
    "COUNTRY_ZW": "Simbabwe",
    "COUNTRY": "Land",
    "CREATE_A_NEW_GROUP": "Eine neue Gruppe erstellen",
    "CREATE_A_NEW_TEST": "Neuen Test erstellen",
    "CREATE_COMPLETE": "Erstellung abgeschlossen",
    "CREATE_IN_PROGRESS": "Erstellung läuft",
    "CREATE_TEST_TEXT": "Erstellen Sie einen Test, indem Sie den Namen, die Beschreibung, das Ziel und das Protokoll (HTTP/HTTPS) für den Test eingeben. Nach dem Erstellen kann der Test bei Bedarf ausgeführt werden. Beim Ausführen simuliert dieser Test mit dem eingegebenen Protokoll einen curl-Befehl zum Ziel. Die Transaktion sollte in den Weiterleitungs-Logs, Firewall-Logs, Web-Logs usw. sichtbar sein.",
    "CREATE_TEST": "Test erstellen",
    "CREATE": "Erstellen",
    "CREATING": "Erstellung läuft",
    "CREDENTIALS_INFO": "Geben Sie die Zugangsdaten für den Azure-Dienstprinzipal ein, um auf Ihre Azure-Abonnements zuzugreifen. Sie können entweder einen einzelnen Dienstprinzipal für mehrere Abonnements verwenden oder für jedes Abonnement einen Dienstprinzipal verwenden. Achten Sie darauf, dass der Dienstprinzipal {1}Zugriffsberechtigungen{2} hat.",
    "CREDENTIALS_TEXT": "Geben Sie die Zugangsdaten für das Azure-Konto ein, um auf Ihr Azure-Konto zuzugreifen.",
    "CREDENTIALS": "Zugangsdaten",
    "CRITERIA_TEXT": "Kriterien",
    "CRITERIA": "KRITERIEN",
    "CROATIA_EUROPE_ZAGREB": "Europa / Zagreb",
    "CROATIA": "Kroatien",
    "CRYPTOMINING": "Cryptomining",
    "CTL_CONNECTION_FAIL": "SVPN-Steuerungsverbindung fehlgeschlagen.",
    "CTL_GW_CONN_CLOSE": "Aktive Verbindung zum Steuerungs-Gateway geschlossen.",
    "CTL_GW_CONN_SETUP_FAIL": "Verbindung zum Steuerungs-Gateway fehlgeschlagen (interner Fehler).",
    "CTL_GW_CONNECT_FAIL": "Verbindung zum Steuerungs-Gateway fehlgeschlagen (Netzwerkehler).",
    "CTL_GW_DNS_RESOLVE_FAIL": "DNS-Auflösung für Steuerungs-Gateway fehlgeschlagen.",
    "CTL_GW_KA_FAIL": "Keepalive für Verbindung zum Steuerungs-Gateway fehlgeschlagen.",
    "CTL_GW_NO_CONN": "Verbindung zum Steuerungs-Gateway vom Client noch nicht eingeleitet.",
    "CTL_GW_PAC_RESOLVE_FAIL": "PAC-Auflösung für Steuerungs-Gateway fehlgeschlagen.",
    "CTL_GW_PAC_RESOLVE_NOIP": "PAC-Auflösung für Steuerungs-Gateway lieferte keine IPS zurück.",
    "CTL_GW_PROTO_MSG_ERROR": "Nachrichtenformatfehler in der Antwort des Steuerungs-GW.",
    "CTL_GW_SRV_ERR_RESPONSE": "Steuerungs-Gateway hat eine HTTP-Fehlerantwort vom Server erhalten.",
    "CTL_GW_UNHEALTHY": "Steuerungs-Gateway ist fehlerhaft (transienter Zustand).",
    "CTL_KEEAPLIVE_FAIL": "Keepalive für SVPN-Steuerungsverbindung fehlgeschlagen.",
    "CTL_KEEPALIVE_FAIL": "Keepalive für SVPN-Steuerungsverbindung fehlgeschlagen.",
    "CUBA_AMERICA_HAVANA": "Amerika/Havana",
    "CUBA": "Kuba",
    "CULT": "Kult",
    "CURRENT_API_KEY": "Aktueller API-Schlüssel",
    "CURRENT_DAY": "Aktueller Tag",
    "CURRENT_MODE": "Aktueller Modus",
    "CURRENT_MONTH": "Aktueller Monat",
    "CURRENT_PASSWORD_NOT_VALID": "Geben Sie Ihr aktuelles Passwort ein",
    "CURRENT_PASSWORD": "Aktuelles Passwort",
    "CURRENT_VERSION": "Aktuelle Version",
    "CURRENT_WEEK": "Aktuelle Woche",
    "CURRENTLY_EDITING": "AKTUELL BEARBEITET",
    "CUSTOM_00": "Benutzerdefinierte Kategorie 0",
    "CUSTOM_01": "Benutzerdefinierte Kategorie 1",
    "CUSTOM_02": "Benutzerdefinierte Kategorie 2",
    "CUSTOM_03": "Benutzerdefinierte Kategorie 3",
    "CUSTOM_04": "Benutzerdefinierte Kategorie 4",
    "CUSTOM_05": "Benutzerdefinierte Kategorie 5",
    "CUSTOM_06": "Benutzerdefinierte Kategorie 6",
    "CUSTOM_07": "Benutzerdefinierte Kategorie 7",
    "CUSTOM_08": "Benutzerdefinierte Kategorie 8",
    "CUSTOM_09": "Benutzerdefinierte Kategorie 9",
    "CUSTOM_10": "Benutzerdefinierte Kategorie 0",
    "CUSTOM_100": "Benutzerdefinierte Kategorie 100",
    "CUSTOM_101": "Benutzerdefinierte Kategorie 101",
    "CUSTOM_102": "Benutzerdefinierte Kategorie 102",
    "CUSTOM_103": "Benutzerdefinierte Kategorie 103",
    "CUSTOM_104": "Benutzerdefinierte Kategorie 104",
    "CUSTOM_105": "Benutzerdefinierte Kategorie 105",
    "CUSTOM_106": "Benutzerdefinierte Kategorie 106",
    "CUSTOM_107": "Benutzerdefinierte Kategorie 107",
    "CUSTOM_108": "Benutzerdefinierte Kategorie 108",
    "CUSTOM_109": "Benutzerdefinierte Kategorie 109",
    "CUSTOM_11": "Benutzerdefinierte Kategorie 11",
    "CUSTOM_110": "Benutzerdefinierte Kategorie 110",
    "CUSTOM_111": "Benutzerdefinierte Kategorie 111",
    "CUSTOM_112": "Benutzerdefinierte Kategorie 112",
    "CUSTOM_113": "Benutzerdefinierte Kategorie 113",
    "CUSTOM_114": "Benutzerdefinierte Kategorie 114",
    "CUSTOM_115": "Benutzerdefinierte Kategorie 115",
    "CUSTOM_116": "Benutzerdefinierte Kategorie 116",
    "CUSTOM_117": "Benutzerdefinierte Kategorie 117",
    "CUSTOM_118": "Benutzerdefinierte Kategorie 118",
    "CUSTOM_119": "Benutzerdefinierte Kategorie 119",
    "CUSTOM_12": "Benutzerdefinierte Kategorie 12",
    "CUSTOM_120": "Benutzerdefinierte Kategorie 120",
    "CUSTOM_121": "Benutzerdefinierte Kategorie 121",
    "CUSTOM_122": "Benutzerdefinierte Kategorie 122",
    "CUSTOM_123": "Benutzerdefinierte Kategorie 123",
    "CUSTOM_124": "Benutzerdefinierte Kategorie 124",
    "CUSTOM_125": "Benutzerdefinierte Kategorie 125",
    "CUSTOM_126": "Benutzerdefinierte Kategorie 126",
    "CUSTOM_127": "Benutzerdefinierte Kategorie 127",
    "CUSTOM_128": "Benutzerdefinierte Kategorie 128",
    "CUSTOM_129": "Benutzerdefinierte Kategorie 129",
    "CUSTOM_13": "Benutzerdefinierte Kategorie 13",
    "CUSTOM_130": "Benutzerdefinierte Kategorie 130",
    "CUSTOM_131": "Benutzerdefinierte Kategorie 131",
    "CUSTOM_132": "Benutzerdefinierte Kategorie 132",
    "CUSTOM_133": "Benutzerdefinierte Kategorie 133",
    "CUSTOM_134": "Benutzerdefinierte Kategorie 134",
    "CUSTOM_135": "Benutzerdefinierte Kategorie 135",
    "CUSTOM_136": "Benutzerdefinierte Kategorie 136",
    "CUSTOM_137": "Benutzerdefinierte Kategorie 137",
    "CUSTOM_138": "Benutzerdefinierte Kategorie 138",
    "CUSTOM_139": "Benutzerdefinierte Kategorie 139",
    "CUSTOM_14": "Benutzerdefinierte Kategorie 14",
    "CUSTOM_140": "Benutzerdefinierte Kategorie 140",
    "CUSTOM_141": "Benutzerdefinierte Kategorie 141",
    "CUSTOM_142": "Benutzerdefinierte Kategorie 142",
    "CUSTOM_143": "Benutzerdefinierte Kategorie 143",
    "CUSTOM_144": "Benutzerdefinierte Kategorie 144",
    "CUSTOM_145": "Benutzerdefinierte Kategorie 145",
    "CUSTOM_146": "Benutzerdefinierte Kategorie 146",
    "CUSTOM_147": "Benutzerdefinierte Kategorie 147",
    "CUSTOM_148": "Benutzerdefinierte Kategorie 148",
    "CUSTOM_149": "Benutzerdefinierte Kategorie 149",
    "CUSTOM_15": "Benutzerdefinierte Kategorie 15",
    "CUSTOM_150": "Benutzerdefinierte Kategorie 150",
    "CUSTOM_151": "Benutzerdefinierte Kategorie 151",
    "CUSTOM_152": "Benutzerdefinierte Kategorie 152",
    "CUSTOM_153": "Benutzerdefinierte Kategorie 153",
    "CUSTOM_154": "Benutzerdefinierte Kategorie 154",
    "CUSTOM_155": "Benutzerdefinierte Kategorie 155",
    "CUSTOM_156": "Benutzerdefinierte Kategorie 156",
    "CUSTOM_157": "Benutzerdefinierte Kategorie 157",
    "CUSTOM_158": "Benutzerdefinierte Kategorie 158",
    "CUSTOM_159": "Benutzerdefinierte Kategorie 159",
    "CUSTOM_16": "Benutzerdefinierte Kategorie 16",
    "CUSTOM_160": "Benutzerdefinierte Kategorie 160",
    "CUSTOM_161": "Benutzerdefinierte Kategorie 161",
    "CUSTOM_162": "Benutzerdefinierte Kategorie 162",
    "CUSTOM_163": "Benutzerdefinierte Kategorie 163",
    "CUSTOM_164": "Benutzerdefinierte Kategorie 164",
    "CUSTOM_165": "Benutzerdefinierte Kategorie 165",
    "CUSTOM_166": "Benutzerdefinierte Kategorie 166",
    "CUSTOM_167": "Benutzerdefinierte Kategorie 167",
    "CUSTOM_168": "Benutzerdefinierte Kategorie 168",
    "CUSTOM_169": "Benutzerdefinierte Kategorie 169",
    "CUSTOM_17": "Benutzerdefinierte Kategorie 17",
    "CUSTOM_170": "Benutzerdefinierte Kategorie 170",
    "CUSTOM_171": "Benutzerdefinierte Kategorie 171",
    "CUSTOM_172": "Benutzerdefinierte Kategorie 172",
    "CUSTOM_173": "Benutzerdefinierte Kategorie 173",
    "CUSTOM_174": "Benutzerdefinierte Kategorie 174",
    "CUSTOM_175": "Benutzerdefinierte Kategorie 175",
    "CUSTOM_176": "Benutzerdefinierte Kategorie 176",
    "CUSTOM_177": "Benutzerdefinierte Kategorie 177",
    "CUSTOM_178": "Benutzerdefinierte Kategorie 178",
    "CUSTOM_179": "Benutzerdefinierte Kategorie 179",
    "CUSTOM_18": "Benutzerdefinierte Kategorie 18",
    "CUSTOM_180": "Benutzerdefinierte Kategorie 180",
    "CUSTOM_181": "Benutzerdefinierte Kategorie 181",
    "CUSTOM_182": "Benutzerdefinierte Kategorie 182",
    "CUSTOM_183": "Benutzerdefinierte Kategorie 183",
    "CUSTOM_184": "Benutzerdefinierte Kategorie 184",
    "CUSTOM_185": "Benutzerdefinierte Kategorie 185",
    "CUSTOM_186": "Benutzerdefinierte Kategorie 186",
    "CUSTOM_187": "Benutzerdefinierte Kategorie 187",
    "CUSTOM_188": "Benutzerdefinierte Kategorie 188",
    "CUSTOM_189": "Benutzerdefinierte Kategorie 189",
    "CUSTOM_19": "Benutzerdefinierte Kategorie 19",
    "CUSTOM_190": "Benutzerdefinierte Kategorie 190",
    "CUSTOM_191": "Benutzerdefinierte Kategorie 191",
    "CUSTOM_192": "Benutzerdefinierte Kategorie 192",
    "CUSTOM_193": "Benutzerdefinierte Kategorie 193",
    "CUSTOM_194": "Benutzerdefinierte Kategorie 194",
    "CUSTOM_195": "Benutzerdefinierte Kategorie 195",
    "CUSTOM_196": "Benutzerdefinierte Kategorie 196",
    "CUSTOM_197": "Benutzerdefinierte Kategorie 197",
    "CUSTOM_198": "Benutzerdefinierte Kategorie 198",
    "CUSTOM_199": "Benutzerdefinierte Kategorie 199",
    "CUSTOM_20": "Benutzerdefinierte Kategorie 20",
    "CUSTOM_200": "Benutzerdefinierte Kategorie 200",
    "CUSTOM_201": "Benutzerdefinierte Kategorie 201",
    "CUSTOM_202": "Benutzerdefinierte Kategorie 202",
    "CUSTOM_203": "Benutzerdefinierte Kategorie 203",
    "CUSTOM_204": "Benutzerdefinierte Kategorie 204",
    "CUSTOM_205": "Benutzerdefinierte Kategorie 205",
    "CUSTOM_206": "Benutzerdefinierte Kategorie 206",
    "CUSTOM_207": "Benutzerdefinierte Kategorie 207",
    "CUSTOM_208": "Benutzerdefinierte Kategorie 208",
    "CUSTOM_209": "Benutzerdefinierte Kategorie 209",
    "CUSTOM_21": "Benutzerdefinierte Kategorie 21",
    "CUSTOM_210": "Benutzerdefinierte Kategorie 210",
    "CUSTOM_211": "Benutzerdefinierte Kategorie 211",
    "CUSTOM_212": "Benutzerdefinierte Kategorie 212",
    "CUSTOM_213": "Benutzerdefinierte Kategorie 213",
    "CUSTOM_214": "Benutzerdefinierte Kategorie 214",
    "CUSTOM_215": "Benutzerdefinierte Kategorie 215",
    "CUSTOM_216": "Benutzerdefinierte Kategorie 216",
    "CUSTOM_217": "Benutzerdefinierte Kategorie 217",
    "CUSTOM_218": "Benutzerdefinierte Kategorie 218",
    "CUSTOM_219": "Benutzerdefinierte Kategorie 219",
    "CUSTOM_22": "Benutzerdefinierte Kategorie 22",
    "CUSTOM_220": "Benutzerdefinierte Kategorie 220",
    "CUSTOM_221": "Benutzerdefinierte Kategorie 221",
    "CUSTOM_222": "Benutzerdefinierte Kategorie 222",
    "CUSTOM_223": "Benutzerdefinierte Kategorie 223",
    "CUSTOM_224": "Benutzerdefinierte Kategorie 224",
    "CUSTOM_225": "Benutzerdefinierte Kategorie 225",
    "CUSTOM_226": "Benutzerdefinierte Kategorie 226",
    "CUSTOM_227": "Benutzerdefinierte Kategorie 227",
    "CUSTOM_228": "Benutzerdefinierte Kategorie 228",
    "CUSTOM_229": "Benutzerdefinierte Kategorie 229",
    "CUSTOM_23": "Benutzerdefinierte Kategorie 23",
    "CUSTOM_230": "Benutzerdefinierte Kategorie 230",
    "CUSTOM_231": "Benutzerdefinierte Kategorie 231",
    "CUSTOM_232": "Benutzerdefinierte Kategorie 232",
    "CUSTOM_233": "Benutzerdefinierte Kategorie 233",
    "CUSTOM_234": "Benutzerdefinierte Kategorie 234",
    "CUSTOM_235": "Benutzerdefinierte Kategorie 235",
    "CUSTOM_236": "Benutzerdefinierte Kategorie 236",
    "CUSTOM_237": "Benutzerdefinierte Kategorie 237",
    "CUSTOM_238": "Benutzerdefinierte Kategorie 238",
    "CUSTOM_239": "Benutzerdefinierte Kategorie 239",
    "CUSTOM_24": "Benutzerdefinierte Kategorie 24",
    "CUSTOM_240": "Benutzerdefinierte Kategorie 240",
    "CUSTOM_241": "Benutzerdefinierte Kategorie 241",
    "CUSTOM_242": "Benutzerdefinierte Kategorie 242",
    "CUSTOM_243": "Benutzerdefinierte Kategorie 243",
    "CUSTOM_244": "Benutzerdefinierte Kategorie 244",
    "CUSTOM_245": "Benutzerdefinierte Kategorie 245",
    "CUSTOM_246": "Benutzerdefinierte Kategorie 246",
    "CUSTOM_247": "Benutzerdefinierte Kategorie 247",
    "CUSTOM_248": "Benutzerdefinierte Kategorie 248",
    "CUSTOM_249": "Benutzerdefinierte Kategorie 249",
    "CUSTOM_25": "Benutzerdefinierte Kategorie 25",
    "CUSTOM_250": "Benutzerdefinierte Kategorie 250",
    "CUSTOM_251": "Benutzerdefinierte Kategorie 251",
    "CUSTOM_252": "Benutzerdefinierte Kategorie 252",
    "CUSTOM_253": "Benutzerdefinierte Kategorie 253",
    "CUSTOM_254": "Benutzerdefinierte Kategorie 254",
    "CUSTOM_255": "Benutzerdefinierte Kategorie 255",
    "CUSTOM_256": "Benutzerdefinierte Kategorie 256",
    "CUSTOM_257": "Benutzerdefinierte Kategorie 257",
    "CUSTOM_258": "Benutzerdefinierte Kategorie 258",
    "CUSTOM_259": "Benutzerdefinierte Kategorie 259",
    "CUSTOM_26": "Benutzerdefinierte Kategorie 26",
    "CUSTOM_260": "Benutzerdefinierte Kategorie 260",
    "CUSTOM_261": "Benutzerdefinierte Kategorie 261",
    "CUSTOM_262": "Benutzerdefinierte Kategorie 262",
    "CUSTOM_263": "Benutzerdefinierte Kategorie 263",
    "CUSTOM_264": "Benutzerdefinierte Kategorie 264",
    "CUSTOM_265": "Benutzerdefinierte Kategorie 265",
    "CUSTOM_266": "Benutzerdefinierte Kategorie 266",
    "CUSTOM_267": "Benutzerdefinierte Kategorie 267",
    "CUSTOM_268": "Benutzerdefinierte Kategorie 268",
    "CUSTOM_269": "Benutzerdefinierte Kategorie 269",
    "CUSTOM_27": "Benutzerdefinierte Kategorie 27",
    "CUSTOM_270": "Benutzerdefinierte Kategorie 270",
    "CUSTOM_271": "Benutzerdefinierte Kategorie 271",
    "CUSTOM_272": "Benutzerdefinierte Kategorie 272",
    "CUSTOM_273": "Benutzerdefinierte Kategorie 273",
    "CUSTOM_274": "Benutzerdefinierte Kategorie 274",
    "CUSTOM_275": "Benutzerdefinierte Kategorie 275",
    "CUSTOM_276": "Benutzerdefinierte Kategorie 276",
    "CUSTOM_277": "Benutzerdefinierte Kategorie 277",
    "CUSTOM_278": "Benutzerdefinierte Kategorie 278",
    "CUSTOM_279": "Benutzerdefinierte Kategorie 279",
    "CUSTOM_28": "Benutzerdefinierte Kategorie 28",
    "CUSTOM_280": "Benutzerdefinierte Kategorie 280",
    "CUSTOM_281": "Benutzerdefinierte Kategorie 281",
    "CUSTOM_282": "Benutzerdefinierte Kategorie 282",
    "CUSTOM_283": "Benutzerdefinierte Kategorie 283",
    "CUSTOM_284": "Benutzerdefinierte Kategorie 284",
    "CUSTOM_285": "Benutzerdefinierte Kategorie 285",
    "CUSTOM_286": "Benutzerdefinierte Kategorie 286",
    "CUSTOM_287": "Benutzerdefinierte Kategorie 287",
    "CUSTOM_288": "Benutzerdefinierte Kategorie 288",
    "CUSTOM_289": "Benutzerdefinierte Kategorie 289",
    "CUSTOM_29": "Benutzerdefinierte Kategorie 29",
    "CUSTOM_290": "Benutzerdefinierte Kategorie 290",
    "CUSTOM_291": "Benutzerdefinierte Kategorie 291",
    "CUSTOM_292": "Benutzerdefinierte Kategorie 292",
    "CUSTOM_293": "Benutzerdefinierte Kategorie 293",
    "CUSTOM_294": "Benutzerdefinierte Kategorie 294",
    "CUSTOM_295": "Benutzerdefinierte Kategorie 295",
    "CUSTOM_296": "Benutzerdefinierte Kategorie 296",
    "CUSTOM_297": "Benutzerdefinierte Kategorie 297",
    "CUSTOM_298": "Benutzerdefinierte Kategorie 298",
    "CUSTOM_299": "Benutzerdefinierte Kategorie 299",
    "CUSTOM_30": "Benutzerdefinierte Kategorie 30",
    "CUSTOM_300": "Benutzerdefinierte Kategorie 300",
    "CUSTOM_301": "Benutzerdefinierte Kategorie 301",
    "CUSTOM_302": "Benutzerdefinierte Kategorie 302",
    "CUSTOM_303": "Benutzerdefinierte Kategorie 303",
    "CUSTOM_304": "Benutzerdefinierte Kategorie 304",
    "CUSTOM_305": "Benutzerdefinierte Kategorie 305",
    "CUSTOM_306": "Benutzerdefinierte Kategorie 306",
    "CUSTOM_307": "Benutzerdefinierte Kategorie 307",
    "CUSTOM_308": "Benutzerdefinierte Kategorie 308",
    "CUSTOM_309": "Benutzerdefinierte Kategorie 309",
    "CUSTOM_31": "Benutzerdefinierte Kategorie 31",
    "CUSTOM_310": "Benutzerdefinierte Kategorie 310",
    "CUSTOM_311": "Benutzerdefinierte Kategorie 311",
    "CUSTOM_312": "Benutzerdefinierte Kategorie 312",
    "CUSTOM_313": "Benutzerdefinierte Kategorie 313",
    "CUSTOM_314": "Benutzerdefinierte Kategorie 314",
    "CUSTOM_315": "Benutzerdefinierte Kategorie 315",
    "CUSTOM_316": "Benutzerdefinierte Kategorie 316",
    "CUSTOM_317": "Benutzerdefinierte Kategorie 317",
    "CUSTOM_318": "Benutzerdefinierte Kategorie 318",
    "CUSTOM_319": "Benutzerdefinierte Kategorie 319",
    "CUSTOM_32": "Benutzerdefinierte Kategorie 32",
    "CUSTOM_320": "Benutzerdefinierte Kategorie 320",
    "CUSTOM_321": "Benutzerdefinierte Kategorie 321",
    "CUSTOM_322": "Benutzerdefinierte Kategorie 322",
    "CUSTOM_323": "Benutzerdefinierte Kategorie 323",
    "CUSTOM_324": "Benutzerdefinierte Kategorie 324",
    "CUSTOM_325": "Benutzerdefinierte Kategorie 325",
    "CUSTOM_326": "Benutzerdefinierte Kategorie 326",
    "CUSTOM_327": "Benutzerdefinierte Kategorie 327",
    "CUSTOM_328": "Benutzerdefinierte Kategorie 328",
    "CUSTOM_329": "Benutzerdefinierte Kategorie 329",
    "CUSTOM_33": "Benutzerdefinierte Kategorie 33",
    "CUSTOM_330": "Benutzerdefinierte Kategorie 330",
    "CUSTOM_331": "Benutzerdefinierte Kategorie 331",
    "CUSTOM_332": "Benutzerdefinierte Kategorie 332",
    "CUSTOM_333": "Benutzerdefinierte Kategorie 333",
    "CUSTOM_334": "Benutzerdefinierte Kategorie 334",
    "CUSTOM_335": "Benutzerdefinierte Kategorie 335",
    "CUSTOM_336": "Benutzerdefinierte Kategorie 336",
    "CUSTOM_337": "Benutzerdefinierte Kategorie 337",
    "CUSTOM_338": "Benutzerdefinierte Kategorie 338",
    "CUSTOM_339": "Benutzerdefinierte Kategorie 339",
    "CUSTOM_34": "Benutzerdefinierte Kategorie 34",
    "CUSTOM_340": "Benutzerdefinierte Kategorie 340",
    "CUSTOM_341": "Benutzerdefinierte Kategorie 341",
    "CUSTOM_342": "Benutzerdefinierte Kategorie 342",
    "CUSTOM_343": "Benutzerdefinierte Kategorie 343",
    "CUSTOM_344": "Benutzerdefinierte Kategorie 344",
    "CUSTOM_345": "Benutzerdefinierte Kategorie 345",
    "CUSTOM_346": "Benutzerdefinierte Kategorie 346",
    "CUSTOM_347": "Benutzerdefinierte Kategorie 347",
    "CUSTOM_348": "Benutzerdefinierte Kategorie 348",
    "CUSTOM_349": "Benutzerdefinierte Kategorie 349",
    "CUSTOM_35": "Benutzerdefinierte Kategorie 35",
    "CUSTOM_350": "Benutzerdefinierte Kategorie 350",
    "CUSTOM_351": "Benutzerdefinierte Kategorie 351",
    "CUSTOM_352": "Benutzerdefinierte Kategorie 352",
    "CUSTOM_353": "Benutzerdefinierte Kategorie 353",
    "CUSTOM_354": "Benutzerdefinierte Kategorie 354",
    "CUSTOM_355": "Benutzerdefinierte Kategorie 355",
    "CUSTOM_356": "Benutzerdefinierte Kategorie 356",
    "CUSTOM_357": "Benutzerdefinierte Kategorie 357",
    "CUSTOM_358": "Benutzerdefinierte Kategorie 358",
    "CUSTOM_359": "Benutzerdefinierte Kategorie 359",
    "CUSTOM_36": "Benutzerdefinierte Kategorie 36",
    "CUSTOM_360": "Benutzerdefinierte Kategorie 360",
    "CUSTOM_361": "Benutzerdefinierte Kategorie 361",
    "CUSTOM_362": "Benutzerdefinierte Kategorie 362",
    "CUSTOM_363": "Benutzerdefinierte Kategorie 363",
    "CUSTOM_364": "Benutzerdefinierte Kategorie 364",
    "CUSTOM_365": "Benutzerdefinierte Kategorie 365",
    "CUSTOM_366": "Benutzerdefinierte Kategorie 366",
    "CUSTOM_367": "Benutzerdefinierte Kategorie 367",
    "CUSTOM_368": "Benutzerdefinierte Kategorie 368",
    "CUSTOM_369": "Benutzerdefinierte Kategorie 369",
    "CUSTOM_37": "Benutzerdefinierte Kategorie 37",
    "CUSTOM_370": "Benutzerdefinierte Kategorie 370",
    "CUSTOM_371": "Benutzerdefinierte Kategorie 371",
    "CUSTOM_372": "Benutzerdefinierte Kategorie 372",
    "CUSTOM_373": "Benutzerdefinierte Kategorie 373",
    "CUSTOM_374": "Benutzerdefinierte Kategorie 374",
    "CUSTOM_375": "Benutzerdefinierte Kategorie 375",
    "CUSTOM_376": "Benutzerdefinierte Kategorie 376",
    "CUSTOM_377": "Benutzerdefinierte Kategorie 377",
    "CUSTOM_378": "Benutzerdefinierte Kategorie 378",
    "CUSTOM_379": "Benutzerdefinierte Kategorie 379",
    "CUSTOM_38": "Benutzerdefinierte Kategorie 38",
    "CUSTOM_380": "Benutzerdefinierte Kategorie 380",
    "CUSTOM_381": "Benutzerdefinierte Kategorie 381",
    "CUSTOM_382": "Benutzerdefinierte Kategorie 382",
    "CUSTOM_383": "Benutzerdefinierte Kategorie 383",
    "CUSTOM_384": "Benutzerdefinierte Kategorie 384",
    "CUSTOM_385": "Benutzerdefinierte Kategorie 385",
    "CUSTOM_386": "Benutzerdefinierte Kategorie 386",
    "CUSTOM_387": "Benutzerdefinierte Kategorie 387",
    "CUSTOM_388": "Benutzerdefinierte Kategorie 388",
    "CUSTOM_389": "Benutzerdefinierte Kategorie 389",
    "CUSTOM_39": "Benutzerdefinierte Kategorie 39",
    "CUSTOM_390": "Benutzerdefinierte Kategorie 390",
    "CUSTOM_391": "Benutzerdefinierte Kategorie 391",
    "CUSTOM_392": "Benutzerdefinierte Kategorie 392",
    "CUSTOM_393": "Benutzerdefinierte Kategorie 393",
    "CUSTOM_394": "Benutzerdefinierte Kategorie 394",
    "CUSTOM_395": "Benutzerdefinierte Kategorie 395",
    "CUSTOM_396": "Benutzerdefinierte Kategorie 396",
    "CUSTOM_397": "Benutzerdefinierte Kategorie 397",
    "CUSTOM_398": "Benutzerdefinierte Kategorie 398",
    "CUSTOM_399": "Benutzerdefinierte Kategorie 399",
    "CUSTOM_40": "Benutzerdefinierte Kategorie 40",
    "CUSTOM_400": "Benutzerdefinierte Kategorie 400",
    "CUSTOM_401": "Benutzerdefinierte Kategorie 401",
    "CUSTOM_402": "Benutzerdefinierte Kategorie 402",
    "CUSTOM_403": "Benutzerdefinierte Kategorie 403",
    "CUSTOM_404": "Benutzerdefinierte Kategorie 404",
    "CUSTOM_405": "Benutzerdefinierte Kategorie 405",
    "CUSTOM_406": "Benutzerdefinierte Kategorie 406",
    "CUSTOM_407": "Benutzerdefinierte Kategorie 407",
    "CUSTOM_408": "Benutzerdefinierte Kategorie 408",
    "CUSTOM_409": "Benutzerdefinierte Kategorie 409",
    "CUSTOM_41": "Benutzerdefinierte Kategorie 41",
    "CUSTOM_410": "Benutzerdefinierte Kategorie 410",
    "CUSTOM_411": "Benutzerdefinierte Kategorie 411",
    "CUSTOM_412": "Benutzerdefinierte Kategorie 412",
    "CUSTOM_413": "Benutzerdefinierte Kategorie 413",
    "CUSTOM_414": "Benutzerdefinierte Kategorie 414",
    "CUSTOM_415": "Benutzerdefinierte Kategorie 415",
    "CUSTOM_416": "Benutzerdefinierte Kategorie 416",
    "CUSTOM_417": "Benutzerdefinierte Kategorie 417",
    "CUSTOM_418": "Benutzerdefinierte Kategorie 418",
    "CUSTOM_419": "Benutzerdefinierte Kategorie 419",
    "CUSTOM_42": "Benutzerdefinierte Kategorie 42",
    "CUSTOM_420": "Benutzerdefinierte Kategorie 420",
    "CUSTOM_421": "Benutzerdefinierte Kategorie 421",
    "CUSTOM_422": "Benutzerdefinierte Kategorie 422",
    "CUSTOM_423": "Benutzerdefinierte Kategorie 423",
    "CUSTOM_424": "Benutzerdefinierte Kategorie 424",
    "CUSTOM_425": "Benutzerdefinierte Kategorie 425",
    "CUSTOM_426": "Benutzerdefinierte Kategorie 426",
    "CUSTOM_427": "Benutzerdefinierte Kategorie 427",
    "CUSTOM_428": "Benutzerdefinierte Kategorie 428",
    "CUSTOM_429": "Benutzerdefinierte Kategorie 429",
    "CUSTOM_43": "Benutzerdefinierte Kategorie 43",
    "CUSTOM_430": "Benutzerdefinierte Kategorie 430",
    "CUSTOM_431": "Benutzerdefinierte Kategorie 431",
    "CUSTOM_432": "Benutzerdefinierte Kategorie 432",
    "CUSTOM_433": "Benutzerdefinierte Kategorie 433",
    "CUSTOM_434": "Benutzerdefinierte Kategorie 434",
    "CUSTOM_435": "Benutzerdefinierte Kategorie 435",
    "CUSTOM_436": "Benutzerdefinierte Kategorie 436",
    "CUSTOM_437": "Benutzerdefinierte Kategorie 437",
    "CUSTOM_438": "Benutzerdefinierte Kategorie 438",
    "CUSTOM_439": "Benutzerdefinierte Kategorie 439",
    "CUSTOM_44": "Benutzerdefinierte Kategorie 44",
    "CUSTOM_440": "Benutzerdefinierte Kategorie 440",
    "CUSTOM_441": "Benutzerdefinierte Kategorie 441",
    "CUSTOM_442": "Benutzerdefinierte Kategorie 442",
    "CUSTOM_443": "Benutzerdefinierte Kategorie 443",
    "CUSTOM_444": "Benutzerdefinierte Kategorie 444",
    "CUSTOM_445": "Benutzerdefinierte Kategorie 445",
    "CUSTOM_446": "Benutzerdefinierte Kategorie 446",
    "CUSTOM_447": "Benutzerdefinierte Kategorie 447",
    "CUSTOM_448": "Benutzerdefinierte Kategorie 448",
    "CUSTOM_449": "Benutzerdefinierte Kategorie 449",
    "CUSTOM_45": "Benutzerdefinierte Kategorie 45",
    "CUSTOM_450": "Benutzerdefinierte Kategorie 450",
    "CUSTOM_451": "Benutzerdefinierte Kategorie 451",
    "CUSTOM_452": "Benutzerdefinierte Kategorie 452",
    "CUSTOM_453": "Benutzerdefinierte Kategorie 453",
    "CUSTOM_454": "Benutzerdefinierte Kategorie 454",
    "CUSTOM_455": "Benutzerdefinierte Kategorie 455",
    "CUSTOM_456": "Benutzerdefinierte Kategorie 456",
    "CUSTOM_457": "Benutzerdefinierte Kategorie 457",
    "CUSTOM_458": "Benutzerdefinierte Kategorie 458",
    "CUSTOM_459": "Benutzerdefinierte Kategorie 459",
    "CUSTOM_46": "Benutzerdefinierte Kategorie 46",
    "CUSTOM_460": "Benutzerdefinierte Kategorie 460",
    "CUSTOM_461": "Benutzerdefinierte Kategorie 461",
    "CUSTOM_462": "Benutzerdefinierte Kategorie 462",
    "CUSTOM_463": "Benutzerdefinierte Kategorie 463",
    "CUSTOM_464": "Benutzerdefinierte Kategorie 464",
    "CUSTOM_465": "Benutzerdefinierte Kategorie 465",
    "CUSTOM_466": "Benutzerdefinierte Kategorie 466",
    "CUSTOM_467": "Benutzerdefinierte Kategorie 467",
    "CUSTOM_468": "Benutzerdefinierte Kategorie 468",
    "CUSTOM_469": "Benutzerdefinierte Kategorie 469",
    "CUSTOM_47": "Benutzerdefinierte Kategorie 47",
    "CUSTOM_470": "Benutzerdefinierte Kategorie 470",
    "CUSTOM_471": "Benutzerdefinierte Kategorie 471",
    "CUSTOM_472": "Benutzerdefinierte Kategorie 472",
    "CUSTOM_473": "Benutzerdefinierte Kategorie 473",
    "CUSTOM_474": "Benutzerdefinierte Kategorie 474",
    "CUSTOM_475": "Benutzerdefinierte Kategorie 475",
    "CUSTOM_476": "Benutzerdefinierte Kategorie 476",
    "CUSTOM_477": "Benutzerdefinierte Kategorie 477",
    "CUSTOM_478": "Benutzerdefinierte Kategorie 478",
    "CUSTOM_479": "Benutzerdefinierte Kategorie 479",
    "CUSTOM_48": "Benutzerdefinierte Kategorie 48",
    "CUSTOM_480": "Benutzerdefinierte Kategorie 480",
    "CUSTOM_481": "Benutzerdefinierte Kategorie 481",
    "CUSTOM_482": "Benutzerdefinierte Kategorie 482",
    "CUSTOM_483": "Benutzerdefinierte Kategorie 483",
    "CUSTOM_484": "Benutzerdefinierte Kategorie 484",
    "CUSTOM_485": "Benutzerdefinierte Kategorie 485",
    "CUSTOM_486": "Benutzerdefinierte Kategorie 486",
    "CUSTOM_487": "Benutzerdefinierte Kategorie 487",
    "CUSTOM_488": "Benutzerdefinierte Kategorie 488",
    "CUSTOM_489": "Benutzerdefinierte Kategorie 489",
    "CUSTOM_49": "Benutzerdefinierte Kategorie 49",
    "CUSTOM_490": "Benutzerdefinierte Kategorie 490",
    "CUSTOM_491": "Benutzerdefinierte Kategorie 491",
    "CUSTOM_492": "Benutzerdefinierte Kategorie 492",
    "CUSTOM_493": "Benutzerdefinierte Kategorie 493",
    "CUSTOM_494": "Benutzerdefinierte Kategorie 494",
    "CUSTOM_495": "Benutzerdefinierte Kategorie 495",
    "CUSTOM_496": "Benutzerdefinierte Kategorie 496",
    "CUSTOM_497": "Benutzerdefinierte Kategorie 497",
    "CUSTOM_498": "Benutzerdefinierte Kategorie 498",
    "CUSTOM_499": "Benutzerdefinierte Kategorie 499",
    "CUSTOM_50": "Benutzerdefinierte Kategorie 50",
    "CUSTOM_500": "Benutzerdefinierte Kategorie 500",
    "CUSTOM_501": "Benutzerdefinierte Kategorie 501",
    "CUSTOM_502": "Benutzerdefinierte Kategorie 502",
    "CUSTOM_503": "Benutzerdefinierte Kategorie 503",
    "CUSTOM_504": "Benutzerdefinierte Kategorie 504",
    "CUSTOM_505": "Benutzerdefinierte Kategorie 505",
    "CUSTOM_506": "Benutzerdefinierte Kategorie 506",
    "CUSTOM_507": "Benutzerdefinierte Kategorie 507",
    "CUSTOM_508": "Benutzerdefinierte Kategorie 508",
    "CUSTOM_509": "Benutzerdefinierte Kategorie 509",
    "CUSTOM_51": "Benutzerdefinierte Kategorie 51",
    "CUSTOM_510": "Benutzerdefinierte Kategorie 510",
    "CUSTOM_511": "Benutzerdefinierte Kategorie 511",
    "CUSTOM_512": "Benutzerdefinierte Kategorie 512",
    "CUSTOM_52": "Benutzerdefinierte Kategorie 52",
    "CUSTOM_53": "Benutzerdefinierte Kategorie 53",
    "CUSTOM_54": "Benutzerdefinierte Kategorie 54",
    "CUSTOM_55": "Benutzerdefinierte Kategorie 55",
    "CUSTOM_56": "Benutzerdefinierte Kategorie 56",
    "CUSTOM_57": "Benutzerdefinierte Kategorie 57",
    "CUSTOM_58": "Benutzerdefinierte Kategorie 58",
    "CUSTOM_59": "Benutzerdefinierte Kategorie 59",
    "CUSTOM_60": "Benutzerdefinierte Kategorie 60",
    "CUSTOM_61": "Benutzerdefinierte Kategorie 61",
    "CUSTOM_62": "Benutzerdefinierte Kategorie 62",
    "CUSTOM_63": "Benutzerdefinierte Kategorie 63",
    "CUSTOM_64": "Benutzerdefinierte Kategorie 64",
    "CUSTOM_65": "Benutzerdefinierte Kategorie 65",
    "CUSTOM_66": "Benutzerdefinierte Kategorie 66",
    "CUSTOM_67": "Benutzerdefinierte Kategorie 67",
    "CUSTOM_68": "Benutzerdefinierte Kategorie 68",
    "CUSTOM_69": "Benutzerdefinierte Kategorie 69",
    "CUSTOM_70": "Benutzerdefinierte Kategorie 70",
    "CUSTOM_71": "Benutzerdefinierte Kategorie 71",
    "CUSTOM_72": "Benutzerdefinierte Kategorie 72",
    "CUSTOM_73": "Benutzerdefinierte Kategorie 73",
    "CUSTOM_74": "Benutzerdefinierte Kategorie 74",
    "CUSTOM_75": "Benutzerdefinierte Kategorie 75",
    "CUSTOM_76": "Benutzerdefinierte Kategorie 76",
    "CUSTOM_77": "Benutzerdefinierte Kategorie 77",
    "CUSTOM_78": "Benutzerdefinierte Kategorie 78",
    "CUSTOM_79": "Benutzerdefinierte Kategorie 79",
    "CUSTOM_80": "Benutzerdefinierte Kategorie 80",
    "CUSTOM_81": "Benutzerdefinierte Kategorie 81",
    "CUSTOM_82": "Benutzerdefinierte Kategorie 82",
    "CUSTOM_83": "Benutzerdefinierte Kategorie 83",
    "CUSTOM_84": "Benutzerdefinierte Kategorie 84",
    "CUSTOM_85": "Benutzerdefinierte Kategorie 85",
    "CUSTOM_86": "Benutzerdefinierte Kategorie 86",
    "CUSTOM_87": "Benutzerdefinierte Kategorie 87",
    "CUSTOM_88": "Benutzerdefinierte Kategorie 88",
    "CUSTOM_89": "Benutzerdefinierte Kategorie 89",
    "CUSTOM_90": "Benutzerdefinierte Kategorie 90",
    "CUSTOM_91": "Benutzerdefinierte Kategorie 91",
    "CUSTOM_92": "Benutzerdefinierte Kategorie 92",
    "CUSTOM_93": "Benutzerdefinierte Kategorie 93",
    "CUSTOM_94": "Benutzerdefinierte Kategorie 94",
    "CUSTOM_95": "Benutzerdefinierte Kategorie 95",
    "CUSTOM_96": "Benutzerdefinierte Kategorie 96",
    "CUSTOM_97": "Benutzerdefinierte Kategorie 97",
    "CUSTOM_98": "Benutzerdefinierte Kategorie 98",
    "CUSTOM_99": "Benutzerdefinierte Kategorie 99",
    "CUSTOM_AUP_FREQUENCY": "Benutzerdefinierte AUP-Häufigkeit (Tage)",
    "CUSTOM_DNS_SERVER": "Benutzerdefinierter DNS-Server",
    "CUSTOM_OPTION": "Benutzerdefinierte Option",
    "CUSTOM": "Benutzerdefiniert",
    "CUSTOMIZE_COLS": "Spalten anpassen",
    "CUSTOMIZE_COLUMNS": "Spalten anpassen",
    "CYPRUS_ASIA_NICOSIA": "Asien/Nicosia",
    "CYPRUS": "Zypern",
    "CZECH_REPUBLIC_EUROPE_PRAGUE": "Europa / Prag",
    "CZECH_REPUBLIC": "Tschechien",
    "CZECHIA": "Tschechien",
    "DASHBOARD": "Dashboard",
    "Data Centers": "Rechenzentren",
    "DATA_CENTER": "Rechenzentrum",
    "DATA_CENTERS": "Rechenzentren",
    "DATA_COLLECTION": "Datenerfassung",
    "DATA_CONNECTION_FAIL": "SVPN-Datenverbindung fehlgeschlagen.",
    "DATA_TYPE": "Datentyp",
    "DATACENTER": "Rechenzentrum",
    "DAYS": "Tage",
    "DC": "Public Service Edge",
    "DEDICATED_BANDWIDTH": "Dedizierte Bandbreite",
    "DEFAUL_GATEWAY_IP_ADDRESS": "Standard-Gateway-IP-Adresse",
    "DEFAULT_AWS_REGIONS": "Alle Regionen",
    "DEFAULT_AZURE_REGIONS": "Keine",
    "DEFAULT_GATEWAY": "Standard-Gateway",
    "DEFAULT_GW": "Standard-Gateway",
    "DEFAULT_LEASE_TIME": "Standard-Lease-Dauer (Sek.)",
    "DEFAULT_NAMESPACE": "Standard",
    "DEFAULT_PREFIX": "Standard-Präfix",
    "DEFAULT_REGIONS": "Alle Standorte",
    "DEFAULT_ROUTE_CAN_NOT_BE_SET_AS_STATIC_ROUTE": "Die Standardroute konnte nicht als statische Route verwendet werden.",
    "DEFAULT": "Standardwert",
    "DEFINITION": "Definition",
    "DELETE_5G_DEPLOYMENT_CONFIGURATION": "Soll diese Deployment-Konfiguration wirklich gelöscht werden? Deployments können nur gelöscht werden, wenn sie mit keiner Connector-Gruppe verknüpft sind. Die Änderungen können nicht rückgängig gemacht werden. ",
    "DELETE_5G_USER_PLANE": "Soll diese Benutzerebenen-Funktion wirklich gelöscht werden? Die Änderungen können nicht rückgängig gemacht werden. ",
    "DELETE_ACCOUNT_DESCRIPTION": "Das Löschen des Kontos führt dazu, dass Zscaler alle Zugangsdaten für Ihr Konto entfernt. Sie müssen die Berechtigungen erneut erteilen, wenn Sie dieses Konto wieder hinzufügen wollen.",
    "DELETE_ACCOUNT": "Konto löschen",
    "DELETE_ACCOUNTS": "Konto löschen",
    "DELETE_ADMIN_MESSAGE": "Soll dieser Administrator auch aus der Userliste entfernt werden?{1}Dieser Vorgang {2}kann nicht rückgängig gemacht werden.{3}",
    "DELETE_API_KEY_CONFIRMATION_MESSAGE": "Das Löschen des API-Schlüssels macht ihn sofort ungültig. Dies kann nicht rückgängig gemacht werden.",
    "DELETE_API_KEY_CONFIRMATION_TITLE": "API-Schlüssel löschen",
    "DELETE_API_KEY_TOOLTIP": "Den API-Schlüssel löschen",
    "DELETE_APPLICATION": "Anwendung löschen",
    "DELETE_BC_CONFIRMATION": "Soll der folgende Cloud Connector wirklich gelöscht werden? Die Änderungen können nicht rückgängig gemacht werden.",
    "DELETE_BC_GROUP_CONFIRMATION_ALERT": "Die VM wird dadurch nicht gelöscht. Achten Sie darauf, die Ressourcen getrennt zu löschen.",
    "DELETE_BC_GROUP_CONFIRMATION": "Soll die folgende Branch Connector-Gruppe wirklich gelöscht werden? Die Änderungen können nicht rückgängig gemacht werden.",
    "DELETE_BC_GROUP": "Branch Connector-Gruppe löschen",
    "DELETE_CC_CONFIRMATION": "Soll der folgende Cloud Connector wirklich gelöscht werden? Die Änderungen können nicht rückgängig gemacht werden.",
    "DELETE_CC_GROUP_CONFIRMATION_ALERT": "Die VM wird dadurch nicht aus der öffentlichen Cloud gelöscht. Achten Sie darauf, die Ressourcen getrennt zu löschen.",
    "DELETE_CC_GROUP_CONFIRMATION": "Soll die folgende Cloud Connector-Gruppe wirklich gelöscht werden? Die Änderungen können nicht rückgängig gemacht werden.",
    "DELETE_CC_GROUP": "Cloud Connector-Gruppe löschen",
    "DELETE_CONFIRMATION_MESSAGE": "Bestätigen Sie, dass Sie die Ressource(n) löschen wollen.",
    "DELETE_CONFIRMATION_MESSAGE1": "Soll diese Ressource wirklich gelöscht werden?",
    "DELETE_CONFIRMATION_MESSAGE2": "Sollen diese Ressourcen wirklich gelöscht werden?",
    "DELETE_CONFIRMATION": "Löschung bestätigen",
    "DELETE_DATA_OLLECTION_TEXT": "Das Löschen des Kontos führt dazu, dass Zscaler alle Zugangsdaten für Ihr Konto entfernt. Sie müssen die Berechtigungen erneut erteilen, wenn Sie dieses Konto wieder hinzufügen wollen.",
    "DELETE_DATA_OLLECTION": "Datensammlung löschen",
    "DELETE_GROUP_CONFIRMATION": "Gruppenbestätigung löschen",
    "DELETE_GROUP_MESSAGE_WITH_CC_GROUP": "Diese Kontogruppe ist mit {1} Konto/Konten und {2} Cloud Connector-Gruppe(n) verknüpft. Wenn Sie diese Kontogruppe löschen, führt das dazu, dass die {2} Cloud Connector-Gruppe(n) keine Workload-Tags aus den {1} Konten verwenden können.",
    "DELETE_GROUP_MESSAGE": "{1}Soll diese Gruppe wirklich gelöscht werden?{2}\n\nWenn Sie diese Gruppe löschen, wird sie aus allen relevanten Richtlinien entfernen. Prüfen Sie die Auswirkungen sorgfältig, bevor Sie die Gruppe löschen.",
    "DELETE_GROUP": "Gruppe löschen",
    "DELETE_INTERFACE_TEXT": "Soll die Schnittstellenkonfiguration wirklich gelöscht werden?\n\nDiese Aktion kann nicht rückgängig gemacht werden.",
    "DELETE_INTERFACE": "Schnittstelle löschen",
    "DELETE_PORT_TEXT": "Soll der Port mit der gesamten Schnittstellenkonfiguration wirklich gelöscht werden?\n\nDiese Aktion kann nicht rückgängig gemacht werden.",
    "DELETE_PORT": "Port löschen",
    "DELETE_TENANT_DESCRIPTION": "Das Löschen der Instanz führt dazu, dass Zscaler alle Zugangsdaten für Ihre Instanz entfernt. Sie müssen die Berechtigungen erneut erteilen, wenn Sie diese Instanz wieder hinzufügen wollen.",
    "DELETE_TENANT": "Instanz löschen",
    "DELETE_THIS_ITEM": "Dieses Element löschen",
    "DELETE_VDI_GROUP_TEXT": "Das Löschen der Gerätegruppe führt dazu, dass Zscaler alle Informationen in Bezug auf diese Gruppe entfernt.Sie müssen die Gruppe erneut einrichten, wenn Sie wieder zu diesem Konto hinzufügen wollen.",
    "DELETE_VDI_GROUP": "VDI-Gruppe löschen",
    "DELETE_ZERO_TRUST_GATEWAY": "Zero-Trust-Gateway löschen",
    "DELETE_ZTG_DESCRIPTION": "Das Löschen des Zero-Trust-Gateways führt dazu, dass Zscaler alle Zugangsdaten für Ihr Gateway entfernt. Sie müssen die Berechtigungen erneut erteilen, wenn Sie dieses Gateway wieder hinzufügen wollen.",
    "DELETE": "Löschen",
    "DELETING": "Löschen läuft",
    "DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA": "Afrika/Kinshasa",
    "DENIED": "Verweigert",
    "DENMARK_EUROPE_COPENHAGEN": "Europa / Kopenhagen",
    "DENMARK": "Dänemark",
    "DEPARTMENT": "Abteilung",
    "DEPLOY_AS_GATEWAY": "Als Gateway bereitstellen",
    "DEPLOY_NSS_VIRTUAL_APPLIANCE": "Virtuelle NSS-Appliance bereitstellen",
    "DEPLOYED_FILTERED": "Bereitgestellt (gefiltert)",
    "DEPLOYED_OTHER_REGION": "Bereitgestellte andere Regionen",
    "DEPLOYED": "Bereitgestellt",
    "DEPLOYMENT_CONFIGURATION_NAME": "Name der Deployment-Konfiguration",
    "DEPLOYMENT_CONFIGURATION_WITH_ZPA": "Einstiegs-Bereitstellungsvorlage mit Lastenausgleich",
    "DEPLOYMENT_CONFIGURATION": "Einstiegs-Bereitstellungsvorlage mit Lastenausgleich",
    "DEPLOYMENT_DETAILS": "Bereitstellungsdetails",
    "DEPLOYMENT_NAME": "Deployment-Name",
    "DEPLOYMENT_STATUS": "Bereitstellungsstatus",
    "DEPLOYMENT_TEMPLATES_DEPRECATED": "Die Bereitstellungsvorlagen sind auf der öffentlichen Zscaler {0}GitHub{1}-Seite unter den unten aufgeführten Links verfügbar. Die Änderungen werden auf diesen Seiten protokolliert. Nähere Informationen dazu finden Sie im {2}Zscaler-Hilfeportal{3}.",
    "DEPLOYMENT_TEMPLATES": "Bereitstellungsvorlagen",
    "DEPLOYMENT_TYPE": "Bereitstellungsart",
    "DESCRIPTION_MAX_LIMIT_ERROR": "Dieses Feld darf nicht mehr als 10.240 Zeichen enthalten",
    "DESCRIPTION_OPTIONAL": "Beschreibung optional",
    "DESCRIPTION_PARENTHESIS_OPTIONAL": "Beschreibung (optional)",
    "DESCRIPTION": "Beschreibung",
    "DESELECT_ALL": "Alle Auswahlen aufheben",
    "DESIRED_CAPACITY": "Gewünschte Kapazität",
    "DESTINATION_ADDRESSES": "Zieladressen",
    "DESTINATION_COUNTRIES": "Zielländer",
    "DESTINATION_COUNTRY": "Zielland",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_ACCDRESSES": "Ziel-FQDN/Domains",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_GROUP": "Ziel-FQDN/Domain-Gruppen",
    "DESTINATION_GROUPS": "Zielgruppen",
    "DESTINATION_IP_AND_FQDN_ACCDRESSES": "Ziel-IP-Adresse/FQDN",
    "DESTINATION_IP_AND_FQDN_GROUPS": "Ziel-IP-/FQDN-Gruppe",
    "DESTINATION_IP_AND_FQDN": "Ziel-IP/FQDN",
    "DESTINATION_IP_GROUP": "Zielgruppe",
    "DESTINATION_IP_GROUPS": "Ziel-IP-Gruppen",
    "DESTINATION_IP": "Ziel-IP",
    "DESTINATION_IPV4_GROUPS": "Ziel-IPv4-Gruppen",
    "DESTINATION_STATUS": "Zielstatus",
    "DESTINATION": "Reiseziel",
    "DEVICE_APP_VER": "Geräte-App-Version",
    "DEVICE_CRITERIA": "Gerätekriterien",
    "DEVICE_DETAILS": "Gerätedetails",
    "DEVICE_GROUP_TYPE": "Gerätegruppentyp",
    "DEVICE_HOST_NAME": "Geräte-Hostname",
    "DEVICE_ID": "Geräte ID",
    "DEVICE_INFO": "Geräteinformationen",
    "DEVICE_METRICS": "Ressource",
    "DEVICE_MODEL": "Gerätemodell",
    "DEVICE_NAME": "Gerätename",
    "DEVICE_OS_TYPE": "Geräte-Betriebssystemtyp",
    "DEVICE_OS_VER": "Geräte-Betriebssystemversion",
    "DEVICE_OWNER": "Geräteeigentümer",
    "DEVICE_PLATFORM": "Geräteplattform",
    "DEVICE_PORT": "Geräteport",
    "DEVICE_SELECTION": "Geräteauswahl",
    "DEVICE_SERIAL_NO": "Geräte-Seriennummer",
    "DEVICE_SERIAL_NUMBER": "Geräte-Seriennummer",
    "DEVICE_TYPE": "Gerätetyp",
    "DEVICES_CRITERIA_TEXT": "Wählen Sie die Kriterien aus, die zum Gruppieren der VDI-Geräte verwendet werden sollen.",
    "DEVICES_CRITERIA": "Gerätekriterien",
    "DEVICES": "Geräte",
    "DEVO": "Devo",
    "DHCP_ADDRESS_RANGE": "DHCP-Adressbereich",
    "DHCP_DESC": "Das DHCP-Protokoll wird verwendet, um automatisch die Netzwerkparameter  einer Station zu konfigurieren",
    "DHCP_MANAGEMENT_IP": "DHCP-Verwaltungs-IP",
    "DHCP_OPTIONS": "DHCP-Optionen",
    "DHCP_SERVER": "DHCP-Server",
    "DHCP_SERVICE_IP": "DHCP-Dienst-IP",
    "DHCP": "DHCP",
    "DINING_AND_RESTAURANT": "Gastronomie & Restaurants",
    "DIRECT_THROUGHPUT_KBPS_SESSION": "Direkt (Durchsatz kbit/s pro Sitzung)",
    "DIRECT": "DIRECT",
    "DIRECTION": "TS-Richtung",
    "DIRECTORY_ID": "Verzeichnis-ID",
    "DISABLE_BRANCH_CONNECTOR_CONFIRMATION": "Soll dieser Branch Connector wirklich deaktiviert werden?",
    "DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Soll diese Branch Connector-Gruppe wirklich deaktiviert werden? Damit werden alle {0} Cloud Connectors deaktiviert, die zu dieser Gruppe gehören.",
    "DISABLE_BRANCH_CONNECTOR_GROUP": "Branch Connector-Gruppe deaktivieren",
    "DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Sollen wirklich alle {0} ausgewählten Branch Connectors deaktiviert werden?",
    "DISABLE_BRANCH_CONNECTOR_SELECTED": "Alle ausgewählten Branch Connectors deaktivieren",
    "DISABLE_BRANCH_CONNECTOR": "Branch Connector deaktivieren",
    "DISABLE_CLOUD_CONNECTOR_CONFIRMATION": "Soll dieser Cloud Connector wirklich deaktiviert werden?",
    "DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Soll diese Cloud Connector-Gruppe wirklich deaktiviert werden? Damit werden alle {0} Cloud Connectors deaktiviert, die zu dieser Gruppe gehören.",
    "DISABLE_CLOUD_CONNECTOR_GROUP": "Cloud Connector-Gruppe deaktivieren",
    "DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Sollen wirklich alle {0} ausgewählten Cloud Connectors deaktiviert werden?",
    "DISABLE_CLOUD_CONNECTOR_SELECTED": "Alle ausgewählten Cloud Connectors deaktivieren",
    "DISABLE_CLOUD_CONNECTOR": "Cloud Connector deaktivieren",
    "DISABLE_DATA_COLLECTION_DESCRIPTION": "Durch Deaktivieren der Synchronisierung wird Zscaler daran gehindert, die neuesten Tag-Informationen abzurufen. Außerdem können die Ressourcen dieses Kontos nicht in Workload-Gruppen aufgenommen werden.",
    "DISABLE_DATA_COLLECTION": "Datenerfassung deaktivieren",
    "DISABLE_POLICY_INFORMATION": "Weiterleitungsinformationen deaktivieren",
    "DISABLE_TIPS_MESSAGE": "Mein Profil, um Richtlinieninformationen zu deaktivieren",
    "DISABLE": "Deaktivieren",
    "DISABLED": "Deaktiviert",
    "DISABLING": "Deaktivieren",
    "DISCOVERY_SERVICE_STATUS": "Erkennungsdienst-Status",
    "DISCUSSION_FORUMS": "Diskussionsforen",
    "DISK_STORAGE": "Festplattenspeicher",
    "DISMISS": "Schließen",
    "DISPLAY": "Anzeige",
    "DJIBOUTI_AFRICA_DJIBOUTI": "Afrika / Dschibuti",
    "DJIBOUTI": "Dschibuti",
    "DNS_ACTIONS": "Aktionen",
    "DNS_ACTIVITY": "DNS-Aktivität",
    "DNS_APPLICATION_CATEGORIES": "DNS-Tunnel- und Netzwerk-App-Kategorien",
    "DNS_APPLICATION_CATEGORY": "DNS-Anwendungskategorie",
    "DNS_APPLICATION_GROUP": "DNS-Anwendungsgruppe",
    "DNS_APPLICATION": "DNS-Anwendung",
    "DNS_BLOCKED_TRAFFIC_OVERVIEW": "Blockierter DNS-Verkehr - Überblick",
    "DNS_CACHE": "DNS-Cache",
    "DNS_CONTROL_RECOMMENDED_POLICY": "Empfohlene DNS Kontrolle Regel",
    "DNS_CONTROL_TIPS_DESC": "Sie können Regeln zum Steuern von DNS-Anfragen und -Antworten definieren.",
    "DNS_CONTROL_TIPS_TITLE": "DNS-Steuerrichtlinie konfigurieren",
    "DNS_CONTROL": "DNS-Steuerung",
    "DNS_DESC": " Das DNS-Protokoll wird verwendet, um Internet-Namen (www.site.com) in IP-Adressen und umgekehrt zu übersetzen.",
    "DNS_DESTINATION": "Reiseziel",
    "DNS_DETAILS": "DNS-Details",
    "DNS_ERROR_CODE": "DNS-Fehlercode",
    "DNS_ERROR_STATUS": "DNS-Fehlerstatus",
    "DNS_FILTERING_RULE": "DNS-Filterregel",
    "DNS_FILTERING": "DNS-Filterung",
    "DNS_FILTERS": "DNS-Filter",
    "DNS_GATEWAY": "DNS-Gateway",
    "DNS_INSIGHTS": "DNS-Einblicke",
    "DNS_IPV6_CHANGE": "Korrekte Verarbeitung von IPv6-Anfragen an DNS zulassen",
    "DNS_MONITOR": "DNS-Überwachung",
    "DNS_NETWORK_APPLICATION": "DNS-Tunnel und Netzwerk-Apps",
    "DNS_NW_APP_CATEGORY": "DNS-Tunnel- und Netzwerk-App-Kategorien",
    "DNS_NW_APP": "DNS-Tunnel und Netzwerk-Apps",
    "DNS_OVER_HTTPS": "DNS über HTTPS-Dienste",
    "DNS_OVERVIEW": "DNS-Überblick",
    "DNS_POLICIES": "DNS-Richtlinien",
    "DNS_POLICY": "DNS-Richtlinie",
    "DNS_REQ_RESP_ACTION": "Aktion",
    "DNS_REQ_TYPE": "DNS-Anfragetyp",
    "DNS_REQUEST_TYPE": "DNS-Anfragetyp",
    "DNS_REQUEST_TYPES": "DNS-Anfragetypen",
    "DNS_RES_TYPE": "DNS-Antworttyp",
    "DNS_RESOLVED_BY_ZPA": "Von ZPA aufgelöst",
    "DNS_RESOLVER": "Auflöser",
    "DNS_RESPONSE_CODES": "DNS-Antwortcodes",
    "DNS_RESPONSE_TYPE": "DNS-Antworttyp",
    "DNS_RESPONSE": "DNS-Antwort",
    "DNS_RESPONSES": "DNS-Antworten",
    "DNS_RULE_NAME_DEFAULT": "DNS_{0}",
    "DNS_RULE_NAME": "DNS-Regelname",
    "DNS_RULE": "Regelname",
    "DNS_SERVER_IP_ADDRESS": "DNS-Server-IP-Adresse",
    "DNS_SERVER_IP_ADDRESS1": "DNS-Server-IP-Adresse 1",
    "DNS_SERVER_IP_ADDRESS2": "DNS-Server-IP-Adresse 2",
    "DNS_SERVER_IP_ADDRESSES": "DNS-Server-IP-Adressen",
    "DNS_SERVER_IP_GROUPS": "DNS-Server-IP-Gruppen",
    "DNS_SERVER_ONE": "DNS-Server 1",
    "DNS_SERVER_TWO_OPTIONAL": "DNS-Server 2 (optional)",
    "DNS_SERVER_TWO": "DNS-Server 2",
    "DNS_SERVER": "DNS-Server",
    "DNS_SERVICES": "DNS-Dienste",
    "DNS_SOURCE": "Quelle",
    "DNS_TCP_PORTS_DNS_SPECIFIC": "DNS-Ports (TCP) für DNS-spezifische Regeln",
    "DNS_TIMEOUT": "Zeitüberschreitung bei der DNS-Auflösung",
    "DNS_TOP_BLOCKED_BY_LOCATION": "Blockierter Verkehr nach Standort",
    "DNS_TOP_BLOCKED_BY_RULE": "Blockierter Verkehr nach Regel",
    "DNS_TOP_BLOCKED_BY_USER": "Blockierter Verkehr nach Benutzer",
    "DNS_TRAFFIC_BY_DEPARTMENT": "DNS-Verkehr nach Abteilung",
    "DNS_TRAFFIC_BY_LOCATION": "Verkehr nach Standort",
    "DNS_TRAFFIC_BY_USER": "Verkehr nach Benutzer",
    "DNS_TRAFFIC_OVERVIEW": "DNS-Verkehr gesamt - Überblick",
    "DNS_TRANSACTION_POLICY": "DNS-Transaktionsrichtlinie",
    "DNS_TRANSACTION_RULE": "DNS-Transaktionsregel",
    "DNS_TRANSACTION_TREND": "DNS-Transaktionstrends",
    "DNS_TRANSACTION": "Transaktion",
    "DNS_UDP_PORTS_DNS_SPECIFIC": "DNS-Ports (UDP) für DNS-spezifische Regeln",
    "DNS": "DNS",
    "DNSLOG": "DNS Logs",
    "DNSREQ_A": "Eine Hostadresse",
    "DNSREQ_AAAA": "IP6-Adresse",
    "DNSREQ_AFSDB": "Für AFS-Datenbankstandort",
    "DNSREQ_CNAME": "Der kanonische Name für ein Alias",
    "DNSREQ_DNSKEY": "Öffentlicher DNS-Schlüssel",
    "DNSREQ_DS": "Unterzeichner der Delegation",
    "DNSREQ_HINFO": "Hostinformationen",
    "DNSREQ_HIP": "Host-Identitätsprotokoll",
    "DNSREQ_ISDN": "Für ISDN-Adresse",
    "DNSREQ_LOC": "Information über Standort",
    "DNSREQ_MB": "Ein Mailbox-Domänenname",
    "DNSREQ_MG": "Ein E-Mail-Gruppenmitglied",
    "DNSREQ_MINFO": "Mailbox- oder E-Mail-Listeninformationen",
    "DNSREQ_MR": "Ein Domänenname zum Umbenennen von E-Mails",
    "DNSREQ_MX": "Mail Exchange",
    "DNSREQ_NAPTR": "Naming Authority Pointer",
    "DNSREQ_NS": "Ein autoritativer Namensserver",
    "DNSREQ_NSEC": "DNS-Sicherheitserweiterungen",
    "DNSREQ_PTR": "Ein Domänennamen-Pointer",
    "DNSREQ_RP": "Für verantwortliche Person",
    "DNSREQ_RT": "Für die Weiterleitung",
    "DNSREQ_SOA": "Markiert den Beginn einer Autoritätszone",
    "DNSREQ_SRV": "Serverauswahl",
    "DNSREQ_TXT": "Textzeichenfolgen",
    "DNSREQ_UNKNOWN": "DNS-Typ nicht von ZS-Firewall zugeordnet",
    "DNSREQ_WKS": "Eine bekannte Dienstbeschreibung",
    "DNSRES_CNAME": "Antworttyp ist CNAME",
    "DNSRES_IPV4": "Antworttyp ist IPV4",
    "DNSRES_IPV6": "Antworttyp ist IPV6",
    "DNSRES_SRV_CODE": "Für Antworttyp ist Serverfehlercode eingerichtet",
    "DNSRES_ZSCODE": "Für Antworttyp ist benutzerdefinierter Zscaler-Code eingerichtet",
    "DOES_NOT_CONTAINS": "Enthält nicht",
    "DOES_NOT_ENDS_WITH": "Endet nicht mit",
    "DOES_NOT_STARTS_WITH": "Beginnt nicht mit",
    "DOHTTPS_RULE": "DNS über HTTPS",
    "DOMAIN_CATEGORY": "Domänenkategorie",
    "DOMAIN_NAME": "Domänenname",
    "DOMAIN": "Domäne",
    "DOMAINS": "Domains",
    "DOMINICA_AMERICA_DOMINICA": "Amerika/Dominica",
    "DOMINICA": "Dominica",
    "DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO": "Amerika/Santo Domingo",
    "DOMINICAN_REPUBLIC": "Dominikanische Republik",
    "DONE": "Erledigt",
    "DONT_SHOW_AGAIN": "Nicht wieder anzeigen",
    "DOWN": "Abwärts",
    "DOWNLOAD_AWS_CLOUDFORMATION_TEMPLATE": "AWS Cloudformation-Vorlage herunterladen",
    "DOWNLOAD_CERTIFICATE": "Zertifikat herunterladen",
    "DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION": "Cloudformation-Vorlage zur späteren Ausführung herunterladen",
    "DOWNLOAD_CSV": "Herunterladen (.csv)",
    "DOWNLOAD_ERROR": "Upgrade konnte nicht aus der Zscaler-Cloud heruntergeladen werden. Cloud Connector im gesunden Zustand",
    "DOWNLOAD_MBPS": "Download (Mbps)",
    "DOWNLOAD_MIB_FILES": "MIB-Dateien herunterladen",
    "DOWNLOAD_PROGRESS": "Downloadfortschritt",
    "DOWNLOAD": "Herunterladen",
    "DPDRCV": "Empfangene DPD",
    "DR_CONGO": "DR Kongo",
    "DROP": "verwerfen",
    "DSTN_DOMAIN": "Wildcard-Domäne",
    "DSTN_FQDN": "FQDN",
    "DSTN_IP": "IP-Adresse",
    "DSTN_OTHER": "Sonstiges",
    "DSTN_WILDCARD_FQDN": "Wildcard-Domäne",
    "DUPLICATE_IP_ADDRESS": "Diese IP-Adresse wird bereits verwendet",
    "DUPLICATE_IP_ADDRESSES": "Doppelte IP-Adressen",
    "DUPLICATE_ITEM": "Der angegebene Name wird bereits verwendet",
    "DUPLICATE_VLAN_ID": "Doppelte VLAN-ID.",
    "DYNAMIC_DNS": "Dynamischer DNS-Host",
    "DYNAMIC_LOCATION_GROUPS": "Dynamische Standortgruppen",
    "DYNAMIC": "Dynamisch",
    "EASTASIA": "(Asien-Pazifikraum) Ostasien",
    "EASTASIASTAGE": "(Asien-Pazifikraum) Ostasien (Stufe)",
    "EASTUS": "(US) USA Ost",
    "EASTUS2": "(US) USA Ost 2",
    "EASTUS2EUAP": "(US) USA Ost 2 EUAP",
    "EASTUS2STAGE": "(USA) USA Ost 2 (Stufe)",
    "EASTUSSTAGE": "(USA) USA Ost (Stufe)",
    "EBS_STORAGE": "EBS-Speicher",
    "EC_ACC_ID": "AWS-Konto-ID",
    "EC_AVAILABILITY_ZONE": "Verfügbarkeitszone",
    "EC_AWS_AVAILABILITY_ZONE": "AWS-Verfügbarkeitszone",
    "EC_AWS_REGION": "AWS-Region",
    "EC_AZURE_AVAILABILITY_ZONE": "Azure-Verfügbarkeitszone",
    "EC_DEVICE_APP_VERSION": "Geräte-App-Version",
    "EC_DEVICE_HOSTNAME": "Geräte-Hostname",
    "EC_DEVICE_ID": "Gerätename",
    "EC_DEVICE_OS_TYPE": "Geräte-Betriebssystemtyp",
    "EC_DEVICE_TYPE": "Gerätetyp",
    "EC_DNS_GW_FLAG": "DNS-Gateway-Markierung",
    "EC_DNS_GW_NAME": "DNS-Gateway-Name",
    "EC_DNS": "CC-DNS",
    "EC_DNSLOG": "DNS Logs",
    "EC_EVENTLOG": "EREIGNIS",
    "EC_FORWARDING_TYPE": "Weiterleitungsart",
    "EC_FW_RULE": "Weiterleitungsregel",
    "EC_GROUP": "Cloud Connector-Gruppe",
    "EC_INSTANCE_NAME": "Cloud Connector-Instanz",
    "EC_INSTANCE": "Cloud Connector-Instanz",
    "EC_PLATFORM": "Plattform",
    "EC_PROJECT_ID": "GCP-Projekt-ID",
    "EC_RDRRULESLOT": "Traffic-Weiterleitungsregeln",
    "EC_SELFRULESLOT": "Protokollierungs- und Steuerungsregeln für Weiterleitung",
    "EC_SOURCE_IP": "Cloud Connector-Quell-IP",
    "EC_SOURCE_PORT": "Cloud Connector-Quellport",
    "EC_SUBSCRIPTION_ID": "Azure-Abonnement-ID",
    "EC_TRAFFIC_DIRECTION": "Anfrage-Typ",
    "EC_TRAFFIC_TYPE": "Verkehrsart",
    "EC_TS_DIRECTION": "TS-Richtung",
    "EC_UI": "Cloud Connector-UI",
    "EC_VM": "Cloud Connector-VM",
    "EC_VMNAME": "Cloud Connector-VM-Name",
    "EC2_INSTANCE_TYPE": "EC2-Instanztyp",
    "ECDIRECTSCTPXFORM": "Direkt mit SCTP-Übersetzung",
    "ECHO_DESC": "Echo-Protokoll ist ein Dienst der Internet Protocol Suite, wie unter RFC 862 definiert. Es wurde ursprünglich für die Prüfung und Messung der Roundtrip-Zeiten in IP-Netzwerken verwendet.",
    "ECHO": "Echo",
    "ECHOREP": "Echo-Antwort",
    "ECHOREQ": "Echo-Anfrage",
    "ECHOSIGN": "AdobeEchoSign",
    "ECLOG": "Sitzungsprotokolle",
    "ECSELF": "GW für vom Konnektor ausgehenden Traffic",
    "ECUADOR_AMERICA_GUAYAQUIL": "Amerika/Guayaquil",
    "ECUADOR_PACIFIC_GALAPAGOS": "Pacific/Galapagos",
    "ECUADOR": "Ecuador",
    "ECZPA": "ZPA",
    "ECZPASCTPXFORM": "ZPA mit SCTP-Übersetzung",
    "ECZPAXSCTPXFORM": "Tunnel zum Konnektor-ZPA mit SCTP-Transformation",
    "EDGE_CONNECTOR_ADMIN_MANAGEMENT": "Administratorverwaltung",
    "EDGE_CONNECTOR_ADMIN": "Cloud Connector-Administrator",
    "EDGE_CONNECTOR_CCA_DEVICE": "CCA",
    "EDGE_CONNECTOR_CLOUD_PROVISIONING": "Cloud Connector-Bereitstellung",
    "EDGE_CONNECTOR_DASHBOARD": "Dashboard",
    "EDGE_CONNECTOR_FORWARDING": "Weiterleitung (Verkehr, DNS und Protokolle)",
    "EDGE_CONNECTOR_LOCATION_MANAGEMENT": "Standortverwaltung",
    "EDGE_CONNECTOR_NSS_CONFIGURATION": "NSS-Protokollierung",
    "EDGE_CONNECTOR_POLICY_CONFIGURATION": "Richtlinienkonfiguration",
    "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Konfigurationsverwaltung Öffentliche Cloud",
    "EDGE_CONNECTOR_TEMPLATE": "Vorlage (Standort & Bereitstellung)",
    "EDGE_CONNECTOR_VM_SIZE": "VM-Größe",
    "EDGE_ONLY": "Nur Edge",
    "EDGECONNECTOR_NAME": "Cloud Connector-Name",
    "EDIT_5G_DEPLOYMENT": "Deployment-Konfiguration bearbeiten",
    "EDIT_API_KEY_TOOLTIP": "API-Schlüssel bearbeiten",
    "EDIT_APPLIANCE": "Appliance bearbeiten",
    "EDIT_AWS_ACCOUNT": "AWS-Konto bearbeiten",
    "EDIT_AWS_CLOUD_ACCOUNT": "AWS Cloud-Konto bearbeiten",
    "EDIT_AWS_GROUP": "AWS-Gruppe bearbeiten",
    "EDIT_AZURE_CLOUD_ACCOUNT": "Azure Cloud-Konto bearbeiten",
    "EDIT_AZURE_TENANT": "Azure-Instanz bearbeiten",
    "EDIT_BC_PROVISIONING_TEMPLATE": "Branch Connector-Bereitstellungsvorlage bearbeiten",
    "EDIT_CLOUD_APP_PROVIDER": "Cloud-App-Anbieter bearbeiten",
    "EDIT_CLOUD_CONNECTOR_ADMIN": "Cloud Connector-Administrator bearbeiten",
    "EDIT_CLOUD_CONNECTOR_ROLE": "Cloud Connector-Rolle bearbeiten",
    "EDIT_CLOUD_CONNECTOR": "Cloud Connector bearbeiten",
    "EDIT_CLOUD_CONNECTORS": "Connectors bearbeiten",
    "EDIT_CLOUD_SERVICE_API_KEY": "Cloud-Dienst-API-Schlüssel bearbeiten",
    "EDIT_CONFIRMATION_DEPLOYED_GATEWAY": "Dieses Gerät befindet sich im Status „Bereitgestellt“. Änderungen an der Konfiguration können sich auf den Traffic auswirken. Wollen Sie wirklich fortfahren?",
    "EDIT_CONFIRMATION_PREDEFINED_RULE": "Diese vordefinierte Regel gilt nur für BC-Gruppen/Standorte im Gateway-Modus.",
    "EDIT_CONFIRMATION": "Bearbeitungsbestätigung",
    "EDIT_CONNECTORS": "Connectors bearbeiten",
    "EDIT_DESTINATION_IP_GROUP": "Ziel-IP-Gruppe bearbeiten",
    "EDIT_DNS_GATEWAY": "DNS-Gateway bearbeiten",
    "EDIT_DNS_POLICIES": "DNS-Filterregel bearbeiten",
    "EDIT_DYNAMIC_VDI_GROUP": "Dynamische VDI-Gruppe bearbeiten",
    "EDIT_EC_NSS_CLOUD_FEED": "NSS Cloud-Feed bearbeiten",
    "EDIT_EC_NSS_FEED": "NSS-Feed bearbeiten",
    "EDIT_EC_NSS_SERVER": "NSS-Server bearbeiten",
    "EDIT_EDGECONNECTOR": "Cloud Connector bearbeiten",
    "EDIT_IP_POOL": "IP-Pool bearbeiten",
    "EDIT_LOCATION_TEMPLATE": "Standortvorlage bearbeiten",
    "EDIT_LOCATIONS": "Standorte bearbeiten",
    "EDIT_LOG_AND_CONTROL_FORWARDING_RULE": "Protokollierungs- und Steuerungsregel für Weiterleitung bearbeiten",
    "EDIT_LOG_AND_CONTROL_GATEWAY": "Protokollierungs- und Steuerungs-Gateway bearbeiten",
    "EDIT_NETWORK_SERVICE_GROUP": "Netzwerkdienstgruppe hinzufügen",
    "EDIT_NETWORK_SERVICE": "Netzwerkdienst bearbeiten",
    "EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE": "Das Bearbeiten des API-Schlüssels macht ihn sofort ungültig. Sie müssen alle Verweise auf den alten Schlüssel durch Verweise auf den neuen ersetzen.",
    "EDIT_PHYSICAL_BRANCH_DEVICE": "Physisches Zweigstellengerät bearbeiten",
    "EDIT_PROVISIONING_TEMPLATE": "Cloud Connector-Bereitstellungsvorlage bearbeiten",
    "EDIT_SOURCE_IP_GROUP": "Quell-IP-Gruppe bearbeiten",
    "EDIT_TRAFFIC_FWD_POLICIES": "Verkehrsweiterleitungsregeln bearbeiten",
    "EDIT_UPF": "Benutzerebenenfunktion bearbeiten",
    "EDIT_VDI_AGENT_FORWARDING_PROFILE": "VDI-Weiterleitungsprofil bearbeiten",
    "EDIT_VDI_TEMPLATE": "VDI-Vorlage bearbeiten",
    "EDIT_VIRTUAL_BRANCH_DEVICE": "Virtuelles Zweigstellengerät bearbeiten",
    "EDIT_ZERO_TRUST_GATEWAY": "Zero-Trust-Gateway bearbeiten",
    "EDIT_ZIA_GATEWAY": "ZIA-Gateway bearbeiten",
    "EDIT_ZT_DEVICE": "ZT-Gerät bearbeiten",
    "EDIT": "Bearbeiten",
    "EGRESS_DETAILS": "Austrittdetails",
    "EGYPT_AFRICA_CAIRO": "Afrika / Kairo",
    "EGYPT": "Ägypten",
    "EITHER_REQ_RESP_BLOCK": "Blockieren",
    "EITHER_REQ_RESP_REDIRECT_NO_BLOCK": "Weiterleitung",
    "EL_SALVADOR_AMERICA_EL_SALVADOR": "Amerika/El Salvador",
    "EL_SALVADOR": "El Salvador",
    "EMAIL_HOST": "Webmail",
    "EMAIL": "Email",
    "EMPTY_RESP": "DNS-Antwort ist kein Fehler, hat aber leeren Antwortbereich",
    "ENABLE_AUP": "AUP aktivieren",
    "ENABLE_BRANCH_CONNECTOR_CONFIRMATION": "Soll dieser Branch Connector wirklich aktiviert werden?",
    "ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "Soll diese Branch Connector-Gruppe wirklich aktiviert werden? Damit werden alle {0} Branch Connectors deaktiviert, die zu dieser Gruppe gehören.",
    "ENABLE_BRANCH_CONNECTOR_GROUP": "Branch Connector-Gruppe aktivieren",
    "ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "Sollen wirklich alle {0} ausgewählten Branch Connectors aktiviert werden?",
    "ENABLE_BRANCH_CONNECTOR_SELECTED": "Alle ausgewählten Branch Connectors aktivieren",
    "ENABLE_BRANCH_CONNECTOR": "Branch Connector aktivieren",
    "ENABLE_CAUTION": "Warnung aktivieren",
    "ENABLE_CLOUD_CONNECTOR_CONFIRMATION": "Soll dieser Cloud Connector wirklich aktiviert werden?",
    "ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "Soll dieser Cloud Connector wirklich aktiviert werden? Damit werden alle {0} Cloud Connectors aktiviert, die zu dieser Gruppe gehören.",
    "ENABLE_CLOUD_CONNECTOR_GROUP": "Cloud Connector-Gruppe aktivieren",
    "ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "Sollen wirklich alle {0} ausgewählten Cloud Connectors aktiviert werden?",
    "ENABLE_CLOUD_CONNECTOR_SELECTED": "Alle ausgewählten Cloud Connectors aktivieren",
    "ENABLE_CLOUD_CONNECTOR": "Cloud Connector aktivieren",
    "ENABLE_DATA_COLLECTION_DESCRIPTION": "Wenn Sie die Synchronisierung aktivieren, kann Zscaler die neuesten Tag-Informationen abrufen. Und die Ressourcen aus diesem Konto werden in Workload-Gruppen eingeschlossen.",
    "ENABLE_DATA_COLLECTION": "Datenerfassung aktivieren",
    "ENABLE_FULL_ACCESS": "Vollzugriff aktivieren",
    "ENABLE_GEO_IP_LOOKUP": "GEO IP-Suche aktivieren",
    "ENABLE_IPS_CONTROL": "IPS-Steuerung aktivieren",
    "ENABLE_MOBILE_APP": "Zugriff auf Executive Insights-App",
    "ENABLE_POLICY_INFORMATION": "Weiterleitungsinformationen aktivieren",
    "ENABLE_SSL_INSPECTION": "SSL-Überprüfung aktivieren",
    "ENABLE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Surrogate IP für bekannte Browser erzwingen",
    "ENABLE_USER_IP_MAPPING": "IP-Surrogate aktivieren",
    "ENABLE_VIEW_ONLY_ACCESS": "Schreibgeschützten Zugriff aktivieren",
    "ENABLE_XFF_FORWARDING": "XFF Forwarding aktivieren",
    "ENABLE": "Aktivieren",
    "ENABLED": "Aktiviert",
    "ENABLING": "Aktivieren",
    "ENCR_WEB_CONTENT": "Benutzerdefinierter verschlüsselter Inhalt",
    "ENCRYPTED_DTLS": "DTLS",
    "END_TIME": "Endzeit",
    "END_USER_AUTHENDICATION": "Benutzerauthentifizierung beenden",
    "ENDPOINT_ID": "ID des Endgeräts",
    "ENDPOINT_SERVICE_NAME": "Dienstname des Endgeräts",
    "ENDPOINT_SEVICE_NAME": "Dienstname des Endgeräts",
    "ENDPOINTS_SEVICE_NAME": "Dienstname der Endgeräte",
    "ENDPOINTS": "Endgeräte",
    "ENDS_WITH": "Endet mit",
    "ENFORCE_AUTHENTICATION": "Authenthifizierung durchführen",
    "ENFORCE_BAND_WIDTH_CONTROL": "Bandbreitensteuerung durchsetzen",
    "ENFORCE_FIREWALL_CONTROL": "Firewall-Kontrolle anwenden",
    "ENFORCE_IPS_CONTROL": "IPS-Steuerung aktivieren",
    "ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Surrogate IP für bekannte Browser erzwingen",
    "ENFORCE_ZAPP_SSL_SETTING": "ZApp-SSL-Einstellung durchsetzen",
    "ENROLLED_DEVICE_APP_VERSION": "Version der registrierten Geräte-App",
    "ENSURE_ALL_INFORMATION_IS_CORRECT": "Stellen Sie sicher, dass die nachfolgenden Informationen korrekt sind, bevor Sie diese Cloud Connector-Bereitstellungsvorlage erstellen.",
    "ENTER_AWS_ACCOUNT_ID": "AWS-Konto-ID eingeben...",
    "ENTER_CC_ROLE_NAME": "Cloud Connector-Rollennamen eingeben",
    "ENTER_CLOUD_WATCH_GROUP_ARN": "Cloud Watch Group ARN eingeben",
    "ENTER_CUSTOM_OPTION_CODE": "Geben Sie den Code ein. Beispiel: 42",
    "ENTER_CUSTOM_OPTION_NAME": "Geben Sie den Namen der benutzerdefinierten Option ein.",
    "ENTER_DESCRIPTION_HERE": "Beschreibung eingeben (optional)",
    "ENTER_DESCRIPTION": "Beschreibung eingeben...",
    "ENTER_DEVICE_NAME": "Gerätenamen eingeben",
    "ENTER_HEADERS_PARAMETERS": "Header-Parameter eingeben",
    "ENTER_HOSTNAME_PREFIX": "Hostnamenpräfix eingeben",
    "ENTER_IP_ADDRESS_OR_FQDN": "IP-Adresse oder FQDN eingeben",
    "ENTER_IP_ADDRESS": "IP-Adresse eingeben...",
    "ENTER_LOG_INFO_TYPE": "Protokollinformationstyp eingeben",
    "ENTER_MTU": "MTU eingeben...",
    "ENTER_NAME_HERE": "Namen hier eingeben",
    "ENTER_NAME": "Namen eingeben...",
    "ENTER_NAMESPACE": "Namespace eingeben",
    "ENTER_NUMBER": "Nummer eingeben",
    "ENTER_ROLE_NAME": "Rollennamen eingeben...",
    "ENTER_TEXT": "Text eingeben...",
    "ENTER_THE_VALUE": "Geben Sie einen Wert ein",
    "ENTER_URL": "URL eingeben",
    "ENTERTAINMENT": "Unterhaltung",
    "ENTITLEMENT_STATUS_TOOLTIP": "Die Gateway-Berechtigung wird als Anzahl der Verfügbarkeitszonen (Availability Zones, AZ), für die das Konto berechtigt ist, und die aktuelle Nutzung angezeigt. Jedes Gateway verwendet zwei oder mehr AZs. Wie viele AZs von einem Gateway genutzt werden, wird beim Erstellen eines neuen Gateways festgelegt.",
    "ENTITLEMENT_STATUS": "Berechtigungsstatus",
    "EQUATORIAL_GUINEA_AFRICA_MALABO": "Afrika / Malabo",
    "EQUATORIAL_GUINEA": "Äquatorialguinea",
    "ERITREA_AFRICA_ASMARA": "Afrika / Asmara",
    "ERITREA": "Eritrea",
    "ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER": "Die Google Project-ID darf nicht in Kombination mit der AWS-Konto-ID verwendet werden",
    "ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "Azure-Abonnement-ID darf nicht in Kombination mit AWS-Konto-ID verwendet werden",
    "ERROR_API_MUST_BE_DIFFERENT": "Der neue API-Schlüssel darf nicht gleich dem aktuellen Schlüssel sein",
    "ERROR_BLACKLIST": "Abweisungslisten-IP-Fehler",
    "ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR": "Beim Laden von Cloud und Branch Connectors ist ein Problem aufgetreten.Versuchen Sie es später erneut.",
    "ERROR_DEFAULT_LEASE_TME_SHOULD_BE_SMALLER_THAN_MAX_LEASE_TIME": "Die Standard-Lease-Dauer darf nicht größer als die maximale Lease-Dauer sein.",
    "ERROR_DELETING_ADMIN_MANAGEMENT": "Beim Löschen der Administratorverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_API_KEY_MANAGEMENT": "Beim Löschen der API-Schlüsselverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_APPLIANCE": "Beim Löschen des ZT-Geräts ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_CELLULAR_CONFIGURATION": "Beim Löschen der Mobilfunk-Konfiguration ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_CELLULAR_USER_PLANE": "Beim Löschen der Mobilfunk-Benutzerebene ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_GATEWAY": "Beim Löschen von Gateways ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_LOCATION_TEMPLATE": "Beim Löschen der Standortvorlage ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_LOCATION": "Beim Löschen des Standorts ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_PARTNER_ACCOUNT": "Beim Löschen des Partnerkontos ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_ROLE_MANAGEMENT": "Beim Löschen der Rollenverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DELETING_TESTING": "Beim Löschen des Tests ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_DISABLING_PARTNER_ACCOUNT": "Fehler beim Deaktivieren der Partnerkonto-Datenerfassung",
    "ERROR_DUPLICATE_DNS_SERVER": "Wählen Sie unterschiedliche Optionen für den primären und sekundären DNS.",
    "ERROR_DUPLICATE_HA_VIRTUAL_ID": "Virtuelle IDs für hohe Verfügbarkeit müssen eindeutig sein. Doppelt vorhandene ID-Werte für hohe Verfügbarkeit ",
    "ERROR_DUPLICATE_INTERFACE": "Die Schnittstelle darf nicht mit einer bereits vorhandenen identisch sein.",
    "ERROR_DUPLICATE_SUBNETS": "Überprüfen Sie das doppelte Subnetz.",
    "ERROR_EDITING_API_KEY_MANAGEMENT": "Beim Löschen der API-Schlüsselverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_EDITING_TESTING": "Beim Bearbeiten des Tests ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_EDITING": "Beim Bearbeiten der Daten ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_ENABLING_PARTNER_ACCOUNT": "Fehler beim Aktivieren der Partnerkonto-Datenerfassung ",
    "ERROR_HTTP_REQUEST_FAILURE": "HTTP-Anfragefehler gemeldet.",
    "ERROR_LIST_DNS_SERVER_HAS_DUPLICATE": "Die IP-Adressen des DNS-Servers haben doppelte Werte.",
    "ERROR_LIST_DNS_SERVER_LIMIT_4": "Es sind höchstens 4 IP-Adressen für DNS-Server zulässig.",
    "ERROR_LIST_DOMAIN_NAME_HAS_DUPLICATE": "Die Domainnamen haben doppelte Werte.",
    "ERROR_LIST_DOMAIN_NAME_LIMIT_4": "Es sind höchstens 4 Domainnamen zulässig.",
    "ERROR_LOADING_ADMIN_MANAGEMENT": "Beim Laden der Administratorverwaltungsdaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_API_KEY_MANAGEMENT": "Beim Laden der API-Schlüsseldaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_DATA": "Beim Laden der Daten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_DOMAINS": "Beim Laden der Domänendaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_FORWARDING_POLICIES": "Beim Laden der Weiterleitungsrichtlinien ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_LOCATION_TEMPLATE": "Beim Laden der Standortvorlagendaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_LOCATIONS": "Beim Laden der Standortdaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING_ROLE_MANAGEMENT": "Beim Laden der Rollenverwaltungsdaten ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "ERROR_LOADING": "Fehler beim Laden der Daten.",
    "ERROR_LOOKUP": "SUCH-URL-Fehler",
    "ERROR_NO_SCHEDULED_VERSION_AVAILABLE": "Keine Version verfügbar.",
    "ERROR_OCCURRED_WHILE_CREATING_NEW_PASSWORD": "Beim Erstellen des neuen Passworts ist ein Fehler aufgetreten. Versuchen Sie es später noch einmal",
    "ERROR_OCCURRED_WHILE_VERIFYING_PASSWORD": "Beim Verifizieren des aktuellen Passworts ist ein Fehler aufgetreten. Versuchen Sie es später noch einmal",
    "ERROR_OPERATIONAL_STATUS_SAVE_ERROR": "Beim Speichern des Betriebsstatus ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "Azure-Abonnement-ID darf nicht in Kombination mit Google Project-ID verwendet werden",
    "ERROR_REGENERATE_API_KEY_MANAGEMENT": "Beim Neugenerieren der API-Schlüsselverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_REMOVE_DELETED_SEGMENTS": "Die Regel kann nicht mit einem gelöschten Segment gespeichert werden.",
    "ERROR_SAVING_GATEWAY": "Beim Speichern des Gateways ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_SAVING_LOCATION": "Beim Speichern des Standorts ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_SAVING_NETWORK_SERVICES_GROUPS": "Beim Speichern der Netzwerkdienstgruppen ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_SAVING_NETWORK_SERVICES": "Beim Speichern der Netzwerkdienste ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_SAVING_ROLE_MANAGEMENT": "Beim Speichern der Rollenverwaltung ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_SAVING": "Beim Speichern der Daten ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR_TESTING": "Beim Ausführen des Tests ist ein Problem aufgetreten.",
    "ERROR_UPDATING_PERMISSION_STATUS": "Beim Aktualisieren des Betriebsstatus ist ein Problem aufgetreten. Versuchen Sie es später erneut.",
    "ERROR": "Fehler",
    "ESP_PROTOCOL_DESC": "Encapsulating Security Payload (ESP) ist ein Protokoll in IPSec, das Authentifizierung, Integrität und Vertraulichkeit von Netzwerkpaketen in IPv4- und IPv6-Netzwerken gewährleistet.",
    "ESP_PROTOCOL": "ESP",
    "ESTABLISH_SUPPORT_TUNNEL": "Support-Tunnel einrichten",
    "ESTONIA_EUROPE_TALLINN": "Europa / Tallinn",
    "ESTONIA": "Estland",
    "ETHIOPIA_AFRICA_ADDIS_ABABA": "Afrika / Addis Abeba",
    "ETHIOPIA": "Äthiopien",
    "EU_CENTRAL_1": "eu-central-1 (Frankfurt)",
    "EU_CENTRAL_1A": "eu-central-1a",
    "EU_CENTRAL_1B": "eu-central-1b",
    "EU_CENTRAL_1C": "eu-central-1c",
    "EU_CENTRAL_2": "Europa (Zürich)",
    "EU_NORTH_1": "eu-north-1 (Stockholm)",
    "EU_NORTH_1A": "eu-north-1a",
    "EU_NORTH_1B": "eu-north-1b",
    "EU_NORTH_1C": "eu-north-1c",
    "EU_SOUTH_1": "eu-south-1 (Mailand)",
    "EU_SOUTH_1A": "eu-south-1a",
    "EU_SOUTH_1B": "eu-south-1b",
    "EU_SOUTH_1C": "eu-south-1c",
    "EU_SOUTH_2": "Europa (Spanien)",
    "EU_WEST_1": "eu-west-1 (Irland)",
    "EU_WEST_1A": "eu-west-1a",
    "EU_WEST_1B": "eu-west-1b",
    "EU_WEST_1C": "eu-west-1c",
    "EU_WEST_2": "eu-west-2 (London)",
    "EU_WEST_2A": "eu-west-2a",
    "EU_WEST_2B": "eu-west-2b",
    "EU_WEST_2C": "eu-west-2c",
    "EU_WEST_3": "wu-west-3 (Paris)",
    "EU_WEST_3A": "eu-west-3a",
    "EU_WEST_3B": "eu-west-3b",
    "EU_WEST_3C": "eu-west-3c",
    "EUROPE_CENTRAL2_A": "europe-central2-a",
    "EUROPE_CENTRAL2_B": "europe-central2-b",
    "EUROPE_CENTRAL2_C": "europe-central2-c",
    "EUROPE_CENTRAL2": "europe-central2",
    "EUROPE_NORTH1_A": "europe-north1-a",
    "EUROPE_NORTH1_B": "europe-north1-b",
    "EUROPE_NORTH1_C": "europe-north1-c",
    "EUROPE_NORTH1": "europe-north1",
    "EUROPE_SOUTHWEST1_A": "europe-southwest1-a",
    "EUROPE_SOUTHWEST1_B": "europe-southwest1-b",
    "EUROPE_SOUTHWEST1_C": "europe-southwest1-c",
    "EUROPE_SOUTHWEST1": "europe-southwest1",
    "EUROPE_WEST1_B": "europe-west1-b",
    "EUROPE_WEST1_C": "europe-west1-c",
    "EUROPE_WEST1_D": "europe-west1-d",
    "EUROPE_WEST1": "europe-west1",
    "EUROPE_WEST10": "europe-west10",
    "EUROPE_WEST12_A": "europe-west12-a",
    "EUROPE_WEST12_B": "europe-west12-b",
    "EUROPE_WEST12_C": "europe-west12-c",
    "EUROPE_WEST12": "europe-west12",
    "EUROPE_WEST2_A": "europe-west2-a",
    "EUROPE_WEST2_B": "europe-west2-b",
    "EUROPE_WEST2_C": "europe-west2-c",
    "EUROPE_WEST2": "europe-west2",
    "EUROPE_WEST3_A": "europe-west3-a",
    "EUROPE_WEST3_B": "europe-west3-b",
    "EUROPE_WEST3_C": "europe-west3-c",
    "EUROPE_WEST3": "europe-west3",
    "EUROPE_WEST4_A": "europe-west4-a",
    "EUROPE_WEST4_B": "europe-west4-b",
    "EUROPE_WEST4_C": "europe-west4-c",
    "EUROPE_WEST4": "europe-west4",
    "EUROPE_WEST6_A": "europe-west6-a",
    "EUROPE_WEST6_B": "europe-west6-b",
    "EUROPE_WEST6_C": "europe-west6-c",
    "EUROPE_WEST6": "europe-west6",
    "EUROPE_WEST8_A": "europe-west8-a",
    "EUROPE_WEST8_B": "europe-west8-b",
    "EUROPE_WEST8_C": "europe-west8-c",
    "EUROPE_WEST8": "europe-west8",
    "EUROPE_WEST9_A": "europe-west9-a",
    "EUROPE_WEST9_B": "europe-west9-b",
    "EUROPE_WEST9_C": "europe-west9-c",
    "EUROPE_WEST9": "europe-west9",
    "EUROPE": "Europa",
    "EUSA_AGREEMENT": "Zscaler Endbenutzer-Abonnementvertrag",
    "EVENT_BUS_NAME": "Name des Event Bus",
    "EVENT_GRID_TEXT": "Wählen Sie die Regionen, Abonnements und Ressourcengruppen aus, in denen das Partnerthema und das Ziel erstellt werden.",
    "EVENT_GRID": "Ereignisraster",
    "EVENT_TIME": "Ereigniszeit",
    "EVENT": "EREIGNIS",
    "EVENTS": "Ereignisse",
    "EXACT_MATCH": "Genauer Treffer",
    "EXCEEDS_UPGRADE_WINDOW": "Fehlgeschlagen. Upgrade dauerte länger als das Upgrade-Fenster. Cloud Connector wurde auf einen gesunden Zustand zurückgesetzt",
    "EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS": "Aus dynamischen Standortgruppen ausschließen",
    "EXCLUDE_FROM_STATIC_LOCATION_GROUPS": "Aus manuellen Standortgruppen ausschließen",
    "EXCLUDE_IP_ADDRESSES": "IP-Adressen ausschließen",
    "EXCLUDE": "AUSSCHLIESSEN",
    "EXEC_INSIGHT_AND_ORG_ADMIN": "Executive Insight & Organization-Administrator",
    "EXISTING_LOCATION": "Bestehender Standort",
    "EXISTING": "Bestehend",
    "EXPIRES_IN": "Läuft ab in",
    "EXPIRES": "Ablauf",
    "EXPIRY_DATE": "Ablaufdatum",
    "EXPORT_TO_CSV": "Als CSV exportieren",
    "EXTERNAL_ID": "Externe ID",
    "EXTERNAL": "Externer Verkehr",
    "FAIL_ALLOW_IGNORE_DNAT": "An ursprünglichen DNS-Server weiterleiten",
    "FAIL_CLOSE": "Fehlschlag Schließen",
    "FAIL_CLOSED": "Fehlschlag Geschlossen",
    "FAIL_OPEN_TEXT": "Engine für die Traffic-Weiterleitung umgehen",
    "FAIL_OPEN_TOOLTIP": "Wenn Sie diese Option aktivieren, kann der gesamte lokale und für das Internet bestimmte Traffic ohne Richtlinien-Validierung oder Inhaltsüberprüfung fließen, falls die Engine für die Richtlinienweiterleitung ausfällt oder für Upgrades gestoppt wird. Der Zugriff auf ZPA geschützte Anwendungen wäre in diesem Status nicht zulässig.",
    "FAIL_OPEN": "ÖFFNEN NICHT MÖGLICH",
    "FAIL_RET_ERR": "Fehlerantwort zurückgeben",
    "FAILED_OTHER": "Anderes. Cloud Connector in gesundem Zustand",
    "FAILED": "Fehlgeschlagen",
    "FAILURE_BEHAVIOR": "Fehlerverhalten",
    "FAILURE": "Fehler",
    "FALKLAND_ISLANDS_ATLANTIC_STANLEY": "Atlantik/Stanley",
    "FALKLAND_ISLANDS_MALVINAS": "Falkland Inseln (Malvinas)",
    "FALKLAND_ISLANDS": "Falkland Inseln",
    "FALLBACK_TO_TLS": "Fallback auf TLS",
    "FALSE": "Falsch",
    "FAMILY_ISSUES": "Familienthemen",
    "FAROE_ISLANDS_ATLANTIC_FAROE": "Atlantik/Färöer Inseln",
    "FAROE_ISLANDS": "Färöer Inseln",
    "FEDERATED_STATES_OF_MICRONESIA": "Mikronesien",
    "FETCHING_MORE_LIST_ITEMS": "Weitere Listenelemente werden abgerufen...",
    "FIJI": "Fiji",
    "FILE_CERTIFICATE_FILTER": "Datei (.pem, .cer)",
    "FILE_HOST": "Daten-Hosting",
    "FILTERING": "Filterung",
    "FINANCE": "Finanzen",
    "FINISH": "Fertig stellen",
    "FINLAND_EUROPE_HELSINKI": "Europa / Helsinki",
    "FINLAND": "Finland",
    "FIREWALL_ACCESS_CONTROL": "Firewall-Zugangskontrolle",
    "FIREWALL_FORWARDING": "Firewall-Weiterleitung",
    "FIREWALL_LOGS": "Firewall Logs",
    "FIREWALL_RESOURCE": "Firewall-Ressource",
    "FIRST_TIME_AUP_BEHAVIOR": "Erstmaliges AUP-Verhalten",
    "FO_DEST_DROP": "Abfrage verworfen",
    "FO_DEST_ERR": "Fehlerantwort an Client zurückgegeben",
    "FO_DEST_PASS": "Abfrage an Ziel weitergeleitet",
    "FOOTER_PATENTS_TOOLTIP": "Entsprechend den Bestimmungen für virtuelles Marketing im America Invents Act sind die Sicherheitsangebote von Zscaler in den USA und in anderen Ländern durch Patente geschützt, wie näher beschrieben unter: https://www.zscaler.com/patents.",
    "FOR_AUTOMATION": "Für Automatisierung",
    "FORCE_ACTIVATE": "Aktivierung erzwingen",
    "FORCE_DELETE_VM": "Löschen der VM erzwingen",
    "FORCE_SSL_INTERCEPTION": "SSL-Inspektion erzwingen",
    "FORCED_ACTIVATE": "Aktivierung erzwingen",
    "FORWARD_TO_ORIGINAL_SERVER": "An ursprünglichen DNS-Server weiterleiten",
    "FORWARD_TO_PROXY_GATEWAY": "An Proxy-Gateway weiterleiten",
    "FORWARD_TO_ZPA_GATEWAY": "An ZPA-Gateway weiterleiten",
    "FORWARDING_CONTROL": "Weiterleitungssteuerung",
    "FORWARDING_INFORMATION": "Weiterleitungsinformationen",
    "FORWARDING_INTERFACE": "Weiterleitungsschnittstelle",
    "FORWARDING_IP_ADDRESS": "Weiterleitungs-IP-Adresse",
    "FORWARDING_METHOD": "Weiterleitungsart",
    "FORWARDING_METHODS": "Weiterleitungsmethoden",
    "FORWARDING_POLICIES": "Weiterleitungsrichtlinien",
    "FORWARDING_RULE": "Weiterleitungsregel",
    "FORWARDING": "Weiterleitung",
    "FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN / Domains",
    "FQDN_WILDCARD_DOMAINS_GROUP": "FQDN / Domaingruppen",
    "FRANCE_EUROPE_PARIS": "Europa / Paris",
    "FRANCE": "Frankreich",
    "FRANCECENTRAL": "(Europa) Frankreich Mitte",
    "FRANCESOUTH": "(Europa) Frankreich Süd",
    "FRENCH_GUIANA_AMERICA_CAYENNE": "Amerika/Cayenne",
    "FRENCH_GUIANA": "Französisch Guiana",
    "FRENCH_POLYNESIA_PACIFIC_GAMBIER": "Pacific/Gambier",
    "FRENCH_POLYNESIA_PACIFIC_MARQUESAS": "Pacific/Marquesas",
    "FRENCH_POLYNESIA_PACIFIC_TAHITI": "Pacific/Tahiti",
    "FRENCH_POLYNESIA": "Französisch Polynesien",
    "FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN": "Indisch/Kerguelen",
    "FRENCH_SOUTHERN_TERRITORIES": "Südlichen französischen Kolonien",
    "FRIDAY": "Freitag",
    "FROM": "Von",
    "FTP_000": "Ungültige FTP-Antwort",
    "FTP_110": "110 - Markierungswiedergabe neu starten",
    "FTP_125": "125 - Datenverbindung bereits geöffnet; Übertragung wird gestartet",
    "FTP_150": "150 - Dateistatus OK; Öffnen der Datenverbindung steht bevor",
    "FTP_200": "200 - Verzeichnis erfolgreich geändert",
    "FTP_226": "226 - Übertragung abgeschlossen",
    "FTP_250": "250 - Abgeschlossen",
    "FTP_421": "421 - Dienst nicht verfügbar",
    "FTP_425": "425 - Datenverbindung kann nicht geöffnet werden",
    "FTP_426": "426 - Übertragung abgebrochen",
    "FTP_450": "450 - Angeforderte Dateiaktion nicht ausgeführt",
    "FTP_451": "451 - Lokaler Fehler bei der Verarbeitung",
    "FTP_452": "452 - Unzureichender Speicherplatz",
    "FTP_453": "453 - MD5-Konflikt",
    "FTP_500": "500 - Syntaxfehler",
    "FTP_501": "501 - Syntaxfehler in Parametern",
    "FTP_502": "502 - Befehl nicht implementiert",
    "FTP_530": "530 - Nicht angemeldet",
    "FTP_532": "532 - Konto zum Speichern von Dateien erforderlich",
    "FTP_550": "550 - Datei nicht verfügbar",
    "FTP_551": "551 - Unbekannter Seitentyp",
    "FTP_552": "552 - Speicherzuweisung überschritten",
    "FTP_553": "553 - Dateiname nicht zulässig",
    "FTP_554": "554 - Datei ist infiziert",
    "FTP_555": "555 - Von Dateityprichtlinie blockiert",
    "FTP_556": "556 - Von DLP blockiert",
    "FTP_557": "557 - Von BA blockiert",
    "FTP_558": "558 - Von BWCTL blockiert",
    "FTP_559": "559 - Von URL-Kategorie blockiert",
    "FTP_560": "560 - Von ATP blockiert",
    "FTP_561": "561 - Von 'Internetzugriff blockieren' blockiert",
    "FTP_ALLOW_OVER_HTTP": "FTP über HTTP zulassen",
    "FTP_ALLOWED_URL_CATEGORIES": "Zulässige URL-Kategorien",
    "FTP_ALLOWED_URLS": "Zulässige URLs",
    "FTP_APPE": "appe",
    "FTP_CONNECT_CMD": "CONNECT",
    "FTP_CONNECT": "FTP-Datenverkehr (über HTTP) von Bridging zu nativem FTP konvertieren",
    "FTP_CONTROL_RECOMMENDED_POLICY": "empfohlene FTP-Regel",
    "FTP_CONTROL_TIPS_DESC": "Standardmäßig gestattet der Zscaler-Dienst Benutzern an einem Standort nicht, Dateien von FTP-Sites hoch- oder herunterzuladen. Sie können die FTP-Richtlinie jedoch so konfigurieren, dass sie den Zugriff auf bestimmte Sites zulässt.",
    "FTP_CONTROL_TIPS_TITLE": "FTP-Steuerrichtlinie konfigurieren",
    "FTP_CONTROL": "FTP-Steuerung",
    "FTP_CWD": "cwd",
    "FTP_DATA_DESC": " Dieses Protokoll wird verwendet, um Daten in Datenverbindungen von FTP-Kommunikation zu transportieren",
    "FTP_DATA": "FTP-Data",
    "FTP_DENIED": "Zugriff auf FTP-Sites ist nicht gestattet",
    "FTP_DESC": " Das FTP-Protokoll wird für die sichere Datenübertragung zwischen einem Client und einem Server verwendet",
    "FTP_INVALID": "Ungültig",
    "FTP_LIST": "list",
    "FTP_NATIVE_TRAFFIC": "Nativer FTP-Datenverkehr",
    "FTP_OVER_HTTP_TRAFFIC": "FTP über HTTP-Verkehr",
    "FTP_PROXY_PORT": "FTP-Proxyport",
    "FTP_PROXY": "FTP-Proxy",
    "FTP_RETR": "retr",
    "FTP_RULE": "Natives FTP",
    "FTP_SECURITY": "FTP-Sicherheit umfasst AV, DLP, BA, FT usw.",
    "FTP_SERVICES": "FTP-Dienste",
    "FTP_STOR": "stor",
    "FTP_UPLOAD_DENIED": "FTP über HTTP darf nicht zum Hochladen verwendet werden",
    "FTP": "FTP",
    "FTPOVERHTTP": "FTP über HTTP",
    "FTPRULESLOT": "File Type Control",
    "FTPS_DATA_DESC": " Dieses Protokoll wird verwendet, um Daten in Datenverbindungen mit sicherer FTP-Kommunikation zu transportieren",
    "FTPS_DATA": "ftps_data",
    "FTPS_DESC": " Sichere Version des FTP-Protokolls",
    "FTPS_IMPLICIT_DESC": "Implizit FTPS startet automatisch eine SSL/TLS-Verbindung zum Server, sobald der FTP-Client eine Verbindung zu einem FTP-Server herstellt.",
    "FTPS_IMPLICIT": "Implizites FTPS",
    "FTPS": "FTPS",
    "FULL_ACCESS_ENABLED_UNTIL": "Vollzugriff aktiviert bis",
    "FULL_ACCESS": "Vollzugriff",
    "FULL_SESSION_LOGS": "Vollständige Sitzungsprotokolle",
    "FULL": "Vollständig",
    "FUNCTIONAL_SCOPE": "Funktioneller Bereich",
    "FWD_METHOD": "Weiterleitungsart",
    "FWD_RULE": "Weiterleitungsregel",
    "FWD_TRAFFIC_DIRECTION": "Anfrage-Typ",
    "FWD_TYPE": "Weiterleitungsart",
    "FWD_TYPES": "Weiterleitungsarten",
    "GABON_AFRICA_LIBREVILLE": "Afrika / Libreville",
    "GABON": "Gabon",
    "GAMBIA_AFRICA_BANJUL": "Afrika / Bangui",
    "GAMBIA": "Gambia",
    "GAMBLING": "Glücksspiel",
    "GATEWAY_DEST_IP": "Gateway-Ziel-IP",
    "GATEWAY_DEST_PORT": "Gateway-Zielport",
    "GATEWAY_DETAILS": "Gateway-Details",
    "GATEWAY_IP_ADDRESS": "Gateway-IP-Adresse",
    "GATEWAY_NAME": "Gateway-Name",
    "GATEWAY_OPTIONS": "Gateway-Optionen",
    "GATEWAY": "Gateway",
    "GATEWAYS": "Gateways",
    "GCP_AVAILABILITY_ZONE": "GCP-Verfügbarkeitszone",
    "GCP_REGION": "GCP-Region",
    "GENERAL_AVAILABILITY": "Allgemeine Verfügbarkeit",
    "GENERAL_INFORMATION": "Allgemeine Informationen",
    "GENERAL": "Allgemein",
    "GENERATE_NEW_CERTIFICATE": "Neues Zertifikat generieren",
    "GENERATE_TOKEN": "Token generieren",
    "GEO_LOCATION": "Geolocation",
    "GEO_VIEW": "Geo-Ansicht",
    "GEORGIA_ASIA_TBILISI": "Asien/Tiflis",
    "GEORGIA": "Georgien",
    "GERMANY_EUROPE_BERLIN": "Europa/ Berlin",
    "GERMANY": "Deutschland",
    "GERMANYNORTH": "(Europa) Deutschland Nord",
    "GERMANYWESTCENTRAL": "(Europa) Deutschland Mitte West",
    "GHANA_AFRICA_ACCRA": "Afrika/Accra",
    "GHANA": "Ghana",
    "GIBRALTAR_EUROPE_GIBRALTAR": "Europa / Gibraltar",
    "GIBRALTAR": "Gibraltar",
    "GLOBAL": "Weltweit",
    "GMT_01_00_AZORES": "GMT-01:00",
    "GMT_01_00_WESTERN_EUROPE_GMT_01_00": "GMT+01:00",
    "GMT_02_00_EASTERN_EUROPE_GMT_02_00": "GMT+02:00",
    "GMT_02_00_EGYPT_GMT_02_00": "GMT+02:00",
    "GMT_02_00_ISRAEL_GMT_02_00": "GMT+02:00",
    "GMT_02_00_MID_ATLANTIC": "GMT-02:00",
    "GMT_03_00_ARGENTINA": "GMT-03:00",
    "GMT_03_00_BRAZIL": "GMT-03:00",
    "GMT_03_00_RUSSIA_GMT_03_00": "GMT+03:00",
    "GMT_03_00_SAUDI_ARABIA_GMT_03_00": "GMT+03:00",
    "GMT_03_30_IRAN_GMT_03_30": "GMT+03:30",
    "GMT_03_30_NEWFOUNDLAND_CANADA": "GMT-03:30",
    "GMT_04_00_ARABIAN_GMT_04_00": "GMT+04:00",
    "GMT_04_00_ATLANTIC_TIME": "GMT-04:00",
    "GMT_04_30_AFGHANISTAN_GMT_04_30": "GMT+04:30",
    "GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA": "GMT-05:00",
    "GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00": "GMT+05:00",
    "GMT_05_00_US_EASTERN_TIME_INDIANA": "GMT-05:00",
    "GMT_05_00_US_EASTERN_TIME": "GMT-05:00",
    "GMT_05_30_INDIA_GMT_05_30": "GMT+05:30",
    "GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00": "GMT+06:00",
    "GMT_06_00_MEXICO": "GMT-06:00",
    "GMT_06_00_US_CENTRAL_TIME": "GMT-06:00",
    "GMT_06_30_BURMA_GMT_06_30": "GMT+06:30",
    "GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00": "GMT+07:00",
    "GMT_07_00_US_MOUNTAIN_TIME_ARIZONA": "GMT-07:00",
    "GMT_07_00_US_MOUNTAIN_TIME": "GMT-07:00",
    "GMT_08_00_AUSTRALIA_WT_GMT_08_00": "GMT+08:00",
    "GMT_08_00_CHINA_TAIWAN_GMT_08_00": "GMT+08:00",
    "GMT_08_00_PACIFIC_TIME": "GMT-08:00",
    "GMT_08_00_SINGAPORE_GMT_08_00": "GMT+08:00",
    "GMT_08_30_PITCARN": "GMT-08:30",
    "GMT_09_00_JAPAN_GMT_09_00": "GMT+09:00",
    "GMT_09_00_KOREA_GMT_09_00": "GMT+09:00",
    "GMT_09_00_US_ALASKA_TIME": "GMT-09:00",
    "GMT_09_30_AUSTRALIA_CT_GMT_09_30": "GMT+09:30",
    "GMT_09_30_MARQUESAS": "GMT-09:30",
    "GMT_10_00_AUSTRALIA_ET_GMT_10_00": "GMT+10:00",
    "GMT_10_00_US_HAWAIIAN_TIME": "GMT-10:00",
    "GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30": "GMT+10:30",
    "GMT_11_00_CENTRAL_PACIFIC_GMT_11_00": "GMT+11:00",
    "GMT_11_00_SAMOA": "GMT-11:00",
    "GMT_11_30_NORFOLK_ISLANDS_GMT_11_30": "GMT+11:30",
    "GMT_12_00_DATELINE": "GMT-12:00",
    "GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00": "GMT+12:00",
    "GMT_UK_SPAIN": "GMT",
    "GMT": "GMT",
    "GMTP": "",
    "GNUTELLA_DESC": " Gnutella ist ein Peer-to-Peer-Protokoll",
    "GNUTELLA": "Gnutella",
    "GO_BACK": "Zurück",
    "GOOD": "Gut",
    "GOVERNMENT": "Sonstiges - Politik und Regierung",
    "GRE_PROTOCOL_DESC": "Generic Routing Encapsulation (GRE) ist ein Tunnelprotokoll, das eine Vielzahl von Netzwerkschichtprotokollen in virtuellen Punkt-zu-Punkt-Links oder Punkt-zu-Multipoint-Verbindungen über ein Internet Protocol-Netzwerk einkapseln kann.",
    "GRE_PROTOCOL": "GRE",
    "GRE_TUNNEL_INFO": "GRE-Tunnelinformationen",
    "GRE": "GRE",
    "GREECE_EUROPE_ATHENS": "Europa/ Athens",
    "GREECE": "Griechenland",
    "GREENLAND_AMERICA_DANMARKSHAVN": "Amerika/Danmarkshavn",
    "GREENLAND_AMERICA_GODTHAB": "Amerika/Godthab",
    "GREENLAND_AMERICA_SCORESBYSUND": "Amerika/Scoresbysund",
    "GREENLAND_AMERICA_THULE": "Amerika/Thule",
    "GREENLAND": "Grönland",
    "GRENADA_AMERICA_GRENADA": "Amerika/Grenada",
    "GRENADA": "Grenada",
    "GROUP_INFORMATION": "Gruppeninformationen",
    "GROUP_NAME": "Gruppenname",
    "GROUP_ONLY": "Gruppe",
    "GROUP": "Gruppe",
    "GROUPS": "Gruppen",
    "GUADELOUPE_AMERICA_GUADELOUPE": "Amerika/Guadeloupe",
    "GUADELOUPE": "Guadeloupe",
    "GUAM_PACIFIC_GUAM": "Pazifik/Guam",
    "GUAM": "Guam",
    "GUATEMALA_AMERICA_GUATEMALA": "Amerika/Guatemala",
    "GUATEMALA": "Guatemala",
    "GUERNSEY_EUROPE_GUERNSEY": "Europa / Guernsey",
    "GUERNSEY": "Guernsey",
    "GUESTWIFI": "Gast-WLAN-Verkehrstyp",
    "GUINEA_AFRICA_CONAKRY": "Afrika / Conakry",
    "GUINEA_BISSAU_AFRICA_BISSAU": "Afrika / Bissau",
    "GUINEA_BISSAU": "Guinea-Bissau",
    "GUINEA": "Guinea",
    "GUYANA_AMERICA_GUYANA": "Amerika/Guyana",
    "GUYANA": "Guyana",
    "GW_CONNECT_FAILED": "Verbindungsaufbau zu GW fehlgeschlagen.",
    "GW_CONNECTION_CLOSE": "GW-Verbindung wurde mit EOF geschlossen.",
    "GW_CONNECTION_FAIL": "Gateway-Verbindung fehlgeschlagen.",
    "GW_KEEPALIVE_FAIL": "Zeitüberschreitung bei GW-Keepalive-Sonde.",
    "GW_RESOLVE_FAIL": "Gateway-Auflösung fehlgeschlagen.",
    "GW_RESOLVE_NOIP": "PAC hat keine IPs für GW-Auflösung zurückgegeben.",
    "GW_UNHEALTHY": "Ein oder mehrere Gateways sind fehlerhaft.",
    "H_323_DESC": "H.323 ist ein Standard, der von der Internationalen Fernmeldeunion genehmigt (ITU) wurde. Er definiert die Übertragung audiovisueller Konferenzdaten über Netzwerke",
    "H_323": "H.323",
    "HA_DEPLOYMENT_STATUS": "HA-Deployment-Status",
    "HA_DEPLOYMENT": "High Availability Deployment",
    "HA_STATE": "HA-Status",
    "HA_STATUS": "HA-Status",
    "HAITI_AMERICA_PORT_AU_PRINCE": "Amerika/Port-au-Prince",
    "HAITI": "Haiti",
    "HARDWARE_DEVICE": "Hardwaregerät",
    "HARDWARE_MANAGEMENT": "Hardwareverwaltung",
    "HEALTH_MONITORING_CC": "Cloud Connectors zur Überwachung des Gesundheitszustands",
    "HEALTH_STATUS_TOOLTIP": "Der Zustand zeigt den Zustand jedes bereitgestellten Gateways an. Die Anzahl der hier angezeigten Gateways ist geringer als die in Berechtigungen angezeigte Anzahl, da jedes Gateway 2 oder mehr Verfügbarkeitszonen (Availability Zones, AZ) enthalten kann.",
    "HEALTH_STATUS": "Gesundheitszustand",
    "HEALTH": "Gesundheit",
    "HEALTHY": "Gesund",
    "HELP": "Hilfe",
    "HIGH_AVAILABILITY_STATUS": "Hochverfügbarkeitsstatus",
    "HIGH_AVAILABILITY": "Hohe Verfügbarkeit",
    "HISTORY": "Geschichte",
    "HOBBIES_AND_LEISURE": "Hobbies",
    "HOLY_SEE_VATICAN_CITY_STATE": "Vatikan",
    "HONDURAS_AMERICA_TEGUCIGALPA": "Amerika/Tegucigalpa",
    "HONDURAS": "Honduras",
    "HONG_KONG_ASIA_HONG_KONG": "Asien/Hong Kong",
    "HONG_KONG": "Hong Kong",
    "HOP_COUNT": "Hop-Anzahl",
    "HOSTED_DB": "Zscaler-Datenbank",
    "HOSTNAME_PREFIX": "Hostnamenpräfix",
    "HOSTNAME": "Host Name",
    "HOURS": "Stündlich",
    "HTTP_0_0": "schlecht",
    "HTTP_000": "Ungültige HTTP-Antwort",
    "HTTP_1_0": "1",
    "HTTP_1_1": "1.1",
    "HTTP_100": "100 - Continue",
    "HTTP_101": "101 - Switching Protocols",
    "HTTP_102": "102 - Verarbeitung",
    "HTTP_150": "150 - Andere 1XX-Fehler",
    "HTTP_2_0": "2.0",
    "HTTP_200": "200 - OK",
    "HTTP_201": "201 - Created",
    "HTTP_202": "202 - Accepted",
    "HTTP_203": "203 - Non-Authoritative Information",
    "HTTP_204": "204 - No Content",
    "HTTP_205": "205 - Reset Content",
    "HTTP_206": "206 - Partial Content",
    "HTTP_207": "207 - Multi-Status",
    "HTTP_226": "226 - IM verwendet",
    "HTTP_250": "250 - Andere 2XX-Fehler",
    "HTTP_300": "300 - Multiple Choices",
    "HTTP_301": "301 - Moved Permanently",
    "HTTP_302": "302 - Found",
    "HTTP_303": "303 - See Other",
    "HTTP_304": "304 - Not Modified",
    "HTTP_305": "305 - Use Proxy",
    "HTTP_306": "306 - Unused",
    "HTTP_307": "307 - Temporary Redirect",
    "HTTP_308": "308 - Permanente Weiterleitung",
    "HTTP_400": "400 - Bad Request",
    "HTTP_401": "401 - Unauthorized",
    "HTTP_402": "402 - Payment Required",
    "HTTP_403": "403 - Forbidden",
    "HTTP_404": "404 - Not Found",
    "HTTP_405": "405 - Method Not Allowed",
    "HTTP_406": "406 - Not Acceptable",
    "HTTP_407": "407 - Proxy Authentication Required",
    "HTTP_408": "408 - Request Timeout",
    "HTTP_409": "409 - Conflict",
    "HTTP_410": "410 - Gone",
    "HTTP_411": "411 - Length Required",
    "HTTP_412": "412 - Precondition Failed",
    "HTTP_413": "413 - Request Entity Too Large",
    "HTTP_414": "414 - Request-URI Too Long",
    "HTTP_415": "415 - Unsupported Media Type",
    "HTTP_416": "416 - Requested Range Not Satisfiable",
    "HTTP_417": "417 - Expectation Failed",
    "HTTP_421": "421 - Fehlgeleitete Anfrage",
    "HTTP_422": "422 - Nicht verarbeitbare Entität",
    "HTTP_423": "423 - Gesperrt",
    "HTTP_424": "424 - Fehlgeschlagene Abhängigkeit",
    "HTTP_426": "426 - Upgrade erforderlich",
    "HTTP_428": "428 - Vorbedingung erforderlich",
    "HTTP_429": "429 - Zu viele Anfragen",
    "HTTP_450": "450 - Andere 4XX-Fehler",
    "HTTP_500": "500 - Internal Server Error",
    "HTTP_501": "501 - Not Implemented",
    "HTTP_502": "502 - Bad Gateway",
    "HTTP_503": "503 - Service Unavailable",
    "HTTP_504": "504 - Gateway Timeout",
    "HTTP_505": "505 - Version Not Supported",
    "HTTP_506": "506 - Variante verhandelt auch",
    "HTTP_507": "507 - Unzureichender Speicher",
    "HTTP_508": "508 - Schleife erkannt",
    "HTTP_510": "510 - Nicht erweitert",
    "HTTP_550": "550 - Andere 5XX-Fehler",
    "HTTP_BASELINECONTROL": "Baselinecontrol",
    "HTTP_BCOPY": "Bcopy",
    "HTTP_BDELETE": "Bdelete",
    "HTTP_BMOVE": "Bmove",
    "HTTP_BPROPFIND": "Bpropfind",
    "HTTP_BPROPPATCH": "Bproppatch",
    "HTTP_CHECKIN": "Checkin",
    "HTTP_CHECKOUT": "Checkout",
    "HTTP_CONNECT_DENIED": "Darf HTTP-Tunnel nicht verwenden",
    "HTTP_CONNECT": "CONNECT",
    "HTTP_COPY": "Kopieren",
    "HTTP_DELETE": "Löschen",
    "HTTP_DESC": " Das Hypertext  Transfer Protocol (HTTP) wird für das Surfen im Web verwendet",
    "HTTP_DNS_PORT_SETTINGS": "HTTP- und DNS-Porteinstellungen",
    "HTTP_DPI_DISABLED": "SME DPI: bestimmt, ob der Proxy-HTTP-Datenverkehr an DPI gesendet wird oder nicht; standardmäßig ist dieses Feature-Bit deaktiviert und der Datenfluss wird an DPI gesendet, wenn die App-ID nicht anhand von ZURLDB ermittelt werden kann.",
    "HTTP_GET": "Erhalten",
    "HTTP_HEAD": "HEAD",
    "HTTP_LABEL": "Beschriftung",
    "HTTP_LOCK": "Sperren",
    "HTTP_MAILPOST": "MAILPOST",
    "HTTP_MERGE": "Zusammenführen",
    "HTTP_MKACTIVITY": "Mkactivity",
    "HTTP_MKCOL": "Mkcol",
    "HTTP_MKWORKSPACE": "Mkworkspace",
    "HTTP_MOVE": "MOVE",
    "HTTP_NOTIFY": "Benachrichtigen",
    "HTTP_OPTIONS": "Optionen",
    "HTTP_POLL": "Abfragen",
    "HTTP_PORTS_FORWARDED_TO_WEB_PROXY": "An Webproxy weitergeleitete HTTP-Ports",
    "HTTP_POST": "Post",
    "HTTP_PROPFIND": "Propfind",
    "HTTP_PROPPATCH": "Proppatch",
    "HTTP_PROXY_DESC": "HTTP-Tunneling ist eine Technik, durch die Kommunikation verschiedener Netzwerkprotokolle ermöglicht wird. Das HTTP-Protokoll wird dabei eingekapselt. In der Regel die gehören Netzwerkprotokolle zu der TCP / IP Protokollfamilie .",
    "HTTP_PROXY_PORT": "HTTP-Proxyport",
    "HTTP_PROXY": "HTTP-Proxy",
    "HTTP_PUT": "Put",
    "HTTP_REPORT": "Report",
    "HTTP_REQMOD": "Reqmod",
    "HTTP_REQUEST": "HTTP-Abfrage",
    "HTTP_REQUESTS": "HTTP-Anfragen",
    "HTTP_RESPMOD": "Respmod",
    "HTTP_RESPONSE": "HTTP-Antwort",
    "HTTP_RULE": "HTTP",
    "HTTP_SEARCH": "Suche",
    "HTTP_SECURITY_HEADERS": "HTTP-Sicherheits-Header",
    "HTTP_SERVICES": "HTTP-Dienste",
    "HTTP_SUBSCRIBE": "Abonnieren",
    "HTTP_TRACE": "Verfolgen",
    "HTTP_TUNNEL_CONTROL": "HTTP-Tunnelsteuerung",
    "HTTP_TUNNEL": "HTTP Tunnel",
    "HTTP_UNCHECKOUT": "Checkout aufheben",
    "HTTP_UNKNOWN_DESC": " Dies identifiziert HTTP-Proxy / Firewall-Traffic für den keine detailliertere App ermittelt werden kann",
    "HTTP_UNKNOWN": "HTTP unbekannt",
    "HTTP_UNLOCK": "Entsperren",
    "HTTP_UNSUBSCRIBE": "Abonnement stornieren",
    "HTTP_UPDATE": "Update",
    "HTTP_VERSIONCONTROL": "Versioncontrol",
    "HTTP_VS_HTTPS": "HTTP / HTTPS",
    "HTTP": "HTTP",
    "HTTP2_DESC": " Das Hypertext Transfer Protocol (HTTP2.0) wird für das Surfen im Web verwendet",
    "HTTP2": "HTTPv2",
    "HTTPS_DESC": " HTTPS ist die sichere Version von HTTP",
    "HTTPS_PORTS_FORWARDED_TO_WEB_PROXY": "An Webproxy weitergeleitete HTTPS-Ports",
    "HTTPS_PROXY_PORT": "HTTPS-Proxyport",
    "HTTPS_PROXY": "HTTPS-Proxy",
    "HTTPS_RULE": "HTTPS",
    "HTTPS_SERVICES": "HTTPS-Dienste",
    "HTTPS_SSL_TRAFFIC_TREND": "HTTPS- UND SSL-VERKEHRSTREND",
    "HTTPS_SSL_TRAFFIC": "HTTPS- UND SSL-VERKEHR",
    "HTTPS_UNKNOWN_DESC": " Identifiziert HTTPS-Proxy / Firewall-Traffic, für den granularere App  nicht ermittelt werden kann",
    "HTTPS_UNKNOWN": "HTTPS unbekannt",
    "HTTPS": "HTTPS",
    "HTTPTUNNEL_DESC": " HTTP-Tunneling ist eine Technik für die Kommunikation durch verschiedene Netzwerkprotokolle; das HTTP-Protokoll ist eingekapselt, in der Regel gehören die Netzwerkprotokolle  zu der TCP / IP Protokollfamilie",
    "HTTPTUNNEL": "HttpTunnel",
    "HUNGARY_EUROPE_BUDAPEST": "Europa / Budapest",
    "HUNGARY": "Ungarn",
    "HYPERVISOR_OS": "Hypervisor",
    "HYPERVISOR_VERSION": "Hypervisor-Version",
    "I_AGREE": "Ich stimme zu",
    "I_GAMER_DESC": " Online Spiele- und Manga-Website",
    "I_GAMER": "i-gamer",
    "I_PART_DESC": " Taiwanesische Online-Dating-Website",
    "I_PART": "i-part.com",
    "I_UNDERSTAND_THE_CONSEQUENCE_AND_WANT_TO_PROCEED": "Ich bin mir der Konsequenzen bewusst und möchte fortfahren",
    "I_UNDERSTAND_THE_CONSEQUENCE": "Ich bin mir der Konsequenzen bewusst und möchte fortfahren",
    "ICELAND_ATLANTIC_REYKJAVIK": "Atlantik/Reykjavik",
    "ICELAND": "Island",
    "ICMP_ANY_DESC": "ICMP ist eines der wichtigsten Protokolle der Internet Protocol Suite, die von Netzwerkgeräten wie Routern verwendet werden um Fehlermeldungen zu senden, bzw. um anzuzeigen, dass ein angeforderter Dienst nicht verfügbar ist, oder dass ein Host oder Router nicht erreichtbar ist. ICMP kann auch für Relais-Anfragen verwendet werden.",
    "ICMP_ANY": "ICMP",
    "ICMP_DESC": " Das Internet Control Message Protocol (ICMP) ist ein des wichtigsten Protokoll der Internet Protocol Suite",
    "ICMP_UNKNOWN_DESC": " Dies identifiziert UDP-Proxy / Firewall-Traffic, für den keine granularere App ermittelt werden kann",
    "ICMP_UNKNOWN": "ICMP Unbekannt",
    "IDENT_DESC": " Das Identifizierungs-Protokoll dient der Bestimmung der Identität eines Anwenders einer bestimmten TCP-Verbindung",
    "IDENT": "Ident",
    "IDLE_TIME_DISASSOCIATION": "Zeit im Leerlauf bis zur Trennung",
    "IDP_NAME": "IdP-Name",
    "IDP": "IdP",
    "IGNORE_INSECURE_KEY": "Nicht sicher",
    "IKE_ALG": "Dieser LB unterstützt IKE-Alg.",
    "IKE_DESC": "IKE ist ein Protokoll, um authentifizierte Schlüssel für die Verwendung  von IPSEC mit ISAKMP zu erhalten.",
    "IKE_NAT_DESC": "IKE-NAT ermöglicht Network Address Translation für ISAKMP und ESP-Pakete.",
    "IKE_NAT": "IKE-NAT",
    "IKE": "IKE",
    "IKEA_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host ikea.com",
    "IKEA": "ikea",
    "IKEV1_PHASE1": "IKE Version 1",
    "IKEV1_PHASE2": "IKE Version 2",
    "IKEV1": "IKE Version 1",
    "IKEV2_ALL_PHASES": "IKE Version 2",
    "IKEV2": "IKE Version 2",
    "IL_CENTRAL_1": "Israel (Tel Aviv)",
    "ILOVEIM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host iloveim.com",
    "ILOVEIM": "ILoveIM",
    "ILS_DESC": "Internet Locator Service beinhaltet LDAP, User Locator Service und LDAP over TLS/SSL.",
    "ILS": "ILS",
    "IMAGE_HOST_DESC": " Seiten, die Video-/Bilder-Hosting, -Verlinkung oder Austausch ermöglichen.",
    "IMAGE_HOST": "Bild Speicherung",
    "IMAGE_ID": "Bild-ID",
    "IMAGES": "Bilder",
    "IMAGESHACK_DESC": " Kostenloser Online Bilderaustausch-Service",
    "IMAGESHACK": "ImageShack",
    "IMAP_DESC": "IMAP (Internet Message Access Protocol) ist ein Protokoll, das für das Abrufen von Emails verwendet wird.",
    "IMAP": "IMAP",
    "IMDB_DESC": " Online Informations-Datenbank für Filme und TV-Shows",
    "IMDB": "IMDb",
    "IMEEM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host imeem.com",
    "IMEEM": "imeem",
    "IMEET_DESC": " Online Video Conferencing-Service mit Cloud-basierter Technologie",
    "IMEET": "i-Meet",
    "IMESH_DESC": " iMesh ist ein Peer-to-Peer-Protokoll",
    "IMESH": "iMesh",
    "IMFRULESLOT": "Instant Messaging/App-Kontrolle",
    "IMGUR_DESC": " Ein kostenloser Online Bilder Hosting-Service",
    "IMGUR": "imgur",
    "IMO": "IMO",
    "IMOIM_DESC": " imo.im",
    "IMOIM": "imo.im",
    "IMP_DESC": " IMP ist das IMAP-Webmail des Horde-Projekts",
    "IMP": "IMP",
    "IMPERSONATION": "Imitierung",
    "IMPLUS_DESC": " IM+",
    "IMPLUS": "IM+",
    "IMPORT_ACCOUNT_ID": "Konto-ID importieren",
    "IMPORT_PROJECT_ID": "Projekt-ID importieren",
    "IMPORT_SUBSCRIPTION_ID": "Abonnement-ID importieren",
    "IMPORT": "Importieren",
    "IMPRESS_DESC": " Japanische IT News-Webseite",
    "IMPRESS": "impress",
    "IMVU_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host imvu.com",
    "IMVU": "IMVU",
    "INACTIVE": "Inaktiv",
    "INBOUND": "Eingehend",
    "INBOX_DESC": " Das inbox.com Portal bietet einen kostenlosen Email-Service",
    "INBOX": "Inbox",
    "INBYTES": "In Byte",
    "INCLUDE_ADDRESS_RANGE": "Adressbereich einschließen",
    "INCLUDE_IP_ADDRESSES": "IPs-Adressen einschließen",
    "INCLUDE": "Einschließen",
    "INCOMPLETE_DESC": " Unvollständig wird verwendet, wenn die Protokoll-Signatur zu lang ist",
    "INCOMPLETE": "Unvollständig",
    "INDABA_MUSIC_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host indabamusic.com",
    "INDABA_MUSIC": "Indaba Music",
    "INDIA_ASIA_CALCUTTA": "Asien/Kalkutta",
    "INDIA_ASIA_KOLKATA": "Asien/Kolkata",
    "INDIA": "Indien",
    "INDIATIMES_DESC": " Diese Signatur erkennt Indiatimes und eine große Anzahl von ihren Unterdomains. Es handelt sich um eine der beliebtesten Internet Plattformen mit Portalen für mobile Mehrwertdienste in Indien.",
    "INDIATIMES": "timesofindia",
    "INDONESIA_ASIA_JAKARTA": "Asien/Jakarta",
    "INDONESIA_ASIA_JAYAPURA": "Asien/Jayapura",
    "INDONESIA_ASIA_MAKASSAR": "Asien/Makassar",
    "INDONESIA_ASIA_PONTIANAK": "Asien/Pontianak",
    "INDONESIA": "Indonesien",
    "INDONETWORK_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host indonetwork.co.id",
    "INDONETWORK": "Indonetwork",
    "INDOWEBSTER_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host indowebster.com",
    "INDOWEBSTER": "Indowebster",
    "INFO": "Info",
    "INFOARMOR_DESC": " InfoArmor sorgt für branchenführende Lösungen für den Schutz der Mitarbeiteridentität und nutzt hochentwickelten Bedrohungsschutz",
    "INFOARMOR": "InfoArmor",
    "INFORMIX_DESC": " Informix ist eine Familie an relationalen Datenbank-Management-Systemen von IBM. IBM erwarb die Informix-Technologie im Jahr 2001, die auf 1981 zurücjdatiert. Die Systeme laufen auf IBM-Mainframes und stehen auch für Linux / Unix / Windows zur Verfügung.",
    "INFORMIX": "Informix",
    "INGRESS_DETAILS": "Eintrittdetails",
    "INILAH_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host inilah.com",
    "INILAH": "inilah",
    "INSIGHTS": "Insights",
    "INST_GBL_METRICS": "Instanz",
    "INSTAGRAM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an die Hosts instagr.am und instagram.com. Es klassifiziert auch den SSL-Traffic zu dem Common Name instagram.com ",
    "INSTAGRAM": "Instagram",
    "INSTANCE_ROLE": "Instanzrolle",
    "INTALKING_DESC": " Taiwanesisches Portal für Beauty und Make-up",
    "INTALKING": "intalking.com",
    "INTEGER_REQUIRED": "Geben Sie eine Zahl ein.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_LAN": "LAN benötigt mindestens eine aktivierte Schnittstelle oder Sub-Schnittstelle.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_WAN": "WAN benötigt mindestens eine aktivierte Schnittstelle oder Sub-Schnittstelle. Es dürfen höchstens zwei Schnittstellen oder Sub-Schnittstellen aktiviert sein.",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE": "Schnittstelle benötigt mindestens eine Sub-Schnittstelle.",
    "INTERFACE_NAME": "Schnittstellenname",
    "INTERFACE_SHUTDOWN": "Herunterfahren der Schnittstelle",
    "INTERFACE": "Schnittstelle",
    "INTERNAL_EXTERNAL_TRAFFIC": "Interner/externer Verkehr",
    "INTERNAL_GATEWAY_IP_ADDRESS": "Interne Gateway-IP-Adresse",
    "INTERNAL": "Interner Verkehr",
    "INTERNATIONS_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host internations.org. Es klassifiziert auch den SSL-Traffic zu dem Common Name .internations.org",
    "INTERNATIONS": "InterNations",
    "INTERNET_ACCESS": "Internetzugriff",
    "INTERNET_COMMUNICATION": "Internet-Kommunikation",
    "INTERNET_SERVICES_DESC": " Websites im Zusammenhang mit Leistungen, die über das Internet durchgeführt werden.",
    "INTERNET_SERVICES": "Internet Dienstleistungen",
    "INTERNET_USAGE_TREND": "Internet-Nutzungstrend",
    "INTERNET": "Internet",
    "INTERPARK_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host www.interpark.com.  Es klassifiziert auch den SSL-Traffic zu dem Common Name .interpark.com",
    "INTERPARK": "Interpark",
    "INTUIT_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host intuit.com",
    "INTUIT": "Intuit",
    "INVALID_API_KEY": "Ungültiger API-Schlüssel",
    "INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE": "Ungültige Einheit der maximalen Batchgröße für den SIEM-Typ. Dieser Wert muss zwischen 16 und 512 KB liegen.",
    "INVALID_NAME": "Geben Sie einen gültigen Namen ein",
    "INVALID_USERNAME_OR_PASSWORD": "Ungültige Anmelde-ID oder ungültiges Passwort",
    "IOS_APPSTORE_DESC": " Der Apple App Store ist eine Vertriebsplattform für digitale Anwendungen für iOS, der von Apple Inc The Apple App Store entwickelt und gepflegt wird.",
    "IOS_APPSTORE": "iOS Appstore",
    "IOS_OTA_UPDATE_DESC": " iOS OTA-Update ist das Protokoll für iOS-Updates Over The Air ",
    "IOS_OTA_UPDATE": "IP-Adresse",
    "IOS_OTHERS": "iOS (andere)",
    "IOS_TUNES": "iOS iTunes-App",
    "IOT": "IoT-Datenverkehr",
    "IP_ABUSE_CHECK_DESCRIPTION": "IPs überprüfen, die möglicherweise Proxys missbrauchen",
    "IP_ABUSE_CHECK": "IP-Missbrauchsprüfung",
    "IP_ADDRESS_FROM": "IP-Adresse von",
    "IP_ADDRESS_HA_DEVICE": "Geben Sie die Virtual IP-Adresse für das HA-Gerät ein.",
    "IP_ADDRESS_LAN_SECTION": "Geben Sie eine IP-Adresse für den LAN-Bereich Ihres Geräts ein.",
    "IP_ADDRESS_OPTIONAL": "IP-Adresse (optional)",
    "IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN": "IP-Adresse oder FQDN oder Wildcard-FQDN",
    "IP_ADDRESS_OR_FQDN": "IP-Adresse oder FQDN",
    "IP_ADDRESS_OR_WILDCARD_FQDN": "IP-Adresse oder Wildcard-FQDN",
    "IP_ADDRESS_RANCE_CIDR": "IP-Adressbereich / CIDR",
    "IP_ADDRESS_SHOULD_NOT_BE_PART_OF_ADDRESS_RANGES_POOL": "Die IP-Adresse darf nicht Teil des DHCP-Adresspools sein.",
    "IP_ADDRESS_TO": "IP-Adresse bis",
    "IP_ADDRESS_WAN_SECTION": "Geben Sie eine IP-Adresse für den WAN-Bereich Ihres Geräts ein.",
    "IP_ADDRESS": "IP-Adresse",
    "IP_ADDRESSES": "IP-Adresse",
    "IP_ADDRESSESS": "IP-Adresse",
    "IP_BASED_COUNTRIES": "IP-basierte Länder",
    "IP_CAT_LOOKUP": "Dynamische IP CAT-Ermittlung aktivieren",
    "IP_CATEGORIES": "IP-Kategorien",
    "IP_CONNECT_TRANSPARENT": "Switch VERBINDET sich mit IP:Port in transparenten Modus",
    "IP_DESC": " Das Internet-Protokoll (IP) ist das Haupt-Kommunikationsprotokoll der Internet-Protokoll-Suite für die Übertragung von Datagrammen über Netzgrenzen hinweg.",
    "IP_EXAMPLE_WITH_RANGE_CIDR": "Beispiel: ********, ********, ********-********, ********/24",
    "IP_EXAMPLE": "Beispiel: ********,********",
    "IP_FQDN_GROUPS": "IP- und FQDN-Gruppen",
    "IP_INFO": "IP-Informationen",
    "IP_POOL": "IP-Pool",
    "IP_UNKNOWN_DESC": " Identifiziert IP-Traffic, für den granularere App  nicht ermittelt werden kann.",
    "IP_UNKNOWN": "IP unbekannt",
    "IP": "IP",
    "IP6_DESC": " Internet Protocol Version 6 (IPv6) ist die neueste Version des Internet-Protokolls, das für Computer in Netzwerken ein Identifikations- und Ortungssystem bietet und den Datenverkehr über das Internet leitet ",
    "IP6": "IP6",
    "IPASS_DESC": " iPass ist der Branchenpionier in den Bereichen globaler, mobiler Konnektivität und ermöglicht unbegrenten Zugang zu unbeschränktem Inhalt auf einer unbegrenzten Zahl von Geräten",
    "IPASS": "Ipass",
    "IPERF_DESC": " Das iperf Protokoll wird dem selbsternannten Tool zur Messung der für Netzwerk-Performance verwendet",
    "IPERF": "Iperf",
    "IPLAYER_DESC": " iPlayer",
    "IPLAYER": "iPlayer",
    "IPSEC_DESC": " IPSec-Protokoll bietet Dienstleistungen zur Sicherung der Hostkommunikation. IPsec bietet zwei Authentication Header (AH) Sicherheitsservices; Diese erlauben die Authentifizierung des Absenders und das einkapseln der Security Payload (ESP); Damit wird die Authentifizierung des Absenders und die Verschlüsselung von Daten ermöglicht.",
    "IPSEC": "IPSEC",
    "IPV4_ALL_DESTINATION_GROUP_NAME": "IP V4-Gruppenname Alle Ziele",
    "IPV4_DNS_RESOLUTION_ONLY": "Nur IPv4-DNS-Auflösung",
    "IPV4": "IPv4 Encapuslation",
    "IPV6_HERE": "IPv6 i-am-here",
    "IPV6_WHERE": "IPv6 where-are-you",
    "IPV6": "IP6 header",
    "IPV6CP_DESC": " Dieses Protokoll ist für die Einrichtung und Konfiguration von IPv6 über PPP erforderlich",
    "IPV6CP": "IPV6CP",
    "IPXRIP_DESC": " RIPMAX ist das Äquivalent des RIP-Protokolls in Novell-Netzwerken",
    "IPXRIP": "RIPIPX",
    "IQIYI_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host iqiyi.com. Es klassifiziert auch den SSL-Traffic zu dem Common Name .iqiyi.com",
    "IQIYI": "iqiyi.com",
    "IRAN_ASIA_TEHRAN": "Asien/Teheran",
    "IRAN": "Iran",
    "IRAQ_ASIA_BAGHDAD": "Asien/Baghdad",
    "IRAQ": "Irak",
    "IRC_DESC": " IRC (Internet Relay Chat) ist ein Instant Messaging Protokoll.",
    "IRC_GALLERIA_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host irc-galleria.net",
    "IRC_GALLERIA": "IRC-Galleria",
    "IRC_TRANSFER_DESC": " Dieses Protokoll wird benutzt, um Daten via IRC zu transferieren",
    "IRC_TRANSFER": "IRC Dateiübertragung",
    "IRC": "IRC",
    "IRCS_DESC": " IRCs ist die sichere Version des IRC-Protokolls",
    "IRCS": "Sicherer IRC",
    "IRELAND_EUROPE_DUBLIN": "Europa / Dublin",
    "IRELAND": "Irland",
    "IS_NULL": "Ist null",
    "ISAE_3402": "ISAE 3402",
    "ISAKMP_DESC": " Das Internet Security Association and Key Management Protocol (ISAKMP) legt das Verfahren und Paketformate fest, um Sicherheitszuordnungen (SA) zu etablieren, zu verhandeln, zu ändern und zu löschen",
    "ISAKMP": "ISAKMP",
    "ISLE_OF_MAN_EUROPE_ISLE_OF_MAN": "Europa / Isle of Man",
    "ISLE_OF_MAN": "Isle of Man",
    "ISRAEL_ASIA_JERUSALEM": "Asien/Jerusalem",
    "ISRAEL": "Israel",
    "ISSUER": "Aussteller",
    "ITALKI_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host italki.com. Es klassifiziert auch den SSL-Traffic zu dem Common Name .italki.com. ",
    "ITALKI": "italki",
    "ITALY_EUROPE_ROME": "Europa / Rom",
    "ITALY": "Italien",
    "ITALYNORTH": "(Europa) Italien Nord",
    "ITEMS_TOTAL": "Elemente gesamt",
    "ITSMY_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host mobile.itsmy.com",
    "ITSMY": "GameCloud (itsmy.com)",
    "ITUNES_DESC": " iTunes ist eine proprietäre digital Media Player-Applikation von Apple, die für das Abspielen und Verwalten digitaler Musik und von Video-Dateien verwendet wird",
    "ITUNES": "iTunes",
    "ITUNESU": "iTunes U",
    "IVORY_COAST": "Elfenbeinküste",
    "IWF": "IWF",
    "IWIW_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host iwiw.hu",
    "IWIW": "Iwiw",
    "JABBER_DESC": " Jabber ist ein offener Standard Instant Messaging- und Präsenz-System auf Basis des XMPP-Protokolls",
    "JABBER_TRANSFER_DESC": " Jabber Transfer ist ein offener Standard zum Datentransfer zwischen zwei Jabber-Clients",
    "JABBER_TRANSFER": "Jabber Dateiübertragung",
    "JABBER": "Jabber",
    "JAIKU_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jaiku.com",
    "JAIKU": "jaiku.com",
    "JAILBREAK": "Jailbreak aktiviert",
    "JAILBROKEN_ROOTED": "Jailbroken/Rooted",
    "JAJAH_DESC": " Jajah ist ein VoIP-Provider im Besitz von Telefonica Europe",
    "JAJAH": "Jajah",
    "JAMAICA_AMERICA_JAMAICA": "Amerika/Jamaika",
    "JAMAICA": "Jamaika",
    "JAMMERDIRECT_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jammerdirect.com",
    "JAMMERDIRECT": "Jammer Direct",
    "JANGO_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jango.com",
    "JANGO": "Jango",
    "JANUS_END": "Ende der Janus-Fehlercodes.",
    "JAPAN_ASIA_TOKYO": "Asien/Tokio",
    "JAPAN": "Japan",
    "JAPANEAST": "(Asien-Pazifikraum) Japan Ost",
    "JAPANWEST": "(Asien-Pazifikraum) Japan West",
    "JAVA_UPDATE_DESC": " Java-Update ist das Protokoll für das Update der Java Virtual Machines, auch JVM genannt",
    "JAVA_UPDATE": "Java Update",
    "JEDI_DESC": " JEDI ist der Name des Citrix Streaming-Verbindungsprotokolls",
    "JEDI": "JEDI",
    "JERSEY_EUROPE_JERSEY": "Europa / Jersey",
    "JERSEY": "Jersey",
    "JINGDONG_DESC": " Beliebter chinesischer Online Hi-Tech-Shop",
    "JINGDONG": "JingDong",
    "JIOINDIACENTRAL": "(Asien-Pazifikraum) Jio Indien Mitte",
    "JIOINDIAWEST": "(Asien-Pazifikraum) Jio Indien West",
    "JIRA_DESC": " Dieses Protokoll-Plugin klassifiziert den HTTP-Verkehr zum Host onjira.com. Es klassifiziert auch den SSL-Verkehr zum Common Name onjira.com.",
    "JIRA": "JIRA",
    "JIVE_DESC": " Jive Software ist ein Software-Unternehmen in der Social Business Software-Industrie mit Sitz in Palo Alto, Kalifornien",
    "JIVE": "Jive",
    "JNE_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jne.co.id",
    "JNE": "JNE",
    "JOB_EMPLOYMENT_SEARCH_DESC": " Seiten mit Stellenangeboten oder Beschäftigungsmöglichkeiten.",
    "JOB_EMPLOYMENT_SEARCH": "Stellenanzeigen Suche",
    "JOB_SEARCH_DESC": " Seiten mit Stellenangeboten oder Beschäftigungsmöglichkeiten.",
    "JOB_SEARCH": "Stellenanzeigen Suche",
    "JOBSTREET_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jobstreet.co.id",
    "JOBSTREET": "JobStreet",
    "JOONGANG_DAILY_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an die Hosts www.joins.com und www.joinsmsn.com",
    "JOONGANG_DAILY": "Joongang Daily",
    "JOOST_DESC": " Joost",
    "JOOST": "Joost",
    "JORDAN_ASIA_AMMAN": "Asien/Amman",
    "JORDAN": "Jordan",
    "JPEG": "Jpeg Datei",
    "JS_VIEW": "JS-Ansicht",
    "JSON_CLOUDINFO_STAGGER_SIZE": "Anzahl der Instanzen, deren Cloudinfo von Cloud CA an FCC CA gesendet wird",
    "JSON_CLOUDINFO": "Cloudinfo von Cloud-CA an FCC CA als JSON senden",
    "JSONNOTFOUND": "JSON-Datei nicht gefunden",
    "JUBII_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host jubii.dk",
    "JUBII": "Jubii",
    "JUSTIN_TV_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host justin.tv. Es klassifiziert auch den SSL-Traffic zu dem Common Name .justin.tv",
    "JUSTIN_TV": "Justin.tv",
    "K_12_SEX_EDUCATION": "K-12 Sexualkunde",
    "K_12": "K-12",
    "KAIOO_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kaioo.com",
    "KAIOO": "kaioo.com",
    "KAIXIN_CHAT_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kaixin001.com",
    "KAIXIN_CHAT": "kaixin",
    "KAKAKU_DESC": " Website für Preisvergleiche und Produkttests",
    "KAKAKU": "kakaku.com",
    "KAKAOTALK_DESC": " Kakaotalk ist eine Instant Messaging-Plattform für mobile Geräte; Benutzer oder Benutzergruppen können Nachrichten, Fotos, Videos und Kontaktinformationen senden",
    "KAKAOTALK": "KakaoTalk",
    "KANKAN_DESC": " Chinesische Video-Streaming-Website",
    "KANKAN": "kankan.com",
    "KAPANLAGI_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kapanlagi.com",
    "KAPANLAGI": "KapanLagi",
    "KAROSGAME_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host karosgame.ru",
    "KAROSGAME": "Karos (karosgame.ru)",
    "KASKUS_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kaskus.co.id",
    "KASKUS": "Kaskus",
    "KASPERSKY_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kaspersky.com",
    "KASPERSKY_UPDATE_DESC": " Kaspersky-Update ist das Protokoll für Kaspersky Software-Updates",
    "KASPERSKY_UPDATE": "Kaspersky Update",
    "KASPERSKY": "Kaspersky",
    "KAZAA_DESC": " Kazaa ist ein Peer-to-Peer-Protokoll",
    "KAZAA": "Kazaa",
    "KAZAKHSTAN_ASIA_ALMATY": "Asien/Almaty",
    "KAZAKHSTAN_ASIA_AQTAU": "Asien/Aqtau",
    "KAZAKHSTAN_ASIA_AQTOBE": "Asien/Aqtobe",
    "KAZAKHSTAN_ASIA_ORAL": "Asien/Oral",
    "KAZAKHSTAN_ASIA_QYZYLORDA": "Asien/Qyzylorda",
    "KAZAKHSTAN": "Kasachstan",
    "KB_BANK_DESC": " Dieses Protokoll-Plugin klassifiziert den HTTP-Verkehr an den Host .kbstar.com. Es klassifiziert auch den SSL-Verkehr an den Common Name .kbstar.com.",
    "KB_BANK": "KB Bank (kbstar.com)",
    "KBS_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host www.kbs.co.kr",
    "KBSTAR_DESC": " Dieser Web-Service ist geschlossen",
    "KBSTAR": "kbstar (geschlossen)",
    "KEEPLIVE": "Keepalive-Datensätze senden, um die TCP-Verbindung aktiv zu halten, wenn keine Daten fließen",
    "KEEZMOVIES_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host keezmovies.com",
    "KEEZMOVIES": "KeezMovies",
    "KEK_ROTATION": "Schlüsselrotation für SME aktivieren ",
    "KEMENKUMHAM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kemenkumham.go.id",
    "KEMENKUMHAM": "kemenkumham.go.id",
    "KENYA_AFRICA_NAIROBI": "Afrika / Nairobi",
    "KENYA": "Kenia",
    "KERBEROS_SEC_DESC": "Kerberos ist ein Netzwerkprotokoll, das die Authentifizierung in unsicheren Netzwerken Ticket-basierend durchführt, um eine sichere Authentifizierung zu gewährleisten. ",
    "KERBEROS_SEC": "Kerberos",
    "KERBEROS_SHARED_KEY": "Domänen-Vertrauenspasswort",
    "KEY": "Schlüssel",
    "KHAN_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host khan.co.kr",
    "KHAN": "Khan (khan.co.kr)",
    "KHANACADEMY": "Khan Academy",
    "KICKASSTORRENTS_DESC": " KickAssTorrents ist eine beliebte Torrent- und Magnetsuchmaschine",
    "KICKASSTORRENTS": "KickAssTorrents",
    "KIK_DESC": " Kik Messenger ist ein chinesischer Instant Messaging-Service",
    "KIK": "Kik Messenger",
    "KINDLE": "Kindle",
    "KINO_DESC": " Dieses Protokoll-Plugin klassifiziert den HTTP-Verkehr an den Host kino.to.",
    "KINO": "Kino",
    "KIRIBATI_PACIFIC_ENDERBURY": "Pazifik/Enderbury",
    "KIRIBATI_PACIFIC_KIRITIMATI": "Pazifik/Kiritimati",
    "KIRIBATI_PACIFIC_TARAWA": "Pazifik/Tarawa",
    "KIRIBATI": "Kiribati",
    "KIWIBOX_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kiwibox.com",
    "KIWIBOX": "Kiwibox",
    "KLIKBCA_DESC": " Dieses Protokoll-Plugin klassifiziert den HTTP-Verkehr an den Host klikbca.com.",
    "KLIKBCA": "KlikBCA",
    "KOMPAS_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kompas.com",
    "KOMPAS": "Kompas",
    "KOMPASIANA_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host kompasiana.com",
    "KOMPASIANA": "Kompasiana",
    "KONAMINET_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host konaminet.jp. Es klassifiziert auch den SSL-Traffic zu dem Common Name konaminet.jp",
    "KONAMINET": "KONAMI",
    "KOOLIM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host koolim.com",
    "KOOLIM": "KoolIm",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG": "Asien/Pyongyang",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF": "Volksrepublik Korea",
    "KOREA_REPUBLIC_OF_ASIA_SEOUL": "Asien/Seoul",
    "KOREA_REPUBLIC_OF": "Republik Korea",
    "KOREA": "Korea",
    "KOREACENTRAL": "Asien-Pazifikraum (Korea Mitte)",
    "KOREASOUTH": "Asien-Pazifikraum (Korea Süd)",
    "KUWAIT_ASIA_KUWAIT": "Asien/Kuwait",
    "KUWAIT": "Kuwait",
    "KYRGYZSTAN_ASIA_BISHKEK": "Asia/Bishkek",
    "KYRGYZSTAN": "Kirgisistan",
    "L2TP_DESC": " L2TP (Layer Two Tunneling Protocol) ist eine Erweiterung des Point-to-Point Tunneling Protocols (PPTP), die einem Internet Service Provider (ISP) den Betrieb eines virtuellen privaten Netzwerks (VPN) über das Internet ermöglicht.",
    "L2TP": "L2TP",
    "LAN_DESTINATIONS_GROUP": "LAN-Zielgruppe",
    "LAN_DNS": "LAN-DNS",
    "LAN_IP_GROUP": "LAN-IP-Gruppe",
    "LAN_PRI_DNS": "Primärer LAN-DNS Server",
    "LAN_RX": "LAN Rx",
    "LAN_SEC_DNS": "Sekundärer LAN-DNS-Server",
    "LAN_TX": "LAN Tx",
    "LAN": "LAN",
    "LANGUAGE": "Sprache",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE": "Asien/Vientiane",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC": "Laos",
    "LAOS": "Laos",
    "LARGE": "Large",
    "last_1_hour": "Letzte 1 Stunde",
    "LAST_1_HOUR": "Letzte 1 Stunde",
    "last_1_min": "Letzte 1 Min.",
    "LAST_1_MIN": "Letzte 1 Minute",
    "last_1_month": "Letzter 1 Monat",
    "LAST_1_MONTH": "Letzter 1 Monat",
    "last_1_week": "Letzte 1 Woche",
    "LAST_1_WEEK": "Letzte 1 Woche",
    "last_10_hours": "Letzte 10 Stunden",
    "LAST_10_HOURS": "Letzte 10 Stunden",
    "last_15_mins": "Letzte 15 Min.",
    "LAST_15_MINS": "Letzte 15 Minuten",
    "last_2_hours": "Letzte 2 Stunden",
    "LAST_2_HOURS": "Letzte 2 Stunden",
    "last_2_mins": "Letzte 2 Min.",
    "LAST_2_MINS": "Letzte 2 Minuten",
    "LAST_24_HOURS": "Letzte 24 Stunden",
    "last_24_hrs": "Letzte 24 Stunden",
    "last_30_mins": "Letzte 30 Min.",
    "LAST_30_MINS": "Letzte 30 Minuten",
    "last_5_hours": "Letzte 5 Stunden",
    "LAST_5_HOURS": "Letzte 5 Stunden",
    "last_5_mins": "Letzte 5 Min.",
    "LAST_5_MINS": "Letzte 5 Minuten",
    "LAST_ACTIVE": "Zuletzt aktiv",
    "LAST_CONFIG_TEMPLATE_PUSH_FAILED": "Fehler beim letzten Push der Config-Vorlage",
    "LAST_CONNECTIVITY_TEST": "Letzter Konnektivitätstest",
    "LAST_HEARTBEAT_RECEIVED_ON": "Letzter Heartbeat empfangen am",
    "LAST_KNOW_IP": "Letzte bekannte IP-Adresse",
    "LAST_KNOWN_IP": "Letzte bekannte IP-Adresse",
    "LAST_MODIFIED_BY": "Zuletzt geändert von",
    "LAST_MODIFIED_ON": "Zuletzt geändert am",
    "LAST_SYNC": "Letzte Synchronisierung",
    "LAST_UPDATE": "Zuletzt aktualisiert",
    "LAST_UPDATES": "Letzte Aktualisierungen",
    "LAST_UPGRADE_ON": "Letztes Upgrade am",
    "LASTEST_SYNC": "Letzte Synchronisierung",
    "LATITUDE": "Breitengrad",
    "LATVIA_EUROPE_RIGA": "Europa / Riga",
    "LATVIA": "Lettland",
    "LAUNCH_CLOUDFORMATION_TEMPLATE_AWS_CONSOLE": "Cloudformation-Vorlage in der AWS-Konsole starten",
    "LAUNCH_CLOUDFORMATION": "Cloudformation starten",
    "LDAP_CONNECTION_DOWN": "LDAP Verbindung unterbrochen",
    "LDAP_DESC": " LDAP (Lightweight  Directory Access Protocol) ist ein Protokoll für den Zugriff auf Verzeichnisdienste. Windows-Umgebungen verwenden dieses Protokoll um Abfragen an Active Directory zu senden.",
    "LDAP_FAILURE": "LDAP Fehler",
    "LDAP_SETTINGS": "LDAP-Einstellungen",
    "LDAP_SUCCESS": "LDAP erfolgreich",
    "LDAP": "LDAP",
    "LDAPS_DESC": " Secure LDAP ist die sichere Version des LDAP-Protokolls.",
    "LDAPS": "LDAPS",
    "LEARN_TO_SETUP_EC2_INSTANCE": "Lernen, wie Sie die EC2-Instanz einrichten",
    "LEBANON_ASIA_BEIRUT": "Asien/Beirut",
    "LEBANON": "Libanon",
    "LESOTHO_AFRICA_MASERU": "Afrika / Maseru",
    "LESOTHO": "Königreich Lesotho",
    "LESS_THAN": "Kleiner als",
    "LIBERIA_AFRICA_MONROVIA": "Afrika / Monrovia",
    "LIBERIA": "Liberia",
    "LIBYA": "Libyen",
    "LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI": "Afrika / Tripoli",
    "LIBYAN_ARAB_JAMAHIRIYA": "Libyan Arab Jamahiriya",
    "LIECHTENSTEIN_EUROPE_VADUZ": "Europa / Vaduz",
    "LIECHTENSTEIN": "Liechtenstein",
    "LIMITED_AVAILABILITY": "Begrenzte Verfügbarkeit",
    "LIMITED": "Beschränkt",
    "LINGERIE_BIKINI": "Lingerie/Bikini",
    "LINK_SCORE": "Verbindungs-Score",
    "LINUX_OS": "Linux-Betriebssystem",
    "LINUX": "Linux",
    "LITHUANIA_EUROPE_VILNIUS": "Europa / Vilnius",
    "LITHUANIA": "Litauen",
    "LOAD_BALANCER_IP_ADDRESS": "Lastenausgleich-IP-Adresse",
    "LOAD_BALANCER": "Lastenausgleich",
    "LOAD_MORE": "Mehr laden",
    "LOC_DEFAULT": "Road Warrior",
    "LOCAL_EGRESS": "Lokaler Ausgang",
    "LOCAL_TIME_ZONE_CC_GROUP": "Lokale Zeitzone der Cloud Connector-Gruppe",
    "LOCATION_ALREADY_IN_USE_PLEASE_ENTER_A_NEW_LOCATION": "Standort ist nicht gültig. Geben Sie einen anderen ein.",
    "LOCATION_CREATION": "Standorterstellung",
    "LOCATION_DETAILS_OPTIONAL": "Standortdetails (optional)",
    "LOCATION_DETAILS": "Standortdetails",
    "LOCATION_GROUP_TYPE": "Standorttyp",
    "LOCATION_GROUP": "Standortgruppe",
    "LOCATION_GROUPS": "Standortgruppen",
    "LOCATION_INFORMATION": "Information über Standort",
    "LOCATION_MANAGEMENT": "Standortverwaltung",
    "LOCATION_NAME": "Standortname",
    "LOCATION_SUBLOCATION": "Standort/Unterstandort",
    "LOCATION_TEMPLATE": "Standortvorlage",
    "LOCATION_TEMPLATES": "Standortvorlagen",
    "LOCATION_TYPE": "Standorttyp",
    "LOCATION_UNAUTHENTICATED_AUP_FREQUENCY": "Benutzerdefinierte AUP-Häufigkeit (Tage)",
    "LOCATION": "Standort",
    "LOCATIONS": "Standorte",
    "LOG_AND_CONTROL_FORWARDING": "Protokoll- und Steuerungsweiterleitung",
    "LOG_AND_CONTROL_GATEWAY": "Protokollierungs- und Steuerungs-Gateway",
    "LOG_AND_CONTROL_GW": "Protokollierungs- und Steuerungs-GW",
    "LOG_AND_CONTROL": "Protokollierung und Steuerung",
    "LOG_GW_CONN_CLOSE": "Aktive Verbindung zum Protokollierungs-Gateway geschlossen.",
    "LOG_GW_CONN_SETUP_FAIL": "Verbindung zum Protokollierungs-Gateway fehlgeschlagen (interner Fehler).",
    "LOG_GW_CONNECT_FAIL": "Verbindung zum Protokollierungs-Gateway fehlgeschlagen (Netzwerkfehler).",
    "LOG_GW_DNS_RESOLVE_FAIL": "DNS-Auflösung für Protokollierungs-Gateway fehlgeschlagen.",
    "LOG_GW_KA_FAIL": "Keepalive für Verbindung zum Protokollierungs-Gateway fehlgeschlagen.",
    "LOG_GW_NO_CONN": "Verbindung zum Protokollierungs-Gateway vom Client noch nicht eingeleitet.",
    "LOG_GW_PAC_RESOLVE_FAIL": "PAC-Auflösung für Protokollierungs-Gateway fehlgeschlagen.",
    "LOG_GW_PAC_RESOLVE_NOIP": "PAC-Auflösung für Protokollierungs-Gateway lieferte keine IPS zurück.",
    "LOG_GW_PROTO_MSG_ERROR": "Nachrichtenformatfehler in der Antwort des Protokollierungs-GW.",
    "LOG_GW_SRV_ERR_RESPONSE": "Protokollierungs-Gateway hat eine HTTP-Fehlerantwort vom Server erhalten.",
    "LOG_GW_UNHEALTHY": "Protokollierungs-Gateway ist fehlerhaft (transienter Zustand).",
    "LOG_INFO_TYPE": "Protokollinformationstyp",
    "LOG_STREAMING": "Protokoll-Streaming",
    "LOG_TIME": "Protokollierungszeit",
    "LOG_TYPE": "LOG-TYP",
    "LOGGED_TIME": "Protokollierte Zeit",
    "LOGGING": "Protokollierung",
    "LOGIN_ID": "Login ID",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_GROUP": "Wählen Sie den Namen einer App Connector-Gruppe aus der Dropdownliste aus.",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_KEY": "Wählen Sie einen Bereitstellungschlüssel aus der Dropdownliste aus.",
    "LOGIN_TYPE": "Anmeldetyp",
    "LOGIN": "Anmeldung",
    "LOGS": "Logs",
    "LONGITUDE": "Längengrad",
    "LOOKUP_URL_CATEGORY": "Such-URL-Klassifizierungen",
    "LOOKUP": "Lookup URL",
    "LUXEMBOURG_EUROPE_LUXEMBOURG": "Europa / Luxemburg",
    "LUXEMBOURG": "Luxemburg",
    "MAC_ADDRESS": "MAC-Adresse",
    "MAC_OS": "Mac",
    "MACAO_ASIA_MACAU": "Asien/Macau",
    "MACAO": "Macao",
    "MACAU": "Macau",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE": "Europa / Skopje",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF": "ehemalige jugoslawische Republik Mazedonien",
    "MACEDONIA": "Mazedonien",
    "MADAGASCAR_INDIAN_ANTANANARIVO": "Indisch/Antananarivo",
    "MADAGASCAR": "Madagaskar",
    "MALAWI_AFRICA_BLANTYRE": "Afrika / Blantyre",
    "MALAWI": "Malawi",
    "MALAYSIA_ASIA_KUALA_LUMPUR": "Asien/Kuala Lumpur",
    "MALAYSIA_ASIA_KUCHING": "Asien/Kuching",
    "MALAYSIA": "Malaysien",
    "MALDIVES_INDIAN_MALDIVES": "Indisch/Maldiven",
    "MALDIVES": "Malediven",
    "MALI_AFRICA_BAMAKO": "Afrika / Bamako",
    "MALI": "Mali",
    "MALICIOUS_TLD": "Schädliche TLDs",
    "MALTA_EUROPE_MALTA": "Europa / Malta",
    "MALTA": "Malta",
    "MALWARE_SITE": "Schadcode",
    "MANAGED_APP_DEF_ZPA": "Definition verwalteter Apps mit ZPA",
    "MANAGED_APP_DEF": "Definition verwalteter Apps",
    "Management IP": "Management IP",
    "MANAGEMENT_DEFAULT_GATEWAY": "Standard-Verwaltungs-Gateway",
    "MANAGEMENT_DETAILS": "Verwaltungsdetails",
    "MANAGEMENT_DNS_SERVER": "Verwaltungs-DNS-Server",
    "MANAGEMENT_INFORMATION": "Verwaltungsinformationen",
    "MANAGEMENT_INTERFACE": "Verwaltungsschnittstelle",
    "MANAGEMENT_IP_ADDRESS_POOL": "Pool von Verwaltungs-IP-Adressen",
    "MANAGEMENT_IP_ADDRESS": "Verwaltungs-IP-Adresse",
    "MANAGEMENT_IP": "Management IP",
    "MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS": "Abgehende Verwaltungs-Gateway-IP-Adresse",
    "MANAGEMENT": "Verwaltung",
    "MANUAL_MANAGEMENT_IP": "Manuelle Verwaltungs-IP",
    "MANUAL_SERVICE_IP": "Manuelle Dienst-IP",
    "MANUAL": "Benuterhandbuch",
    "MARIJUANA": "Marijuana",
    "MARSHALL_ISLANDS_PACIFIC_KWAJALEIN": "Pacific/Kwajalein",
    "MARSHALL_ISLANDS_PACIFIC_MAJURO": "Pacific/Majuro",
    "MARSHALL_ISLANDS": "Marshallinseln",
    "MARTINIQUE_AMERICA_MARTINIQUE": "Amerika/Martinique",
    "MARTINIQUE": "Martinique",
    "MATURE_HUMOR": "Reifer Humor",
    "MAURITANIA_AFRICA_NOUAKCHOTT": "Afrika / Nouakchott",
    "MAURITANIA": "Mauretanien",
    "MAURITIUS_INDIAN_MAURITIUS": "Indisch/Mauritius",
    "MAURITIUS": "Mauritius",
    "MAX_AMF_NUMBER": "Es können maximal 5 AMFs hinzugefügt werden.",
    "MAX_CAPACITY": "Maximale Kapazität",
    "MAX_CHARACTER_LIMIT_EXCEEDED": "Maximale Anzahl von Zeichen überschritten",
    "MAX_EC_COUNT": "Max. Anzahl",
    "MAX_INTERFACES_NUMBER": "Die maximal zulässige Anzahl an Schnittstellen wurde hinzugefügt.",
    "MAX_LEASE_TIME": "Max. Lease-Dauer (Sek.)",
    "MAX_NUM_DESINATION_ADRESS_IS_1000": "Pro Regel sind höchstens 1000 Zieladressen zulässig.",
    "MAX_REUSE_PROVISIONING_KEY": "Maximale Wiederverwendung des Bereitstellungschlüssels",
    "MAX_STATIC_ROUTES_NUMBER": "Die maximale Anzahl statischer Routen beträgt 32.",
    "MAX_SUB_INTERFACES_NUMBER": "Die maximale Anzahl an Schnittstellen wurde erreicht.",
    "MAX_SUBINTERFACE_STATIC_LEASES": "Die maximale Anzahl statischer Leases beträgt 32.",
    "MAX_USER_TUNNELS_PER_CC": "Maximale Anzahl an User-Tunneln pro Connector",
    "MAX_VALUE_LIMIT_ERROR": "Wert überschreitet das Limit von",
    "MAX_WAN_INTERFACES_NUMBER": "Es können höchstens 2 WAN-Schnittstellen hinzugefügt werden.",
    "MAX": "Max",
    "MAYOTTE_INDIAN_MAYOTTE": "Indisch/Mayotte",
    "MAYOTTE": "Mayotte",
    "ME_CENTRAL_1": "Mittlerer Osten (VAE)",
    "ME_CENTRAL1_A": "me-central1-a",
    "ME_CENTRAL1_B": "me-central1-b",
    "ME_CENTRAL1_C": "me-central1-c",
    "ME_CENTRAL1": "me-central1",
    "ME_SOUTH_1": "Mittlerer Osten (Bahrain)",
    "ME_SOUTH_1A": "me-south-1a",
    "ME_SOUTH_1B": "me-south-1b",
    "ME_SOUTH_1C": "me-south-1c",
    "ME_WEST1_A": "me-west1-a",
    "ME_WEST1_B": "me-west1-b",
    "ME_WEST1_C": "me-west1-c",
    "ME_WEST1": "me-west1",
    "MEDIUM": "Medium",
    "MEMORY": "Speicher",
    "MEXICO_AMERICA_CANCUN": "Amerika/Cancun",
    "MEXICO_AMERICA_CHIHUAHUA": "Amerika/Chihuahua",
    "MEXICO_AMERICA_HERMOSILLO": "Amerika/Hermosillo",
    "MEXICO_AMERICA_MAZATLAN": "Amerika/Mazatlan",
    "MEXICO_AMERICA_MERIDA": "Amerika/Merida",
    "MEXICO_AMERICA_MEXICO_CITY": "Amerika/Mexico City",
    "MEXICO_AMERICA_MONTERREY": "Amerika/Monterrey",
    "MEXICO_AMERICA_TIJUANA": "Amerika/Tijuana",
    "MEXICO": "Mexiko",
    "MGCP_CA_DESC": "Media Gateway Control Protocol CA Service",
    "MGCP_CA": "MGCP-Anrufagent",
    "MGCP_DESC": " MGCP-Protokoll wird als Signalprotokoll für Voice-IP-Anwendungen verwendet",
    "MGCP_UA_DESC": "Media Gateway Control Protocol UA Service",
    "MGCP_UA": "MGCP-Benutzeragent",
    "MGCP": "MGCP",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE": "Pacific/Kosrae",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE": "Pacific/Ponape",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK": "Pacific/Truk",
    "MICRONESIA_FEDERATED_STATES_OF": "(Föderierte Staaten von) Mikronesien",
    "MICRONESIA": "Mikronesien",
    "MICROSOFT_AZURE": "Microsoft Azure",
    "MICROSOFT_HYPER_V": "Microsoft Hyper-V",
    "MILITANCY_HATE_AND_EXTREMISM": "Militär/Hass und Extremismus",
    "MILITARY": "Militär",
    "MIN_CAPACITY": "Mindestkapazität",
    "MIN_VALUE_LIMIT_ERROR": "Wert liegt unter dem Limit von",
    "MINUTES": "Minute",
    "MISCELLANEOUS_OR_UNKNOWN": "Verschiedenes oder Unbekannt",
    "MODEL_NUMBER": "Modellnummer",
    "MODEL": "Modell",
    "MOLDOVA_EUROPE_CHISINAU": "Europa / Chisinau",
    "MOLDOVA": "Moldawien",
    "MONACO_EUROPE_MONACO": "Europa / Monaco",
    "MONACO": "Monaco",
    "MONDAY": "Montag",
    "MONGOLIA_ASIA_CHOIBALSAN": "Asien/Choibalsan",
    "MONGOLIA_ASIA_HOVD": "Asien/Hovd",
    "MONGOLIA_ASIA_ULAANBAATAR": "Asien/Ulanbaatar",
    "MONGOLIA": "Mongolien",
    "MONTENEGRO_EUROPE_PODGORICA": "Europa / Podgorica",
    "MONTENEGRO": "Montenegro",
    "MONTSERRAT_AMERICA_MONTSERRAT": "Amerika/Montserrat",
    "MONTSERRAT": "Montserrat",
    "MORE_ITEMS_SELECTED": "Weitere Elemente ausgewählt",
    "MOROCCO_AFRICA_CASABLANCA": "Afrika / Casablanca",
    "MOROCCO": "Marokko",
    "MOZAMBIQUE_AFRICA_MAPUTO": "Afrika / Maputo",
    "MOZAMBIQUE": "Mosambik",
    "MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS": "Microsoft Azure-Bereitstellungsanleitung für NSS",
    "MSI_URL_32BITS": "MSI-URL (32 Bit)",
    "MSI_URL_64BITS": "MSI-URL (64 Bit)",
    "MSN_DESC": " Das MSN-Protokoll ermöglicht den Austausch von Instant Messages. Es wird von der Microsoft Software Microsoft Messenger verwendet.",
    "MSN_EXPLORER": "MSN Explorer",
    "MSN_GROUPS_DEPRECATED": "MSN Groups",
    "MSN_GROUPS_DESC": " MSN Groups war eine Teil des MSN-Netzwerks, das Online-Communities gehosted hat, Webseiten mit den darin enthaltenen Bildern verwaltete und ein Message Board enthielt. MSN Groups wurde im Februar 2009 im Rahmen einer Migration von Online-Anwendungen und Diensten für die Windows Live Marke eingestellt und später als Windows Live Groups neu gestartet.",
    "MSN_GROUPS": "MSN Groups",
    "MSN_MESSENGER": "MSN Messenger",
    "MSN_MSDW": "MSN MSDW",
    "MSN_SEARCH_DESC": " Dieses Protokoll wird für das Senden von Anwenderanfragen  an die Suchmaschine MSN Live verwendet",
    "MSN_SEARCH": "MSN Search",
    "MSN_VIDEO_DESC": " Dieses Protokoll wurde von MSN Messenger für Video-Gespräche verwendet (Findet keine Anwendung mehr seit MSN Version 8.X )",
    "MSN_VIDEO": "MSN Video",
    "MSN_WEB_MESSENGER": "MSN Messenger",
    "MSN": "MSN",
    "MSNMOBILE_DESC": " MSN Mobile ist der MSN Instant Messenger für mobile Geräte",
    "MSNMOBILE": "MSN Mobile",
    "MTS_ERROR": "MTS-App-Serverfehler.",
    "MTU_TITLE": "MTU",
    "MULTIFEEDLOG": "Metriken",
    "MULTIPLE_APPLIANCES_ADDED_INFO": "Sie können die neuen Appliances auf der Seite 'Appliances' sehen. Weitere Informationen finden Sie im {1}Connector-Hilfeportal{2}.",
    "MULTIPLE_APPLIANCES_ADDED": "neue Appliances wurden zu Ihrem Mandanten hinzugefügt.",
    "MULTIPLE": "Mehrere",
    "MUSIC": "Musik",
    "MY_ACTIVATION_STATUS": "MEIN AKTIVIERUNGSSTATUS",
    "MY_PROFILE": "Mein Profil",
    "MYANMAR_ASIA_RANGOON": "Asien/Rangoon",
    "MYANMAR": "Myanmar",
    "NA": "N/Z",
    "NAME_MAX_LIMIT_ERROR": "Dieses Feld darf nicht mehr als 255 Zeichen enthalten",
    "NAME_VALUE_PAIRS": "Namen-/Wertepaare",
    "NAME": "Name",
    "NAMESPACE_OPTIONAL": "Namespace (optional)",
    "NAMESPACE": "Namespace",
    "NAMIBIA_AFRICA_WINDHOEK": "Afrika / Windhoek",
    "NAMIBIA": "Namibia",
    "NANOLOG_STREAMING_SERVICES": "Nanolog Streaming-Dienst",
    "NAT_IP_ADDRESS": "NAT-IP-Adresse",
    "NAURU_PACIFIC_NAURU": "Pacific/Nauru",
    "NAURU": "Nauru",
    "NAVIGATE_TO_ADMINISTRATION": "Zur Verwaltung navigieren",
    "NEPAL_ASIA_KATMANDU": "Asien/Katmandu",
    "NEPAL": "Nepal",
    "NET_MASK": "Netzmaske",
    "NETBIOS_DESC": "NetBIOS Name/Datagram Service",
    "NETBIOS": "NetBIOS",
    "NETHERLANDS_ANTILLES_AMERICA_CURACAO": "Amerika/Curacao",
    "NETHERLANDS_ANTILLES": "Niederländische Antillen",
    "NETHERLANDS_EUROPE_AMSTERDAM": "Europa/ Amsterdam",
    "NETHERLANDS": "Niederlande",
    "NETMEETING_DESC": "Microsoft NetMeeting wird für Telekonferenzen über das Internet benutzt",
    "NETMEETING_ILS_DESC": " NetMeeting ILS ist ein Protokoll das zwischen Netmeeting und Internet Locator Server verwendet ist (ILS). Netmeeting ist ein VoIP-und Mehrpunkt -Videokonferenz-Client in vielen Versionen von Microsoft Windows enthalten",
    "NETMEETING_ILS": "NetMeeting ILS",
    "NETMEETING": "NetMeeting",
    "NETWORK_INTERFACE_ID": "Netzwerkschnittstellen-ID",
    "NETWORK_PROTOCOL_ADFS": "Any distributed FS",
    "NETWORK_PROTOCOL_AH": "IP5 Auth Header",
    "NETWORK_PROTOCOL_AHIP": "any host internal protocol",
    "NETWORK_PROTOCOL_APES": "any private encr. scheme",
    "NETWORK_PROTOCOL_ARGUS": "Argus",
    "NETWORK_PROTOCOL_AX25": "AX.25 Frames",
    "NETWORK_PROTOCOL_BHA": "BHA",
    "NETWORK_PROTOCOL_BLT": "Bulk Data Transfer",
    "NETWORK_PROTOCOL_BRSATMON": "BackRoom SATNET Monitoring",
    "NETWORK_PROTOCOL_CARP": "CARP",
    "NETWORK_PROTOCOL_CFTP": "CFTP",
    "NETWORK_PROTOCOL_CHAOS": "Chaos",
    "NETWORK_PROTOCOL_CMTP": "Control Message Transport",
    "NETWORK_PROTOCOL_CPHB": "Comp. Prot. HeartBeat",
    "NETWORK_PROTOCOL_CPNX": "Comp. Prot. Net. Executive",
    "NETWORK_PROTOCOL_DDP": "Datagram Delivery",
    "NETWORK_PROTOCOL_DGP": "dissimilar gateway prot.",
    "NETWORK_PROTOCOL_DSTOPTS": "IP6 destination option",
    "NETWORK_PROTOCOL_EGP": "exterior gateway protocol",
    "NETWORK_PROTOCOL_EMCON": "EMCON",
    "NETWORK_PROTOCOL_ENCAP": "encapsulation header",
    "NETWORK_PROTOCOL_EON": "ISO cnlp",
    "NETWORK_PROTOCOL_ESP": "IP6 encap. security payload",
    "NETWORK_PROTOCOL_ETHERIP": "Ethernet IP encapsulation",
    "NETWORK_PROTOCOL_FRAGMENT": "IP6 fragmentation header",
    "NETWORK_PROTOCOL_GGP": "gateway^2 (deprecated)",
    "NETWORK_PROTOCOL_GMTP": "GMTP",
    "NETWORK_PROTOCOL_GRE": "General Routing Encap.",
    "NETWORK_PROTOCOL_HELLO": "hello routing protocol",
    "NETWORK_PROTOCOL_HMP": "Host Monitoring",
    "NETWORK_PROTOCOL_ICMP": "control message protocol",
    "NETWORK_PROTOCOL_ICMPV6": "ICMP6",
    "NETWORK_PROTOCOL_IDP": "xns idp",
    "NETWORK_PROTOCOL_IDPR": "InterDomain Policy Routing",
    "NETWORK_PROTOCOL_IDRP": "InterDomain",
    "NETWORK_PROTOCOL_IGMP": "group mgmt protocol",
    "NETWORK_PROTOCOL_IGP": "NSFNET-IGP",
    "NETWORK_PROTOCOL_IGRP": "Cisco/GXS IGRP",
    "NETWORK_PROTOCOL_IL": "IL Transport Protokoll",
    "NETWORK_PROTOCOL_INLSP": "Integ. Net Layer Security",
    "NETWORK_PROTOCOL_INP": "Merit Internodal",
    "NETWORK_PROTOCOL_IP": "dummy for IP",
    "NETWORK_PROTOCOL_IPCOMP": "payload compression (IPComp)",
    "NETWORK_PROTOCOL_IPCV": "Packet Core Utility",
    "NETWORK_PROTOCOL_IPEIP": "IP encapsulated in IP",
    "NETWORK_PROTOCOL_IPPC": "Pluribus Packet Core",
    "NETWORK_PROTOCOL_IPV4": "IPv4 Encapuslation",
    "NETWORK_PROTOCOL_IPV6": "IP6 header",
    "NETWORK_PROTOCOL_IRTP": "Reliable Transaction",
    "NETWORK_PROTOCOL_KRYPTOLAN": "Kryptolan",
    "NETWORK_PROTOCOL_LARP": "Locus Address Resoloution",
    "NETWORK_PROTOCOL_LEAF1": "Leaf-1",
    "NETWORK_PROTOCOL_LEAF2": "Leaf-2",
    "NETWORK_PROTOCOL_MEAS": "DCN Measurement Subsystems",
    "NETWORK_PROTOCOL_MHRP": "Mobile Host Routing",
    "NETWORK_PROTOCOL_MICP": "Mobile Int.ing control",
    "NETWORK_PROTOCOL_MOBILE": "IP Mobility",
    "NETWORK_PROTOCOL_MTP": "Multicast Transport",
    "NETWORK_PROTOCOL_MUX": "Multiplexing",
    "NETWORK_PROTOCOL_ND": "Sun net disk proto (temp.)",
    "NETWORK_PROTOCOL_NHRP": "Next Hop Resolution",
    "NETWORK_PROTOCOL_NO_NEXT_HDR": "IP6 no next header",
    "NETWORK_PROTOCOL_NSP": "Netzwerk Dienst",
    "NETWORK_PROTOCOL_NVPII": "network voice",
    "NETWORK_PROTOCOL_OLD_DIVERT": "OLD divert pseudo-proto",
    "NETWORK_PROTOCOL_OSPFIGP": "OSPFIGP",
    "NETWORK_PROTOCOL_PFSYNC": "PFSYNC",
    "NETWORK_PROTOCOL_PGM": "PGM",
    "NETWORK_PROTOCOL_PIGP": "private interior gateway",
    "NETWORK_PROTOCOL_PIM": "Protocol Independent Mcast",
    "NETWORK_PROTOCOL_PRM": "Packet Radio Measurement",
    "NETWORK_PROTOCOL_PUP": "pup",
    "NETWORK_PROTOCOL_PVP": "Packet Video Protocol",
    "NETWORK_PROTOCOL_RAW": "raw IP packet",
    "NETWORK_PROTOCOL_RCCMON": "BBN RCC Monitoring",
    "NETWORK_PROTOCOL_RDP": "Reliable Data",
    "NETWORK_PROTOCOL_ROUTING": "IP6 routing header",
    "NETWORK_PROTOCOL_RSVP": "resource reservation",
    "NETWORK_PROTOCOL_RVD": "Remote Virtual Disk",
    "NETWORK_PROTOCOL_SATEXPAK": "SATNET/Backroom EXPAK",
    "NETWORK_PROTOCOL_SATMON": "Satnet Monitoring",
    "NETWORK_PROTOCOL_SCCSP": "Semaphore Comm. Sicherheit",
    "NETWORK_PROTOCOL_SCTP": "SCTP",
    "NETWORK_PROTOCOL_SDRP": "Source Demand Routing",
    "NETWORK_PROTOCOL_SEP": "Sequential Exchange",
    "NETWORK_PROTOCOL_SKIP": "ÜBERSPRINGEN",
    "NETWORK_PROTOCOL_SRPC": "Strite RPC protocol",
    "NETWORK_PROTOCOL_ST": "Stream Protokoll II",
    "NETWORK_PROTOCOL_SVMTP": "Secure VMTP",
    "NETWORK_PROTOCOL_SWIPE": "IP with encryption",
    "NETWORK_PROTOCOL_TCF": "TCF",
    "NETWORK_PROTOCOL_TCP": "TCP",
    "NETWORK_PROTOCOL_TLSP": "Transport Layer Security",
    "NETWORK_PROTOCOL_TP": "tp-4 w/ class negotiation",
    "NETWORK_PROTOCOL_TPC": "Third Party Connect",
    "NETWORK_PROTOCOL_TPXX": "TP++ Transport",
    "NETWORK_PROTOCOL_TRUNK1": "Trunk-1",
    "NETWORK_PROTOCOL_TRUNK2": "Trunk-2",
    "NETWORK_PROTOCOL_TTP": "TTP",
    "NETWORK_PROTOCOL_UDP": "UDP - user datagram protocol",
    "NETWORK_PROTOCOL_VINES": "Banyon VINES",
    "NETWORK_PROTOCOL_VISA": "VISA Protocol",
    "NETWORK_PROTOCOL_VMTP": "VMTP",
    "NETWORK_PROTOCOL_WBEXPAK": "WIDEBAND EXPAK",
    "NETWORK_PROTOCOL_WBMON": "WIDEBAND Monitoring",
    "NETWORK_PROTOCOL_WSN": "Wang Span Network",
    "NETWORK_PROTOCOL_XNET": "Cross Net Debugger",
    "NETWORK_PROTOCOL_XTP": "XTP",
    "NETWORK_SERVICE_GROUP": "Netzwerkdienstgruppe",
    "NETWORK_SERVICE_GROUPS": "Netzwerkdienstgruppe",
    "NETWORK_SERVICE": "Netzwerk Dienst",
    "NETWORK_SERVICES_GROUP": "Netzwerkdienstegruppe",
    "NETWORK_SERVICES": "Netzwerk Dienst",
    "NETWORK_TRAFFIC": "Netzwerkverkehr",
    "NEW_API_KEY": "Neuer API-Schlüssel",
    "NEW_CALEDONIA_PACIFIC_NOUMEA": "Pazifik/Noumea",
    "NEW_CALEDONIA": "Neukaledonien",
    "NEW_PASSWORD_EQUALITY": "Das neue Passwort darf nicht gleich dem aktuellen Passwort sein",
    "NEW_PASSWORD_PLACEHOLDER": "Es muss mindestens 8 Zeichen lang sein und mindestens 1 Ziffer, 1 Großbuchstaben und 1 Sonderzeichen enthalten",
    "NEW_PASSWORD": "Neues Passwort",
    "NEW_ZEALAND_PACIFIC_AUCKLAND": "Pazifik/Auckland",
    "NEW_ZEALAND_PACIFIC_CHATHAM": "Pazifik/Chatham",
    "NEW_ZEALAND": "Neuseeland",
    "NEW": "neu",
    "NEWLY_REG_DOMAINS": "Neu registrierte Domains",
    "NEWS_AND_MEDIA": "Nachrichten und Medien",
    "NEXT_PERIODIC_UPDATE": "Das nächste regelmäßige Update erfolgt gemäß dem für VMs festgelegten Zeitplan in",
    "NEXT_UPDATE": "Das nächste regelmäßige Softwareupdate erfolgt am ",
    "NEXT": "Weiter",
    "NFL_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host nfl.com",
    "NFL": "NFL",
    "NFLMOBILE": "NFL Mobil",
    "NFS_DESC": " Das NFS-Protokoll bietet einen transparenten Zugriff auf gemeinsam genutzte Dateisysteme in Netzwerken, wie in RFC 1813 beschrieben.",
    "NFS": "NFS",
    "NGAP_SCTP_DESC": "Das NG Application Protocol (NGAP) stellt die Steuerungsebenensignalisierung zwischen dem NG-RAN-Knoten und der Access and Mobility Management Function (AMF) bereit und bietet NAS-Signalisierung für Benutzergeräte (UE) und AMF",
    "NGAP_SCTP": "NGAP-SCTP",
    "NGAP_UDP_DESC": "Das NG Application Protocol (NGAP) ist als UDP eingekapselt, um den Transport über Netzwerke zu ermöglichen, die SCTP nicht unterstützen",
    "NGAP_UDP": "NGAP-UDP",
    "NICARAGUA_AMERICA_MANAGUA": "Amerika/Managua",
    "NICARAGUA": "Nicaragua",
    "NIGER_AFRICA_NIAMEY": "Afrika / Niamey",
    "NIGER": "Niger",
    "NIGERIA_AFRICA_LAGOS": "Afrika / Lagos",
    "NIGERIA": "Nigeria",
    "NIUE_PACIFIC_NIUE": "Pazifik/Niue",
    "NIUE": "Niue",
    "NLOCKMGR_DESC": " Der Network Lock-Manager arbeitet mit dem Network File System (NFS) zusammen, um ein System V Style der Advisory-Datein und Record-Locking über das Netzwerk bereitzustellen.",
    "NLOCKMGR": "nlockmgr",
    "NLSP_DESC": " NetWare Link Services Protocol (NLSP) bietet state-Link-Routing für Internetwork Packet Exchange Netzwerke",
    "NLSP": "NLSP",
    "NMAP_DESC": "Nmap ist ein Werkzeug zum Scannen und Auswerten von Hosts in einem Computernetzwerk und fällt somit in die Kategorie der Portscanner. Der Name steht für Network Mapper.",
    "NMAP": "Nmap",
    "NNTP_DESC": " Das Network News Transport Protocol (NNTP) ist für die Verbreitung, Anfrage,den Abruf und das Posten von Netznachrichten zuständig und basiert auf einem zuverlässigen Stream-basierten Mechanismus.",
    "NNTP": "NNTP",
    "NNTPS_DESC": " Sichere Version des NNTP-Protokolls",
    "NNTPS": "SecureNNTP",
    "NO_ACTIVATION_PENDING": "Keine Aktivierung ausstehend",
    "NO_BC_GROUPS_AVAILABLE_FOR_SELECTED_LOCATION": "Branch Connector-Gruppen sind für den ausgewählten Standort nicht verfügbar!",
    "NO_DATA_AVAILABLE_AWS_ACCOUNT_GROUP": "Keine Daten verfügbar\n\nUm eine AWS-Kontogruppe zu erstellen, \n\ngehen Sie zu\n\nAdmin > Partnerintegration\n\n\n",
    "NO_DATA": "Keine übereinstimmenden Elemente gefunden",
    "NO_DESCRIPTION": "Keine Beschreibung",
    "NO_GROUPING": "Verkehr gesamt",
    "NO_ITEMS_AVAILABLE": "Keine Daten gefunden",
    "NO_MATCHING_ITEMS_FOUND": "Keine übereinstimmenden Elemente gefunden",
    "NO_MORE_DHCP_OPTIONS_AVAILABLE": "Keine weiteren DHCP-Optionen verfügbar",
    "NO_OF_CLOUD_CONNECTOR_GROUPS": "Anzahl der Cloud Connector-Gruppen",
    "NO_OF_CLOUD_CONNECTORS": "Anzahl der Cloud Connectors",
    "NO_OF_DUPLICATES_IP": "Anzahl doppelter IP-Adressen",
    "NO_OF_EDGE_CONNECTOR_GROUPS": "Anzahl Cloud Connector-Gruppen",
    "NO_OF_PRIVATE_IP_ADDRESSES": "Anzahl privater IP-Adressen",
    "NO_PENDING_UPGRADES": "Keine ausstehenden Upgrades",
    "NO_PRESIGNED_URL_WAS_GENERATED": "Es wurde keine vorab signierte URL generiert.",
    "NO_REGION_WAS_PREVIOUS_SELECTED_TEXT": "Im Ereignisraster wurde keine Region ausgewählt, die für das Speicherkonto verwendet werden soll.",
    "NO_REGION_WAS_PREVIOUS_SELECTED": "Keine Region ausgewählt",
    "NO_STATIC_LEASE_CONFIGURED": "Kein statisches Lease konfiguriert",
    "NO_SUBSCRIPTION_AVAILABLE": "Kein Abonnement verfügbar.",
    "NO_VALUE_SELECTED": "Kein Wert ausgewählt",
    "NO_WIDGET_DATA": "Keine Daten für den ausgewählten Zeitraum",
    "NO": "Nr.",
    "NON_CATEGORIZABLE": "Nicht kategorisierbar",
    "NON_NUMERIC_VALUE": "Dieses Feld darf nur Zahlen enthalten.",
    "NONE": "Keine",
    "NORFOLK_ISLAND_PACIFIC_NORFOLK": "Pazifik/Norfolk",
    "NORFOLK_ISLAND": "Norfolkinsel",
    "NORTH_KOREA": "Nord-Korea",
    "NORTH_MACEDONIA": "Nordmazedonien",
    "NORTHAMERICA_NORTHEAST1_A": "northamerica-northeast1a",
    "NORTHAMERICA_NORTHEAST1_B": "northamerica-northeast1b",
    "NORTHAMERICA_NORTHEAST1_C": "northamerica-northeast1c",
    "NORTHAMERICA_NORTHEAST1": "northamerica-northeast1",
    "NORTHAMERICA_NORTHEAST2_A": "northamerica-northeast2a",
    "NORTHAMERICA_NORTHEAST2_B": "northamerica-northeast2b",
    "NORTHAMERICA_NORTHEAST2_C": "northamerica-northeast2c",
    "NORTHAMERICA_NORTHEAST2": "northamerica-northeast2",
    "NORTHCENTRALUS": "(USA) USA Mitte Nord",
    "NORTHCENTRALUSSTAGE": "(USA) USA Mitte Nord (Stufe)",
    "NORTHERN_EUROPE": "Nordeuropa",
    "NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN": "Pazifik/Saipan",
    "NORTHERN_MARIANA_ISLANDS": "Northern Mariana Islands",
    "NORTHEUROPE": "(Europa) Nordeuropa",
    "NORWAY_EUROPE_OSLO": "Europa / Oslo",
    "NORWAY": "Norwegen",
    "NORWAYEAST": "(Europa) Nordeuropa",
    "NORWAYWEST": "(Europa) Norwegen West",
    "NOT_AVAILABLE": "Nicht verfügbar",
    "NOT_DEPLOYED": "Bereit zur Implementierung",
    "NOT_NULL": "Nicht null",
    "NOT_SPECIFIED": "nicht spezifiziert",
    "NSS_CLOUD_FEED_API_URL": "API-URL",
    "NSS_CLOUD_FEED_AUTHENTICATION_URL": "Autorisierungs-URL",
    "NSS_CLOUD_FEED_AWS_ACCESS_ID": "AWS-Zugriffs-ID",
    "NSS_CLOUD_FEED_AWS_SECRET_KEY": "AWS-Geheimschlüssel",
    "NSS_CLOUD_FEED_CLIENT_ID": "Client-ID",
    "NSS_CLOUD_FEED_CLIENT_SECRET": "Client-Geheimschlüssel",
    "NSS_CLOUD_FEED_GENERAL": "Allgemein",
    "NSS_CLOUD_FEED_GRANT_TYPE": "Gewährungsart",
    "NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "JSON-Array-Notation",
    "NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Max. Batchgröße",
    "NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "OAuth 2.0-Authentifizierung",
    "NSS_CLOUD_FEED_SCOPE": "Bereich",
    "NSS_CLOUD_FEED_SIEM_TYPE": "Siem-Typ",
    "NSS_CLOUD_FEEDS_API_URL": "API-URL",
    "NSS_CLOUD_FEEDS_FEED_NAME": "Feed-Name",
    "NSS_CLOUD_FEEDS_FEED_OVERVIEW": "Feed – Überblick",
    "NSS_CLOUD_FEEDS_FEED_TYPE": "FEED-TYP",
    "NSS_CLOUD_FEEDS_LOG_TYPE": "LOG-TYP",
    "NSS_CLOUD_FEEDS_S3_FOLDER_URL": "S3-Ordner-URL",
    "NSS_CLOUD_FEEDS_SIEM_TYPE": "Siem-Typ",
    "NSS_CLOUD_FEEDS_STATUS": "Status",
    "NSS_CLOUD_FEEDS": "Cloud-NSS-Feeds",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connector-Gruppen",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Branch & Cloud Connector",
    "NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Client-IP-Adressen",
    "NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "DNS-Anfragetypen",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "DNS-Antwortcodes",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "DNS-Antworttypen",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "DNS-Antworten",
    "NSS_FEED_DNS_FILTERS_DOMAINS": "Domains",
    "NSS_FEED_DNS_FILTERS_DURATIONS": "Dauern",
    "NSS_FEED_DNS_FILTERS_LOCATIONS": "Standorte",
    "NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Richtlinienaktion",
    "NSS_FEED_DNS_FILTERS_RULE_NAME": "Regelname",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_ADDRESS": "Server IP-Adressen",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_PORTS": "Server Ports",
    "NSS_FEED_DUPLICATE_LOGS": "Protokolle duplizieren",
    "NSS_FEED_EC_METRICS_RECORD_TYPE": "Metriken-Datensatztyp",
    "NSS_FEED_ESCAPE_CHARACTER": "Feed-Escapezeichen",
    "NSS_FEED_FILTERS": "Filter",
    "NSS_FEED_FORMATTING": "FORMATIERUNG",
    "NSS_FEED_GENERAL": "Allgemein",
    "NSS_FEED_LOG_TYPE": "LOG-TYP",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Cloud/Branch Connector-VM",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Cloud/Branch Connector-VM",
    "NSS_FEED_NAME": "Feed-Name",
    "NSS_FEED_OUTPUT_FORMAT": "Feed-Ausgabeformat",
    "NSS_FEED_OUTPUT_TYPE": "FEED-AUSGABETYP",
    "NSS_FEED_SERVER": "NSS-Server",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connector-Gruppen",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Branch & Cloud Connector",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Client-IP-Adressen",
    "NSS_FEED_SESSION_FILTERS_FIREWALL_LOG_TYPE": "Firewall-Protokolltyp",
    "NSS_FEED_SESSION_FILTERS_GATEWAY": "Gateway",
    "NSS_FEED_SESSION_FILTERS_LOCATIONS": "Standorte",
    "NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Netzwerk Dienst",
    "NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Richtlinienaktion",
    "NSS_FEED_SESSION_FILTERS_RULE_NAME": "Regelname",
    "NSS_FEED_SESSION_LOG_TYPE": "Art des Sitzungsprotokolls",
    "NSS_FEED_SIEM_CONNECTIVITY": "SIEM-Konnektivität",
    "NSS_FEED_SIEM_DESTINATION_TYPE": "SIEM-Zieltyp",
    "NSS_FEED_SIEM_FQDN": "SIEM-FQDN",
    "NSS_FEED_SIEM_IP_ADDRESS": "SIEM-IP-Adresse",
    "NSS_FEED_SIEM_RATE_LIMIT": "SIEM-Ratenlimit (Ereignisse pro Sekunde)",
    "NSS_FEED_SIEM_RATE": "SIEM-Rate",
    "NSS_FEED_SIEM_TCP_PORT": "SIEM-TCP-Port",
    "NSS_FEED_STATUS": "Status",
    "NSS_FEED_TIMEZONE": "Zeitzone",
    "NSS_FEED": "NSS-FEED",
    "NSS_FEEDS_AGGREGATE_LOGS": "Protokolle aggregieren",
    "NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS": "Sitzungs- und Aggregatprotokolle",
    "NSS_FEEDS_DUPLICATE_LOG": "Protokolle duplizieren",
    "NSS_FEEDS_FEED_ATTRIBUTES": "Feed-Attribute",
    "NSS_FEEDS_FEED_NAME": "Feed-Name",
    "NSS_FEEDS_FEED_OUTPUT_FORMAT": "Feed-Ausgabeformat",
    "NSS_FEEDS_FEED_OVERVIEW": "Feed – Überblick",
    "NSS_FEEDS_FEED_TYPE": "FEED-TYP",
    "NSS_FEEDS_FULL_SESSION_LOGS": "Vollständige Sitzungsprotokolle",
    "NSS_FEEDS_LOG_FILTER": "Log-Filter",
    "NSS_FEEDS_LOG_TYPE": "LOG-TYP",
    "NSS_FEEDS_NSS_SERVER_TEXT": "NSS-Server",
    "NSS_FEEDS_OUTPUT_DESTINATION": "AUSGABEZIEL",
    "NSS_FEEDS_SIEM_RATE": "SIEM-Rate",
    "NSS_FEEDS_STATUS": "Status",
    "NSS_FEEDS_TIMEZONE": "Zeitzone",
    "NSS_FEEDS_USER_OBFUSCATION": "Benutzerverschleierung",
    "NSS_FEEDS": "NSS-Feeds",
    "NSS_FOR_FIREWALL_AND_EC": "NSS für Firewall, Cloud & Branch Connector",
    "NSS_FOR_FIREWALL": "NSS für Firewall",
    "NSS_GCP_DEPLOYMENT_GUIDE": "Google Cloud Platform-Bereitstellungsanleitung für NSS",
    "NSS_LOGGING": "NSS-Protokollierung",
    "NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Anzahl Benutzer",
    "NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Spitzenwert für DNS-Anfragen pro Stunde",
    "NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR": "Spitzenwert für Sitzungen pro Stunde",
    "NSS_SERVER_DEPLOYMENT_PLATFORM": "Plattform",
    "NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE": "Virtuelle NSS-Appliance herunterladen",
    "NSS_SERVER": "NSS-Server",
    "NSS_SERVERS": "NSS-Server",
    "NSS_TYPE": "NSS-Typ",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC": "Bereitstellung von virtueller NSS-Appliance",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT": "BEREITSTELLUNG VON VIRTUELLER NSS-APPLIANCE",
    "NSS_VIRTUAL_MACHINE": "NSS Virtuelle Maschine",
    "NTP_DESC": "NTP (Network Time Protocol) ist ein Standard zur Synchronisierung von Uhren in Computersystemen über paketbasierte Kommunikationsnetze. NTP verwendet das verbindungslose Transportprotokoll UDP. NTP wurde speziell entwickelt, um eine zuverlässige Zeitangabe über Netzwerke mit variabler Paketlaufzeit zu ermöglichen.",
    "NTP": "NTP",
    "NTV_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host ntv.co.jp",
    "NTV": "NTV",
    "NUDITY_DESC": " Seiten, die Nacktheit künstlerisch oder nicht-künstlerisch zur Schau stellen in unterschiedlichsten Formen (Skulptur, Photographie, Malerei, etc.).",
    "NUDITY": "Nacktheit",
    "NUMBER_ABBR": "Anz.",
    "NUMBER_OF_ACCOUNTS": "Anzahl der Konten",
    "NUMBER_OF_CONNECTORS": "Anz. Connectors",
    "NUMBER_OF_CORES": "Anzahl Kerne",
    "NUMBER_OF_RECORDS_DISPLAYED": "Anzahl angezeigter Datensätze",
    "NUMBER_OF_RECORDS_FETCHED_SO_FAR": "Anzahl bisher abgerufener Datensätze:",
    "NUMBER_OF_SELECTED_ITEM_PLURAL": "{{count}} Elemente ausgewählt",
    "NUMBER_OF_SELECTED_ITEM": "{{count}} Element ausgewählt",
    "NUMBER_OF_SELECTED_ITEMS": "Anz. ausgewählter Elemente",
    "OBFUSCATED": "Ausblenden",
    "OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA": "Asien/Gaza",
    "OCCUPIED_PALESTINIAN_TERRITORY": "Palästinensische Autonomiegebiete",
    "OFF": "Aus",
    "OKAY": "OK",
    "OMAN_ASIA_MUSCAT": "Asien/Muscat",
    "OMAN": "Oman",
    "ON_PREMISE": "Standortbasiert",
    "ON": "Ein",
    "ONE_OR_MORE_CC_FAILED": "Ein oder mehrere Cloud Connector-Upgrades sind fehlgeschlagen",
    "ONLINE_AUCTIONS": "Online-Auktionen",
    "ONLINE_CHAT": "Online-Chat",
    "ONLY_ONE_PHISICAL_INTERFACE_PER_PORT": "Nur eine getaggte Schnittstelle pro Port.",
    "OPEN_A_NEW_TAB": "In neuer Registerkarte öffnen",
    "OPENVPN_DESC": "OpenVPN ist ein Programm zum Aufbau eines Virtuellen Privaten Netzwerkes (VPN) über eine verschlüsselte TLS-Verbindung. Zur Verschlüsselung werden die Bibliotheken des Programmes OpenSSL benutzt. OpenVPN verwendet wahlweise UDP oder TCP zum Transport.",
    "OPENVPN": "OpenVPN",
    "OPERATIONAL_STATUS": "Betriebsstatus",
    "OPTION_NAME": "Name der Option",
    "OPTIONAL_PARENTHESIS": "(optional)",
    "OPTIONAL": "Optional",
    "OPTIONS_COLON": "Optionen:",
    "OPTIONS": "Optionen",
    "ORACLE_LINUX": "ORACLE LINUX",
    "ORDER_DEFAULT": "Standard",
    "ORG_ADMIN": "Organisatons-Administrator",
    "ORG_ID": "Org-ID",
    "ORGANIZATION": "Organisation",
    "OS_TYPE": "Betriebssystemtyp",
    "OS_VERSION": "Version des Betriebssystems",
    "OSS_UPDATES": "Betriebssystem- und Software-Updates",
    "OTHER_ADULT_MATERIAL": "Sonstiges - Erwachseneninhalte",
    "OTHER_BUSINESS_AND_ECONOMY": "Sonstiges - Wirtschaft und Gewerbe",
    "OTHER_CLOUDS": "Andere Clouds",
    "OTHER_DRUGS": "Andere Drogen",
    "OTHER_EDUCATION": "Sonstiges - Bildung",
    "OTHER_ENTERTAINMENT_AND_RECREATION": "andere Seiten für Unterhaltung und Freizeit",
    "OTHER_GAMES": "Online- und andere Spiele",
    "OTHER_GOVERNMENT_AND_POLITICS": "",
    "OTHER_ILLEGAL_OR_QUESTIONABLE": "Sonstiges - Illegal und Fragwürdiges",
    "OTHER_INFORMATION_TECHNOLOGY": "",
    "OTHER_INTERNET_COMMUNICATION": "Sonstiges - Internet-Kommunikation",
    "OTHER_MISCELLANEOUS": "Sonstiges - Verschiedenes",
    "OTHER_OS": "Anderes Betriebssystem",
    "OTHER_RELIGION": "Sonstiges - Religion",
    "OTHER_SECURITY": "Sonstiges - Sicherheit",
    "OTHER_SHOPPING_AND_AUCTIONS": "Sonstiges - Shopping und Auktionen",
    "OTHER_SOCIAL_AND_FAMILY_ISSUES": "Sonstiges - soziale und familiäre Themen",
    "OTHER_SOCIETY_AND_LIFESTYLE": "Sonstiges - Gesellschaft und Lifestyle",
    "OTHER_THREAT": "Sonstiges - Bedrohung",
    "OTHER": "Sonstiges",
    "out of": "von",
    "OUT_OF": "von",
    "OUTBOUND": "Abgehend",
    "OUTBYTES": "Abgehende Bytes",
    "OUTGOING_GATEWAY_IP_ADDRESS": "Abgehende Gateway-IP-Adresse",
    "OVER_ALL_TRAFFIC": "Verkehr gesamt",
    "OVERRIDE": "Überschreiben",
    "P2P_COMMUNICATION": "Peer-to-Peer-Site",
    "P2P": "Peer-to-Peer-",
    "PACKET_LOSS": "Paketverlust",
    "PAGE_OF": "Seite {1} von {2}",
    "PAGE_RISK_INDEX": "Verdächtiger Inhalt",
    "PAGE": "Seite",
    "PAKISTAN_ASIA_KARACHI": "Asien/Karachi",
    "PAKISTAN": "Pakistan",
    "PALAU_PACIFIC_PALAU": "Pazifik/Palau",
    "PALAU": "Palau",
    "PALESTINE": "Palästina",
    "PALESTINIAN_TERRITORY": "Palästinensische Autonomiegebiete",
    "PANAMA_AMERICA_PANAMA": "Amerika/Panama",
    "PANAMA": "Panama",
    "PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY": "Pazifik/Port Moresby",
    "PAPUA_NEW_GUINEA": "Papua-Neuguinea",
    "PARAGUAY_AMERICA_ASUNCION": "Amerika/Asuncion",
    "PARAGUAY": "Paraguay",
    "PARTNER_INTEGRATIONS": "Partnerintegrationen",
    "PASSPHRASE": "Passphrase",
    "PASSWORD_CHANGE_REMINDER_NEW_PASSWORD_INPUT_PLACEHOLDER": "Muss mindestens 8 Zeichen lang sein und mindestens 1 Ziffer, 1 Großbuchstaben und 1 Sonderzeichen enthalten...",
    "PASSWORD_DONT_MATCH": "Passwörter stimmen nicht überein",
    "PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE": "Diese Änderung gilt auch für Administratoren, die im Admin-Portal für einen anderen Dienst existieren. Fortfahren?",
    "PASSWORD_EXPIRATION_DAYS_RANGE": "Das Passwortablauf muss zwischen 15 und 365 Tagen liegen",
    "PASSWORD_EXPIRATION": "Passwortablauf",
    "PASSWORD_EXPIRED_ALREADY": "Ihr Passwort ist bereits abgelaufen",
    "PASSWORD_EXPIRED": "Passwort abgelaufen",
    "PASSWORD_EXPIRES_AFTER": "Passwort läuft ab nach",
    "PASSWORD_EXPIRY": "Passwortablauf",
    "PASSWORD_MANAGEMENT": "PASSWORTVERWALTUNG",
    "PASSWORD_MESSAGE_WARNING_MESSAGE": "Diese Änderung gilt auch für Administratoren, die im Admin-Portal für einen anderen Dienst existieren. Fortfahren?",
    "PASSWORD_STRENGTH_REQUIRED": "Das Passwort muss mindestens acht Zeichen lang sein und mindestens eine Ziffer, einen Großbuchstaben und ein Sonderzeichen enthalten.",
    "PASSWORD_STRENGTH": "Es muss mindestens 8 Zeichen lang sein und mindestens 1 Ziffer, 1 Großbuchstaben und 1 Sonderzeichen enthalten",
    "PASSWORD_UPDATE_SUCCESSFULLY": "Passwort erfolgreich aktualisiert",
    "PASSWORD": "Passwort",
    "PATCH": "Patch",
    "PATENTS": "Patente",
    "PC_ANYWHERE_DESC": "PC-Anywhere ist eine Remote Control and Dateitransfer Software",
    "PC_ANYWHERE": "pcAnywhere",
    "PCANYWHERE_DESC": " pcAnywhere ist eine Fernsteuerungslösung. Sie kann sowohl Windows- als auch Linux-Systeme verwalten. Verbesserte Videoleistung und integrierte 256-Bit-AES-Verschlüsselung sorgen für schnelle und sichere Kommunikation. pcAnywhere bietet auch leistungsstarke Dateiübertragungsfunktionen.",
    "PCANYWHERE": "pcAnywhere",
    "PEER_DHCP": "Peer-DHCP (optional)",
    "PENDING": "Ausstehend",
    "PERMISSION_REQUIRED_MESSAGE": "Für diese Funktion ist eine Berechtigung erforderlich, die Sie gegenwärtig nicht haben.",
    "PERMISSION_REQUIRED": "Berechtigung erforderlich",
    "PERMISSION": "Berechtigung",
    "PERMISSIONS": "Berechtigungen",
    "PERSIST_LOCAL_VERSION_PROFILE": "Lokales Versionsprofil dauerhaft machen",
    "PERU_AMERICA_LIMA": "Amerika/Lima",
    "PERU": "Peru",
    "PFCP_DESC": "Ein 3GPP-Protokoll, das auf der N4-Schnittstelle zwischen der Steuerebene und der Benutzerebenenfunktion (UPF) verwendet wird",
    "PFCP_PORT": "PFCP-Port",
    "PHILIPPINES_ASIA_MANILA": "Asien/Manila",
    "PHILIPPINES": "Philippinen",
    "PHISHING": "Phishing",
    "PHYSICAL": "Physisch",
    "PITCAIRN_ISLANDS": "Pitcairn Inseln",
    "PITCAIRN_PACIFIC_PITCAIRN": "Pazifik/Pitcairn",
    "PITCAIRN": "Pitcairn",
    "PLACEHOLDER_NETWORK_SERVICE_GROUP_NAME": "Netzwerkdienstgruppennamen hier eingeben",
    "PLACEHOLDER_NETWORK_SERVICE_NAME": "Netzwerkdienstnamen hier eingeben",
    "PLAIN_UDP": "Unverschlüsseltes UDP",
    "PLATFORM": "Plattform",
    "PLEASE_ADD_BC_GROUP_INFO": "Geben Sie die folgenden Informationen für Ihre Branch Connector-Gruppe ein.",
    "PLEASE_ADD_CLOUD_CONNECTOR_NAME": "Fügen Sie den Cloud Connector-Filter hinzu.",
    "PLEASE_ADD_DATACENTER_FILTER": "Fügen Sie den Rechenzentrumsfilter hinzu.",
    "PLEASE_ADD_EC_DEVICE_APP_VERSION": "Fügen Sie den Filter „Geräteanwendungsversion“ hinzu.",
    "PLEASE_ADD_EC_DEVICE_HOSTNAME": "Fügen Sie den Filter „Gerätehostname“ hinzu.",
    "PLEASE_ADD_EC_DEVICE_ID": "Fügen Sie den Filter „Gerätename“ hinzu.",
    "PLEASE_CONFIGURE_THE_IP_ADDRESS": "Konfigurieren Sie die IP-Adresse.",
    "PLEASE_CONFIGURE_THE_MAC_ADDRESS": "Konfigurieren Sie die MAC-Adresse.",
    "PLEASE_CONFIGURE_THE_ROUTE_ADDRESS": "Konfigurieren Sie die Routenadresse.",
    "PLEASE_ENTER_BELOW_VALUES": "Geben Sie die folgenden Werte ein",
    "PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW": "Geben Sie die folgenden Informationen ein.",
    "PLEASE_ENTER_VALID_EMAIL_ADDRESS": "Geben Sie eine gültige E-Mail-Adresse ein.",
    "PLEASE_FILL_BRANCH_INFO": "Geben Sie die folgenden Informationen für Ihren einzelnen Branch Connector ein.",
    "PLEASE_FILL_DEVICE_INFO": "Wählen Sie ein Gerät für die Bereitstellung Ihrer Branch Connector-Konfiguration aus.",
    "PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM": "Bitte kontaktieren Sie Ihr Kundenteam, um die Tunnelverschlüsselung zu aktivieren",
    "PLEASE_REMOVE_DELETED_LOCATION": "Bitte entfernen Sie die gelöschten Standorte.",
    "PLEASE_SELECT_A_STORAGE_ACCOUNT": "Wählen Sie ein Speicherkonto aus.",
    "PLEASE_VERIFY_THE_DHCP_CUSTOM_CONFIGURTION_FOR_DUPLICATES": "Überprüfen Sie die Konfiguration der benutzerdefinierten DHCP-Optionen auf Duplikate.",
    "POLAND_EUROPE_WARSAW": "Europa / Warschau",
    "POLAND": "Polen",
    "POLICY_CONFIGURATION": "Richtlinienkonfiguration",
    "POLICY_INFORMATION": "Weiterleitungsinformationen",
    "POLICY_MANAGEMENT": "Richtlinienverwaltung",
    "POLICY_SYNC": "Richtliniensynchronisierung",
    "Policy": "Policy",
    "POLICY": "Policy",
    "POLITICS": "Politik",
    "POOR": "Schlecht",
    "POP3_DESC": "POP3 (Post Office Protocol) ist ein Protokoll zum Empfang von Emails.",
    "POP3": "POP3",
    "PORNOGRAPHY": "Pornografie",
    "PORT_DETAILS": "Portdetails",
    "PORT_NAME": "[Portname]",
    "PORT_NO": "Portnummer  ",
    "PORT_STATUS": "Portstatus",
    "PORT": "Port",
    "PORTALS": "Portale",
    "PORTS": "Proxy Ports",
    "PORTUGAL_ATLANTIC_AZORES": "Atlanik/Azoren",
    "PORTUGAL_ATLANTIC_MADEIRA": "Atlantik/Madeira",
    "PORTUGAL_EUROPE_LISBON": "Europa / Lissabon",
    "PORTUGAL": "Portugal",
    "PPP_DESC": " PP (Point-to-Point Protocol) ist ein Link-Layer-Protokoll für die Übertragung von Daten entlang eines Point-to-Point Links. Es sorgt für eine dynamische IP-Adressierung, Passwort-Unterstützung, Fehlerprüfung und die Übertragung mehrerer Protokolle auf dem gleichen Link",
    "PPP": "PPP",
    "PPPOE_DESC": " PPP over Ethernet (PPPoE) bietet die Möglichkeit, ein Netzwerk von Hosts über eine einfache Bridge zu einem Remote Access Concentrator zu verbinden",
    "PPPOE": "PPPoE",
    "PPS_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host pps.tv",
    "PPS": "PPS (pps.tv)",
    "PPSTREAM_DESC": " Das PPStream Protokoll bietet Audio- und Video-Streaming. Es basiert auf Bittorrent (Peer-to-Peer)-Technologie. Es wird vor allem in China verwendet.",
    "PPSTREAM": "PPStream",
    "PPTP_DATA_DESC": " Point-to-Point Tunneling Protocol allows the Point to Point Protocol (PPP) to be tunnelled through an IP network",
    "PPTP_DATA": "PPTP data channel. PPTP",
    "PPTP_DESC": " Point-to-Point Tunneling Protocol allows the Point to Point Protocol (PPP) to be tunnelled through an IP network",
    "PPTP_SERVICES": "PPTP-Dienste",
    "PPTP": "PPTP",
    "PPTV_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host pptv.com",
    "PPTV": "pptv",
    "PRE_SIGNED_URL": "Vorab signierte URL",
    "PREDEFINED_RULE_CONFIRMATION": "Bestätigung der vordefinierten Regel",
    "PREDEFINED": "Vordefiniert",
    "PREFERRED_COLLON": "Bevorzugt:",
    "PREFERRED_TEXT": "Wählen Sie „Ja“, um dem bevorzugten Gerät Vorrang zu geben und zulassen, dass es die Kontrolle übernimmt, wenn es aktiv ist.",
    "PREFERRED": "Bevorzugt",
    "PREFIX": "Präfix",
    "PREV_DAY": "Voriger Tag",
    "PREV_MONTH": "Voriger Monat",
    "PREV_WEEK": "Vorige Woche",
    "PREVIOUS": "Zurück",
    "PRIMARY_DNS_IS_MADATORY_BEFORE_SECONDARY_DNS": "Primärer DNS muss angegeben werden, bevor ein sekundärer DNS angegeben werden kann.",
    "PRIMARY_DNS_SERVER_IP_ADDRESS": "IP-Adresse des primären DNS-Servers",
    "PRIMARY_DNS_SERVER": "Primärer DNS Server",
    "PRIMARY_DNS": "Primärer DNS",
    "PRIMARY_PROXY": "Primärer Proxy",
    "PRIMARY_SERVER_RESPONSE_PASS": "Primärer Server hat versucht",
    "PRIMARY": "Hauptkontakt",
    "PRINT_VIEW": "Ansicht drucken",
    "PRINT": "Drucken",
    "PRIVATE_APLICATIONS": "Private Anwendungen",
    "PRIVATE_IP_ADDRESS": "Private IP-Adresse",
    "PRIVATE_IP_ADDRESSES": "Private IP-Adressen",
    "PROCEED": "Fortfahren",
    "PROCESSED_BYTES": "Verarbeitete Bytes",
    "PROFANITY": "Obszönitäten",
    "PROFESSIONAL_SERVICES": "Professionelle Dienstleistungen",
    "PROTOCOL_TYPE": "Protokolltyp",
    "PROTOCOL": "Protokoll",
    "PROVISION_KEY_NAME": "Name des Bereitstellungsschlüssels",
    "PROVISION_KEY": "Bereitstellungschlüssel",
    "PROVISIONED": "Bereitgestellt",
    "Provisioning URL": "Bereitstellungs-URL",
    "PROVISIONING_AND_CONFIGUATION": "Bereitstellung und Konfiguration",
    "PROVISIONING_CONTROL": "Bereitstellungssteuerung",
    "PROVISIONING_KEY": "Bereitstellungschlüssel",
    "PROVISIONING_MANAGEMENT": "Bereitstellungsmanagement",
    "PROVISIONING_TEMPLATE_IS_BROKEN": "Diese Bereitstellungsvorlage ist ungültig. Bitte löschen Sie sie und erstellen Sie eine neue.",
    "PROVISIONING_TEMPLATE": "Bereitstellungsvorlage",
    "PROVISIONING_TEMPLATES": "Bereitstellungsvorlagen",
    "PROVISIONING_URL": "Bereitstellungs-URL",
    "PROXY_TEST": "Proxy-Test",
    "PUBLIC_CLOUD_COFIGURATION": "Öffentliche Cloud-Konfiguration",
    "PUBLIC_CLOUD_CONFIG_MANAGEMENT": "Konfigurationsverwaltung Öffentliche Cloud",
    "PUBLIC_IP_FOR_DIRECT_FORWARDING": "Öffentliche IP-Adresse für direkte Weiterleitung",
    "PUBLIC_IP": "Öffentliche IP-Adresse",
    "PUBLIC_IPS": "Öffentliche IP-Adressen",
    "PUERTO_RICO_AMERICA_PUERTO_RICO": "Amerika/Puerto Rico",
    "PUERTO_RICO": "Puerto Rico",
    "PZEN": "Private Service Edge",
    "QATAR_ASIA_QATAR": "Asien/Qatar",
    "QATAR": "Katar",
    "QUESTIONABLE": "Fraglich",
    "QUEUED_ACTIVATIONS": "AKTIVIERUNGEN IN DER WARTESCHLANGE",
    "QUIC_DESC": "QUIC ist ein neues Transport-Protokoll für das Internet, welches von Google entwickelt wurde",
    "QUIC": "QUIC",
    "QUICK_LINKS": "Quick Links",
    "QUICKOFFICE": "Quickoffice",
    "QUICKSEC": "QUICKSEC",
    "QUICKTIME_VIDEO": "QuickTime Video (mov, qt)",
    "QUICKTIME": "QuickTime",
    "RADIO_STATIONS": "Radio",
    "RADIUS_DESC": " RADIUS (Remote Authentication Dial-In User Service) ist ein Client / Server-Protokoll, das mit einem zentralen Server kommuniziert, um Dial-In User zu authentisieren und den Zugang zu dem angeforderten System oder Service RAS-Server zu ermöglichen.",
    "RADIUS": "RADIUS",
    "RADIUSIM_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host radiusim.com",
    "RADIUSIM": "RadiusIM",
    "RANGE_ERROR": "Ungültiger Bereich. Die Zahl muss zwischen {{min}} und {{max}} liegen",
    "RANGE_FROM_BIGGER_THAN_TO": "Der Bereich muss mit einer IP-Adresse beginnen, die höchstens so groß wie die End-IP-Adresse ist.",
    "RBA_LIMITED": "Zugriff beschränkt",
    "REAL_ESTATE": "Bau & Immobilien",
    "REAL_MEDIA_DESC": "Real Media ist eine Technologie zum Streamen von Videos und Audio",
    "REAL_MEDIA": "RealMedia",
    "REASON": "Grund",
    "RECEIVE_COUNT": "Anzahl empfangen",
    "RECEIVED_BYTES": "Bytes empfangen",
    "RECEIVED_MESSAGES": "Empfangene Nachrichten",
    "RECOMMENDED_HYPERVISOR_SPECS": "EMPFOHLENE HYPERVISOR-SPEZIFIKATION",
    "RECOMMENDED_VM_SPECS": "EMPFOHLENE VM-SPEZIFIKATION",
    "REDHAT_LINUX": "Red Hat Linux",
    "REDIR_ZPA": "An ZPA weiterleiten",
    "REDIRECT_REQUEST": "Anfrage umleiten",
    "REFERENCE_SITES": "Referenz-Websites",
    "REFRESH": "Aktualisieren",
    "REGENARATE": "Regenerieren",
    "REGENERATE_API_KEY_CONFIRMATION_MESSAGE": "Das Neugenerieren des API-Schlüssels macht ihn sofort ungültig. Der neue Schlüssel behält den Geltungsbereich, die Berechtigungen und den Namen des bestehenden Schlüssels. Dies kann nicht rückgängig gemacht werden.",
    "REGENERATE_API_KEY_CONFIRMATION_TITLE": "API-Schlüssel neu generieren",
    "REGENERATE_API_KEY_TOOLTIP": "Den API-Schlüssel neu generieren",
    "REGENERATE_API_KEY": "API-Schlüssel neu generieren",
    "REGION_TEXT": "Wählen Sie die Regionen aus, in denen Zscaler Tags in Ihrem AWS-Konto erkennen soll. In der Dropdownliste sehen Sie die Regionen, die von Zscalers Tag-Erkennungsdienst unterstützt werden. Nähere Informationen zu unterstützten Regionen finden Sie {1}hier{2}.",
    "REGION": "Region",
    "REGIONS_AND_SUBSCRIPTIONS": "Regionen und Abonnements",
    "REGIONS_SUBSCRIPTION_TEXT": "Wählen Sie die Regionen und Abonnements aus. Zscaler liest die benutzerdefinierten Tags für Workloads in diesen Regionen und Abonnements.",
    "REGIONS_SUBSCRIPTION": "Regionen und Abonnements",
    "REGIONS_WORKLOAD_INVENTORY": "Workload-Inventar der Regionen",
    "REGIONS": "Regionen",
    "REGISTERED": "Registriert",
    "RELEASES_NOTES": "Versionshinweise",
    "REMOTE_ACCESS": "Fernzugriff",
    "REMOTE_ASSISTANCE_MANAGEMENT": "Remotesupport-Verwaltung",
    "REMOTE_ASSISTANCE": "Remote-Unterstützung",
    "REMOVE_ALL": "Alle entfernen",
    "RENEW": "Verlängern",
    "REPORT": "Report",
    "REQ_ACTION": "Anfrageaktion",
    "REQ_DURATION": "Anfragedauer",
    "REQ_RULE_NAME": "Anfrage-Regelname",
    "REQUESTED_DOMAIN": "Angeforderte Domäne",
    "REQUIRED": "Dieses Feld darf nicht leer sein.",
    "RES_ACTION": "Antwortaktion",
    "RES_RULE_NAME": "Antwort-Regelname",
    "RESEARCH_BLOG": "ThreatLabz | Security Research",
    "RESET_COUNT": "Zähler zurücksetzen",
    "RESET": "Zurücksetzen",
    "RESOLVE_BY_ZPA": "Über ZPA lösen",
    "RESOLVED_BY_ZPA": "Gelöst",
    "RESOLVED_IP_OR_NAME": "Aufgelöste(r) IP/Name",
    "RESOLVED_IP": "Aufgelöste IP",
    "RESOLVER_IP_OR_NAME": "Auflöser-IP/Name",
    "RESOLVER": "Auflöser",
    "RESOURCE_GROUP": "Ressourcengruppe",
    "RESOURCE_GROUPS": "Ressourcengruppen",
    "RESOURCE_NAME": "Ressource",
    "RESOURCE_NOT_FOUND": "Ressource nicht gefunden",
    "RESOURCE_TYPE": "Ressourcentyp",
    "RESOURCE": "Ressource",
    "RESPONSE_ACTION": "Antwortaktion",
    "REST": "Rest",
    "RETRIEVING_FOR": "Abruf für",
    "RETURN_ERROR": "Rücklieferungsfehler",
    "REUNION_INDIAN_REUNION": "Indisch/Reunion",
    "REUNION": "Reunion",
    "REVIEW_ENSURE_INFORMATION": "Stellen Sie sicher, dass die nachfolgenden Informationen korrekt sind, bevor Sie diese Branch Connector-Bereitstellungsvorlage erstellen.",
    "REVIEW_TENANT": "Stellen Sie sicher, dass die nachfolgenden Informationen korrekt sind, bevor Sie dieses Konto hinzufügen.",
    "REVIEW_TEXT": "Stellen Sie sicher, dass die nachfolgenden Informationen korrekt sind, bevor Sie dieses Konto hinzufügen.",
    "REVIEW_YOUR_CHANGES": "Prüfen Sie Ihre Änderungen",
    "REVIEW": "Überprüfung",
    "RMA": "RMA angefordert",
    "ROLE_MANAGEMENT": "Rollen Management",
    "ROLE_NAME": "Rollenname",
    "ROLE": "Rolle",
    "ROMANIA_EUROPE_BUCHAREST": "Europa / Bukarest",
    "ROMANIA": "Rumänien",
    "ROUTE_HAS_DUPLICATE_VALUES": "Route hat doppelte Werte.",
    "ROUTE": "Route",
    "ROUTING": "Routing",
    "ROWS_PER_PAGE": "Zeilen pro Seite",
    "RSH_DESC": " Das RSH-Protokoll ermöglicht dem Anwender eine sichere Verbindung zu einem Remote-Host zu etablieren und eine Shell zu erhalten, mit der die Befehle an den Remote-Computer geschickt und ausgeführt werden",
    "RSH": "rsh",
    "RSLTS_READFAILED": "Fehler beim Lesen des Ergebnisvolumens.",
    "RSS_DESC": " RSS ist eine Familie von Web-Feed-Formaten, die dazu dient häufig aktualisierte Arbeiten in einem standardisierten Format zu veröffentlichen",
    "RSS": "rss",
    "RSTAT_DESC": " Das RStat Protokoll wird in der Sun NFS Familie zum Austausch von Statistiken über die Netzwerkaktivität verwendet",
    "RSTAT": "RStat",
    "RSVP_DESC": " RSVP ist ein Ressourcenreservierungs Setup-Protokoll, das für einen integrierten Internet Service entwickelt wurde. RSVP bietet die Empfänger-initiierte Einrichtung von Ressourcenreservierungen für Multicast- oder Unicast-Datenströme, mit guter Skalierbarkeit und Robustheit. Das RSVP-Protokoll wird von einem Host verwendet, um besondere Servicequalität vom Netzwerk zu erhalten für bestimmte Anwendungsdatenströme. RSVP wird auch von Routern verwendet, um Quality-of-Service (QoS)-Anfragen an alle Knoten entlang des Pfads zu schicken die der Schaffung und Aufrechterhaltung des Zustands für den angefragten Dienst dienen.",
    "RSVP": "RSVP",
    "RSYNC_DESC": "rsync ist ein Datei Synchronisierungs- und Transfer Programm für Unix Betriebssysteme, dass den Datentransfer minimiert und nur das Delta mit dem rsync Algorythmus verschickt.",
    "RSYNC": "Rsync",
    "RTCP_DESC": " Das Realtime -Transport Control Protokoll RTP erlaubt  das Monitoring der Datenlieferung in einer Art und Weise, die skalierbar ist bis hin zu großen Multicast-Netzwerken und bietet minimale Kontroll- und Indentifizierungsfunktionalitäten.",
    "RTCP": "RTCP",
    "RTL_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host rtl.de",
    "RTL": "RTL",
    "RTMP_DESC": "Real Time Messaging Protocol (RTMP) ist ein von Adobe Systems entwickeltes proprietäres Protokoll zum Streamen von Audio, Video und Daten über das Internet zwischen einem Flashplayer und einem Server.",
    "RTMP": "RTMP",
    "RTP_DESC": " RTP ist ein Echtzeit-Transport-Protokoll, um Daten wie beispielsweise Audio-, Video- oder Simulationsdaten über Multicast oder Unicast-Netzdienste in Echtzeit zu übertragen ",
    "RTP": "RTP",
    "RTSP_DESC": " RTSP (Real Time Streaming Protocol) ist ein Applikation-Level Protokoll, das die Auslieferung der Daten mit Echtzeit-Properties kontrolliert. RTSP stellt ein erweiterbares Framework zur Verfügung, um kontrollierte On-Demand-Delivery von Echtzeitdaten, wie Audio und Video zu ermöglichen.",
    "RTSP_SERVICES": "RTSP-Dienste",
    "RTSP": "RTSP",
    "RULE_CRITERIA": "Kriterien",
    "RULE_NAME": "Regelname",
    "RULE_ORDER": "Regelposition",
    "RULE_STATUS": "Regel Status",
    "RULES": "Regeln",
    "RUN_TEST": "Test ausführen",
    "RUSSIA": "Russland",
    "RUSSIAN_FEDERATION_ASIA_ANADYR": "Asien/Anadyr",
    "RUSSIAN_FEDERATION_ASIA_IRKUTSK": "Asien/Irkutsk",
    "RUSSIAN_FEDERATION_ASIA_KAMCHATKA": "Asien/Kamchatka",
    "RUSSIAN_FEDERATION_ASIA_KRASNOYARSK": "Asia/Krasnoyarsk",
    "RUSSIAN_FEDERATION_ASIA_MAGADAN": "Asien/Magadan",
    "RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK": "Asien/Novosibirsk",
    "RUSSIAN_FEDERATION_ASIA_OMSK": "Asien/Omsk",
    "RUSSIAN_FEDERATION_ASIA_SAKHALIN": "Asien/Sachalin",
    "RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK": "Asien/Wladiwostok",
    "RUSSIAN_FEDERATION_ASIA_YAKUTSK": "Asien/Jakutsk",
    "RUSSIAN_FEDERATION_ASIA_YEKATERINBURG": "Asien/Jekaterinburg",
    "RUSSIAN_FEDERATION_EUROPE_KALININGRAD": "Europa / Kaliningrad",
    "RUSSIAN_FEDERATION_EUROPE_MOSCOW": "Europa / Moskau",
    "RUSSIAN_FEDERATION_EUROPE_SAMARA": "Europa / Samara",
    "RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD": "Europa / Wolgograd",
    "RUSSIAN_FEDERATION": "Russische Förderation",
    "RWANDA_AFRICA_KIGALI": "Afrika / Kigali",
    "RWANDA": "Ruanda",
    "RX_BYTES": "Empfangene Bytes",
    "RX_PACKETS": "Empfangene Pakete",
    "SA_EAST_1": "sa-east-1 (Sao Paulo)",
    "SA_EAST_1A": "sa-east-1a",
    "SA_EAST_1B": "sa-east-1b",
    "SA_EAST_1C": "sa-east-1c",
    "SAFE_SEARCH_ENGINE": "Sichere Suchmaschine",
    "SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY": "Amerika/St. Barthelemy",
    "SAINT_BARTHELEMY": "St. Barthelemy",
    "SAINT_HELENA": "St. Helena",
    "SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS": "Amerika/St. Kitts",
    "SAINT_KITTS_AND_NEVIS": "St. Kitts und Nevis",
    "SAINT_LUCIA_AMERICA_ST_LUCIA": "Amerika/St. Lucia",
    "SAINT_LUCIA": "St. Lucia",
    "SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT": "Amerika/Marigot",
    "SAINT_MARTIN_FRENCH_PART": "St. Martin (franz. Teil)",
    "SAINT_MARTIN": "St. Martin",
    "SAINT_PIERRE_AND_MIQUELON": "St. Pierre und Miquelon",
    "SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT": "Amerika/St. Vincent",
    "SAINT_VINCENT_AND_THE_GRENADINES": "St. Vincent und die Grenadinen",
    "SAML_CERTIFICATE_FILENAME": "IdP SAML-Zertifikat",
    "SAMOA_PACIFIC_APIA": "Pazifik/Apia",
    "SAMOA": "Samoa",
    "SAN_MARINO_EUROPE_SAN_MARINO": "Europa / San Marino",
    "SAN_MARINO": "San Marino",
    "SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME": "Afrika / Sao Tome",
    "SAO_TOME_AND_PRINCIPE": "Sao Tome und Principe",
    "SATURDAY": "Samstag",
    "SAUDI_ARABIA_ASIA_RIYADH": "Asien/Riad",
    "SAUDI_ARABIA": "Saudi Arabien",
    "SAVE_AND_NEXT": "Speichern und fortfahren",
    "SAVE_ERROR_MESSAGE": "Ein Validierungsfehler ist aufgetreten. Bitte prüfen Sie alle Felder vor dem Speichern.",
    "SAVE_SUCCESS_MESSAGE": "Alle Änderungen wurden gespeichert.",
    "SAVE": "Speichern",
    "SCCP_DESC": " SCCP (Skinny Client Control Protocol) ist ein proprietäres Cisco-Protokoll, das zwischen Cisco Call Manager und Cisco VoIP-Telefonen verwendet wird. Es wird auch von einigen anderen Anbietern unterstützt.",
    "SCCP": "SCCP",
    "SCHEDULE_UPGRADE": "Upgrade planen",
    "SCHEDULED_VERSION": "Geplante Version",
    "SCHEDULED": "Geplant",
    "SCIENCE_AND_TECHNOLOGY": "Wissenschaft/Technologie",
    "SCOPE": "Bereich",
    "SCTP_ANY_DESC": "Das Stream Control Transmission Protocol (SCTP) ist ein Protokoll in der Transportschicht der Internet Protocol-Suite (IP). Es wurde ursprünglich für den Signalling System 7 (SS7)-Nachrichtentransport in der Telekommunikation konzipiert und bietet die nachrichtenorientierte Funktion des User Datagram Protocol (UDP). Gleichzeitig gewährleistet es den zuverlässigen, sequenziellen Transport von Nachrichten mit Überlastungskontrolle wie das Transmission Control Protocol (TCP).",
    "SCTP_ANY": "SCTP",
    "SCTP_DEST_PORTS": "SCTP-Zielports",
    "SCTP_PORT": "SCTP-Port",
    "SCTP_PORTS": "SCTP-Ports",
    "SCTP_SRC_PORTS": "SCTP-Quellports",
    "SCTP_UDP_Translation": "SCTP/UDP-Übersetzung",
    "SEARCH_BY": "Suchen nach:",
    "SEARCH_ELLIPSIS": "Suchen...",
    "SEARCH_LOCATION": "Standort suchen",
    "SEARCH_RESULT": "Suchergebnis",
    "SEARCH_TO_SEE_MORE": "Suchen, um weitere Elemente anzuzeigen",
    "SEARCH": "Suche",
    "SECONDARY_DNS_OPTIONAL": "Sekundärer DNS (optional)",
    "SECONDARY_DNS_SERVER_IP_ADDRESS": "IP-Adresse des sekundären DNS-Servers",
    "SECONDARY_DNS_SERVER": "Sekundärer DNS-Server",
    "SECONDARY_DNS": "Sekundärer DNS",
    "SECONDARY_PROXY": "Sekundärer Proxy",
    "SECONDARY_SERVER_RESPONSE_PASS": "Sekundärer Server versucht",
    "SECONDARY": "Alternativer Kontakt",
    "SECURITY_GROUP_ID": "Sicherheitsgruppen-ID",
    "SECURITY_GROUP_NAME": "Sicherheitsgruppenname",
    "SEGMENT_GROUPS": "Segmentgruppen",
    "SELECT_A_LOCATION": "Wählen Sie einen Standort",
    "SELECT_A_TEST": "Test auswählen",
    "SELECT_ALL": "Alle auswählen",
    "SELECT_AN_EXISTING_LOCATION": "Wählen Sie einen bestehenden Standort",
    "SELECT_BRANCH_PROVISIONING_LOCATION": "Wählen Sie einen Standort für Ihre Branch Connector-Bereitstellungsvorlage aus.",
    "SELECT_CC_GROUP": "Cloud Connector-Gruppe auswählen",
    "SELECT_CC_LOCATION": "Cloud Connector-Standort auswählen",
    "SELECT_CC_VERSION": "Cloud Connector-Version auswählen",
    "SELECT_CHART_TYPE": "Diagrammtyp auswählen",
    "SELECT_DESTINATION_IP": "Ziel-IP auswählen",
    "SELECT_EVENT_TIME": "Ereigniszeit auswählen",
    "SELECT_FILTERS": "Filter auswählen",
    "SELECT_HYPERVISOR_VERSION": "Hypervisor-Version auswählen",
    "SELECT_RESOURCE_GROUP_NAME": "Ressourcengruppe auswählen",
    "SELECT_STORAGE_ACCOUNT": "Speicherkonto auswählen",
    "SELECT_SUBSCRIPTION_GROUP_NAME": "Namen der Abonnementgruppe auswählen",
    "SELECT_SUBSCRIPTION": "Abonnement auswählen",
    "SELECT_TWO_VERSIONS_TO_COMPARE": "Wählen Sie zwei Versionen für den Vergleich aus.",
    "SELECT_UPGRADE_WINDOW": "Upgrade-Fenster auswählen",
    "SELECT_ZSCALER_IP": "Zscaler-IP auswählen",
    "SELECT": "Auswählen",
    "SELECTED_ITEMS": "Ausgewählte Elemente ({{count}})",
    "SELECTED": "Ausgewählt",
    "SENEGAL_AFRICA_DAKAR": "Afrika / Dakar",
    "SENEGAL": "Senegal",
    "SENT_BYTES": "Gesendete Bytes",
    "SENT_COUNT": "Anzahl gesendet",
    "SENT_MESSAGES": "Gesendete Nachrichten",
    "SERBIA_EUROPE_BELGRADE": "Europa / Belgrad",
    "SERBIA": "Serbien",
    "SERIAL_NUMBER": "Seriennummer",
    "SERVER_DESTINATION_IP": "Server-Ziel-IP",
    "SERVER_DESTINATION_PORT": "Server-Zielport",
    "SERVER_IP_CATEGORY": "Server-IP-Kategorie",
    "SERVER_IP": "Server-IP",
    "SERVER_NAME": "Server-Name",
    "SERVER_NETWORK_PROTOCOL": "Server-NW-Protokoll",
    "SERVER_PORT": "Serverport",
    "SERVER_SOURCE_IP": "Server-Quell-IP",
    "SERVER_SOURCE_PORT": "Server-Quellport",
    "SERVER": "Serverdatenverkehr",
    "SERVERS": "Server",
    "SERVFAIL": "Serverfehler",
    "Service IP": "Service IP",
    "SERVICE_GATEWAY_IP_ADDRESS": "Dienst-Gateway-IP-Adresse",
    "SERVICE_GROUPS": "Dienstgruppen",
    "SERVICE_INFORMATION": "Dienstinformationen",
    "SERVICE_INTERFACE": "Dienstschnittstelle",
    "SERVICE_IP_ADDRESS_ONE": "Dienst-IP-Adresse 1",
    "SERVICE_IP_ADDRESS_POOL": "Server-IP-Adresspool",
    "SERVICE_IP_ADDRESS_THREE": "Dienst-IP-Adresse 3",
    "SERVICE_IP_ADDRESS_TWO": "Dienst-IP-Adresse 2",
    "SERVICE_IP_ADDRESS": "Dienst-IP-Adresse",
    "SERVICE_IP": "Service IP",
    "SERVICE_STATUS": "Dienststatus",
    "SERVICE_VIRTUAL_IP_ADDRESS": "Virtuelle Dienst-IP-Adresse",
    "SERVICE": "Dienst",
    "SERVICES": "Dienste",
    "SESSION_COUNT_TREND": "Sitzungsanzahl-Trend",
    "SESSION_COUNT": "Sitzungsanzahl",
    "SESSION_DURATION": "Sitzungsdauer",
    "SESSION_ID": "Sitzungs-ID",
    "SESSION_INSIGHTS": "Sitzungseinblicke",
    "SESSION_LOGS": "Sitzungsprotokolle",
    "SESSION_TIMED_OUT": "Ihre Sitzung ist abgelaufen. Melden Sie sich wieder an, um fortzufahren.",
    "SESSION": "Sitzung",
    "SESSIONS_ACROSS_SERVICES": "Dienstübergreifende Sitzungen",
    "SESSIONS": "Sessions",
    "SET_PASSWORD": "PASSWORT EINRICHTEN",
    "SEXUALITY": "Sexualität",
    "SEYCHELLES_INDIAN_MAHE": "Indisch/Mahe",
    "SEYCHELLES": "Seychellen",
    "SHAREWARE_DOWNLOAD": "Shareware-Download",
    "SHOW_DETAILS": "Details anzeigen",
    "SHOW_LESS_ELLIPSIS": "Weniger anzeigen …",
    "SHOW_MORE_ELLIPSIS": "Mehr anzeigen …",
    "SHOW": "Anzeigen",
    "SHUTDOWN": "Herunterfahren",
    "SIERRA_LEONE_AFRICA_FREETOWN": "Afrika / Freetown",
    "SIERRA_LEONE": "Sierra Leone",
    "SIGN_IN": "Anmelden",
    "SIGN_OUT": "Abmelden",
    "SIGNING_CERTIFICATE": "Signierzertifikat",
    "SINGAPORE": "Singapur",
    "SINGLE_APPLIANCE_ADDED_INFO": "Sie können die neue Appliance auf der Seite 'Appliances' sehen. Weitere Informationen finden Sie im {1}Connector-Hilfeportal{2}.",
    "SINGLE_APPLIANCE_ADDED": "1 neue Appliance wurde zu Ihrem Mandanten hinzugefügt.",
    "SIP_DESC": " SIP (Session Initiation Protocol) ist der Internet Engineering Task Force (IETF)-Standard für Multimedia-Conferencing-over-IP. Wie bei anderen VoIP-Protokollen wurde SIP entwickelt, um die Funktionen des Signalings und Session-Managements in einem Paket-Telefonie-Netzwerk zu adressieren.",
    "SIP": "SIP",
    "SIZE_MUST_BE_EXACT_LENGTH": "Dieses Feld muss folgende Größe haben: ",
    "SLOVAKIA_EUROPE_BRATISLAVA": "Europa / Bratislava",
    "SLOVAKIA": "Slowakei",
    "SLOVENIA_EUROPE_LJUBLJANA": "Europa / Ljubljana",
    "SLOVENIA": "Slowakei",
    "SMALL": "Small",
    "SMB_DESC": " SMB / SMB2 (Server Message Block-Protokoll) stellt ein Verfahren für Client-Applikationen dar, um Dateien zu lesen und zu schreiben und Dienste von Server-Programmen in einem Computer-Netzwerk anzufordern.",
    "SMB": "SMB",
    "SMBA": "SMBA",
    "SMBAC": "Sandbox-Controller",
    "SMBAUI": "Sandbox-Benutzeroberfläche",
    "SMEDGE_BOOTING": "SMEDGE: Wird hochgefahren.",
    "SMEDGE_END": "Ende der SMEDGE-Fehlercodes. Nicht verwenden.",
    "SMEDGE_INIT": "SMEDGE: Wird initialisiert.",
    "SMEDGE_NOT_RUNNING": "SMEDGE: Prozess wird nicht ausgeführt.",
    "SMEDGE_PKG_DOWNLOAD": "SMEDGE: Paket-Download läuft.",
    "SMEDGE_PKG_INSTALL": "SMEDGE: Paketinstallation läuft.",
    "SMEDGE_START": "Anfang der SMEDGE-Fehlercodes. Nicht verwenden.",
    "SMEDGE_UNKNOWN_ERROR": "SMEDGE: Unbekannter Gateway-Fehlercode.",
    "SMEDGE_UPDATING": "SMEDGE: Wird aktualisiert.",
    "SMEDGE_UPGRADING": "SMEDGE: Upgrade läuft.",
    "SMEDGE_ZIA_BRINGUP": "SMEDGE: ZIA-Tunnel werden aufgerufen.",
    "SMEDGE_ZIA_ZPA_BRINGUP": "SMEDGE: ZIA-Tunnel werden aufgerufen und ZPA-Verbindung wird eingerichtet.",
    "SMRES_ERROR": "SMRES-Server-App hat eine HTTP-Fehlerantwort zurückgegeben.",
    "SMTP_AV_ENCRYPTED_ALLOW": "Verschlüsselter Dateianhang erlaubt",
    "SMTP_AV_ENCRYPTED_ATTACH_DROP": "Anhang mit Passwort verschlüsselt, Anhang verworfen",
    "SMTP_AV_ENCRYPTED_DROP": "Anhang mit Passwort verschlüsselt, Nachricht abgelehnt",
    "SMTP_AV_UNSCANNABLE_ALLOW": "unscannbarer Dateianhang erlaubt",
    "SMTP_AV_UNZIPPABLE_ATTACH_DROP": "Nicht scannbarer Anhang, Nachricht verworfen",
    "SMTP_AV_UNZIPPABLE_DROP": "Nicht scannbarer Anhang, Nachricht zurückgewiesen",
    "SMTP_AV_UWL": "Virus UWL",
    "SMTP_AV_VIRUS_ATTACH_DROP": "Virus gefunden, Dateianhang nicht angehängt",
    "SMTP_AV_VIRUS_DROP": "Virus gefunden, Nachricht blockiert",
    "SMTP_CMD_TIMEOUT_LIMIT": "SMTP/SMTQTN: Befehlszeitüberschreitung (in Sek.)",
    "SMTP_CONCURRENT_CLIENT_CONN_LIMIT": "SMTP/SMQTN: Limit für gleichzeitige Clientverbindungen",
    "SMTP_CONCURRENT_SERVER_CONN_LIMIT": "SMTP/SMQTN: Limit für gleichzeitige Serververbindungen",
    "SMTP_DATA_TIMEOUT_LIMIT": "SMTP/SMQTN: Zeitüberschreitung beim DATA-Befehl (in Sek.)",
    "SMTP_DESC": "SMTP (Simple Mail Transfer Protocol) ist ein Protokoll zum Versenden von Mails zwischen Servern.",
    "SMTP_DLP_ALLOW": "DLP hit, Nachricht erlaubt",
    "SMTP_DLP_DROP": "DLP-Hit, Meldung abgewiesen",
    "SMTP_DLP_QTN": "DLP-Hit, Nachricht unter Quarantäne gestellt",
    "SMTP_DLP_SIGN_REQUIRED": "DLP-Hit, Nachricht wird nicht signiert, Meldung abgewiesen",
    "SMTP_DLP_TLS_REQUIRED": "DLP-Hit, Verbindung ist nicht TLS, Meldung abgewiesen",
    "SMTP_DLP": "SMTP - Einhaltung",
    "SMTP_EODT_TIMEOUT_LIMIT": "SMTP/SMQTN: Zeitüberschreitung beim EODT-Befehl (in Sek.)",
    "SMTP_ERRINJECTION_DELAY_LIMIT": "GULPER: Verzögerung für Fehlereinschleusung (in Sek.)",
    "SMTP_FLOWCONTROL": "Flusssteuerung bei Nachrichtenzustellung – gilt nur für SMQTN",
    "SMTP_INCOMPL_TRANS": "SMTP Transaktion abgeschlossen durch Peer",
    "SMTP_INSPOL": "SMTP-Versicherungsrichtlinie",
    "SMTP_MAILPERCONN_LIMIT": "SMTP/SMQTN: Nachrichten pro Verbindungslimit",
    "SMTP_MAILSIZE_LIMIT": "SMTP/SMQTN: Limit für Nachrichtengröße",
    "SMTP_MF_ATTACHBLK_ATTACH_DROP": "Anhang wurde blockiert und nicht weitergeleitet",
    "SMTP_MF_ATTACHBLK_MSG_DROP": "Anhang wurde blockiert und nicht weitergeleitet",
    "SMTP_MF_RCPT_DROP": "Empfänger abgelehnt",
    "SMTP_MF_SIGN_REQUIRED": "Empfänger abgelehnt, die Nachricht ist nicht signiert",
    "SMTP_MF_TLS_REQUIRED": "Empfänger abgelehnt, die Verbindung ist kein TLS",
    "SMTP_MIN_MSBC_DENSITY": "SMTP/SMQTN: verdichten, wenn Dichte der in data_in empfangenen MSBS unter diesem Mindestprozentwert liegt",
    "SMTP_NOCA_BYPASS_CONFIG": "Richtlinie umgangen",
    "SMTP_NOCA_BYPASS": "SMTP/SMQTN: SMTP-Standardkonfiguration verwenden, wenn CA nicht verfügbar ist oder Konfigurationsantwort fehlerhaft ist",
    "SMTP_OUTBD_DROP_SUSPECTED_SPAM": "Abgehende Nachrichten verwerfen, die als Spam vermutet werden",
    "SMTP_PLATFORM_1": "SMTP - Plattform",
    "SMTP_PLATFORM_2": "SMTP - Plattform II(Outbound)",
    "SMTP_PRETRY": "Wiederholung vom Proxy – gilt nur auf SMTP-Knoten",
    "SMTP_PROXY": "SMTP-Proxy-Cluster",
    "SMTP_RCPT_COPY": "Empfänger ist eine Kopie",
    "SMTP_RCPT_REDIRECT": "Empfänger ist eine Weiterleitung,",
    "SMTP_RCPT_UNDELIVERABLE": "Empfänger abgelehnt, eine Zustellung ist nicht möglich",
    "SMTP_RCPTS_LIMIT": "SMTP/SMQTN: Empfänger pro SMTP-Transaktionslimit",
    "SMTP_RESERVED2": "SMTP: reserviert2",
    "SMTP_RESERVED3": "SMTP: reserviert3",
    "SMTP_RESERVED4": "SMTP: reserviert4",
    "SMTP_RESERVED5": "SMTP: reserviert5",
    "SMTP_REUSE_TIMEOUT_LIMIT": "SMTP/SMQTN: Zeitüberschreitung bei Pool für Wiederverwendung der Verbindung",
    "SMTP_SECURE": "SMTP - Sicher",
    "SMTP_SENDER_MASQ": "Sender maskiert",
    "SMTP_SMQTN": "SMTP-Quarantäne-Cluster",
    "SMTP_SPAM_DROP": "Spam erkannt, Nachricht abgewiesen",
    "SMTP_SPAM_IPWL": "Spam IPWL",
    "SMTP_SPAM_SUSPECT_ALLOW": "Spam vermuted, Nachricht erlaubt",
    "SMTP_SPAM_SUSPECT_DROP": "Spam vermuted, Nachricht abgewiesen",
    "SMTP_SPAM_SUSPECT_MARKSUBJ": "Spam vermuted, Betreff vorangestellt",
    "SMTP_SPAM_SUSPECT_QTN": "Spam vermuted, Nachricht in Quarantäne",
    "SMTP_SPAM_UBL_DROP": "Spam UBL erkannt, Nachricht abgewiesen",
    "SMTP_SPAM_UWL": "Spam UWL",
    "SMTP_SPAM": "SMTP - Anti-Spam Einstellungen",
    "SMTP_SPF_ENABLED": "SPF-Lookup von diesem Unternehmen ist aktiviert",
    "SMTP_TRANS_TIMEOUT_LIMIT": "SMTP/SMQTN: Transaktionszeitüberschreitung (in Sek.)",
    "SMTP_USERLIST_LIMIT": "SMTP-Limit-Wert für Abfrage der Benutzerliste",
    "SMTP": "SMTP",
    "SMTPEVT_VERBOSE": "Gekennzeichnete SMTP-Ereignisse ausführlich protokollieren",
    "SMTPTDL": "TDL – Transaktionsdatenlimit",
    "SMUI_PROCESS_TIMEOUT": "Zeitüberschreitungswert des SMUI-Prozesses",
    "SMUI": "SMUI",
    "SN_POSTING_CAUTIONED": "Es wird davor gewarnt, eine Nachricht auf dieser Website zu posten",
    "SN_POSTING_DENIED": "Es ist nicht gestattet, Nachrichten auf dieser Website zu posten",
    "SN_WEBUSE_CAUTIONED": "Es wird davor gewarnt, dieses soziale Netzwerk / diese Blogging-Site zu verwenden",
    "SN_WEBUSE_DENIED": "Es ist nicht gestattet, dieses soziale Netzwerk / diese Blogging-Site zu verwenden",
    "SNAGFILMS_DESC": " SnagFilms",
    "SNAGFILMS": "SnagFilms",
    "SNMP_DESC": " SNMP-Applikationen (auch bekannt als SNMP-Manager und SNMP-Agenten)",
    "SNMP": "SNMP",
    "SNMPTRAP_DESC": "SNMP Traps ermöglichen einem Agent, einen signifikanten Vorfall mittels einer unaufgeforderten Nachricht an eine Management-Station zu melden.",
    "SNMPTRAP": "SNMP-Falle",
    "SOA": "SOA",
    "SOAP_DESC": " SOAP ist ein einfaches Protokoll, für den Austausch von strukturierten Informationen in einer dezentralisierten, verteilten Umgebung. Es definiert mit Hilfe von XML-Technologien ein erweiterbares Messaging-Framework, das ein Message-Konstrukt enthält, das über eine Vielzahl von zugrunde liegenden Protokollen ausgetauscht werden kann.",
    "SOAP": "SOAP",
    "SOC1": "SOC1",
    "SOC2": "SOC2",
    "SOC3": "SOC3",
    "SOCIAL_ACTIVITY": "Aktivitäten in sozialen Netzwerken",
    "SOCIAL_ADULT_DESC": "Websites, die soziale Netzwerke für Erwachsene bereitstellen, wie z. B. Datingsites.",
    "SOCIAL_ADULT": "Soziales Netzwerk Erwachsene",
    "SOCIAL_ISSUES": "Soziale Fragen",
    "SOCIAL_NETWORKING_GAMES": "Social Networking-Spiele",
    "SOCIAL": "Social Networking",
    "SOCIALBAKERS": "SocialBakers",
    "SOCIALTV_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host srv.sixdegs.com",
    "SOCIALTV": "Sozial TV",
    "SOCIALVIBE_DESC": " Dieses Protokoll-Plugin klassifiziert den HTTP-Verkehr an den Host socialvibe.com.",
    "SOCIALVIBE": "SocialVibe",
    "SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP": "Der geplante Upgrade-Zeitpunkt basiert auf der Zeitzone Ihres Connectors. Upgrades müssen mindestens eine Stunde im Voraus geplant werden.",
    "SOFTWARE_UPGRADE_SCHEDULE": "Softwareupgrade-Zeitplan",
    "SOLOMON_ISLANDS_PACIFIC_GUADALCANAL": "Pacific/Guadalcanal",
    "SOLOMON_ISLANDS": "Solomon Inseln",
    "SOMALIA_AFRICA_MOGADISHU": "Afrika / Mogadischu",
    "SOMALIA": "Somalia",
    "SORRY_THIS_CODE_CAN_NOT_BE_USED": "Das kann leider nicht verwendet werden.",
    "SOURCE_IP_ADDRESSES": "Quell-IP-Adressen",
    "SOURCE_IP_GROUP": "Quell-IP-Gruppe",
    "SOURCE_IP_GROUPS": "Quell-IP-Gruppen",
    "SOURCE_IP": "Quell-IP",
    "SOURCE_PORT": "Quellport",
    "SOURCE": "Quelle",
    "SOUTH_AFRICA_AFRICA_JOHANNESBURG": "Afrika / Johannesburg",
    "SOUTH_AFRICA": "Südafrika",
    "SOUTH_AMERICA": "Südamerika",
    "SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS": "Südgeorgien und die Südlichen Sandwichinseln",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA": "Atlantik/South Georgia",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS": "Süd-Georgia und südliche Sandwich Inseln",
    "SOUTH_KOREA": "Süd-Korea",
    "SOUTHAFRICA": "Südafrika",
    "SOUTHAFRICANORTH": "(Afrika) Südafrika Nord",
    "SOUTHAFRICAWEST": "(Afrika) Südafrika Nord",
    "SOUTHAMERICA_EAST1_A": "southamerica-east1-a",
    "SOUTHAMERICA_EAST1_B": "southamerica-east1-b",
    "SOUTHAMERICA_EAST1_C": "southamerica-east1-c",
    "SOUTHAMERICA_EAST1": "southamerica-east1",
    "SOUTHAMERICA_WEST1_A": "southamerica-west1-a",
    "SOUTHAMERICA_WEST1_B": "southamerica-west1-b",
    "SOUTHAMERICA_WEST1_C": "southamerica-west1-c",
    "SOUTHAMERICA_WEST1": "southamerica-west1",
    "SOUTHCENTRALUS": "(USA) USA Mitte Süd",
    "SOUTHCENTRALUSSTAGE": "(USA) USA Mitte Süd (Stufe)",
    "SOUTHEASTASIA": "(Asien-Pazifikraum) Südostasien",
    "SOUTHEASTASIASTAGE": "(Asien-Pazifikraum) Südostasien (Stufe)",
    "SOUTHINDIA": "(Asien-Pazifikraum) Südindien",
    "SPAIN_AFRICA_CEUTA": "Afrika / Ceuta",
    "SPAIN_ATLANTIC_CANARY": "Atlantik/Kanarische Inseln",
    "SPAIN_EUROPE_MADRID": "Europa / Madrid",
    "SPAIN": "Spanien",
    "SPECIAL_INTERESTS": "Interessenvertretungen/soziale Organisationen",
    "SPECIALIZED_SHOPPING": "Online Shopping",
    "SPLIT_DEPLOY_CORE": "Geteiltes Deployment – Core",
    "SPLIT_DEPLOY_EDGE": "Geteiltes Deployment – Edge",
    "SPLUNK": "Splunk",
    "SPORTS": "Sport",
    "SPYWARE_OR_ADWARE": "Spyware oder Adware",
    "SRI_LANKA_ASIA_COLOMBO": "Asien/Colombo",
    "SRI_LANKA": "Sri Lanka",
    "SRV_RX_BYTES": "Vom Server empfangene Bytes",
    "SRV_TIMEOUT": "Zeitüberschreitung bei DNS-Transaktion, da der Server nicht geantwortet hat",
    "SRV_TX_BYTES": "Vom Server gesendete Bytes",
    "SRV_TX_DROPS": "Vom Server verworfene Bytes",
    "SSDP_DESC": " Das Simple Service Discovery Protocol (SSDP) bietet einen Mechanismus, mit dem Netzwerk-Clients gesuchte Netzwerkdienste erkennen können.",
    "SSDP": "SSDP",
    "SSH_DESC": " SSH (Secure Shell), auch bekannt als Secure Socket Shell, ist eine UNIX-basierte Befehlsschnittstelle und ein Protokoll für den sicheren Zugriff auf einen Remote-Computer.",
    "SSH": "SSH",
    "SSHFP": "SSHFP",
    "SSL_CERTIFICATE": "SSL-Zertifikat",
    "SSO_LOGOUT_MESSAGE": "Sie haben sich erfolgreich vom Cloud Connector Portal abgemeldet",
    "ST_HELENA_ATLANTIC_ST_HELENA": "Atlantik/St. Helena",
    "ST_HELENA": "St. Helena",
    "ST_KITTS_AND_NEVIS": "St. Kitts und Nevis",
    "ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON": "Amerika/Miquelon",
    "ST_PIERRE_AND_MIQUELON": "St. Pierre und Miquelon",
    "ST_VINCENT_AND_THE_GRENADINES": "St. Vincent und die Grenadinen",
    "STAGED": "Inszeniert",
    "STANDBY": "Standby",
    "START_OVER": "Von vorne beginnen",
    "START_TIME": "Startzeit",
    "STARTS_WITH": "Beginnt mit",
    "STAT": "Statisch",
    "STATE_PROVINCE": "Stadt/Bundesland/Provinz",
    "STATE": "Status",
    "STATIC_IP_ADDRESS": "Statische IP-Adresse",
    "STATIC_IP_ADDRESSES": "Statische IP-Adressen und GRE-Tunnel",
    "STATIC_IP_CONFLICT_WITH_SUBINTERFACE_IP": "IP der statischen Lease steht im Konflikt mit der IP der Sub-Schnittstelle.",
    "STATIC_IP_HAS_DUPLICATES_IPs": "Statisches Lease hat doppelte IP-Adressen",
    "STATIC_IP_HAS_DUPLICATES_MACS": "Statisches Lease hat doppelte MAC-Adressen",
    "STATIC_LEASE": "Statische Lease",
    "STATIC_LOCATION_GROUPS": "Manuelle Standortgruppen",
    "STATIC_MANAGEMENT_IP": "Statische Verwaltungs-IP",
    "STATIC_ROUTE_OPTIONAL": "Statische Route (optional)",
    "STATIC_ROUTE": "Statische Route",
    "STATIC_SERVICE_IP": "Statische Dienst-IP",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1": "Soll der Vorlagenstatus wirklich aktualisiert werden?",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED": "Aktualisiertes Element kann bearbeitbar/löschbar sein, nachdem es den Status „Inszeniert“ erreicht hat.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1": "Die Konfiguration wird auf das Gerät angewendet, wenn es online ist.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED": "Die Vorlage kann nicht mehr bearbeitet werden, sobald sie auf „Bereit zur Bereitstellung“ aktualisiert wurde.",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE": "Soll diese Ressource wirklich aktualisiert werden?",
    "STATUS_UPDATE_CONFIRMATION": "Bestätigung der Statusaktualisierung!",
    "STATUS": "Status",
    "STORAGE_ACCOUNT_TEXT": "Wählen Sie die Regionen, Abonnements und Speicherkontogruppen aus, in denen das Partnerthema und das Ziel erstellt werden.",
    "STORAGE_ACCOUNT": "Speicherkonto",
    "STREAMING_MEDIA": "Video-Streaming",
    "STRING": "Zeichenfolge",
    "SUB_CATEGORIES": "Unterkategorien",
    "SUB_CATEGORY": "Unterkategorie",
    "SUB_INTERFACE_SHUTDOWN": "Herunterfahren der Sub-Schnittstelle",
    "SUB_INTERFACE_VLAN": "VLAN der Sub-Schnittstelle",
    "SUB_INTERFACE": "Sub-Schnittstelle",
    "SUBCLOUDS": "Unter-Clouds",
    "SUBLOCATIONS": "Unterstandorte",
    "SUBMIT_A_TICKET": "Ein Ticket einreichen",
    "SUBMIT_TICKET": "Ein Ticket einreichen",
    "SUBMIT": "Absenden",
    "SUBMITTED_ON": "Gesendet am",
    "SUBNET_ID": "Subnetz-ID",
    "SUBSCRIPTION_GROUP_NAME": "Name der Abonnementgruppe",
    "SUBSCRIPTION_GROUP": "Abonnementgruppe",
    "SUBSCRIPTION_GROUPS_TEXT": "Konfigurieren Sie die Regionen und Abonnements für Ihr Azure-Konto.",
    "SUBSCRIPTION_GROUPS": "Abonnementgruppen",
    "SUBSCRIPTION_ID": "Azure-Abonnement-ID",
    "SUBSCRIPTION_REQUIRED_MESSAGE": "Für diese Funktion ist ein Abonnement erforderlich, das Ihre Organisation gegenwärtig nicht hat.  Wenden Sie sich an Ihren Kundenvertreter, um mehr über diese Funktion zu erfahren.",
    "SUBSCRIPTION_REQUIRED": "Abonnement erforderlich",
    "SUBSCRIPTIONS": "Abonnements",
    "SUCCESS": "Erfolg",
    "SUCCESSFULLY_DELETED": "Erfolgreich gelöscht",
    "SUCCESSFULLY_DISABLED": "Erfolgreich deaktiviert",
    "SUCCESSFULLY_ENABLED": "Erfolgreich aktiviert",
    "SUCCESSFULLY_REGENERATED": "Erfolgreich neu generiert",
    "SUCCESSFULLY_SAVED": "Erfolgreich gespeichert",
    "SUCCESSFULLY_UPDATED": "Erfolgreich aktualisiert",
    "SUDAN_AFRICA_KHARTOUM": "Afrika / Khartoum",
    "SUDAN": "Sudan",
    "SUM": "SUMME",
    "SUMO_LOGIC": "Sumo Logic",
    "SUNDAY": "Sonntag",
    "SUPPORT_INFORMATION": "Support-Informationen",
    "SUPPORT_TUNNEL": "Support-Tunnel",
    "SUPPORT": "Support",
    "SURINAME_AMERICA_PARAMARIBO": "Amerika/Paramaribo",
    "SURINAME": "Surinam",
    "SURROGATE_IP_REFRESH_RATE": "Dauer bis zur nächsten Validierung des Passworts",
    "SUSPICIOUS_DESTINATION": "Verdächtiges Ziel",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN": "Arctic/Longyearbyen",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS": "Spitzbergen und Jan Mayen Inseln",
    "SVALBARD_AND_JAN_MAYEN": "Spitzbergen und Jan Mayen",
    "SVPN": "Z-Tunnel 2.0",
    "SWAZILAND_AFRICA_MBABANE": "Afrika / Mbabane",
    "SWAZILAND": "Swasiland",
    "SWEDEN_EUROPE_STOCKHOLM": "Europa / Stockholm",
    "SWEDEN": "Schweden",
    "SWEDENCENTRAL": "(Europa) Schweden Mitte",
    "SWITZERLAND_EUROPE_ZURICH": "Europa / Zürich",
    "SWITZERLAND": "Schweiz",
    "SWITZERLANDNORTH": "(Europa) Schweiz Nord",
    "SWITZERLANDWEST": "(Europa) Schweiz West",
    "SYRIA": "Syrien",
    "SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS": "Asien/Damascus",
    "SYRIAN_ARAB_REPUBLIC": "Syrisch Arabische Republik",
    "SYSLOG_DESC": " Das Syslog-Protokoll wird für die Übertragung von Benachrichtigungen über Ereignisse zwischen einem Client und einem Server über Netzwerke eingesetzt.",
    "SYSLOG": "Syslog",
    "SYSTEM_IN_READ_ONLY_MODE_ONLY": "Benutzeroberfläche befindet sich derzeit im schreibgeschützten Modus.",
    "SYSTEM_IN_READ_ONLY_MODE": "Die Benutzeroberfläche wird aktualisiert. Sie befindet sich gegenwärtig im schreibgeschützten Modus.",
    "SYSTEM_SETTINGS": "Systemeinstellungen",
    "SYSTEM_USER": "Systembenutzer",
    "SYSTEM": "System",
    "TAB_SEPARATED": "Durch Tabzeichen getrennt",
    "TABLE_OPTIONS": "Tabellenoptionen",
    "TACACS_DESC": "Terminal Access Controller Access-Control-System bezieht sich auf eine Familie verwandter Protokolle für die Remote-Authentifizierung und zugehörige Dienste zur vernetzten Zugangskontrolle durch einen zentralen Server.",
    "TACACS_PLUS_DESC": " TACACS + (Terminal Access Controller Access-Control System Plus) ist ein proprietäres Protokoll von Cisco Systems, das für die Zugriffskontrolle von Routern, Network Access Servern und andere Netzwerk-fähige Geräte über einen oder mehrere zentrale Server sorgt.",
    "TACACS_PLUS": "TACACS+",
    "TACACS": "TACACS",
    "TAGGED": "Tagged",
    "TAGS": "Tags",
    "TAIWAN_ASIA_TAIPEI": "Asien/Taipeh",
    "TAIWAN": "Taiwan",
    "TAJIKISTAN_ASIA_DUSHANBE": "Asien/Dushanbe",
    "TAJIKISTAN": "Tajikistan",
    "TANZANIA_AFRICA_DAR_ES_SALAAM": "Afrika / Dar es Salaam",
    "TANZANIA": "Tansania",
    "TARGET_ORG_ID": "Ziel-OrgID",
    "TARINGA_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host taringa.net",
    "TARINGA": "Taringa",
    "TASTELESS_DESC": " Seiten im Zusammenhang mit Folter, unangebrachtem Verhalten gegenüber Menschen und Tieren.",
    "TASTELESS": "Geschmacklos",
    "TATTOODESIGNS": "Tätowierungs-Designs",
    "TB": "TB",
    "TCF": "TCF",
    "TCHATCHE_DESC": "Tchatche ist eine Instant Messaging Webseite",
    "TCHATCHE": "Tchatche",
    "TCP_ANY_DESC": "TCP (Transmission Control Protocol) ist eines der zentralen Protokolle der Internet-Protokoll-Suite (IP). Deshalb wird die gesamte Suite auch TCP / IP genannt.",
    "TCP_ANY": "TCP",
    "TCP_DESC": " Das Transmission Control Protocol (TCP) ist eines der zentralen Protokolle der Internet-Protokollsuite. Es ist so verbreitet, dass die gesamte Suite oft TCP / IP genannt wird.",
    "TCP_DEST_PORTS": "TCP-Zielports",
    "TCP_OVER_DNS_DESC": " Tcp-over-dns enthält einen speziellen DNS-Server und einen speziellen DNS-Client. Client und Server arbeiten zusammen, um einen TCP- und UDP-Tunnel durch das Standard-DNS-Protokoll aufzubauen.",
    "TCP_OVER_DNS": "TCP über DNS",
    "TCP_PORT": "TCP-Port",
    "TCP_PORTS": "TCP-Ports",
    "TCP_SRC_PORTS": "TCP-Quellports",
    "TCP_STATS_COUNTER_INTERVAL": "TCP-Statuszähler erfassen",
    "TCP_UNKNOWN_DESC": " Dies identifiziert TCP-Proxy / Firewall-Traffic, für den keine granularere App ermittelt werden kann",
    "TCP_UNKNOWN": "TCP Unbekannt",
    "TCP": "TCP",
    "TDS_DESC": "Protokoll für das relationale Datenbankverwaltungssystem Microsoft SQL",
    "TDS": "TDS",
    "TEACHERTUBE_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an die Hosts teachertube.com und teachertube.biz",
    "TEACHERTUBE": "TeacherTube",
    "TEACHSTREET_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host teachstreet.com",
    "TEACHSTREET": "TeachStreet",
    "TEAMSPEAK_DESC": " Die proprietäre Speak 2-Protokoll wird von Spielern und der Speak 2 VoIP-Software verwendet",
    "TEAMSPEAK_V3_DESC": " Teamspeak 3 setzt die Tradition des Kommunikationssystems Teamspeaks fort. Teamspeak 3 ist nicht nur eine Erweiterung seiner Vorgänger, sondern ein komplett neu in C ++ geschriebes, proprietäres Protokoll mit Core-Technologie",
    "TEAMSPEAK_V3": "TeamSpeak 3",
    "TEAMSPEAK": "TeamSpeak",
    "TEAMVIEWER_DESC": " Teamviewer  ist eine Applikation, die eine Verbindung zu einem Remote-Computer herstellt, um Wartungsarbeiten durchführen zu können. Es ist ebenfalls möglich, das aktuelle Display auf einem entfernten Computer anzuzeigen, um die Übertragung von Dateien zu ermöglichen oder einen VPN-Tunnel zu erstellen",
    "TEAMVIEWER": "TeamViewer",
    "TECHINLINE_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host techline.com.  Es klassifiziert auch den SSL-Traffic zu dem Common Name techline.com",
    "TECHINLINE": "Techinline",
    "TECHNICAL_PRIMARY": "Primärer technischer Kontakt",
    "TECHNICAL_SECONDARY": "Sekundärer technischer Kontakt",
    "TECHNOLOGY_COMMUNICATION": "Technologie und Kommunikation",
    "TECHNOLOGY": "Technologie",
    "TED": "TED",
    "TELECOMMUNICATION": "Telekommunikation",
    "TELEGRAM_DESC": " Telegram ist ein Instant Messaging-Protokoll wie Whatsapp",
    "TELEGRAM": "Telegram",
    "TELEVISION_AND_MOVIES_DESC": " Seiten in Bezug auf Fernsehprogramme oder Filme, unabhängig davon, ob es sich um Streaming oder Herunterladen von Medieninhalten handelt.",
    "TELEVISION_AND_MOVIES": "TV/Filme",
    "TELEVISION_MOVIES_DESC": " Seiten in Bezug auf Fernsehprogramme oder Filme, unabhängig davon, ob es sich um Streaming oder Herunterladen von Medieninhalten handelt.",
    "TELEVISION_MOVIES": "TV/Filme",
    "TELNET_DESC": " Telnet bietet eine allgemeine, bi-direktionale, 8-Bit-Byte-orientierte Kommunikationsvorrichtung. Vorrangiges Ziel ist eine Standardschnittstelle zwischen Endgeräten und Prozessen zur Verfügung zu stellen",
    "TELNET": "Telnet",
    "TELNETS_DESC": " Sichere Version von Telnet",
    "TELNETS": "Sicheres Telnet",
    "TEMPLATE_NAME": "Vorlagenname",
    "TEMPLATE_NOT_FOUND": "Vorlage nicht gefunden",
    "TEMPLATE_PREFIX": "Vorlagenpräfix",
    "TEMPLATE": "Vorlage",
    "TENANT_ID": "Mandanten-ID",
    "TENANT_NAME": "Instanzname",
    "TERRA_FORMATION": "Terraform",
    "TEST_CONNECTIVITY_FAILED": "Konnektivitätstest fehlgeschlagen",
    "TEST_CONNECTIVITY_SUCCESSFUL": "Konnektivitätstest erfolgreich",
    "TEST_ENVIRONMENT_TEXT": "Traffic-Tests sind simulierte HTTP/HTTPS-Anfragen, die aus einer von Zscaler erstellten Testumgebung ausgeführt werden. Diese Testumgebung ist über ein VPC-Endgerät mit dem Gateway verbunden. Eine einzelne Testumgebung ist mit einer Zscaler-Instanz verknüpft. Alle Tests werden aus derselben Umgebung ausgeführt.",
    "TEST_ENVIRONMENT": "Testumgebung",
    "TEST_EXECUTED": "Test wurde ausgeführt",
    "TEST_NAME": "Name des Tests",
    "TEST_PROTOCOL": "Testprotokoll",
    "TESTS": "Tests",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_R53": "Einstiegs-Bereitstellungsvorlage mit ZPA und hoher Verfügbarkeit",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_TEMPLATE": "Einstiegs-Bereitstellungsvorlage mit hoher Verfügbarkeit",
    "TF_DEFAULT_DEPLOYMENT_TEMPLATE": "Einstiegs-Bereitstellungsvorlage",
    "TF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "Einstiegs-Bereitstellungsvorlage mit ZPA",
    "TF_STARTER_DEPLOYMENT_GWLB_TEMPLATE": "Einstiegs-Bereitstellungsvorlage mit Gateway Load Balancer (GWLB)",
    "TFTP_DESC": "TFTP (Trivial File Transfer Protocol) ist ein File Transfer Protokoll, das besonders einfach in der Anwendung ist.",
    "TFTP": "TFTP",
    "THAILAND_ASIA_BANGKOK": "Asien/Bangkok",
    "THAILAND": "Thailand",
    "THE_BASE_URL_FOR_YOUR_API": "Der Basis-URL für Ihre API ist",
    "THE_FOLLOW_REGIONS_ARE_PENDING": "Die folgenden Regionen sind ausstehend ",
    "THE_FOLLOWING_STATIC_LEASE_IP_IS_NOT_INCLUDED_ON_THE_ADDRESS_RANGE": "Die folgende statische Lease-IP-Adresse ist nicht im Adressbereich enthalten.",
    "THE_GAMBIA": "Gambia",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_SUBNET": " Die Gateway-IP muss sich in einem LAN-Subnetz befinden.",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_WAN_SUBNET": " Die Gateway-IP-Adresse muss sich in einem LAN- oder WAN-Subnetz befinden.",
    "THE_NETHERLANDS": "Niederlande",
    "THERE_IS_A_PROBLEM_SAVING_PROVISIONING_TEMPLATE": "Beim Löschen der Bereitstellungsvorlage ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "THERE_IS_A_PROBLEM_SAVING_VDI_TEMPLATE": "Beim Löschen der VDI-Vorlage ist ein Problem aufgetreten. Bitte versuchen Sie es später erneut.",
    "THREAT_LIBRARY": "Bedrohungsbibliothek",
    "THROUGHPUT_ACROSS_SERVICES": "Dienstübergreifender Durchsatz",
    "THROUGHPUT_KBPS_PER_SESSION": "Durchsatz (kbit/s) pro Sitzung",
    "THROUGHPUT_SESSION": "[Durchsatz | Sitzung]",
    "THURSDAY": "Donnerstag",
    "TIME_FRAME": "Zeitraum",
    "TIME_ZONE": "Zeitzone",
    "TIMESTAMP": "Zeitstempel",
    "TIMEZONE": "Zeitzone",
    "TIMOR_LESTE_ASIA_DILI": "Asien/Dili",
    "TIMOR_LESTE": "Timor-Leste",
    "TLS": "TLS",
    "TO": "An",
    "TOGO_AFRICA_LOME": "Afrika / Lome",
    "TOGO": "Tokelau",
    "TOKELAU_PACIFIC_FAKAOFO": "Pacific/Fakaofo",
    "TOKELAU": "Tokelau",
    "TOKEN_VALUE": "Tokenwert",
    "TOKEN": "Token",
    "TONGA_PACIFIC_TONGATAPU": "Pacific/Tongatapu",
    "TONGA": "Tonga",
    "TOOLS": "Tools",
    "TOOLTIP_ACCOUNT_GROUP_DESCRIPTION": "Geben Sie beschreibende Informationen über die Gruppe ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_AUTH_DEVICES": "Zeigt die Geräte des Administrators an, die für die Nutzung der Executive Insights-App autorisiert sind. Administratoren können bis zu 5 Geräte registrieren.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_COMMENTS": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Kommentare dürfen nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_CONFIRM_PASSWORD": "Geben Sie das Passwort zur Bestätigung erneut ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL": "Geben Sie die gültige geschäftliche E-Mail-Adresse des Administrators ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EXEC_MOBILE_APP_ENABLE": "Gewährt dem Administrator Zugriff auf die Executive Insights-App.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_LOGIN_ID": "Geben Sie die Anmelde-ID ein, die der Administrator für die Anmeldung auf Ihrem SSO-Anbieterportal verwendet. Wählen Sie den betreffenden Domänennamen aus. (Die Domänenamen, die Sie für Zscaler angegeben haben, werden in der Dropdownliste angezeigt.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_MOBILE_APP_ENABLE": "Aktivieren, um einem Administrator Zugriff auf die Executive Insights-App zu gewähren. Um diese Einstellung zu aktivieren, benötigt der Administrator den Geltungsbereich <b>Organisation</b> und die Rolle <b>Administrator</b>, wobei <b>Berechtigungen für die Executive Insights-App aktivieren</b> ausgewählt ist.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_NAME": "Geben Sie den Namen des Administrators ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD_BASED_LOGIN": "Aktivieren, wenn der Administrator die Möglichkeit haben soll, sich direkt beim Admin-Portal anzumelden. Dies kann zusätzlich zur Aktivierung von <b>SAML-SSO für Administratoren</b> geschehen.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD": "Geben Sie ein Passwort für den Administrator ein. Dies kann zwischen 8 und 100 Zeichen lang sein und es muss mindestens eine Zahl, ein Sonderzeichen und einen Großbuchstaben enthalten.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PRODUCT_UPDATES": "Aktivieren, wenn der Administrator E-Mail-Benachrichtigungen zu wichtigen Änderungen und Aktualisierungen unseres Dienstes erhalten soll.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_ROLE": "Wählen Sie eine {0}Rolle{1}, um die Zugriffsebene des Administrators für das Admin-Portal festzulegen. Rollen, die Sie konfiguriert haben, werden in der Dropdownliste angezeigt. Sie können auch nach Rollen suchen oder auf das Symbol {2}Hinzufügen{3} klicken, um eine neue Rolle hinzuzufügen. Wenn Sie {4}Admininistratorenrang{5} aktiviert haben, bestimmt Ihr zugewiesener Administratorrang die Rollen, die Sie auswählen können.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_DEPARTMENTS": "Legen Sie fest, welche Abteilungen der Administrator im Admin-Portal verwalten darf. ",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATION_GROUPS": "Legen Sie fest, welche Gruppen der Administrator im Admin-Portal verwalten darf.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATIONS": "Legen Sie fest, welche Standorte der Administrator im Admin-Portal verwalten darf.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE": "Wählen Sie einen {0}Geltungsbereich{1} aus, um festzulegen, welche Bereiche der Organisation ein Administrator im Admin-Portal verwalten kann. Der Ihnen zugewiesene Geltungsbereich bestimmt, welche Bereiche Sie auswählen können.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SECURITY_UPDATES": "Aktivieren, wenn der Administrator E-Mail-Benachrichtigungen zu Schwachstellen und Bedrohungen erhalten soll, die sich auf Ihr Unternehmen auswirken können.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SERVICE_UPDATES": "Aktivieren, wenn der Administrator E-Mail-Benachrichtigungen zu neuen Dienst- und Produktverbesserungen erhalten soll, einschließlich Benachrichtigungen zu neuen Rechenzentren und Informationen zu Cloud-Versionen.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_STATUS": "Den Administrator aktivieren oder deaktivieren",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_UNAUTH_DEVICE": "Autorisierung zurückziehen",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ALLOW_TO_CREATE_NEW_LOCATION": "Gestattet dem Benutzer, neue Standorte zu erstellen.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_COMMENTS": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Kommentare dürfen nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_CONFIRM_PASSWORD": "Geben Sie das Passwort zur Bestätigung erneut ein",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_LOGIN_ID": "Geben Sie die Anmelde-ID des <b>Auditors</b> ein und wählen Sie den entsprechenden Domänennamen aus. (Die Domänenamen, die Sie für Zscaler angegeben haben, werden in der Dropdownliste angezeigt.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NAME": "Geben Sie den Namen des <b>Auditors</b> ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NEW_PASSWORD": "Geben Sie ein Passwort für den Auditor ein. Dies kann zwischen 8 und 100 Zeichen lang sein und es muss mindestens eine Zahl, ein Sonderzeichen und einen Großbuchstaben enthalten.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_EMAIL": "Geben Sie die E-Mail-Adresse des Partneradministrators ein und wählen Sie den entsprechenden Domänennamen aus. (Die Domänenamen, die Sie für Zscaler angegeben haben, werden in der Dropdownliste angezeigt.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_LOGIN_ID": "Geben Sie die Anmelde-ID ein, mit der sich der Partneradministrator bei Ihrem SSO-Anbieterportal anmeldet, und wählen Sie den betreffenden Domänennamen aus. (Die Domänenamen, die Sie für Zscaler angegeben haben, werden in der Dropdownliste angezeigt.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_NAME": "Geben Sie den Namen des Partneradministrators ein.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_PASSWORD": "Wenn der Partneradministrator die Möglichkeit haben soll, sich direkt beim Admin-Portal anzumelden, geben Sie ein Passwort für ihn ein. Das Passwort kann zwischen 8 und 100 Zeichen lang sein und es muss mindestens eine Zahl, ein Sonderzeichen und einen Großbuchstaben enthalten. Dies kann zusätzlich zur Aktivierung von SAML-SSO für Partneradministratoren geschehen.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_ROLE": "Wählen Sie eine <b>Partnerrolle</b> aus, um die Zugriffsebene des Partneradministrators für das Admin-Portal festzulegen. Partnerrollen, die Sie konfiguriert haben, werden in der Dropdownliste angezeigt. Sie können auch nach Rollen suchen oder auf das Symbol <b>Hinzufügen</b> klicken, um eine neue Partnerrolle hinzuzufügen.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_DOWNLOAD_XML_METADATA": "Klicken Sie auf {0}Herunterladen{1}, um die XML-Metadaten des Zscaler-Dienstes zu exportieren. Die Metadaten beschreiben Zscaler-SAML-Funktionen und sie werden für die automatische Konfiguration verwendet. Einige {2}IdPs{3} benötigen die Metadaten, um Dienstanbieter zu konfigurieren.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ENABLE_SAML_AUTH": "Aktivieren, um dem Administrator zu gestatten, sich mit einem Passwort direkt beim {0}ZIA-Admin-Portal{1} anzumelden. Es muss bereits ein {2} IdP{3} (wie z. B. ADFS oder Okta) für Ihre Organisation konfiguriert sein und Sie müssen{4} das Administratorkonto im Admin-Portal hinzufügen{5} (anstatt über die automatische Bereitstellung.)",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ISSUERS": "Geben Sie optional den IdP-Aussteller ein, der mit dem Zscaler-Dienst verknüpft ist.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_UPLOAD_SSL_CERTIFICATE": "Klicken Sie auf {0} Hochladen{1}, um das öffentliche SSL-Zertifikat hochzuladen, das zum Verifizieren der digitalen Signatur des IdP verwendet werden soll. Dies ist das Basis-64-codierte PEM-Format, das Sie vom IdP heruntergeladen haben. Die Dateierweiterung muss .pem oder .cer sein und der Dateiname darf keine weiteren Punkte enthalten.",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML": "Aktivieren, damit Administratoren sich mit einem Passwort direkt beim Cloud Connector-Admin-Portal anmelden können. Sie können diese Authentifizierungsmethode mit SAML-SSO verwenden.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_ADMIN_EDGE_CONNECTOR_TRAFFIC_FORWARDING_DNS": "Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Weiterleitung (Datenverkehr, DNS und Protokolle) erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die API-Schlüsselverwaltung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Remotesupport-Verwaltung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Administrationssteuerung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Cloud Connector-Bereitstellung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf Dashboards erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_LOCATION_MANAGEMENT": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Standortverwaltung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION": " Festlegen, ob Administratoren vollen oder keinen Zugriff auf NSS erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_POLICY_CONFIGURATION": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf Richtlinien und Verwaltung erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": " Legen Sie fest, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf die Konfigurationsverwaltung der öffentlichen Cloud erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE": " Festlegen, ob Administratoren vollen, schreibgeschützten oder keinen Zugriff auf Standort- und Bereitstellungsvorlagen erhalten sollen.",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME": "Geben Sie einen Namen für die {0}Rolle{1} ein.",
    "TOOLTIP_ALL_APP_SEGMENTS_ONLY": "Das ZPA Edge App Segment ist nur verfügbar, wenn 'Auf alle App Segments anwenden' aktiviert ist.",
    "TOOLTIP_ALLOW_AWS_ACCOUNT_ID": "Wenn Sie dies aktivieren, kann Zscaler die AWS-Konto-ID importieren und im Portal anzeigen.",
    "TOOLTIP_ALLOW_AZURE_SUBSCRIPTION_ID": "Wenn Sie dies aktivieren, kann Zscaler die Azure-Abonnement-ID importieren und im Portal anzeigen.",
    "TOOLTIP_ALLOW_GCP_PROJECT_ID": "Wenn Sie dies aktivieren, kann Zscaler die GCP Project-ID importieren und im Portal anzeigen.",
    "TOOLTIP_AWS_CONFIG_NAME": "Der Name des AWS-Kontos.",
    "TOOLTIP_AWS_GROUP_NAME": "Geben Sie den Namen ein, der für diese Gruppe verwendet werden soll.",
    "TOOLTIP_AWS_ROLE_NAME": "Der Name der AWS-Rolle im zuvor eingegebenen AWS-Konto, das von Zscaler übernommen wird.",
    "TOOLTIP_BC_BC_GROUP": "Wählen Sie eine bestehende Branch Connector-Gruppe für Ihre Bereitstellungsvorlage aus.",
    "TOOLTIP_BC_COUNTRY": "Wählen Sie ein Land für den neuen Standort aus.",
    "TOOLTIP_BC_DNS_SERVER": "Geben Sie die IP-Adresse des DNS-Servers ein.",
    "TOOLTIP_BC_FORWARDING_NET_MASK": "Geben Sie die Netzmaske für die interne Gateway-IP-Adresse ein.",
    "TOOLTIP_BC_GROUP_5G": "Diese Connector-Gruppe wird mit dieser Deployment-Konfiguration verknüpft.",
    "TOOLTIP_BC_GROUP_NAME": "Geben Sie einen Namen für die neue Branch Connector-Gruppe ein, die Sie hinzufügen wollen.",
    "TOOLTIP_BC_HARDWARE_DEVICE": "Wählen Sie das Hardwaregerät für Ihren Branch Connector aus.",
    "TOOLTIP_BC_HYPERVISOR": "Wählen Sie den Hypervisor für Ihren Branch Connector aus.",
    "TOOLTIP_BC_INTERNAL_GATEWAY_IP_ADDRESS": "Geben Sie die interne Gateway-IP-Adresse ein.",
    "TOOLTIP_BC_IP_ADDRESS": "Geben Sie die IP-Adresse des Branch Connectors ein.",
    "TOOLTIP_BC_LOAD_BALANCER_IP_ADDRESS": "Geben Sie die IP-Adresse des Lastenausgleichs ein.",
    "TOOLTIP_BC_LOCATION_NAME": "Geben Sie einen Namen für den neuen Standort ein, den Sie hinzufügen wollen.",
    "TOOLTIP_BC_LOCATION_TEMPLATE": "Wählen Sie eine konfigurierte oder eine Standard-Standortvorlage für Ihren Bereitstellungs-URL aus.",
    "TOOLTIP_BC_LOCATION": "Wählen Sie einen bestehenden Standort für Ihren Bereitstellungs-URL aus.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_1": "Geben Sie die IP-Adresse des primären DNS-Servers ein. Dies ist einer der beiden DNS-Server, die für den Lastenausgleich verwendet werden.",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_2": "Geben Sie die IP-Adresse des sekundären DNS-Servers ein. Dies ist einer der beiden DNS-Server, die für den Lastenausgleich verwendet werden.",
    "TOOLTIP_BC_MANAGEMENT_NET_MASK": "Geben Sie die Netzmaske für die Branch Connector-IP-Adresse ein.",
    "TOOLTIP_BC_PROVISIONING_NAME": "Geben Sie einen Namen für die Branch Connector-Bereitstellungsvorlage ein.",
    "TOOLTIP_BC_SERVER_IP_ADDRESS": "Geben Sie die IP-Adresse des Dienstes ein.",
    "TOOLTIP_BC_VM_SIZE": "Dieses Feld ist standardmäßig auf 'Klein' eingestellt.",
    "TOOLTIP_BLOCK_INTERNET_ACCESS": "Aktivieren, um jeglichen Zugriff auf das Internet, einschließlich Nicht-HTTP-Datenverkehr, zu deaktivieren, bis der Benutzer die Richtlinie zur akzeptablen Nutzung akzeptiert",
    "TOOLTIP_BW_DOWNLOAD": "Legen Sie das Bandbreitenlimit für Downloads (Mbit/s) fest.",
    "TOOLTIP_BW_UPLOAD": "Legen Sie das Bandbreitenlimit für Uploads (Mbit/s) fest.",
    "TOOLTIP_CC_ROLE_NAME": "QuickInfo-Text für Cloud Connector: Dieser Name sollte mit allen Cloud Cloud Connectors in diesem Konto verknüpft sein",
    "TOOLTIP_CLOUD_COONECTOR_GROUP": "Wählen Sie die Cloud Connector Gruppen aus, die mit dieser Kontogruppe verknüpft werden sollen.",
    "TOOLTIP_CLOUD_NSS_HTTP_HEADERS": "",
    "TOOLTIP_CLOUD_PROVIDER": "Wählen Sie den Cloud-Anbieter für Ihren Cloud Connector aus.",
    "TOOLTIP_CPU": "Die empfohlene CPU für den Hypervisor.",
    "TOOLTIP_CUSTOM_AUP_FREQUENCY": "",
    "TOOLTIP_DEDICATED_BANDWIDTH": "Dies ist die Spitzenbandbreite, die zum Herunterladen der Protokolle vom Nanolog in der Zscaler-Cloud erforderlich ist. Wenn dem NSS nicht die benötigte Bandbreite zugewiesen wird, können Protokolle sich im Nanolog ansammeln. Dies kann zu häufigen Verbindungsrücksetzungen führen und die Protokolle werden nicht an den NSS gestreamt.",
    "TOOLTIP_DEFAULT_GATEWAY_IP_ADDRESS": "Geben Sie die Standard-Gateway-IP-Adresse ein.",
    "TOOLTIP_DEFAULT_GATEWAY": "Geben Sie eine gültige IP-Adresse für das Standard-Gateway ein.",
    "TOOLTIP_DEFAULT_LEASE_TIME": "Geben Sie die Standard-Lease-Zeit in Sekunden ein.",
    "TOOLTIP_DEPLOY_AS_GATEWAY": "Wählen Sie „Ja“ oder „Nein“, um zu festzulegen, ob das Hardwaregerät als Gateway bereitgestellt werden soll.",
    "TOOLTIP_DESCRIPTION": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein.",
    "TOOLTIP_DESTINATION_IP_ADDRESS": "Geben Sie IP-Adressen ein. Sie können einzelne IP-Adressen, Subnetze oder Adressbereiche eingeben. Wenn Sie mehrere Elemente hinzufügen, drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_DESTINATION_IP_COUNTRIES": "Um Ziele anhand des Standorts eines Servers zu identifizieren, wählen Sie 'Alle', um alle Länder in die Gruppe einzuschließen, oder wählen Sie bestimmte Länder aus.",
    "TOOLTIP_DESTINATION_IP_DOMAIN": "Geben Sie vollständig qualifizierte Domänennamen (FQDNs) oder Wildcard-FQDNs ein. Verwenden Sie den Punkt ('.') als Platzhalterzeichen. Um mehrere Elemente hinzufügen, drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_DESTINATION_IP_FQDN": "Geben Sie vollständig qualifizierte Domänennamen (FQDNs) ein. Wenn Sie mehrere Elemente hinzufügen, drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_DESTINATION_IP_NAME": "Gruppieren Sie Ziele, die Sie über eine Firewall-Regel steuern möchten, indem Sie IP-Adressen, Serverländer und URL-Kategorien angeben.",
    "TOOLTIP_DESTINATION_TYPE": "Zielgruppentyp auswählen",
    "TOOLTIP_DEVICE_SN": "Wählen Sie eine Geräte-Seriennummer aus.",
    "TOOLTIP_DHCP_OPTIONS": "Sie können Standard-Gateways und Domainnamen als Kriterien für DHCP erstellen.",
    "TOOLTIP_DHCP": "Wählen Sie „Aktiviert“, um DNS-Serverdetails einzugeben, oder „Deaktiviert“, um das Dynamic Host Configuration Protokoll (DHCP) zu deaktivieren.",
    "TOOLTIP_DISK_STORAGE": "Zeigt den empfohlenen Festplattenspeicher für die Arbeitslast Ihrer Organisation an.",
    "TOOLTIP_DNS_SERVER_IP_1": "Geben Sie die IP-Adresse des primären DNS-Servers ein.",
    "TOOLTIP_DNS_SERVER_IP_2": "Geben Sie die IP-Adresse des sekundären DNS-Servers ein.",
    "TOOLTIP_DNS_SERVER": "Geben Sie eine gültige IP-Adresse für den DNS-Server ein.",
    "TOOLTIP_DOMAIN_NAME": "Geben Sie einen gültigen Domänennamen ein.",
    "TOOLTIP_EBS_STORAGE": "Empfohlener Speicher",
    "TOOLTIP_EC2_INSTANCE_TYPE": "Empfohlener EC2-Instanztyp",
    "TOOLTIP_EDIT_ORGANIZATION_API_KEY_NEW_KEY": "Der neue API-Schlüssel muss alphanumerisch (A-Z, a-z, 0-9) und genau 12 Zeichen lang sein.",
    "TOOLTIP_ENABLE_AUP": "Aktivieren, um eine Nutzungsrichtlinie für nicht authentifizierten Verkehr anzuzeigen, die Benutzer akzeptieren müssen",
    "TOOLTIP_ENABLE_CAUTION": "Aktivieren, um die Warnungsrichtlinien-Aktion durchzusetzen und für nicht authentifizierten Verkehr eine Endbenutzerbenachrichtigung anzuzeigen. Wenn deaktiviert, wird die Aktion als Zulassungsrichtlinie behandelt.",
    "TOOLTIP_ENABLE_IPS_CONTROL": "Aktivieren, um einem Administrator den Zugriff auf IPS-Steuerung zu gewähren.",
    "TOOLTIP_ENABLE_SURROGATE_BROWSER": "Wenn die Option aktiviert ist und die IP-Benutzerzuordnung existiert, wird die Surrogat-Benutzeridentität auch für den Datenverkehr von bekannten Browsern verwendet. Wenn die Option deaktiviert ist, wird der Datenverkehr von bekannten Browsern immer mithilfe des konfigurierten Authentifizierungsmechanismus abgefragt und die Surrogat-Benutzeridentität wird ignoriert.",
    "TOOLTIP_ENABLE_SURROGATE_REFRESH_TIME": "Dies ist der Zeitraum, in dem die Surrogat-Benutzeridentität für den Datenverkehr von bekannten Browsern verwendet werden kann, bevor sie aktualisiert und über den konfigurierten Authentifizierungsmechanismus neu validiert werden muss. {0}{1} HINWEIS: Der Zeitraum für die Verlängerung des IP-Surrogats muss kürzer als die DHCP-Lease-Zeit sein. Andernfalls können falsche Benutzerrichtlinien angewendet werden.",
    "TOOLTIP_ENABLE_SURROGATE": "Ermöglicht die Zuordnung von Benutzern zu Geräten, wenn die interne IP-Adresse von der öffentlichen IP-Adresse unterschieden werden kann. Dies wird verwendet, um Benutzerrichtlinien für Datenverkehr durchzusetzen, der nicht mit Cookies kompatibel ist. Weitere Informationen dazu finden Sie auf der Hilfeseite.",
    "TOOLTIP_ENABLE_XFF_FORWARDING": "Aktivieren Sie 'XFF von Clientanfrage', wenn der Zscaler-Dienst die X-Forwarded-For (XFF)-Header verwenden soll, die Ihr Proxyserver am Standort in abgehende HTTP-Anfragen einfügt. Hinweis: Wenn der Dienst den Datenverkehr an sein Ziel weiterleitet, entfernt er diesen ursprünglichen XFF-Header und ersetzt ihn durch einen XFF-Header, der die IP-Adresse des Client-Gateways (die öffentliche IP-Adresse der Organisation) enthält. Dadurch wird sichergestellt, dass die internen IP-Adressen eines Unternehmens niemals der Außenwelt ausgesetzt sind.",
    "TOOLTIP_ENFORCE_AUTHENTICATION": "Aktivieren Sie 'Authentifizierung', um die Identifikation des Verkehrs einzelner Benutzer zu erzwingen, indem Sie den konfigurierten Benutzerauthentifizierungsmechanismus anwenden.",
    "TOOLTIP_ENFORCE_BAND_WIDTH_CONTROL": "Wählen Sie 'Aktivieren', um die Bandbreitensteuerung für den Standort durchzusetzen",
    "TOOLTIP_ENFORCE_FIREWALL_CONTROL": "Wählen Sie 'Firewall-Kontrolle anwenden', um die Firewall am Standort zu aktivieren.",
    "TOOLTIP_ENTER_AWS_ACCOUNT_ID": "Die AWS-Konto-ID, unter der die Workloads bereitgestellt werden.",
    "TOOLTIP_EVENT_BUS_NAME": "Eventbridge Event Bus wird zum Senden von Echtzeitbenachrichtigungen an Zscaler Event Bus verwendet. Eine Regel in Eventbridge kann verwendet werden, um Benachrichtigungen zu Ressourcenänderungen in Echtzeit an Zscaler zu senden. Dies ist erforderlich, um Echtzeitaktualisierungen von Richtlinien zu ermöglichen.Zscaler wird über die Erstellung der VM benachrichtigt.",
    "TOOLTIP_EXTERNAL_ID": "Zscaler verwendet diese externe ID im API-Aufruf an AWS beim Abrufen der Tag-Informationen. Die externe ID ist für jedes erstellte Konto eindeutig. Diese ID muss zur AWS IAM-Rollenkonfiguration hinzugefügt werden. Wenn Sie diese ID neu generieren, müssen Sie sie auch im AWS-Konto aktualisieren.",
    "TOOLTIP_FAILURE_BEHAVIOR": "Wählen Sie das Fehlerverhalten aus.",
    "TOOLTIP_FORCE_SSL_INTERCEPTION": "Aktivieren, damit SSL-Abhören eine Nutzungsrichtlinie für HTTPS-Datenverkehr durchsetzt",
    "TOOLTIP_GATEWAY_FAIL_CLOSE": "Diese Option legt fest, wie Verkehr abgewickelt wird, wenn sowohl der primäre als auch der sekundäre Proxy für dieses Gateway nicht erreichbar ist. Wenn Sie die Option aktivieren, wird der Verkehr unterbrochen, und durch Deaktivieren der Option wird der Verkehr ermöglicht. Standardmäßig ist die Option aktiviert.",
    "TOOLTIP_GATEWAY_NAME": "Geben Sie einen Namen für das Gateway ein, das für einen Drittanbieter-Proxydienst erstellt werden soll.",
    "TOOLTIP_GENERAL_DESCRIPTION": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Beschreibung darf nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_HA_ID": "Geben Sie die ID für das HA-Gerät ein.",
    "TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS": "In diesem Feld können Anmerkungen zur eingegebenen IP-Adresse angezeigt werden. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK": "Sie können eine IP-Adresse eingeben, um zu prüfen, ob sie in eine Abweisungsliste aufgenommen wurde. ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS": "Das Feld zeigt an, ob die eingegebene IP-Adresse in eine Abweisungsliste aufgenommen wurde. ",
    "TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE": "Zscaler-Supporttechnikern gestatten, sich mit vollen Administratorrechten per Fernzugriff bei Ihrem Admin-Portal anzumelden. Sie brauchen keine Konten zu erstellen und keine Passwörter zu teilen, um den Zugriff zu ermöglichen.",
    "TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE": "Autorisierten Zscaler-Mitarbeitern Nur-Lesezugriff auf Ihr Admin-Portal gewähren. Der Zugriff auf das Portal wird verwendet, um Inhalte und Berichte zum Kundenerfolg zu erstellen, Remoteunterstützung bereitzustellen und Berichte und Konfigurationen anzuzeigen, um das Produkt und die Dienste von Zscaler zu verbessern.",
    "TOOLTIP_HELP_LOOKUP_URL_ENTER_URL": "Um nach der Kategorie (oder den Kategorien) zu suchen, zu der ein URL gehört, geben Sie den URL ein und klicken Sie auf {0} URL suchen{1}. Der Dienst zeigt die {2}vordefinierte oder übergeordnete Kategorie des URL{3} an und er gibt an, ob eine Sicherheitswarnung mit dem URL verknüpft ist.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_SECURITY_ALERT": "Dieses Feld zeigt an, ob eine Sicherheitswarnung mit dem URL verknüpft ist.",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL_CLASSIFICATIONS": "Dieses Feld zeigt die {0} vordefinierte oder übergeordnete Kategorie{1} an, zu der der URL gehört. ",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL": "Dieses Feld zeigt den URL, den Sie gesucht haben.",
    "TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL": "Wir empfehlen, den Zugriff für mindestens zwei Tage zu gewähren.",
    "TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL": "Wir empfehlen, den Zugriff für mindestens ein Jahr zu gewähren.",
    "TOOLTIP_HW_DEVICE_NAME": "Geben Sie einen Namen für die Vorlage ein.",
    "TOOLTIP_HW_SUBINTERFACE_SHUTDOWN": "Wählen Sie „Aktiv“ oder „Standby“ als Uplink-Modus.",
    "TOOLTIP_HW_VLAN_ID": "Geben Sie die ID des VLAN ein.",
    "TOOLTIP_HW_VLAN": "Wählen Sie „Tagged“ oder „Untagged“ für das virtuelle lokale Netzwerk (VLAN) aus.",
    "TOOLTIP_INTERFACE_SHUTDOWN": "Wählen Sie „Ja“ oder „Nein“, um das Verhalten der Schnittstelle beim Herunterfahren festzulegen.",
    "TOOLTIP_IP_ADDRESS_RANGE": "Geben Sie den IP-Adressbereich für Ihr Gerät ein.",
    "TOOLTIP_IP_ADDRESS_WITH_NETMASK": "Geben Sie eine IP-Adresse im Format „a.b.c.d/mask“ ein.",
    "TOOLTIP_IP_POOL_NAME": "Der Name des IP-Pools.",
    "TOOLTIP_LIST_CUSTOM_OPTION_CODE": "Geben Sie einen gültigen DHCP-Optionscode gemäß IANA-Definition ein. Vordefinierte Optionen sind nicht zulässig",
    "TOOLTIP_LIST_CUSTOM_OPTION_NAME": "Geben Sie einen Namen für diese benutzerdefinierte Option ein – Nicht geparst",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_IP": "Der Datentyp IP-Adresse muss als explizite IP-Adresse eingegeben werden.  Es können bis zu vier kommagetrennte IP-Adressen definiert werden.\nBeispiel: *************, ************",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_STRING": "Der Datentyp String gibt entweder einen NVT-ASCII-Wert oder eine Reihe von Oktetten an, die hexadezimal angegeben werden.\nBeispiel: '********:/var/tmp/rootfs' ODER 43:4c:49:45:54:2d:46:4f:4f",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE": "Wählen Sie entweder IP-Adresse oder String als Typ aus.",
    "TOOLTIP_LIST_CUSTOM_OPTION": "Mit benutzerdefinierten DHCP-Optionen können Sie DHCP-Optionen konfigurieren, die nicht in der Drop-down-Liste vordefiniert sind.",
    "TOOLTIP_LIST_DNS_SERVER": "Geben Sie eine gültige IP-Adresse für den DNS-Server oder eine Liste mit Adressen ein.",
    "TOOLTIP_LIST_DOMAIN_NAME": "Geben Sie einen gültigen Domainnamen oder eine Liste mit Namen ein.",
    "TOOLTIP_LOCATION_CREATION": "Dieses Feld ist standardmäßig auf 'Automatisch' eingestellt.",
    "TOOLTIP_LOCATION_TEMPLATE_NAME": "Geben Sie einen Namen für die Standortvorlage ein, die Sie hinzufügen wollen.",
    "TOOLTIP_LOCATION_TEMPLATE": "Wählen Sie eine konfigurierte oder eine Standard-Standortvorlage für Ihren Bereitstellungs-URL aus.",
    "TOOLTIP_LOCATIONS_CUSTOM_AUP_FREQUENCY_TEXT": "Legen Sie fest (in Tagen), wie oft die Richtlinie zur akzeptablen Nutzung für Benutzer angezeigt wird.",
    "TOOLTIP_LOCATIONS_ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "Wenn die Option aktiviert ist und die IP-Benutzerzuordnung existiert, wird die Surrogat-Benutzeridentität auch für den Datenverkehr von bekannten Browsern verwendet. Wenn die Option deaktiviert ist, wird der Datenverkehr von bekannten Browsern immer mithilfe des konfigurierten Authentifizierungsmechanismus abgefragt und die Surrogat-Benutzeridentität wird ignoriert.",
    "TOOLTIP_LOCATIONS_IDLE_TIME_DISASSOCIATION": "Wenn Sie IP-Surrogat aktiviert haben, legen Sie unter 'Zeit im Leerlauf bis zur Trennung' fest, wie lange der Dienst nach einer abgeschlossenen Transaktion die IP-Adresse für die Benutzerzuordnung beibehält.",
    "TOOLTIP_MAX_LEASE_TIME": "Geben Sie die maximale Lease-Zeit in Sekunden ein.",
    "TOOLTIP_MTU_1500": "Die Maximum Transmission Unit (MTU) in Byte.",
    "TOOLTIP_MTU": "Die Maximum Transmission Unit (MTU) in Byte. Die Standardeinstellung ist 1400.",
    "TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD": "Wenn aktiviert, werden die Dashboards automatisch alle 15 Minuten aktualisiert.",
    "TOOLTIP_MY_PROFILE_CONFIRM_NEW_PASSWORD": "Geben Sie Ihr neues Passwort erneut ein. Es muss mit dem Passwort übereinstimmen, das im Feld {1}Neues Passwort{0} eingegeben wurde.",
    "TOOLTIP_MY_PROFILE_LANGUAGE": "Das Admin-Portal wird standardmäßig in englischer Sprache angezeigt. Sie können auch Spanisch, Französisch, traditionelles Chinesisch oder Japanisch wählen.",
    "TOOLTIP_MY_PROFILE_NEW_PASSWORD": "Geben Sie Ihr neues Passwort ein. Es muss mindestens acht Zeichen lang sein und mindestens eine Ziffer, einen Großbuchstaben und ein Sonderzeichen enthalten. Nur ASCII-Zeichen sind zulässig.",
    "TOOLTIP_MY_PROFILE_OLD_PASSWORD": "Geben Sie Ihr aktuelles Passwort ein.",
    "TOOLTIP_MY_PROFILE_PASSWORD": "Passwörter müssen mindestens acht Zeichen lang sein und mindestens eine Ziffer, einen Großbuchstaben und ein Sonderzeichen enthalten. Nur ASCII-Zeichen sind zulässig.",
    "TOOLTIP_MY_PROFILE_POLICY_INFORMATION": "Aktivieren, um die Richtlinieninformationen anzuzeigen.",
    "TOOLTIP_MY_PROFILE_TIMEZONE": "Beim Speichern von Transaktionen verwendet der Dienst UTC. Er verwendet die angegebene Zeitzone, bei der Anzeige von Protokollen.",
    "TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME": "Die Anmelde-ID des Administrators, die beim Erstellen des Administratorkontos zugewiesen wurde.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_DESCRIPTION": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Beschreibung darf nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_NAME": "Geben Sie einen Namen für die {0}Netzwerkdienstgruppe{1} ein. Dieser kann beliebige Zeichen und Leerzeichen enthalten.",
    "TOOLTIP_NETWORK_SERVICE_GROUP_SERVICES": "Wählen Sie eine beliebige Anzahl von benutzerdefinierten und vorkonfigurierten Diensten aus, die Sie in die Gruppe aufnehmen wollen.",
    "TOOLTIP_NETWORK_SERVICES_DEFINITION": "Der Dienst zeigt {0} Benutzerdefiniert{1} an, wenn es sich um einen von einem Administrator definierten Dienst handelt.",
    "TOOLTIP_NETWORK_SERVICES_DESCRIPTION": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Beschreibung darf nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_NETWORK_SERVICES_NAME": "Geben Sie einen Namen für den {0}Anwendungsschicht-Dienst{1} ein, den Sie steuern wollen. Der Name kann beliebige Zeichen und Leerzeichen enthalten.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_DESTINATION_PORTS": "Die SCTP-Zielportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NETWORK_SERVICES_SCTP_SOURCE_PORTS": "Die SCTP-Quellportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NETWORK_SERVICES_TCP_DESTINATION_PORTS": "Die TCP-Zielportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NETWORK_SERVICES_TCP_SOURCE_PORTS": "Die TCP-Quellportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NETWORK_SERVICES_UDP_DESTINATION_PORTS": "Die UDP-Zielportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NETWORK_SERVICES_UDP_SOURCE_PORTS": "Die UDP-Quellportnummer (Beispiel: 50) oder der Portnummernbereich (Beispiel: 1000-1050), falls vorhanden, die/der vom Netzwerkdienst verwendet wird.",
    "TOOLTIP_NSS_CLOUD_FEED_API_URL": "Die HTTPS-URL des API-Endpunktes der SIEM-Protokollsammlung.",
    "TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Den Autorisierungs-URL mit der in AZURE generierten Verzeichnis-(Instanz-)ID eingeben",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Die Zugriffsschlüssel-ID für den in AWS erstellten Benutzer eingeben",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Den geheimen Zugriffsschlüssel für den in AWS erstellten Benutzer eingeben",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID": "Die in AZURE generierte Anwendungs-(Client-)ID eingeben",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET": "Den in AZURE generierten Anwendungsclient-Geheimwert eingeben",
    "TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE": "Die folgende Zeichenfolge eingeben: client_credentials",
    "TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "Protokolle im JSON-Array-Format streamen (z. B. [{JSON1},{JSON2}])",
    "TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Die Größe einer einzelnen HTTP-Anfragenutzlast auf die SIEM Best Practice begrenzen",
    "TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "Standardmäßig aktiviert und nicht bearbeitbar",
    "TOOLTIP_NSS_CLOUD_FEED_SCOPE": "Die folgende Zeichenfolge eingeben: https://monitor.azure.com//.default",
    "TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE": "Ihr cloud-basiertes SIEM auswählen",
    "TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL": "Den URL für den im S3-Bucket erstellten Ordner eingeben",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Diesen Filter verwenden, um die Protokolle auf bestimmte Cloud/Branch Connector-Gruppen zu beschränken.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Diesen Filter verwenden, um die Protokolle auf bestimmte Cloud/Branch Connectors zu beschränken.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "Verwenden Sie diesen Filter, um die Protokolle basierend auf der privaten IP-Adresse eines Clients einzuschränken Sie können Folgendes eingeben:{0} Eine IP-Adresse wie z. B. **************{1}Einen Bereich von IP-Adressen, wie z. B. *********-**********{2}Eine IP-Adresse mit einer Netzmaske, wie z. B. ***********/24{3}Klicken Sie nach jedem Eintrag auf 'Eingabe'.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "Verwenden Sie diesen Filter, um die Protokolle auf bestimmte DNS-Anfragetypen zu beschränken.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "Verwenden Sie diesen Filter, um die Protokolle auf Sitzungen zu beschränken, die mit bestimmten DNS-Antwortcodes verknüpft sind.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "Verwenden Sie diesen Filter, um die Protokolle auf Sitzungen zu beschränken, die mit bestimmten DNS-Antwortcodes verknüpft sind.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "Die Protokolle auf Sitzungen zu beschränken, die bestimmte Daten in den DNS-Antworten enthielten. Sie können Domänennamen, IPv4- und IPv6-Adressen eingeben. Bei IPv4-Adressen können Sie eine IP-Adresse, einen Bereich von IP-Adressen oder eine IP-Adresse mit Netzmaske eingeben.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DOMAINS": "Verwenden Sie diesen Filter, um die Protokolle auf Sitzungen zu beschränken, die mit bestimmten Domänen verknüpft sind.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DURATIONS": "Verwenden Sie diesen Filter, um die Protokolle basierend auf der Dauer der Sitzung in Sekunden zu beschränken.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_LOCATIONS": "Verwenden Sie diesen Filter, um die Protokolle auf bestimmte Standorte zu beschränken, an denen Transaktionen generiert wurden. Sie können nach Standorten suchen. Sie können beliebig viele Standorte auswählen. Standorte, die nach ihrer Auswahl gelöscht wurden, erscheinen durchgestrichen.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_POLICY_ACTION": "Die Protokolle basierend auf bestimmten DNS-Richtlinienaktionen beschränken",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_RULE_NAME": "Verwenden Sie diesen Filter, um die Protokolle auf der Grundlage bestimmter Regeln in der DNS-Kontrollrichtlinie einzuschränken. Wählen Sie die Regeln aus der Liste aus.",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_ADDRESS": "Diesen Filter verwenden, um die Protokolle auf bestimmte Server-IP-Adressen zu beschränken. Sie können Folgendes eingeben:{0} Eine IP-Adresse wie z. B. **************{1}Einen Bereich von IP-Adressen, wie z. B. *********-**********{2}Eine IP-Adresse mit einer Netzmaske, wie z. B. ***********/24{3}Klicken Sie nach jedem Eintrag auf 'Eingabe'.{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_PORTS": "Verwenden Sie diesen Filter, um die Protokolle auf bestimmte Server-Ports zu beschränken. Sie können einzelne Ports oder einen Port-Bereich eingeben.",
    "TOOLTIP_NSS_FEED_DUPLICATE_LOGS": "Um sicherzustellen, dass während einer Ausfallzeit keine Protokolle übersprungen werden, geben Sie an, wie viele Minuten lang NSS doppelte Protokolle sendet.",
    "TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE": "Die Protokolle basierend auf dem Metriken-Datensatztyp beschränken",
    "TOOLTIP_NSS_FEED_ESCAPE_CHARACTER": "Geben Sie optional ein Zeichen ein, das hexadezimal kodiert werden soll, wenn es für URL, Host oder Referrer erscheint. Geben Sie zum Beispiel ein Komma (,) ein, um dieses als %2C zu kodieren. Dies ist nützlich, wenn Sie dieses Zeichen als Trennzeichen verwenden und Sie sicherstellen wollen, dass es keine fehlerhafte Trennung verursacht. Wenn eine benutzerdefinierte Codierung für einen Datensatz durchgeführt wurde, ist das Feld {eedone} für diesen Datensatz auf 'JA' gesetzt.",
    "TOOLTIP_NSS_FEED_LOG_TYPE": "Wählen Sie die Protokolltypen aus, die Sie streamen.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Diesen Filter verwenden, um die Protokolle auf bestimmte Cloud/Branch Connectors zu beschränken.",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Diesen Filter verwenden, um die Protokolle auf bestimmte Branch Connector VMs zu beschränken.",
    "TOOLTIP_NSS_FEED_NAME": "Jeder Feed ist eine Verbindung zwischen NSS und Ihrem SIEM. Geben Sie einen Namen für den Feed ein.",
    "TOOLTIP_NSS_FEED_OUTPUT_FORMAT": "Dies sind die Felder, die in der Ausgabe angezeigt werden. Sie können die Standardliste bearbeiten. Wenn Sie als Feldausgabetyp Benutzerdefiniert gewählt haben, können Sie auch das Trennzeichen ändern. Weitere Informationen zu den verfügbaren Feldern und deren Syntax finden Sie unter 'NSS-Feed-Ausgabeformat'.",
    "TOOLTIP_NSS_FEED_OUTPUT_TYPE": "Die Ausgabe erfolgt standardmäßig als kommagetrennte Liste (CSV). Wählen Sie: 'Tab-getrennt', um eine durch Tabulatoren getrennte Liste zu erstellen 'Benutzerdefiniert', um ein anderes Trennzeichen zu verwenden, z. B. einen Bindestrich. Geben Sie das Trennzeichen ein, wenn Sie das Feed-Ausgabeformat angeben. Der Feed-Ausgabetyp Ihres SIEM, falls aufgeführt",
    "TOOLTIP_NSS_FEED_SERVER": "Wählen Sie einen NSS aus der Liste aus.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Diesen Filter verwenden, um die Protokolle auf bestimmte Cloud/Branch Connector-Gruppen zu beschränken.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Diesen Filter verwenden, um die Protokolle auf bestimmte Cloud/Branch Connectors zu beschränken.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "Verwenden Sie diesen Filter, um die Protokolle basierend auf der privaten IP-Adresse eines Clients einzuschränken Sie können Folgendes eingeben:{0} Eine IP-Adresse wie z. B. **************{1}Einen Bereich von IP-Adressen, wie z. B. *********-**********{2}Eine IP-Adresse mit einer Netzmaske, wie z. B. ***********/24{3}Klicken Sie nach jedem Eintrag auf 'Eingabe'.{4}",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_GATEWAY": "Verwenden Sie diesen Filter, um die Protokolle auf bestimmte Gateways zu beschränken.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_LOCATIONS": "Verwenden Sie diesen Filter, um die Protokolle auf bestimmte Standorte zu beschränken, an denen Transaktionen generiert wurden. Sie können nach Standorten suchen. Sie können beliebig viele Standorte auswählen. Standorte, die nach ihrer Auswahl gelöscht wurden, erscheinen durchgestrichen.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "Wählen Sie 'Alle', um den NSS-Feed aller Netzwerkdienste anzuwenden, oder wählen Sie bestimmte Netzwerkdienste aus.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "Verwenden Sie diesen Filter, um die Protokolle auf der Grundlage der Aktion zu beschränken, die der Dienst entsprechend den Regeln der Weiterleitungs-Kontrollrichtlinie durchgeführt hat.",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME": "Verwenden Sie diesen Filter, um die Protokolle auf der Grundlage bestimmter Regeln in der Weiterleitungs-Kontrollrichtlinie einzuschränken. Wählen Sie die Regeln aus der Liste aus.",
    "TOOLTIP_NSS_FEED_SESSION_LOG_TYPE": "Wählen Sie den Typ von Sitzungsprotokoll aus",
    "TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE": "Wählen Sie entweder IP-Adresse oder FQDN als Zieltyp des SIEM aus, an den die Protokolle gestreamt werden.",
    "TOOLTIP_NSS_FEED_SIEM_FQDN": "Geben Sie den FQDN des SIEM ein, an das die Protokolle gestreamt werden. Stellen Sie sicher, dass das SIEM so konfiguriert ist, dass es den Feed vom NSS akzeptiert.",
    "TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS": "Geben Sie die IP-Adresse des SIEM ein, an das die Protokolle gestreamt werden. Stellen Sie sicher, dass das SIEM so konfiguriert ist, dass es den Feed vom NSS akzeptiert.",
    "TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT": "Geben Sie eine geeignete Ratenbegrenzung für die Ereignisse pro Sekunde ein, die an Ihr SIEM gestreamt werden sollen. Wenn die Begrenzung für das Datenverkehrsvolumen zu niedrig ist, kommt es zu Protokollverlusten.",
    "TOOLTIP_NSS_FEED_SIEM_RATE": "Belassen Sie die Einstellung auf „Unbegrenzt“, sofern Sie nicht den Ausgabestream wegen SIEM-Lizenzierungs- oder anderer Beschränkungen drosseln müssen.",
    "TOOLTIP_NSS_FEED_SIEM_TCP_PORT": "Geben Sie die Port des SIEM ein, an das die Protokolle gestreamt werden. Stellen Sie sicher, dass das SIEM so konfiguriert ist, dass es den Feed vom NSS akzeptiert.",
    "TOOLTIP_NSS_FEED_STATUS": "Der NSS-Feed ist standardmäßig aktiviert. Deaktivieren Sie ihn, falls Sie ihn erst später aktivieren möchten.",
    "TOOLTIP_NSS_FEED_TIMEZONE": "Dies ist standardmäßig die Zeitzone der Organisation. Die von Ihnen festgelegte Zeitzone gilt für das Zeitfeld in der Ausgabedatei. Die Zeitzone wird automatisch an Beginn und Ende der Sommerzeit in der jeweiligen Zeitzone angepasst. Die konfigurierte Zeitzone als kann als getrenntes Feld an die Protokolle ausgegeben werden. Die Liste der Zeitzonen wird aus der IANA-Zeitzonen-Datenbank abgeleitet. Es können auch die Unterschiede zu GMT angegeben werden.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "Geben Sie optional die Anzahl der Benutzer an.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "Sie können diese Daten abrufen, indem Sie das Dashboard „DNS-Überblick“ aufrufen. Dies wird für die Feineinstellung der VM-Spezifikation für die Arbeitslast Ihres Unternehmens empfohlen.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR": "Sie können diese Daten abrufen, indem Sie das Dashboard „Firewall-Überblick“ aufrufen. Dies wird für die Feineinstellung der VM-Spezifikation für die Arbeitslast Ihres Unternehmens empfohlen.",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM": "Zeigt die für die Bereitstellung von NSS verwendete Plattform an.",
    "TOOLTIP_NSS_SERVER_NAME": "Geben Sie einen Namen für den NSS-Server ein.",
    "TOOLTIP_NSS_SERVER_SSL_CERTIFICATE": "",
    "TOOLTIP_NSS_SERVER_STATE": "Der Zustand des NSS-Servers.",
    "TOOLTIP_NSS_SERVER_STATUS": "Der NSS ist standardmäßig aktiviert.",
    "TOOLTIP_NSS_TYPE": "Dieses Feld ist schreibgeschützt.",
    "TOOLTIP_NSS_VIRTUAL_MACHINE": "Klicken, um die NSS-OVA-Datei herunterzuladen.",
    "TOOLTIP_NUMBER_OF_CORES": "Die empfohlene Anzahl von Kernen für den Hypervisor.",
    "TOOLTIP_PASSPHRASE": "Geben Sie eine Passphrase für das Gerät ein.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRATION_EC": "Aktivieren Sie das Ablaufen von Passwörtern für alle Administratoren, die sich bei ZIA-, Cloud Connector- und ZDX Admin-Portalen anmelden. Wenn diese Option deaktiviert ist, werden Passwörter nie ungültig.",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRES_AFTER": "Geben Sie ein, wie viele Tage die Passwörter für ZIA- und ZDX-Administratoren gültig sein sollen. Der Zeitraum kann 15 bis 365 Tage betragen.",
    "TOOLTIP_PEER_DHCP": "Geben Sie eine Peer-DHCP-IP-Adresse ein.",
    "TOOLTIP_POLICY_APP_SEGMENT_GROUP": "Wählen Sie bis zu 600 Segmentgruppen aus, auf die die Traffic-Weiterleitungsregel angewendet werden soll. Wenn keine Gruppe ausgewählt ist, gilt die Regel für keine Gruppen",
    "TOOLTIP_POLICY_APP_SEGMENT": "Wählen Sie bis zu 50.000 Application Segments aus, auf die die Traffic-Weiterleitungsregel angewendet werden soll. Wenn keine Segmente ausgewählt sind, gilt die Regel für keine Segmente.",
    "TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS": "Wählen Sie eine beliebige Anzahl vorkonfigurierter oder benutzerdefinierter Netzwerkdienstgruppen aus. Wenn keine Netzwerkdienstgruppe ausgewählt ist, gilt die Regel für alle Netzwerkdienstgruppen.",
    "TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES": "Geben Sie Wildcards und vollständig qualifizierte Domainnamen (FQDNs) ein. Wenn Sie mehrere Elemente hinzufügen, drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_POLICY_DNS_DESTINATION_GROUPS": "Wählen Sie eine beliebige Anzahl von Zielgruppen aus. Wenn keine Zielgruppe ausgewählt wird, gilt die Regel für alle Zielgruppen.",
    "TOOLTIP_POLICY_DNS_GATEWAY": "Wählen Sie ein DNS-Gateway aus.",
    "TOOLTIP_POLICY_DNS_RULE_ORDER": "Richtlinienregeln werden in aufsteigender numerischer Reihenfolge ausgewertet (Regel 1 vor Regel 2 usw.), und die Regelreihenfolge gibt den Platz dieser Regel in der Reihenfolge an.",
    "TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT": "Aktivieren Sie diese Einstellung, um die Traffic-Weiterleitungsregel auf alle bestehenden App Segments anzuwenden und auf alle zukünftigen, die erstellt werden. Deaktivieren Sie diese Einstellung, um bestimmte Application Segments oder Segmentgruppen auszuwählen.",
    "TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC": "Wählen Sie bis zu 32 Gruppen aus. Wenn keine Gruppe ausgewählt ist, gilt die Regel für alle Gruppen.",
    "TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES": "Wählen Sie eine beliebige Anzahl von Netzwerkdiensten aus. Wenn kein Netzwerkdienst ausgewählt ist, gilt die Regel für alle Netzwerkdienste. Die Zscaler-Firewall umfasst vorkonfigurierte Dienste und Sie können bis zu 1024 zusätzliche benutzerdefinierte Dienste konfigurieren.",
    "TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC": "Wählen Sie aus den folgenden Optionen:{0}Zulassen{1}: DNS-Anfragen und -Antworten zulassen.{2}Blockieren{3}: Alle DNS-Anfragen und -Antworten im Hintergrund blockieren.{4}Von ZPA aufgelöst{5}: Fordert den Cloud Connector auf, DNS-Anfragen über den IP-Pool aufzulösen. Für diese Option muss ein IP-Pool verfügbar sein.{6}Anfrage umleiten{7}: Alle DNS-Anfragen und -Antworten an ein DNS-Gateway umleiten. Für diese Option muss ein DNS-Gateway verfügbar sein.{8}",
    "TOOLTIP_POLICY_FIREWALL_DESCRIPTION": "(Optional) Geben Sie zusätzliche Hinweise oder Informationen ein. Die Beschreibung darf nicht länger als 10.240 Zeichen sein.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY": "Um Ziele basierend auf dem Standort eines Servers zu identifizieren, wählen Sie eine beliebige Anzahl von Ländern aus. Wenn kein Land ausgewählt ist, gilt die Regel für alle Länder.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES": "Geben Sie IP-Adressen und voll qualifizierten Domänennamen (FQDNs) ein, wenn die Domäne über mehrere Ziel-IP-Adressen verfügt oder wenn sich ihre IP-Adressen ändern können. Für IP-Adressen können Sie einzelne IP-Adressen, Subnetze oder Adressbereiche eingeben. Wenn Sie mehrere Elemente hinzufügen, drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS": "Wählen Sie eine beliebige Anzahl von Ziel-IP-Adressgruppen aus. Wenn keine Ziel-IP-Adressgruppe ausgewählt ist, gilt die Regel für alle Ziel-IP-Adressgruppen.",
    "TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD": "Wählen Sie eine der folgenden Weiterleitungsmethoden für diese Regel aus:{0}Direkt:{1} Umgeht Zscaler Internet Access (ZIA) und/oder Zscaler Private Access (ZPA) und leitet Traffic unter Verwendung der IP-Adresse des Zscaler-Dienstes direkt an den Zielserver weiter{2}Direkt mit SCTP-Übersetzung:{3} Leitet Traffic direkt an das Ziel weiter, während SCTP- Traffic über UDP getunnelt wird (und umgekehrt).{4}ZIA:{5} Leitet Traffic über das ZIA-Gateway an ZIA weiter.{6}ZPA:{7} Leitet Traffic über die ZPA-Cloud an ZPA weiter.{8}ZPA mit SCTP-Übersetzung:{9} Leitet Traffic über die ZPA-Cloud an Zscaler Private Access (ZPA) weiter, während SCTP-Traffic über UDP getunnelt wird (und umgekehrt).{0}Verwerfen:{1} Verwirft alle Pakete, die der Traffic-Weiterleitungsregel entsprechen{2}",
    "TOOLTIP_POLICY_FIREWALL_GATEWAY": "Wählen Sie ein Gateway aus",
    "TOOLTIP_POLICY_FIREWALL_IPPOOL": "Wählen Sie einen IP-Pool aus",
    "TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP": "Wählen Sie bis zu 32 Standortgruppen aus. Wenn keine Standortgruppe ausgewählt ist, gilt die Regel für alle Standortgruppen.",
    "TOOLTIP_POLICY_FIREWALL_LOCATION": "Wählen Sie bis zu 8 Standorte aus. Wenn kein Standort ausgewählt ist, gilt die Regel für alle Standorte.",
    "TOOLTIP_POLICY_FIREWALL_RULE_MSFT_OFFICE_365": "Diese Regel wird automatisch erstellt, wenn Sie 'Microsoft Recommended One Click configuration for Office 365' aktivieren, um lokales Breakout für allen Office 365-Datenverkehr im Cloud Firewall-Produkt zu ermöglichen.",
    "TOOLTIP_POLICY_FIREWALL_RULE_NAME": "Der DNS erstellt automatisch einen Regelnamen, den Sie ändern können. Der Name kann maximal 31 Zeichen lang sein.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ORDER": "Richtlinienregeln werden in aufsteigender numerischer Reihenfolge ausgewertet (Regel 1 vor Regel 2 usw.), und die Regelreihenfolge gibt den Platz dieser Regel in der Reihenfolge an.",
    "TOOLTIP_POLICY_FIREWALL_RULE_STATUS": "Eine aktivierte Regel wird aktiv durchgesetzt. Eine deaktivierte Regel wird nicht aktiv durchgesetzt, verliert aber nicht ihren Platz in der Regelreihenfolge. Der Dienst überspringt sie und fährt mit der nächsten Regel fort.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER_WARNING": "Diese Regel wird automatisch erstellt, um Datenverkehr an ZPA umzuleiten. Es wird empfohlen, die vordefinierte DNS-Regel für ZPA an Position 1 oder 2 zu verschieben.",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER": "Diese Regel wird automatisch erstellt, um Datenverkehr an ZPA umzuleiten.",
    "TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS": "Wählen Sie eine beliebige Anzahl von Quell-IP-Adressgruppen aus. Wenn keine Quell-IP-Adressgruppe ausgewählt ist, gilt die Regel für alle Quell-IP-Adressgruppen.",
    "TOOLTIP_POLICY_FIREWALL_UCAAS": "Diese Regel wird automatisch erstellt, wenn Sie für eine oder alle UCaaS-Apps festlegen, dass sie Datenverkehr von der Cloud Firewall zulassen",
    "TOOLTIP_PORT_NO": "Wählen Sie eine Portnummer aus der Dropdownliste aus.",
    "TOOLTIP_PRIMARY_DNS_SERVER": "Geben Sie die IP-Adresse des primären DNS-Servers ein.",
    "TOOLTIP_PRIMARY_PROXY": "Wählen Sie den primären Proxy für das Gateway aus.",
    "TOOLTIP_PROV_TEMPLATE_NAME": "Geben Sie einen Namen für die Cloud-Bereitstellungsvorlage ein.",
    "TOOLTIP_REGION": "Wählen Sie die Regionen aus, in denen Zscaler Tags erkennen soll. Zscaler erkennt Tags auf regionaler Ebene.",
    "TOOLTIP_SECONDARY_DNS_SERVER": "Geben Sie die IP-Adresse des sekundären DNS-Servers ein.",
    "TOOLTIP_SECONDARY_PROXY": "Wählen Sie den sekundären Proxy für das Gateway aus. Dieser wird verwendet, wenn der primäre Proxy nicht erreichbar ist.",
    "TOOLTIP_SESSIONS_ACROSS_SERVICES": "Gesamtzahl der Sitzungen, die in den letzten 24 Stunden über alle Cloud/Branch Connectors protokolliert wurden",
    "TOOLTIP_SHUTDOWN": "Wählen Sie „Ja“ oder „Nein“, um das Verhalten beim Herunterfahren festzulegen.",
    "TOOLTIP_SOURCE_IP_ADDRESSES": "Geben Sie eine beliebige Anzahl von IP-Adressen ein. Sie können Folgendes eingeben:{0} Eine IP-Adresse (**************){1} Einen Bereich von IP-Adressen (*********-**********){2} Eine IP-Adresse mit einer Netzmaske (***********/24){3} Drücken Sie die {4}Eingabetaste{5} nach jedem Eintrag.",
    "TOOLTIP_SOURCE_IP_GROUP_NAME": "Der Name der Quell-IP-Adressgruppe. Beispiel: Social Media. Das Gruppieren von Quell-IP-Adressen vereinfacht das Referenzieren in Firewall-Richtlinien.",
    "TOOLTIP_SOURCE_IP_NAME": "Quell-IP-Name",
    "TOOLTIP_STATIC_LEASE": "Geben Sie die Mac-Adresse und die IP-Adresse für DHCP ein.",
    "TOOLTIP_STATIC_ROUTE": "Geben Sie die Details für Route und Gateway ein.",
    "TOOLTIP_SUBSCRIPTIONS": "Wählen Sie die Abonnements aus, die Sie gruppieren möchten.",
    "TOOLTIP_TEMPLATE_PREFIX": "Geben Sie das Präfix für den Namen Ihrer Standortvorlage ein.",
    "TOOLTIP_THROUGHPUT_ACROSS_DIRECT": "Durchschnittliche Durchsatzauslastung durch direkten Datenverkehr in kbit/s \n\nGesamtzahl der protokollierten Sitzungen pro Cloud/Branch Connector in den letzten 24 Stunden",
    "TOOLTIP_THROUGHPUT_ACROSS_SERVICES": "Durchschnittliche Durchsatzauslastung des Datenverkehrs über alle Cloud/Branch Connectors in den letzten 24 Stunden",
    "TOOLTIP_THROUGHPUT_ACROSS_ZIA": "Durchschnittliche Durchsatzauslastung durch direkten Datenverkehr in kbit/s \n\nGesamtzahl der protokollierten Sitzungen pro Cloud/Branch Connector in den letzten 24 Stunden",
    "TOOLTIP_THROUGHPUT_ACROSS_ZPA": "Durchschnittliche Durchsatzauslastung durch ZPA-Verkehr in kbit/s \n\nGesamtzahl der protokollierten Sitzungen pro Cloud/Branch Connector in den letzten 24 Stunden",
    "TOOLTIP_TRAFFIC_DISTRIBUTION": "Wählen Sie „Ausgewogen“ oder „Beste Verbindung“, um festzulegen, wie der Traffic verteilt wird.",
    "TOOLTIP_UPGRADE_WINDOW": "Die Upgrades werden gestaffelt durchgeführt, ohne dass sich dies auf den Dienst auswirkt",
    "TOOLTIP_USE_WAN_DNS_SERVER": "Wählen Sie „Ja“, um den WAN-DNS-Server zu verwenden, oder „Nein“, um die LAN-DNS-Serverdetails manuell einzugeben. ",
    "TOOLTIP_VDI_AGENT_DESCRIPTION": "Geben Sie beschreibende Informationen, um den Agent leichter identifizieren zu können.",
    "TOOLTIP_VDI_AGENT_PROFILE_NAME": "Geben Sie einen Namen für das VDI-Profil, das Sie hinzufügen wollen.",
    "TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE": "Wählen Sie entweder IdP oder „Gehostete DB“ als Authentifizierungstyp aus.",
    "TOOLTIP_VDI_FORWRDING_PROFILE_IPS": "Verwenden Sie diesen Filter, um die Protokolle basierend auf der privaten IP-Adresse eines Clients einzuschränken. Sie können Folgendes eingeben: {0}IP-Adresse, z. B. ************** {1}IP-Adresse:Port-Bereich, z. B. *********:80 oder ********* für alle Bereiche {3}IP-Adresse:Port:Protokoll, z. B. *********:80:TCP, *********:80-100:UDP oder *********:80 für alle Protokolle {4}Drücken Sie nach jedem Eintrag die Eingabetaste.",
    "TOOLTIP_VDI_GROUP_DESCRIPTION": "Geben Sie beschreibende Informationen ein, um die Gruppe und ihren Zweck zu identifizieren",
    "TOOLTIP_VDI_GROUP_NAME": "Der Name der VDI-Gruppe.",
    "TOOLTIP_VDI_HOSTNAME_PREFIX": "Das Hostnamenpräfix, das zum Gruppieren der VDI-Geräte verwendet wird. Dies ist der von Zscaler Client Connector entdeckte Hostname für VDI.",
    "TOOLTIP_VDI_LOCATION": "Der Zscaler-Standort, der mit dieser VDI-Gruppe verknüpft werden soll. Dies ist der gleiche Standort wie die Cloud.",
    "TOOLTIP_VDI_MTU": "Dies ist der MTU-Wert (Maximum Transmission Unit) für die Geräte in der VDI-Gruppe. Der Standardwert ist 1400. Wenn dieser Wert nicht richtig eingestellt ist, kann dies die Netzwerkperformance des VDI-Agent beeinträchtigen. Achten Sie darauf, Geräte mit demselben MTU-Wert in einer VDI-Gruppe zusammenzufassen.",
    "TOOLTIP_VDI_OS_TYPE": "Der Typ des Betriebssystems der VDI-Geräte, die dieser VDI-Gruppe zugeordnet werden sollen. Es wird der von Zscaler Client Connector für VDI entdeckte Betriebssystemtyp verwendet, um ein Gerät zur Gruppe hinzuzufügen.",
    "TOOLTIP_VDI_TEMPLATE_IDP_NAME": "Suchen Sie nach einem IdP-Namen oder wählen Sie einen aus.",
    "TOOLTIP_VDI_TEMPLATE_NAME": "Einen Namen für die VDI-Vorlage eingeben.",
    "TOOLTIP_VDI_TEMPLATE_SYSTEM_USER": "Suchen Sie nach einem System-User oder wählen Sie einen aus.",
    "TOOLTIP_VDI_ZPA_USER_TUNNEL_FALLBACK": "Konnektoren erstellen für jeden VDI-User einen User-Tunnel zu ZPA bis zu der hier angegebenen Anzahl von User-Tunneln. Wenn die Anzahl der VDI-ZPA-User-Tunnel eines Konnektors diese Anzahl überschreitet, werden alle nachfolgenden Transaktionen über den Konnektor-basierten Tunnel an ZPA weitergeleitet. Der User wird als Cloud Connector-Gruppe in diesem Konnektor-basierten Tunnel erkannt.",
    "TOOLTIP_WAN_SELECTION": "Die WAN-Auswahl bestimmt, wie Traffic über mehrere WAN-Verbindungen weitergeleitet wird. Bei der Einstellung „Ausgewogen“ wird der Traffic gleichmäßig verteilt. Wenn „Beste Verbindung“ eingestellt ist, wird der Traffic immer über die WAN-Verbindung mit der besten Leistung weitergeleitet.",
    "TOOLTIP_ZIA_TUNNEL_MODEL": "Der Verschlüsselungstyp, der beim Erstellen eines Tunnels zu ZIA verwendet werden soll.",
    "TOPIC_STATUS": "Status des Themas",
    "TOTAL_CC_DEPLOYED": "Bereitgestellte Cloud Connectors gesamt",
    "TOTAL_DEPLOYED": "Bereitgestellt gesamt",
    "TOTAL_ENTITLED": "Berechtigt gesamt",
    "TOTAL_LATENCY": "Latenz gesamt",
    "TOTAL_TRAFFIC": "Verkehr gesamt",
    "TOTAL_TRANSACTIONS": "Transaktionen gesamt",
    "TOTAL": "Gesamt",
    "TRACE": "Traceroute",
    "TRACEROUTE_DESC": "Trace Route ist ein Dienstprogramm, um den Weg auf einen bestimmten Host aufzuzeigen.",
    "TRACEROUTE": "Traceroute",
    "TRADING_BROKARAGE_INSURANCE": "Online-Handel, Maklertätigkeit, Versicherungen",
    "TRADITIONAL_RELIGION": "Traditionelle Religion",
    "TRAFFIC_DIRECTON": "Anfrage-Typ",
    "TRAFFIC_DISTRIBUTION": "Traffic-Verteilung",
    "TRAFFIC_FLOW": "Verkehrsfluss",
    "TRAFFIC_FORWARDING_METHOD": "Weiterleitungsmethode",
    "TRAFFIC_FORWARDING_RESOURCE": "Verkehrsweiterleitungsressource",
    "TRAFFIC_FORWARDING": "Weiterleitung des Datenverkehrs",
    "TRAFFIC_FORWRDING_DNS": "Weiterleitung (Verkehr, DNS und Protokolle)",
    "TRAFFIC_MONITORING": "Verkehrsüberwachung",
    "TRAFFIC_OVERVIEW": "Verkehr - Überblick",
    "TRAFFIC_TEST": "Traffic-Test",
    "TRAFFIC_TREND": "Verkehrstrend",
    "TRAFFIC_TYPE": "Verkehrsart",
    "TRANSACTIONS": "Transaktionen",
    "TRANSLATORS": "Sonstiges - Informatik",
    "TRAVEL": "Reisen",
    "TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN": "Amerika/Port of Spain",
    "TRINIDAD_AND_TOBAGO": "Trinidad und Tobago",
    "TROUBLESHOOTING_LOGGING": "Fehlerbehebung zur Protokollierung",
    "TRUE": "Wahr",
    "TRUSTED_ACCOUNT_ID": "Vertrauenswürdige Konto-ID",
    "TRUSTED_ROLE": "Vertrauenswürdige Rolle",
    "TS_DIRECTON": "TS-Richtung",
    "TUESDAY": "Dienstag",
    "TUNISIA_AFRICA_TUNIS": "Afrika / Tunis",
    "TUNISIA": "Tunesien",
    "TUNNEL_AUTH_ALGORITHM": "Authentifizierungs-Algorithmus",
    "TUNNEL_AUTH_TYPE": "Art der Authentifizierung",
    "TUNNEL_DEAD_PEER_DETECTION": "Keepalive-Pakete",
    "TUNNEL_DESTINATION_IP_END": "P2 Richtlinien-Ziel-IP - Ende",
    "TUNNEL_DESTINATION_IP_START": "P2 Richtlinien-Ziel-IP - Start",
    "TUNNEL_DESTINATION_IP": "Tunnel-Ziel-IP",
    "TUNNEL_DESTINATION_PORT_END": "P2 Richtlinien-Zielport - Ende",
    "TUNNEL_ENCRYPTION_ALGORITHM": "Verschlüsselungs-Algorithmus",
    "TUNNEL_EVENT_REASON": "Ereignisursache",
    "TUNNEL_INFORMATION": "Tunnelinformationen",
    "TUNNEL_INITIATOR_COOKIE": "Initiator-Cookie",
    "TUNNEL_INSIGHTS": "Tunnel-Einblicke",
    "TUNNEL_IP": "Tunnel-IP",
    "TUNNEL_IPSEC_PHASE2_SPI": "IKE Phase 2 SPI",
    "TUNNEL_LIFEBYTES": "Bytes Lebensdauer",
    "TUNNEL_LIFETIME": "Tunnel-Lebensdauer",
    "TUNNEL_LOG_TYPE": "LOG-TYP",
    "TUNNEL_LOGS": "Tunnel-Protokolle",
    "TUNNEL_POLICY_DIRECTION": "Richtlinienrichtung",
    "TUNNEL_PROTOCOL_NAME": "P2-Richtlinienprotokoll",
    "TUNNEL_PROTOCOL": "IPSec-Protokoll",
    "TUNNEL_RECEIVED_PACKETS": "Empfangene Pakete",
    "TUNNEL_RESPONDER_COOKIE": "Responder-Cookie",
    "TUNNEL_SENT_PACKETS": "Gesendete Pakete",
    "TUNNEL_SOURCE_IP_END": "P2 Richtlinien-Quell-IP - Ende",
    "TUNNEL_SOURCE_IP_START": "P2 Richtlinien-Quell-IP - Start",
    "TUNNEL_SOURCE_IP": "Tunnel-Quell-IP",
    "TUNNEL_SOURCE_PORT_START": "P2 Richtlinien-Quellport - Start",
    "TUNNEL_STATUS": "Tunnel-Status",
    "TUNNEL_TYPE": "Tunneltyp",
    "TUNNEL_VENDOR_ID": "Anbieter-ID",
    "TUNNEL_VPN_CREDENTIAL": "VPN-Zugangsdaten",
    "TURKEY_EUROPE_ISTANBUL": "Europa / Istanbul",
    "TURKEY": "Türkei",
    "TURKMENISTAN_ASIA_ASHGABAT": "Asien/Ashgabat",
    "TURKMENISTAN": "Turkmenistan",
    "TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK": "Amerika/Grand Turk",
    "TURKS_AND_CAICOS_ISLANDS": "Turks und Caicos Inseln",
    "TUVALU_PACIFIC_FUNAFUTI": "Pacific/Funafuti",
    "TUVALU": "Tuvalu",
    "TWO_CLOUD_CONNECTOR_TEMPLATE": "Add-on-Vorlage mit hoher Verfügbarkeit",
    "TX_BYTES": "Gesendete Bytes",
    "TX_PACKETS": "Gesendete Pakete",
    "TX_RX_BYTES": " TX | RX Bytes",
    "TX_RX_PACKETS": " TX | RX Pakete",
    "TYPE_ACCOUNT_ID": "Konto-ID eingeben",
    "TYPE_ACCOUNT_NAME": "Kontonamen eingeben",
    "TYPE_APPLICATION_ID": "Anwendungs-ID eingeben",
    "TYPE_APPLICATION_KEY": "Anwendungsschlüssel eingeben",
    "TYPE_AWS_ACCESS_KEY_ID": "AWS-Zugriffsschlüssel-ID eingeben",
    "TYPE_AWS_SECRET_ACCESS_KEY": "AWS-Geheimschlüssel eingeben",
    "TYPE_BASE_URL": "Basis-URL eingeben",
    "TYPE_CLIENT_SECRET": "Client-Geheimschlüssel eingeben",
    "TYPE_DESCRIPTION_HERE": "Beschreibung hier eingeben",
    "TYPE_DOWNLOAD_MBPS": "Download (Mbps) eingeben",
    "TYPE_DSTN_IP_NAME": "Ziel-IP-Namen eingeben",
    "TYPE_FEED_ESCAPE_CHARACTER": "Text eingeben",
    "TYPE_GATEWAY_NAME": "Gateway-Namen hier eingeben",
    "TYPE_GROUP_NAME_HERE": "Gruppennamen hier eingeben",
    "TYPE_IP_ADDRESS_HERE": "IP-Adresse hier eingeben",
    "TYPE_IP_ADDRESSESS_HERE": "IP-Adresse eingeben",
    "TYPE_IP_POOL_NAME": "IP-Poolnamen eingeben",
    "TYPE_KEY": "Schlüssel eingeben",
    "TYPE_LOCATION_TEMPLATE_NAME": "Standortvorlagennamen eingeben",
    "TYPE_NSS_CLOUD_FEED_API_URL": "API-URL eingeben",
    "TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Autorisierungs-URL eingeben",
    "TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID": "Zugriffsschlüssel-ID eingeben",
    "TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY": "Geheimschlüssel eingeben",
    "TYPE_NSS_CLOUD_FEED_CLIENT_ID": "Anwendungsclient-ID eingeben",
    "TYPE_NSS_CLOUD_FEED_CLIENT_SECRET": "Anwendungsclient-Geheimschlüssel eingeben",
    "TYPE_NSS_CLOUD_FEED_GRANT_TYPE": "client_credentials eingeben",
    "TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "Text eingeben",
    "TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL": "S3-Ordner-URL eingeben",
    "TYPE_NSS_CLOUD_FEED_SCOPE": "https://monitor.azure.com//.default eingeben",
    "TYPE_NSS_FEED_NAME": "Text eingeben",
    "TYPE_NSS_SERVER_NAME": "Text eingeben",
    "TYPE_SIEM_FQDN": "Text eingeben",
    "TYPE_SIEM_IP_ADDRESS": "Text eingeben",
    "TYPE_SIEM_RATE_LIMIT": "Text eingeben",
    "TYPE_SIEM_TCP_PORT": "Text eingeben",
    "TYPE_SOURCE_IP_NAME": "Quell-IP-Gruppennamen eingeben",
    "TYPE_SUBSCRIPTION_ID": "Abonnement-ID eingeben",
    "TYPE_TEMPLATE_PREFIX": "Vorlagenpräfix eingeben",
    "TYPE_TENANT_ID": "Mandanten-ID eingeben",
    "TYPE_UPLOAD_MBPS": "Upload (Mbps) eingeben",
    "TYPE_VALUE": "Wert eingeben",
    "TYPE_ZS_TAG_OPTIONAL": "ZS-Tag eingeben (optional)",
    "TYPE": "Typ",
    "UAE": "Vereinigte Arabische Emirate",
    "UAECENTRAL": "(Mittlerer Osten) VAE Mitte",
    "UAENORTH": "(Mittlerer Osten) VAE Nord",
    "UBUNTU_LINUX": "ubuntu LINUX",
    "UDP_ANY_DESC": "UDP (User Datagram Protocol) ist eines der Kernelemente der Internet Protokoll Suite (Netzwerkprotokolle für das Internet).",
    "UDP_ANY": "UDP",
    "UDP_DESC": " UDP (User Datagram Protocol) ist eines der Kernelemente der Internet-Protokoll-Suite (das Set von Netzwerkprotokollen  für das Internet)",
    "UDP_DEST_PORTS": "UDP-Zielports",
    "UDP_PORTS": "UDP-Ports",
    "UDP_SRC_PORTS": "UDP-Quellports",
    "UDP_UNKNOWN_DESC": " Dies identifiziert UDP-Proxy / Firewall-Traffic, für den keine granularere App ermittelt werden kann",
    "UDP_UNKNOWN": "UDP Unbekannt",
    "UDP": "UDP",
    "UGANDA_AFRICA_KAMPALA": "Afrika / Kampala",
    "UGANDA": "Uganda",
    "UK": "Vereinigtes Königreich",
    "UKRAINE_EUROPE_KIEV": "Europa / Kiew",
    "UKRAINE_EUROPE_SIMFEROPOL": "Europa / Simferopol",
    "UKRAINE_EUROPE_UZHGOROD": "Europa / Uzhgorod",
    "UKRAINE_EUROPE_ZAPOROZHYE": "Europa / Zaporozhye",
    "UKRAINE": "Ukraine",
    "UKSOUTH": "(Europa) Großbritannien Süd",
    "UKWEST": "(Europa) Großbritannien West",
    "UNABLE_TO_LOGIN_TRY_AGAIN": "Anmeldung nicht möglich. Bitte versuchen Sie es später noch einmal",
    "UNAUTHORIZED_COMMUNICATION": "Nicht autorisierte Kommunikation",
    "UNENCRYPTED": "Unverschlüsselt",
    "UNEXPECTED_ERROR": "Ein unerwarteter Fehler ist aufgetreten",
    "UNHEALTHY": "Ungesund",
    "UNITED_ARAB_EMIRATES_ASIA_DUBAI": "Asien/Dubai",
    "UNITED_ARAB_EMIRATES": "Vereinigte Arabische Emirate",
    "UNITED_KINGDOM_EUROPE_LONDON": "Europa / London",
    "UNITED_KINGDOM": "Vereinigtes Königreich",
    "UNITED_STATES_AMERICA_ADAK": "Amerika/Adak",
    "UNITED_STATES_AMERICA_ANCHORAGE": "Amerika/Anchorage",
    "UNITED_STATES_AMERICA_BOISE": "Amerika/Boise",
    "UNITED_STATES_AMERICA_CHICAGO": "Amerika/Chicago",
    "UNITED_STATES_AMERICA_DENVER": "Amerika/Denver",
    "UNITED_STATES_AMERICA_DETROIT": "Amerika/Detroit",
    "UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS": "Amerika/Indiana/Indianapolis",
    "UNITED_STATES_AMERICA_INDIANA_KNOX": "Amerika/Indiana/Knox",
    "UNITED_STATES_AMERICA_INDIANA_MARENGO": "Amerika/Indiana/Marengo",
    "UNITED_STATES_AMERICA_INDIANA_PETERSBURG": "Amerika/Indiana/Petersburg",
    "UNITED_STATES_AMERICA_INDIANA_TELL_CITY": "Amerika/Indiana/Tell City",
    "UNITED_STATES_AMERICA_INDIANA_VEVAY": "Amerika/Indiana/Vevay",
    "UNITED_STATES_AMERICA_INDIANA_VINCENNES": "Amerika/Indiana/Vincennes",
    "UNITED_STATES_AMERICA_INDIANA_WINAMAC": "Amerika/Indiana/Winamac",
    "UNITED_STATES_AMERICA_JUNEAU": "Amerika/Juneau",
    "UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE": "Amerika/Kentucky/Louisville",
    "UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO": "Amerika/Kentucky/Monticello",
    "UNITED_STATES_AMERICA_LOS_ANGELES": "Amerika/Los Angeles",
    "UNITED_STATES_AMERICA_MENOMINEE": "Amerika/Menominee",
    "UNITED_STATES_AMERICA_NEW_YORK": "Amerika/New York",
    "UNITED_STATES_AMERICA_NOME": "Amerika/Nome",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER": "Amerika/North Dakota/Center",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM": "Amerika/North Dakota/New Salem",
    "UNITED_STATES_AMERICA_PHOENIX": "Amerika/Phoenix",
    "UNITED_STATES_AMERICA_SHIPROCK": "Amerika/Shiprock",
    "UNITED_STATES_AMERICA_YAKUTAT": "Amerika/Yakutat",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON": "Pacific/Johnston",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY": "Pacific/Midway",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE": "Pacific/Wake",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS": "Amerikanisch-Ozeanien",
    "UNITED_STATES_PACIFIC_HONOLULU": "Pacific/Honolulu",
    "UNITED_STATES": "Vereinigte Staaten von Amerika",
    "UNITEDSTATES": "Vereinigte Staaten von Amerika",
    "UNITEDSTATESEUAP": "Vereinigte Staaten EUAP",
    "UNITS": "Einheiten",
    "UNKNOWN_ERROR_CODE": "Unbekannter Fehlercode",
    "UNKNOWN_HOPS": "Unbekannte Hops",
    "UNLIMITED": "Unbegrenzt",
    "UNREGISTERED": "Nicht registriert",
    "UNSELECTED_ITEMS": "Nicht ausgewähltes Element",
    "UNSELECTED": "Nicht ausgewählt",
    "UNTAGGED": "Nicht markiert",
    "UP": "Aufwärts",
    "UPDATE": "Update",
    "UPDATED": "Aktualisiert",
    "UPF_IP_CIDR": "IP/CDR der Benutzerebenenfunktion",
    "UPF_NAME": "Name der Benutzerebenenfunktion",
    "UPGRADE_ON": "Upgrade am",
    "UPGRADE_SCHEDULE": "Upgrade-Zeitplan",
    "UPGRADE_STATUS": "Upgrade-Status",
    "UPGRADE_WILL_BE_SCHEDULED": "Upgrades werden gemäß der lokalen Zeitzone des Cloud Connectors geplant",
    "UPGRADE_WINDOW": "Upgrade-Fenster",
    "UPLINK_MODE": "Uplink-Modus",
    "UPLOAD_MBPS": "Upload (Mbps)",
    "UPLOAD": "Upload",
    "UPTIME": "Betriebszeit",
    "URL_LOOKUP": "URL-Lookup",
    "URUGUAY_AMERICA_MONTEVIDEO": "Amerika/Montevideo",
    "URUGUAY": "Uruguay",
    "US_CENTRAL1_A": "us-central1-a",
    "US_CENTRAL1_B": "us-central1-b",
    "US_CENTRAL1_C": "us-central1-c",
    "US_CENTRAL1_F": "us-central1-f",
    "US_CENTRAL1": "us-central1",
    "US_EAST_1": "us-east-1 (N. Virginia)",
    "US_EAST_1A": "us-east-1a",
    "US_EAST_1B": "us-east-1b",
    "US_EAST_1C": "us-east-1c",
    "US_EAST_1D": "us-east-1d",
    "US_EAST_1E": "us-east-1e",
    "US_EAST_1F": "us-east-1f",
    "US_EAST_2": "us-east-2 (Ohio)",
    "US_EAST_2A": "us-east-2a",
    "US_EAST_2B": "us-east-2b",
    "US_EAST_2C": "us-east-2c",
    "US_EAST1_B": "us-east1-b",
    "US_EAST1_C": "us-east1-c",
    "US_EAST1_D": "us-east1-d",
    "US_EAST1": "us-east1",
    "US_EAST4_A": "us-east4-a",
    "US_EAST4_B": "us-east4-b",
    "US_EAST4_C": "us-east4-c",
    "US_EAST4": "us-east4",
    "US_EAST5_A": "us-east5-a",
    "US_EAST5_B": "us-east5-b",
    "US_EAST5_C": "us-east5-c",
    "US_EAST5": "us-east5",
    "US_GOV_EAST_1": "AWS GovCloud (USA Ost)",
    "US_GOV_EAST_1A": "us-gov-east-1a",
    "US_GOV_EAST_1B": "us-gov-east-1b",
    "US_GOV_EAST_1C": "us-gov-east-1c",
    "US_GOV_WEST_1": "AWS GovCloud (USA West)",
    "US_GOV_WEST_1A": "us-gov-west-1a",
    "US_GOV_WEST_1B": "us-gov-west-1b",
    "US_GOV_WEST_1C": "us-gov-west-1c",
    "US_OUTLYING_ISLANDS": "U.S. Outlying Islands",
    "US_SOUTH1_A": "us-south1-a",
    "US_SOUTH1_B": "us-south1-b",
    "US_SOUTH1_C": "us-south1-c",
    "US_SOUTH1": "us-south1",
    "US_VIRGIN_ISLANDS": "Amerikanische Jungferninseln",
    "US_WEST_1": "us-west-1 (N. California)",
    "US_WEST_1A": "us-west-1a",
    "US_WEST_1B": "us-west-1b",
    "US_WEST_1C": "us-west-1c",
    "US_WEST_2_LAX_1A": "us-west-2-lax-1a",
    "US_WEST_2": "us-west-2 (Oregon)",
    "US_WEST_2A": "us-west-2a",
    "US_WEST_2B": "us-west-2b",
    "US_WEST_2C": "us-west-2c",
    "US_WEST_2D": "us-west-2d",
    "US_WEST1_A": "us-west1-a",
    "US_WEST1_B": "us-west1-b",
    "US_WEST1_C": "us-west1-c",
    "US_WEST1": "us-west1",
    "US_WEST2_A": "us-west2-a",
    "US_WEST2_B": "us-west2-b",
    "US_WEST2_C": "us-west2-c",
    "US_WEST2": "us-west2",
    "US_WEST3_A": "us-west3-a",
    "US_WEST3_B": "us-west3-b",
    "US_WEST3_C": "us-west3-c",
    "US_WEST3": "us-west3",
    "US_WEST4_A": "us-west4-a",
    "US_WEST4_B": "us-west4-b",
    "US_WEST4_C": "us-west4-c",
    "US_WEST4": "us-west4",
    "USDODCENTRAL": "US-Verteidigungsministerium, Mitte",
    "USDODEAST": "US-Verteidigungsministerium, Ost",
    "USE_WAN_DNS_SERVER": "WAN-DNS-Server verwenden",
    "USE_WAN_DNS_SEVER": "WAN-DNS-Server verwenden",
    "USER_ACCOUNT_LOCKED": "Ihr Konto wurde aufgrund von zu vielen Anmeldefehlern vorübergehend gesperrt. Versuchen Sie es später noch einmal.",
    "USER_DEFINED_TAGS": "Benutzerdefinierte Tags",
    "USER_DEFINED": "Benutzerdefiniert",
    "USER_ID": "User-ID",
    "USER_MANAGEMENT": "User-Management",
    "USER_NAME_VISIBILITY": "Benutzernamen-Sichtbarkeit",
    "USER_NAME": "Angezeigter User-Name",
    "USER_NAMES": "User-Namen",
    "USER_PLANE_FUNCTION": "Benutzerebenen-Funktion",
    "USER": "Benutzer",
    "USERNAMES": "User-Namen",
    "USGOVARIZONA": "USGov Arizona",
    "USGOVIOWA": "USGov Iowa",
    "USGOVTEXAS": "USGov Texas",
    "USGOVVIRGINIA": "USGov Virginia",
    "USSECEAST": "US SEC Ost",
    "USSECWEST": "US SEC West",
    "USSECWESTCENTRAL": "US SEC Mitte",
    "UZBEKISTAN_ASIA_SAMARKAND": "Asien/Samarkand",
    "UZBEKISTAN_ASIA_TASHKENT": "Asien/Tschkent",
    "UZBEKISTAN": "Usbekistan",
    "VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE": "Es sind bis zu {0} Elemente erlaubt.",
    "VALIDATION_ERROR_EMPTY_PROTOCOL": "Geben Sie ein Protokoll ein.",
    "VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS": "Ein oder mehrere Elemente in dieser Liste sind ungültig.",
    "VALIDATION_ERROR_INVALID_AWS_ROLENAME": "Geben Sie einen gültigen AWS-Rollennamen ein. Dieser darf nur alphanumerische Zeichen und +=,.@- enthalten.",
    "VALIDATION_ERROR_INVALID_DOMAIN": "Geben Sie einen gültigen Domänennamen ein.",
    "VALIDATION_ERROR_INVALID_END_PORT_RANGE": "Der End-Port muss zwischen 1 und 65535 liegen und größer als der Start-Port sein.",
    "VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS": "Geben Sie einen gültigen FQDN, eine IP-Adresse, einen IP-Adressbereich oder einen IP-CIDR-Block ein.",
    "VALIDATION_ERROR_INVALID_IP_ADDRESS": "Geben Sie eine gültige IP-Adresse, einen IP-Adressbereich oder einen IP-CIDR-Block ein.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK_IS_BROADCAST": "Bitte geben Sie eine gültige IP-Adresse ein, dies ist eine Broadcast-IP für das Subnetz.",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK": "Stellen Sie sicher, dass IP-Adressen sich im selben Subnetz befinden.",
    "VALIDATION_ERROR_INVALID_IP_PORT": "Geben Sie einen gültigen TCP-Port ein (0-65535)",
    "VALIDATION_ERROR_INVALID_IP_WITH_CIDR": "Geben Sie gültige IP-Adresse mit CIDR ein.",
    "VALIDATION_ERROR_INVALID_IP": "Geben Sie eine gültige IP ein",
    "VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS": "Geben Sie einen gültigen URL ohne das Präfix http://oder https://ein. Der URL sollte mindestens das Muster Host.Domäne aufweisen, um sich zu qualifizieren.",
    "VALIDATION_ERROR_INVALID_MAC_ADDRESS": "Geben Sie eine gültige MAC-Adresse ein.",
    "VALIDATION_ERROR_INVALID_NAME": "Geben Sie einen gültigen Namen ein",
    "VALIDATION_ERROR_INVALID_PORT_STRING": "Geben Sie eine gültige Portnummer oder einen gültigen Portnummernbereich ein (z. B. 587, 1-65535).",
    "VALIDATION_ERROR_INVALID_PROTOCOL": "Geben Sie ein gültiges Protokoll ein.",
    "VALIDATION_ERROR_INVALID_SECONDARY_FIELD": "Sekundäres Feld (Manuell) darf nicht leer sein!",
    "VALIDATION_ERROR_INVALID_SERVICE_IP_MASK": "Alle Dienst-IPs sollten dieselbe Subnetzmaske haben.",
    "VALIDATION_ERROR_INVALID_START_PORT_RANGE": "Portnummern müssen zwischen 1 und 65535 liegen.",
    "VALIDATION_ERROR_INVALID_URL": "Geben Sie einen gültigen URL mit dem Präfix http://oder https://ein.",
    "VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE": "Diese IP liegt in einem der folgenden ungültigen Bereiche. \n\n [0.0.0.0–*************]\n [**********–***************]\n [*********–***************]\n [***********–***************]\n [240.0.0.0–***************] ",
    "VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE": "Dieser Wert muss zwischen 128 KB und 1 MB liegen",
    "VALIDATION_ERROR_SAME_IP": "Die IP-Adressen müssen unterschiedlich sein.",
    "VALIDATION_ERROR_SAME_SERVICE_IP": "Die Dienst-IP-Adressen müssen unterschiedlich sein.",
    "VALIDATION_ERROR_SAME_START_END_PORT_RANGE": "Der Start-Port und der End-Port dürfen nicht identisch sein.",
    "VALIDATION_NETWORK_SERVICE_GROUP_NAME_REQUIRED": "Der Name der Netzwerkdienstgruppe darf nicht leer sein.",
    "VALIDATION_NETWORK_SERVICE_GROUP_SERIVCE_REQUIRED": "Es muss mindestens eine Art von Dienst angegeben werden.",
    "VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT": "Geben Sie mindestens einen TCP- oder UDP-Zielport ein. Quellports benötigen einen entsprechenden Zielport.",
    "VALIDATION_NETWORK_SERVICE_MIN_PORT_REQUIRED": "Es muss mindestens eine Art von Port angegeben werden.",
    "VALIDATION_NETWORK_SERVICE_NAME_REQUIRED": "Der Netzwerkdienstname darf nicht leer sein.",
    "VALUE": "Wert",
    "VANUATU_PACIFIC_EFATE": "Pacific/Efate",
    "VANUATU": "Vanuatu",
    "VATICAN_CITY_STATE_EUROPE_VATICAN": "Europa / Vatikan",
    "VATICAN_CITY_STATE": "Vatikan Staat",
    "VATICAN_CITY": "Vatikanstadt",
    "VDI_AGENT_APP": "VDI App Store",
    "VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT": "Platzhaltertext mit wenig Details…",
    "VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT": "Diese Einstellung wird von Zscaler Client Connector für VDI verwendet, um Traffic vom Tunnel zur Cloud oder zum Branch Connector ein- oder auszuschließen. In einigen Fällen müssen Einschluss- und Ausschlusslisten auch in Ihrem Cloud oder Branch Connector konfiguriert werden.",
    "VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT": "Geben Sie Text durch einen Zeilenumbruch (Eingabetaste) getrennt ein.",
    "VDI_AGENT_FORWARDING_PROFILE": "VDI-Weiterleitungsprofil",
    "VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT": "Diese Konfiguration wird für die Enduser-Authentifizierung und die dem User zugeordnete User-ID verwendet.",
    "VDI_AGENT_TEMPLATE": "VDI-Vorlage",
    "VDI_AGENT_TEMPLATES": "VDI-Vorlagen",
    "VDI_DEVICE_GENERAL_TEXT": "Geben Sie Informationen ein, die es erleichtern, eine bestimmte Gruppe zu identifizieren.",
    "VDI_DEVICE_MANAGEMENT": "VDI-Geräteverwaltung",
    "VDI_DEVICES": "VDI-Geräte",
    "VDI_FORWARDING_PROFILE_TEXT": "Das Weiterleitungsprofil, das dieser VDI-Gruppe zugeordnet werden soll. Die Einschluss- oder Ausschlussrichtlinien des gewählten Weiterleitungsprofils werden von Zscaler Client Connector für VDI angewendet und auf den in dieser VDI-Gruppe enthaltenen Geräten installiert.",
    "VDI_FORWARDING_PROFILE": "VDI-Weiterleitungsprofil",
    "VDI_GROUPS": "VDI-Gruppen",
    "VDI_MANAGEMENT": "VDI-Verwaltung",
    "VDI_REVIEW_TEXT": "Stellen Sie sicher, dass die nachfolgenden Informationen korrekt sind, bevor Sie diese Gruppe hinzufügen.",
    "VDI_ZPA_USER_TUNNEL_FALLBACK": "VDI ZPA User-Tunnel-Fallback",
    "VDO_LIVE_DESC": "VDOLive ist eine skalierbare Video Streaming Technologie",
    "VDO_LIVE": "VDOLive",
    "VEHICLES": "Fahrzeuge",
    "VENDOR": "Anbieter",
    "VENEZUELA_AMERICA_CARACAS": "Amerika/Caracas",
    "VENEZUELA": "Venezuela",
    "VERBOSE": "Ausführlich",
    "VERIFY_CURRENT_PASSWORD": "Aktuelles Passwort verifizieren",
    "VERSION_PROFILE": "Versionsprofil",
    "VERSION": "Version",
    "VIET_NAM_ASIA_SAIGON": "Asien/Saigon",
    "VIET_NAM": "Viet_nam",
    "VIETNAM": "Vietnam",
    "VIEW_5G_DEPLOYMENT": "Deployment-Konfiguration anzeigen",
    "VIEW_APPLIANCE": "Appliance anzeigen",
    "VIEW_AWS_ACCOUNT": "AWS-Konto anzeigen",
    "VIEW_AWS_GROUP": "AWS-Gruppe anzeigen",
    "VIEW_AZURE_TENANT": "Azure-Instanz anzeigen",
    "VIEW_BRANCH_PROVISIONING_TEMPLATE": "Branch Connector-Bereitstellungsvorlage anzeigen",
    "VIEW_CLOUD_CONNECTOR_ADMIN": "Cloud Connector-Administrator anzeigen",
    "VIEW_CLOUD_CONNECTOR_ROLE": "Cloud Connector-Rolle anzeigen",
    "VIEW_CLOUD_CONNECTORS": "Connectors anzeigen",
    "VIEW_CLOUD_NSS_FEED": "Cloud NSS-Feed anzeigen",
    "VIEW_CLOUD_PROVIDER_AWS": "AWS Cloud-Konto anzeigen",
    "VIEW_CLOUD_PROVIDER_AZURE": "Azure Cloud-Konto anzeigen",
    "VIEW_CONNECTORS": "Connectors anzeigen",
    "VIEW_DESTINATION_IP_GROUP": "Ziel-IP-Gruppe anzeigen",
    "VIEW_DNS_GATEWAYS": "DNS-Gateway anzeigen",
    "VIEW_DNS_POLICIES": "DNS-Filterregel anzeigen",
    "VIEW_DYNAMIC_VDI_GROUP": "Dynamische VDI-Gruppe anzeigen",
    "VIEW_GATEWAYS": "Gateways anzeigen",
    "VIEW_INFO": "Informationen anzeigen",
    "VIEW_IP_POOL_GROUP": "IP-Pool anzeigen",
    "VIEW_LOCATION_TEMPLATE": "Standortvorlage anzeigen",
    "VIEW_LOCATIONS": "Standorte anzeigen",
    "VIEW_LOG_AND_CONTROL_FORWARDING_RULE": "Protokollierungs- und Steuerungsregel für Weiterleitung anzeigen",
    "VIEW_NETWORK_SERVICE_GROUP": "Netzwerkdienstgruppe anzeigen",
    "VIEW_NETWORK_SERVICE": "Netzwerkdienst anzeigen",
    "VIEW_NSS_FEEDS": "NSS-Feed anzeigen",
    "VIEW_NSS_SERVER": "NSS-Server anzeigen",
    "VIEW_ONLY_ACCESS": "Schreibgeschützter Zugriff",
    "VIEW_ONLY_ENABLED_UNTIL": "Schreibgeschützter Zugriff aktiviert bis",
    "VIEW_ONLY": "Nur Ansehen",
    "VIEW_PHYSICAL_BRANCH_DEVICE": "Physisches Zweigstellengerät anzeigen",
    "VIEW_PROVISIONING_TEMPLATE": "Cloud Connector-Bereitstellungsvorlage anzeigen",
    "VIEW_SOURCE_IP_GROUP": "Quell-IP-Gruppe anzeigen",
    "VIEW_SUB_LOCATIONS": "Unterstandorte anzeigen",
    "VIEW_SUBLOCATIONS": "Unterstandorte anzeigen",
    "VIEW_TRAFFIC_FWD_POLICIES": "Verkehrsweiterleitungsregeln anzeigen",
    "VIEW_UPF": "Benutzerebenenfunktion anzeigen",
    "VIEW_VDI_AGENT_FORWARDING_PROFILE": "VDI-Weiterleitungsprofil anzeigen",
    "VIEW_VDI_TEMPLATE": "VDI-Vorlage anzeigen",
    "VIEW_VIRTUAL_BRANCH_DEVICE": "Virtuelles Zweigstellengerät anzeigen",
    "VIEW_ZERO_TRUST_GATEWAY": "Zero-Trust-Gateway anzeigen",
    "VIEW_ZT_DEVICE": "ZT-Gerät anzeigen",
    "VIEW": "Ansicht",
    "VIOLENCE": "Gewalt",
    "VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA": "Amerika/Tortola",
    "VIRGIN_ISLANDS_BRITISH": "Virgin Islands (Britisch)",
    "VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS": "Amerika/St. Thomas",
    "VIRGIN_ISLANDS_US": "Virgin Islands (USA)",
    "VIRTUAL_IP_ADDRESS": "Virtuelle IP-Adresse",
    "VIRTUAL_IP_AND_LAN_IP_MUST_BE_DIFFERENT": "Virtual IP und LAN-IP-Adresse müssen unterschiedlich sein.",
    "VIRTUAL_IP_AND_PEER_DHCP_MUST_BE_DIFFERENT": "Virtual IP und Peer-DHCP-Adresse müssen unterschiedlich sein.",
    "VIRTUAL_SERVICE_EDGE_ID": "Virtual Service Edge-ID",
    "VIRTUAL": "Virtuell",
    "VISIBLE": "Sichtbar",
    "VLAN_ID": "VLAN-ID",
    "VM_GBL_METRICS": "VM",
    "VM_HEALTH_FETCH_API_ERROR": "VM-Zustand kann gegenwärtig nicht abgerufen werden. Versuchen Sie es später erneut.",
    "VM_ID": "VM-ID",
    "VM_NAME": "VM-Name",
    "VM_SIZE": "VM-Größe",
    "VMWARE_ESXI": "VMware ESXi",
    "VMWARE": "VMWare",
    "VNC_DESC": "VNC (Virtual Network Computing)",
    "VNC": "VNC",
    "VPC_ID": "VPC-ID",
    "VPC_NAME": "VPC-Name",
    "VPC_VNET_NAME": "VPC/VNET-Name",
    "VPN_CREDENTIAL_DROPDOWN": "Dropdownliste für VPN-Anmeldeinformationen",
    "VPN_CREDENTIAL": "VPN-Zugangsdaten",
    "VPN_CREDENTIALS": "VPN-Zugangsdaten",
    "VSE_CLUSTERS": "Virtual Service Edge Clusters",
    "VSE_NODES": "Virtual Service Edges",
    "WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS": "Pacific/Wallis",
    "WALLIS_AND_FUTUNA_ISLANDS": "Wallis und Futuna Inseln",
    "WALLIS_AND_FUTUNA": "Wallis und Futuna",
    "WAN_DESTINATIONS_GROUP": "WAN-Zielgruppe",
    "WAN_NEED_AT_LEAST_ONE_ACTIVE_INTERFACE": "WAN benötigt mindestens eine aktive Schnittstelle.",
    "WAN_PRI_DNS": "Primärer WAN-DNS-Server",
    "WAN_SEC_DNS": "Sekundärer WAN-DNS-Server",
    "WAN_SELECTION": "WAN-Auswahl",
    "WAN": "WAN",
    "WEAPONS_AND_BOMBS": "Waffen/Bomben",
    "WEB_BANNERS": "Werbung",
    "WEB_CONFERENCING": "Web Conferencing",
    "WEB_HOST": "Web-Host",
    "WEB_SEARCH": "WebSearch",
    "WEB_SPAM": "Web Spam",
    "WEDNESDAY": "Mittwoch",
    "WESTCENTRALUS": "(USA) USA Mitte West",
    "WESTERN_SAHARA_AFRICA_EL_AAIUN": "Afrika / El Aaiún",
    "WESTERN_SAHARA": "West Sahara",
    "WESTEUROPE": "(Europa) Westeuropa",
    "WESTINDIA": "(Asien/Pazifikraum) Indien West",
    "WESTUS": "(USA) USA West",
    "WESTUS2": "(USA) USA West 2",
    "WESTUS2STAGE": "(USA) USA Ost 2 (Stufe)",
    "WESTUS3": "(USA) USA West 3",
    "WESTUSSTAGE": "(USA) USA Ost (Stufe)",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_PLACEHOLDER": "Client-/Anwendungs-ID eingeben",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_TOOLTIP": "Geben Sie die Client-/Anwendungs-ID ein.",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS": "Client-/Anwendungs-ID",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_PLACEHOLDER": "Client-/Anwendungs-Geheimschlüssel eingeben",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_TOOLTIP": "Geben Sie den Client-/Anwendungs-Geheimschlüssel ein.",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET": "Client-/Anwendungs-Geheimschlüssel",
    "WHAT_DO_YOU_CALL_THIS_TENANT_PLACEHOLDER": "Name für das Azure-Konto eingeben",
    "WHAT_DO_YOU_CALL_THIS_TENANT_TOOLTIP": "Geben Sie einen Namen ein, der zur Verwaltung Ihrer Konten verwendet wird.",
    "WHAT_DO_YOU_CALL_THIS_TENANT": "Kontoname",
    "WHAT_IS_THE_DIRECTORY_ID_PLACEHOLDER": "Verzeichnis-ID eingeben",
    "WHAT_IS_THE_DIRECTORY_ID_TOOLTIP": "Geben Sie die Verzeichnis-ID (Instanz-ID) ein, die dem Azure-Dienstprinzipal zugeordnet ist, der für das Onboarding des Azure-Kontos verwendet wird.",
    "WHAT_IS_THE_DIRECTORY_ID": "Verzeichnis-ID (Mandant)",
    "WHITESPACES_ARE_NOT_ALLOWED": "Leerzeichen sind nicht gestattet!",
    "WHOIS_DESC": "Network Directory Service Protocol ist ein Protokoll, mit dem von einem verteilten Datenbanksystem Informationen zu Internet-Domains und IP-Adressen und deren Eigentümern abgefragt werden kann.",
    "WHOIS": "WHOIS",
    "WINDOWS_OS": "Windows-Betriebssystem",
    "WORKLOAD_SERVICE_REQUIRED_MESSAGE": "Für Partnerintegrationen sind ein Abonnement erforderlich, über das Ihre Organisation gegenwärtig nicht verfügt. Kontaktieren Sie den Zscaler-Support, um das Basispaket ohne zusätzliche Kosten zu aktivieren. Über Partnerintegrationen kann Zscaler Dienste in der öffentlichen Cloud ausführen und verwalten, die mit Ihrem Konto oder Abonnement verbunden sind. Zscaler weist Ihr Konto basierend auf unserer Kapazität einen Dienst zu.",
    "WORKLOAD_SERVICE_REQUIRED": "Workload-Dienst muss konfiguriert werden",
    "WORKLOAD": "Workload-Verkehrstyp",
    "WORKLOADS": "Workloads",
    "XSS": "Cross-Site-Scripting",
    "YEMEN_ASIA_ADEN": "Asien / Aden",
    "YEMEN": "Jemen",
    "YES": "Ja",
    "YESKY_DESC": " Dieses Protokoll Plug-in klassifiziert den HTTP-Traffic  an den Host yesky.com",
    "YESKY": "Yesky",
    "YIHAODIAN_DESC": " Chinesisches Online-Shopping",
    "YIHAODIAN": "Yihaodian",
    "YMAIL_CLASSIC_DESC": " Yahoo Mail Classic war das ursprüngliche Interface für Yahoo! Mail",
    "YMAIL_CLASSIC": "Yahoo Mail Classic",
    "YMAIL_MOBILE_DESC": " (Auslaufend) Yahoo Mail (mobile) ist die yahoo.com-Webmail, die für Handys angepasst ist.",
    "YMAIL_MOBILE_NEW_DESC": " Yahoo Mail (Mobile) ist die neue Webmail von yahoo.com, die an Mobiltelefone angepasst ist",
    "YMAIL_MOBILE_NEW": "Yahoo Mail (Mobile)",
    "YMAIL_MOBILE": "Ymail (Mobile)",
    "YMAIL2_DESC": " Dieses Protokoll ist die Ajax-basierte Version von Webmail Yahoo",
    "YMAIL2": "Ymail2",
    "YMSG_CONF_DESC": " Dieses Protokoll wird für den Signalising-Teil einer Konferenz verwendet",
    "YMSG_CONF": "Yahoo Messenger Conference",
    "YMSG_DESC": " Yahoo Messenger wird von der Yahoo Instant Messenger-Applikation verwendet, um Instant Messages, Dateien und E-Mails zwischen Anwender zu senden",
    "YMSG_TRANSFER_DESC": " Dieses Protokoll wird für Dateiübertragungen  über YMSG verwendet",
    "YMSG_TRANSFER": "Yahoo Messenger Datei-Transfer",
    "YMSG_VIDEO_DESC": " (Versionen vor 10.0.0.270) Das Protokoll, das von Yahoo Messenger für Video-Gespräche verwendet wird",
    "YMSG_VIDEO": "Yahoo Messenger Video",
    "YMSG_WEBMESSENGER_DESC": " Yahoo Messenger for the Web",
    "YMSG_WEBMESSENGER": "Yahoo Messenger for the Web",
    "YMSG": "Yahoo Messenger",
    "YOU_DO_NOT_HAVE_THE_NECESSARY_PERMISSION": "Sie haben nicht die erforderlichen Rechte, um diese Seite anzuzeigen",
    "YOUR_ACCOUNT_INFORMATION_WAS_SAVED_BUT_SOME_REGIONS_FAILED": "Ihre Kontoinformationen wurden gespeichert, aber die folgenden Regionen konnten nicht gespeichert werden:",
    "ZAMBIA_AFRICA_LUSAKA": "Afrika / Lusaka",
    "ZAMBIA": "Zambia",
    "ZDX_UI": "ZDX-UI",
    "ZERO_TRUST_GATEWAY": "Zero-Trust-Gateway",
    "ZIA_GATEWAY": "ZIA-Gateway",
    "ZIA_GW_AUTH_FAIL": "Authentifizierung mit ZIA-Gateway fehlgeschlagen.",
    "ZIA_GW_CONN_SETUP_FAIL": "Verbindung zum ZIA-Gateway fehlgeschlagen (interner Fehler).",
    "ZIA_GW_CONNECT_FAIL": "Verbindung zum ZIA-Gateway fehlgeschlagen (Netzwerkfehler).",
    "ZIA_GW_CTL_CONN_CLOSE": "Aktive Verbindung zum ZIA-Gateway geschlossen.",
    "ZIA_GW_CTL_KA_FAIL": "Keepalive für Verbindung zum ZIA-Gateway fehlgeschlagen.",
    "ZIA_GW_DATA_CONN_CLOSE": "Aktive ZIA-Datenverbundung geschlossen.",
    "ZIA_GW_DATA_KA_FAIL": "Keepalive für ZIA-Datenverbindung fehlgeschlagen.",
    "ZIA_GW_DNS_RESOLVE_FAIL": "DNS-Auflösung für ZIA-Gateway fehlgeschlagen.",
    "ZIA_GW_PAC_RESOLVE_FAIL": "PAC-Auflösung für ZIA-Gateway fehlgeschlagen.",
    "ZIA_GW_PAC_RESOLVE_NOIP": "PAC-Auflösung für ZIA-Gateway lieferte keine IPS zurück.",
    "ZIA_GW_PROTO_MSG_ERROR": "Nachrichtenformatfehler im ZIA GW Steuerungs-/Datenkanal.",
    "ZIA_GW_PROTO_VER_ERROR": "ZIA-Protokoll-Versionskonflikt.",
    "ZIA_GW_SSL_ERROR": "SSL-Fehler im ZIA GW-Steuerung/Datenkanal.",
    "ZIA_GW_UNHEALTHY": "ZIA-Gateway ist fehlerhaft (transienter Zustand).",
    "ZIA_THROUGHPUT_KBPS_SESSION": "ZIA (Durchsatz kbit/s pro Sitzung)",
    "ZIA_TUNNEL_MODEL": "ZIA-Tunnelmodus",
    "ZIA_TUNNEL": "ZIA-Tunnel",
    "ZIA": "ZIA",
    "ZIMBABWE_AFRICA_HARARE": "Afrika / Harare",
    "ZIMBABWE": "Simbabwe",
    "ZONE": "Zone",
    "ZPA_BROKER": "ZPA Broker",
    "ZPA_EDGE_APP_SEGMENT": "ZPA Edge App Segment",
    "ZPA_IP_POOL": "ZPA-IP-Pool",
    "ZPA_POLICY_VIOLATION_INDICATOR": "ZPA-Richtlinienverstoß-Indikator",
    "ZPA_THROUGHPUT_KBPS_SESSION": "ZPA (Durchsatz kbit/s pro Sitzung)",
    "ZPA_TUNNEL": "ZPA-Tunnel",
    "ZPA": "ZPA",
    "ZS_TAG_OPTIONAL": "ZS-Tag (optional)",
    "ZSCALER_ANALYZER": "Zscaler Analyzer",
    "ZSCALER_CLOUD_ENDPOINTS": "Zscaler Cloud-Endgeräte",
    "ZSCALER_DOMAINS": "Zscaler-Domains",
    "ZSCALER_ESTABLISH_SUPPORT_TUNNEL": "Von Zscaler initiierter On-Demand-Support-Tunnel",
    "ZSCALER_GATEWAY_DETAILS": "Zscaler Gateway-Details",
    "ZSCALER_HELP_PORTAL": "Zscaler-Hilfe-Portal",
    "ZSCALER_INC_ALL_RIGHTS_RESERVED": "Zscaler Inc. Alle Rechte vorbehalten.",
    "ZSCALER_INTERFACE_NAME": "Zscaler-Schnittstellenname",
    "ZSCALER_IP": "Zscaler-IP",
    "ZSCALER_IPS": "Zscaler-IP-Adressen",
    "ZSCALER_PROXY_NW_SERVICES_DESC": "Dieser Netzwerkdienst umfasst alle spezifischen Web-Proxy-Ports von Zscaler einschließlich kundenspezifischer DPPC-Ports.",
    "ZSCALER_PROXY_NW_SERVICES": "Zscaler Proxy Network Services",
    "ZSLOGIN_ADMINISTRATION": "ZIdentity-Verwaltung",
    "ZSPROXY_IPS": "Zscaler-Proxy-IPs",
    "ZT_DEVICES": "ZT-Geräte",
    "ZT_GATEWAY": "Zero-Trust-Gateway",
    "ZTG_ACCOUNT_TEXT": "Das Gateway akzeptiert eingehende Endgeräteanforderungen aus der Liste der angegebenen Konten. Es können AWS-Konten und Kontogruppen, die über die Seite \"Partnerintegrationen\" integriert wurden, ausgewählt werden. Für Konten, die nicht über die Seite \"Partnerintegrationen\" integriert wurden, können Sie die 12-stellige AWS-Konto-ID manuell eingeben. Weitere Informationen zu Partnerintegrationen finden Sie in der {0}Dokumentation zu Cloud Connector-Partnerintegrationen{1}.",
    "ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP": "Wenn Ihr AWS-Konto nicht über die Seite Partnerintegrationen integriert wurde, geben Sie Ihre 12-stellige AWS-Konto-ID ein. Das Gateway akzeptiert Anforderungen, die von Endgeräten aus derselben Region wie das Gateway stammen.",
    "ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP": "Wählen Sie die AWS-Kontogruppen aus, die eine Verbindung zu diesem Gateway herstellen dürfen. Das Gateway akzeptiert Anforderungen, die von Endgeräten aus derselben Region wie das Gateway stammen. Kontogruppen werden auf der Seite \"Partnerintegrationen\" erstellt.",
    "ZTG_ALLOWED_ACCOUNTS_TOOLTIP": "Wählen Sie die AWS-Konten aus, die eine Verbindung zu diesem Gateway herstellen dürfen. Das Gateway akzeptiert Anforderungen, die von Endgeräten aus derselben Region wie das Gateway stammen. Konten werden auf der Seite \"Partnerintegrationen\" hinzugefügt.",
    "ZTG_AVIABILITY_ZONE_TOOLTIP": "Wählen Sie die Verfügbarkeitszonen aus, in denen die Gateway-Komponenten erstellt werden. Es müssen mindestens zwei Verfügbarkeitszonen ausgewählt werden. Beachten Sie, dass es sich hierbei um die IDs der AWS-Verfügbarkeitszonen handelt, nicht um die Namen, die im AWS-Konto angezeigt werden. Es werden IDs angezeigt, da derselbe Verfügbarkeitszonen-Name unterschiedlichen Verfügbarkeitszonen-IDs in verschiedenen AWS-Konten zugeordnet sein kann. Weitere Informationen zur Zuordnung von Verfügbarkeitszonennamen zu IDs und zur Ermittlung der Verfügbarkeitszonen-ID Ihres Kontos finden Sie in der {0}AWS-Dokumentation{1}.",
    "ZTG_CONFIGURATION_TEXT": "Geben Sie die Konfiguration für dieses Gateway ein. Diese Konfiguration steuert die AWS-Region und -Verfügbarkeitszonen, in denen der Dienst verfügbar ist.",
    "ZTG_ID": "ID des Zero-Trust-Gateways",
    "ZTG_LOCATION_TEMPLATE_TOOLTIP": "Wählen Sie die Standortvorlage aus, die zum Erstellen des Standorts verwendet wird, der dem Gateway zugeordnet ist.",
    "ZTG_LOCATION_TOOLTIP": "Geben Sie den Namen des Standorts ein, der diesem Gateway zugeordnet werden soll. Dieser Name ist in allen Richtlinien sichtbar, in denen ein Standort-Objekt verfügbar ist.",
    "ZTG_NAME_TOOLTIP": "Geben Sie einen Namen für Ihr Gateway ein. Dieser Name ist mit dem Gateway verknüpft.",
    "ZTG_REGION_TOOLTIP": "Wählen Sie die Region aus, in der das Gateway erstellt wird. Gateways sind regional und können nur in einer Region bereitgestellt werden. Nach dem Erstellen des Gateways kann die Region nicht mehr geändert werden.",
    "ZTG_REVIEW_TEXT": "Achten Sie darauf, dass alle Informationen korrekt sind, bevor Sie das Gateway erstellen. Nachdem das Gateway erstellt wurde, kann die Bereitstellung der Komponenten einige Minuten dauern. Den Status des Gateways können Sie auf der Seite \"Zero-Trust-Gateway\" sehen.",
    "ZTGW_GROUP": "Zero-Trust-Gateway-Gruppe",
    "ZTGW_VM": "Zero-Trust-Gateway-VM",
    "ZULU": "Zulu",
  },
};
