/* eslint-disable quotes */
/* eslint-disable quote-props */

// Notes: We can add phrases as a key, it does not have to be a key/constant.
// However, use key and constant if the phrase is a long sentence or paragraph.

// Please keep the translations below sorted for easy access and to avoid duplicates

// WIP - will keep this changes until we complete the csv2json conversion

export default {
  translation: {
    "1_HOUR": "1時間",
    "1_MONTH": "1か月",
    "1_WEEK": "1週間",
    "24_HOURS": "24時間",
    "4_HOURS": "4時間",
    "ACCEPT": "承諾",
    "ACCEPTED_ON": "承諾日",
    "ACCESS_TOKEN": "アクセス トークン",
    "ACCOUNT_GROUP": "アカウント グループ",
    "ACCOUNT_ID_ONLY": "アカウントID",
    "ACCOUNT_ID": "AWSアカウントID",
    "ACCOUNT_LIST": "アカウント リスト",
    "ACCOUNT_NAME": "アカウント名",
    "ACCOUNT": "アカウント",
    "ACCOUNTS": "アカウント",
    "ACTION_CAN_NOT_BE_UNDONE": "この操作は元に戻すことができません。",
    "ACTION_CAPS": "アクション",
    "ACTION_INTERFACE": "インターフェイス",
    "ACTION_RESULT": "結果",
    "ACTION_TYPE": "アクション",
    "ACTION": "アクション",
    "ACTIONS": "アクション",
    "ACTIVATE": "アクティブ化",
    "ACTIVATION_FAILED": "アクティベーションに失敗しました",
    "ACTIVATION": "アクティベーション",
    "ACTIVE_ACTIVE": "アクティブ-アクティブ",
    "ACTIVE_CONNECTION": "アクティブ接続",
    "ACTIVE_STANDBY": "アクティブ-スタンバイ",
    "ACTIVE_STATUS": "アクティブ ステータス",
    "ACTIVE": "アクティブ",
    "ADD_5G_DEPLOYMENT": "展開構成を追加",
    "ADD_ACCOUNT": "アカウントを追加",
    "ADD_API_KEY": "クラウド サービスAPIキーを追加",
    "ADD_AWS_ACCOUNT": "AWSアカウントを追加",
    "ADD_AWS_CLOUD_ACCOUNT": "AWSクラウド アカウントを追加",
    "ADD_AWS_GROUP": "AWSグループを追加",
    "ADD_AZURE_ACCOUNT": "Azureアカウントを追加",
    "ADD_AZURE_CLOUD_ACCOUNT": "Azureクラウド アカウントを追加",
    "ADD_BC_PROVISIONING_TEMPLATE": "Branch Connector構成テンプレートを追加",
    "ADD_BRANCH_CONNECTOR_PROV_TEMPLATE": "Branch Connector構成テンプレートを追加",
    "ADD_CLOUD_APP_PROVIDER": "クラウド アプリ プロバイダーを追加",
    "ADD_CLOUD_CONNECTOR_ADMIN": "管理者を追加",
    "ADD_CLOUD_CONNECTOR_ROLE": "管理者ロールを追加",
    "ADD_CLOUD_CONNECTOR": "Cloud Connectorを追加",
    "ADD_CLOUD_NSS_FEED": "クラウドNSSフィードを追加",
    "ADD_CLOUD_PROVISIONING_TEMPLATE": "Cloud Connectorのプロビジョニング テンプレートを追加",
    "ADD_CRITERIA": "条件を追加",
    "ADD_DEPLOYMENT_CONFIGURATION": "展開構成を追加",
    "ADD_DESTINATION_IP_GROUP": "宛先IPグループを追加",
    "ADD_DNS_GATEWAY": "DNSゲートウェイを追加",
    "ADD_DNS_POLICIES": "DNSフィルタリング ルールを追加",
    "ADD_DYNAMIC_VDI_GROUP": "動的VDIグループを追加",
    "ADD_EC_NSS_CLOUD_FEED": "クラウドNSSフィードを追加",
    "ADD_EC_NSS_FEED": "NSSフィードを追加",
    "ADD_EC_NSS_SERVER": "NSSサーバーを追加",
    "ADD_EVENT_GRID": "Event Gridを追加(省略可)",
    "ADD_FILTER": "フィルターを追加",
    "ADD_FILTERS": "フィルターを追加",
    "ADD_GROUP": "グループを追加",
    "ADD_HTTP_HEADER": "HTTPヘッダーを追加",
    "ADD_INTERFACE": "インターフェイスを追加",
    "ADD_IP_INFO": "IP情報を追加",
    "ADD_IP_POOL": "IPプールを追加",
    "ADD_ITEMS": "項目を追加",
    "ADD_LOCATION_AND_CCG": "ロケーションとCloud Connectorsを追加",
    "ADD_LOCATION_TEMPLATE": "ロケーション テンプレートを追加",
    "ADD_LOG_AND_CONTROL_FORWARDING_RULE": "ログとコントロール転送ルールを追加",
    "ADD_LOG_AND_CONTROL_FORWARDING": "ログとコントロール転送を追加",
    "ADD_LOG_AND_CONTROL_GATEWAY": "ログとコントロール ゲートウェイを追加",
    "ADD_MORE": "さらに追加",
    "ADD_NETWORK_SERVICE_GROUP": "ネットワーク サービス グループを追加",
    "ADD_NETWORK_SERVICE": "ネットワーク サービスを追加",
    "ADD_NEW_GATEWAY": "新しいゲートウェイを追加",
    "ADD_NEW": "新規追加",
    "ADD_NSS_FEED": "NSSフィードを追加",
    "ADD_NSS_SERVER": "NSSサーバーを追加",
    "ADD_PORT": "ポートを追加",
    "ADD_PROVISIONING_TEMPLATE": "Cloud Connectorのプロビジョニング テンプレートを追加",
    "ADD_SOURCE_IP_GROUP": "送信元IPグループを追加",
    "ADD_STORAGE_ACCOUNT": "ストレージ アカウントを追加",
    "ADD_SUB_INTERFACE": "サブインターフェイスを追加",
    "ADD_TENANT": "テナントを追加",
    "ADD_TO_A_LOCATION": "ロケーションに追加",
    "ADD_TO_AN_EXISTING_GROUP": "既存のグループに追加",
    "ADD_TRAFFIC_FORWARDING_RULE": "トラフィック転送ルールを追加",
    "ADD_TRAFFIC_FORWARDING": "トラフィック転送を追加",
    "ADD_TRAFFIC_FWD_POLICIES": "トラフィック転送ルールを追加",
    "ADD_UPF": "ユーザー プレーン機能を追加",
    "ADD_VDI_AGENT_FORWARDING_PROFILE": "VDI転送プロファイルを追加",
    "ADD_VDI_TEMPLATE": "VDIテンプレートを追加",
    "ADD_ZERO_TRUST_GATEWAY": "ゼロトラスト ゲートウェイを追加",
    "ADD_ZIA_GATEWAY": "ZIAゲートウェイを追加",
    "ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128": "AWSアカウントの追加制限は128です。",
    "ADDITIONAL_AWS_ACCOUNTS": "追加のAWSアカウント",
    "ADDRESS_RANGES_SHOULD_NOT_OVERLAP": "アドレス範囲は重複しないようにしてください。",
    "ADM_ACTIVATING": "アクティブ化中",
    "ADM_ACTV_DONE": "管理者がアクティブ化されました",
    "ADM_ACTV_FAIL": "管理者のアクティブ化に失敗しました",
    "ADM_ACTV_QUEUED": "キューで待機中のアクティベーション",
    "ADM_EDITING": "編集中",
    "ADM_EXPIRED": "管理者セッションの有効期限が切れました",
    "ADM_LOGGED_IN": "ログイン中の管理者",
    "ADMIN_ID": "管理者ID",
    "ADMIN_LOGIN_NAME_ALREADY_EXISTS_MESSAGE": "この管理者は、別のサービスの管理ポータルにも存在します。この管理者は他の管理ポータルの同じ管理者アカウントに関連付けられます。Zscalerサービスは、メール アドレス、名前、スコープ、パスワード、ステータス、コメントなど、他の管理者アカウントに加えられた変更をすべて更新します。続行しますか？",
    "ADMIN_MANAGEMENT": "管理者の管理",
    "ADMIN_ROLE": "管理者ロール",
    "ADMIN_SAML_PUBLICCERT_INVALID_EXTENSION": "SAML証明書は.cerまたは.pem形式のみ有効です。",
    "ADMINISTRATION_CONFIGURATION": "管理構成",
    "ADMINISTRATION_CONTROL": "管理制御",
    "ADMINISTRATION": "管理",
    "ADMINISTRATOR_ADMIN_USER": "管理者",
    "ADMINISTRATOR_AUDITOR": "監査役",
    "ADMINISTRATOR_MANAGEMENT": "管理者の管理",
    "ADMINISTRATOR_PASSWORD_BASED_LOGIN": "パスワードによるログイン",
    "ADMINISTRATOR_ROLE": "ロール管理",
    "ADMINISTRATOR_SAML_CONFIGURE": "管理者向けSAML認証",
    "ADMINISTRATOR_SAML_ENABLED": "SAML認証を有効化",
    "ADMINISTRATOR_SAML_METADATA": "XMLメタデータのダウンロード",
    "ADMINISTRATOR_SAML": "SAML",
    "ADMINISTRATORS_MANAGEMENT": "管理者の管理",
    "ADMINISTRATORS": "管理者",
    "ADSPYWARE_SITES": "アドウェア/スパイウェアのサイト",
    "ADULT_SEX_EDUCATION": "成人向けの性教育",
    "ADULT_THEMES": "成人向けのトピック",
    "ADVANCED_SETTINGS": "詳細設定",
    "ADWARE_OR_SPYWARE": "スパイウェア/アドウェア",
    "AF_SOUTH_1": "アフリカ(ケープタウン)",
    "AF_SOUTH_1A": "af-south-1a",
    "AF_SOUTH_1B": "af-south-1b",
    "AF_SOUTH_1C": "af-south-1c",
    "AFGHANISTAN_ASIA_KABUL": "アジア/カブール",
    "AFGHANISTAN": "アフガニスタン",
    "AGENT_STATUS": "エージェント ステータス",
    "AGGREGATE_LOGS": "ログを集計",
    "AIAIGAME_DESC": " このプロトコル プラグインは、ホストaiaigame.comへのhttpトラフィックを分類します",
    "AIAIGAME": "AiAi Games",
    "AILI_DESC": " 中国のファッション ショッピングWebサイト",
    "AILI": "Aili",
    "AIM_DESC": " AIM (AOL Instant Messenger)はインスタント メッセンジャーのアプリケーションです。プロトコル名はOSCAR (Open System for CommunicAtion in Realtime)で、ICQにもAIMにも使用されます",
    "AIM_EXPRESS_DESC": " AOL Instant Messaging Expressは、AIMに含まれる標準機能をサポートしていますが、ファイル転送やボイス チャット、ビデオ会議などの高度な機能は提供していません",
    "AIM_EXPRESS": "Aim_express",
    "AIM_TRANSFER_DESC": " AIMはインスタント メッセージング プロトコルです",
    "AIM_TRANSFER": "AIM File Transfer",
    "AIM": "AIM",
    "AIMEXPRESS": "AIM Express",
    "AIMINI_DESC": " Aiminiはファイルを保存、送信、共有するためのオンライン ソリューションです",
    "AIMINI": "Aimini",
    "AIMS_DESC": " AIMSはAIMのセキュアなバージョンです",
    "AIMS": "AIMS",
    "AIOWRITE_THROTTLE": "aiowriteスロットル。ca-ftのみ本日使用",
    "AIRAIM_DESC": " このプロトコル プラグインは、ホストairaim.comへのhttpトラフィックを分類します。コモンネームairaim.comへのsslトラフィックも分類します",
    "AIRAIM": "Airaim",
    "ALAND_ISLANDS_EUROPE_MARIEHAMN": "ヨーロッパ/マリエハムン",
    "ALAND_ISLANDS": "オーランド諸島",
    "ALAND": "オーランド",
    "ALBANIA_EUROPE_TIRANE": "ヨーロッパ/ティラナ",
    "ALBANIA": "アルバニア",
    "ALCOHOL_TOBACCO": "酒/たばこ",
    "ALGERIA_AFRICA_ALGIERS": "アフリカ/アルジェ",
    "ALGERIA": "アルジェリア",
    "ALL_VALUES": "すべての値",
    "ALL_ZSCALER_LOCATION_GROUPS": "すべてのZscalerロケーション グループ",
    "ALL_ZSCALER_LOCATION_TYPES": "すべてのZscalerロケーション タイプ",
    "ALL_ZSCALER_LOCATIONS": "すべてのZscalerロケーション",
    "ALL_ZSCALER_NETWORK_SERVICE": "すべてのZscalerネットワーク サービス",
    "ALL": "すべて",
    "ALLOW_TO_CREATE_NEW_LOCATION": "新しいロケーションの作成を許可する",
    "ALLOW": "許可",
    "ALLOWED_ACCOUNT_GROUPS": "許可されたアカウント グループ",
    "ALLOWED_ACCOUNTS_GROUPS": "許可されたアカウント グループ",
    "ALLOWED_ACCOUNTS": "許可されたアカウント",
    "ALLOWED": "許可済み",
    "ALT_NEW_AGE": "オルタナティブ/ニューエイジ",
    "ALTERNATE_LIFESTYLE": "ライフスタイル",
    "AMAZON_WEB_SERVICES_CONSOLE": "Amazon Web Servicesコンソール",
    "AMAZON_WEB_SERVICES": "Amazon Web Services",
    "AMERICAN_SAMOA_PACIFIC_PAGO_PAGO": "太平洋/パゴパゴ",
    "AMERICAN_SAMOA": "アメリカ領サモア",
    "AMF_IP_CIDR": "AMF IP/CIDR",
    "AMF_NAME": "AMF名",
    "AMI_ID": "AMI ID",
    "ANALYTICS": "分析",
    "ANDORRA_EUROPE_ANDORRA": "ヨーロッパ/アンドラ",
    "ANDORRA": "アンドラ",
    "ANDROID_OS": "Android",
    "ANGOLA_AFRICA_LUANDA": "アフリカ/ルアンダ",
    "ANGOLA": "アンゴラ",
    "ANGUILLA_AMERICA_ANGUILLA": "アメリカ/アンギラ",
    "ANGUILLA": "アンギラ",
    "ANONYMIZER": "P2Pとアノニマイザー",
    "ANTARCTICA_CASEY": "南極大陸/ケーシー",
    "ANTARCTICA_DAVIS": "南極大陸/デイビス",
    "ANTARCTICA_DUMONTDURVILLE": "南極大陸/デュモンデュルヴィユ",
    "ANTARCTICA_MAWSON": "南極大陸/モーソン",
    "ANTARCTICA_MCMURDO": "南極大陸/マクマード",
    "ANTARCTICA_PALMER": "南極大陸/パーマー",
    "ANTARCTICA_ROTHERA": "南極大陸/ロセラ",
    "ANTARCTICA_SOUTH_POLE": "南極大陸/南極",
    "ANTARCTICA_SYOWA": "南極大陸/昭和基地",
    "ANTARCTICA_VOSTOK": "南極大陸/ボストーク基地",
    "ANTARCTICA": "南極大陸",
    "ANTIGUA_AND_BARBUDA_AMERICA_ANTIGUA": "アメリカ/アンティグア",
    "ANTIGUA_AND_BARBUDA": "アンティグア・バーブーダ",
    "ANY_NON_MATCHED_IP_FROM_ZPA_IP_POOLS": "ZPA IPプールの一致しないIP",
    "ANY_RULE": "任意",
    "ANY": "任意",
    "AP_EAST_1": "アジア太平洋(香港)",
    "AP_EAST_1A": "ap-east-1a",
    "AP_EAST_1B": "ap-east-1b",
    "AP_EAST_1C": "ap-east-1c",
    "AP_NORTHEAST_1": "ap-northeast-1 (東京)",
    "AP_NORTHEAST_1A": "ap-northeast-1a",
    "AP_NORTHEAST_1C": "ap-northeast-1c",
    "AP_NORTHEAST_1D": "ap-northeast-1d",
    "AP_NORTHEAST_1E": "ap-northeast-1e",
    "AP_NORTHEAST_2": "ap-northeast-2 (ソウル)",
    "AP_NORTHEAST_2A": "ap-northeast-2a",
    "AP_NORTHEAST_2B": "ap-northeast-2b",
    "AP_NORTHEAST_2C": "ap-northeast-2c",
    "AP_NORTHEAST_3": "ap-northeast-3 (大阪ローカル)",
    "AP_NORTHEAST_3A": "ap-northeast-3a",
    "AP_SOUTH_1": "ap-south-1 (ムンバイ)",
    "AP_SOUTH_1A": "ap-south-1a",
    "AP_SOUTH_1B": "ap-south-1b",
    "AP_SOUTH_1C": "ap-south-1c",
    "AP_SOUTH_2": "アジア太平洋(ハイデラバード)",
    "AP_SOUTHEAST_1": "ap-southeast-1 (シンガポール)",
    "AP_SOUTHEAST_1A": "ap-southeast-1a",
    "AP_SOUTHEAST_1B": "ap-southeast-1b",
    "AP_SOUTHEAST_1C": "ap-southeast-1c",
    "AP_SOUTHEAST_2": "ap-southeast-2 (シドニー)",
    "AP_SOUTHEAST_2A": "ap-southeast-2a",
    "AP_SOUTHEAST_2B": "ap-southeast-2b",
    "AP_SOUTHEAST_2C": "ap-southeast-2c",
    "AP_SOUTHEAST_3": "ap-southeast-3 (ジャカルタ)",
    "AP_SOUTHEAST_4": "アジア太平洋(メルボルン)",
    "API_KEY_MANAGEMENT": "APIキーの管理",
    "API_KEY": "APIキー",
    "APIKEY_MANAGEMENT": "APIキーの管理",
    "APP_CONNECTOR_DEPLOYMENT_STATUS": "App Connector展開ステータス",
    "APP_CONNECTOR_DESCRIPTION": "App Connectorsはこのテンプレートの一部としてプロビジョニングできます。以下の情報を入力するか、省略して次の手順に進んでください。",
    "APP_CONNECTOR_GROUP_NAME": "App Connectorグループ名",
    "APP_CONNECTOR_GROUP_TYPE": "App Connectorグループのタイプ",
    "APP_CONNECTOR_GROUP": "App Connectorグループ",
    "APP_CONNECTOR_INTERFACE": "App Connectorインターフェイス",
    "APP_CONNECTOR": "App Connector",
    "APPLIANCE_MANAGEMENT": "アプライアンス管理",
    "APPLIANCE_NAME": "アプライアンス名",
    "APPLIANCE": "アプライアンス",
    "APPLIANCES": "アプライアンス",
    "APPLICABLE_FOR": "適用対象",
    "APPLICATION_ID": "アプリケーションID",
    "APPLICATION_KEY": "アプリケーション キー",
    "APPLICATION_SEGMENT": "Application Segment",
    "APPLICATION_SEGMENTS": "Application Segments",
    "APPLICATION_SERVICE_GROUPS": "アプリケーション サービス グループ",
    "APPLICATION_VERSION": "アプリケーションのバージョン",
    "APPLICATIONS": "アプリケーション",
    "APPLY_FILTER": "フィルターを適用",
    "APPLY_FILTERS": "フィルターを適用",
    "APPLY_TO_ALL_APP_SEGMENTS": "すべてのApp Segmentsに適用",
    "APPLY": "適用",
    "ARE_YOU_SURE_YOU_WANT_TO_PROCEED": "続行してもよろしいですか？",
    "ARE_YOU_SURE": "続行してもよろしいですか？",
    "ARGENTINA_AMERICA_ARGENTINA_BUENOS_AIRES": "アメリカ/アルゼンチン/ブエノスアイレス",
    "ARGENTINA_AMERICA_ARGENTINA_CATAMARCA": "アメリカ/アルゼンチン/カタマルカ",
    "ARGENTINA_AMERICA_ARGENTINA_CORDOBA": "アメリカ/アルゼンチン/コルドバ",
    "ARGENTINA_AMERICA_ARGENTINA_JUJUY": "アメリカ/アルゼンチン/フフイ",
    "ARGENTINA_AMERICA_ARGENTINA_LA_RIOJA": "アメリカ/アルゼンチン/ラ・リオハ",
    "ARGENTINA_AMERICA_ARGENTINA_MENDOZA": "アメリカ/アルゼンチン/メンドーサ",
    "ARGENTINA_AMERICA_ARGENTINA_RIO_GALLEGOS": "アメリカ/アルゼンチン/リオ・ガジェゴス",
    "ARGENTINA_AMERICA_ARGENTINA_SAN_JUAN": "アメリカ/アルゼンチン/サンフアン",
    "ARGENTINA_AMERICA_ARGENTINA_TUCUMAN": "アメリカ/アルゼンチン/トゥクマン",
    "ARGENTINA_AMERICA_ARGENTINA_USHUAIA": "アメリカ/アルゼンチン/ウシュアイア",
    "ARGENTINA": "アルゼンチン",
    "ARMENIA_ASIA_YEREVAN": "アジア/エレバン",
    "ARMENIA": "アルメニア",
    "ART_CULTURE": "芸術/文化",
    "ARUBA_AMERICA_ARUBA": "アメリカ/アルバ",
    "ARUBA": "アルバ",
    "ASIA_EAST1_A": "asia-east1-a",
    "ASIA_EAST1_B": "asia-east1-b",
    "ASIA_EAST1_C": "asia-east1-c",
    "ASIA_EAST1": "asia-east1",
    "ASIA_EAST2_A": "asia-east2-a",
    "ASIA_EAST2_B": "asia-east2-b",
    "ASIA_EAST2_C": "asia-east2-c",
    "ASIA_EAST2": "asia-east2",
    "ASIA_NORTHEAST1_A": "asia-northeast1-a",
    "ASIA_NORTHEAST1_B": "asia-northeast1-b",
    "ASIA_NORTHEAST1_C": "asia-northeast1-c",
    "ASIA_NORTHEAST1": "asia-northeast1",
    "ASIA_NORTHEAST2_A": "asia-northeast2-a",
    "ASIA_NORTHEAST2_B": "asia-northeast2-b",
    "ASIA_NORTHEAST2_C": "asia-northeast2-c",
    "ASIA_NORTHEAST2": "asia-northeast2",
    "ASIA_NORTHEAST3_A": "asia-northeast3-a",
    "ASIA_NORTHEAST3_B": "asia-northeast3-b",
    "ASIA_NORTHEAST3_C": "asia-northeast3-c",
    "ASIA_NORTHEAST3": "asia-northeast3",
    "ASIA_SOUTH1_A": "asia-south1-a",
    "ASIA_SOUTH1_B": "asia-south1-b",
    "ASIA_SOUTH1_C": "asia-south1-c",
    "ASIA_SOUTH1": "asia-south1",
    "ASIA_SOUTH2_A": "asia-south2-a",
    "ASIA_SOUTH2_B": "asia-south2-b",
    "ASIA_SOUTH2_C": "asia-south2-c",
    "ASIA_SOUTH2": "asia-south2",
    "ASIA_SOUTHEAST1_A": "asia-southeast1-a",
    "ASIA_SOUTHEAST1_B": "asia-southeast1-b",
    "ASIA_SOUTHEAST1_C": "asia-southeast1-c",
    "ASIA_SOUTHEAST1": "asia-southeast1",
    "ASIA_SOUTHEAST2_A": "asia-southeast2-a",
    "ASIA_SOUTHEAST2_B": "asia-southeast2-b",
    "ASIA_SOUTHEAST2_C": "asia-southeast2-c",
    "ASIA_SOUTHEAST2": "asia-southeast2",
    "ASIA": "アジア",
    "ASIAPACIFIC": "アジア太平洋",
    "AT": "：",
    "ATTRIBUTES_APPLICABLE_ONLY_TO_KNOWN_HOPS": "これらの属性は既知のホップにのみ適用されます。",
    "ATTRIBUTES": "属性",
    "AUDIT_LOGS": "監査ログ",
    "AUDIT_OPERATION": "監査業務",
    "AUSTRALIA_ADELAIDE": "オーストラリア/アデレード",
    "AUSTRALIA_BRISBANE": "オーストラリア/ブリスベン",
    "AUSTRALIA_BROKEN_HILL": "オーストラリア/ブロークンヒル",
    "AUSTRALIA_CURRIE": "オーストラリア/カリー",
    "AUSTRALIA_DARWIN": "オーストラリア/ダーウィン",
    "AUSTRALIA_EUCLA": "オーストラリア/ユークラ",
    "AUSTRALIA_HOBART": "オーストラリア/ホバート",
    "AUSTRALIA_LINDEMAN": "オーストラリア/リンデマン",
    "AUSTRALIA_LORD_HOWE": "オーストラリア/ロード・ハウ島",
    "AUSTRALIA_MELBOURNE": "オーストラリア/ニュージーランド",
    "AUSTRALIA_NEWZEALAND": "オーストラリアとニュージーランド",
    "AUSTRALIA_PERTH": "オーストラリア/パース",
    "AUSTRALIA_SOUTHEAST1_A": "australia-southeast1-a",
    "AUSTRALIA_SOUTHEAST1_B": "australia-southeast1-b",
    "AUSTRALIA_SOUTHEAST1_C": "australia-southeast1-c",
    "AUSTRALIA_SOUTHEAST1": "australia-southeast1",
    "AUSTRALIA_SOUTHEAST2_A": "australia-southeast2-a",
    "AUSTRALIA_SOUTHEAST2_B": "australia-southeast2-b",
    "AUSTRALIA_SOUTHEAST2_C": "australia-southeast2-c",
    "AUSTRALIA_SOUTHEAST2": "australia-southeast2",
    "AUSTRALIA_SYDNEY": "オーストラリア/シドニー",
    "AUSTRALIA": "オーストラリア",
    "AUSTRALIACENTRAL": "(アジア太平洋)オーストラリア中部",
    "AUSTRALIACENTRAL2": "(アジア太平洋)オーストラリア中部2",
    "AUSTRALIAEAST": "(アジア太平洋)オーストラリア東部",
    "AUSTRALIASOUTHEAST": "(アジア太平洋)オーストラリア南東部",
    "AUSTRIA_EUROPE_VIENNA": "ヨーロッパ/ウィーン",
    "AUSTRIA": "オーストリア",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_AUP_ENABLED": "AUPが有効な場合は、必要な認証を無効にする必要があります。",
    "AUTH_REQUIRED_MUST_BE_DISABLED_WHEN_CAUTION_ENABLED": "警告が有効な場合は、必要な認証を無効にする必要があります。",
    "AUTH_TYPE": "認証タイプ",
    "AUTHENTICATION_CONFIGURATION": "認証構成",
    "AUTHENTICATION_FAILED": "認証失敗",
    "AUTO_POPULATE_DNS_CACHE": "DNSキャッシュの自動入力",
    "AUTO_REFRESH_DASHBOARD": "ダッシュボードの自動更新",
    "AUTO_SCALING_OPTIONS": "Auto Scalingオプション",
    "AUTO_SCALING": "Auto Scaling",
    "AUTO": "自動",
    "AUTOMATIC_MANAGEMENT_IP": "自動管理IP",
    "AUTOMATIC_SERVICE_IP": "自動サービスIP",
    "AUTOMATIC": "自動",
    "Availability Zone": "アベイラビリティー ゾーン",
    "AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES": "アベイラビリティー ゾーンIDには、少なくとも2つのゾーンが必要です。",
    "AVAILABILITY_ZONE_ID": "アベイラビリティー ゾーンID",
    "AVAILABILITY_ZONE": "アベイラビリティー ゾーン",
    "AVAILABILITY_ZONES": "アベイラビリティー ゾーン",
    "AVAILABLE": "利用可能",
    "AVERAGE": "平均",
    "AWS_ACCESS_KEY_ID": "AWSアクセス キーID",
    "AWS_ACCOUNT_ID": "AWSアカウントID",
    "AWS_ACCOUNT": "AWSアカウント",
    "AWS_AVAILABILITY_ZONE": "AWSアベイラビリティー ゾーン",
    "AWS_CLOUD_FORMATION": "AWS CloudFormation",
    "AWS_CLOUD": "AWS",
    "AWS_GROUP": "AWSグループ",
    "AWS_REGION": "AWSリージョン",
    "AWS_REGIONS": "AWSリージョン",
    "AWS_ROLE_NAME": "AWSロール名",
    "AWS_SECRET_ACCESS_KEY": "AWSシークレット アクセス キー",
    "AWS": "Amazon Web Services",
    "AZERBAIJAN_ASIA_BAKU": "アジア/バク",
    "AZERBAIJAN": "アゼルバイジャン",
    "AZURE_ACCOUNT": "Azureアカウント",
    "AZURE_AVAILABILITY_ZONE": "Azure可用性ゾーン",
    "AZURE_CLOUD": "Azure",
    "AZURE_REGION": "Azureリージョン",
    "AZURE_RESOURCES": "Azureリソース",
    "AZURE_SENTINEL": "Azure Sentinel",
    "AZURE": "Azure",
    "BACK": "戻る",
    "BAHAMAS_AMERICA_NASSAU": "アメリカ/ナッソー",
    "BAHAMAS": "バハマ",
    "BAHRAIN_ASIA_BAHRAIN": "アジア/バーレーン",
    "BAHRAIN": "バーレーン",
    "BAIDUYUNDNS_DESC": " .baiduyundns.comで通知されたDNSトンネル アクティビティー",
    "BAIDUYUNDNS": "BaiduYunDns",
    "BALANCED_RULE": "バランス",
    "BALANCED": "バランス",
    "BANDWIDTH_CONTROL_ENABLED": "サブロケーションは、このロケーションに割り当てられた帯域幅値を共有します",
    "BANDWIDTH_CONTROL": "帯域幅コントロール",
    "BANGLADESH_ASIA_DHAKA": "アジア/ダッカ",
    "BANGLADESH": "バングラデシュ",
    "BARBADOS_AMERICA_BARBADOS": "アメリカ/バルバドス",
    "BARBADOS": "バルバドス",
    "BASE_URL": "ベースURL",
    "BC_APP_CONNECTOR": "Branch ConnectorとApp Connector",
    "BC_CONNECTOR_GROUP": "Cloud & Branch Connectorグループ",
    "BC_CONNECTOR_LOCATION": "Cloud & Branch Connectorロケーション",
    "BC_CONNECTOR_VM": "Branch Connector VM",
    "BC_CONNECTOR": "Branch Connector",
    "BC_DESCRIPTION": "Branch Connectorの説明",
    "BC_DETAILS": "Branch Connectorの詳細",
    "BC_DEVICE_GROUP": "Branch Connectorデバイス グループ",
    "BC_GENERAL_INFORMATION_DESCRIPTION": "Zscaler Cloud & Branch Connector Admin Portal内でBranch Connectorプロビジョニング テンプレートを構成し、Branch Connectorを拠点のアカウントまたはデータ センターの仮想マシンとして展開します。詳細は、{0}はじめに{1}のドキュメントを参照してください。",
    "BC_GROUP_DETAILS": "Branch Connectorグループの詳細",
    "BC_GROUP_NAME": "Branch Connectorグループ名",
    "BC_GROUP_TYPE": "Branch Connectorグループのタイプ",
    "BC_GROUP": "Branch Connectorグループ",
    "BC_IMAGES_DESCRIPTION": "Terraformを使用してBranch ConnectorまたはBranch Connector + App Connectorを展開するには、{0}GitHub{1}にアクセスしてください。詳細は、{2}Zscalerヘルプ ポータル{3}を参照してください。",
    "BC_IMAGES_DETAIL1": "Branch Connectorのイメージには、まだプロビジョニングされていないApp Connectorが含まれます。Branch ConnectorとApp Connectorを展開するには、結合されたインスタンスを選択し、それに応じて展開プロパティーを構成する必要があります。",
    "BC_IMAGES_DETAIL2": "詳細は、{0}仮想デバイスの展開管理{1}を参照してください。",
    "BC_VM_SIZE": "Branch ConnectorのVMサイズ",
    "BELARUS_EUROPE_MINSK": "ヨーロッパ/ミンスク",
    "BELARUS": "ベラルーシ",
    "BELGIUM_EUROPE_BRUSSELS": "ヨーロッパ/ブリュッセル",
    "BELGIUM": "ベルギー",
    "BELIZE_AMERICA_BELIZE": "アメリカ/ベリーズ",
    "BELIZE": "ベリーズ",
    "BENIN_AFRICA_PORTO_NOVO": "アフリカ/ポルトノボ",
    "BENIN": "ベニン共和国",
    "BERMUDA_ATLANTIC_BERMUDA": "大西洋/バミューダ諸島",
    "BERMUDA": "バミューダ諸島",
    "BEST_LINK": "最適なリンク",
    "BEST": "最適なリンク",
    "BESTLINK_RULE": "最適なリンク",
    "BHUTAN_ASIA_THIMPHU": "アジア/ティンプー",
    "BHUTAN": "ブータン",
    "BLACKLIST_LOOKUP_RESULTS": "拒否リストのルックアップ結果",
    "BLACKLISTED_IP_CHECK": "拒否リストのIPチェック",
    "BLOCK_INTERNET_ACCESS": "インターネット アクセスをブロック",
    "BLOCK": "ブロック",
    "BLOG": "ブログ",
    "BLUEJEANS": "BlueJeans",
    "BOLIVIA_AMERICA_LA_PAZ": "アメリカ/ラパス",
    "BOLIVIA": "ボリビア",
    "BOSNIA_AND_HERZEGOVINA_EUROPE_SARAJEVO": "ヨーロッパ/サラエヴォ",
    "BOSNIA_AND_HERZEGOVINA": "ボスニア・ヘルツェゴビナ",
    "BOSNIA_AND_HERZEGOWINA_EUROPE_SARAJEVO": "ヨーロッパ/サラエヴォ",
    "BOSNIA_AND_HERZEGOWINA": "ボスニア・ヘルツェゴビナ",
    "BOTH_REQ_RESP_ALLOW": "許可",
    "BOTH_SESSION_AND_AGGREGATE_LOGS": "セッション ログと集約ログの両方",
    "BOTNET": "ボットネット コールバック",
    "BOTSWANA_AFRICA_GABORONE": "アフリカ/ハボローネ",
    "BOTSWANA": "ボツワナ",
    "BRANCH_AND_CLOUD_CONNECTOR_GROUP_NAME": "Cloud & Branch Connectorのグループ名",
    "BRANCH_AND_CLOUD_CONNECTOR_MONITORING": "Cloud & Branch Connectorのモニタリング",
    "BRANCH_AND_CLOUD_CONNECTOR": "Cloud & Branch Connector",
    "BRANCH_AND_CLOUD_MONITORING": "クラウドとブランチのモニタリング",
    "BRANCH_CLOUD_CONNECTOR_GROUP": "Cloud & Branch Connectorグループ",
    "BRANCH_CONFIGURATION": "ブランチ構成",
    "BRANCH_CONNECTOR_GROUP": "Branch Connectorグループ",
    "BRANCH_CONNECTOR_GROUPS": "Branch Connectorグループ",
    "BRANCH_CONNECTOR_IMAGES": "Branch Connectorのイメージ",
    "BRANCH_CONNECTOR_INFORMATION": "コネクター情報",
    "BRANCH_CONNECTOR_LOCS": "Branch Connectorのロケーション",
    "BRANCH_CONNECTOR_MONITORING": "Branch Connectorの監視",
    "BRANCH_CONNECTOR": "Branch Connector",
    "BRANCH_DEVICES": "ブランチ デバイス",
    "BRANCH_MANAGEMENT": "ブランチ管理",
    "BRANCH_PROVISIONING": "ブランチ プロビジョニング",
    "BRANCH_TYPE": "ブランチ タイプ",
    "BRAZIL_AMERICA_ARAGUAINA": "アメリカ/アラグァイーナ",
    "BRAZIL_AMERICA_BAHIA": "アメリカ/バイーア州",
    "BRAZIL_AMERICA_BELEM": "アメリカ/ベレン",
    "BRAZIL_AMERICA_BOA_VISTA": "アメリカ/ボア・ヴィスタ",
    "BRAZIL_AMERICA_CAMPO_GRANDE": "アメリカ/カンポ・グランデ",
    "BRAZIL_AMERICA_CUIABA": "アメリカ/クイアバ",
    "BRAZIL_AMERICA_EIRUNEPE": "アメリカ/エイルネペ",
    "BRAZIL_AMERICA_FORTALEZA": "アメリカ/フォルタレザ",
    "BRAZIL_AMERICA_MACEIO": "アメリカ/マセイオ",
    "BRAZIL_AMERICA_MANAUS": "アメリカ/マナウス",
    "BRAZIL_AMERICA_NORONHA": "アメリカ/ノローニャ",
    "BRAZIL_AMERICA_PORTO_VELHO": "アメリカ/ポルト・ヴェーリョ",
    "BRAZIL_AMERICA_RECIFE": "アメリカ/レシフェ",
    "BRAZIL_AMERICA_RIO_BRANCO": "アメリカ/リオブランコ",
    "BRAZIL_AMERICA_SAO_PAULO": "アメリカ/サンパウロ",
    "BRAZIL": "ブラジル",
    "BRAZILSOUTH": "(南米)ブラジル南部",
    "BRAZILSOUTHEAST": "(南米)ブラジル南東部",
    "BRAZILUS": "(南米)ブラジル、アメリカ大陸",
    "BRITISH_INDIAN_OCEAN_TERRITORY_INDIAN_CHAGOS": "インド洋/チャゴス諸島",
    "BRITISH_INDIAN_OCEAN_TERRITORY": "イギリス領インド洋地域",
    "BRITISH_VIRGIN_ISLANDS": "イギリス領ヴァージン諸島",
    "BROWSER_EXPLOIT": "ブラウザー エクスプロイト",
    "BRUNEI_DARUSSALAM_ASIA_BRUNEI": "アジア/ブルネイ",
    "BRUNEI_DARUSSALAM": "ブルネイ・ダルサラーム国",
    "BRUNEI": "ブルネイ",
    "BULGARIA_EUROPE_SOFIA": "ヨーロッパ/ソフィア",
    "BULGARIA": "ブルガリア",
    "BURKINA_FASO_AFRICA_OUAGADOUGOU": "アフリカ/ワガドゥグー",
    "BURKINA_FASO": "ブルキナファソ",
    "BURUNDI_AFRICA_BUJUMBURA": "アフリカ/ブジュンブラ",
    "BURUNDI": "ブルンジ",
    "BW_DOWNLOAD": "ダウンロード(Mbps)",
    "BW_UPLOAD": "アップロード(Mbps)",
    "BYTES": "バイト",
    "CA_CENTRAL_1": "カナダ(中部)",
    "CA_CENTRAL_1A": "ca-central-1a",
    "CA_CENTRAL_1B": "ca-central-1b",
    "CA_CENTRAL_1D": "ca-central-1d",
    "CA_INACTIVE": "正常性モニターが失敗しました。システムの停止状態が長すぎます。",
    "CABO_VERDE": "カーボベルデ",
    "CAMBODIA_ASIA_PHNOM_PENH": "アジア/プノンペン",
    "CAMBODIA": "カンボジア王国",
    "CAMEROON_AFRICA_DOUALA": "アフリカ/ドゥアラ",
    "CAMEROON": "カメルーン",
    "CANADA_AMERICA_ATIKOKAN": "アメリカ/アティコーカン",
    "CANADA_AMERICA_BLANC_SABLON": "アメリカ/ブラン・サブロン",
    "CANADA_AMERICA_CAMBRIDGE_BAY": "アメリカ/ケンブリッジ・ベイ",
    "CANADA_AMERICA_DAWSON_CREEK": "アメリカ/ドーソン・クリーク",
    "CANADA_AMERICA_DAWSON": "アメリカ/ドーソン",
    "CANADA_AMERICA_EDMONTON": "アメリカ/エドモントン",
    "CANADA_AMERICA_GLACE_BAY": "アメリカ/グレス・ベイ",
    "CANADA_AMERICA_GOOSE_BAY": "アメリカ/グース・ベイ",
    "CANADA_AMERICA_HALIFAX": "アメリカ/ハリファックス",
    "CANADA_AMERICA_INUVIK": "アメリカ/イヌヴィック",
    "CANADA_AMERICA_IQALUIT": "アメリカ/イカルイト",
    "CANADA_AMERICA_MONCTON": "アメリカ/モンクトン",
    "CANADA_AMERICA_MONTREAL": "アメリカ/モントリオール",
    "CANADA_AMERICA_NIPIGON": "アメリカ/ニピゴン",
    "CANADA_AMERICA_PANGNIRTUNG": "アメリカ/パングナータング",
    "CANADA_AMERICA_RAINY_RIVER": "アメリカ/レイニー川",
    "CANADA_AMERICA_RANKIN_INLET": "アメリカ/ランキン・インレット",
    "CANADA_AMERICA_REGINA": "アメリカ/レジャイナ",
    "CANADA_AMERICA_RESOLUTE": "アメリカ/レゾリュート",
    "CANADA_AMERICA_ST_JOHNS": "アメリカ/セントジョンズ",
    "CANADA_AMERICA_SWIFT_CURRENT": "アメリカ/スウィフト・カレント",
    "CANADA_AMERICA_THUNDER_BAY": "アメリカ/サンダーベイ",
    "CANADA_AMERICA_TORONTO": "アメリカ/トロント",
    "CANADA_AMERICA_VANCOUVER": "アメリカ/バンクーバー",
    "CANADA_AMERICA_WHITEHORSE": "アメリカ/ホワイトホース",
    "CANADA_AMERICA_WINNIPEG": "アメリカ/ウィニペグ",
    "CANADA_AMERICA_YELLOWKNIFE": "アメリカ/イエローナイフ",
    "CANADA": "カナダ",
    "CANADACENTRAL": "(カナダ)カナダ中部",
    "CANADAEAST": "(カナダ)カナダ東部",
    "CANCEL_SUCCESS_MESSAGE": "すべての変更がキャンセルされました。",
    "CANCEL": "キャンセル",
    "CAPE_VERDE_ATLANTIC_CAPE_VERDE": "大西洋/カーボベルデ",
    "CAPE_VERDE": "カーボベルデ",
    "CATEGORIES": "カテゴリー",
    "CATEGORY": "カテゴリー",
    "CAYMAN_ISLANDS_AMERICA_CAYMAN": "アメリカ/ケイマン諸島",
    "CAYMAN_ISLANDS": "ケイマン諸島",
    "CC_ADMIN": "Cloud Connector管理者",
    "CC_BC_DETAILS": "Cloud & Branch Connectorの詳細",
    "CC_CLOUD_PROVIDER_DESCRIPTION": "次のクラウド プロバイダーのいずれかを選択します。",
    "CC_DETAILS": "Cloud Connectorの詳細",
    "CC_ENABLE_XFF_FORWARDING": "クライアント リクエストのXFFを使用",
    "CC_GENERAL_INFORMATION_DESCRIPTION": "Amazon Web Services (AWS)、Google Cloud Platform (GCP)、またはMicrosoft Azureの仮想マシンとしてCloud Connectorを展開するために、Zscaler Cloud & Branch Connector Admin Portal内でクラウド プロビジョニング テンプレートを構成します。詳細は、{0}クラウド プロビジョニング テンプレートについて{1}を参照してください。",
    "CC_GROUP_NAME": "Cloud Connectorグループ名",
    "CC_GROUP": "CCグループ",
    "CC_INSTANCE": "CCインスタンス",
    "CC_LOCATION": "Cloud Connectorのロケーション",
    "CC_NW": "CLOUD CONNECTORSネットワーク",
    "CC_ROLE_NAME": "Cloud Connectorロール名",
    "CC_SOURCE_IP": "CCの送信元IP",
    "CC_SOURCE_PORT": "CCの送信元ポート",
    "CC_STATUS": "Cloud Connectorのステータス",
    "CC_VERSION": "Cloud Connectorのバージョン",
    "CC_VM_NAME": "Cloud ConnectorのVM名",
    "CC_VM": "CC VM",
    "CCA_DEVICE_GROUP": "VDIデバイス グループ",
    "CCA_DEVICE": "VDIデバイス",
    "CCA_FWD_PROFILE": "VDI転送プロファイル",
    "CCA_TEMPLATE_APIKEY": "VDIテンプレートAPIキー",
    "CCA_TEMPLATE_KEY": "VDIテンプレート キー",
    "CCA_TEMPLATE": "VDIテンプレート",
    "CDN": "CDN",
    "CELLULAR_CONFIGURATION_MODE_CORE": "分割展開 - コア",
    "CELLULAR_CONFIGURATION_MODE_EDGE": "分割展開 - エッジ",
    "CELLULAR_CONFIGURATION_MODE_EDGEONLY": "エッジのみ",
    "CELLULAR_CONFIGURATION_MODE": "セルラー構成モード",
    "CELLULAR_CONFIGURATION_SELECTION": "セルラー構成を選択",
    "CELLULAR_CONFIGURATION": "セルラー構成",
    "CELLULAR_DEPLOYMENT_CONFIGURATION": "展開構成",
    "CELLULAR": "セルラー",
    "CENTOS": "CentOS",
    "CENTRAL_AFRICAN_REPUBLIC_AFRICA_BANGUI": "アフリカ/バンギ",
    "CENTRAL_AFRICAN_REPUBLIC": "中央アフリカ共和国",
    "CENTRALINDIA": "(アジア太平洋)インド中部",
    "CENTRALUS": "(米国)米国中部",
    "CENTRALUSEUAP": "(米国)米国中部EUAP",
    "CENTRALUSSTAGE": "(米国)米国中部(ステージ)",
    "CF_ADD_ON_GWLB_TEMPLATE": "ゲートウェイ ロード バランサー(GWLB)付きのアドオン テンプレート",
    "CF_CUSTOM_DEPLOYMENT_TEMPLATE": "カスタム展開テンプレート",
    "CF_DEFAULT_DEPLOYMENT_TEMPLATE": "スターター展開テンプレート",
    "CF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "ZPA付きアドオン テンプレート",
    "CF_PRE_DEPLOYMENT_TEMPLATE": "展開前テンプレート",
    "CHAD_AFRICA_NDJAMENA": "アフリカ/ンジャメナ",
    "CHAD": "チャド共和国",
    "CHANGE_PASSWORD_REMINDER": "パスワードの変更に関するリマインダー",
    "CHANGE_PASSWORD_WINDOW": "次の期間ごとにパスワードを変更してください：",
    "CHANGE_PASSWORD": "パスワードを変更",
    "CHECK_BLACKLIST": "拒否リストを確認",
    "CHILE_AMERICA_SANTIAGO": "アメリカ/サンティアゴ",
    "CHILE_PACIFIC_EASTER": "太平洋/イースター島",
    "CHILE": "チリ",
    "CHINA_ASIA_CHONGQING": "アジア/重慶",
    "CHINA_ASIA_HARBIN": "アジア/ハルビン市",
    "CHINA_ASIA_KASHGAR": "アジア/カシュガル市",
    "CHINA_ASIA_SHANGHAI": "アジア/上海",
    "CHINA_ASIA_URUMQI": "アジア/ウルムチ",
    "CHINA": "中国",
    "CHINAEAST": "(アジア太平洋)中国東部",
    "CHINAEAST2": "(アジア太平洋)中国東部2",
    "CHINAEAST3": "(アジア太平洋)中国東部3",
    "CHINANORTH": "(アジア太平洋)中国北部",
    "CHINANORTH2": "(アジア太平洋)中国北部2",
    "CHINANORTH3": "(アジア太平洋)中国北部3",
    "CHOOSE_EXISTING_LOCATION": "既存のロケーションを選択",
    "CHOOSE_TO_RECEIVE_UPDATES": "更新を受信する",
    "CHRISTMAS_ISLAND_INDIAN_CHRISTMAS": "インド洋/クリスマス島",
    "CHRISTMAS_ISLAND": "クリスマス島",
    "CHROME_OS": "Chrome",
    "CIPHER_PROTOCOL": "暗号プロトコル",
    "CIPHER": "暗号",
    "CITY_STATE_PROVINCE_OPTIONAL": "市区町村、都道府県(省略可)",
    "CITY": "都市",
    "CLASSIFIEDS": "クラシファイド広告",
    "CLEAR_ALL": "すべてクリア",
    "CLEAR_FILTERS": "フィルターをクリア",
    "CLEAR_SEARCH_AND_SORT": "検索と並べ替えをクリア",
    "CLICK_FOR_MORE_INFO": "クリックすると詳細情報が表示されます",
    "CLICK_HERE_TO_ACCEPT_EUSA": "ここをクリックして、保留中のEUSA契約に同意します",
    "CLIENT_CONNECTOR_FOR_VDI": "VDI用Client Connector",
    "CLIENT_DEST_NAME": "クライアントの宛先名",
    "CLIENT_DESTINATION_IP": "クライアント宛先IP",
    "CLIENT_DESTINATION_PORT": "クライアント宛先ポート",
    "CLIENT_ID": "クライアントID",
    "CLIENT_IP": "クライアントIP",
    "CLIENT_NETWORK_PROTOCOL": "クライアントNWプロトコル",
    "CLIENT_SECRET": "クライアント シークレット",
    "CLIENT_SOURCE_IP": "クライアント送信元IP",
    "CLIENT_SOURCE_PORT": "クライアント送信元ポート",
    "CLOSE": "閉じる",
    "CLOUD_ACCOUNT": "クラウド アカウント",
    "CLOUD_AUTOMATION_SCRIPTS": "クラウド自動化スクリプト",
    "CLOUD_CONFIG_REQUIREMENTS": "クラウド構成要件",
    "CLOUD_CONFIGURATION": "クラウド構成",
    "CLOUD_CONNECTOR_CONFIGURATION_NOT_APPLICABLE": "この構成モードはCloud Connectorには適用されません",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_OPTIONAL": "グループと名前空間",
    "CLOUD_CONNECTOR_GROUP_AND_NAMESPACE_TEXT": "必要に応じて、このサブスクリプション グループに含めるCloud Connectorグループをリストから選択します。異なるアカウントで同じサブスクリプションを再利用できるようにする名前空間を入力します。",
    "CLOUD_CONNECTOR_GROUP_CREATION": "Cloud Connectorグループの作成",
    "CLOUD_CONNECTOR_GROUP": "Cloud Connectorグループ",
    "CLOUD_CONNECTOR_GROUPS": "Cloud Connectorグループ",
    "CLOUD_CONNECTOR_INFORMATION": "コネクター情報",
    "CLOUD_CONNECTOR_INSTANCE_ROLE_NAME": "Cloud Connectorインスタンス ロール名 ",
    "CLOUD_CONNECTOR_MANAGEMENT": "Cloud Connectorの管理",
    "CLOUD_CONNECTOR_MONITORING": "Cloud Connectorの監視",
    "CLOUD_CONNECTOR_NAME": "Cloud Connector名",
    "CLOUD_CONNECTOR_PROVISIONING": "Cloud Connectorのプロビジョニング",
    "CLOUD_CONNECTOR_TRAFFIC_FLOW": "Cloud Connectorトラフィック フロー",
    "CLOUD_CONNECTOR": "Cloud Connector",
    "CLOUD_CONNECTORS_GROUP_AND_NAMESPACE": "グループと名前空間",
    "CLOUD_CONNECTORS": "Cloud Connectors",
    "CLOUD_FORMATION": "CloudFormation",
    "CLOUD_MANAGEMENT": "クラウド管理",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_FAILED": "失敗",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PENDING": "検証は保留中です。アイコンをクリックして接続をテストしてください。",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_PREFIX": "最終検証日",
    "CLOUD_NSS_CONNECTIVITY_VALIDATION_SUCCESSFUL": "成功",
    "CLOUD_NSS_HTTP_HEADERS": "HTTPヘッダー",
    "CLOUD_OR_ON_PREMISE": "クラウドまたはオンプレミス",
    "CLOUD_PROVIDER_TYPE": "クラウド プロバイダー タイプ",
    "CLOUD_PROVIDER": "クラウド プロバイダー",
    "CLOUD_PROVISIONING": "クラウド プロビジョニング",
    "CLOUD_WATCH_GROUP_ARN": "CloudWatchグループARN",
    "CLOUD": "クラウド",
    "CLOUDFORMATION_TEXT": "CloudFormationテンプレートを使用して、Zscalerにアカウント情報へのアクセス権限を提供します。[CloudFormationを起動]のハイパーリンクを使用して、AWSアカウントで事前に入力されたCloudFormationテンプレートを開きます。起動オプションが機能しない場合は、{1}CloudFormationテンプレートをダウンロード{2}します。",
    "CLOUDFORMATION": "CloudFormation",
    "CLOUDWATCH_ARN_OPTIONAL": "CloudWatch ARN (省略可)",
    "CLT_RX_BYTES": "クライアントが受信したバイト数",
    "CLT_TX_BYTES": "クライアントが送信したバイト数",
    "CLT_TX_DROPS": "クライアント ドロップ バイト数",
    "CLUSTERS": "クラスター",
    "CN_NORTH_1": "中国(北京)",
    "CN_NORTH_1A": "cn-north-1a",
    "CN_NORTH_1B": "cn-north-1b",
    "CN_NORTHWEST_1": "中国(寧夏)",
    "CN_NORTHWEST_1A": "cn-northwest-1a",
    "CN_NORTHWEST_1B": "cn-northwest-1b",
    "CN_NORTHWEST_1C": "cn-northwest-1c",
    "COCOS_KEELING_ISLANDS_INDIAN_COCOS": "インド洋/ココス諸島",
    "COCOS_KEELING_ISLANDS": "ココス(キーリング)諸島",
    "CODE": "コード",
    "COLOMBIA_AMERICA_BOGOTA": "アメリカ/ボゴタ",
    "COLOMBIA": "コロンビア",
    "COMMANDS": "コマンド",
    "COMMENTS": "コメント",
    "COMOROS_INDIAN_COMORO": "インド洋/コモロ",
    "COMOROS": "コモロ",
    "COMPARE_VERSIONS": "バージョンを比較",
    "COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE": "推奨されるEC2インスタンスタイプを計算",
    "COMPUTE": "計算する",
    "COMPUTER_HACKING": "コンピューターのハッキング",
    "CONFIG": "構成",
    "CONFIGURATION_INFO": "構成情報",
    "CONFIGURATION_MODE": "構成モード",
    "CONFIGURATION_NAME": "構成名",
    "CONFIGURATION_TEMPLATE_NAME": "構成テンプレート名",
    "CONFIGURATION_TEXT": "Zscalerには、AWSアカウントのIAMロールを引き受ける権限が必要です。これらの権限により、ZscalerはAWSでリアルタイムの構成メタデータを収集できます。詳細は、{1}新しいAWSアカウントの追加{2}を参照してください。",
    "CONFIGURATION": "構成",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP": "ログとコントロール転送のリクエストとレスポンスを制御するルールを定義できます。",
    "CONFIGURE_LOG_AND_CONTROL_FORWARD": "ログとコントロール転送ポリシーの構成",
    "CONFIGURE_TRAFFIC_FORWARD_TIP": "トラフィック転送のリクエストとレスポンスを制御するルールを定義できます。",
    "CONFIGURE_TRAFFIC_FORWARD": "トラフィック転送の構成",
    "CONFIGURED_MODE": "構成済みモード",
    "CONFIRM_CHANGES": "変更を確認",
    "CONFIRM_PASSWORD_NON_EQUALITY": "新しいパスワードと確認用に入力したパスワードは一致する必要があります",
    "CONFIRM_PASSWORD_PLACEHOLDER": "新しいパスワードを入力して確認してください",
    "CONFIRM_PASSWORD": "パスワードを確認",
    "CONFIRM": "確認",
    "CONGO_CONGO_BRAZZAVILLE_AFRICA_BRAZZAVILLE": "アフリカ/ブラザビル",
    "CONGO_CONGO_BRAZZAVILLE": "アフリカ/ブラザビル",
    "CONGO_DEM_REP_AFRICA_KINSHASA": "アフリカ/キンシャサ",
    "CONGO_DEM_REP_AFRICA_LUBUMBASHI": "アフリカ/ルブンバシ",
    "CONGO_DEM_REP": "コンゴ(民主共和国)",
    "CONGO_REP_AFRICA_BRAZZAVILLE": "アフリカ/ブラザビル",
    "CONGO_REP": "コンゴ(共和国)",
    "CONGO_REPUBLIC": "コンゴ共和国",
    "CONNECTOR_GROUP": "コネクター グループ",
    "CONNECTOR_GROUPS": "コネクター グループ",
    "CONNECTOR_INSTANCE": "コネクター インスタンス",
    "CONNECTOR_IP": "コネクターIP",
    "CONNECTOR_MANAGEMENT": "コネクタ管理",
    "CONNECTOR_NAME": "コネクター名",
    "CONNECTOR_NAMES": "コネクター名",
    "CONNECTOR_SOURCE_IP": "コネクター送信元IP",
    "CONNECTOR_SOURCE_PORT": "コネクター送信元ポート",
    "CONNECTOR_VM_SIZE": "コネクターVMサイズ",
    "CONNECTOR_VM": "コネクターVM",
    "CONTAINS": "次の値を含む",
    "CONTINUING_EDUCATION_COLLEGES": "教育/大学を継続",
    "CONTROL_SLASH_DATA": "コントロール/データ",
    "COOK_ISLANDS_PACIFIC_RAROTONGA": "太平洋/ラロトンガ島",
    "COOK_ISLANDS": "クック諸島",
    "Copy for provisioning": "プロビジョニング用にコピー",
    "COPY_CLOUD_CONNECTOR": "Cloud Connectorをコピー",
    "COPY_FOR_PROVISIONING": "プロビジョニング用にコピー",
    "COPY_PROVISIONING_URL": "プロビジョニングURLをコピー",
    "COPY_RIGHT": "Copyright ©2007-2020 Zscaler Inc. All rights reserved.",
    "COPY": "コピー",
    "COPYRIGHT_INFRINGEMENT": "著作権侵害",
    "COPYRIGHT": "Copyright",
    "CORPORATE_MARKETING": "コーポレート マーケティング",
    "CORPORATE": "企業ユーザーのトラフィック タイプ",
    "COSTA_RICA_AMERICA_COSTA_RICA": "アメリカ/コスタリカ",
    "COSTA_RICA": "コスタリカ",
    "COTE_DIVOIRE_AFRICA_ABIDJAN": "アフリカ/アビジャン",
    "COTE_DIVOIRE": "コートジボワール",
    "COUNT": "回数",
    "COUNTRIES": "国",
    "COUNTRY_A1": "匿名プロキシ",
    "COUNTRY_A2": "衛星プロバイダー",
    "COUNTRY_AC": "アセンション島",
    "COUNTRY_AD": "アンドラ",
    "COUNTRY_AE": "アラブ首長国連邦",
    "COUNTRY_AF": "アフガニスタン",
    "COUNTRY_AG": "アンティグア・バーブーダ",
    "COUNTRY_AI": "アンギラ",
    "COUNTRY_AL": "アルバニア",
    "COUNTRY_AM": "アルメニア",
    "COUNTRY_AN": "オランダ領アンティル",
    "COUNTRY_AO": "アンゴラ",
    "COUNTRY_AP": "アジア/太平洋地域",
    "COUNTRY_AQ": "南極大陸",
    "COUNTRY_AR": "アルゼンチン",
    "COUNTRY_AS": "アメリカ領サモア",
    "COUNTRY_AT": "オーストリア",
    "COUNTRY_AU": "オーストラリア",
    "COUNTRY_AW": "アルバ",
    "COUNTRY_AX": "オーランド諸島",
    "COUNTRY_AZ": "アゼルバイジャン",
    "COUNTRY_BA": "ボスニア・ヘルツェゴビナ",
    "COUNTRY_BB": "バルバドス",
    "COUNTRY_BD": "バングラデシュ",
    "COUNTRY_BE": "ベルギー",
    "COUNTRY_BF": "ブルキナファソ",
    "COUNTRY_BG": "ブルガリア",
    "COUNTRY_BH": "バーレーン",
    "COUNTRY_BI": "ブルンジ",
    "COUNTRY_BJ": "ベニン共和国",
    "COUNTRY_BL": "サン・バルテルミー島",
    "COUNTRY_BM": "バミューダ諸島",
    "COUNTRY_BN": "ブルネイ・ダルサラーム国",
    "COUNTRY_BO": "ボリビア",
    "COUNTRY_BQ": "ボネール、シント・ユースタティウスおよびサバ",
    "COUNTRY_BR": "ブラジル",
    "COUNTRY_BS": "バハマ",
    "COUNTRY_BT": "ブータン",
    "COUNTRY_BU": "ミャンマー",
    "COUNTRY_BV": "ブーベ島",
    "COUNTRY_BW": "ボツワナ",
    "COUNTRY_BX": "ベネルクス商標およびデザイン事務所",
    "COUNTRY_BY": "ベラルーシ",
    "COUNTRY_BZ": "ベリーズ",
    "COUNTRY_CA": "カナダ",
    "COUNTRY_CC": "ココス(キーリング)諸島",
    "COUNTRY_CD": "コンゴ民主共和国(コンゴ - キンシャサ)",
    "COUNTRY_CF": "中央アフリカ共和国",
    "COUNTRY_CG": "コンゴ民主共和国(コンゴ - ブラザヴィル)",
    "COUNTRY_CH": "スイス",
    "COUNTRY_CI": "コートジボワール",
    "COUNTRY_CK": "クック諸島",
    "COUNTRY_CL": "チリ",
    "COUNTRY_CM": "カメルーン",
    "COUNTRY_CN": "中国",
    "COUNTRY_CO": "コロンビア",
    "COUNTRY_CODE": "国",
    "COUNTRY_CP": "クリッパートン島",
    "COUNTRY_CR": "コスタリカ",
    "COUNTRY_CS": "セルビア・モンテネグロ",
    "COUNTRY_CT": "カントン島およびエンダーベリー島",
    "COUNTRY_CU": "キューバ共和国",
    "COUNTRY_CV": "カーボベルデ",
    "COUNTRY_CW": "キュラソー",
    "COUNTRY_CX": "クリスマス島",
    "COUNTRY_CY": "キプロス共和国",
    "COUNTRY_CZ": "チェコ共和国",
    "COUNTRY_DD": "ドイツ民主共和国",
    "COUNTRY_DE": "ドイツ",
    "COUNTRY_DG": "ディエゴガルシア島",
    "COUNTRY_DJ": "ジブチ共和国",
    "COUNTRY_DK": "デンマーク",
    "COUNTRY_DM": "ドミニカ国",
    "COUNTRY_DO": "ドミニカ共和国",
    "COUNTRY_DY": "ベニン共和国",
    "COUNTRY_DZ": "アルジェリア",
    "COUNTRY_EA": "セウタ、メリリャ",
    "COUNTRY_EC": "エクアドル共和国",
    "COUNTRY_EE": "エストニア共和国",
    "COUNTRY_EF": "欧州特許条約下にある連合国",
    "COUNTRY_EG": "エジプト",
    "COUNTRY_EH": "西サハラ",
    "COUNTRY_EM": "欧州商標庁",
    "COUNTRY_EP": "欧州特許庁",
    "COUNTRY_ER": "エリトリア国",
    "COUNTRY_ES": "スペイン",
    "COUNTRY_ET": "エチオピア",
    "COUNTRY_EU": "ヨーロッパ",
    "COUNTRY_EV": "ユーラシア特許庁",
    "COUNTRY_EW": "エストニア共和国",
    "COUNTRY_FI": "フィンランド共和国",
    "COUNTRY_FJ": "フィジー共和国",
    "COUNTRY_FK": "フォークランド諸島(マルビナス諸島)",
    "COUNTRY_FL": "リヒテンシュタイン公国",
    "COUNTRY_FM": "ミクロネシア連邦",
    "COUNTRY_FO": "フェロー諸島",
    "COUNTRY_FQ": "フランス領南方・南極地域",
    "COUNTRY_FR": "フランス共和国",
    "COUNTRY_FX": "フランス・メトロポリテーヌ",
    "COUNTRY_GA": "ガボン共和国",
    "COUNTRY_GB": "英国",
    "COUNTRY_GC": "湾岸アラブ諸国協力会議特許庁(GCC)",
    "COUNTRY_GD": "グレナダ",
    "COUNTRY_GE": "グルジア",
    "COUNTRY_GF": "フランス領ギアナ",
    "COUNTRY_GG": "ガーンジー",
    "COUNTRY_GH": "ガーナ共和国",
    "COUNTRY_GI": "ジブラルタル",
    "COUNTRY_GL": "グリーンランド",
    "COUNTRY_GM": "ガンビア共和国",
    "COUNTRY_GN": "ギニア共和国",
    "COUNTRY_GP": "グアドループ",
    "COUNTRY_GQ": "赤道ギニア共和国",
    "COUNTRY_GR": "ギリシャ共和国",
    "COUNTRY_GS": "サウスジョージア・サウスサンドウィッチ諸島",
    "COUNTRY_GT": "グアテマラ共和国",
    "COUNTRY_GU": "グアム",
    "COUNTRY_GW": "ギニアビサウ共和国",
    "COUNTRY_GY": "ガイアナ共和国",
    "COUNTRY_HK": "香港",
    "COUNTRY_HM": "ハード島およびマクドナルド諸島",
    "COUNTRY_HN": "ホンジュラス共和国",
    "COUNTRY_HR": "クロアチア共和国",
    "COUNTRY_HT": "ハイチ共和国",
    "COUNTRY_HU": "ハンガリー",
    "COUNTRY_HV": "オートボルタ",
    "COUNTRY_IB": "WIPO国際事務局",
    "COUNTRY_IC": "カナリア諸島",
    "COUNTRY_ID": "インドネシア共和国",
    "COUNTRY_IE": "アイルランド",
    "COUNTRY_IL": "イスラエル国",
    "COUNTRY_IM": "マン島",
    "COUNTRY_IN": "インド共和国",
    "COUNTRY_IO": "イギリス領インド洋地域",
    "COUNTRY_IQ": "イラク共和国",
    "COUNTRY_IR": "イラン",
    "COUNTRY_IS": "アイスランド共和国",
    "COUNTRY_IT": "イタリア共和国",
    "COUNTRY_JA": "ジャマイカ",
    "COUNTRY_JE": "ジャージー島",
    "COUNTRY_JM": "ジャマイカ",
    "COUNTRY_JO": "ヨルダン・ハシミテ王国",
    "COUNTRY_JP": "日本",
    "COUNTRY_JT": "ジョンストン島",
    "COUNTRY_KE": "ケニア共和国",
    "COUNTRY_KG": "キルギス共和国",
    "COUNTRY_KH": "カンボジア王国",
    "COUNTRY_KI": "キリバス共和国",
    "COUNTRY_KM": "コモロ",
    "COUNTRY_KN": "セントキッツ・ネイビス連邦",
    "COUNTRY_KP": "北朝鮮",
    "COUNTRY_KR": "大韓民国",
    "COUNTRY_KW": "クウェート国",
    "COUNTRY_KY": "ケイマン諸島",
    "COUNTRY_KZ": "カザフスタン共和国",
    "COUNTRY_LA": "ラオス",
    "COUNTRY_LB": "レバノン共和国",
    "COUNTRY_LC": "セントルシア",
    "COUNTRY_LF": "リビア フェザーン",
    "COUNTRY_LI": "リヒテンシュタイン公国",
    "COUNTRY_LK": "スリランカ民主社会主義共和国",
    "COUNTRY_LR": "リベリア共和国",
    "COUNTRY_LS": "レソト王国",
    "COUNTRY_LT": "リトアニア共和国",
    "COUNTRY_LU": "ルクセンブルク大公国",
    "COUNTRY_LV": "ラトビア共和国",
    "COUNTRY_LY": "リビア国",
    "COUNTRY_MA": "モロッコ王国",
    "COUNTRY_MC": "モナコ公国",
    "COUNTRY_MD": "モルドバ共和国",
    "COUNTRY_ME": "モンテネグロ",
    "COUNTRY_MF": "セント・マーチン島",
    "COUNTRY_MG": "マダガスカル共和国",
    "COUNTRY_MH": "マーシャル諸島共和国",
    "COUNTRY_MI": "ミッドウェー島",
    "COUNTRY_MK": "マケドニア共和国",
    "COUNTRY_ML": "マリ共和国",
    "COUNTRY_MM": "ミャンマー連邦共和国",
    "COUNTRY_MN": "モンゴル国",
    "COUNTRY_MO": "マカオ",
    "COUNTRY_MP": "北マリアナ諸島",
    "COUNTRY_MQ": "マルティニーク",
    "COUNTRY_MR": "モーリタニア・イスラム共和国",
    "COUNTRY_MS": "モントセラト",
    "COUNTRY_MT": "マルタ共和国",
    "COUNTRY_MU": "モーリシャス共和国",
    "COUNTRY_MV": "モルディブ共和国",
    "COUNTRY_MW": "マラウイ共和国",
    "COUNTRY_MX": "メキシコ合衆国",
    "COUNTRY_MY": "マレーシア",
    "COUNTRY_MZ": "モザンビーク共和国",
    "COUNTRY_NA": "ナミビア共和国",
    "COUNTRY_NC": "ニューカレドニア",
    "COUNTRY_NE": "ニジェール共和国",
    "COUNTRY_NF": "ノーフォーク島",
    "COUNTRY_NG": "ナイジェリア連邦共和国",
    "COUNTRY_NH": "ニューヘブリディーズ諸島",
    "COUNTRY_NI": "ニカラグア共和国",
    "COUNTRY_NL": "オランダ",
    "COUNTRY_NO": "ノルウェー王国",
    "COUNTRY_NP": "ネパール連邦民主共和国",
    "COUNTRY_NQ": "ドローニング・モード・ランド",
    "COUNTRY_NR": "ナウル共和国",
    "COUNTRY_NT": "ニュートラルゾーン",
    "COUNTRY_NU": "ニウエ",
    "COUNTRY_NZ": "ニュージーランド",
    "COUNTRY_O1": "その他",
    "COUNTRY_OA": "アフリカ広域知的財産機関",
    "COUNTRY_OM": "オマーン国",
    "COUNTRY_OPTIONAL": "国(省略可)",
    "COUNTRY_PA": "パナマ共和国",
    "COUNTRY_PC": "太平洋諸島信託統治領",
    "COUNTRY_PE": "ペルー共和国",
    "COUNTRY_PF": "フランス領ポリネシア",
    "COUNTRY_PG": "パプアニューギニア独立国",
    "COUNTRY_PH": "フィリピン共和国",
    "COUNTRY_PI": "フィリピン共和国",
    "COUNTRY_PK": "パキスタン・イスラム共和国",
    "COUNTRY_PL": "ポーランド共和国",
    "COUNTRY_PM": "サンピエール島・ミクロン島",
    "COUNTRY_PN": "ピトケアン諸島",
    "COUNTRY_PR": "プエルトリコ自治連邦区",
    "COUNTRY_PS": "パレスチナ自治区",
    "COUNTRY_PT": "ポルトガル共和国",
    "COUNTRY_PU": "米領太平洋諸島",
    "COUNTRY_PW": "パラオ共和国",
    "COUNTRY_PY": "パラグアイ共和国",
    "COUNTRY_PZ": "パナマ運河地帯",
    "COUNTRY_QA": "カタール国",
    "COUNTRY_RA": "アルゼンチン",
    "COUNTRY_RB": "ボリビア多民族国",
    "COUNTRY_RC": "中国",
    "COUNTRY_RE": "レユニオン",
    "COUNTRY_RH": "ハイチ共和国",
    "COUNTRY_RI": "インドネシア共和国",
    "COUNTRY_RL": "レバノン共和国",
    "COUNTRY_RM": "マダガスカル共和国",
    "COUNTRY_RN": "ニジェール共和国",
    "COUNTRY_RO": "ルーマニア",
    "COUNTRY_RP": "フィリピン共和国",
    "COUNTRY_RS": "セルビア共和国",
    "COUNTRY_RU": "ロシア連邦",
    "COUNTRY_RW": "ルワンダ共和国",
    "COUNTRY_SA": "サウジアラビア王国",
    "COUNTRY_SB": "ソロモン諸島",
    "COUNTRY_SC": "セーシェル共和国",
    "COUNTRY_SD": "スーダン共和国",
    "COUNTRY_SE": "スウェーデン王国",
    "COUNTRY_SF": "フィンランド共和国",
    "COUNTRY_SG": "シンガポール共和国",
    "COUNTRY_SH": "セントヘレナ",
    "COUNTRY_SI": "スロヴェニア共和国",
    "COUNTRY_SJ": "スヴァールバル諸島およびヤンマイエン島",
    "COUNTRY_SK": "スロバキア共和国",
    "COUNTRY_SL": "シエラレオネ共和国",
    "COUNTRY_SM": "サンマリノ共和国",
    "COUNTRY_SN": "セネガル共和国",
    "COUNTRY_SO": "ソマリア連邦共和国",
    "COUNTRY_SR": "スリナム共和国",
    "COUNTRY_SS": "南スーダン共和国",
    "COUNTRY_ST": "サントメ・プリンシペ民主共和国",
    "COUNTRY_SU": "ソビエト社会主義共和国連邦",
    "COUNTRY_SV": "エルサルバドル共和国",
    "COUNTRY_SX": "セント・マーチン島(ドイツ領)",
    "COUNTRY_SY": "シリア・アラブ共和国",
    "COUNTRY_SZ": "スワジランド王国",
    "COUNTRY_TA": "トリスタンダクーニャ",
    "COUNTRY_TC": "タークス・カイコス諸島",
    "COUNTRY_TD": "チャド共和国",
    "COUNTRY_TF": "フランス領南方・南極地域",
    "COUNTRY_TG": "トーゴ共和国",
    "COUNTRY_TH": "タイ王国",
    "COUNTRY_TJ": "タジキスタン共和国",
    "COUNTRY_TK": "トケラウ",
    "COUNTRY_TL": "東ティモール民主共和国",
    "COUNTRY_TM": "トルクメニスタン",
    "COUNTRY_TN": "チュニジア共和国",
    "COUNTRY_TO": "トンガ王国",
    "COUNTRY_TP": "東ティモール民主共和国",
    "COUNTRY_TR": "トルコ共和国",
    "COUNTRY_TT": "トリニダード・トバゴ",
    "COUNTRY_TV": "ツバル",
    "COUNTRY_TW": "台湾",
    "COUNTRY_TZ": "タンザニア連合共和国",
    "COUNTRY_UA": "ウクライナ",
    "COUNTRY_UG": "ウガンダ共和国",
    "COUNTRY_UK": "英国",
    "COUNTRY_UM": "合衆国領有小離島",
    "COUNTRY_US": "アメリカ合衆国",
    "COUNTRY_USA": "アメリカ合衆国",
    "COUNTRY_UY": "ウルグアイ東方共和国",
    "COUNTRY_UZ": "ウズベキスタン共和国",
    "COUNTRY_VA": "聖座(バチカン市国)",
    "COUNTRY_VC": "セントビンセントおよびグレナディーン諸島",
    "COUNTRY_VD": "ベトナム民主共和国",
    "COUNTRY_VE": "ベネズエラ・ボリバル共和国",
    "COUNTRY_VG": "ヴァージン諸島(イギリス領)",
    "COUNTRY_VI": "ヴァージン諸島(アメリカ領)",
    "COUNTRY_VN": "ベトナム社会主義共和国",
    "COUNTRY_VU": "バヌアツ共和国",
    "COUNTRY_WF": "ウォリス・フツナ",
    "COUNTRY_WG": "グレナダ",
    "COUNTRY_WK": "ウェーク島",
    "COUNTRY_WL": "セントルシア",
    "COUNTRY_WO": "世界知的所有権機関",
    "COUNTRY_WS": "サモア独立国",
    "COUNTRY_WV": "セントビンセント島",
    "COUNTRY_YD": "イエメン人民民主共和国",
    "COUNTRY_YE": "イエメン共和国",
    "COUNTRY_YT": "マヨット",
    "COUNTRY_YU": "ユーゴスラビア",
    "COUNTRY_YV": "ベネズエラ・ボリバル共和国",
    "COUNTRY_ZA": "南アフリカ共和国",
    "COUNTRY_ZM": "ザンビア共和国",
    "COUNTRY_ZR": "ザイール共和国",
    "COUNTRY_ZW": "ジンバブエ共和国",
    "COUNTRY": "国",
    "CREATE_A_NEW_GROUP": "新規グループを作成",
    "CREATE_A_NEW_TEST": "新しいテストを作成",
    "CREATE_COMPLETE": "作成完了",
    "CREATE_IN_PROGRESS": "作成中",
    "CREATE_TEST_TEXT": "テストの名前、説明、宛先、プロトコル(HTTP/HTTPS)を入力して、テストを作成します。作成したテストは、オンデマンドで実行できます。このテストを実行すると、入力されたプロトコルを使用して宛先へのcurlコマンドがシミュレートされます。トランザクションは、転送ログ、ファイアウォール ログ、Webログなどで確認できます。",
    "CREATE_TEST": "テストを作成",
    "CREATE": "作成",
    "CREATING": "作成",
    "CREDENTIALS_INFO": "Azureサブスクリプションにアクセスするには、Azureサービス プリンシパルの資格情報を入力します。複数のサブスクリプションに1つのサービス プリンシパルを使用することも、サブスクリプションごとにサービス プリンシパルを用意することもできます。サービス プリンシパルに{1}アクセス権限{2}があることを確認してください。",
    "CREDENTIALS_TEXT": "Azureアカウントにアクセスするには、Azureアカウントの資格情報を入力します。",
    "CREDENTIALS": "資格情報",
    "CRITERIA_TEXT": "条件",
    "CRITERIA": "条件",
    "CROATIA_EUROPE_ZAGREB": "ヨーロッパ/ザグレブ",
    "CROATIA": "クロアチア共和国",
    "CRYPTOMINING": "クリプトマイニング",
    "CTL_CONNECTION_FAIL": "SVPNコントロール接続に失敗しました。",
    "CTL_GW_CONN_CLOSE": "コントロール ゲートウェイのアクティブな接続が閉じられました。",
    "CTL_GW_CONN_SETUP_FAIL": "コントロール ゲートウェイ接続のセットアップに失敗しました(内部エラー)。",
    "CTL_GW_CONNECT_FAIL": "コントロール ゲートウェイ接続に失敗しました(ネットワークエラー)。",
    "CTL_GW_DNS_RESOLVE_FAIL": "コントロール ゲートウェイのDNS解決に失敗しました。",
    "CTL_GW_KA_FAIL": "コントロール ゲートウェイ接続のキープアライブに失敗しました。",
    "CTL_GW_NO_CONN": "コントロール ゲートウェイ接続はクライアントによってまだ開始されていません。",
    "CTL_GW_PAC_RESOLVE_FAIL": "コントロール ゲートウェイPACの解決に失敗しました。",
    "CTL_GW_PAC_RESOLVE_NOIP": "コントロール ゲートウェイPACの解決でIPSが返されませんでした。",
    "CTL_GW_PROTO_MSG_ERROR": "コントロールGWレスポンスのメッセージ形式エラー。",
    "CTL_GW_SRV_ERR_RESPONSE": "コントロール ゲートウェイがサーバーからHTTPエラー レスポンスを受信しました。",
    "CTL_GW_UNHEALTHY": "コントロール ゲートウェイが異常です(一時的な状態)。",
    "CTL_KEEAPLIVE_FAIL": "SVPNコントロール接続のキープアライブに失敗しました。",
    "CTL_KEEPALIVE_FAIL": "SVPNコントロール接続のキープアライブに失敗しました。",
    "CUBA_AMERICA_HAVANA": "アメリカ/ハバナ",
    "CUBA": "キューバ共和国",
    "CULT": "カルト",
    "CURRENT_API_KEY": "現在のAPIキー",
    "CURRENT_DAY": "現在の日",
    "CURRENT_MODE": "現在のモード",
    "CURRENT_MONTH": "現在の月",
    "CURRENT_PASSWORD_NOT_VALID": "現在の有効なパスワードを入力してください",
    "CURRENT_PASSWORD": "現在のパスワード",
    "CURRENT_VERSION": "現在のバージョン",
    "CURRENT_WEEK": "現在の週",
    "CURRENTLY_EDITING": "現在編集中",
    "CUSTOM_00": "カスタム カテゴリー0",
    "CUSTOM_01": "カスタム カテゴリー1",
    "CUSTOM_02": "カスタム カテゴリー2",
    "CUSTOM_03": "カスタム カテゴリー3",
    "CUSTOM_04": "カスタム カテゴリー4",
    "CUSTOM_05": "カスタム カテゴリー5",
    "CUSTOM_06": "カスタム カテゴリー6",
    "CUSTOM_07": "カスタム カテゴリー7",
    "CUSTOM_08": "カスタム カテゴリー8",
    "CUSTOM_09": "カスタム カテゴリー9",
    "CUSTOM_10": "カスタム カテゴリー0",
    "CUSTOM_100": "カスタム カテゴリー100",
    "CUSTOM_101": "カスタム カテゴリー101",
    "CUSTOM_102": "カスタム カテゴリー102",
    "CUSTOM_103": "カスタム カテゴリー103",
    "CUSTOM_104": "カスタム カテゴリー104",
    "CUSTOM_105": "カスタム カテゴリー105",
    "CUSTOM_106": "カスタム カテゴリー106",
    "CUSTOM_107": "カスタム カテゴリー107",
    "CUSTOM_108": "カスタム カテゴリー108",
    "CUSTOM_109": "カスタム カテゴリー109",
    "CUSTOM_11": "カスタム カテゴリー11",
    "CUSTOM_110": "カスタム カテゴリー110",
    "CUSTOM_111": "カスタム カテゴリー111",
    "CUSTOM_112": "カスタム カテゴリー112",
    "CUSTOM_113": "カスタム カテゴリー113",
    "CUSTOM_114": "カスタム カテゴリー114",
    "CUSTOM_115": "カスタム カテゴリー115",
    "CUSTOM_116": "カスタム カテゴリー116",
    "CUSTOM_117": "カスタム カテゴリー117",
    "CUSTOM_118": "カスタム カテゴリー118",
    "CUSTOM_119": "カスタム カテゴリー119",
    "CUSTOM_12": "カスタム カテゴリー12",
    "CUSTOM_120": "カスタム カテゴリー120",
    "CUSTOM_121": "カスタム カテゴリー121",
    "CUSTOM_122": "カスタム カテゴリー122",
    "CUSTOM_123": "カスタム カテゴリー123",
    "CUSTOM_124": "カスタム カテゴリー124",
    "CUSTOM_125": "カスタム カテゴリー125",
    "CUSTOM_126": "カスタム カテゴリー126",
    "CUSTOM_127": "カスタム カテゴリー127",
    "CUSTOM_128": "カスタム カテゴリー128",
    "CUSTOM_129": "カスタム カテゴリー129",
    "CUSTOM_13": "カスタム カテゴリー13",
    "CUSTOM_130": "カスタム カテゴリー130",
    "CUSTOM_131": "カスタム カテゴリー131",
    "CUSTOM_132": "カスタム カテゴリー132",
    "CUSTOM_133": "カスタム カテゴリー133",
    "CUSTOM_134": "カスタム カテゴリー134",
    "CUSTOM_135": "カスタム カテゴリー135",
    "CUSTOM_136": "カスタム カテゴリー136",
    "CUSTOM_137": "カスタム カテゴリー137",
    "CUSTOM_138": "カスタム カテゴリー138",
    "CUSTOM_139": "カスタム カテゴリー139",
    "CUSTOM_14": "カスタム カテゴリー14",
    "CUSTOM_140": "カスタム カテゴリー140",
    "CUSTOM_141": "カスタム カテゴリー141",
    "CUSTOM_142": "カスタム カテゴリー142",
    "CUSTOM_143": "カスタム カテゴリー143",
    "CUSTOM_144": "カスタム カテゴリー144",
    "CUSTOM_145": "カスタム カテゴリー145",
    "CUSTOM_146": "カスタム カテゴリー146",
    "CUSTOM_147": "カスタム カテゴリー147",
    "CUSTOM_148": "カスタム カテゴリー148",
    "CUSTOM_149": "カスタム カテゴリー149",
    "CUSTOM_15": "カスタム カテゴリー15",
    "CUSTOM_150": "カスタム カテゴリー150",
    "CUSTOM_151": "カスタム カテゴリー151",
    "CUSTOM_152": "カスタム カテゴリー152",
    "CUSTOM_153": "カスタム カテゴリー153",
    "CUSTOM_154": "カスタム カテゴリー154",
    "CUSTOM_155": "カスタム カテゴリー155",
    "CUSTOM_156": "カスタム カテゴリー156",
    "CUSTOM_157": "カスタム カテゴリー157",
    "CUSTOM_158": "カスタム カテゴリー158",
    "CUSTOM_159": "カスタム カテゴリー159",
    "CUSTOM_16": "カスタム カテゴリー16",
    "CUSTOM_160": "カスタム カテゴリー160",
    "CUSTOM_161": "カスタム カテゴリー161",
    "CUSTOM_162": "カスタム カテゴリー162",
    "CUSTOM_163": "カスタム カテゴリー163",
    "CUSTOM_164": "カスタム カテゴリー164",
    "CUSTOM_165": "カスタム カテゴリー165",
    "CUSTOM_166": "カスタム カテゴリー166",
    "CUSTOM_167": "カスタム カテゴリー167",
    "CUSTOM_168": "カスタム カテゴリー168",
    "CUSTOM_169": "カスタム カテゴリー169",
    "CUSTOM_17": "カスタム カテゴリー17",
    "CUSTOM_170": "カスタム カテゴリー170",
    "CUSTOM_171": "カスタム カテゴリー171",
    "CUSTOM_172": "カスタム カテゴリー172",
    "CUSTOM_173": "カスタム カテゴリー173",
    "CUSTOM_174": "カスタム カテゴリー174",
    "CUSTOM_175": "カスタム カテゴリー175",
    "CUSTOM_176": "カスタム カテゴリー176",
    "CUSTOM_177": "カスタム カテゴリー177",
    "CUSTOM_178": "カスタム カテゴリー178",
    "CUSTOM_179": "カスタム カテゴリー179",
    "CUSTOM_18": "カスタム カテゴリー18",
    "CUSTOM_180": "カスタム カテゴリー180",
    "CUSTOM_181": "カスタム カテゴリー181",
    "CUSTOM_182": "カスタム カテゴリー182",
    "CUSTOM_183": "カスタム カテゴリー183",
    "CUSTOM_184": "カスタム カテゴリー184",
    "CUSTOM_185": "カスタム カテゴリー185",
    "CUSTOM_186": "カスタム カテゴリー186",
    "CUSTOM_187": "カスタム カテゴリー187",
    "CUSTOM_188": "カスタム カテゴリー188",
    "CUSTOM_189": "カスタム カテゴリー189",
    "CUSTOM_19": "カスタム カテゴリー19",
    "CUSTOM_190": "カスタム カテゴリー190",
    "CUSTOM_191": "カスタム カテゴリー191",
    "CUSTOM_192": "カスタム カテゴリー192",
    "CUSTOM_193": "カスタム カテゴリー193",
    "CUSTOM_194": "カスタム カテゴリー194",
    "CUSTOM_195": "カスタム カテゴリー195",
    "CUSTOM_196": "カスタム カテゴリー196",
    "CUSTOM_197": "カスタム カテゴリー197",
    "CUSTOM_198": "カスタム カテゴリー198",
    "CUSTOM_199": "カスタム カテゴリー199",
    "CUSTOM_20": "カスタム カテゴリー20",
    "CUSTOM_200": "カスタム カテゴリー200",
    "CUSTOM_201": "カスタム カテゴリー201",
    "CUSTOM_202": "カスタム カテゴリー202",
    "CUSTOM_203": "カスタム カテゴリー203",
    "CUSTOM_204": "カスタム カテゴリー204",
    "CUSTOM_205": "カスタム カテゴリー205",
    "CUSTOM_206": "カスタム カテゴリー206",
    "CUSTOM_207": "カスタム カテゴリー207",
    "CUSTOM_208": "カスタム カテゴリー208",
    "CUSTOM_209": "カスタム カテゴリー209",
    "CUSTOM_21": "カスタム カテゴリー21",
    "CUSTOM_210": "カスタム カテゴリー210",
    "CUSTOM_211": "カスタム カテゴリー211",
    "CUSTOM_212": "カスタム カテゴリー212",
    "CUSTOM_213": "カスタム カテゴリー213",
    "CUSTOM_214": "カスタム カテゴリー214",
    "CUSTOM_215": "カスタム カテゴリー215",
    "CUSTOM_216": "カスタム カテゴリー216",
    "CUSTOM_217": "カスタム カテゴリー217",
    "CUSTOM_218": "カスタム カテゴリー218",
    "CUSTOM_219": "カスタム カテゴリー219",
    "CUSTOM_22": "カスタム カテゴリー22",
    "CUSTOM_220": "カスタム カテゴリー220",
    "CUSTOM_221": "カスタム カテゴリー221",
    "CUSTOM_222": "カスタム カテゴリー222",
    "CUSTOM_223": "カスタム カテゴリー223",
    "CUSTOM_224": "カスタム カテゴリー224",
    "CUSTOM_225": "カスタム カテゴリー225",
    "CUSTOM_226": "カスタム カテゴリー226",
    "CUSTOM_227": "カスタム カテゴリー227",
    "CUSTOM_228": "カスタム カテゴリー228",
    "CUSTOM_229": "カスタム カテゴリー229",
    "CUSTOM_23": "カスタム カテゴリー23",
    "CUSTOM_230": "カスタム カテゴリー230",
    "CUSTOM_231": "カスタム カテゴリー231",
    "CUSTOM_232": "カスタム カテゴリー232",
    "CUSTOM_233": "カスタム カテゴリー233",
    "CUSTOM_234": "カスタム カテゴリー234",
    "CUSTOM_235": "カスタム カテゴリー235",
    "CUSTOM_236": "カスタム カテゴリー236",
    "CUSTOM_237": "カスタム カテゴリー237",
    "CUSTOM_238": "カスタム カテゴリー238",
    "CUSTOM_239": "カスタム カテゴリー239",
    "CUSTOM_24": "カスタム カテゴリー24",
    "CUSTOM_240": "カスタム カテゴリー240",
    "CUSTOM_241": "カスタム カテゴリー241",
    "CUSTOM_242": "カスタム カテゴリー242",
    "CUSTOM_243": "カスタム カテゴリー243",
    "CUSTOM_244": "カスタム カテゴリー244",
    "CUSTOM_245": "カスタム カテゴリー245",
    "CUSTOM_246": "カスタム カテゴリー246",
    "CUSTOM_247": "カスタム カテゴリー247",
    "CUSTOM_248": "カスタム カテゴリー248",
    "CUSTOM_249": "カスタム カテゴリー249",
    "CUSTOM_25": "カスタム カテゴリー25",
    "CUSTOM_250": "カスタム カテゴリー250",
    "CUSTOM_251": "カスタム カテゴリー251",
    "CUSTOM_252": "カスタム カテゴリー252",
    "CUSTOM_253": "カスタム カテゴリー253",
    "CUSTOM_254": "カスタム カテゴリー254",
    "CUSTOM_255": "カスタム カテゴリー255",
    "CUSTOM_256": "カスタム カテゴリー256",
    "CUSTOM_257": "カスタム カテゴリー257",
    "CUSTOM_258": "カスタム カテゴリー258",
    "CUSTOM_259": "カスタム カテゴリー259",
    "CUSTOM_26": "カスタム カテゴリー26",
    "CUSTOM_260": "カスタム カテゴリー260",
    "CUSTOM_261": "カスタム カテゴリー261",
    "CUSTOM_262": "カスタム カテゴリー262",
    "CUSTOM_263": "カスタム カテゴリー263",
    "CUSTOM_264": "カスタム カテゴリー264",
    "CUSTOM_265": "カスタム カテゴリー265",
    "CUSTOM_266": "カスタム カテゴリー266",
    "CUSTOM_267": "カスタム カテゴリー267",
    "CUSTOM_268": "カスタム カテゴリー268",
    "CUSTOM_269": "カスタム カテゴリー269",
    "CUSTOM_27": "カスタム カテゴリー27",
    "CUSTOM_270": "カスタム カテゴリー270",
    "CUSTOM_271": "カスタム カテゴリー271",
    "CUSTOM_272": "カスタム カテゴリー272",
    "CUSTOM_273": "カスタム カテゴリー273",
    "CUSTOM_274": "カスタム カテゴリー274",
    "CUSTOM_275": "カスタム カテゴリー275",
    "CUSTOM_276": "カスタム カテゴリー276",
    "CUSTOM_277": "カスタム カテゴリー277",
    "CUSTOM_278": "カスタム カテゴリー278",
    "CUSTOM_279": "カスタム カテゴリー279",
    "CUSTOM_28": "カスタム カテゴリー28",
    "CUSTOM_280": "カスタム カテゴリー280",
    "CUSTOM_281": "カスタム カテゴリー281",
    "CUSTOM_282": "カスタム カテゴリー282",
    "CUSTOM_283": "カスタム カテゴリー283",
    "CUSTOM_284": "カスタム カテゴリー284",
    "CUSTOM_285": "カスタム カテゴリー285",
    "CUSTOM_286": "カスタム カテゴリー286",
    "CUSTOM_287": "カスタム カテゴリー287",
    "CUSTOM_288": "カスタム カテゴリー288",
    "CUSTOM_289": "カスタム カテゴリー289",
    "CUSTOM_29": "カスタム カテゴリー29",
    "CUSTOM_290": "カスタム カテゴリー290",
    "CUSTOM_291": "カスタム カテゴリー291",
    "CUSTOM_292": "カスタム カテゴリー292",
    "CUSTOM_293": "カスタム カテゴリー293",
    "CUSTOM_294": "カスタム カテゴリー294",
    "CUSTOM_295": "カスタム カテゴリー295",
    "CUSTOM_296": "カスタム カテゴリー296",
    "CUSTOM_297": "カスタム カテゴリー297",
    "CUSTOM_298": "カスタム カテゴリー298",
    "CUSTOM_299": "カスタム カテゴリー299",
    "CUSTOM_30": "カスタム カテゴリー30",
    "CUSTOM_300": "カスタム カテゴリー300",
    "CUSTOM_301": "カスタム カテゴリー301",
    "CUSTOM_302": "カスタム カテゴリー302",
    "CUSTOM_303": "カスタム カテゴリー303",
    "CUSTOM_304": "カスタム カテゴリー304",
    "CUSTOM_305": "カスタム カテゴリー305",
    "CUSTOM_306": "カスタム カテゴリー306",
    "CUSTOM_307": "カスタム カテゴリー307",
    "CUSTOM_308": "カスタム カテゴリー308",
    "CUSTOM_309": "カスタム カテゴリー309",
    "CUSTOM_31": "カスタム カテゴリー31",
    "CUSTOM_310": "カスタム カテゴリー310",
    "CUSTOM_311": "カスタム カテゴリー311",
    "CUSTOM_312": "カスタム カテゴリー312",
    "CUSTOM_313": "カスタム カテゴリー313",
    "CUSTOM_314": "カスタム カテゴリー314",
    "CUSTOM_315": "カスタム カテゴリー315",
    "CUSTOM_316": "カスタム カテゴリー316",
    "CUSTOM_317": "カスタム カテゴリー317",
    "CUSTOM_318": "カスタム カテゴリー318",
    "CUSTOM_319": "カスタム カテゴリー319",
    "CUSTOM_32": "カスタム カテゴリー32",
    "CUSTOM_320": "カスタム カテゴリー320",
    "CUSTOM_321": "カスタム カテゴリー321",
    "CUSTOM_322": "カスタム カテゴリー322",
    "CUSTOM_323": "カスタム カテゴリー323",
    "CUSTOM_324": "カスタム カテゴリー324",
    "CUSTOM_325": "カスタム カテゴリー325",
    "CUSTOM_326": "カスタム カテゴリー326",
    "CUSTOM_327": "カスタム カテゴリー327",
    "CUSTOM_328": "カスタム カテゴリー328",
    "CUSTOM_329": "カスタム カテゴリー329",
    "CUSTOM_33": "カスタム カテゴリー33",
    "CUSTOM_330": "カスタム カテゴリー330",
    "CUSTOM_331": "カスタム カテゴリー331",
    "CUSTOM_332": "カスタム カテゴリー332",
    "CUSTOM_333": "カスタム カテゴリー333",
    "CUSTOM_334": "カスタム カテゴリー334",
    "CUSTOM_335": "カスタム カテゴリー335",
    "CUSTOM_336": "カスタム カテゴリー336",
    "CUSTOM_337": "カスタム カテゴリー337",
    "CUSTOM_338": "カスタム カテゴリー338",
    "CUSTOM_339": "カスタム カテゴリー339",
    "CUSTOM_34": "カスタム カテゴリー34",
    "CUSTOM_340": "カスタム カテゴリー340",
    "CUSTOM_341": "カスタム カテゴリー341",
    "CUSTOM_342": "カスタム カテゴリー342",
    "CUSTOM_343": "カスタム カテゴリー343",
    "CUSTOM_344": "カスタム カテゴリー344",
    "CUSTOM_345": "カスタム カテゴリー345",
    "CUSTOM_346": "カスタム カテゴリー346",
    "CUSTOM_347": "カスタム カテゴリー347",
    "CUSTOM_348": "カスタム カテゴリー348",
    "CUSTOM_349": "カスタム カテゴリー349",
    "CUSTOM_35": "カスタム カテゴリー35",
    "CUSTOM_350": "カスタム カテゴリー350",
    "CUSTOM_351": "カスタム カテゴリー351",
    "CUSTOM_352": "カスタム カテゴリー352",
    "CUSTOM_353": "カスタム カテゴリー353",
    "CUSTOM_354": "カスタム カテゴリー354",
    "CUSTOM_355": "カスタム カテゴリー355",
    "CUSTOM_356": "カスタム カテゴリー356",
    "CUSTOM_357": "カスタム カテゴリー357",
    "CUSTOM_358": "カスタム カテゴリー358",
    "CUSTOM_359": "カスタム カテゴリー359",
    "CUSTOM_36": "カスタム カテゴリー36",
    "CUSTOM_360": "カスタム カテゴリー360",
    "CUSTOM_361": "カスタム カテゴリー361",
    "CUSTOM_362": "カスタム カテゴリー362",
    "CUSTOM_363": "カスタム カテゴリー363",
    "CUSTOM_364": "カスタム カテゴリー364",
    "CUSTOM_365": "カスタム カテゴリー365",
    "CUSTOM_366": "カスタム カテゴリー366",
    "CUSTOM_367": "カスタム カテゴリー367",
    "CUSTOM_368": "カスタム カテゴリー368",
    "CUSTOM_369": "カスタム カテゴリー369",
    "CUSTOM_37": "カスタム カテゴリー37",
    "CUSTOM_370": "カスタム カテゴリー370",
    "CUSTOM_371": "カスタム カテゴリー371",
    "CUSTOM_372": "カスタム カテゴリー372",
    "CUSTOM_373": "カスタム カテゴリー373",
    "CUSTOM_374": "カスタム カテゴリー374",
    "CUSTOM_375": "カスタム カテゴリー375",
    "CUSTOM_376": "カスタム カテゴリー376",
    "CUSTOM_377": "カスタム カテゴリー377",
    "CUSTOM_378": "カスタム カテゴリー378",
    "CUSTOM_379": "カスタム カテゴリー379",
    "CUSTOM_38": "カスタム カテゴリー38",
    "CUSTOM_380": "カスタム カテゴリー380",
    "CUSTOM_381": "カスタム カテゴリー381",
    "CUSTOM_382": "カスタム カテゴリー382",
    "CUSTOM_383": "カスタム カテゴリー383",
    "CUSTOM_384": "カスタム カテゴリー384",
    "CUSTOM_385": "カスタム カテゴリー385",
    "CUSTOM_386": "カスタム カテゴリー386",
    "CUSTOM_387": "カスタム カテゴリー387",
    "CUSTOM_388": "カスタム カテゴリー388",
    "CUSTOM_389": "カスタム カテゴリー389",
    "CUSTOM_39": "カスタム カテゴリー39",
    "CUSTOM_390": "カスタム カテゴリー390",
    "CUSTOM_391": "カスタム カテゴリー391",
    "CUSTOM_392": "カスタム カテゴリー392",
    "CUSTOM_393": "カスタム カテゴリー393",
    "CUSTOM_394": "カスタム カテゴリー394",
    "CUSTOM_395": "カスタム カテゴリー395",
    "CUSTOM_396": "カスタム カテゴリー396",
    "CUSTOM_397": "カスタム カテゴリー397",
    "CUSTOM_398": "カスタム カテゴリー398",
    "CUSTOM_399": "カスタム カテゴリー399",
    "CUSTOM_40": "カスタム カテゴリー40",
    "CUSTOM_400": "カスタム カテゴリー400",
    "CUSTOM_401": "カスタム カテゴリー401",
    "CUSTOM_402": "カスタム カテゴリー402",
    "CUSTOM_403": "カスタム カテゴリー403",
    "CUSTOM_404": "カスタム カテゴリー404",
    "CUSTOM_405": "カスタム カテゴリー405",
    "CUSTOM_406": "カスタム カテゴリー406",
    "CUSTOM_407": "カスタム カテゴリー407",
    "CUSTOM_408": "カスタム カテゴリー408",
    "CUSTOM_409": "カスタム カテゴリー409",
    "CUSTOM_41": "カスタム カテゴリー41",
    "CUSTOM_410": "カスタム カテゴリー410",
    "CUSTOM_411": "カスタム カテゴリー411",
    "CUSTOM_412": "カスタム カテゴリー412",
    "CUSTOM_413": "カスタム カテゴリー413",
    "CUSTOM_414": "カスタム カテゴリー414",
    "CUSTOM_415": "カスタム カテゴリー415",
    "CUSTOM_416": "カスタム カテゴリー416",
    "CUSTOM_417": "カスタム カテゴリー417",
    "CUSTOM_418": "カスタム カテゴリー418",
    "CUSTOM_419": "カスタム カテゴリー419",
    "CUSTOM_42": "カスタム カテゴリー42",
    "CUSTOM_420": "カスタム カテゴリー420",
    "CUSTOM_421": "カスタム カテゴリー421",
    "CUSTOM_422": "カスタム カテゴリー422",
    "CUSTOM_423": "カスタム カテゴリー423",
    "CUSTOM_424": "カスタム カテゴリー424",
    "CUSTOM_425": "カスタム カテゴリー425",
    "CUSTOM_426": "カスタム カテゴリー426",
    "CUSTOM_427": "カスタム カテゴリー427",
    "CUSTOM_428": "カスタム カテゴリー428",
    "CUSTOM_429": "カスタム カテゴリー429",
    "CUSTOM_43": "カスタム カテゴリー43",
    "CUSTOM_430": "カスタム カテゴリー430",
    "CUSTOM_431": "カスタム カテゴリー431",
    "CUSTOM_432": "カスタム カテゴリー432",
    "CUSTOM_433": "カスタム カテゴリー433",
    "CUSTOM_434": "カスタム カテゴリー434",
    "CUSTOM_435": "カスタム カテゴリー435",
    "CUSTOM_436": "カスタム カテゴリー436",
    "CUSTOM_437": "カスタム カテゴリー437",
    "CUSTOM_438": "カスタム カテゴリー438",
    "CUSTOM_439": "カスタム カテゴリー439",
    "CUSTOM_44": "カスタム カテゴリー44",
    "CUSTOM_440": "カスタム カテゴリー440",
    "CUSTOM_441": "カスタム カテゴリー441",
    "CUSTOM_442": "カスタム カテゴリー442",
    "CUSTOM_443": "カスタム カテゴリー443",
    "CUSTOM_444": "カスタム カテゴリー444",
    "CUSTOM_445": "カスタム カテゴリー445",
    "CUSTOM_446": "カスタム カテゴリー446",
    "CUSTOM_447": "カスタム カテゴリー447",
    "CUSTOM_448": "カスタム カテゴリー448",
    "CUSTOM_449": "カスタム カテゴリー449",
    "CUSTOM_45": "カスタム カテゴリー45",
    "CUSTOM_450": "カスタム カテゴリー450",
    "CUSTOM_451": "カスタム カテゴリー451",
    "CUSTOM_452": "カスタム カテゴリー452",
    "CUSTOM_453": "カスタム カテゴリー453",
    "CUSTOM_454": "カスタム カテゴリー454",
    "CUSTOM_455": "カスタム カテゴリー455",
    "CUSTOM_456": "カスタム カテゴリー456",
    "CUSTOM_457": "カスタム カテゴリー457",
    "CUSTOM_458": "カスタム カテゴリー458",
    "CUSTOM_459": "カスタム カテゴリー459",
    "CUSTOM_46": "カスタム カテゴリー46",
    "CUSTOM_460": "カスタム カテゴリー460",
    "CUSTOM_461": "カスタム カテゴリー461",
    "CUSTOM_462": "カスタム カテゴリー462",
    "CUSTOM_463": "カスタム カテゴリー463",
    "CUSTOM_464": "カスタム カテゴリー464",
    "CUSTOM_465": "カスタム カテゴリー465",
    "CUSTOM_466": "カスタム カテゴリー466",
    "CUSTOM_467": "カスタム カテゴリー467",
    "CUSTOM_468": "カスタム カテゴリー468",
    "CUSTOM_469": "カスタム カテゴリー469",
    "CUSTOM_47": "カスタム カテゴリー47",
    "CUSTOM_470": "カスタム カテゴリー470",
    "CUSTOM_471": "カスタム カテゴリー471",
    "CUSTOM_472": "カスタム カテゴリー472",
    "CUSTOM_473": "カスタム カテゴリー473",
    "CUSTOM_474": "カスタム カテゴリー474",
    "CUSTOM_475": "カスタム カテゴリー475",
    "CUSTOM_476": "カスタム カテゴリー476",
    "CUSTOM_477": "カスタム カテゴリー477",
    "CUSTOM_478": "カスタム カテゴリー478",
    "CUSTOM_479": "カスタム カテゴリー479",
    "CUSTOM_48": "カスタム カテゴリー48",
    "CUSTOM_480": "カスタム カテゴリー480",
    "CUSTOM_481": "カスタム カテゴリー481",
    "CUSTOM_482": "カスタム カテゴリー482",
    "CUSTOM_483": "カスタム カテゴリー483",
    "CUSTOM_484": "カスタム カテゴリー484",
    "CUSTOM_485": "カスタム カテゴリー485",
    "CUSTOM_486": "カスタム カテゴリー486",
    "CUSTOM_487": "カスタム カテゴリー487",
    "CUSTOM_488": "カスタム カテゴリー488",
    "CUSTOM_489": "カスタム カテゴリー489",
    "CUSTOM_49": "カスタム カテゴリー49",
    "CUSTOM_490": "カスタム カテゴリー490",
    "CUSTOM_491": "カスタム カテゴリー491",
    "CUSTOM_492": "カスタム カテゴリー492",
    "CUSTOM_493": "カスタム カテゴリー493",
    "CUSTOM_494": "カスタム カテゴリー494",
    "CUSTOM_495": "カスタム カテゴリー495",
    "CUSTOM_496": "カスタム カテゴリー496",
    "CUSTOM_497": "カスタム カテゴリー497",
    "CUSTOM_498": "カスタム カテゴリー498",
    "CUSTOM_499": "カスタム カテゴリー499",
    "CUSTOM_50": "カスタム カテゴリー50",
    "CUSTOM_500": "カスタム カテゴリー500",
    "CUSTOM_501": "カスタム カテゴリー501",
    "CUSTOM_502": "カスタム カテゴリー502",
    "CUSTOM_503": "カスタム カテゴリー503",
    "CUSTOM_504": "カスタム カテゴリー504",
    "CUSTOM_505": "カスタム カテゴリー505",
    "CUSTOM_506": "カスタム カテゴリー506",
    "CUSTOM_507": "カスタム カテゴリー507",
    "CUSTOM_508": "カスタム カテゴリー508",
    "CUSTOM_509": "カスタム カテゴリー509",
    "CUSTOM_51": "カスタム カテゴリー51",
    "CUSTOM_510": "カスタム カテゴリー510",
    "CUSTOM_511": "カスタム カテゴリー511",
    "CUSTOM_512": "カスタム カテゴリー512",
    "CUSTOM_52": "カスタム カテゴリー52",
    "CUSTOM_53": "カスタム カテゴリー53",
    "CUSTOM_54": "カスタム カテゴリー54",
    "CUSTOM_55": "カスタム カテゴリー55",
    "CUSTOM_56": "カスタム カテゴリー56",
    "CUSTOM_57": "カスタム カテゴリー57",
    "CUSTOM_58": "カスタム カテゴリー58",
    "CUSTOM_59": "カスタム カテゴリー59",
    "CUSTOM_60": "カスタム カテゴリー60",
    "CUSTOM_61": "カスタム カテゴリー61",
    "CUSTOM_62": "カスタム カテゴリー62",
    "CUSTOM_63": "カスタム カテゴリー63",
    "CUSTOM_64": "カスタム カテゴリー64",
    "CUSTOM_65": "カスタム カテゴリー65",
    "CUSTOM_66": "カスタム カテゴリー66",
    "CUSTOM_67": "カスタム カテゴリー67",
    "CUSTOM_68": "カスタム カテゴリー68",
    "CUSTOM_69": "カスタム カテゴリー69",
    "CUSTOM_70": "カスタム カテゴリー70",
    "CUSTOM_71": "カスタム カテゴリー71",
    "CUSTOM_72": "カスタム カテゴリー72",
    "CUSTOM_73": "カスタム カテゴリー73",
    "CUSTOM_74": "カスタム カテゴリー74",
    "CUSTOM_75": "カスタム カテゴリー75",
    "CUSTOM_76": "カスタム カテゴリー76",
    "CUSTOM_77": "カスタム カテゴリー77",
    "CUSTOM_78": "カスタム カテゴリー78",
    "CUSTOM_79": "カスタムカテゴリー79",
    "CUSTOM_80": "カスタム カテゴリー80",
    "CUSTOM_81": "カスタム カテゴリー81",
    "CUSTOM_82": "カスタム カテゴリー82",
    "CUSTOM_83": "カスタム カテゴリー83",
    "CUSTOM_84": "カスタム カテゴリー84",
    "CUSTOM_85": "カスタム カテゴリー85",
    "CUSTOM_86": "カスタムカテゴリー86",
    "CUSTOM_87": "カスタムカテゴリー87",
    "CUSTOM_88": "カスタムカテゴリー88",
    "CUSTOM_89": "カスタムカテゴリー89",
    "CUSTOM_90": "カスタム カテゴリー90",
    "CUSTOM_91": "カスタム カテゴリー91",
    "CUSTOM_92": "カスタム カテゴリー92",
    "CUSTOM_93": "カスタム カテゴリー93",
    "CUSTOM_94": "カスタム カテゴリー94",
    "CUSTOM_95": "カスタム カテゴリー95",
    "CUSTOM_96": "カスタム カテゴリー96",
    "CUSTOM_97": "カスタム カテゴリー97",
    "CUSTOM_98": "カスタム カテゴリー98",
    "CUSTOM_99": "カスタム カテゴリー99",
    "CUSTOM_AUP_FREQUENCY": "カスタムAUPの頻度(日数)",
    "CUSTOM_DNS_SERVER": "カスタムDNSサーバー",
    "CUSTOM_OPTION": "カスタム オプション",
    "CUSTOM": "カスタム",
    "CUSTOMIZE_COLS": "列のカスタマイズ",
    "CUSTOMIZE_COLUMNS": "列のカスタマイズ",
    "CYPRUS_ASIA_NICOSIA": "アジア/ニコシア",
    "CYPRUS": "キプロス共和国",
    "CZECH_REPUBLIC_EUROPE_PRAGUE": "ヨーロッパ/プラハ",
    "CZECH_REPUBLIC": "チェコ共和国",
    "CZECHIA": "チェコ",
    "DASHBOARD": "ダッシュボード",
    "Data Centers": "データ センター",
    "DATA_CENTER": "データ センター",
    "DATA_CENTERS": "データ センター",
    "DATA_COLLECTION": "データ収集",
    "DATA_CONNECTION_FAIL": "SVPNデータ接続に失敗しました。",
    "DATA_TYPE": "データ タイプ",
    "DATACENTER": "データ センター",
    "DAYS": "日",
    "DC": "Public Service Edge",
    "DEDICATED_BANDWIDTH": "専用帯域幅",
    "DEFAUL_GATEWAY_IP_ADDRESS": "デフォルト ゲートウェイのIPアドレス",
    "DEFAULT_AWS_REGIONS": "すべてのリージョン",
    "DEFAULT_AZURE_REGIONS": "なし",
    "DEFAULT_GATEWAY": "デフォルト ゲートウェイ",
    "DEFAULT_GW": "デフォルト ゲートウェイ",
    "DEFAULT_LEASE_TIME": "デフォルトのリース時間(秒)",
    "DEFAULT_NAMESPACE": "デフォルト",
    "DEFAULT_PREFIX": "デフォルトのプレフィックス",
    "DEFAULT_REGIONS": "すべてのロケーション",
    "DEFAULT_ROUTE_CAN_NOT_BE_SET_AS_STATIC_ROUTE": "デフォルト ルートを静的ルートとして使用できませんでした。",
    "DEFAULT": "デフォルト値",
    "DEFINITION": "定義",
    "DELETE_5G_DEPLOYMENT_CONFIGURATION": "この展開構成を削除してもよろしいですか？展開は、どのコネクター グループにも関連付けられていない場合にのみ削除できます。この変更を元に戻すことはできません。 ",
    "DELETE_5G_USER_PLANE": "このユーザー プレーン機能を削除してもよろしいですか？この変更を元に戻すことはできません。 ",
    "DELETE_ACCOUNT_DESCRIPTION": "アカウントを削除すると、Zscalerはアカウントのすべてのアクセス情報を削除します。このアカウントを再度追加する場合は、権限を再度付与する必要があります。",
    "DELETE_ACCOUNT": "アカウントを削除",
    "DELETE_ACCOUNTS": "アカウントを削除",
    "DELETE_ADMIN_MESSAGE": "この管理者をユーザー リストからも削除しますか？{1}この操作は{2}元に戻すことができません。{3}",
    "DELETE_API_KEY_CONFIRMATION_MESSAGE": "削除したAPIキーはすぐに無効になります。この操作は元に戻すことはできません。",
    "DELETE_API_KEY_CONFIRMATION_TITLE": "APIキーの削除",
    "DELETE_API_KEY_TOOLTIP": "APIキーを削除します",
    "DELETE_APPLICATION": "アプリケーションを削除",
    "DELETE_BC_CONFIRMATION": "次のBranch Connectorを削除してもよろしいですか？この変更を元に戻すことはできません。",
    "DELETE_BC_GROUP_CONFIRMATION_ALERT": "この操作を実行してもVMは削除されません。リソースは必ず個別に削除してください。",
    "DELETE_BC_GROUP_CONFIRMATION": "次のBranch Connectorグループを削除してもよろしいですか？この変更を元に戻すことはできません。",
    "DELETE_BC_GROUP": "Branch Connectorグループを削除",
    "DELETE_CC_CONFIRMATION": "次のCloud Connectorを削除してもよろしいですか？この変更を元に戻すことはできません。",
    "DELETE_CC_GROUP_CONFIRMATION_ALERT": "この操作を実行しても、パブリック クラウド内のVMは削除されません。リソースは必ず個別に削除してください。",
    "DELETE_CC_GROUP_CONFIRMATION": "次のCloud Connectorグループを削除してもよろしいですか？この変更を元に戻すことはできません。",
    "DELETE_CC_GROUP": "Cloud Connectorグループを削除",
    "DELETE_CONFIRMATION_MESSAGE": "リソースを削除することを確認してください。",
    "DELETE_CONFIRMATION_MESSAGE1": "このリソースを削除してもよろしいですか？",
    "DELETE_CONFIRMATION_MESSAGE2": "このリソースを削除してもよろしいですか？",
    "DELETE_CONFIRMATION": "削除を確認",
    "DELETE_DATA_OLLECTION_TEXT": "アカウントを削除すると、Zscalerはアカウントのすべてのアクセス情報を削除します。このアカウントを再度追加する場合は、権限を再度付与する必要があります。",
    "DELETE_DATA_OLLECTION": "データ収集を削除",
    "DELETE_GROUP_CONFIRMATION": "グループ削除の確認",
    "DELETE_GROUP_MESSAGE_WITH_CC_GROUP": "このアカウント グループは、{1}個のアカウントと{2}個のCloud Connectorグループに関連付けられています。このアカウント グループを削除すると、{2}個のCloud Connectorグループが{1}個のアカウントのワークロード タグを使用できなくなります。",
    "DELETE_GROUP_MESSAGE": "{1}このグループを削除してもよろしいですか？{2}\n\nこのグループを削除すると、関連するすべてのポリシーからこのグループが削除されます。グループを削除する前に、必ず影響を確認してください。",
    "DELETE_GROUP": "グループを削除",
    "DELETE_INTERFACE_TEXT": "インターフェイス構成を削除してもよろしいですか？\n\nこの操作は元に戻すことができません。",
    "DELETE_INTERFACE": "インターフェイスを削除",
    "DELETE_PORT_TEXT": "ポートとそのすべてのインターフェイス構成を削除してもよろしいですか？\n\nこの操作は元に戻すことができません。",
    "DELETE_PORT": "ポートを削除",
    "DELETE_TENANT_DESCRIPTION": "テナントを削除すると、Zscalerはテナントのすべてのアクセス情報を削除します。このテナントを再度追加する場合は、権限を再度付与する必要があります。",
    "DELETE_TENANT": "テナントを削除",
    "DELETE_THIS_ITEM": "この項目を削除します。",
    "DELETE_VDI_GROUP_TEXT": "デバイス グループを削除すると、Zscalerはこのグループに関連するすべての情報を削除します。このアカウントを再度追加する場合は、グループを再度作成する必要があります。",
    "DELETE_VDI_GROUP": "VDIグループを削除",
    "DELETE_ZERO_TRUST_GATEWAY": "ゼロトラスト ゲートウェイを削除",
    "DELETE_ZTG_DESCRIPTION": "ゼロトラスト ゲートウェイを削除すると、Zscalerはゲートウェイのすべてのアクセス情報を削除します。このゲートウェイを再度追加する場合は、権限を再度付与する必要があります。",
    "DELETE": "削除",
    "DELETING": "削除",
    "DEMOCRATIC_REPUBLIC_OF_CONGO_CONGO_KINSHASA": "アフリカ/キンシャサ",
    "DENIED": "拒否",
    "DENMARK_EUROPE_COPENHAGEN": "ヨーロッパ/コペンハーゲン",
    "DENMARK": "デンマーク",
    "DEPARTMENT": "部署",
    "DEPLOY_AS_GATEWAY": "ゲートウェイとして展開",
    "DEPLOY_NSS_VIRTUAL_APPLIANCE": "NSS仮想アプライアンスを展開",
    "DEPLOYED_FILTERED": "展開済み(フィルター処理済み)",
    "DEPLOYED_OTHER_REGION": "その他の展開済みリージョン",
    "DEPLOYED": "展開済み",
    "DEPLOYMENT_CONFIGURATION_NAME": "展開構成名",
    "DEPLOYMENT_CONFIGURATION_WITH_ZPA": "ロード バランサー付きスターター展開テンプレート",
    "DEPLOYMENT_CONFIGURATION": "スターター展開テンプレート",
    "DEPLOYMENT_DETAILS": "展開の詳細",
    "DEPLOYMENT_NAME": "展開名",
    "DEPLOYMENT_STATUS": "展開のステータス",
    "DEPLOYMENT_TEMPLATES_DEPRECATED": "展開テンプレートは、以下のリンクにあるZscaler {0}GitHub{1}ページで入手できます。変更はこれらのページで追跡されます。詳細は、{2}Zscalerヘルプ ポータル{3}を参照してください。",
    "DEPLOYMENT_TEMPLATES": "展開テンプレート",
    "DEPLOYMENT_TYPE": "展開のタイプ",
    "DESCRIPTION_MAX_LIMIT_ERROR": "このフィールドは10,240文字以内にしてください",
    "DESCRIPTION_OPTIONAL": "説明(省略可)",
    "DESCRIPTION_PARENTHESIS_OPTIONAL": "説明(省略可)",
    "DESCRIPTION": "説明",
    "DESELECT_ALL": "すべて選択解除",
    "DESIRED_CAPACITY": "必要な容量",
    "DESTINATION_ADDRESSES": "宛先アドレス",
    "DESTINATION_COUNTRIES": "宛先の国",
    "DESTINATION_COUNTRY": "宛先の国",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_ACCDRESSES": "宛先FQDN/ドメイン",
    "DESTINATION_FQDN_WILDCARD_DOMAINS_GROUP": "宛先FQDN/ドメイン グループ",
    "DESTINATION_GROUPS": "宛先グループ",
    "DESTINATION_IP_AND_FQDN_ACCDRESSES": "宛先IPアドレス / FQDN",
    "DESTINATION_IP_AND_FQDN_GROUPS": "宛先IP / FQDNグループ",
    "DESTINATION_IP_AND_FQDN": "宛先IP / FQDN",
    "DESTINATION_IP_GROUP": "宛先グループ",
    "DESTINATION_IP_GROUPS": "宛先IPグループ",
    "DESTINATION_IP": "宛先IP",
    "DESTINATION_IPV4_GROUPS": "宛先IPv4グループ",
    "DESTINATION_STATUS": "宛先のステータス",
    "DESTINATION": "宛先",
    "DEVICE_APP_VER": "デバイスのアプリのバージョン",
    "DEVICE_CRITERIA": "デバイスの条件",
    "DEVICE_DETAILS": "デバイスの詳細",
    "DEVICE_GROUP_TYPE": "デバイス グループの種類",
    "DEVICE_HOST_NAME": "デバイスのホスト名",
    "DEVICE_ID": "デバイスID",
    "DEVICE_INFO": "デバイス情報",
    "DEVICE_METRICS": "リソース",
    "DEVICE_MODEL": "デバイス モデル",
    "DEVICE_NAME": "デバイス名",
    "DEVICE_OS_TYPE": "デバイスOSタイプ",
    "DEVICE_OS_VER": "デバイスのOSバージョン",
    "DEVICE_OWNER": "デバイスの所有者",
    "DEVICE_PLATFORM": "デバイス プラットフォーム",
    "DEVICE_PORT": "デバイス ポート",
    "DEVICE_SELECTION": "デバイスの選択",
    "DEVICE_SERIAL_NO": "デバイスのシリアル番号",
    "DEVICE_SERIAL_NUMBER": "デバイスのシリアル番号",
    "DEVICE_TYPE": "デバイス タイプ",
    "DEVICES_CRITERIA_TEXT": "VDIデバイスのグループ化に使用する条件を選択します。",
    "DEVICES_CRITERIA": "デバイスの条件",
    "DEVICES": "デバイス",
    "DEVO": "Devo",
    "DHCP_ADDRESS_RANGE": "DHCPアドレス範囲",
    "DHCP_DESC": "DHCPプロトコルは、ステーションのネットワーク パラメーターを自動的に構成するために使用されます",
    "DHCP_MANAGEMENT_IP": "DHCP管理IP",
    "DHCP_OPTIONS": "DHCPオプション",
    "DHCP_SERVER": "DHCPサーバー",
    "DHCP_SERVICE_IP": "DHCPサービスIP",
    "DHCP": "DHCP",
    "DINING_AND_RESTAURANT": "レストラン",
    "DIRECT_THROUGHPUT_KBPS_SESSION": "直接(スループットkbps / セッション)",
    "DIRECT": "ダイレクト",
    "DIRECTION": "TSの方向",
    "DIRECTORY_ID": "ディレクトリーID",
    "DISABLE_BRANCH_CONNECTOR_CONFIRMATION": "このBranch Connectorを無効にしてもよろしいですか？",
    "DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "このBranch Connectorグループを無効にしてもよろしいですか？これにより、このグループに属する{0}個のBranch Connectorがすべて無効になります",
    "DISABLE_BRANCH_CONNECTOR_GROUP": "Branch Connectorグループを無効化",
    "DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "選択した{0}個のBranch Connectorをすべて無効にしてもよろしいですか？",
    "DISABLE_BRANCH_CONNECTOR_SELECTED": "選択したすべてのBranch Connectorを無効化",
    "DISABLE_BRANCH_CONNECTOR": "Branch Connectorを無効化",
    "DISABLE_CLOUD_CONNECTOR_CONFIRMATION": "このCloud Connectorを無効にしてもよろしいですか？",
    "DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "このCloud Connectorグループを無効にしてもよろしいですか？これにより、このグループに属する{0}個のCloud Connectorsがすべて無効になります",
    "DISABLE_CLOUD_CONNECTOR_GROUP": "Cloud Connectorグループの無効化",
    "DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "選択した{0}個のCloud Connectorsをすべて無効にしてもよろしいですか？",
    "DISABLE_CLOUD_CONNECTOR_SELECTED": "選択したすべてのCloud Connectorsの無効化",
    "DISABLE_CLOUD_CONNECTOR": "Cloud Connectorの無効化",
    "DISABLE_DATA_COLLECTION_DESCRIPTION": "同期を無効にすると、Zscalerは最新のタグ情報をフェッチできなくなります。また、このアカウントのリソースはワークロード グループに含まれなくなります。",
    "DISABLE_DATA_COLLECTION": "データ収集を無効化",
    "DISABLE_POLICY_INFORMATION": "転送情報の無効化",
    "DISABLE_TIPS_MESSAGE": "自分のプロファイル、ポリシー情報を無効化します。",
    "DISABLE": "無効化",
    "DISABLED": "無効",
    "DISABLING": "無効化",
    "DISCOVERY_SERVICE_STATUS": "検出サービスのステータス",
    "DISCUSSION_FORUMS": "掲示板",
    "DISK_STORAGE": "ディスク ストレージ",
    "DISMISS": "閉じる",
    "DISPLAY": "表示",
    "DJIBOUTI_AFRICA_DJIBOUTI": "アフリカ/ジブチ共和国",
    "DJIBOUTI": "ジブチ共和国",
    "DNS_ACTIONS": "アクション",
    "DNS_ACTIVITY": "DNSアクティビティー",
    "DNS_APPLICATION_CATEGORIES": "DNSトンネルとネットワーク アプリのカテゴリー",
    "DNS_APPLICATION_CATEGORY": "DNSアプリケーション カテゴリー",
    "DNS_APPLICATION_GROUP": "DNSアプリケーション グループ",
    "DNS_APPLICATION": "DNSアプリケーション",
    "DNS_BLOCKED_TRAFFIC_OVERVIEW": "ブロックされたDNSトラフィックの概要",
    "DNS_CACHE": "DNSキャッシュ",
    "DNS_CONTROL_RECOMMENDED_POLICY": "推奨されるDNS制御ポリシー",
    "DNS_CONTROL_TIPS_DESC": "DNSのリクエストとレスポンスを制御するルールを定義できます。",
    "DNS_CONTROL_TIPS_TITLE": "DNS制御ポリシーの構成",
    "DNS_CONTROL": "DNS制御",
    "DNS_DESC": " DNSプロトコルはインターネット名(www.site.com)をIPアドレスに、またはその逆に変換するために使用されます",
    "DNS_DESTINATION": "宛先",
    "DNS_DETAILS": "DNSの詳細",
    "DNS_ERROR_CODE": "DNSエラー コード",
    "DNS_ERROR_STATUS": "DNSエラー ステータス",
    "DNS_FILTERING_RULE": "DNSフィルタリング ルール",
    "DNS_FILTERING": "DNSフィルタリング",
    "DNS_FILTERS": "DNSフィルター",
    "DNS_GATEWAY": "DNSゲートウェイ",
    "DNS_INSIGHTS": "DNSインサイト",
    "DNS_IPV6_CHANGE": "dnsへのipv6リクエストの正しい処理を許可",
    "DNS_MONITOR": "DNSモニター",
    "DNS_NETWORK_APPLICATION": "DNSトンネルとネットワーク アプリ",
    "DNS_NW_APP_CATEGORY": "DNSトンネルとネットワーク アプリのカテゴリー",
    "DNS_NW_APP": "DNSトンネルとネットワーク アプリ",
    "DNS_OVER_HTTPS": "DNS Over HTTPSサービス",
    "DNS_OVERVIEW": "DNSの概要",
    "DNS_POLICIES": "DNSポリシー",
    "DNS_POLICY": "DNSポリシー",
    "DNS_REQ_RESP_ACTION": "アクション",
    "DNS_REQ_TYPE": "DNSリクエスト タイプ",
    "DNS_REQUEST_TYPE": "DNSリクエスト タイプ",
    "DNS_REQUEST_TYPES": "DNSリクエスト タイプ",
    "DNS_RES_TYPE": "DNSレスポンス タイプ",
    "DNS_RESOLVED_BY_ZPA": "ZPAによって解決済み",
    "DNS_RESOLVER": "リゾルバー",
    "DNS_RESPONSE_CODES": "DNSレスポンス コード",
    "DNS_RESPONSE_TYPE": "DNSレスポンス タイプ",
    "DNS_RESPONSE": "DNSレスポンス",
    "DNS_RESPONSES": "DNSレスポンス",
    "DNS_RULE_NAME_DEFAULT": "DNS_{0}",
    "DNS_RULE_NAME": "DNSルール名",
    "DNS_RULE": "ルール名",
    "DNS_SERVER_IP_ADDRESS": "DNSサーバーのIPアドレス",
    "DNS_SERVER_IP_ADDRESS1": "DNSサーバーのIPアドレス1",
    "DNS_SERVER_IP_ADDRESS2": "DNSサーバーのIPアドレス2",
    "DNS_SERVER_IP_ADDRESSES": "DNSサーバーのIPアドレス",
    "DNS_SERVER_IP_GROUPS": "DNSサーバーのIPグループ",
    "DNS_SERVER_ONE": "DNSサーバー1",
    "DNS_SERVER_TWO_OPTIONAL": "DNSサーバー2 (省略可)",
    "DNS_SERVER_TWO": "DNSサーバー2",
    "DNS_SERVER": "DNSサーバー",
    "DNS_SERVICES": "DNSサービス",
    "DNS_SOURCE": "送信元",
    "DNS_TCP_PORTS_DNS_SPECIFIC": "DNS固有のルール用のDNS(TCP)ポート",
    "DNS_TIMEOUT": "DNS解決のタイムアウト",
    "DNS_TOP_BLOCKED_BY_LOCATION": "ロケーション別のブロックされたトラフィック",
    "DNS_TOP_BLOCKED_BY_RULE": "ルール別のブロックされたトラフィック",
    "DNS_TOP_BLOCKED_BY_USER": "ユーザー別のブロックされたトラフィック",
    "DNS_TRAFFIC_BY_DEPARTMENT": "部署別のDNSトラフィック",
    "DNS_TRAFFIC_BY_LOCATION": "ロケーション別のトラフィック",
    "DNS_TRAFFIC_BY_USER": "ユーザー別のトラフィック",
    "DNS_TRAFFIC_OVERVIEW": "DNS全体のトラフィックの概要",
    "DNS_TRANSACTION_POLICY": "DNSトランザクション ポリシー",
    "DNS_TRANSACTION_RULE": "DNSトランザクション ルール",
    "DNS_TRANSACTION_TREND": "DNSトランザクションの傾向",
    "DNS_TRANSACTION": "トランザクション",
    "DNS_UDP_PORTS_DNS_SPECIFIC": "DNS固有のルール用のDNS (UDP)ポート",
    "DNS": "DNS",
    "DNSLOG": "DNSログ",
    "DNSREQ_A": "ホスト アドレス",
    "DNSREQ_AAAA": "IP6アドレス",
    "DNSREQ_AFSDB": "AFSデータ ベース ロケーションの場合",
    "DNSREQ_CNAME": "エイリアスの正規名",
    "DNSREQ_DNSKEY": "DNS公開キー",
    "DNSREQ_DS": "委任状署名者",
    "DNSREQ_HINFO": "ホスト情報",
    "DNSREQ_HIP": "ホスト アイデンティティー プロトコル",
    "DNSREQ_ISDN": "ISDNアドレスの場合",
    "DNSREQ_LOC": "ロケーション情報",
    "DNSREQ_MB": "メールボックスのドメイン名",
    "DNSREQ_MG": "メール グループ メンバー",
    "DNSREQ_MINFO": "メールボックスまたはメール リストの情報",
    "DNSREQ_MR": "メールの名前変更ドメイン名",
    "DNSREQ_MX": "メール交換",
    "DNSREQ_NAPTR": "命名権限ポインター",
    "DNSREQ_NS": "認証ネーム サーバー",
    "DNSREQ_NSEC": "DNSセキュリティ拡張機能",
    "DNSREQ_PTR": "ドメイン名ポインター",
    "DNSREQ_RP": "責任者の場合",
    "DNSREQ_RT": "ルート スルーの場合",
    "DNSREQ_SOA": "権限ゾーンの開始をマークします",
    "DNSREQ_SRV": "サーバーを選択",
    "DNSREQ_TXT": "テキスト文字列",
    "DNSREQ_UNKNOWN": "ZSファイアウォールによってマップされていないDNSタイプ",
    "DNSREQ_WKS": "よく知られたサービスの説明",
    "DNSRES_CNAME": "レスポンス タイプはCNAMEです",
    "DNSRES_IPV4": "レスポンス タイプはIPV4です",
    "DNSRES_IPV6": "レスポンス タイプはIPV6です",
    "DNSRES_SRV_CODE": "レスポンス タイプにサーバー エラー コードが設定されています",
    "DNSRES_ZSCODE": "レスポンス タイプにZscalerのカスタムコードが設定されています",
    "DOES_NOT_CONTAINS": "次の値を含まない",
    "DOES_NOT_ENDS_WITH": "次の値で終わらない",
    "DOES_NOT_STARTS_WITH": "次の値で始まらない",
    "DOHTTPS_RULE": "DNS over HTTPS",
    "DOMAIN_CATEGORY": "ドメイン カテゴリー",
    "DOMAIN_NAME": "ドメイン名",
    "DOMAIN": "ドメイン",
    "DOMAINS": "ドメイン",
    "DOMINICA_AMERICA_DOMINICA": "アメリカ/ドミニカ国",
    "DOMINICA": "ドミニカ国",
    "DOMINICAN_REPUBLIC_AMERICA_SANTO_DOMINGO": "アメリカ/サントドミンゴ",
    "DOMINICAN_REPUBLIC": "ドミニカ共和国",
    "DONE": "終了",
    "DONT_SHOW_AGAIN": "次回から表示しない",
    "DOWN": "ダウン",
    "DOWNLOAD_AWS_CLOUDFORMATION_TEMPLATE": "AWS CloudFormationテンプレートをダウンロード",
    "DOWNLOAD_CERTIFICATE": "証明書をダウンロード",
    "DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION": "後で実行するためにCloudFormationテンプレートをダウンロード",
    "DOWNLOAD_CSV": "ダウンロード(.csv)",
    "DOWNLOAD_ERROR": "Zscalerクラウドからアップグレードをダウンロードできません。Cloud Connectorは正常な状態です",
    "DOWNLOAD_MBPS": "ダウンロード(Mbps)",
    "DOWNLOAD_MIB_FILES": "MIBファイルをダウンロード",
    "DOWNLOAD_PROGRESS": "進捗状況をダウンロード",
    "DOWNLOAD": "ダウンロード",
    "DPDRCV": "DPDを受信しました",
    "DR_CONGO": "コンゴ民主共和国",
    "DROP": "ドロップ",
    "DSTN_DOMAIN": "ワイルドカード ドメイン",
    "DSTN_FQDN": "FQDN",
    "DSTN_IP": "IPアドレス",
    "DSTN_OTHER": "その他",
    "DSTN_WILDCARD_FQDN": "ワイルドカード ドメイン",
    "DUPLICATE_IP_ADDRESS": "このIPアドレスはすでに使用されています",
    "DUPLICATE_IP_ADDRESSES": "重複するIPアドレス",
    "DUPLICATE_ITEM": "入力された名前はすでに使用されています",
    "DUPLICATE_VLAN_ID": "VLAN IDが重複しています。",
    "DYNAMIC_DNS": "動的DNSホスト",
    "DYNAMIC_LOCATION_GROUPS": "動的なロケーション グループ",
    "DYNAMIC": "動的",
    "EASTASIA": "(アジア太平洋)東アジア",
    "EASTASIASTAGE": "(アジア太平洋)東アジア(ステージ)",
    "EASTUS": "(米国)米国東部",
    "EASTUS2": "(米国)米国東部2",
    "EASTUS2EUAP": "(米国)米国東部2 EUAP",
    "EASTUS2STAGE": "(米国)米国東部2 (ステージ)",
    "EASTUSSTAGE": "(米国)米国東部(ステージ)",
    "EBS_STORAGE": "EBSストレージ",
    "EC_ACC_ID": "AWSアカウントID",
    "EC_AVAILABILITY_ZONE": "アベイラビリティー ゾーン",
    "EC_AWS_AVAILABILITY_ZONE": "AWSアベイラビリティー ゾーン",
    "EC_AWS_REGION": "AWSリージョン",
    "EC_AZURE_AVAILABILITY_ZONE": "Azure可用性ゾーン",
    "EC_DEVICE_APP_VERSION": "デバイスのアプリのバージョン",
    "EC_DEVICE_HOSTNAME": "デバイスのホスト名",
    "EC_DEVICE_ID": "デバイス名",
    "EC_DEVICE_OS_TYPE": "デバイスOSタイプ",
    "EC_DEVICE_TYPE": "デバイス タイプ",
    "EC_DNS_GW_FLAG": "DNSゲートウェイ フラグ",
    "EC_DNS_GW_NAME": "DNSゲートウェイ名",
    "EC_DNS": "CC DNS",
    "EC_DNSLOG": "DNSログ",
    "EC_EVENTLOG": "イベント",
    "EC_FORWARDING_TYPE": "転送タイプ",
    "EC_FW_RULE": "転送ルール",
    "EC_GROUP": "Cloud Connectorグループ",
    "EC_INSTANCE_NAME": "Cloud Connectorインスタンス",
    "EC_INSTANCE": "Cloud Connectorインスタンス",
    "EC_PLATFORM": "プラットフォーム",
    "EC_PROJECT_ID": "GCPプロジェクトID",
    "EC_RDRRULESLOT": "トラフィック転送ルール",
    "EC_SELFRULESLOT": "ログとコントロール転送ルール",
    "EC_SOURCE_IP": "Cloud Connector送信元IP",
    "EC_SOURCE_PORT": "Cloud Connector送信元ポート",
    "EC_SUBSCRIPTION_ID": "AzureサブスクリプションID",
    "EC_TRAFFIC_DIRECTION": "トラフィックの方向",
    "EC_TRAFFIC_TYPE": "トラフィック タイプ",
    "EC_TS_DIRECTION": "TSの方向",
    "EC_UI": "Cloud Connector UI",
    "EC_VM": "Cloud Connector VM",
    "EC_VMNAME": "Cloud ConnectorのVM名",
    "EC2_INSTANCE_TYPE": "EC2インスタンス タイプ",
    "ECDIRECTSCTPXFORM": "SCTP変換を使用したダイレクト",
    "ECHO_DESC": "Echo ProtocolはRFC 862内で定義されたInternet Protocol Suite内のサービスです。当初、IPネットワーク内での往復時間のテストと測定のために発案されました。",
    "ECHO": "エコー",
    "ECHOREP": "エコー応答",
    "ECHOREQ": "エコー リクエスト",
    "ECHOSIGN": "AdobeEchoSign",
    "ECLOG": "セッション ログ",
    "ECSELF": "Connector発信トラフィックのGW",
    "ECUADOR_AMERICA_GUAYAQUIL": "アメリカ/グアヤキル",
    "ECUADOR_PACIFIC_GALAPAGOS": "太平洋/ガラパゴス",
    "ECUADOR": "エクアドル共和国",
    "ECZPA": "ZPA",
    "ECZPASCTPXFORM": "SCTP変換を使用したZPA",
    "ECZPAXSCTPXFORM": "SCTP変換によるConnector ZPAへのトンネル",
    "EDGE_CONNECTOR_ADMIN_MANAGEMENT": "管理者の管理",
    "EDGE_CONNECTOR_ADMIN": "Cloud Connector管理者",
    "EDGE_CONNECTOR_CCA_DEVICE": "CCA",
    "EDGE_CONNECTOR_CLOUD_PROVISIONING": "Cloud Connectorのプロビジョニング",
    "EDGE_CONNECTOR_DASHBOARD": "ダッシュボード",
    "EDGE_CONNECTOR_FORWARDING": "転送(トラフィック、DNSとログ)",
    "EDGE_CONNECTOR_LOCATION_MANAGEMENT": "ロケーション管理",
    "EDGE_CONNECTOR_NSS_CONFIGURATION": "NSSログの記録",
    "EDGE_CONNECTOR_POLICY_CONFIGURATION": "ポリシー構成",
    "EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": "パブリック クラウド構成管理",
    "EDGE_CONNECTOR_TEMPLATE": "テンプレート(ロケーションとプロビジョニング)",
    "EDGE_CONNECTOR_VM_SIZE": "VMサイズ",
    "EDGE_ONLY": "エッジのみ",
    "EDGECONNECTOR_NAME": "Cloud Connector名",
    "EDIT_5G_DEPLOYMENT": "展開構成を編集",
    "EDIT_API_KEY_TOOLTIP": "APIキーを編集します",
    "EDIT_APPLIANCE": "アプライアンスを編集",
    "EDIT_AWS_ACCOUNT": "AWSアカウントを編集",
    "EDIT_AWS_CLOUD_ACCOUNT": "AWSクラウド アカウントを編集",
    "EDIT_AWS_GROUP": "AWSグループを編集",
    "EDIT_AZURE_CLOUD_ACCOUNT": "Azureクラウド アカウントを編集",
    "EDIT_AZURE_TENANT": "Azureテナントを編集",
    "EDIT_BC_PROVISIONING_TEMPLATE": "Branch Connectorのプロビジョニング テンプレートを編集",
    "EDIT_CLOUD_APP_PROVIDER": "クラウド アプリ プロバイダーを編集",
    "EDIT_CLOUD_CONNECTOR_ADMIN": "Cloud Connector管理者を編集",
    "EDIT_CLOUD_CONNECTOR_ROLE": "Cloud Connectorロールを編集",
    "EDIT_CLOUD_CONNECTOR": "Cloud Connectorを編集",
    "EDIT_CLOUD_CONNECTORS": "コネクターを編集",
    "EDIT_CLOUD_SERVICE_API_KEY": "クラウド サービスAPIキーを編集",
    "EDIT_CONFIRMATION_DEPLOYED_GATEWAY": "このデバイスは展開済みの状態です。構成の変更はトラフィックに影響を与える可能性があります。続行してもよろしいですか？",
    "EDIT_CONFIRMATION_PREDEFINED_RULE": "この定義済みルールは、ゲートウェイ モードのBCグループ/ロケーションにのみ適用されます。",
    "EDIT_CONFIRMATION": "確認を編集",
    "EDIT_CONNECTORS": "コネクターを編集",
    "EDIT_DESTINATION_IP_GROUP": "宛先IPグループを編集",
    "EDIT_DNS_GATEWAY": "DNSゲートウェイを編集",
    "EDIT_DNS_POLICIES": "DNSフィルタリング ルールを編集",
    "EDIT_DYNAMIC_VDI_GROUP": "動的VDIグループを編集",
    "EDIT_EC_NSS_CLOUD_FEED": "NSSクラウド フィードを編集",
    "EDIT_EC_NSS_FEED": "NSSフィードを編集",
    "EDIT_EC_NSS_SERVER": "NSSサーバーを編集",
    "EDIT_EDGECONNECTOR": "Cloud Connectorを編集",
    "EDIT_IP_POOL": "IPプールを編集",
    "EDIT_LOCATION_TEMPLATE": "ロケーション テンプレートを編集",
    "EDIT_LOCATIONS": "ロケーションを編集",
    "EDIT_LOG_AND_CONTROL_FORWARDING_RULE": "ログとコントロール転送ルールを編集",
    "EDIT_LOG_AND_CONTROL_GATEWAY": "ログとコントロール ゲートウェイを編集",
    "EDIT_NETWORK_SERVICE_GROUP": "ネットワーク サービス グループを追加",
    "EDIT_NETWORK_SERVICE": "ネットワーク サービスを編集",
    "EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE": "APIキーを変更すると既存のキーはすぐに無効になります。古いキーへの参照を新しいもので置き換える必要があります。",
    "EDIT_PHYSICAL_BRANCH_DEVICE": "物理ブランチ デバイスを編集",
    "EDIT_PROVISIONING_TEMPLATE": "Cloud Connectorのプロビジョニング テンプレートを編集",
    "EDIT_SOURCE_IP_GROUP": "送信元IPグループを編集",
    "EDIT_TRAFFIC_FWD_POLICIES": "トラフィック転送ルールを編集",
    "EDIT_UPF": "ユーザー プレーン機能を編集",
    "EDIT_VDI_AGENT_FORWARDING_PROFILE": "VDI転送プロファイルを編集",
    "EDIT_VDI_TEMPLATE": "VDIテンプレートを編集",
    "EDIT_VIRTUAL_BRANCH_DEVICE": "仮想ブランチ デバイスを編集",
    "EDIT_ZERO_TRUST_GATEWAY": "ゼロトラスト ゲートウェイを編集",
    "EDIT_ZIA_GATEWAY": "ZIAゲートウェイを編集",
    "EDIT_ZT_DEVICE": "ZTデバイスを編集",
    "EDIT": "編集",
    "EGRESS_DETAILS": "出力の詳細",
    "EGYPT_AFRICA_CAIRO": "アフリカ/カイロ",
    "EGYPT": "エジプト",
    "EITHER_REQ_RESP_BLOCK": "ブロック",
    "EITHER_REQ_RESP_REDIRECT_NO_BLOCK": "リダイレクト",
    "EL_SALVADOR_AMERICA_EL_SALVADOR": "アメリカ/エルサルバドル共和国",
    "EL_SALVADOR": "エルサルバドル共和国",
    "EMAIL_HOST": "Webメール",
    "EMAIL": "メール アドレス",
    "EMPTY_RESP": "DNSレスポンスにエラーはありませんが、空の応答セクションがあります",
    "ENABLE_AUP": "AUPを有効化",
    "ENABLE_BRANCH_CONNECTOR_CONFIRMATION": "このBranch Connectorを有効にしてもよろしいですか？",
    "ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION": "このBranch Connectorグループを有効にしてもよろしいですか？これにより、このグループに属する{0}個のBranch Connectorがすべて有効になります",
    "ENABLE_BRANCH_CONNECTOR_GROUP": "Branch Connectorグループを有効化",
    "ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION": "選択した{0}個のBranch Connectorをすべて有効にしてもよろしいですか？",
    "ENABLE_BRANCH_CONNECTOR_SELECTED": "選択したすべてのBranch Connectorを有効化",
    "ENABLE_BRANCH_CONNECTOR": "Branch Connectorの有効化",
    "ENABLE_CAUTION": "警告を有効化",
    "ENABLE_CLOUD_CONNECTOR_CONFIRMATION": "このCloud Connectorを有効にしてもよろしいですか？",
    "ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION": "このCloud Connectorグループを有効にしてもよろしいですか？これにより、このグループに属する{0}個のCloud Connectorsがすべて有効になります",
    "ENABLE_CLOUD_CONNECTOR_GROUP": "Cloud Connectorグループの有効化",
    "ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION": "選択した{0}個のCloud Connectorsをすべて有効にしてもよろしいですか？",
    "ENABLE_CLOUD_CONNECTOR_SELECTED": "選択したすべてのCloud Connectorsの有効化",
    "ENABLE_CLOUD_CONNECTOR": "Cloud Connectorの有効化",
    "ENABLE_DATA_COLLECTION_DESCRIPTION": "同期を有効にすると、Zscalerは最新のタグ情報をフェッチできるようになります。また、このアカウントのリソースはワークロード グループに含まれるようになります。",
    "ENABLE_DATA_COLLECTION": "データ収集を有効化",
    "ENABLE_FULL_ACCESS": "フル アクセスを有効化",
    "ENABLE_GEO_IP_LOOKUP": "ジオIPルックアップを有効化",
    "ENABLE_IPS_CONTROL": "IPSコントロールを有効化",
    "ENABLE_MOBILE_APP": "Executive Insightsアプリへのアクセス",
    "ENABLE_POLICY_INFORMATION": "転送情報の有効化",
    "ENABLE_SSL_INSPECTION": "SSLインスペクションを有効化",
    "ENABLE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "既知のブラウザーにサロゲートIPを適用",
    "ENABLE_USER_IP_MAPPING": "IPサロゲートを有効化",
    "ENABLE_VIEW_ONLY_ACCESS": "表示専用アクセスを有効化",
    "ENABLE_XFF_FORWARDING": "XFF転送を有効化",
    "ENABLE": "有効化",
    "ENABLED": "有効",
    "ENABLING": "有効化",
    "ENCR_WEB_CONTENT": "カスタム暗号化コンテンツ",
    "ENCRYPTED_DTLS": "DTLS",
    "END_TIME": "終了時刻",
    "END_USER_AUTHENDICATION": "エンド ユーザー認証",
    "ENDPOINT_ID": "エンドポイントID",
    "ENDPOINT_SERVICE_NAME": "エンドポイント サービス名",
    "ENDPOINT_SEVICE_NAME": "エンドポイント サービス名",
    "ENDPOINTS_SEVICE_NAME": "エンドポイント サービス名",
    "ENDPOINTS": "エンドポイント",
    "ENDS_WITH": "次の値で終わる",
    "ENFORCE_AUTHENTICATION": "認証を強制",
    "ENFORCE_BAND_WIDTH_CONTROL": "帯域幅コントロールを強制",
    "ENFORCE_FIREWALL_CONTROL": "ファイアウォール コントロールを強制",
    "ENFORCE_IPS_CONTROL": "IPSコントロールを有効化",
    "ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "既知のブラウザーにサロゲートIPを適用",
    "ENFORCE_ZAPP_SSL_SETTING": "ZApp SSL設定を適用",
    "ENROLLED_DEVICE_APP_VERSION": "登録済みデバイスのアプリのバージョン",
    "ENSURE_ALL_INFORMATION_IS_CORRECT": "このCloud Connectorプロビジョニング テンプレートを作成する前に、以下のすべての情報が正しいことを確認してください。",
    "ENTER_AWS_ACCOUNT_ID": "AWSアカウントIDを入力...",
    "ENTER_CC_ROLE_NAME": "Cloud Connectorロール名を入力...",
    "ENTER_CLOUD_WATCH_GROUP_ARN": "CloudWatchグループARNを入力",
    "ENTER_CUSTOM_OPTION_CODE": "コードを入力します。例：42",
    "ENTER_CUSTOM_OPTION_NAME": "カスタム オプション名を入力します。",
    "ENTER_DESCRIPTION_HERE": "説明を入力(省略可)",
    "ENTER_DESCRIPTION": "説明を入力...",
    "ENTER_DEVICE_NAME": "デバイス名を入力",
    "ENTER_HEADERS_PARAMETERS": "ヘッダー パラメーターを入力",
    "ENTER_HOSTNAME_PREFIX": "ホスト名プレフィックスを入力",
    "ENTER_IP_ADDRESS_OR_FQDN": "IPアドレスまたはFQDNを入力",
    "ENTER_IP_ADDRESS": "IPアドレスを入力...",
    "ENTER_LOG_INFO_TYPE": "ログ情報タイプを入力",
    "ENTER_MTU": "MTUを入力...",
    "ENTER_NAME_HERE": "ここに名前を入力",
    "ENTER_NAME": "名前を入力...",
    "ENTER_NAMESPACE": "名前空間を入力",
    "ENTER_NUMBER": "数値を入力",
    "ENTER_ROLE_NAME": "ロール名を入力...",
    "ENTER_TEXT": "テキストを入力...",
    "ENTER_THE_VALUE": "値を入力してください",
    "ENTER_URL": "URLを入力",
    "ENTERTAINMENT": "エンターテイメント",
    "ENTITLEMENT_STATUS_TOOLTIP": "ゲートウェイのエンタイトルメントは、アカウントがエンタイトルメントを持つアベイラビリティー ゾーン(AZ)の数と現在の使用状況として表示されます。各ゲートウェイは2つ以上のAZを使用します。ゲートウェイで使用されるAZの数は、新しいゲートウェイの作成時に選択します。",
    "ENTITLEMENT_STATUS": "エンタイトルメントのステータス",
    "EQUATORIAL_GUINEA_AFRICA_MALABO": "アフリカ/マラボ",
    "EQUATORIAL_GUINEA": "赤道ギニア共和国",
    "ERITREA_AFRICA_ASMARA": "アフリカ/アスマラ",
    "ERITREA": "エリトリア国",
    "ERROR_ACCOUNT_ID_AND_PROJECT_ID_NOT_ALLOWED_TOGETHER": "GoogleプロジェクトIDをAWSアカウントIDと組み合わせて使用することはできません",
    "ERROR_ACCOUNT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "AzureサブスクリプションIDをAWSアカウントIDと組み合わせて使用することはできません",
    "ERROR_API_MUST_BE_DIFFERENT": "新しいAPIキーを現在のキーと同じにすることはできません",
    "ERROR_BLACKLIST": "拒否リストIPエラー",
    "ERROR_BRANCH_CLOUD_CONNECTORS_GROUP_ERROR": "Cloud ConnectorとBranch Connectorの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_DEFAULT_LEASE_TME_SHOULD_BE_SMALLER_THAN_MAX_LEASE_TIME": "デフォルトのリース時間は、最大リース時間より短くする必要があります。",
    "ERROR_DELETING_ADMIN_MANAGEMENT": "管理者の管理の削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_API_KEY_MANAGEMENT": "APIキーの管理の削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_APPLIANCE": "ZTデバイスの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_CELLULAR_CONFIGURATION": "セルラー構成の削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_CELLULAR_USER_PLANE": "セルラー ユーザー プレーンの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_GATEWAY": "ゲートウェイの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_LOCATION_TEMPLATE": "ロケーション テンプレートの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_LOCATION": "ロケーションの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_PARTNER_ACCOUNT": "パートナー アカウントの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_ROLE_MANAGEMENT": "ロール管理の削除に問題があります。後でもう一度お試しください。",
    "ERROR_DELETING_TESTING": "テストの削除に問題があります。後でもう一度お試しください。",
    "ERROR_DISABLING_PARTNER_ACCOUNT": "パートナー アカウント データ収集の無効化エラー",
    "ERROR_DUPLICATE_DNS_SERVER": "プライマリーDNSとセカンダリーDNSには、それぞれ異なるオプションを選択してください。",
    "ERROR_DUPLICATE_HA_VIRTUAL_ID": "高可用性仮想IDは一意である必要があります。高可用性ID値が重複しています ",
    "ERROR_DUPLICATE_INTERFACE": "インターフェイスを既存のものと同じにすることはできません。",
    "ERROR_DUPLICATE_SUBNETS": "サブネットの重複を確認してください",
    "ERROR_EDITING_API_KEY_MANAGEMENT": "APIキーの管理の編集に問題があります。後でもう一度お試しください。",
    "ERROR_EDITING_TESTING": "テストの編集に問題があります。後でもう一度お試しください。",
    "ERROR_EDITING": "データの編集に問題があります。後でもう一度お試しください。",
    "ERROR_ENABLING_PARTNER_ACCOUNT": "パートナー アカウント データ収集の有効化エラー ",
    "ERROR_HTTP_REQUEST_FAILURE": "HTTPリクエストの失敗がレポートされました。",
    "ERROR_LIST_DNS_SERVER_HAS_DUPLICATE": "DNSサーバーのIPアドレスに重複した値があります。",
    "ERROR_LIST_DNS_SERVER_LIMIT_4": "DNSサーバーのIPアドレスは4つまでです。",
    "ERROR_LIST_DOMAIN_NAME_HAS_DUPLICATE": "ドメイン名の値に重複した値があります。",
    "ERROR_LIST_DOMAIN_NAME_LIMIT_4": "ドメイン名は4つまでです。",
    "ERROR_LOADING_ADMIN_MANAGEMENT": "管理者の管理のデータの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_API_KEY_MANAGEMENT": "APIキー データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_DATA": "データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_DOMAINS": "ドメイン データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_FORWARDING_POLICIES": "転送ポリシー データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_LOCATION_TEMPLATE": "ロケーション テンプレート データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_LOCATIONS": "ロケーション データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING_ROLE_MANAGEMENT": "ロール管理データの読み込みに問題があります。後でもう一度お試しください。",
    "ERROR_LOADING": "データの読み込みにエラーが発生しました。",
    "ERROR_LOOKUP": "ルックアップURLエラー",
    "ERROR_NO_SCHEDULED_VERSION_AVAILABLE": "利用可能なバージョンはありません",
    "ERROR_OCCURRED_WHILE_CREATING_NEW_PASSWORD": "新しいパスワードの作成中にエラーが発生しました。後でもう一度お試しください",
    "ERROR_OCCURRED_WHILE_VERIFYING_PASSWORD": "現在のパスワードの検証中にエラーが発生しました。後でもう一度お試しください",
    "ERROR_OPERATIONAL_STATUS_SAVE_ERROR": "運用ステータスの保存に問題があります。後でもう一度お試しください。",
    "ERROR_PROJECT_ID_AND_SUBSCRIPTION_ID_NOT_ALLOWED_TOGETHER": "AzureサブスクリプションIDをGoogleプロジェクトIDと組み合わせて使用することはできません",
    "ERROR_REGENERATE_API_KEY_MANAGEMENT": "APIキー管理の再生成に問題があります。後でもう一度お試しください。",
    "ERROR_REMOVE_DELETED_SEGMENTS": "削除されたセグメントを含むルールは保存できません。",
    "ERROR_SAVING_GATEWAY": "ゲートウェイの保存に問題があります。後でもう一度お試しください。",
    "ERROR_SAVING_LOCATION": "ロケーションの保存に問題があります。後でもう一度お試しください。",
    "ERROR_SAVING_NETWORK_SERVICES_GROUPS": "ネットワーク サービス グループの保存に問題があります。後でもう一度お試しください。",
    "ERROR_SAVING_NETWORK_SERVICES": "ネットワーク サービスの保存に問題があります。後でもう一度お試しください。",
    "ERROR_SAVING_ROLE_MANAGEMENT": "ロール管理の保存に問題があります。後でもう一度お試しください。",
    "ERROR_SAVING": "データの保存に問題があります。後でもう一度お試しください。",
    "ERROR_TESTING": "テストの実行中にエラーが発生しました。",
    "ERROR_UPDATING_PERMISSION_STATUS": "権限ステータスの更新に問題があります。後でもう一度お試しください。",
    "ERROR": "エラー",
    "ESP_PROTOCOL_DESC": "セキュリティ ペイロードのカプセル化(ESP)は、IPv4およびIPv6ネットワークでネットワーク パケットのペイロードに対して認証、整合性、機密性を提供するIPsec内のプロトコルです。",
    "ESP_PROTOCOL": "ESP",
    "ESTABLISH_SUPPORT_TUNNEL": "サポート トンネルの確立",
    "ESTONIA_EUROPE_TALLINN": "ヨーロッパ/タリン",
    "ESTONIA": "エストニア共和国",
    "ETHIOPIA_AFRICA_ADDIS_ABABA": "アフリカ/アディスアベバ",
    "ETHIOPIA": "エチオピア",
    "EU_CENTRAL_1": "eu-central-1 (フランクフルト)",
    "EU_CENTRAL_1A": "eu-central-1a",
    "EU_CENTRAL_1B": "eu-central-1b",
    "EU_CENTRAL_1C": "eu-central-1c",
    "EU_CENTRAL_2": "ヨーロッパ(チューリッヒ)",
    "EU_NORTH_1": "eu-north-1 (ストックホルム)",
    "EU_NORTH_1A": "eu-north-1a",
    "EU_NORTH_1B": "eu-north-1b",
    "EU_NORTH_1C": "eu-north-1c",
    "EU_SOUTH_1": "eu-south-1 (ミラノ)",
    "EU_SOUTH_1A": "eu-south-1a",
    "EU_SOUTH_1B": "eu-south-1b",
    "EU_SOUTH_1C": "eu-south-1c",
    "EU_SOUTH_2": "ヨーロッパ(スペイン)",
    "EU_WEST_1": "eu-west-1 (アイルランド)",
    "EU_WEST_1A": "eu-west-1a",
    "EU_WEST_1B": "eu-west-1b",
    "EU_WEST_1C": "eu-west-1c",
    "EU_WEST_2": "eu-west-2 (ロンドン)",
    "EU_WEST_2A": "eu-west-2a",
    "EU_WEST_2B": "eu-west-2b",
    "EU_WEST_2C": "eu-west-2c",
    "EU_WEST_3": "wu-west-3 (パリ)",
    "EU_WEST_3A": "eu-west-3a",
    "EU_WEST_3B": "eu-west-3b",
    "EU_WEST_3C": "eu-west-3c",
    "EUROPE_CENTRAL2_A": "europe-central2-a",
    "EUROPE_CENTRAL2_B": "europe-central2-b",
    "EUROPE_CENTRAL2_C": "europe-central2-c",
    "EUROPE_CENTRAL2": "europe-central2",
    "EUROPE_NORTH1_A": "europe-north1-a",
    "EUROPE_NORTH1_B": "europe-north1-b",
    "EUROPE_NORTH1_C": "europe-north1-c",
    "EUROPE_NORTH1": "europe-north1",
    "EUROPE_SOUTHWEST1_A": "europe-southwest1-a",
    "EUROPE_SOUTHWEST1_B": "europe-southwest1-b",
    "EUROPE_SOUTHWEST1_C": "europe-southwest1-c",
    "EUROPE_SOUTHWEST1": "europe-southwest1",
    "EUROPE_WEST1_B": "europe-west1-b",
    "EUROPE_WEST1_C": "europe-west1-c",
    "EUROPE_WEST1_D": "europe-west1-d",
    "EUROPE_WEST1": "europe-west1",
    "EUROPE_WEST10": "europe-west10",
    "EUROPE_WEST12_A": "europe-west12-a",
    "EUROPE_WEST12_B": "europe-west12-b",
    "EUROPE_WEST12_C": "europe-west12-c",
    "EUROPE_WEST12": "europe-west12",
    "EUROPE_WEST2_A": "europe-west2-a",
    "EUROPE_WEST2_B": "europe-west2-b",
    "EUROPE_WEST2_C": "europe-west2-c",
    "EUROPE_WEST2": "europe-west2",
    "EUROPE_WEST3_A": "europe-west3-a",
    "EUROPE_WEST3_B": "europe-west3-b",
    "EUROPE_WEST3_C": "europe-west3-c",
    "EUROPE_WEST3": "europe-west3",
    "EUROPE_WEST4_A": "europe-west4-a",
    "EUROPE_WEST4_B": "europe-west4-b",
    "EUROPE_WEST4_C": "europe-west4-c",
    "EUROPE_WEST4": "europe-west4",
    "EUROPE_WEST6_A": "europe-west6-a",
    "EUROPE_WEST6_B": "europe-west6-b",
    "EUROPE_WEST6_C": "europe-west6-c",
    "EUROPE_WEST6": "europe-west6",
    "EUROPE_WEST8_A": "europe-west8-a",
    "EUROPE_WEST8_B": "europe-west8-b",
    "EUROPE_WEST8_C": "europe-west8-c",
    "EUROPE_WEST8": "europe-west8",
    "EUROPE_WEST9_A": "europe-west9-a",
    "EUROPE_WEST9_B": "europe-west9-b",
    "EUROPE_WEST9_C": "europe-west9-c",
    "EUROPE_WEST9": "europe-west9",
    "EUROPE": "ヨーロッパ",
    "EUSA_AGREEMENT": "Zscaler End User Subscription Agreement",
    "EVENT_BUS_NAME": "イベント バス名",
    "EVENT_GRID_TEXT": "パートナー トピックと宛先が作成されるリージョン、サブスクリプション、リソース グループを選択します。",
    "EVENT_GRID": "Event Grid",
    "EVENT_TIME": "イベント時間",
    "EVENT": "イベント",
    "EVENTS": "イベント",
    "EXACT_MATCH": "完全一致",
    "EXCEEDS_UPGRADE_WINDOW": "失敗しました。アップグレードにウィンドウのアップグレードよりも時間がかかりました。Cloud Connectorは正常な状態にロールバックされました",
    "EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS": "動的ロケーション グループから除外",
    "EXCLUDE_FROM_STATIC_LOCATION_GROUPS": "手動ロケーション グループから除外",
    "EXCLUDE_IP_ADDRESSES": "IPアドレスを除外",
    "EXCLUDE": "除外",
    "EXEC_INSIGHT_AND_ORG_ADMIN": "Executive Insightと組織管理者",
    "EXISTING_LOCATION": "既存のロケーション",
    "EXISTING": "既存",
    "EXPIRES_IN": "有効期限",
    "EXPIRES": "有効期限",
    "EXPIRY_DATE": "有効期限",
    "EXPORT_TO_CSV": "CSVにエクスポート",
    "EXTERNAL_ID": "外部ID",
    "EXTERNAL": "外部トラフィック",
    "FAIL_ALLOW_IGNORE_DNAT": "元のDNSサーバーに転送",
    "FAIL_CLOSE": "フェール クローズ",
    "FAIL_CLOSED": "フェール クローズド",
    "FAIL_OPEN_TEXT": "トラフィック転送エンジンをバイパス",
    "FAIL_OPEN_TOOLTIP": "このオプションを有効にすると、ポリシー転送エンジンに障害が発生した場合や、アップグレードのために停止した場合でも、ポリシーの検証やコンテンツの検査を行わずに、すべてのローカルおよびインターネット向けのトラフィックを通過させることができます。この状態では、ZPAで保護されたアプリケーションへのアクセスは許可されません。",
    "FAIL_OPEN": "フェール オープン",
    "FAIL_RET_ERR": "エラー レスポンスを返す",
    "FAILED_OTHER": "その他。Cloud Connectorは正常な状態です",
    "FAILED": "失敗",
    "FAILURE_BEHAVIOR": "失敗時の動作",
    "FAILURE": "失敗",
    "FALKLAND_ISLANDS_ATLANTIC_STANLEY": "大西洋/スタンリー",
    "FALKLAND_ISLANDS_MALVINAS": "フォークランド諸島(マルビナス諸島)",
    "FALKLAND_ISLANDS": "フォークランド諸島",
    "FALLBACK_TO_TLS": "TLSにフォールバック",
    "FALSE": "False",
    "FAMILY_ISSUES": "家族の問題",
    "FAROE_ISLANDS_ATLANTIC_FAROE": "大西洋/フェロー諸島",
    "FAROE_ISLANDS": "フェロー諸島",
    "FEDERATED_STATES_OF_MICRONESIA": "ミクロネシア連邦",
    "FETCHING_MORE_LIST_ITEMS": "他のリスト項目をフェッチ中...",
    "FIJI": "フィジー共和国",
    "FILE_CERTIFICATE_FILTER": "ファイル(.pem、.cer)",
    "FILE_HOST": "ファイルホスト",
    "FILTERING": "フィルタリング",
    "FINANCE": "金融",
    "FINISH": "終了",
    "FINLAND_EUROPE_HELSINKI": "ヨーロッパ/ヘルシンキ",
    "FINLAND": "フィンランド共和国",
    "FIREWALL_ACCESS_CONTROL": "ファイアウォール アクセス制御",
    "FIREWALL_FORWARDING": "ファイアウォール転送",
    "FIREWALL_LOGS": "ファイアウォール ログ",
    "FIREWALL_RESOURCE": "ファイアウォール リソース",
    "FIRST_TIME_AUP_BEHAVIOR": "初回AUPの動作",
    "FO_DEST_DROP": "クエリーのドロップ",
    "FO_DEST_ERR": "クライアントに返されたエラー レスポンス",
    "FO_DEST_PASS": "宛先に転送されたクエリー",
    "FOOTER_PATENTS_TOOLTIP": "米国発明法のバーチャル マーキング条項に基づき、Zscalerのセキュリティ製品は米国およびその他の地域の特許によって保護されています。詳細は、https://www.zscaler.jp/patentsを参照してください。",
    "FOR_AUTOMATION": "自動化向け",
    "FORCE_ACTIVATE": "アクティブ化を強制",
    "FORCE_DELETE_VM": "VMを強制削除",
    "FORCE_SSL_INTERCEPTION": "SSLインスペクションを強制",
    "FORCED_ACTIVATE": "アクティブ化を強制",
    "FORWARD_TO_ORIGINAL_SERVER": "元のDNSサーバーに転送",
    "FORWARD_TO_PROXY_GATEWAY": "プロキシ ゲートウェイに転送",
    "FORWARD_TO_ZPA_GATEWAY": "ZPAゲートウェイに転送",
    "FORWARDING_CONTROL": "転送制御",
    "FORWARDING_INFORMATION": "転送情報",
    "FORWARDING_INTERFACE": "転送インターフェイス",
    "FORWARDING_IP_ADDRESS": "IPアドレスを転送中",
    "FORWARDING_METHOD": "転送タイプ",
    "FORWARDING_METHODS": "転送方法",
    "FORWARDING_POLICIES": "転送ポリシー",
    "FORWARDING_RULE": "転送ルール",
    "FORWARDING": "転送中",
    "FQDN_WILDCARD_DOMAINS_ACCDRESSES": "FQDN/ドメイン",
    "FQDN_WILDCARD_DOMAINS_GROUP": "FQDN/ドメイン グループ",
    "FRANCE_EUROPE_PARIS": "ヨーロッパ/パリ",
    "FRANCE": "フランス共和国",
    "FRANCECENTRAL": "(ヨーロッパ)フランス中部",
    "FRANCESOUTH": "(ヨーロッパ)フランス南部",
    "FRENCH_GUIANA_AMERICA_CAYENNE": "アメリカ/カイエンヌ",
    "FRENCH_GUIANA": "フランス領ギアナ",
    "FRENCH_POLYNESIA_PACIFIC_GAMBIER": "太平洋/ガンビエ",
    "FRENCH_POLYNESIA_PACIFIC_MARQUESAS": "太平洋/マルキーズ",
    "FRENCH_POLYNESIA_PACIFIC_TAHITI": "太平洋/タヒチ",
    "FRENCH_POLYNESIA": "フランス領ポリネシア",
    "FRENCH_SOUTHERN_TERRITORIES_INDIAN_KERGUELEN": "インド洋/ケルゲレン諸島",
    "FRENCH_SOUTHERN_TERRITORIES": "フランス領南方・南極地域",
    "FRIDAY": "金曜日",
    "FROM": "開始",
    "FTP_000": "無効なFTPレスポンス",
    "FTP_110": "110 - Restart marker replay",
    "FTP_125": "125 - Data connection already open; transfer starting",
    "FTP_150": "150 - File status okay;about to open data connection",
    "FTP_200": "200 - Directory successfully changed",
    "FTP_226": "226 - Transfer Complete",
    "FTP_250": "250 - Completed",
    "FTP_421": "421 - Service not available",
    "FTP_425": "425 - Can't open data connection",
    "FTP_426": "426 - Transfer aborted",
    "FTP_450": "450 - Requested file action not taken",
    "FTP_451": "451 - Local error in processing",
    "FTP_452": "452 - Insufficient storage",
    "FTP_453": "453 - MD5 mismatch",
    "FTP_500": "500 - Syntax error",
    "FTP_501": "501 - Syntax error in parameters",
    "FTP_502": "502 - Command not implemented",
    "FTP_530": "530 - Not logged in",
    "FTP_532": "532 - Need account for storing files",
    "FTP_550": "550 - File unavailable",
    "FTP_551": "551 - Page type unknown",
    "FTP_552": "552 - Exceeded storage allocation",
    "FTP_553": "553 - File name not allowed",
    "FTP_554": "554 - File is infected",
    "FTP_555": "555 - Blocked by file type policy",
    "FTP_556": "556 - Blocked by DLP",
    "FTP_557": "557 - Blocked by BA",
    "FTP_558": "558 - Blocked by BWCTL",
    "FTP_559": "559 - Blocked by URL Category",
    "FTP_560": "560 - Blocked by ATP",
    "FTP_561": "561 - Blocked by Block Internet Access",
    "FTP_ALLOW_OVER_HTTP": "FTP over HTTPを許可",
    "FTP_ALLOWED_URL_CATEGORIES": "許可されたURLカテゴリー",
    "FTP_ALLOWED_URLS": "許可されたURL",
    "FTP_APPE": "appe",
    "FTP_CONNECT_CMD": "接続",
    "FTP_CONNECT": "ネイティブFTPへのブリッジングからFTPトラフィックを変換(HTTP接続を使用)",
    "FTP_CONTROL_RECOMMENDED_POLICY": "推奨されるFTPコントロール ポリシー",
    "FTP_CONTROL_TIPS_DESC": "デフォルトでは、Zscalerサービスは、あるロケーションのユーザーがFTPサイトからファイルをアップロードまたはダウンロードすることを許可していません。特定のサイトへのアクセスを許可するようにFTPコントロール ポリシーを構成できます。",
    "FTP_CONTROL_TIPS_TITLE": "FTPコントロール ポリシーの構成",
    "FTP_CONTROL": "FTP制御",
    "FTP_CWD": "cwd",
    "FTP_DATA_DESC": " このプロトコルは、FTP通信のデータ接続でデータを転送するために使用されます",
    "FTP_DATA": "FTPデータ",
    "FTP_DENIED": "FTPサイトへのアクセスが許可されていない",
    "FTP_DESC": " FTPプロトコルは、クライアントとサーバー間の信頼性の高いデータ転送に使用されます",
    "FTP_INVALID": "無効",
    "FTP_LIST": "リスト",
    "FTP_NATIVE_TRAFFIC": "ネイティブFTPトラフィック",
    "FTP_OVER_HTTP_TRAFFIC": "FTP over HTTPトラフィック",
    "FTP_PROXY_PORT": "FTPプロキシ ポート",
    "FTP_PROXY": "FTPプロキシ",
    "FTP_RETR": "retr",
    "FTP_RULE": "ネイティブFTP",
    "FTP_SECURITY": "FTPセキュリティにはAV、DLP、BA、FTなどが含まれます",
    "FTP_SERVICES": "FTPサービス",
    "FTP_STOR": "stor",
    "FTP_UPLOAD_DENIED": "アップロードにFTP over HTTPを使用することはできません",
    "FTP": "FTP",
    "FTPOVERHTTP": "FTP over HTTP",
    "FTPRULESLOT": "ファイル タイプ コントロール",
    "FTPS_DATA_DESC": " このプロトコルは、セキュアなFTP通信のデータ接続でデータを転送するために使用されます",
    "FTPS_DATA": "ftps_data",
    "FTPS_DESC": " FTPプロトコルのセキュアなバージョン",
    "FTPS_IMPLICIT_DESC": "暗黙的FTPSは、FTPクライアントがFTPサーバーに接続するとすぐに、サーバーへのSSL/TLS接続を自動的に開始します。",
    "FTPS_IMPLICIT": "暗黙的FTPS",
    "FTPS": "FTPS",
    "FULL_ACCESS_ENABLED_UNTIL": "フル アクセスの有効期限",
    "FULL_ACCESS": "フル アクセス",
    "FULL_SESSION_LOGS": "完全なセッション ログ",
    "FULL": "フル",
    "FUNCTIONAL_SCOPE": "機能範囲",
    "FWD_METHOD": "転送タイプ",
    "FWD_RULE": "転送ルール",
    "FWD_TRAFFIC_DIRECTION": "トラフィックの方向",
    "FWD_TYPE": "転送タイプ",
    "FWD_TYPES": "転送タイプ",
    "GABON_AFRICA_LIBREVILLE": "アフリカ/リーブルヴィル",
    "GABON": "ガボン共和国",
    "GAMBIA_AFRICA_BANJUL": "アフリカ/バンジュール",
    "GAMBIA": "ガンビア共和国",
    "GAMBLING": "ギャンブル",
    "GATEWAY_DEST_IP": "ゲートウェイ宛先IP",
    "GATEWAY_DEST_PORT": "ゲートウェイ宛先ポート",
    "GATEWAY_DETAILS": "ゲートウェイの詳細",
    "GATEWAY_IP_ADDRESS": "ゲートウェイIPアドレス",
    "GATEWAY_NAME": "ゲートウェイ名",
    "GATEWAY_OPTIONS": "ゲートウェイ オプション",
    "GATEWAY": "ゲートウェイ",
    "GATEWAYS": "ゲートウェイ",
    "GCP_AVAILABILITY_ZONE": "GCPアベイラビリティー ゾーン",
    "GCP_REGION": "GCPリージョン",
    "GENERAL_AVAILABILITY": "一般提供",
    "GENERAL_INFORMATION": "一般情報",
    "GENERAL": "一般",
    "GENERATE_NEW_CERTIFICATE": "新しい証明書を生成",
    "GENERATE_TOKEN": "トークンを発行",
    "GEO_LOCATION": "ジオロケーション",
    "GEO_VIEW": "ジオ ビュー",
    "GEORGIA_ASIA_TBILISI": "アジア/トビリシ",
    "GEORGIA": "グルジア",
    "GERMANY_EUROPE_BERLIN": "ヨーロッパ/ベルリン",
    "GERMANY": "ドイツ",
    "GERMANYNORTH": "(ヨーロッパ)ドイツ北部",
    "GERMANYWESTCENTRAL": "(ヨーロッパ)ドイツ西中部",
    "GHANA_AFRICA_ACCRA": "アフリカ/アクラ",
    "GHANA": "ガーナ共和国",
    "GIBRALTAR_EUROPE_GIBRALTAR": "ヨーロッパ/ジブラルタル",
    "GIBRALTAR": "ジブラルタル",
    "GLOBAL": "グローバル",
    "GMT_01_00_AZORES": "GMT-01:00",
    "GMT_01_00_WESTERN_EUROPE_GMT_01_00": "GMT+01:00",
    "GMT_02_00_EASTERN_EUROPE_GMT_02_00": "GMT+02:00",
    "GMT_02_00_EGYPT_GMT_02_00": "GMT+02:00",
    "GMT_02_00_ISRAEL_GMT_02_00": "GMT+02:00",
    "GMT_02_00_MID_ATLANTIC": "GMT-02:00",
    "GMT_03_00_ARGENTINA": "GMT-03:00",
    "GMT_03_00_BRAZIL": "GMT-03:00",
    "GMT_03_00_RUSSIA_GMT_03_00": "GMT+03:00",
    "GMT_03_00_SAUDI_ARABIA_GMT_03_00": "GMT+03:00",
    "GMT_03_30_IRAN_GMT_03_30": "GMT+03:30",
    "GMT_03_30_NEWFOUNDLAND_CANADA": "GMT-03:30",
    "GMT_04_00_ARABIAN_GMT_04_00": "GMT+04:00",
    "GMT_04_00_ATLANTIC_TIME": "GMT-04:00",
    "GMT_04_30_AFGHANISTAN_GMT_04_30": "GMT+04:30",
    "GMT_05_00_COLUMBIA_PERU_SOUTH_AMERICA": "GMT-05:00",
    "GMT_05_00_PAKISTAN_WEST_ASIA_GMT_05_00": "GMT+05:00",
    "GMT_05_00_US_EASTERN_TIME_INDIANA": "GMT-05:00",
    "GMT_05_00_US_EASTERN_TIME": "GMT-05:00",
    "GMT_05_30_INDIA_GMT_05_30": "GMT+05:30",
    "GMT_06_00_BANGLADESH_CENTRAL_ASIA_GMT_06_00": "GMT+06:00",
    "GMT_06_00_MEXICO": "GMT-06:00",
    "GMT_06_00_US_CENTRAL_TIME": "GMT-06:00",
    "GMT_06_30_BURMA_GMT_06_30": "GMT+06:30",
    "GMT_07_00_BANGKOK_HANOI_JAKARTA_GMT_07_00": "GMT+07:00",
    "GMT_07_00_US_MOUNTAIN_TIME_ARIZONA": "GMT-07:00",
    "GMT_07_00_US_MOUNTAIN_TIME": "GMT-07:00",
    "GMT_08_00_AUSTRALIA_WT_GMT_08_00": "GMT+08:00",
    "GMT_08_00_CHINA_TAIWAN_GMT_08_00": "GMT+08:00",
    "GMT_08_00_PACIFIC_TIME": "GMT-08:00",
    "GMT_08_00_SINGAPORE_GMT_08_00": "GMT+08:00",
    "GMT_08_30_PITCARN": "GMT-08:30",
    "GMT_09_00_JAPAN_GMT_09_00": "GMT+09:00",
    "GMT_09_00_KOREA_GMT_09_00": "GMT+09:00",
    "GMT_09_00_US_ALASKA_TIME": "GMT-09:00",
    "GMT_09_30_AUSTRALIA_CT_GMT_09_30": "GMT+09:30",
    "GMT_09_30_MARQUESAS": "GMT-09:30",
    "GMT_10_00_AUSTRALIA_ET_GMT_10_00": "GMT+10:00",
    "GMT_10_00_US_HAWAIIAN_TIME": "GMT-10:00",
    "GMT_10_30_AUSTRALIA_LORD_HOWE_GMT_10_30": "GMT+10:30",
    "GMT_11_00_CENTRAL_PACIFIC_GMT_11_00": "GMT+11:00",
    "GMT_11_00_SAMOA": "GMT-11:00",
    "GMT_11_30_NORFOLK_ISLANDS_GMT_11_30": "GMT+11:30",
    "GMT_12_00_DATELINE": "GMT-12:00",
    "GMT_12_00_FIJI_NEW_ZEALAND_GMT_12_00": "GMT+12:00",
    "GMT_UK_SPAIN": "GMT",
    "GMT": "GMT",
    "GMTP": "",
    "GNUTELLA_DESC": " GnutellaはP2Pプロトコルです",
    "GNUTELLA": "Gnutella",
    "GO_BACK": "戻る",
    "GOOD": "良い",
    "GOVERNMENT": "その他の政府および政治",
    "GRE_PROTOCOL_DESC": "汎用ルーティング カプセル化(GRE)は、インターネット プロトコル ネットワーク上の仮想ポイントツーポイント リンクまたはポイントツーマルチポイント リンク内にさまざまなネットワーク層プロトコルをカプセル化できるトンネリング プロトコルです。",
    "GRE_PROTOCOL": "GRE",
    "GRE_TUNNEL_INFO": "GREトンネル情報",
    "GRE": "GRE",
    "GREECE_EUROPE_ATHENS": "ヨーロッパ/アテネ",
    "GREECE": "ギリシャ共和国",
    "GREENLAND_AMERICA_DANMARKSHAVN": "アメリカ/デンマークシャン",
    "GREENLAND_AMERICA_GODTHAB": "アメリカ/ゴットホープ",
    "GREENLAND_AMERICA_SCORESBYSUND": "アメリカ/イトコルトルミット",
    "GREENLAND_AMERICA_THULE": "アメリカ/チューレ",
    "GREENLAND": "グリーンランド",
    "GRENADA_AMERICA_GRENADA": "アメリカ/グレナダ",
    "GRENADA": "グレナダ",
    "GROUP_INFORMATION": "グループ情報",
    "GROUP_NAME": "グループ名",
    "GROUP_ONLY": "グループ",
    "GROUP": "グループ",
    "GROUPS": "グループ",
    "GUADELOUPE_AMERICA_GUADELOUPE": "アメリカ/グアドループ",
    "GUADELOUPE": "グアドループ",
    "GUAM_PACIFIC_GUAM": "太平洋/グアム",
    "GUAM": "グアム",
    "GUATEMALA_AMERICA_GUATEMALA": "アメリカ/グアテマラ共和国",
    "GUATEMALA": "グアテマラ共和国",
    "GUERNSEY_EUROPE_GUERNSEY": "ヨーロッパ/ガーンジー",
    "GUERNSEY": "ガーンジー",
    "GUESTWIFI": "ゲストWi-Fiのトラフィック タイプ",
    "GUINEA_AFRICA_CONAKRY": "アフリカ/コナクリ",
    "GUINEA_BISSAU_AFRICA_BISSAU": "アフリカ/ビサウ",
    "GUINEA_BISSAU": "ギニアビサウ共和国",
    "GUINEA": "ギニア共和国",
    "GUYANA_AMERICA_GUYANA": "アメリカ/ガイアナ共和国",
    "GUYANA": "ガイアナ共和国",
    "GW_CONNECT_FAILED": "GWへの接続を確立できませんでした。",
    "GW_CONNECTION_CLOSE": "GW接続はEOFで閉じられました。",
    "GW_CONNECTION_FAIL": "ゲートウェイ接続に失敗しました。",
    "GW_KEEPALIVE_FAIL": "GWキープアライブ プローブがタイムアウトしました。",
    "GW_RESOLVE_FAIL": "ゲートウェイの解決に失敗しました。",
    "GW_RESOLVE_NOIP": "PACはGW解決のIPを返しませんでした。",
    "GW_UNHEALTHY": "一部のゲートウェイが異常です。",
    "H_323_DESC": "H.323は国際電気通信連合(ITU)によって勧告された標準であり、会議の音声データや動画データをネットワーク上で送信する方法を定義しています",
    "H_323": "H.323",
    "HA_DEPLOYMENT_STATUS": "HA展開のステータス",
    "HA_DEPLOYMENT": "高可用性の展開",
    "HA_STATE": "HAの状態",
    "HA_STATUS": "HAのステータス",
    "HAITI_AMERICA_PORT_AU_PRINCE": "アメリカ/ポルトープランス",
    "HAITI": "ハイチ共和国",
    "HARDWARE_DEVICE": "ハードウェア デバイス",
    "HARDWARE_MANAGEMENT": "ハードウェア管理",
    "HEALTH_MONITORING_CC": "正常性監視Cloud Connectors",
    "HEALTH_STATUS_TOOLTIP": "[正常性ステータス]には、展開された各ゲートウェイの正常性が表示されます。各ゲートウェイは2つ以上のアベイラビリティー ゾーン(AZ)を含むことができるため、ここに表示されるゲートウェイの数はエンタイトルメントに示されている数よりも少なくなります。",
    "HEALTH_STATUS": "正常性ステータス",
    "HEALTH": "正常性",
    "HEALTHY": "正常",
    "HELP": "ヘルプ",
    "HIGH_AVAILABILITY_STATUS": "高可用性ステータス",
    "HIGH_AVAILABILITY": "高可用性",
    "HISTORY": "履歴",
    "HOBBIES_AND_LEISURE": "趣味/レジャー",
    "HOLY_SEE_VATICAN_CITY_STATE": "聖座(バチカン市国)",
    "HONDURAS_AMERICA_TEGUCIGALPA": "アメリカ/テグシガルパ",
    "HONDURAS": "ホンジュラス共和国",
    "HONG_KONG_ASIA_HONG_KONG": "アジア/香港",
    "HONG_KONG": "香港",
    "HOP_COUNT": "ホップ数",
    "HOSTED_DB": "ホスト型DB",
    "HOSTNAME_PREFIX": "ホスト名プレフィックス",
    "HOSTNAME": "ホスト名",
    "HOURS": "時間",
    "HTTP_0_0": "悪い",
    "HTTP_000": "無効なHTTPレスポンス",
    "HTTP_1_0": "1",
    "HTTP_1_1": "1.1",
    "HTTP_100": "100 - Continue",
    "HTTP_101": "101 - Switching Protocols",
    "HTTP_102": "102 - Processing",
    "HTTP_150": "150 - Other 1XX errors",
    "HTTP_2_0": "2.0",
    "HTTP_200": "200 - OK",
    "HTTP_201": "201 - Created",
    "HTTP_202": "202 - Accepted",
    "HTTP_203": "203 - Non-Authoritative Information",
    "HTTP_204": "204 - No Content",
    "HTTP_205": "205 - Reset Content",
    "HTTP_206": "206 - Partial Content",
    "HTTP_207": "207 - Multi-Status",
    "HTTP_226": "226 - IM Used",
    "HTTP_250": "250 - Other 2XX errors",
    "HTTP_300": "300 - Multiple Choices",
    "HTTP_301": "301 - Moved Permanently",
    "HTTP_302": "302 - Found",
    "HTTP_303": "303 - See Other",
    "HTTP_304": "304 - Not Modified",
    "HTTP_305": "305 - Use Proxy",
    "HTTP_306": "306 - Unused",
    "HTTP_307": "307 - Temporary Redirect",
    "HTTP_308": "308 - Permanent Redirect",
    "HTTP_400": "400 - Bad Request",
    "HTTP_401": "401 - Unauthorized",
    "HTTP_402": "402 - Payment Required",
    "HTTP_403": "403 - Forbidden",
    "HTTP_404": "404 - Not Found",
    "HTTP_405": "405 - Method Not Allowed",
    "HTTP_406": "406 - Not Acceptable",
    "HTTP_407": "407 - Proxy Authentication Required",
    "HTTP_408": "408 - Request Timeout",
    "HTTP_409": "409 - Conflict",
    "HTTP_410": "410 - Gone",
    "HTTP_411": "411 - Length Required",
    "HTTP_412": "412 - Precondition Failed",
    "HTTP_413": "413 - Request Entity Too Large",
    "HTTP_414": "414 - Request-URI Too Long",
    "HTTP_415": "415 - Unsupported Media Type",
    "HTTP_416": "416 - Requested Range Not Satisfiable",
    "HTTP_417": "417 - Expectation Failed",
    "HTTP_421": "421 - Misdirected Request",
    "HTTP_422": "422 - Unprocessable Entity",
    "HTTP_423": "423 - Locked",
    "HTTP_424": "424 - Failed Dependency",
    "HTTP_426": "426 - Upgrade Required",
    "HTTP_428": "428 - Precondition Required",
    "HTTP_429": "429 - Too Many Requests",
    "HTTP_450": "450 - Other 4XX errors",
    "HTTP_500": "500 - Internal Server Error",
    "HTTP_501": "501 - Not Implemented",
    "HTTP_502": "502 - Bad Gateway",
    "HTTP_503": "503 - Service Unavailable",
    "HTTP_504": "504 - Gateway Timeout",
    "HTTP_505": "505 - Version Not Supported",
    "HTTP_506": "506 - Variant Also Negotiates",
    "HTTP_507": "507 - Insufficient Storage",
    "HTTP_508": "508 - Loop Detected",
    "HTTP_510": "510 - Not Extended",
    "HTTP_550": "550 - Other 5XX errors",
    "HTTP_BASELINECONTROL": "Baselinecontrol",
    "HTTP_BCOPY": "Bcopy",
    "HTTP_BDELETE": "Bdelete",
    "HTTP_BMOVE": "Bmove",
    "HTTP_BPROPFIND": "Bpropfind",
    "HTTP_BPROPPATCH": "Bproppatch",
    "HTTP_CHECKIN": "Checkin",
    "HTTP_CHECKOUT": "Checkout",
    "HTTP_CONNECT_DENIED": "HTTPトンネルの使用は許可されていません",
    "HTTP_CONNECT": "Connect",
    "HTTP_COPY": "Copy",
    "HTTP_DELETE": "Delete",
    "HTTP_DESC": " Hypertext Transfer Protocol (HTTP)はWebの閲覧に使用されます",
    "HTTP_DNS_PORT_SETTINGS": "HTTPおよびDNS ポートの設定",
    "HTTP_DPI_DISABLED": "SME DPI:プロキシhttpトラフィックをDPIに送信するかどうかを決定します。デフォルトではこの機能ビットはオフになっており、ZURLDBからappidを決定できない場合、DPIにフローを送信します",
    "HTTP_GET": "Get",
    "HTTP_HEAD": "Head",
    "HTTP_LABEL": "Label",
    "HTTP_LOCK": "Lock",
    "HTTP_MAILPOST": "Mailpost",
    "HTTP_MERGE": "Merge",
    "HTTP_MKACTIVITY": "Mkactivity",
    "HTTP_MKCOL": "Mkcol",
    "HTTP_MKWORKSPACE": "Mkworkspace",
    "HTTP_MOVE": "Move",
    "HTTP_NOTIFY": "Notify",
    "HTTP_OPTIONS": "Options",
    "HTTP_POLL": "Poll",
    "HTTP_PORTS_FORWARDED_TO_WEB_PROXY": "Webプロキシに転送するHTTPポート",
    "HTTP_POST": "Post",
    "HTTP_PROPFIND": "Propfind",
    "HTTP_PROPPATCH": "Proppatch",
    "HTTP_PROXY_DESC": "HTTPトンネリングは、さまざまなネットワーク プロトコルで行われる通信を、HTTPプロトコルでカプセル化する技術です。ここでいうネットワーク プロトコルはTCP/IP形式のプロトコルに属します。",
    "HTTP_PROXY_PORT": "HTTPプロキシ ポート",
    "HTTP_PROXY": "HTTPプロキシ",
    "HTTP_PUT": "Put",
    "HTTP_REPORT": "Report",
    "HTTP_REQMOD": "Reqmod",
    "HTTP_REQUEST": "HTTPリクエスト",
    "HTTP_REQUESTS": "HTTPリクエスト",
    "HTTP_RESPMOD": "Respmod",
    "HTTP_RESPONSE": "HTTPレスポンス",
    "HTTP_RULE": "HTTP",
    "HTTP_SEARCH": "Search",
    "HTTP_SECURITY_HEADERS": "HTTPセキュリティ ヘッダー",
    "HTTP_SERVICES": "HTTPサービス",
    "HTTP_SUBSCRIBE": "Subscribe",
    "HTTP_TRACE": "Trace",
    "HTTP_TUNNEL_CONTROL": "HTTPトンネル コントロール",
    "HTTP_TUNNEL": "HTTPトンネル",
    "HTTP_UNCHECKOUT": "Uncheckout",
    "HTTP_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないHTTPプロキシ/ファイアウォール トラフィックが特定されます",
    "HTTP_UNKNOWN": "不明なHTTP",
    "HTTP_UNLOCK": "Unlock",
    "HTTP_UNSUBSCRIBE": "Unsubscribe",
    "HTTP_UPDATE": "Update",
    "HTTP_VERSIONCONTROL": "Versioncontrol",
    "HTTP_VS_HTTPS": "HTTPとHTTPSの比較",
    "HTTP": "HTTP",
    "HTTP2_DESC": " Hypertext Transfer Protocol (HTTP2.0)はWebの閲覧に使用されます",
    "HTTP2": "HTTPv2",
    "HTTPS_DESC": " HTTPSはHTTPのセキュアなバージョンです",
    "HTTPS_PORTS_FORWARDED_TO_WEB_PROXY": "Webプロキシに転送するHTTPSポート",
    "HTTPS_PROXY_PORT": "HTTPSプロキシ ポート",
    "HTTPS_PROXY": "HTTPプロキシ",
    "HTTPS_RULE": "HTTPS",
    "HTTPS_SERVICES": "HTTPSサービス",
    "HTTPS_SSL_TRAFFIC_TREND": "HTTPSおよびSSLトラフィックの傾向",
    "HTTPS_SSL_TRAFFIC": "HTTPSおよびSSLトラフィック",
    "HTTPS_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないHTTPSプロキシ/ファイアウォール トラフィックが特定されます",
    "HTTPS_UNKNOWN": "不明なHTTPS",
    "HTTPS": "HTTPS",
    "HTTPTUNNEL_DESC": " HTTPトンネリングは、さまざまなネットワーク プロトコルで行われる通信を、HTTPプロトコルでカプセル化する技術です。ここでいうネットワーク プロトコルはTCP/IP形式のプロトコルに属します。",
    "HTTPTUNNEL": "HttpTunnel",
    "HUNGARY_EUROPE_BUDAPEST": "ヨーロッパ/ブダペスト",
    "HUNGARY": "ハンガリー",
    "HYPERVISOR_OS": "ハイパーバイザー",
    "HYPERVISOR_VERSION": "ハイパーバイザーのバージョン",
    "I_AGREE": "同意する",
    "I_GAMER_DESC": " オンラインのゲームおよびマンガのWebサイト",
    "I_GAMER": "i-gamer",
    "I_PART_DESC": " 台湾のオンラインお見合いサイト",
    "I_PART": "i-part.com",
    "I_UNDERSTAND_THE_CONSEQUENCE_AND_WANT_TO_PROCEED": "結果を理解したうえで続行します",
    "I_UNDERSTAND_THE_CONSEQUENCE": "結果を理解したうえで続行します",
    "ICELAND_ATLANTIC_REYKJAVIK": "大西洋/レイキャビク",
    "ICELAND": "アイスランド共和国",
    "ICMP_ANY_DESC": "ICMPはインターネットプロトコルスイートの中でも主要なプロトコルの1つで、ルーターのようなネットワーク端末に利用され、リクエストされたサービスを提供できなかったり、ホストまたはルーターにアクセスできなかったりする場合にエラーメッセージを送信します。",
    "ICMP_ANY": "ICMP",
    "ICMP_DESC": " Internet Control Message Protocol (ICMP)はインターネット プロトコル スイートの中の主要なプロトコルの1つです",
    "ICMP_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないUDPプロキシ/ファイアウォール トラフィックが特定されます",
    "ICMP_UNKNOWN": "不明なICMP",
    "IDENT_DESC": " Identification Protocolは、特定のTCP接続のユーザーのアイデンティティーを決定する手段を提供します",
    "IDENT": "Ident",
    "IDLE_TIME_DISASSOCIATION": "関連付け解除までのアイドル時間",
    "IDP_NAME": "IdP名",
    "IDP": "IdP",
    "IGNORE_INSECURE_KEY": "安全でない",
    "IKE_ALG": "このLBはike algをサポートします",
    "IKE_DESC": "IKEはIPSECのために、ISAKMPに利用する認証されたキーマテリアルを取得するためのプロトコルです。",
    "IKE_NAT_DESC": "IKE-NATはISAKMPおよびESPパケットのNAT(Network Address Translation)を許可します。",
    "IKE_NAT": "IKE-NAT",
    "IKE": "IKE",
    "IKEA_DESC": " このプロトコル プラグインは、ホストikea.comへのhttpトラフィックを分類します",
    "IKEA": "ikea",
    "IKEV1_PHASE1": "IKEバージョン1",
    "IKEV1_PHASE2": "IKEバージョン2",
    "IKEV1": "IKEバージョン1",
    "IKEV2_ALL_PHASES": "IKEバージョン2",
    "IKEV2": "IKEバージョン2",
    "IL_CENTRAL_1": "イスラエル(テルアビブ)",
    "ILOVEIM_DESC": " このプロトコル プラグインは、ホストloveim.comへのhttpトラフィックを分類します",
    "ILOVEIM": "ILoveIM",
    "ILS_DESC": "Internet Locator ServiceにはLDAP、User Locator Service、LDAP over TLS/SSLが含まれます",
    "ILS": "ILS",
    "IMAGE_HOST_DESC": " 動画または画像のホスティング/リンク/共有サービスを提供するサイト",
    "IMAGE_HOST": "画像ホスティング",
    "IMAGE_ID": "画像ID",
    "IMAGES": "イメージ",
    "IMAGESHACK_DESC": " オンラインの無料画像共有サービス",
    "IMAGESHACK": "ImageShack",
    "IMAP_DESC": "Internet Message Access Protocolはメール メッセージの取得に使用されるプロトコルです",
    "IMAP": "IMAP",
    "IMDB_DESC": " 映画およびテレビ番組に関連するオンラインの情報データベース",
    "IMDB": "IMDb",
    "IMEEM_DESC": " このプロトコル プラグインは、ホストimeem.comへのhttpトラフィックを分類します",
    "IMEEM": "imeem",
    "IMEET_DESC": " クラウドベースの技術を利用したオンラインのビデオ会議サービス",
    "IMEET": "i-Meet",
    "IMESH_DESC": " iMeshはP2Pプロトコルです",
    "IMESH": "iMesh",
    "IMFRULESLOT": "インスタント メッセージ アプリ コントロール",
    "IMGUR_DESC": " 無料のオンライン画像ホスティング サービス",
    "IMGUR": "imgur",
    "IMO": "imo",
    "IMOIM_DESC": " imo.im",
    "IMOIM": "imo.im",
    "IMP_DESC": " IMPはHordeプロジェクトのIMAP Webメールです",
    "IMP": "IMP",
    "IMPERSONATION": "なりすまし",
    "IMPLUS_DESC": " IM+",
    "IMPLUS": "IM+",
    "IMPORT_ACCOUNT_ID": "アカウントIDをインポート",
    "IMPORT_PROJECT_ID": "プロジェクトIDのインポート",
    "IMPORT_SUBSCRIPTION_ID": "サブスクリプションIDをインポート",
    "IMPORT": "インポート",
    "IMPRESS_DESC": " 日本のITニュースWebサイト",
    "IMPRESS": "impress",
    "IMVU_DESC": " このプロトコル プラグインは、ホストimvu.comへのhttpトラフィックを分類します",
    "IMVU": "IMVU",
    "INACTIVE": "非アクティブ",
    "INBOUND": "インバウンド",
    "INBOX_DESC": " 無料のメール サービスを提供するinbox.comポータル",
    "INBOX": "受信箱",
    "INBYTES": "バイト単位",
    "INCLUDE_ADDRESS_RANGE": "アドレス範囲を含める",
    "INCLUDE_IP_ADDRESSES": "IPアドレスを含める",
    "INCLUDE": "含める",
    "INCOMPLETE_DESC": " プロトコル署名が長すぎる際に[不完全]が使用されます",
    "INCOMPLETE": "不完全",
    "INDABA_MUSIC_DESC": " このプロトコル プラグインは、ホストindabamusic.comへのhttpトラフィックを分類します",
    "INDABA_MUSIC": "Indaba Music",
    "INDIA_ASIA_CALCUTTA": "アジア/カルカッタ",
    "INDIA_ASIA_KOLKATA": "アジア/コルカタ",
    "INDIA": "インド共和国",
    "INDIATIMES_DESC": " この署名は、インドで最も人気のあるインターネットおよびモバイルの付加価値サービスのWebポータルの1つである、indiatimesとその多数のサブドメインを検出します",
    "INDIATIMES": "timesofindia",
    "INDONESIA_ASIA_JAKARTA": "アジア/ジャカルタ",
    "INDONESIA_ASIA_JAYAPURA": "アジア/ジャヤプラ",
    "INDONESIA_ASIA_MAKASSAR": "アジア/マカッサル",
    "INDONESIA_ASIA_PONTIANAK": "アジア/ポンティアナック",
    "INDONESIA": "インドネシア共和国",
    "INDONETWORK_DESC": " このプロトコル プラグインは、ホストindonetwork.co.idへのhttpトラフィックを分類します",
    "INDONETWORK": "Indonetwork",
    "INDOWEBSTER_DESC": " このプロトコル プラグインは、ホストindowebster.comへのhttpトラフィックを分類します",
    "INDOWEBSTER": "Indowebster",
    "INFO": "情報",
    "INFOARMOR_DESC": " 高度な脅威インテリジェンスを活用するInfoArmorは、従業員のID保護に関して業界をリードするソリューションを提供します",
    "INFOARMOR": "InfoArmor",
    "INFORMIX_DESC": " InformixはIBMが開発したリレーショナル データベース管理システムの一部です。2001年にIBMによって買収されましたが、その開発は1981年までさかのぼります。InformixはIBMのメインフレーム上で動作し、Linux/Unix/Windowsでも利用できます",
    "INFORMIX": "Informix",
    "INGRESS_DETAILS": "イングレスの詳細",
    "INILAH_DESC": " このプロトコル プラグインは、ホストinilah.comへのhttpトラフィックを分類します",
    "INILAH": "inilah",
    "INSIGHTS": "インサイト",
    "INST_GBL_METRICS": "インスタンス",
    "INSTAGRAM_DESC": " このプロトコル プラグインは、ホストinstagr.amおよびinstagram.comへのhttpトラフィックを分類します。コモン ネームinstagram.comへのsslトラフィックも分類します",
    "INSTAGRAM": "Instagram",
    "INSTANCE_ROLE": "インスタンス ロール",
    "INTALKING_DESC": " 台湾の美容、化粧品ポータル",
    "INTALKING": "intalking.com",
    "INTEGER_REQUIRED": "数値を入力してください。",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_LAN": "LANでは、少なくとも1つのインターフェイスまたはサブインターフェイスを有効にする必要があります。",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE_WAN": "WANでは、少なくとも1つのインターフェイスまたはサブインターフェイスを有効にする必要があります。最大で2つのインターフェイスまたはサブインターフェイスを有効にできます。",
    "INTERFACE_MUST_HAVE_AT_LEAST_ONE_SUBINTERFACE": "インターフェイスには、少なくとも1つのサブインターフェイスが必要です。",
    "INTERFACE_NAME": "インターフェイス名",
    "INTERFACE_SHUTDOWN": "インターフェイスのシャットダウン",
    "INTERFACE": "インターフェイス",
    "INTERNAL_EXTERNAL_TRAFFIC": "内部および外部トラフィック",
    "INTERNAL_GATEWAY_IP_ADDRESS": "内部ゲートウェイIPアドレス",
    "INTERNAL": "内部トラフィック",
    "INTERNATIONS_DESC": " このプロトコル プラグインは、ホストinternations.orgへのhttpトラフィックを分類します。コモン ネーム.internations.orgへのsslトラフィックも分類します",
    "INTERNATIONS": "InterNations",
    "INTERNET_ACCESS": "インターネット アクセス",
    "INTERNET_COMMUNICATION": "インターネット通信",
    "INTERNET_SERVICES_DESC": " インターネット上で行われるサービスに関連するサイト。",
    "INTERNET_SERVICES": "インターネット サービス",
    "INTERNET_USAGE_TREND": "インターネット使用の傾向",
    "INTERNET": "インターネット",
    "INTERPARK_DESC": " このプロトコル プラグインは、ホストwww.interpark.comへのhttpトラフィックを分類します。コモン ネーム.interpark.comへのsslトラフィックも分類します",
    "INTERPARK": "Interpark",
    "INTUIT_DESC": " このプロトコル プラグインは、ホストintuit.comへのhttpトラフィックを分類します",
    "INTUIT": "Intuit",
    "INVALID_API_KEY": "無効なAPIキー",
    "INVALID_MAX_BATCH_SIZE_UNIT_FOR_SIEM_TYPE": "SIEMタイプの最大バッチ サイズ単位が無効です。この数値は16 KBから512 KBの間にする必要があります。",
    "INVALID_NAME": "有効な名前を入力してください",
    "INVALID_USERNAME_OR_PASSWORD": "無効なログインIDまたはパスワード",
    "IOS_APPSTORE_DESC": " Apple App StoreはApple Incが開発、管理するiOS向けのデジタルアプリケーション配布プラットフォームです",
    "IOS_APPSTORE": "IOS Appstore",
    "IOS_OTA_UPDATE_DESC": " iOS OTA UpdateはOver The AirのiOSアップデートに利用されるプロトコルです",
    "IOS_OTA_UPDATE": "iOS OTA Update",
    "IOS_OTHERS": "iOS (その他)",
    "IOS_TUNES": "iOS iTunes App",
    "IOT": "IoTトラフィック",
    "IP_ABUSE_CHECK_DESCRIPTION": "プロキシを不正使用している可能性のあるIPを確認",
    "IP_ABUSE_CHECK": "IP不正使用を確認",
    "IP_ADDRESS_FROM": "開始IPアドレス",
    "IP_ADDRESS_HA_DEVICE": "HAデバイスの仮想IPアドレスを入力します。",
    "IP_ADDRESS_LAN_SECTION": "デバイスのLANセクションのIPアドレスを入力します。",
    "IP_ADDRESS_OPTIONAL": "IPアドレス(省略可)",
    "IP_ADDRESS_OR_FQDN_OR_WILDCARD_FQDN": "IPアドレス、FQDN、またはワイルドカードFQDN",
    "IP_ADDRESS_OR_FQDN": "IPアドレスまたはFQDN",
    "IP_ADDRESS_OR_WILDCARD_FQDN": "IPアドレスまたはワイルドカードFQDN",
    "IP_ADDRESS_RANCE_CIDR": "IPアドレス範囲/CIDR",
    "IP_ADDRESS_SHOULD_NOT_BE_PART_OF_ADDRESS_RANGES_POOL": "IPアドレスはDHCPアドレス プールの一部であってはなりません。",
    "IP_ADDRESS_TO": "終了IPアドレス",
    "IP_ADDRESS_WAN_SECTION": "デバイスのWANセクションのIPアドレスを入力します。",
    "IP_ADDRESS": "IPアドレス",
    "IP_ADDRESSES": "IPアドレス",
    "IP_ADDRESSESS": "IPアドレス",
    "IP_BASED_COUNTRIES": "IPベースの国",
    "IP_CAT_LOOKUP": "IP cat動的ルックアップの有効化",
    "IP_CATEGORIES": "IPカテゴリー",
    "IP_CONNECT_TRANSPARENT": "接続をIP:ポートの透過モードに切り替える",
    "IP_DESC": " インターネット プロトコル(IP)は、ネットワーク境界を越えてデータグラムを中継するためのインターネット プロトコル スイートの主要な通信プロトコルです",
    "IP_EXAMPLE_WITH_RANGE_CIDR": "例：********、********、********-********、********/24",
    "IP_EXAMPLE": "例：********、********",
    "IP_FQDN_GROUPS": "IPおよびFQDNグループ",
    "IP_INFO": "IP情報",
    "IP_POOL": "IPプール",
    "IP_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないIPトラフィックが特定されます",
    "IP_UNKNOWN": "不明なIP",
    "IP": "IP",
    "IP6_DESC": " インターネット プロトコル バージョン6 (IPv6) は、インターネット プロトコルの最新バージョンです。通信プロトコルは、ネットワーク上のコンピューターに識別および位置情報システムを提供し、インターネット経由でトラフィックをルーティングします",
    "IP6": "IP6",
    "IPASS_DESC": " iPassはアクセス数、アクセス先のコンテンツ、使用するデバイス数に一切制限を設けない、グローバルなモバイル接続における業界のパイオニアです",
    "IPASS": "iPass",
    "IPERF_DESC": " iperfプロトコルは、ネットワーク パフォーマンス測定用のセルフタイトルツールによって使用されます",
    "IPERF": "Iperf",
    "IPLAYER_DESC": " iPlayer",
    "IPLAYER": "iPlayer",
    "IPSEC_DESC": " IPSecプロトコルは、ホスト通信を保護するためのサービスを提供します。IPsecには、送信者の認証を許可する認証ヘッダー(AH)と、送信者の認証とデータの暗号化の両方を許可するセキュリティ ペイロードのカプセル化(ESP)の2つのセキュリティ サービスがあります",
    "IPSEC": "IpSec",
    "IPV4_ALL_DESTINATION_GROUP_NAME": "IP V4のすべての宛先グループ名",
    "IPV4_DNS_RESOLUTION_ONLY": "IPv4 DNS解決のみ",
    "IPV4": "IPv4カプセル化",
    "IPV6_HERE": "IPv6 i-am-here",
    "IPV6_WHERE": "IPv6 where-are-you",
    "IPV6": "IP6ヘッダー",
    "IPV6CP_DESC": " このプロトコルは、IPv6 over PPPの確立と構成に使用されます",
    "IPV6CP": "IPV6CP",
    "IPXRIP_DESC": " RIPIPXは、NovellネットワークのRIPプロトコルに相当します",
    "IPXRIP": "RIPIPX",
    "IQIYI_DESC": " このプロトコル プラグインは、ホストiqiyi.comへのhttpトラフィックを分類します。コモン ネーム.iqiyi.comへのsslトラフィックも分類します",
    "IQIYI": "iqiyi.com",
    "IRAN_ASIA_TEHRAN": "アジア/テヘラン",
    "IRAN": "イラン",
    "IRAQ_ASIA_BAGHDAD": "アジア/バグダード",
    "IRAQ": "イラク共和国",
    "IRC_DESC": " IRC (Internet Relay Chat)はインスタント メッセージのプロトコルです",
    "IRC_GALLERIA_DESC": " このプロトコル プラグインは、ホストirc-galleria.netへのhttpトラフィックを分類します",
    "IRC_GALLERIA": "IRC-Galleria",
    "IRC_TRANSFER_DESC": " このプロトコルは、IRC File Transferでデータを転送するために使用されます",
    "IRC_TRANSFER": "IRC File Transfer",
    "IRC": "IRC",
    "IRCS_DESC": " IRCsはIRCプロトコルのセキュアなバージョンです",
    "IRCS": "Secure IRC",
    "IRELAND_EUROPE_DUBLIN": "ヨーロッパ/ダブリン",
    "IRELAND": "アイルランド",
    "IS_NULL": "次の値がNullである",
    "ISAE_3402": "ISAE 3402",
    "ISAKMP_DESC": " Internet Security Association and Key Management Protocol (ISAKMP)はSecurity Associations (SA)を確立、ネゴシエート、変更、削除する手順とパケット形式を定義します",
    "ISAKMP": "ISAKMP",
    "ISLE_OF_MAN_EUROPE_ISLE_OF_MAN": "ヨーロッパ/マン島",
    "ISLE_OF_MAN": "マン島",
    "ISRAEL_ASIA_JERUSALEM": "アジア/エルサレム",
    "ISRAEL": "イスラエル国",
    "ISSUER": "発行者",
    "ITALKI_DESC": " このプロトコル プラグインは、ホストitalki.comへのhttpトラフィックを分類します。コモン ネーム.italki.comへのsslトラフィックも分類します",
    "ITALKI": "italki",
    "ITALY_EUROPE_ROME": "ヨーロッパ/ローマ",
    "ITALY": "イタリア共和国",
    "ITALYNORTH": "(ヨーロッパ)イタリア北部",
    "ITEMS_TOTAL": "項目の合計",
    "ITSMY_DESC": " このプロトコル プラグインは、ホストmobile.itsmy.comへのhttpトラフィックを分類します",
    "ITSMY": "GameCloud (itsmy.com)",
    "ITUNES_DESC": " iTunesはApple独自のデジタル メディア プレイヤー アプリで、デジタル音楽および動画ファイルの再生と整理に使用されます",
    "ITUNES": "iTunes",
    "ITUNESU": "iTunes U",
    "IVORY_COAST": "コートジボワール",
    "IWF": "IWF",
    "IWIW_DESC": " このプロトコル プラグインは、ホストiwiw.huへのhttpトラフィックを分類します",
    "IWIW": "Iwiw",
    "JABBER_DESC": " Jabberは、XMPPプロトコルを採用したオープン スタンダードのインスタント メッセージングおよびプレゼンス システムです",
    "JABBER_TRANSFER_DESC": " Jabber transferは、2つのJabberクライアント間でファイルを転送するためのオープン規格です",
    "JABBER_TRANSFER": "Jabber File Transfer",
    "JABBER": "Jabber",
    "JAIKU_DESC": " このプロトコル プラグインは、ホストjaiku.comへのhttpトラフィックを分類します",
    "JAIKU": "jaiku.com",
    "JAILBREAK": "ジェイルブレイクが有効化されました",
    "JAILBROKEN_ROOTED": "ジェイルブレーク済み/ルート化済み",
    "JAJAH_DESC": " JajahはTelefonica Europeが所有するVoIPプロバイダーです",
    "JAJAH": "Jajah",
    "JAMAICA_AMERICA_JAMAICA": "アメリカ/ジャマイカ",
    "JAMAICA": "ジャマイカ",
    "JAMMERDIRECT_DESC": " このプロトコル プラグインは、ホストjammerdirect.comへのhttpトラフィックを分類します",
    "JAMMERDIRECT": "Jammer Direct",
    "JANGO_DESC": " このプロトコル プラグインは、ホストjango.comへのhttpトラフィックを分類します",
    "JANGO": "Jango",
    "JANUS_END": "Janusエラー コードの末尾。",
    "JAPAN_ASIA_TOKYO": "アジア/東京",
    "JAPAN": "日本",
    "JAPANEAST": "(アジア太平洋)東日本",
    "JAPANWEST": "(アジア太平洋)西日本",
    "JAVA_UPDATE_DESC": " Java UpdateはJava仮想マシン(JVM)をアップデートするためのプロトコルです",
    "JAVA_UPDATE": "Java Update",
    "JEDI_DESC": " JEDIは、CITRIXストリーミング接続プロトコルの名前です",
    "JEDI": "JEDI",
    "JERSEY_EUROPE_JERSEY": "ヨーロッパ/ジャージー島",
    "JERSEY": "ジャージー島",
    "JINGDONG_DESC": " 中国で人気のあるオンラインのハイテクショップ",
    "JINGDONG": "JingDong",
    "JIOINDIACENTRAL": "(アジア太平洋) Jio India Central",
    "JIOINDIAWEST": "(アジア太平洋) Jio India West",
    "JIRA_DESC": " このプロトコル プラグインは、ホストonjira.comへのhttpトラフィックを分類しますコモン ネームonjira.comへのsslトラフィックも分類します",
    "JIRA": "JIRA",
    "JIVE_DESC": " Jive Softwareは、カリフォルニア州パロアルトに本社を置くソーシャル ビジネス ソフトウェア業界のソフトウェア会社です",
    "JIVE": "Jive",
    "JNE_DESC": " このプロトコル プラグインは、ホストjne.co.idへのhttpトラフィックを分類します",
    "JNE": "JNE",
    "JOB_EMPLOYMENT_SEARCH_DESC": " 求人広告または雇用に関連するサイト。",
    "JOB_EMPLOYMENT_SEARCH": "求人情報",
    "JOB_SEARCH_DESC": " 求人広告または雇用に関連するサイト。",
    "JOB_SEARCH": "求人情報",
    "JOBSTREET_DESC": " このプロトコル プラグインは、ホストjobstreet.co.idへのhttpトラフィックを分類します",
    "JOBSTREET": "JobStreet",
    "JOONGANG_DAILY_DESC": " このプロトコル プラグインは、www.joins.comおよびwww.joinsmsn.comへのhttpトラフィックを分類します",
    "JOONGANG_DAILY": "Joongang Daily",
    "JOOST_DESC": " Joost",
    "JOOST": "Joost",
    "JORDAN_ASIA_AMMAN": "アジア/アンマン",
    "JORDAN": "ヨルダン・ハシミテ王国",
    "JPEG": "JPEGファイル",
    "JS_VIEW": "JS View",
    "JSON_CLOUDINFO_STAGGER_SIZE": "クラウド情報がクラウドCAからFCC CAに送信されるインスタンスの数",
    "JSON_CLOUDINFO": "クラウド情報をクラウドCAからFCC CAにJSONとして送信",
    "JSONNOTFOUND": "JSONファイルが見つかりません",
    "JUBII_DESC": " このプロトコル プラグインは、ホストjubii.dkへのhttpトラフィックを分類します",
    "JUBII": "Jubii",
    "JUSTIN_TV_DESC": " このプロトコル プラグインは、ホストjustin.tvへのhttpトラフィックを分類します。コモン ネーム.justin.tvへのsslトラフィックも分類します",
    "JUSTIN_TV": "Justin.tv",
    "K_12_SEX_EDUCATION": "性教育(高校生以下向け)",
    "K_12": "高校生以下",
    "KAIOO_DESC": " このプロトコル プラグインは、ホストkaioo.comへのhttpトラフィックを分類します",
    "KAIOO": "kaioo.com",
    "KAIXIN_CHAT_DESC": " このプロトコル プラグインは、ホストkaixin001.comへのhttpトラフィックを分類します",
    "KAIXIN_CHAT": "kaixin",
    "KAKAKU_DESC": " 価格比較および製品検証に特化したWebサイト",
    "KAKAKU": "kakaku.com",
    "KAKAOTALK_DESC": " KakaoTalkはモバイル端末用のインスタント メッセージ プラットフォームです。ユーザーやユーザー グループ間でメッセージを送信したり、写真、動画、連絡先情報を共有したりできます",
    "KAKAOTALK": "KakaoTalk",
    "KANKAN_DESC": " 中国の動画配信Webサイト",
    "KANKAN": "kankan.com",
    "KAPANLAGI_DESC": " このプロトコル プラグインは、ホストkapanlagi.comへのhttpトラフィックを分類します",
    "KAPANLAGI": "KapanLagi",
    "KAROSGAME_DESC": " このプロトコル プラグインは、ホストkarosgame.ruへのhttpトラフィックを分類します",
    "KAROSGAME": "Karos (karosgame.ru)",
    "KASKUS_DESC": " このプロトコル プラグインは、ホストkaskus.co.idへのhttpトラフィックを分類します",
    "KASKUS": "Kaskus",
    "KASPERSKY_DESC": " このプロトコル プラグインは、ホストkaspersky.comへのhttpトラフィックを分類します",
    "KASPERSKY_UPDATE_DESC": " Kaspersky_updateは、Kasperskyソフトウェアをアップデートするためのプロトコルです",
    "KASPERSKY_UPDATE": "Kaspersky Update",
    "KASPERSKY": "Kaspersky",
    "KAZAA_DESC": " KazaaはP2Pプロトコルです",
    "KAZAA": "Kazaa",
    "KAZAKHSTAN_ASIA_ALMATY": "アジア/アルマトイ",
    "KAZAKHSTAN_ASIA_AQTAU": "アジア/アクタウ",
    "KAZAKHSTAN_ASIA_AQTOBE": "アジア/アクトベ",
    "KAZAKHSTAN_ASIA_ORAL": "アジア/オラル",
    "KAZAKHSTAN_ASIA_QYZYLORDA": "アジア/クズロルダ",
    "KAZAKHSTAN": "カザフスタン共和国",
    "KB_BANK_DESC": " このプロトコル プラグインは、ホストkbstar.comへのhttpトラフィックを分類します。コモン ネーム.kbstar.comへのsslトラフィックも分類します",
    "KB_BANK": "KB Bank (kbstar.com)",
    "KBS_DESC": " このプロトコル プラグインは、ホストwww.kbs.co.krへのhttpトラフィックを分類します",
    "KBSTAR_DESC": " このWebサービスは終了しました",
    "KBSTAR": "kbstar (終了)",
    "KEEPLIVE": "データが流れていない場合、TCP接続を維持するためにキープアライブ レコードを送信",
    "KEEZMOVIES_DESC": " このプロトコル プラグインは、ホストkeezmovies.comへのhttpトラフィックを分類します",
    "KEEZMOVIES": "KeezMovies",
    "KEK_ROTATION": "sme上のキー回転の有効化 ",
    "KEMENKUMHAM_DESC": " このプロトコル プラグインは、ホストkemenkumham.go.idへのhttpトラフィックを分類します",
    "KEMENKUMHAM": "kemenkumham.go.id",
    "KENYA_AFRICA_NAIROBI": "アフリカ/ナイロビ",
    "KENYA": "ケニア共和国",
    "KERBEROS_SEC_DESC": "Kerberosはコンピューター ネットワーク認証を行うプロトコルで、セキュアな形式でお互いのIDを証明するためにセキュアでないネットワーク上で通信するノードを許可し、「チケット」をベースに機能します。",
    "KERBEROS_SEC": "Kerberos",
    "KERBEROS_SHARED_KEY": "ドメイン信頼パスワード",
    "KEY": "キー",
    "KHAN_DESC": " このプロトコル プラグインは、ホストkhan.co.krへのhttpトラフィックを分類します",
    "KHAN": "Khan (khan.co.kr)",
    "KHANACADEMY": "Khan Academy",
    "KICKASSTORRENTS_DESC": " KickAssTorrentsは、トレントとマグネットの検索エンジンです",
    "KICKASSTORRENTS": "KickAssTorrents",
    "KIK_DESC": " Kik Messengerは、中国のインスタント メッセージ サービスです",
    "KIK": "Kik Messenger",
    "KINDLE": "Kindle",
    "KINO_DESC": " このプロトコル プラグインは、ホストkino.toへのhttpトラフィックを分類します",
    "KINO": "Kino",
    "KIRIBATI_PACIFIC_ENDERBURY": "太平洋/エンダーベリー島",
    "KIRIBATI_PACIFIC_KIRITIMATI": "太平洋/キリスィマスィ島",
    "KIRIBATI_PACIFIC_TARAWA": "太平洋/タラワ",
    "KIRIBATI": "キリバス共和国",
    "KIWIBOX_DESC": " このプロトコル プラグインは、ホストkiwibox.comへのhttpトラフィックを分類します",
    "KIWIBOX": "Kiwibox",
    "KLIKBCA_DESC": " このプロトコル プラグインは、ホストklikbca.comへのhttpトラフィックを分類します",
    "KLIKBCA": "KlikBCA",
    "KOMPAS_DESC": " このプロトコル プラグインは、ホストkompas.comへのhttpトラフィックを分類します",
    "KOMPAS": "Kompas",
    "KOMPASIANA_DESC": " このプロトコル プラグインは、ホストkompasiana.comへのhttpトラフィックを分類します",
    "KOMPASIANA": "Kompasiana",
    "KONAMINET_DESC": " このプロトコル プラグインは、ホストkonaminet.jpへのhttpトラフィックを分類します。コモン ネームkonaminet.jpへのsslトラフィックも分類します",
    "KONAMINET": "KONAMI",
    "KOOLIM_DESC": " このプロトコル プラグインは、ホストkoolim.comへのhttpトラフィックを分類します",
    "KOOLIM": "KoolIm",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF_ASIA_PYONGYANG": "アジア/ピョンヤン",
    "KOREA_DEMOCRATIC_PEOPLES_REPUBLIC_OF": "朝鮮民主主義人民共和国",
    "KOREA_REPUBLIC_OF_ASIA_SEOUL": "アジア/ソウル",
    "KOREA_REPUBLIC_OF": "大韓民国",
    "KOREA": "韓国",
    "KOREACENTRAL": "(アジア太平洋地域)韓国中部",
    "KOREASOUTH": "(アジア太平洋地域)韓国南部",
    "KUWAIT_ASIA_KUWAIT": "アジア/クウェート国",
    "KUWAIT": "クウェート国",
    "KYRGYZSTAN_ASIA_BISHKEK": "アジア/ビシュケク",
    "KYRGYZSTAN": "キルギス共和国",
    "L2TP_DESC": " レイヤー2トンネリング プロトコル(L2TP)は、インターネット サービス プロバイダー(ISP)がインターネット上で仮想プライベート ネットワーク(VPN)を操作するために使用するポイントツーポイント トンネリング プロトコル(PPTP)の拡張です",
    "L2TP": "L2TP",
    "LAN_DESTINATIONS_GROUP": "LAN宛先グループ",
    "LAN_DNS": "LAN DNS",
    "LAN_IP_GROUP": "LAN IPグループ",
    "LAN_PRI_DNS": "LANプライマリーDNSサーバー",
    "LAN_RX": "LAN Rx",
    "LAN_SEC_DNS": "LANセカンダリーDNSサーバー",
    "LAN_TX": "LAN Tx",
    "LAN": "LAN",
    "LANGUAGE": "言語",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC_ASIA_VIENTIANE": "アジア/ヴィエンチャン",
    "LAO_PEOPLES_DEMOCRATIC_REPUBLIC": "ラオス人民民主共和国",
    "LAOS": "ラオス",
    "LARGE": "大",
    "last_1_hour": "過去1時間",
    "LAST_1_HOUR": "過去1時間",
    "last_1_min": "過去1分",
    "LAST_1_MIN": "過去1分",
    "last_1_month": "過去1か月",
    "LAST_1_MONTH": "過去1か月",
    "last_1_week": "過去1週間",
    "LAST_1_WEEK": "過去1週間",
    "last_10_hours": "過去10時間",
    "LAST_10_HOURS": "過去10時間",
    "last_15_mins": "過去15分",
    "LAST_15_MINS": "過去15分",
    "last_2_hours": "過去2時間",
    "LAST_2_HOURS": "過去2時間",
    "last_2_mins": "過去2分",
    "LAST_2_MINS": "過去2分",
    "LAST_24_HOURS": "過去24時間",
    "last_24_hrs": "過去24時間",
    "last_30_mins": "過去30分",
    "LAST_30_MINS": "過去30分",
    "last_5_hours": "過去5時間",
    "LAST_5_HOURS": "過去5時間",
    "last_5_mins": "過去5分",
    "LAST_5_MINS": "過去5分",
    "LAST_ACTIVE": "前回のアクティブ日",
    "LAST_CONFIG_TEMPLATE_PUSH_FAILED": "前回の構成テンプレートのプッシュに失敗しました",
    "LAST_CONNECTIVITY_TEST": "前回の接続テスト",
    "LAST_HEARTBEAT_RECEIVED_ON": "最後に受信したHeartBeat",
    "LAST_KNOW_IP": "最後の既知のIP",
    "LAST_KNOWN_IP": "最後の既知のIP",
    "LAST_MODIFIED_BY": "最終更新者",
    "LAST_MODIFIED_ON": "最終更新日",
    "LAST_SYNC": "最後の同期",
    "LAST_UPDATE": "最終更新日",
    "LAST_UPDATES": "最終更新",
    "LAST_UPGRADE_ON": "最終アップグレード日",
    "LASTEST_SYNC": "最新の同期",
    "LATITUDE": "緯度",
    "LATVIA_EUROPE_RIGA": "ヨーロッパ/リガ",
    "LATVIA": "ラトビア共和国",
    "LAUNCH_CLOUDFORMATION_TEMPLATE_AWS_CONSOLE": "AWSコンソールでCloudFormationテンプレートを起動",
    "LAUNCH_CLOUDFORMATION": "CloudFormationを起動",
    "LDAP_CONNECTION_DOWN": "LDAP接続の停止",
    "LDAP_DESC": " LDAP (Lightweight Directory Access Protocol)は、ディレクトリー サービスへのアクセスに使用されるプロトコルです。Windows環境では、このプロトコルを使用してクエリーをActive Directoryに送信します",
    "LDAP_FAILURE": "LDAP障害",
    "LDAP_SETTINGS": "LDAPの設定",
    "LDAP_SUCCESS": "LDAP成功",
    "LDAP": "LDAP",
    "LDAPS_DESC": " Secure LDAPは、LDAPプロトコルのセキュアなバージョンです",
    "LDAPS": "LDAPS",
    "LEARN_TO_SETUP_EC2_INSTANCE": "EC2インスタンスのセットアップ方法",
    "LEBANON_ASIA_BEIRUT": "アジア/ベイルート",
    "LEBANON": "レバノン共和国",
    "LESOTHO_AFRICA_MASERU": "アフリカ/マセル",
    "LESOTHO": "レソト王国",
    "LESS_THAN": "次の値より小さい",
    "LIBERIA_AFRICA_MONROVIA": "アフリカ/モンロビア",
    "LIBERIA": "リベリア共和国",
    "LIBYA": "リビア国",
    "LIBYAN_ARAB_JAMAHIRIYA_AFRICA_TRIPOLI": "アフリカ/トリポリ",
    "LIBYAN_ARAB_JAMAHIRIYA": "リビア国",
    "LIECHTENSTEIN_EUROPE_VADUZ": "ヨーロッパ/ファドゥーツ",
    "LIECHTENSTEIN": "リヒテンシュタイン公国",
    "LIMITED_AVAILABILITY": "限られた可用性",
    "LIMITED": "制限",
    "LINGERIE_BIKINI": "ランジェリー/ビキニ",
    "LINK_SCORE": "リンク スコア",
    "LINUX_OS": "Linux OS",
    "LINUX": "Linux",
    "LITHUANIA_EUROPE_VILNIUS": "ヨーロッパ/ヴィリニュス",
    "LITHUANIA": "リトアニア共和国",
    "LOAD_BALANCER_IP_ADDRESS": "ロード バランサーのIPアドレス",
    "LOAD_BALANCER": "ロード バランサー",
    "LOAD_MORE": "さらに読み込む",
    "LOC_DEFAULT": "リモート ユーザー",
    "LOCAL_EGRESS": "ローカル出力",
    "LOCAL_TIME_ZONE_CC_GROUP": "Cloud Connectorグループのローカル タイム ゾーン",
    "LOCATION_ALREADY_IN_USE_PLEASE_ENTER_A_NEW_LOCATION": "ロケーションが無効です。新しいものを入力してください。",
    "LOCATION_CREATION": "ロケーションを作成",
    "LOCATION_DETAILS_OPTIONAL": "ロケーションの詳細(省略可)",
    "LOCATION_DETAILS": "ロケーションの詳細",
    "LOCATION_GROUP_TYPE": "ロケーション タイプ",
    "LOCATION_GROUP": "ロケーション グループ",
    "LOCATION_GROUPS": "ロケーション グループ",
    "LOCATION_INFORMATION": "ロケーション情報",
    "LOCATION_MANAGEMENT": "ロケーション管理",
    "LOCATION_NAME": "ロケーション名",
    "LOCATION_SUBLOCATION": "ロケーション/サブロケーション",
    "LOCATION_TEMPLATE": "ロケーション テンプレート",
    "LOCATION_TEMPLATES": "ロケーション テンプレート",
    "LOCATION_TYPE": "ロケーション タイプ",
    "LOCATION_UNAUTHENTICATED_AUP_FREQUENCY": "カスタムAUPの頻度(日数)",
    "LOCATION": "ロケーション",
    "LOCATIONS": "ロケーション",
    "LOG_AND_CONTROL_FORWARDING": "ログとコントロール転送",
    "LOG_AND_CONTROL_GATEWAY": "ログとコントロール ゲートウェイ",
    "LOG_AND_CONTROL_GW": "ログとコントロールGW",
    "LOG_AND_CONTROL": "ログとコントロール",
    "LOG_GW_CONN_CLOSE": "ログ ゲートウェイのアクティブ接続がクローズされました。",
    "LOG_GW_CONN_SETUP_FAIL": "ログ ゲートウェイ接続のセットアップに失敗しました(内部エラー)。",
    "LOG_GW_CONNECT_FAIL": "ログ ゲートウェイ接続に失敗しました(ネットワーク エラー)。",
    "LOG_GW_DNS_RESOLVE_FAIL": "ログ ゲートウェイのDNS解決に失敗しました。",
    "LOG_GW_KA_FAIL": "ログ ゲートウェイ接続のキープアライブに失敗しました。",
    "LOG_GW_NO_CONN": "ログ ゲートウェイ接続はクライアントによってまだ開始されていません。",
    "LOG_GW_PAC_RESOLVE_FAIL": "ログ ゲートウェイのPAC解決に失敗しました。",
    "LOG_GW_PAC_RESOLVE_NOIP": "ログ ゲートウェイのPAC解決でIPSが返されませんでした。",
    "LOG_GW_PROTO_MSG_ERROR": "ログGWレスポンスのメッセージ形式エラー。",
    "LOG_GW_SRV_ERR_RESPONSE": "ログ ゲートウェイがサーバーからHTTPエラー レスポンスを受信しました。",
    "LOG_GW_UNHEALTHY": "ログ ゲートウェイが異常です(一時的な状態)。",
    "LOG_INFO_TYPE": "ログ情報タイプ",
    "LOG_STREAMING": "ログ ストリーミング",
    "LOG_TIME": "ログ時刻",
    "LOG_TYPE": "ログ タイプ",
    "LOGGED_TIME": "ログに記録された時刻",
    "LOGGING": "ログ記録",
    "LOGIN_ID": "ログインID",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_GROUP": "ドロップダウン メニューからApp Connectorグループ名を選択します。",
    "LOGIN_TO_ZPA_PORTAL_TO_GET_PROVISIONING_KEY": "ドロップダウン メニューからプロビジョニング キーを選択します",
    "LOGIN_TYPE": "ログイン タイプ",
    "LOGIN": "ログイン",
    "LOGS": "ログ",
    "LONGITUDE": "経度",
    "LOOKUP_URL_CATEGORY": "ルックアップURLの分類",
    "LOOKUP": "ルックアップURL",
    "LUXEMBOURG_EUROPE_LUXEMBOURG": "ヨーロッパ/ルクセンブルク",
    "LUXEMBOURG": "ルクセンブルク大公国",
    "MAC_ADDRESS": "MACアドレス",
    "MAC_OS": "Mac",
    "MACAO_ASIA_MACAU": "アジア/マカオ",
    "MACAO": "マカオ",
    "MACAU": "マカオ",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF_EUROPE_SKOPJE": "ヨーロッパ/スコピエ",
    "MACEDONIA_THE_FORMER_YUGOSLAV_REPUBLIC_OF": "マケドニア(旧ユーゴスラビア共和国)",
    "MACEDONIA": "マケドニア共和国",
    "MADAGASCAR_INDIAN_ANTANANARIVO": "インド洋/アンタナナリボ",
    "MADAGASCAR": "マダガスカル共和国",
    "MALAWI_AFRICA_BLANTYRE": "アフリカ/ブランタイヤ",
    "MALAWI": "マラウイ共和国",
    "MALAYSIA_ASIA_KUALA_LUMPUR": "アジア/クアラルンプール",
    "MALAYSIA_ASIA_KUCHING": "アジア/クチン",
    "MALAYSIA": "マレーシア",
    "MALDIVES_INDIAN_MALDIVES": "インド洋/モルディブ共和国",
    "MALDIVES": "モルディブ共和国",
    "MALI_AFRICA_BAMAKO": "アフリカ/バマコ",
    "MALI": "マリ共和国",
    "MALICIOUS_TLD": "悪意のあるTLD",
    "MALTA_EUROPE_MALTA": "ヨーロッパ/マルタ共和国",
    "MALTA": "マルタ共和国",
    "MALWARE_SITE": "悪意のあるコンテンツ",
    "MANAGED_APP_DEF_ZPA": "ZPAでの管理対象アプリの定義",
    "MANAGED_APP_DEF": "管理対象アプリの定義",
    "Management IP": "管理IP",
    "MANAGEMENT_DEFAULT_GATEWAY": "管理デフォルト ゲートウェイ",
    "MANAGEMENT_DETAILS": "管理の詳細",
    "MANAGEMENT_DNS_SERVER": "管理DNSサーバー",
    "MANAGEMENT_INFORMATION": "管理情報",
    "MANAGEMENT_INTERFACE": "管理インターフェイス",
    "MANAGEMENT_IP_ADDRESS_POOL": "管理IPアドレス プール",
    "MANAGEMENT_IP_ADDRESS": "管理IPアドレス",
    "MANAGEMENT_IP": "管理IP",
    "MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS": "管理送信ゲートウェイIPアドレス",
    "MANAGEMENT": "管理",
    "MANUAL_MANAGEMENT_IP": "手動管理IP",
    "MANUAL_SERVICE_IP": "手動サービス IP",
    "MANUAL": "手動",
    "MARIJUANA": "マリファナ",
    "MARSHALL_ISLANDS_PACIFIC_KWAJALEIN": "太平洋/クェゼリン環礁",
    "MARSHALL_ISLANDS_PACIFIC_MAJURO": "太平洋/マジュロ",
    "MARSHALL_ISLANDS": "マーシャル諸島共和国",
    "MARTINIQUE_AMERICA_MARTINIQUE": "アメリカ/マルティニーク",
    "MARTINIQUE": "マルティニーク",
    "MATURE_HUMOR": "大人向けのユーモア",
    "MAURITANIA_AFRICA_NOUAKCHOTT": "アフリカ/ヌアクショット",
    "MAURITANIA": "モーリタニア・イスラム共和国",
    "MAURITIUS_INDIAN_MAURITIUS": "インド洋/モーリシャス共和国",
    "MAURITIUS": "モーリシャス共和国",
    "MAX_AMF_NUMBER": "最大5つのAMFを追加できます。",
    "MAX_CAPACITY": "最大容量",
    "MAX_CHARACTER_LIMIT_EXCEEDED": "最大文字数制限を超えました",
    "MAX_EC_COUNT": "最大カウント",
    "MAX_INTERFACES_NUMBER": "インターフェイスの最大数が追加されました。",
    "MAX_LEASE_TIME": "最大リース時間(秒)",
    "MAX_NUM_DESINATION_ADRESS_IS_1000": "ルールごとに許可される宛先アドレスの最大数は1000です。",
    "MAX_REUSE_PROVISIONING_KEY": "プロビジョニング キーの最大再利用",
    "MAX_STATIC_ROUTES_NUMBER": "静的ルートの最大数は32です。",
    "MAX_SUB_INTERFACES_NUMBER": "インターフェイスの最大数に達しました。",
    "MAX_SUBINTERFACE_STATIC_LEASES": "静的リースの最大数は32です。",
    "MAX_USER_TUNNELS_PER_CC": "コネクターごとの最大ユーザー トンネル数",
    "MAX_VALUE_LIMIT_ERROR": "値が次の制限を超えています：",
    "MAX_WAN_INTERFACES_NUMBER": "最大2つのWANインターフェイスを追加できます。",
    "MAX": "最大",
    "MAYOTTE_INDIAN_MAYOTTE": "インド洋/マヨット",
    "MAYOTTE": "マヨット",
    "ME_CENTRAL_1": "中東(UAE)",
    "ME_CENTRAL1_A": "me-central1-a",
    "ME_CENTRAL1_B": "me-central1-b",
    "ME_CENTRAL1_C": "me-central1-c",
    "ME_CENTRAL1": "me-central1",
    "ME_SOUTH_1": "中東(バーレーン)",
    "ME_SOUTH_1A": "me-south-1a",
    "ME_SOUTH_1B": "me-south-1b",
    "ME_SOUTH_1C": "me-south-1c",
    "ME_WEST1_A": "me-west1-a",
    "ME_WEST1_B": "me-west1-b",
    "ME_WEST1_C": "me-west1-c",
    "ME_WEST1": "me-west1",
    "MEDIUM": "中",
    "MEMORY": "メモリー",
    "MEXICO_AMERICA_CANCUN": "アメリカ/カンクン",
    "MEXICO_AMERICA_CHIHUAHUA": "アメリカ/チワワ",
    "MEXICO_AMERICA_HERMOSILLO": "アメリカ/エルモシージョ",
    "MEXICO_AMERICA_MAZATLAN": "アメリカ/マサトラン",
    "MEXICO_AMERICA_MERIDA": "アメリカ/メリダ",
    "MEXICO_AMERICA_MEXICO_CITY": "アメリカ/メキシコシティ",
    "MEXICO_AMERICA_MONTERREY": "アメリカ/モンテレイ",
    "MEXICO_AMERICA_TIJUANA": "アメリカ/ティフアナ",
    "MEXICO": "メキシコ合衆国",
    "MGCP_CA_DESC": "Media Gateway Control Protocol CAサービス",
    "MGCP_CA": "MGCPコール エージェント",
    "MGCP_DESC": " MGCPプロトコルは、音声IPアプリケーションのシグナリング プロトコルとして使用されます",
    "MGCP_UA_DESC": "Media Gateway Control Protocol UAサービス",
    "MGCP_UA": "MGCPユーザー エージェント",
    "MGCP": "MGCP",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_KOSRAE": "太平洋/コスラエ州",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_PONAPE": "太平洋/ポンペイ島",
    "MICRONESIA_FEDERATED_STATES_OF_PACIFIC_TRUK": "太平洋/チューク諸島",
    "MICRONESIA_FEDERATED_STATES_OF": "ミクロネシア連邦",
    "MICRONESIA": "ミクロネシア",
    "MICROSOFT_AZURE": "Microsoft Azure",
    "MICROSOFT_HYPER_V": "Microsoft Hyper-V",
    "MILITANCY_HATE_AND_EXTREMISM": "過激思想",
    "MILITARY": "軍事",
    "MIN_CAPACITY": "最小容量",
    "MIN_VALUE_LIMIT_ERROR": "値が次の制限を下回っています：",
    "MINUTES": "分",
    "MISCELLANEOUS_OR_UNKNOWN": "未分類または不明",
    "MODEL_NUMBER": "モデル番号",
    "MODEL": "モデル",
    "MOLDOVA_EUROPE_CHISINAU": "ヨーロッパ/キシナウ",
    "MOLDOVA": "モルドバ共和国",
    "MONACO_EUROPE_MONACO": "ヨーロッパ/モナコ",
    "MONACO": "モナコ公国",
    "MONDAY": "月曜日",
    "MONGOLIA_ASIA_CHOIBALSAN": "アジア/チョイバルサン",
    "MONGOLIA_ASIA_HOVD": "アジア/ホブド県",
    "MONGOLIA_ASIA_ULAANBAATAR": "アジア/ウランバートル",
    "MONGOLIA": "モンゴル国",
    "MONTENEGRO_EUROPE_PODGORICA": "ヨーロッパ/ポドゴリツァ",
    "MONTENEGRO": "モンテネグロ",
    "MONTSERRAT_AMERICA_MONTSERRAT": "アメリカ/モントセラト",
    "MONTSERRAT": "モントセラト",
    "MORE_ITEMS_SELECTED": "追加の項目が選択されました",
    "MOROCCO_AFRICA_CASABLANCA": "アフリカ/カサブランカ",
    "MOROCCO": "モロッコ王国",
    "MOZAMBIQUE_AFRICA_MAPUTO": "アフリカ/マプト",
    "MOZAMBIQUE": "モザンビーク共和国",
    "MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS": "Microsoft Azure Deployment Guide for NSS",
    "MSI_URL_32BITS": "MSI URL (32ビット)",
    "MSI_URL_64BITS": "MSI URL (64ビット)",
    "MSN_DESC": " MSNプロトコルはインスタント メッセージを通信するためのプロトコルで、Microsoft Messengerで使用されます",
    "MSN_EXPLORER": "MSN Explorer",
    "MSN_GROUPS_DEPRECATED": "MSN Groups",
    "MSN_GROUPS_DESC": " MSN GroupsはMSNネットワークのWebサイトの一部であり、Webページ、ホストされた画像、メッセージ ボードが含まれていました。MSN Groupsは、オンライン アプリケーションやサービスをWindows Liveブランドに移行する一環として、2009年2月に閉鎖され、その後Windows Live Groupsとしてリニューアルされました",
    "MSN_GROUPS": "MSN Groups",
    "MSN_MESSENGER": "MSN Messenger",
    "MSN_MSDW": "MSN MSDW",
    "MSN_SEARCH_DESC": " このプロトコルは、ユーザー クエリーをMSN Live検索エンジンに送信するために使用されます",
    "MSN_SEARCH": "MSN Search",
    "MSN_VIDEO_DESC": " このプロトコルは、ビデオ通話のためにMSN Messengerによって使用されます(MSNバージョン8.X以降は使用されていません)",
    "MSN_VIDEO": "MSN Video",
    "MSN_WEB_MESSENGER": "MSN Messenger",
    "MSN": "MSN",
    "MSNMOBILE_DESC": " MSN Mobileはモバイル端末用のMSNインスタント メッセンジャーです",
    "MSNMOBILE": "MSN Mobile",
    "MTS_ERROR": "MTSアプリ サーバー エラー。",
    "MTU_TITLE": "MTU",
    "MULTIFEEDLOG": "メトリクス",
    "MULTIPLE_APPLIANCES_ADDED_INFO": "新しいアプライアンスは[アプライアンス]ページで確認できます。詳細は、コネクターの{1}ヘルプ ポータル{2}を参照してください。",
    "MULTIPLE_APPLIANCES_ADDED": "新しいアプライアンスがテナントに追加されました。",
    "MULTIPLE": "複数",
    "MUSIC": "音楽",
    "MY_ACTIVATION_STATUS": "自分のアクティベーション ステータス",
    "MY_PROFILE": "自分のプロファイル",
    "MYANMAR_ASIA_RANGOON": "アジア/ヤンゴン",
    "MYANMAR": "ミャンマー連邦共和国",
    "NA": "N/A",
    "NAME_MAX_LIMIT_ERROR": "このフィールドは255文字以内にしてください",
    "NAME_VALUE_PAIRS": "名前と値のペア",
    "NAME": "名前",
    "NAMESPACE_OPTIONAL": "名前空間(省略可)",
    "NAMESPACE": "名前空間",
    "NAMIBIA_AFRICA_WINDHOEK": "アフリカ/ウィントフック",
    "NAMIBIA": "ナミビア共和国",
    "NANOLOG_STREAMING_SERVICES": "Nanologストリーミング サービス",
    "NAT_IP_ADDRESS": "NAT IPアドレス",
    "NAURU_PACIFIC_NAURU": "太平洋/ナウル共和国",
    "NAURU": "ナウル共和国",
    "NAVIGATE_TO_ADMINISTRATION": "[管理]に移動",
    "NEPAL_ASIA_KATMANDU": "アジア/カトマンズ",
    "NEPAL": "ネパール連邦民主共和国",
    "NET_MASK": "ネット マスク",
    "NETBIOS_DESC": "NetBIOS名/データグラム/セッション サービス",
    "NETBIOS": "NetBIOS",
    "NETHERLANDS_ANTILLES_AMERICA_CURACAO": "アメリカ/キュラソー島",
    "NETHERLANDS_ANTILLES": "オランダ領アンティル",
    "NETHERLANDS_EUROPE_AMSTERDAM": "ヨーロッパ/アムステルダム",
    "NETHERLANDS": "オランダ",
    "NETMEETING_DESC": "Microsoft NetMeetingはインターネットを通信媒体として使用して、電話会議を行えるようにします",
    "NETMEETING_ILS_DESC": " NetMeeting ILSはNetmeetingとInternet Locator Servers(ILS)間で使用されるプロトコルです。NetmeetingはMicrosoft Windowsの多くのバージョンに含まれるVoIPおよびマルチポイント ビデオ会議クライアントです",
    "NETMEETING_ILS": "NetMeeting ILS",
    "NETMEETING": "NetMeeting",
    "NETWORK_INTERFACE_ID": "ネットワーク インターフェイスID",
    "NETWORK_PROTOCOL_ADFS": "Any distributed FS",
    "NETWORK_PROTOCOL_AH": "IP6 Auth Header",
    "NETWORK_PROTOCOL_AHIP": "Any Host Internal Protocol",
    "NETWORK_PROTOCOL_APES": "Any Private Encr. Scheme",
    "NETWORK_PROTOCOL_ARGUS": "Argus",
    "NETWORK_PROTOCOL_AX25": "AX.25 Frames",
    "NETWORK_PROTOCOL_BHA": "BHA",
    "NETWORK_PROTOCOL_BLT": "Bulk Data Transfer",
    "NETWORK_PROTOCOL_BRSATMON": "BackRoom SATNET Monitoring",
    "NETWORK_PROTOCOL_CARP": "CARP",
    "NETWORK_PROTOCOL_CFTP": "CFTP",
    "NETWORK_PROTOCOL_CHAOS": "CHAOS",
    "NETWORK_PROTOCOL_CMTP": "Control Message Transport",
    "NETWORK_PROTOCOL_CPHB": "Comp. Prot. HeartBeat",
    "NETWORK_PROTOCOL_CPNX": "Comp. Prot. Net. Executive",
    "NETWORK_PROTOCOL_DDP": "Datagram Delivery",
    "NETWORK_PROTOCOL_DGP": "Dissimilar Gateway Prot.",
    "NETWORK_PROTOCOL_DSTOPTS": "IP6 destination option",
    "NETWORK_PROTOCOL_EGP": "Exterior Gateway Protocol",
    "NETWORK_PROTOCOL_EMCON": "EMCON",
    "NETWORK_PROTOCOL_ENCAP": "Encapsulation Header",
    "NETWORK_PROTOCOL_EON": "ISO cnlp",
    "NETWORK_PROTOCOL_ESP": "IP6 Encap Sec. Payload",
    "NETWORK_PROTOCOL_ETHERIP": "Ethernet IP encapsulation",
    "NETWORK_PROTOCOL_FRAGMENT": "IP6 fragmentation header",
    "NETWORK_PROTOCOL_GGP": "Gateway^2 (deprecated)",
    "NETWORK_PROTOCOL_GMTP": "GMTP",
    "NETWORK_PROTOCOL_GRE": "General Routing Encap.",
    "NETWORK_PROTOCOL_HELLO": "Hello Routing Protocol",
    "NETWORK_PROTOCOL_HMP": "Host Monitoring",
    "NETWORK_PROTOCOL_ICMP": "Control Message Protocol",
    "NETWORK_PROTOCOL_ICMPV6": "ICMP6",
    "NETWORK_PROTOCOL_IDP": "Xns Idp",
    "NETWORK_PROTOCOL_IDPR": "InterDomain Policy Routing",
    "NETWORK_PROTOCOL_IDRP": "InterDomain",
    "NETWORK_PROTOCOL_IGMP": "Group Mgmt Protocol",
    "NETWORK_PROTOCOL_IGP": "NSFNET-IGP",
    "NETWORK_PROTOCOL_IGRP": "Cisco/GXS IGRP",
    "NETWORK_PROTOCOL_IL": "IL transport protocol",
    "NETWORK_PROTOCOL_INLSP": "Integ. Net Layer Security",
    "NETWORK_PROTOCOL_INP": "Merit Internodal",
    "NETWORK_PROTOCOL_IP": "Dummy for IP",
    "NETWORK_PROTOCOL_IPCOMP": "Payload Compression (IPComp)",
    "NETWORK_PROTOCOL_IPCV": "Packet Core Utility",
    "NETWORK_PROTOCOL_IPEIP": "IP encapsulated in IP",
    "NETWORK_PROTOCOL_IPPC": "Pluribus Packet Core",
    "NETWORK_PROTOCOL_IPV4": "IPv4 encapsulation",
    "NETWORK_PROTOCOL_IPV6": "IP6 header",
    "NETWORK_PROTOCOL_IRTP": "Reliable Transaction",
    "NETWORK_PROTOCOL_KRYPTOLAN": "Kryptolan",
    "NETWORK_PROTOCOL_LARP": "Locus Address Resoloution",
    "NETWORK_PROTOCOL_LEAF1": "Leaf-1",
    "NETWORK_PROTOCOL_LEAF2": "Leaf-2",
    "NETWORK_PROTOCOL_MEAS": "DCN Measurement Subsystems",
    "NETWORK_PROTOCOL_MHRP": "Mobile Host Routing",
    "NETWORK_PROTOCOL_MICP": "Mobile Int.ing control",
    "NETWORK_PROTOCOL_MOBILE": "IP Mobility",
    "NETWORK_PROTOCOL_MTP": "Multicast Transport",
    "NETWORK_PROTOCOL_MUX": "Multiplexing",
    "NETWORK_PROTOCOL_ND": "Sun net disk proto (temp.)",
    "NETWORK_PROTOCOL_NHRP": "Next Hop Resolution",
    "NETWORK_PROTOCOL_NO_NEXT_HDR": "IP6 no next header",
    "NETWORK_PROTOCOL_NSP": "Network Services",
    "NETWORK_PROTOCOL_NVPII": "Network Voice",
    "NETWORK_PROTOCOL_OLD_DIVERT": "OLD Divert Pseudo-Proto",
    "NETWORK_PROTOCOL_OSPFIGP": "OSPFIGP",
    "NETWORK_PROTOCOL_PFSYNC": "PFSYNC",
    "NETWORK_PROTOCOL_PGM": "PGM",
    "NETWORK_PROTOCOL_PIGP": "Private Interior Gateway",
    "NETWORK_PROTOCOL_PIM": "Protocol Independent Mcast",
    "NETWORK_PROTOCOL_PRM": "Packet Radio Measurement",
    "NETWORK_PROTOCOL_PUP": "PUP",
    "NETWORK_PROTOCOL_PVP": "Packet Video Protocol",
    "NETWORK_PROTOCOL_RAW": "Raw IP packet",
    "NETWORK_PROTOCOL_RCCMON": "BBN RCC Monitoring",
    "NETWORK_PROTOCOL_RDP": "Reliable Data",
    "NETWORK_PROTOCOL_ROUTING": "IP6 Routing Header",
    "NETWORK_PROTOCOL_RSVP": "Resource Reservation",
    "NETWORK_PROTOCOL_RVD": "Remote Virtual Disk",
    "NETWORK_PROTOCOL_SATEXPAK": "SATNET/Backroom EXPAK",
    "NETWORK_PROTOCOL_SATMON": "Satnet Monitoring",
    "NETWORK_PROTOCOL_SCCSP": "Semaphore Comm. security",
    "NETWORK_PROTOCOL_SCTP": "SCTP",
    "NETWORK_PROTOCOL_SDRP": "Source Demand Routing",
    "NETWORK_PROTOCOL_SEP": "Sequential Exchange",
    "NETWORK_PROTOCOL_SKIP": "SKIP",
    "NETWORK_PROTOCOL_SRPC": "Strite RPC protocol",
    "NETWORK_PROTOCOL_ST": "Stream protocol II",
    "NETWORK_PROTOCOL_SVMTP": "Secure VMTP",
    "NETWORK_PROTOCOL_SWIPE": "IP with encryption",
    "NETWORK_PROTOCOL_TCF": "TCF",
    "NETWORK_PROTOCOL_TCP": "TCP",
    "NETWORK_PROTOCOL_TLSP": "Transport Layer Security",
    "NETWORK_PROTOCOL_TP": "tp-4 w/ class negotiation",
    "NETWORK_PROTOCOL_TPC": "Third Party Connect",
    "NETWORK_PROTOCOL_TPXX": "TP++ Transport",
    "NETWORK_PROTOCOL_TRUNK1": "Trunk-1",
    "NETWORK_PROTOCOL_TRUNK2": "Trunk-2",
    "NETWORK_PROTOCOL_TTP": "TTP",
    "NETWORK_PROTOCOL_UDP": "UDP - User Datagram Protocol",
    "NETWORK_PROTOCOL_VINES": "Banyon VINES",
    "NETWORK_PROTOCOL_VISA": "VISA Protocol",
    "NETWORK_PROTOCOL_VMTP": "VMTP",
    "NETWORK_PROTOCOL_WBEXPAK": "WIDEBAND EXPAK",
    "NETWORK_PROTOCOL_WBMON": "WIDEBAND Monitoring",
    "NETWORK_PROTOCOL_WSN": "Wang Span Network",
    "NETWORK_PROTOCOL_XNET": "Cross Net Debugger",
    "NETWORK_PROTOCOL_XTP": "XTP",
    "NETWORK_SERVICE_GROUP": "ネットワーク サービス グループ",
    "NETWORK_SERVICE_GROUPS": "ネットワーク サービス グループ",
    "NETWORK_SERVICE": "ネットワーク サービス",
    "NETWORK_SERVICES_GROUP": "ネットワーク サービス グループ",
    "NETWORK_SERVICES": "ネットワーク サービス",
    "NETWORK_TRAFFIC": "ネットワーク トラフィック",
    "NEW_API_KEY": "新しいAPIキー",
    "NEW_CALEDONIA_PACIFIC_NOUMEA": "太平洋/ヌメア",
    "NEW_CALEDONIA": "ニューカレドニア",
    "NEW_PASSWORD_EQUALITY": "新しいパスワードには現在のパスワードとは別のパスワードを設定してください",
    "NEW_PASSWORD_PLACEHOLDER": "8文字以上の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります",
    "NEW_PASSWORD": "新しいパスワード",
    "NEW_ZEALAND_PACIFIC_AUCKLAND": "太平洋/オークランド",
    "NEW_ZEALAND_PACIFIC_CHATHAM": "太平洋/チャタム",
    "NEW_ZEALAND": "ニュージーランド",
    "NEW": "新規",
    "NEWLY_REG_DOMAINS": "新規登録ドメイン",
    "NEWS_AND_MEDIA": "ニュースとメディア",
    "NEXT_PERIODIC_UPDATE": "次回の定期更新は、次のVMに設定されたスケジュールに従って行われます：",
    "NEXT_UPDATE": "次回の定期的なソフトウェア アップグレード： ",
    "NEXT": "次へ",
    "NFL_DESC": " このプロトコル プラグインは、ホストnfl.comへのhttpトラフィックを分類します",
    "NFL": "NFL",
    "NFLMOBILE": "NFLモバイル",
    "NFS_DESC": " NFSプロトコルはRFC 1813で説明されているように、ネットワーク全体で共有ファイル システムへの透過的なリモート アクセスを提供します",
    "NFS": "NFS",
    "NGAP_SCTP_DESC": "NGアプリケーション プロトコル(NGAP)は、NG-RANノードとアクセスおよびモビリティー管理機能(AMF)の間のコントロール プレーン シグナリングを提供し、ユーザー機器(UE)とAMFのためのNASシグナリングを提供します",
    "NGAP_SCTP": "NGAP-SCTP",
    "NGAP_UDP_DESC": "NGアプリケーション プロトコル(NGAP)はUDPとしてカプセル化され、SCTPをサポートしないネットワーク上でのトランスポートをサポートします",
    "NGAP_UDP": "NGAP-UDP",
    "NICARAGUA_AMERICA_MANAGUA": "アメリカ/マナグア",
    "NICARAGUA": "ニカラグア共和国",
    "NIGER_AFRICA_NIAMEY": "アフリカ/ニアメ",
    "NIGER": "ニジェール共和国",
    "NIGERIA_AFRICA_LAGOS": "アフリカ/ラゴス",
    "NIGERIA": "ナイジェリア連邦共和国",
    "NIUE_PACIFIC_NIUE": "太平洋/ニウエ",
    "NIUE": "ニウエ",
    "NLOCKMGR_DESC": " Network Lock ManagerはNetwork File System (NFS)と連携して稼働し、System Vスタイルのアドバイザリー ファイルとレコード ロックをネットワーク経由で提供する機能です",
    "NLOCKMGR": "Network Lock Manager",
    "NLSP_DESC": " NetWare Link Services Protocol (NLSP)は、Internetwork Packet Exchangeネットワークのリンク ステート ルーティングを提供します",
    "NLSP": "NLSP",
    "NMAP_DESC": "Nmap (Network Mapper)はコンピューター ネットワーク上のホストやサービスを検出し、ネットワークの「マップ」を作成するために使用されるセキュリティ スキャナーです",
    "NMAP": "Nmap",
    "NNTP_DESC": " Network News Transport Protocol (NNTP)は信頼性の高いストリームベースのメカニズムを用いて、ネット ニュース記事の配信、照会、検索、投稿に使用されます",
    "NNTP": "NNTP",
    "NNTPS_DESC": " NNTPプロトコルのセキュアなバージョン",
    "NNTPS": "SecureNNTP",
    "NO_ACTIVATION_PENDING": "保留中のアクティベーションはありません",
    "NO_BC_GROUPS_AVAILABLE_FOR_SELECTED_LOCATION": "Branch Connectorグループは選択したロケーションで使用できません。",
    "NO_DATA_AVAILABLE_AWS_ACCOUNT_GROUP": "利用可能なデータがありません\n\nAWSアカウント グループを作成するには\n\n[管理者] > [パートナー統合]に移動します\n\n\n",
    "NO_DATA": "一致する項目が見つかりません",
    "NO_DESCRIPTION": "説明はありません",
    "NO_GROUPING": "トラフィック全体",
    "NO_ITEMS_AVAILABLE": "データが見つかりません",
    "NO_MATCHING_ITEMS_FOUND": "一致する項目が見つかりません",
    "NO_MORE_DHCP_OPTIONS_AVAILABLE": "DHCPオプションはこれ以上利用できません",
    "NO_OF_CLOUD_CONNECTOR_GROUPS": "Cloud Connectorグループの数",
    "NO_OF_CLOUD_CONNECTORS": "Cloud Connectorsの数",
    "NO_OF_DUPLICATES_IP": "重複するIPの数",
    "NO_OF_EDGE_CONNECTOR_GROUPS": "Cloud Connectorグループの数",
    "NO_OF_PRIVATE_IP_ADDRESSES": "プライベートIPアドレスの数",
    "NO_PENDING_UPGRADES": "保留中のアップグレードはありません",
    "NO_PRESIGNED_URL_WAS_GENERATED": "署名付きURLは生成されませんでした。",
    "NO_REGION_WAS_PREVIOUS_SELECTED_TEXT": "ストレージ アカウントで使用されるEvent Gridにリージョンが選択されていません。",
    "NO_REGION_WAS_PREVIOUS_SELECTED": "リージョンが選択されていません",
    "NO_STATIC_LEASE_CONFIGURED": "静的リースが構成されていません",
    "NO_SUBSCRIPTION_AVAILABLE": "利用可能なサブスクリプションはありません。",
    "NO_VALUE_SELECTED": "値が選択されていません",
    "NO_WIDGET_DATA": "選択した時間範囲のデータはありません",
    "NO": "いいえ",
    "NON_CATEGORIZABLE": "カテゴリー分類不可",
    "NON_NUMERIC_VALUE": "このフィールドには数値のみを使用してください。",
    "NONE": "なし",
    "NORFOLK_ISLAND_PACIFIC_NORFOLK": "太平洋/ノーフォーク島",
    "NORFOLK_ISLAND": "ノーフォーク島",
    "NORTH_KOREA": "北朝鮮",
    "NORTH_MACEDONIA": "北マケドニア共和国",
    "NORTHAMERICA_NORTHEAST1_A": "northamerica-northeast1a",
    "NORTHAMERICA_NORTHEAST1_B": "northamerica-northeast1b",
    "NORTHAMERICA_NORTHEAST1_C": "northamerica-northeast1c",
    "NORTHAMERICA_NORTHEAST1": "northamerica-northeast1",
    "NORTHAMERICA_NORTHEAST2_A": "northamerica-northeast2a",
    "NORTHAMERICA_NORTHEAST2_B": "northamerica-northeast2b",
    "NORTHAMERICA_NORTHEAST2_C": "northamerica-northeast2c",
    "NORTHAMERICA_NORTHEAST2": "northamerica-northeast2",
    "NORTHCENTRALUS": "(米国)米国中北部",
    "NORTHCENTRALUSSTAGE": "(米国)米国中北部(ステージ)",
    "NORTHERN_EUROPE": "北欧",
    "NORTHERN_MARIANA_ISLANDS_PACIFIC_SAIPAN": "太平洋/サイパン島",
    "NORTHERN_MARIANA_ISLANDS": "北マリアナ諸島",
    "NORTHEUROPE": "(ヨーロッパ)北ヨーロッパ",
    "NORWAY_EUROPE_OSLO": "ヨーロッパ/オスロ",
    "NORWAY": "ノルウェー王国",
    "NORWAYEAST": "(ヨーロッパ)ノルウェー東部",
    "NORWAYWEST": "(ヨーロッパ)ノルウェー西部",
    "NOT_AVAILABLE": "利用できません",
    "NOT_DEPLOYED": "展開準備完了",
    "NOT_NULL": "次の値がNullでない",
    "NOT_SPECIFIED": "指定されていない",
    "NSS_CLOUD_FEED_API_URL": "API URL",
    "NSS_CLOUD_FEED_AUTHENTICATION_URL": "承認URL",
    "NSS_CLOUD_FEED_AWS_ACCESS_ID": "AWSアクセスID",
    "NSS_CLOUD_FEED_AWS_SECRET_KEY": "AWSシークレット キー",
    "NSS_CLOUD_FEED_CLIENT_ID": "クライアントID",
    "NSS_CLOUD_FEED_CLIENT_SECRET": "クライアント シークレット",
    "NSS_CLOUD_FEED_GENERAL": "一般",
    "NSS_CLOUD_FEED_GRANT_TYPE": "付与タイプ",
    "NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "JSON配列表記",
    "NSS_CLOUD_FEED_MAX_BATCH_SIZE": "最大バッチ サイズ",
    "NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "OAuth 2.0認証",
    "NSS_CLOUD_FEED_SCOPE": "範囲",
    "NSS_CLOUD_FEED_SIEM_TYPE": "SIEMタイプ",
    "NSS_CLOUD_FEEDS_API_URL": "API URL",
    "NSS_CLOUD_FEEDS_FEED_NAME": "フィード名",
    "NSS_CLOUD_FEEDS_FEED_OVERVIEW": "フィードの概要",
    "NSS_CLOUD_FEEDS_FEED_TYPE": "フィード タイプ",
    "NSS_CLOUD_FEEDS_LOG_TYPE": "ログ タイプ",
    "NSS_CLOUD_FEEDS_S3_FOLDER_URL": "S3フォルダーのURL",
    "NSS_CLOUD_FEEDS_SIEM_TYPE": "SIEMタイプ",
    "NSS_CLOUD_FEEDS_STATUS": "ステータス",
    "NSS_CLOUD_FEEDS": "クラウドNSSフィード",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connectorグループ",
    "NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "クライアントIPアドレス",
    "NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "DNSリクエスト タイプ",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "DNSレスポンス コード",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "DNSレスポンス タイプ",
    "NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "DNSレスポンス",
    "NSS_FEED_DNS_FILTERS_DOMAINS": "ドメイン",
    "NSS_FEED_DNS_FILTERS_DURATIONS": "期間",
    "NSS_FEED_DNS_FILTERS_LOCATIONS": "ロケーション",
    "NSS_FEED_DNS_FILTERS_POLICY_ACTION": "ポリシー アクション",
    "NSS_FEED_DNS_FILTERS_RULE_NAME": "ルール名",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_ADDRESS": "サーバーIPアドレス",
    "NSS_FEED_DNS_FILTERS_SERVER_IP_PORTS": "サーバー ポート",
    "NSS_FEED_DUPLICATE_LOGS": "重複ログ",
    "NSS_FEED_EC_METRICS_RECORD_TYPE": "メトリクス レコード タイプ",
    "NSS_FEED_ESCAPE_CHARACTER": "フィード エスケープ文字",
    "NSS_FEED_FILTERS": "フィルター",
    "NSS_FEED_FORMATTING": "フォーマット",
    "NSS_FEED_GENERAL": "一般",
    "NSS_FEED_LOG_TYPE": "ログ タイプ",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "Cloud/Branch Connector VM",
    "NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "Cloud/Branch Connector VM",
    "NSS_FEED_NAME": "フィード名",
    "NSS_FEED_OUTPUT_FORMAT": "フィード出力形式",
    "NSS_FEED_OUTPUT_TYPE": "フィード出力タイプ",
    "NSS_FEED_SERVER": "NSSサーバー",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "Cloud/Branch Connectorグループ",
    "NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "Cloud/Branch Connector",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "クライアントIPアドレス",
    "NSS_FEED_SESSION_FILTERS_FIREWALL_LOG_TYPE": "ファイアウォール ログ タイプ",
    "NSS_FEED_SESSION_FILTERS_GATEWAY": "ゲートウェイ",
    "NSS_FEED_SESSION_FILTERS_LOCATIONS": "ロケーション",
    "NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "ネットワーク サービス",
    "NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "ポリシー アクション",
    "NSS_FEED_SESSION_FILTERS_RULE_NAME": "ルール名",
    "NSS_FEED_SESSION_LOG_TYPE": "セッション ログ タイプ",
    "NSS_FEED_SIEM_CONNECTIVITY": "SIEM接続",
    "NSS_FEED_SIEM_DESTINATION_TYPE": "SIEM宛先タイプ",
    "NSS_FEED_SIEM_FQDN": "SIEM FQDN",
    "NSS_FEED_SIEM_IP_ADDRESS": "SIEM IPアドレス",
    "NSS_FEED_SIEM_RATE_LIMIT": "SIEMレート制限(1秒あたりのイベント数)",
    "NSS_FEED_SIEM_RATE": "SIEMレート",
    "NSS_FEED_SIEM_TCP_PORT": "SIEM TCPポート",
    "NSS_FEED_STATUS": "ステータス",
    "NSS_FEED_TIMEZONE": "タイムゾーン",
    "NSS_FEED": "NSSフィード",
    "NSS_FEEDS_AGGREGATE_LOGS": "ログを集計",
    "NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS": "セッション ログと集約ログの両方",
    "NSS_FEEDS_DUPLICATE_LOG": "重複ログ",
    "NSS_FEEDS_FEED_ATTRIBUTES": "フィード属性",
    "NSS_FEEDS_FEED_NAME": "フィード名",
    "NSS_FEEDS_FEED_OUTPUT_FORMAT": "フィード出力形式",
    "NSS_FEEDS_FEED_OVERVIEW": "フィードの概要",
    "NSS_FEEDS_FEED_TYPE": "フィード タイプ",
    "NSS_FEEDS_FULL_SESSION_LOGS": "完全なセッション ログ",
    "NSS_FEEDS_LOG_FILTER": "ログ フィルター",
    "NSS_FEEDS_LOG_TYPE": "ログ タイプ",
    "NSS_FEEDS_NSS_SERVER_TEXT": "NSSサーバー",
    "NSS_FEEDS_OUTPUT_DESTINATION": "出力先",
    "NSS_FEEDS_SIEM_RATE": "SIEMレート",
    "NSS_FEEDS_STATUS": "ステータス",
    "NSS_FEEDS_TIMEZONE": "タイムゾーン",
    "NSS_FEEDS_USER_OBFUSCATION": "ユーザーの難読化",
    "NSS_FEEDS": "NSSフィード",
    "NSS_FOR_FIREWALL_AND_EC": "ファイアウォール、Cloud Connector、Branch ConnectorのNSS",
    "NSS_FOR_FIREWALL": "ファイアウォール用のNSS",
    "NSS_GCP_DEPLOYMENT_GUIDE": "NSS向けGoogle Cloud Platformデプロイメント ガイド",
    "NSS_LOGGING": "NSSログの記録",
    "NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "ユーザー数",
    "NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "ピーク時の1時間あたりのDNSリクエスト",
    "NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR": "ピーク時の1時間あたりのセッション",
    "NSS_SERVER_DEPLOYMENT_PLATFORM": "プラットフォーム",
    "NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE": "NSS仮想アプライアンスのダウンロード",
    "NSS_SERVER": "NSSサーバー",
    "NSS_SERVERS": "NSSサーバー",
    "NSS_TYPE": "NSSタイプ",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC": "NSS仮想アプライアンスの展開",
    "NSS_VIRTUAL_APPLIANCE_DEPLOYMENT": "NSS仮想アプライアンスの展開",
    "NSS_VIRTUAL_MACHINE": "NSS仮想マシン",
    "NTP_DESC": "Network Time Protocolは、パケット交換の可変遅延データ ネットワークを介してコンピューター システム間でクロックを同期するためのネットワーク プロトコルです",
    "NTP": "NTP",
    "NTV_DESC": " このプロトコル プラグインは、ホストntv.co.jpへのhttpトラフィックを分類します",
    "NTV": "NTV",
    "NUDITY_DESC": " あらゆる形態の芸術的または非芸術的なヌード画像(彫刻、写真、絵画、その他)を提供するサイト",
    "NUDITY": "ヌード",
    "NUMBER_ABBR": "No.",
    "NUMBER_OF_ACCOUNTS": "アカウントの数",
    "NUMBER_OF_CONNECTORS": "コネクターの数",
    "NUMBER_OF_CORES": "コアの数",
    "NUMBER_OF_RECORDS_DISPLAYED": "表示するレコードの数",
    "NUMBER_OF_RECORDS_FETCHED_SO_FAR": "これまでにフェッチされたレコードの数",
    "NUMBER_OF_SELECTED_ITEM_PLURAL": "{{count}}個の項目が選択されました",
    "NUMBER_OF_SELECTED_ITEM": "{{count}}個の項目が選択されました",
    "NUMBER_OF_SELECTED_ITEMS": "選択された項目の数",
    "OBFUSCATED": "難読化",
    "OCCUPIED_PALESTINIAN_TERRITORY_ASIA_GAZA": "アジア/ガザ",
    "OCCUPIED_PALESTINIAN_TERRITORY": "イギリス委任統治領パレスチナ",
    "OFF": "オフ",
    "OKAY": "普通",
    "OMAN_ASIA_MUSCAT": "アジア/マスカット",
    "OMAN": "オマーン国",
    "ON_PREMISE": "オンプレミス",
    "ON": "オン",
    "ONE_OR_MORE_CC_FAILED": "1つまたは複数のCloud Connectorのアップグレードに失敗しました",
    "ONLINE_AUCTIONS": "オンライン オークション",
    "ONLINE_CHAT": "オンライン チャット",
    "ONLY_ONE_PHISICAL_INTERFACE_PER_PORT": "タグ付きインターフェイスはポートごとに1つのみです。",
    "OPEN_A_NEW_TAB": "新しいタブで開く",
    "OPENVPN_DESC": "OpenVPNは、ルーティングまたはブリッジ構成およびリモート アクセス機能で安全なポイントツーポイントまたはサイトツーサイト接続を作成するための仮想プライベート ネットワーク(VPN)技術を実装するオープン ソース ソフトウェア アプリケーションです",
    "OPENVPN": "OpenVPN",
    "OPERATIONAL_STATUS": "運用ステータス",
    "OPTION_NAME": "オプション名",
    "OPTIONAL_PARENTHESIS": "(省略可)",
    "OPTIONAL": "省略可",
    "OPTIONS_COLON": "オプション：",
    "OPTIONS": "オプション",
    "ORACLE_LINUX": "ORACLE LINUX",
    "ORDER_DEFAULT": "デフォルト",
    "ORG_ADMIN": "組織管理者",
    "ORG_ID": "組織ID",
    "ORGANIZATION": "組織",
    "OS_TYPE": "OSの種類",
    "OS_VERSION": "OSバージョン",
    "OSS_UPDATES": "オペレーティング システムとソフトウェアの更新",
    "OTHER_ADULT_MATERIAL": "その他のアダルト コンテンツ",
    "OTHER_BUSINESS_AND_ECONOMY": "その他のビジネスと経済",
    "OTHER_CLOUDS": "その他のクラウド",
    "OTHER_DRUGS": "その他の医薬品",
    "OTHER_EDUCATION": "その他の教育",
    "OTHER_ENTERTAINMENT_AND_RECREATION": "その他のエンターテイメント/レクリエーション",
    "OTHER_GAMES": "オンラインゲームとその他のゲーム",
    "OTHER_GOVERNMENT_AND_POLITICS": "",
    "OTHER_ILLEGAL_OR_QUESTIONABLE": "その他の法律違反/不正",
    "OTHER_INFORMATION_TECHNOLOGY": "",
    "OTHER_INTERNET_COMMUNICATION": "その他のインターネット コミュニケーション",
    "OTHER_MISCELLANEOUS": "その他のカテゴリー",
    "OTHER_OS": "その他のOS",
    "OTHER_RELIGION": "その他の宗教",
    "OTHER_SECURITY": "その他のセキュリティ",
    "OTHER_SHOPPING_AND_AUCTIONS": "その他のショッピングとオークション",
    "OTHER_SOCIAL_AND_FAMILY_ISSUES": "その他の社会および家族の問題",
    "OTHER_SOCIETY_AND_LIFESTYLE": "その他の社会とライフスタイル",
    "OTHER_THREAT": "その他の脅威",
    "OTHER": "その他",
    "out of": "／",
    "OUT_OF": "／",
    "OUTBOUND": "アウトバウンド",
    "OUTBYTES": "送信バイト数",
    "OUTGOING_GATEWAY_IP_ADDRESS": "送信ゲートウェイIPアドレス",
    "OVER_ALL_TRAFFIC": "トラフィック全体",
    "OVERRIDE": "オーバーライド",
    "P2P_COMMUNICATION": "P2Pサイト",
    "P2P": "P2P",
    "PACKET_LOSS": "パケット損失",
    "PAGE_OF": "ページ{1}/{2}",
    "PAGE_RISK_INDEX": "不審なコンテンツ",
    "PAGE": "ページ",
    "PAKISTAN_ASIA_KARACHI": "アジア/カラチ",
    "PAKISTAN": "パキスタン・イスラム共和国",
    "PALAU_PACIFIC_PALAU": "太平洋/パラオ共和国",
    "PALAU": "パラオ共和国",
    "PALESTINE": "パレスチナ",
    "PALESTINIAN_TERRITORY": "パレスチナ自治区",
    "PANAMA_AMERICA_PANAMA": "アメリカ/パナマ",
    "PANAMA": "パナマ共和国",
    "PAPUA_NEW_GUINEA_PACIFIC_PORT_MORESBY": "太平洋/ポートモレスビー",
    "PAPUA_NEW_GUINEA": "パプアニューギニア独立国",
    "PARAGUAY_AMERICA_ASUNCION": "アメリカ/アスンシオン",
    "PARAGUAY": "パラグアイ共和国",
    "PARTNER_INTEGRATIONS": "パートナー統合",
    "PASSPHRASE": "パスフレーズ",
    "PASSWORD_CHANGE_REMINDER_NEW_PASSWORD_INPUT_PLACEHOLDER": "8文字以上の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります",
    "PASSWORD_DONT_MATCH": "パスワードが一致しません",
    "PASSWORD_EXPIRATION_CHANGE_WARNING_MESSAGE": "この変更は別のサービスの管理ポータルに存在する管理者にも適用されます。続行しますか？",
    "PASSWORD_EXPIRATION_DAYS_RANGE": "設定できるパスワードの有効期限は15日～365日です",
    "PASSWORD_EXPIRATION": "パスワードの有効期限",
    "PASSWORD_EXPIRED_ALREADY": "パスワードの有効期限が切れています",
    "PASSWORD_EXPIRED": "パスワードの有効期限切れ",
    "PASSWORD_EXPIRES_AFTER": "パスワードの有効期限",
    "PASSWORD_EXPIRY": "パスワードの有効期限",
    "PASSWORD_MANAGEMENT": "パスワード管理",
    "PASSWORD_MESSAGE_WARNING_MESSAGE": "この変更は別のサービスの管理ポータルに存在する管理者にも適用されます。続行しますか？",
    "PASSWORD_STRENGTH_REQUIRED": "パスワードは8文字以上で、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。",
    "PASSWORD_STRENGTH": "8文字以上の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります",
    "PASSWORD_UPDATE_SUCCESSFULLY": "パスワードが正常に変更されました",
    "PASSWORD": "パスワード",
    "PATCH": "パッチ",
    "PATENTS": "特許",
    "PC_ANYWHERE_DESC": "PC-Anywhereはリモート コントロールおよびファイル転送ソフトウェアです",
    "PC_ANYWHERE": "pcAnywhere",
    "PCANYWHERE_DESC": " リモート コントロール ソリューションのPCAnywhereは、WindowsとLinuxの両方のシステムを管理できます。強化されたビデオ パフォーマンスと組み込みのAES 256ビット暗号化により、高速で安全な通信が可能になります。PCAnywhereは、強力なファイル転送機能も搭載しています",
    "PCANYWHERE": "pcanywhere",
    "PEER_DHCP": "ピアDHCP (省略可)",
    "PENDING": "保留中",
    "PERMISSION_REQUIRED_MESSAGE": "この機能を使用するには、現在のものとは別の権限が必要です。",
    "PERMISSION_REQUIRED": "権限が必要です",
    "PERMISSION": "権限",
    "PERMISSIONS": "権限",
    "PERSIST_LOCAL_VERSION_PROFILE": "ローカル バージョン プロファイルを保持",
    "PERU_AMERICA_LIMA": "アメリカ/リマ",
    "PERU": "ペルー共和国",
    "PFCP_DESC": "コントロール プレーンとユーザー プレーン機能(UPF)の間のN4インターフェイスで使用される3GPPプロトコル",
    "PFCP_PORT": "PFCPポート",
    "PHILIPPINES_ASIA_MANILA": "アジア/マニラ",
    "PHILIPPINES": "フィリピン共和国",
    "PHISHING": "フィッシング",
    "PHYSICAL": "物理",
    "PITCAIRN_ISLANDS": "ピトケアン諸島",
    "PITCAIRN_PACIFIC_PITCAIRN": "太平洋/ピトケアン諸島",
    "PITCAIRN": "ピトケアン",
    "PLACEHOLDER_NETWORK_SERVICE_GROUP_NAME": "ここにネットワーク サービス グループ名を入力",
    "PLACEHOLDER_NETWORK_SERVICE_NAME": "ここにネットワーク サービス名を入力",
    "PLAIN_UDP": "未暗号化UDP",
    "PLATFORM": "プラットフォーム",
    "PLEASE_ADD_BC_GROUP_INFO": "Branch Connectorグループの情報を下に入力します。",
    "PLEASE_ADD_CLOUD_CONNECTOR_NAME": "Cloud Connectorフィルターを追加してください。",
    "PLEASE_ADD_DATACENTER_FILTER": "データセンター フィルターを追加してください。",
    "PLEASE_ADD_EC_DEVICE_APP_VERSION": "デバイス アプリ バージョンのフィルターを追加してください。",
    "PLEASE_ADD_EC_DEVICE_HOSTNAME": "デバイス ホスト名のフィルターを追加してください。",
    "PLEASE_ADD_EC_DEVICE_ID": "デバイス名のフィルターを追加してください。",
    "PLEASE_CONFIGURE_THE_IP_ADDRESS": "IPアドレスを構成してください",
    "PLEASE_CONFIGURE_THE_MAC_ADDRESS": "MACアドレスを構成してください",
    "PLEASE_CONFIGURE_THE_ROUTE_ADDRESS": "ルート アドレスを構成してください。",
    "PLEASE_ENTER_BELOW_VALUES": "以下の値を入力してください",
    "PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW": "以下の情報を入力してください。",
    "PLEASE_ENTER_VALID_EMAIL_ADDRESS": "有効なメール アドレスを入力してください。",
    "PLEASE_FILL_BRANCH_INFO": "個々のBranch Connectorの情報を下に入力します。",
    "PLEASE_FILL_DEVICE_INFO": "Branch Connector構成プロビジョニング用のデバイスを選択します。",
    "PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM": "トンネル暗号化を有効化するには、アカウント チームに連絡してください。",
    "PLEASE_REMOVE_DELETED_LOCATION": "削除されたロケーションを削除してください。",
    "PLEASE_SELECT_A_STORAGE_ACCOUNT": "ストレージ アカウントを選択してください。",
    "PLEASE_VERIFY_THE_DHCP_CUSTOM_CONFIGURTION_FOR_DUPLICATES": "DHCPカスタム オプションの構成に重複がないか確認してください。",
    "POLAND_EUROPE_WARSAW": "ヨーロッパ/ワルシャワ",
    "POLAND": "ポーランド共和国",
    "POLICY_CONFIGURATION": "ポリシー構成",
    "POLICY_INFORMATION": "転送情報",
    "POLICY_MANAGEMENT": "ポリシー管理",
    "POLICY_SYNC": "ポリシーの同期",
    "Policy": "ポリシー",
    "POLICY": "ポリシー",
    "POLITICS": "政治",
    "POOR": "悪い",
    "POP3_DESC": "Post Office Protocolはメールの取得に使用されるプロトコルです",
    "POP3": "POP3",
    "PORNOGRAPHY": "ポルノ",
    "PORT_DETAILS": "ポートの詳細",
    "PORT_NAME": "[ポート名]",
    "PORT_NO": "ポート番号  ",
    "PORT_STATUS": "ポート ステータス",
    "PORT": "ポート",
    "PORTALS": "ポータル",
    "PORTS": "プロキシ ポート",
    "PORTUGAL_ATLANTIC_AZORES": "大西洋/アゾレス諸島",
    "PORTUGAL_ATLANTIC_MADEIRA": "大西洋/マデイラ諸島",
    "PORTUGAL_EUROPE_LISBON": "ヨーロッパ/リスボン",
    "PORTUGAL": "ポルトガル共和国",
    "PPP_DESC": " PPP (Point-to-Point Protocol)は、ポイントツーポイント リンクに沿ってデータを転送するために使用されるリンク レイヤー プロトコルです。動的なIPアドレス、パスワード対応、エラー チェック、同じリンク上での複数のプロトコル伝送を提供します",
    "PPP": "PPP",
    "PPPOE_DESC": " PPP over Ethernet (PPPoE)は、シンプルなブリッジ アクセス端末を介してホストのネットワークをリモート アクセス コンセントレーターに接続する機能を提供します",
    "PPPOE": "PPPoE",
    "PPS_DESC": " このプロトコル プラグインは、ホストpps.tvへのhttpトラフィックを分類します",
    "PPS": "PPS (pps.tv)",
    "PPSTREAM_DESC": " PPStreamは音声および動画配信のプロトコルで、bittorrent (ピアツーピア)技術をベースにしています。主に中国で使用されています",
    "PPSTREAM": "PPStream",
    "PPTP_DATA_DESC": " ポイントツーポイント トンネリング プロトコルにより、ポイントツーポイント プロトコル(PPP)をIPネットワーク経由でトンネリングできます",
    "PPTP_DATA": "PPTPデータ チャネル。PPTP",
    "PPTP_DESC": " ポイントツーポイント トンネリング プロトコルにより、ポイントツーポイント プロトコル(PPP)をIPネットワーク経由でトンネリングできます",
    "PPTP_SERVICES": "PPTPサービス",
    "PPTP": "PPTP",
    "PPTV_DESC": " このプロトコル プラグインは、ホストpptv.comへのhttpトラフィックを分類します",
    "PPTV": "pptv",
    "PRE_SIGNED_URL": "署名済みURL",
    "PREDEFINED_RULE_CONFIRMATION": "定義済みルールの確認",
    "PREDEFINED": "定義済み",
    "PREFERRED_COLLON": "優先：",
    "PREFERRED_TEXT": "[はい]を選択すると、優先デバイスがアクティブになったときに、そのデバイスを優先して引き継ぐことができます。",
    "PREFERRED": "優先",
    "PREFIX": "プレフィックス",
    "PREV_DAY": "前日",
    "PREV_MONTH": "前月",
    "PREV_WEEK": "前の週",
    "PREVIOUS": "前へ",
    "PRIMARY_DNS_IS_MADATORY_BEFORE_SECONDARY_DNS": "プライマリーDNSはセカンダリーDNSの前に必須です。",
    "PRIMARY_DNS_SERVER_IP_ADDRESS": "プライマリーDNSサーバーのIPアドレス",
    "PRIMARY_DNS_SERVER": "プライマリーDNSサーバー",
    "PRIMARY_DNS": "プライマリーDNS",
    "PRIMARY_PROXY": "プライマリー プロキシ",
    "PRIMARY_SERVER_RESPONSE_PASS": "試行されたプライマリー サーバー",
    "PRIMARY": "プライマリー",
    "PRINT_VIEW": "印刷ビュー",
    "PRINT": "印刷",
    "PRIVATE_APLICATIONS": "プライベート アプリケーション",
    "PRIVATE_IP_ADDRESS": "プライベートIPアドレス",
    "PRIVATE_IP_ADDRESSES": "プライベートIPアドレス",
    "PROCEED": "続行",
    "PROCESSED_BYTES": "処理済みバイト数",
    "PROFANITY": "冒涜的な表現",
    "PROFESSIONAL_SERVICES": "プロフェッショナル サービス",
    "PROTOCOL_TYPE": "プロトコル タイプ",
    "PROTOCOL": "プロトコル",
    "PROVISION_KEY_NAME": "キー名のプロビジョニング",
    "PROVISION_KEY": "キーのプロビジョニング",
    "PROVISIONED": "プロビジョニング済み",
    "Provisioning URL": "プロビジョニングURL",
    "PROVISIONING_AND_CONFIGUATION": "プロビジョニングと構成",
    "PROVISIONING_CONTROL": "プロビジョニング制御",
    "PROVISIONING_KEY": "プロビジョニング キー",
    "PROVISIONING_MANAGEMENT": "プロビジョニング管理",
    "PROVISIONING_TEMPLATE_IS_BROKEN": "このプロビジョニング テンプレートは無効です。削除して新規作成してください。",
    "PROVISIONING_TEMPLATE": "プロビジョニング テンプレート",
    "PROVISIONING_TEMPLATES": "プロビジョニング テンプレート",
    "PROVISIONING_URL": "プロビジョニングURL",
    "PROXY_TEST": "プロキシ テスト",
    "PUBLIC_CLOUD_COFIGURATION": "パブリック クラウドの構成",
    "PUBLIC_CLOUD_CONFIG_MANAGEMENT": "パブリック クラウド構成管理",
    "PUBLIC_IP_FOR_DIRECT_FORWARDING": "ダイレクト転送用パブリックIP",
    "PUBLIC_IP": "パブリックIP",
    "PUBLIC_IPS": "パブリックIP",
    "PUERTO_RICO_AMERICA_PUERTO_RICO": "アメリカ/プエルトリコ自治連邦区",
    "PUERTO_RICO": "プエルトリコ自治連邦区",
    "PZEN": "Private Service Edge",
    "QATAR_ASIA_QATAR": "アジア/カタール",
    "QATAR": "カタール国",
    "QUESTIONABLE": "不正",
    "QUEUED_ACTIVATIONS": "キューに入れられたアクティベーション",
    "QUIC_DESC": "QUIC (Quick UDP Internet Connections)は、Googleが開発したインターネット用の新しい転送プロトコルです",
    "QUIC": "QUIC",
    "QUICK_LINKS": "Quick Links",
    "QUICKOFFICE": "Quickoffice",
    "QUICKSEC": "QUICKSEC",
    "QUICKTIME_VIDEO": "QuickTime Video (mov, qt)",
    "QUICKTIME": "QuickTime",
    "RADIO_STATIONS": "ラジオ",
    "RADIUS_DESC": " RADIUS (Remote Authentication Dial-In User Service)は、リモート アクセス サーバーが中央サーバーと通信してダイヤルイン ユーザーを認証し、リクエストされたシステムまたはサービスへのアクセスを承認できるようにするクライアント/サーバー プロトコルです",
    "RADIUS": "RADIUS",
    "RADIUSIM_DESC": " このプロトコル プラグインは、ホストradiusim.comへのhttpトラフィックを分類します",
    "RADIUSIM": "RadiusIM",
    "RANGE_ERROR": "無効な範囲です。数値は{{min}}～{{max}}の範囲にする必要があります",
    "RANGE_FROM_BIGGER_THAN_TO": "開始IPの範囲は終了IPと同じか、それより小さくする必要があります。",
    "RBA_LIMITED": "アクセスが制限されています",
    "REAL_ESTATE": "建設と不動産",
    "REAL_MEDIA_DESC": "Real Mediaは動画および音声配信の技術です",
    "REAL_MEDIA": "RealMedia",
    "REASON": "理由",
    "RECEIVE_COUNT": "受信回数",
    "RECEIVED_BYTES": "受信したバイト数",
    "RECEIVED_MESSAGES": "受信メッセージ",
    "RECOMMENDED_HYPERVISOR_SPECS": "推奨されるハイバーバイザーの仕様",
    "RECOMMENDED_VM_SPECS": "推奨されるVMの仕様",
    "REDHAT_LINUX": "RedHat Linux",
    "REDIR_ZPA": "ZPAにリダイレクト",
    "REDIRECT_REQUEST": "リダイレクト リクエスト",
    "REFERENCE_SITES": "リファレンス サイト",
    "REFRESH": "更新",
    "REGENARATE": "再生成",
    "REGENERATE_API_KEY_CONFIRMATION_MESSAGE": "APIキーを再生成すると、既存のキーはすぐに無効になります。新しいキーでは、既存のキーのスコープ、アクセス許可、名前が保持されます。この操作は元に戻すことはできません。",
    "REGENERATE_API_KEY_CONFIRMATION_TITLE": "APIキーの再生成",
    "REGENERATE_API_KEY_TOOLTIP": "APIキーを再生成します",
    "REGENERATE_API_KEY": "APIキーの再生成",
    "REGION_TEXT": "ZscalerでAWSアカウントのタグを検出するリージョンを選択してください。ドロップダウン メニューには、Zscalerのタグ検出サービスでサポートされているリージョンのリストが表示されます。サポートされるリージョンの詳細は、{1}こちら{2}を参照してください。",
    "REGION": "リージョン",
    "REGIONS_AND_SUBSCRIPTIONS": "リージョンとサブスクリプション",
    "REGIONS_SUBSCRIPTION_TEXT": "リージョンとサブスクリプションを選択します。Zscalerは、これらのリージョンとサブスクリプションにあるワークロードのユーザー定義タグを読み取ります。",
    "REGIONS_SUBSCRIPTION": "リージョンとサブスクリプション",
    "REGIONS_WORKLOAD_INVENTORY": "リージョン ワークロード インベントリー",
    "REGIONS": "リージョン",
    "REGISTERED": "登録済み",
    "RELEASES_NOTES": "リリース ノート",
    "REMOTE_ACCESS": "リモート アクセス ツール",
    "REMOTE_ASSISTANCE_MANAGEMENT": "リモート アシスタンス管理",
    "REMOTE_ASSISTANCE": "リモート アシスタンス",
    "REMOVE_ALL": "すべて削除",
    "RENEW": "更新",
    "REPORT": "レポート",
    "REQ_ACTION": "リクエスト アクション",
    "REQ_DURATION": "リクエスト期間",
    "REQ_RULE_NAME": "リクエスト ルール名",
    "REQUESTED_DOMAIN": "リクエストされたドメイン",
    "REQUIRED": "このフィールドを空にすることはできません。",
    "RES_ACTION": "レスポンス アクション",
    "RES_RULE_NAME": "レスポンス ルール名",
    "RESEARCH_BLOG": "ThreatLabz | セキュリティ リサーチ",
    "RESET_COUNT": "リセット回数",
    "RESET": "リセット",
    "RESOLVE_BY_ZPA": "ZPAによる解決",
    "RESOLVED_BY_ZPA": "解決済み",
    "RESOLVED_IP_OR_NAME": "解決済みのIPまたは名前",
    "RESOLVED_IP": "解決済みのIP",
    "RESOLVER_IP_OR_NAME": "解決済みのIPまたは名前",
    "RESOLVER": "リゾルバー",
    "RESOURCE_GROUP": "リソース グループ",
    "RESOURCE_GROUPS": "リソース グループ",
    "RESOURCE_NAME": "リソース",
    "RESOURCE_NOT_FOUND": "リソースが見つかりません",
    "RESOURCE_TYPE": "リソース タイプ",
    "RESOURCE": "リソース",
    "RESPONSE_ACTION": "レスポンス アクション",
    "REST": "REST",
    "RETRIEVING_FOR": "次を取得しています：",
    "RETURN_ERROR": "エラーを返す",
    "REUNION_INDIAN_REUNION": "インド洋/レユニオン",
    "REUNION": "レユニオン",
    "REVIEW_ENSURE_INFORMATION": "このBranch Connectorプロビジョニング テンプレートを作成する前に、以下のすべての情報が正しいことを確認してください。",
    "REVIEW_TENANT": "このアカウントを追加する前に、以下の情報がすべて正しいことを確認してください。",
    "REVIEW_TEXT": "このアカウントを追加する前に、以下の情報がすべて正しいことを確認してください。",
    "REVIEW_YOUR_CHANGES": "変更を確認",
    "REVIEW": "確認",
    "RMA": "RMAリクエスト済み",
    "ROLE_MANAGEMENT": "ロール管理",
    "ROLE_NAME": "ロール名",
    "ROLE": "ロール",
    "ROMANIA_EUROPE_BUCHAREST": "ヨーロッパ/ブカレスト",
    "ROMANIA": "ルーマニア",
    "ROUTE_HAS_DUPLICATE_VALUES": "ルートに重複した値があります。",
    "ROUTE": "ルート",
    "ROUTING": "ルーティング",
    "ROWS_PER_PAGE": "ページあたりの行数",
    "RSH_DESC": " RSHプロトコルを使用すると、リモート ホストへの安全な接続を確立し、コマンドをリモート マシンに送信して実行できるシェルを取得できます",
    "RSH": "rsh",
    "RSLTS_READFAILED": "結果ボリュームの読み取りに失敗しました。",
    "RSS_DESC": " RSSはWebフィード フォーマットの1種で、頻繁にアップデートされる情報を公開するための標準フォーマットです",
    "RSS": "rss",
    "RSTAT_DESC": " RStatプロトコルは、Sun NFSでネットワーク アクティビティーに関する統計情報を交換するために使用されます",
    "RSTAT": "RStat",
    "RSVP_DESC": " Resource Reservation Protocol (RSVP)は、統合サービスインターネット用に設計されたリソース予約を設定するプロトコルで、マルチキャストまたはユニキャスト データ フローのリソース予約を受信側が開始する設定を提供し、優れたスケーリングと堅牢性のプロパティーを備えています。RSVPプロトコルは、ホストが特定のアプリケーション データ ストリームまたはフローに対して、ネットワークから特定のサービス品質をリクエストするために使用されます。また、ルーターがフローのパスに沿ったすべてのノードにサービス品質(QoS)リクエストを配信し、リクエストされたサービスを提供するための状態を確立、維持するためにも使用されます",
    "RSVP": "RSVP",
    "RSYNC_DESC": "rsyncは、Unix系システム用のファイル同期およびファイル転送プログラムで、rsyncアルゴリズムと呼ばれるデルタ エンコードを使用してネットワーク データ転送を最小限に抑えます",
    "RSYNC": "Rsync",
    "RTCP_DESC": " リアルタイム トランスポート コントロール プロトコルRTPは、大規模なマルチキャスト ネットワークに拡張可能な方法でデータ配信を監視し、最小限の制御および識別機能を提供します",
    "RTCP": "RTCP",
    "RTL_DESC": " このプロトコル プラグインは、ホストrtl.deへのhttpトラフィックを分類します",
    "RTL": "RTL",
    "RTMP_DESC": "Real Time Messaging Protocol (RTMP)は、Adobe Systemsが開発した独自のプロトコルで、Flash Playerとサーバーの間で音声、動画、データをインターネット経由でストリーミングします",
    "RTMP": "RTMP",
    "RTP_DESC": " RTPは、マルチキャストまたはユニキャスト ネットワーク サービスを介して、音声、動画、シミュレーション データなどのリアルタイム データを送信するために使用されるリアルタイム トランスポート プロトコルです",
    "RTP": "RTP",
    "RTSP_DESC": " Real Time Streaming Protocol (RTSP)は、リアルタイム プロパティーを使用してデータの配信を制御するためのアプリケーション レベルのプロトコルです。RTSPは、音声や動画などのリアルタイム データの制御されたオンデマンド配信を可能にする拡張可能なフレームワークを提供します",
    "RTSP_SERVICES": "RTSPサービス",
    "RTSP": "RTSP",
    "RULE_CRITERIA": "条件",
    "RULE_NAME": "ルール名",
    "RULE_ORDER": "ルールの順序",
    "RULE_STATUS": "ルールのステータス",
    "RULES": "ルール",
    "RUN_TEST": "テストを実行",
    "RUSSIA": "ロシア連邦",
    "RUSSIAN_FEDERATION_ASIA_ANADYR": "アジア/アナディリ",
    "RUSSIAN_FEDERATION_ASIA_IRKUTSK": "アジア/イルクーツク",
    "RUSSIAN_FEDERATION_ASIA_KAMCHATKA": "アジア/カムチャツカ半島",
    "RUSSIAN_FEDERATION_ASIA_KRASNOYARSK": "アジア/クラスノヤルスク",
    "RUSSIAN_FEDERATION_ASIA_MAGADAN": "アジア/マガダン",
    "RUSSIAN_FEDERATION_ASIA_NOVOSIBIRSK": "アジア/ノヴォシビルスク",
    "RUSSIAN_FEDERATION_ASIA_OMSK": "アジア/オムスク",
    "RUSSIAN_FEDERATION_ASIA_SAKHALIN": "アジア/サハリン",
    "RUSSIAN_FEDERATION_ASIA_VLADIVOSTOK": "アジア/ウラジオストク",
    "RUSSIAN_FEDERATION_ASIA_YAKUTSK": "アジア/ヤクーツク",
    "RUSSIAN_FEDERATION_ASIA_YEKATERINBURG": "アジア/エカテリンブルク",
    "RUSSIAN_FEDERATION_EUROPE_KALININGRAD": "ヨーロッパ/カリーニングラード",
    "RUSSIAN_FEDERATION_EUROPE_MOSCOW": "ヨーロッパ/モスクワ",
    "RUSSIAN_FEDERATION_EUROPE_SAMARA": "ヨーロッパ/サマーラ",
    "RUSSIAN_FEDERATION_EUROPE_VOLGOGRAD": "ヨーロッパ/ヴォルゴグラード",
    "RUSSIAN_FEDERATION": "ロシア連邦",
    "RWANDA_AFRICA_KIGALI": "アフリカ/キガリ",
    "RWANDA": "ルワンダ共和国",
    "RX_BYTES": "受信バイト数",
    "RX_PACKETS": "受信パケット",
    "SA_EAST_1": "sa-east-1 (サンパウロ)",
    "SA_EAST_1A": "sa-east-1a",
    "SA_EAST_1B": "sa-east-1b",
    "SA_EAST_1C": "sa-east-1c",
    "SAFE_SEARCH_ENGINE": "セーフ サーチ エンジン",
    "SAINT_BARTHELEMY_AMERICA_ST_BARTHELEMY": "アメリカ/サン・バルテルミー島",
    "SAINT_BARTHELEMY": "サン・バルテルミー島",
    "SAINT_HELENA": "セントヘレナ",
    "SAINT_KITTS_AND_NEVIS_AMERICA_ST_KITTS": "アメリカ/セントキッツ",
    "SAINT_KITTS_AND_NEVIS": "セントキッツ・ネイビス連邦",
    "SAINT_LUCIA_AMERICA_ST_LUCIA": "アメリカ/セントルシア",
    "SAINT_LUCIA": "セントルシア",
    "SAINT_MARTIN_FRENCH_PART_AMERICA_MARIGOT": "アメリカ/マリゴ",
    "SAINT_MARTIN_FRENCH_PART": "セント・マーチン島(フランス領)",
    "SAINT_MARTIN": "セント・マーチン島",
    "SAINT_PIERRE_AND_MIQUELON": "サンピエール島・ミクロン島",
    "SAINT_VINCENT_AND_THE_GRENADINES_AMERICA_ST_VINCENT": "アメリカ/セントビンセント",
    "SAINT_VINCENT_AND_THE_GRENADINES": "セントビンセントおよびグレナディーン諸島",
    "SAML_CERTIFICATE_FILENAME": "IdP SAML証明書",
    "SAMOA_PACIFIC_APIA": "太平洋/アピア",
    "SAMOA": "サモア独立国",
    "SAN_MARINO_EUROPE_SAN_MARINO": "ヨーロッパ/サンマリノ",
    "SAN_MARINO": "サンマリノ共和国",
    "SAO_TOME_AND_PRINCIPE_AFRICA_SAO_TOME": "アフリカ/サントメ・プリンシペ",
    "SAO_TOME_AND_PRINCIPE": "サントメ・プリンシペ民主共和国",
    "SATURDAY": "土曜日",
    "SAUDI_ARABIA_ASIA_RIYADH": "アジア/リヤド",
    "SAUDI_ARABIA": "サウジアラビア王国",
    "SAVE_AND_NEXT": "保存して次へ",
    "SAVE_ERROR_MESSAGE": "検証エラーがあります。保存する前にすべてのフィールドを再確認してください。",
    "SAVE_SUCCESS_MESSAGE": "すべての変更が保存されました",
    "SAVE": "保存",
    "SCCP_DESC": " Skinny Client Control Protocol (SCCP)は、Cisco Call ManagerおよびCisco VOIP phone間で利用されるCisco独自のプロトコルです。このプロトコルをサポートしているベンダーもあります",
    "SCCP": "SCCP",
    "SCHEDULE_UPGRADE": "アップグレードをスケジュール",
    "SCHEDULED_VERSION": "スケジュールされたバージョン",
    "SCHEDULED": "スケジュール済み",
    "SCIENCE_AND_TECHNOLOGY": "科学/技術",
    "SCOPE": "範囲",
    "SCTP_ANY_DESC": "Stream Control Transmission Protocol (SCTP)は、インターネット プロトコル スイート(IP)のトランスポート層のプロトコルです。このプロトコルはもともと電気通信におけるシグナリング システム7 (SS7)のメッセージ トランスポートを目的としていましたが、ユーザー データグラム プロトコル(UDP)のメッセージ指向機能を提供すると同時に、伝送制御プロトコル(TCP)などの輻輳制御を備えたメッセージの信頼性の高いシーケンス伝送を保証します。",
    "SCTP_ANY": "SCTP",
    "SCTP_DEST_PORTS": "SCTP宛先ポート",
    "SCTP_PORT": "SCTPポート",
    "SCTP_PORTS": "SCTPポート",
    "SCTP_SRC_PORTS": "SCTP送信元ポート",
    "SCTP_UDP_Translation": "SCTP/UDP変換",
    "SEARCH_BY": "検索条件：",
    "SEARCH_ELLIPSIS": "検索...",
    "SEARCH_LOCATION": "ロケーションを検索",
    "SEARCH_RESULT": "検索結果",
    "SEARCH_TO_SEE_MORE": "検索して他の項目を表示",
    "SEARCH": "検索",
    "SECONDARY_DNS_OPTIONAL": "セカンダリーDNS (省略可)",
    "SECONDARY_DNS_SERVER_IP_ADDRESS": "セカンダリーDNSサーバーのIPアドレス",
    "SECONDARY_DNS_SERVER": "セカンダリーDNSサーバー",
    "SECONDARY_DNS": "セカンダリーDNS",
    "SECONDARY_PROXY": "セカンダリー プロキシ",
    "SECONDARY_SERVER_RESPONSE_PASS": "試行されたセカンダリー サーバー",
    "SECONDARY": "セカンダリー",
    "SECURITY_GROUP_ID": "セキュリティ グループID",
    "SECURITY_GROUP_NAME": "セキュリティ グループ名",
    "SEGMENT_GROUPS": "セグメント グループ",
    "SELECT_A_LOCATION": "ロケーションを選択",
    "SELECT_A_TEST": "テストを選択",
    "SELECT_ALL": "すべて選択",
    "SELECT_AN_EXISTING_LOCATION": "既存のロケーションを選択",
    "SELECT_BRANCH_PROVISIONING_LOCATION": "Branch Connectorプロビジョニング テンプレートのロケーションを選択します。",
    "SELECT_CC_GROUP": "Cloud Connectorグループを選択",
    "SELECT_CC_LOCATION": "Cloud Connectorのロケーションを選択",
    "SELECT_CC_VERSION": "Cloud Connectorのバージョンを選択",
    "SELECT_CHART_TYPE": "チャート タイプを選択",
    "SELECT_DESTINATION_IP": "宛先IPを選択",
    "SELECT_EVENT_TIME": "イベント時間を選択",
    "SELECT_FILTERS": "フィルターを選択",
    "SELECT_HYPERVISOR_VERSION": "ハイパーバイザーのバージョンを選択",
    "SELECT_RESOURCE_GROUP_NAME": "リソース グループを選択",
    "SELECT_STORAGE_ACCOUNT": "ストレージ アカウントを選択",
    "SELECT_SUBSCRIPTION_GROUP_NAME": "サブスクリプション グループ名を選択",
    "SELECT_SUBSCRIPTION": "サブスクリプションを選択",
    "SELECT_TWO_VERSIONS_TO_COMPARE": "比較する2つのバージョンを選択してください。",
    "SELECT_UPGRADE_WINDOW": "アップグレード ウィンドウを選択",
    "SELECT_ZSCALER_IP": "Zscaler IPを選択",
    "SELECT": "選択",
    "SELECTED_ITEMS": "選択された項目({{count}})",
    "SELECTED": "選択済み",
    "SENEGAL_AFRICA_DAKAR": "アフリカ/ダカール",
    "SENEGAL": "セネガル共和国",
    "SENT_BYTES": "送信されたバイト数",
    "SENT_COUNT": "送信回数",
    "SENT_MESSAGES": "送信済みメッセージ",
    "SERBIA_EUROPE_BELGRADE": "ヨーロッパ/ベオグラード",
    "SERBIA": "セルビア共和国",
    "SERIAL_NUMBER": "シリアル番号",
    "SERVER_DESTINATION_IP": "サーバー宛先IP",
    "SERVER_DESTINATION_PORT": "サーバー宛先ポート",
    "SERVER_IP_CATEGORY": "サーバーIPカテゴリー",
    "SERVER_IP": "サーバーIP",
    "SERVER_NAME": "サーバー名",
    "SERVER_NETWORK_PROTOCOL": "サーバーNWプロトコル",
    "SERVER_PORT": "サーバー ポート",
    "SERVER_SOURCE_IP": "サーバー送信元IP",
    "SERVER_SOURCE_PORT": "サーバー送信元ポート",
    "SERVER": "サーバー トラフィック",
    "SERVERS": "サーバー",
    "SERVFAIL": "サーバー障害",
    "Service IP": "サービスIP",
    "SERVICE_GATEWAY_IP_ADDRESS": "サービス ゲートウェイIPアドレス",
    "SERVICE_GROUPS": "サービス グループ",
    "SERVICE_INFORMATION": "サービス情報",
    "SERVICE_INTERFACE": "サービス インターフェイス",
    "SERVICE_IP_ADDRESS_ONE": "サービスIPアドレス1",
    "SERVICE_IP_ADDRESS_POOL": "サーバーIPアドレス プール",
    "SERVICE_IP_ADDRESS_THREE": "サービスIPアドレス3",
    "SERVICE_IP_ADDRESS_TWO": "サービスIPアドレス2",
    "SERVICE_IP_ADDRESS": "サービスIPアドレス",
    "SERVICE_IP": "サービスIP",
    "SERVICE_STATUS": "サービス ステータス",
    "SERVICE_VIRTUAL_IP_ADDRESS": "サービスの仮想IPアドレス",
    "SERVICE": "サービス",
    "SERVICES": "サービス",
    "SESSION_COUNT_TREND": "セッション数の傾向",
    "SESSION_COUNT": "セッション数",
    "SESSION_DURATION": "セッション期間",
    "SESSION_ID": "セッションID",
    "SESSION_INSIGHTS": "セッション インサイト",
    "SESSION_LOGS": "セッション ログ",
    "SESSION_TIMED_OUT": "セッションの有効期限が切れました。続行するには再度ログインしてください。",
    "SESSION": "セッション",
    "SESSIONS_ACROSS_SERVICES": "サービス横断的なセッション",
    "SESSIONS": "セッション",
    "SET_PASSWORD": "パスワードを設定",
    "SEXUALITY": "セクシャリティー",
    "SEYCHELLES_INDIAN_MAHE": "インド洋/マヘ",
    "SEYCHELLES": "セーシェル共和国",
    "SHAREWARE_DOWNLOAD": "シェアウェアのダウンロード",
    "SHOW_DETAILS": "詳細を表示",
    "SHOW_LESS_ELLIPSIS": "少なく表示...",
    "SHOW_MORE_ELLIPSIS": "さらに表示...",
    "SHOW": "表示",
    "SHUTDOWN": "シャットダウン",
    "SIERRA_LEONE_AFRICA_FREETOWN": "アフリカ/フリータウン",
    "SIERRA_LEONE": "シエラレオネ共和国",
    "SIGN_IN": "サイン イン",
    "SIGN_OUT": "サイン アウト",
    "SIGNING_CERTIFICATE": "署名証明書",
    "SINGAPORE": "シンガポール共和国",
    "SINGLE_APPLIANCE_ADDED_INFO": "新しいアプライアンスは[アプライアンス]ページで確認できます。詳細は、コネクターの{1}ヘルプ ポータル{2}を参照してください。",
    "SINGLE_APPLIANCE_ADDED": "新しいアプライアンスがテナントに1台追加されました。",
    "SIP_DESC": " Session Initiation Protocol (SIP)は、IPを介したマルチメディア会議に関するInternet Engineering Task Force (IETF)の標準です。他のVoIPプロトコルと同様に、パケット テレフォニー ネットワーク内のシグナリングおよびセッション管理の機能に対応するように設計されています",
    "SIP": "SIP",
    "SIZE_MUST_BE_EXACT_LENGTH": "このフィールドのサイズ ",
    "SLOVAKIA_EUROPE_BRATISLAVA": "ヨーロッパ/ブラチスラヴァ",
    "SLOVAKIA": "スロバキア共和国",
    "SLOVENIA_EUROPE_LJUBLJANA": "ヨーロッパ/リュブリャナ",
    "SLOVENIA": "スロヴェニア共和国",
    "SMALL": "小",
    "SMB_DESC": " Server Message Block Protocol (SMB/SMB2)は、クライアント アプリケーションがファイルを読み書きし、コンピューター ネットワーク内のサーバー プログラムからサービスをリクエストする方法を提供します",
    "SMB": "SMB",
    "SMBA": "SMBA",
    "SMBAC": "サンドボックス コントローラー",
    "SMBAUI": "サンドボックスUI",
    "SMEDGE_BOOTING": "SMEDGE:起動中。",
    "SMEDGE_END": "smedgeエラー コードの末尾。使用しないでください。",
    "SMEDGE_INIT": "SMEDGE:初期化中。",
    "SMEDGE_NOT_RUNNING": "SMEDGE:プロセスが実行されていません。",
    "SMEDGE_PKG_DOWNLOAD": "SMEDGE:パッケージのダウンロードが進行中です。",
    "SMEDGE_PKG_INSTALL": "SMEDGE:パッケージのインストールが進行中です。",
    "SMEDGE_START": "smedgeエラー コードの先頭。使用しないでください。",
    "SMEDGE_UNKNOWN_ERROR": "SMEDGE:不明なゲートウェイ エラー コード。",
    "SMEDGE_UPDATING": "SMEDGE:更新中。",
    "SMEDGE_UPGRADING": "SMEDGE:アップグレード中。",
    "SMEDGE_ZIA_BRINGUP": "SMEDGE: ZIAトンネルを起動しています。",
    "SMEDGE_ZIA_ZPA_BRINGUP": "SMEDGE: ZIAトンネルを起動し、ZPA接続を確立しています。",
    "SMRES_ERROR": "SMRESサーバー アプリがHTTPエラー レスポンスを返しました。",
    "SMTP_AV_ENCRYPTED_ALLOW": "暗号化された添付データを許可",
    "SMTP_AV_ENCRYPTED_ATTACH_DROP": "パスワードで暗号化された添付データ、添付データをドロップ",
    "SMTP_AV_ENCRYPTED_DROP": "パスワードで暗号化された添付データ、メッセージを拒否",
    "SMTP_AV_UNSCANNABLE_ALLOW": "スキャンできない添付データを許可",
    "SMTP_AV_UNZIPPABLE_ATTACH_DROP": "スキャンできない添付データ、添付データをドロップ",
    "SMTP_AV_UNZIPPABLE_DROP": "スキャンできない添付データ、メッセージを拒否",
    "SMTP_AV_UWL": "ウイルスUWL、",
    "SMTP_AV_VIRUS_ATTACH_DROP": "ウイルスを検知、添付データをドロップ",
    "SMTP_AV_VIRUS_DROP": "ウイルスを検知、メッセージを拒否",
    "SMTP_CMD_TIMEOUT_LIMIT": "SMTP/SMTQTN:コマンド タイムアウト(秒単位)",
    "SMTP_CONCURRENT_CLIENT_CONN_LIMIT": "SMTP/SMQTN:クライアント同時接続制限",
    "SMTP_CONCURRENT_SERVER_CONN_LIMIT": "SMTP/SMQTN:サーバー同時接続制限",
    "SMTP_DATA_TIMEOUT_LIMIT": "SMTP/SMQTN: DATAコマンドのタイムアウト(秒単位)",
    "SMTP_DESC": "Simple Mail Transfer Protocolは、サーバー間でメール メッセージを送信するためのプロトコルです",
    "SMTP_DLP_ALLOW": "DLPにヒット、メッセージを許可",
    "SMTP_DLP_DROP": "DLPにヒット、メッセージを拒否",
    "SMTP_DLP_QTN": "DLPにヒット、メッセージを検疫",
    "SMTP_DLP_SIGN_REQUIRED": "DLPにヒット、メッセージが未サイン、メッセージを拒否",
    "SMTP_DLP_TLS_REQUIRED": "DLPにヒット、コネクションがTLSでない、メッセージを拒否",
    "SMTP_DLP": "SMTP - 順守",
    "SMTP_EODT_TIMEOUT_LIMIT": "SMTP/SMQTN: EODTコマンドのタイムアウト(秒単位)",
    "SMTP_ERRINJECTION_DELAY_LIMIT": "GULPER:エラー挿入遅延(秒単位)",
    "SMTP_FLOWCONTROL": "メッセージ配信上のフロー コントロール - SMQTN上でのみ適用可能",
    "SMTP_INCOMPL_TRANS": "SMTPトランザクションがピアによって切断されました",
    "SMTP_INSPOL": "SMTP保険ポリシー",
    "SMTP_MAILPERCONN_LIMIT": "SMTP/SMQTN:接続制限ごとのメッセージ",
    "SMTP_MAILSIZE_LIMIT": "SMTP/SMQTN:メッセージ サイズ制限",
    "SMTP_MF_ATTACHBLK_ATTACH_DROP": "添付データのブロックにヒット、添付データをドロップ",
    "SMTP_MF_ATTACHBLK_MSG_DROP": "添付データのブロックにヒット、メッセージを拒否",
    "SMTP_MF_RCPT_DROP": "受信者を拒否",
    "SMTP_MF_SIGN_REQUIRED": "受信者を拒否、メッセージが未サイン",
    "SMTP_MF_TLS_REQUIRED": "受信者を拒否、コネクションがTLSでない",
    "SMTP_MIN_MSBC_DENSITY": "SMTP/SMQTN: data_inに到着するmsbsの密度がこの最小パーセンテージ値を下回っている場合にスクイーズする",
    "SMTP_NOCA_BYPASS_CONFIG": "バイパスされたポリシー",
    "SMTP_NOCA_BYPASS": "SMTP/SMQTN: CAが利用できない、または構成レスポンスがエラーになる場合にデフォルトのsmtp構成を使用する",
    "SMTP_OUTBD_DROP_SUSPECTED_SPAM": "スパムとして疑われる送信メッセージを破棄する",
    "SMTP_PLATFORM_1": "SMTP - プラットフォーム",
    "SMTP_PLATFORM_2": "SMTP - プラットフォーム II (アウトバウンド)",
    "SMTP_PRETRY": "プロキシからの再試行 - SMTPノードにのみ適用可能",
    "SMTP_PROXY": "SMTPプロキシ クラスター",
    "SMTP_RCPT_COPY": "受信者はコピー、",
    "SMTP_RCPT_REDIRECT": "受信者はリダイレクト、",
    "SMTP_RCPT_UNDELIVERABLE": "受信者を拒否、配信できませんでした",
    "SMTP_RCPTS_LIMIT": "SMTP/SMQTN: smtpトランザクション制限当たりの受信者数",
    "SMTP_RESERVED2": "SMTP: reserved2",
    "SMTP_RESERVED3": "SMTP: reserved3",
    "SMTP_RESERVED4": "SMTP: reserved4",
    "SMTP_RESERVED5": "SMTP: reserved5",
    "SMTP_REUSE_TIMEOUT_LIMIT": "SMTP/SMQTN:接続再利用プールのタイムアウト",
    "SMTP_SECURE": "SMTP - セキュア",
    "SMTP_SENDER_MASQ": "マスカレードされた送信者、",
    "SMTP_SMQTN": "SMTP検疫クラスター",
    "SMTP_SPAM_DROP": "スパムを検知、メッセージを拒否",
    "SMTP_SPAM_IPWL": "スパムIPWL、",
    "SMTP_SPAM_SUSPECT_ALLOW": "スパムの疑いあり、メッセージを許可",
    "SMTP_SPAM_SUSPECT_DROP": "スパムの疑いあり、メッセージを拒否",
    "SMTP_SPAM_SUSPECT_MARKSUBJ": "スパムの疑いあり、サブジェクトをプリペンド",
    "SMTP_SPAM_SUSPECT_QTN": "スパムの疑いあり、メッセージを検疫",
    "SMTP_SPAM_UBL_DROP": "スパムUBLを検知、メッセージを拒否",
    "SMTP_SPAM_UWL": "スパムUWL、",
    "SMTP_SPAM": "SMTP - スパム対策設定",
    "SMTP_SPF_ENABLED": "SPFルックアップはこの会社から有効化されています",
    "SMTP_TRANS_TIMEOUT_LIMIT": "SMTP/SMQTN:トランザクション タイムアウト(秒単位)",
    "SMTP_USERLIST_LIMIT": "SMTPユーザーリスト クエリーLIMIT値",
    "SMTP": "SMTP",
    "SMTPEVT_VERBOSE": "冗長としてフラグされているsmtpイベントを記録する",
    "SMTPTDL": "TDL - トランザクション データ制限",
    "SMUI_PROCESS_TIMEOUT": "SMUIプロセスのタイムアウト値",
    "SMUI": "SMUI",
    "SN_POSTING_CAUTIONED": "このサイトへのメッセージの投稿に対する警告",
    "SN_POSTING_DENIED": "このサイトへのメッセージの投稿は許可されていません",
    "SN_WEBUSE_CAUTIONED": "このソーシャル ネットワークまたはブログ サイトの使用に対する警告",
    "SN_WEBUSE_DENIED": "このソーシャル ネットワークまたはブログ サイトの使用は許可されていません",
    "SNAGFILMS_DESC": " SnagFilms",
    "SNAGFILMS": "SnagFilms",
    "SNMP_DESC": " SNMPアプリケーション(別名SNMPマネージャー)およびSNMPエージェント",
    "SNMP": "SNMP",
    "SNMPTRAP_DESC": "SNMPトラップにより、エージェントは未承諾のSNMPメッセージで重要なイベントを管理ステーションに通知できます",
    "SNMPTRAP": "SNMPトラップ",
    "SOA": "SOA",
    "SOAP_DESC": " SOAPは分散環境で構造化された情報を交換することを目的とした軽量プロトコルです。XML技術を使用して、さまざまな基礎プロトコル上で交換可能なメッセージ構成要素を含む拡張可能なメッセージング フレームワークを定義します",
    "SOAP": "SOAP",
    "SOC1": "SOC1",
    "SOC2": "SOC2",
    "SOC3": "SOC3",
    "SOCIAL_ACTIVITY": "ソーシャル ネットワーキング活動",
    "SOCIAL_ADULT_DESC": "出会い系サイトなど、成人向けのソーシャル ネットワーキングを提供するサイト。",
    "SOCIAL_ADULT": "成人向けソーシャル ネットワーキング",
    "SOCIAL_ISSUES": "社会問題",
    "SOCIAL_NETWORKING_GAMES": "ソーシャル ネットワーキング ゲーム",
    "SOCIAL": "ソーシャル ネットワーキング",
    "SOCIALBAKERS": "SocialBakers",
    "SOCIALTV_DESC": " このプロトコル プラグインは、ホストsrv.sixdegs.comへのhttpトラフィックを分類します",
    "SOCIALTV": "Social TV",
    "SOCIALVIBE_DESC": " このプロトコル プラグインは、ホストsocialvibe.comへのhttpトラフィックを分類します",
    "SOCIALVIBE": "SocialVibe",
    "SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP": "アップグレードの予定時刻はコネクターのタイム ゾーンに基づきます。アップグレードのスケジュールは1時間前までに設定する必要があります。",
    "SOFTWARE_UPGRADE_SCHEDULE": "ソフトウェア アップグレード スケジュール",
    "SOLOMON_ISLANDS_PACIFIC_GUADALCANAL": "太平洋/ガダルカナル島",
    "SOLOMON_ISLANDS": "ソロモン諸島",
    "SOMALIA_AFRICA_MOGADISHU": "アフリカ/モガディシュ",
    "SOMALIA": "ソマリア連邦共和国",
    "SORRY_THIS_CODE_CAN_NOT_BE_USED": "これは使用できません。",
    "SOURCE_IP_ADDRESSES": "送信元IPアドレス",
    "SOURCE_IP_GROUP": "送信元IPグループ",
    "SOURCE_IP_GROUPS": "送信元IPグループ",
    "SOURCE_IP": "送信元IP",
    "SOURCE_PORT": "送信元ポート",
    "SOURCE": "送信元",
    "SOUTH_AFRICA_AFRICA_JOHANNESBURG": "アフリカ/ヨハネスブルグ",
    "SOUTH_AFRICA": "南アフリカ共和国",
    "SOUTH_AMERICA": "南アメリカ",
    "SOUTH_GEORGIA_AND_SOUTH_SANDWICH_ISLANDS": "サウスジョージア・サウスサンドウィッチ諸島",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS_ATLANTIC_SOUTH_GEORGIA": "大西洋/サウスジョージア",
    "SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS": "サウスジョージア・サウスサンドウィッチ諸島",
    "SOUTH_KOREA": "大韓民国",
    "SOUTHAFRICA": "南アフリカ共和国",
    "SOUTHAFRICANORTH": "(アフリカ)南アフリカ北部",
    "SOUTHAFRICAWEST": "(アフリカ)南アフリカ西部",
    "SOUTHAMERICA_EAST1_A": "southamerica-east1-a",
    "SOUTHAMERICA_EAST1_B": "southamerica-east1-b",
    "SOUTHAMERICA_EAST1_C": "southamerica-east1-c",
    "SOUTHAMERICA_EAST1": "southamerica-east1",
    "SOUTHAMERICA_WEST1_A": "southamerica-west1-a",
    "SOUTHAMERICA_WEST1_B": "southamerica-west1-b",
    "SOUTHAMERICA_WEST1_C": "southamerica-west1-c",
    "SOUTHAMERICA_WEST1": "southamerica-west1",
    "SOUTHCENTRALUS": "(米国)米国中南部",
    "SOUTHCENTRALUSSTAGE": "(米国)米国中南部(ステージ)",
    "SOUTHEASTASIA": "(アジア太平洋)東南アジア",
    "SOUTHEASTASIASTAGE": "(アジア太平洋)東南アジア(ステージ)",
    "SOUTHINDIA": "(アジア太平洋)南インド",
    "SPAIN_AFRICA_CEUTA": "アフリカ/セウタ",
    "SPAIN_ATLANTIC_CANARY": "大西洋/カナリア諸島",
    "SPAIN_EUROPE_MADRID": "ヨーロッパ/マドリード",
    "SPAIN": "スペイン",
    "SPECIAL_INTERESTS": "コミュニティー/社会団体",
    "SPECIALIZED_SHOPPING": "オンライン ショッピング",
    "SPLIT_DEPLOY_CORE": "分割展開 - コア",
    "SPLIT_DEPLOY_EDGE": "分割展開 - エッジ",
    "SPLUNK": "Splunk",
    "SPORTS": "スポーツ",
    "SPYWARE_OR_ADWARE": "スパイウェア コールバック",
    "SRI_LANKA_ASIA_COLOMBO": "アジア/コロンボ",
    "SRI_LANKA": "スリランカ民主社会主義共和国",
    "SRV_RX_BYTES": "サーバーが受信したバイト数",
    "SRV_TIMEOUT": "サーバーが応答しなかったため、DNSトランザクションがタイムアウトしました",
    "SRV_TX_BYTES": "サーバーが送信したバイト数",
    "SRV_TX_DROPS": "サーバーがバイトを破棄",
    "SSDP_DESC": " Simple Service Discovery Protocol (SSDP)は、ネットワーク クライアントが目的のネットワーク サービスを検出できるメカニズムを提供します",
    "SSDP": "SSDP",
    "SSH_DESC": " Secure Shell (SSH)はUNIXベースのコマンド インターフェイスで、リモート コンピューターに安全にアクセスするためのプロトコルです。Secure Socket Shellとも呼ばれます",
    "SSH": "SSH",
    "SSHFP": "SSHFP",
    "SSL_CERTIFICATE": "SSL証明書",
    "SSO_LOGOUT_MESSAGE": "Cloud Connectorポータルから正常にログアウトしました",
    "ST_HELENA_ATLANTIC_ST_HELENA": "大西洋/セントヘレナ",
    "ST_HELENA": "セントヘレナ",
    "ST_KITTS_AND_NEVIS": "セントキッツ・ネイビス連邦",
    "ST_PIERRE_AND_MIQUELON_AMERICA_MIQUELON": "アメリカ/ミクロン島",
    "ST_PIERRE_AND_MIQUELON": "サンピエール島・ミクロン島",
    "ST_VINCENT_AND_THE_GRENADINES": "セントビンセントおよびグレナディーン諸島",
    "STAGED": "ステージング済み",
    "STANDBY": "スタンバイ",
    "START_OVER": "やり直す",
    "START_TIME": "開始時刻",
    "STARTS_WITH": "次の値で始まる",
    "STAT": "統計",
    "STATE_PROVINCE": "市区町村、都道府県",
    "STATE": "県",
    "STATIC_IP_ADDRESS": "静的IPアドレス",
    "STATIC_IP_ADDRESSES": "静的IPアドレスとGREトンネル",
    "STATIC_IP_CONFLICT_WITH_SUBINTERFACE_IP": "静的リースIPがサブインターフェイスIPと競合しています",
    "STATIC_IP_HAS_DUPLICATES_IPs": "静的リースのIPが重複しています",
    "STATIC_IP_HAS_DUPLICATES_MACS": "静的リースのMACアドレスが重複しています",
    "STATIC_LEASE": "静的リース",
    "STATIC_LOCATION_GROUPS": "手動ロケーション グループ",
    "STATIC_MANAGEMENT_IP": "静的管理IP",
    "STATIC_ROUTE_OPTIONAL": "静的ルート(省略可)",
    "STATIC_ROUTE": "静的ルート",
    "STATIC_SERVICE_IP": "静的サービスIP",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1": "テンプレートのステータスを更新してもよろしいですか？",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED": "更新された項目は、ステージング状態に移行すると編集/削除できます。",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1": "構成は、デバイスがオンラインになったときに適用されます。",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED": "テンプレートは、[展開準備完了]に更新されると編集できなくなります。",
    "STATUS_UPDATE_CONFIRMATION_MESSAGE": "このリソースを更新してもよろしいですか？",
    "STATUS_UPDATE_CONFIRMATION": "ステータス更新の確認",
    "STATUS": "ステータス",
    "STORAGE_ACCOUNT_TEXT": "パートナー トピックと宛先が作成されるリージョン、サブスクリプション、ストレージ アカウント グループを選択します。",
    "STORAGE_ACCOUNT": "ストレージ アカウント",
    "STREAMING_MEDIA": "ビデオ ストリーミング",
    "STRING": "文字列",
    "SUB_CATEGORIES": "サブ カテゴリー",
    "SUB_CATEGORY": "サブカテゴリー",
    "SUB_INTERFACE_SHUTDOWN": "サブインターフェイスのシャットダウン",
    "SUB_INTERFACE_VLAN": "サブインターフェイスVLAN",
    "SUB_INTERFACE": "サブインターフェイス",
    "SUBCLOUDS": "サブクラウド",
    "SUBLOCATIONS": "サブロケーション",
    "SUBMIT_A_TICKET": "チケットを送信",
    "SUBMIT_TICKET": "チケットを送信",
    "SUBMIT": "送信",
    "SUBMITTED_ON": "送信日",
    "SUBNET_ID": "サブネットID",
    "SUBSCRIPTION_GROUP_NAME": "サブスクリプション グループ名",
    "SUBSCRIPTION_GROUP": "サブスクリプション グループ",
    "SUBSCRIPTION_GROUPS_TEXT": "Azureアカウントのリージョンとサブスクリプションを構成します。",
    "SUBSCRIPTION_GROUPS": "サブスクリプション グループ",
    "SUBSCRIPTION_ID": "AzureサブスクリプションID",
    "SUBSCRIPTION_REQUIRED_MESSAGE": "この機能を使用するには、組織が現在所有するものとは別のサブスクリプションが必要です。この機能の詳細については、営業担当者にお問い合わせください。",
    "SUBSCRIPTION_REQUIRED": "サブスクリプションが必要です",
    "SUBSCRIPTIONS": "サブスクリプション",
    "SUCCESS": "成功",
    "SUCCESSFULLY_DELETED": "正常に削除されました",
    "SUCCESSFULLY_DISABLED": "正常に無効化されました",
    "SUCCESSFULLY_ENABLED": "正常に有効化されました",
    "SUCCESSFULLY_REGENERATED": "正常に再生成されました",
    "SUCCESSFULLY_SAVED": "正常に保存されました",
    "SUCCESSFULLY_UPDATED": "正常に更新されました",
    "SUDAN_AFRICA_KHARTOUM": "アフリカ/ハルツーム",
    "SUDAN": "スーダン共和国",
    "SUM": "Sum",
    "SUMO_LOGIC": "Sumo Logic",
    "SUNDAY": "日曜日",
    "SUPPORT_INFORMATION": "サポート情報",
    "SUPPORT_TUNNEL": "サポート トンネル",
    "SUPPORT": "サポート",
    "SURINAME_AMERICA_PARAMARIBO": "アメリカ/パラマリボ",
    "SURINAME": "スリナム共和国",
    "SURROGATE_IP_REFRESH_RATE": "サロゲートの再検証のための更新時間",
    "SUSPICIOUS_DESTINATION": "疑わしい送信先",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS_ARCTIC_LONGYEARBYEN": "北極/ロングイェールビーン",
    "SVALBARD_AND_JAN_MAYEN_ISLANDS": "スヴァールバル諸島およびヤンマイエン島",
    "SVALBARD_AND_JAN_MAYEN": "スヴァールバル諸島およびヤンマイエン島",
    "SVPN": "Z-Tunnel 2.0",
    "SWAZILAND_AFRICA_MBABANE": "アフリカ/ムババーネ",
    "SWAZILAND": "スワジランド王国",
    "SWEDEN_EUROPE_STOCKHOLM": "ヨーロッパ/ストックホルム",
    "SWEDEN": "スウェーデン王国",
    "SWEDENCENTRAL": "(ヨーロッパ)スウェーデン中部",
    "SWITZERLAND_EUROPE_ZURICH": "ヨーロッパ/チューリッヒ",
    "SWITZERLAND": "スイス",
    "SWITZERLANDNORTH": "(ヨーロッパ)スイス北部",
    "SWITZERLANDWEST": "(ヨーロッパ)スイス西部",
    "SYRIA": "シリア・アラブ共和国",
    "SYRIAN_ARAB_REPUBLIC_ASIA_DAMASCUS": "アジア/ダマスカス",
    "SYRIAN_ARAB_REPUBLIC": "シリア・アラブ共和国",
    "SYSLOG_DESC": " Syslogプロトコルは、クライアントとサーバー間のネットワークを介したイベント通知メッセージの送信に使用されます",
    "SYSLOG": "Syslog",
    "SYSTEM_IN_READ_ONLY_MODE_ONLY": "現在、UIは読み取り専用モードです。",
    "SYSTEM_IN_READ_ONLY_MODE": "UIはアップグレード メンテナンス中のため、現在読み取り専用モードになっています。",
    "SYSTEM_SETTINGS": "システム設定",
    "SYSTEM_USER": "システム ユーザー",
    "SYSTEM": "システム",
    "TAB_SEPARATED": "タブ区切り",
    "TABLE_OPTIONS": "表のオプション",
    "TACACS_DESC": "Terminal Access Controller Access-Control Systemは、リモート認証を処理する関連プロトコルのファミリーと、集中型サーバーを介したネットワーク化されたアクセス制御のための関連サービスを指します",
    "TACACS_PLUS_DESC": " TACACS+ (Terminal Access Controller Access-Control System Plus) は、1つまたは複数の集中型サーバーを介して、ルーター、ネットワーク アクセス サーバー、その他のネットワーク コンピューティング デバイスのアクセス制御を提供するCisco Systems独自のプロトコルです",
    "TACACS_PLUS": "TACACS+",
    "TACACS": "TACACS",
    "TAGGED": "タグ付き",
    "TAGS": "タグ",
    "TAIWAN_ASIA_TAIPEI": "アジア/タイペイ",
    "TAIWAN": "台湾",
    "TAJIKISTAN_ASIA_DUSHANBE": "アジア/ドゥシャンベ",
    "TAJIKISTAN": "タジキスタン共和国",
    "TANZANIA_AFRICA_DAR_ES_SALAAM": "アフリカ/ダルエスサラーム",
    "TANZANIA": "タンザニア連合共和国",
    "TARGET_ORG_ID": "ターゲット組織ID",
    "TARINGA_DESC": " このプロトコル プラグインは、ホストtaringa.netへのhttpトラフィックを分類します",
    "TARINGA": "Taringa",
    "TASTELESS_DESC": " 拷問、人や動物の虐待、一般的に公衆の場では不適切とされる行為に関連するサイト。",
    "TASTELESS": "低俗",
    "TATTOODESIGNS": "Tattoo Designs",
    "TB": "TB",
    "TCF": "TCF",
    "TCHATCHE_DESC": "Tchatcheはインスタント メッセージのWebサイトです",
    "TCHATCHE": "Tchatche",
    "TCP_ANY_DESC": "Transmission Control Protocol (TCP)は、インターネット プロトコル スイート(IP)のコア プロトコルの1つです。非常に一般的であるため、スイート全体がTCP/IPと呼ばれる場合もあります",
    "TCP_ANY": "TCP",
    "TCP_DESC": " Transmission Control Protocol (TCP)は、インターネット プロトコル スイートのコア プロトコルの1つです。非常に一般的であるため、スイート全体がTCP/IPと呼ばれる場合もあります",
    "TCP_DEST_PORTS": "TCP宛先ポート",
    "TCP_OVER_DNS_DESC": " Tcp-over-dnsには、特別なdnsサーバーと特別なdnsクライアントが含まれています。クライアントとサーバーは連携して動作し、標準のDNSプロトコルを介してTCPおよびUDPトンネルを提供します",
    "TCP_OVER_DNS": "TCP Over DNS",
    "TCP_PORT": "TCPポート",
    "TCP_PORTS": "TCPポート",
    "TCP_SRC_PORTS": "TCP送信元ポート",
    "TCP_STATS_COUNTER_INTERVAL": "TCP状態カウンターをキャプチャー",
    "TCP_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないTCPプロキシ/ファイアウォール トラフィックが特定されます",
    "TCP_UNKNOWN": "不明なTCP",
    "TCP": "TCP",
    "TDS_DESC": "Microsoft SQLリレーショナル データベース管理システム用プロトコル",
    "TDS": "TDS",
    "TEACHERTUBE_DESC": " このプロトコル プラグインは、ホストteachertube.com、teachertube.bizへのhttpトラフィックを分類します",
    "TEACHERTUBE": "TeacherTube",
    "TEACHSTREET_DESC": " このプロトコル プラグインは、ホストteachstreet.comへのhttpトラフィックを分類します",
    "TEACHSTREET": "TeachStreet",
    "TEAMSPEAK_DESC": " TeamSpeak2は、ゲーマーやTeamSpeak2 VoIPソフトウェア主義のユーザーだけに使用されるプロトコルです",
    "TEAMSPEAK_V3_DESC": " TeamSpeak 3は、元のTeamSpeakの通信システムを踏襲しています。TeamSpeak 3は単なる前作の拡張版ではなく、独自のプロトコルとコアの技術をC++言語で書き換えたものです",
    "TEAMSPEAK_V3": "TeamSpeak 3",
    "TEAMSPEAK": "TeamSpeak",
    "TEAMVIEWER_DESC": " TeamViewerはメンテナンス処理を行うために、リモート コンピューターへの接続を可能にするアプリケーションです。現在の画面をリモート コンピューターに表示したり、ファイルを転送したり、VPNトンネルを作成したりすることもできます",
    "TEAMVIEWER": "TeamViewer",
    "TECHINLINE_DESC": " このプロトコル プラグインは、ホストtechinline.comへのhttpトラフィックを分類します。コモン ネームtechinline.comへのsslトラフィックも分類します",
    "TECHINLINE": "Techinline",
    "TECHNICAL_PRIMARY": "技術担当の第1連絡先",
    "TECHNICAL_SECONDARY": "技術担当の第2連絡先",
    "TECHNOLOGY_COMMUNICATION": "テクノロジーとコミュニケーション",
    "TECHNOLOGY": "テクノロジー",
    "TED": "TED",
    "TELECOMMUNICATION": "電気通信",
    "TELEGRAM_DESC": " Telegramは、Whatsappのようなインスタント メッセージのプロトコルです",
    "TELEGRAM": "Telegram",
    "TELEVISION_AND_MOVIES_DESC": " 配信またはメディア ファイルとしてのダウンロード可否を問わず、テレビ番組または映画に関連するサイト。",
    "TELEVISION_AND_MOVIES": "テレビ/映画",
    "TELEVISION_MOVIES_DESC": " 配信またはメディア ファイルとしてのダウンロード可否を問わず、テレビ番組または映画に関連するサイト。",
    "TELEVISION_MOVIES": "テレビ/映画",
    "TELNET_DESC": " Telnetは、非常に一般的な双方向の8ビット バイト型の通信機能を提供します。その主な目的は、ターミナル端末とターミナル型プロセスとの間にインターフェイスの標準的な方法を提供することです",
    "TELNET": "Telnet",
    "TELNETS_DESC": " Telnetのセキュアなバージョン",
    "TELNETS": "Secure Telnet",
    "TEMPLATE_NAME": "テンプレート名",
    "TEMPLATE_NOT_FOUND": "テンプレートが見つかりません",
    "TEMPLATE_PREFIX": "テンプレート プレフィックス",
    "TEMPLATE": "テンプレート",
    "TENANT_ID": "テナントID",
    "TENANT_NAME": "テナント名",
    "TERRA_FORMATION": "Terraform",
    "TEST_CONNECTIVITY_FAILED": "接続テストに失敗しました",
    "TEST_CONNECTIVITY_SUCCESSFUL": "接続テストに成功しました",
    "TEST_ENVIRONMENT_TEXT": "トラフィック テストは、Zscalerが作成したテスト環境から実行されるHTTP/HTTPSリクエストのシミュレーションです。このテスト環境は、VPCエンドポイントを使用してゲートウェイに接続されています。1つのテスト環境は、1つのZscalerテナントに関連付けられます。すべてのテストは同じ環境から実行されます。",
    "TEST_ENVIRONMENT": "テスト環境",
    "TEST_EXECUTED": "テストが実行されました",
    "TEST_NAME": "テスト名",
    "TEST_PROTOCOL": "テスト プロトコル",
    "TESTS": "テスト",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_R53": "ZPAおよび高可用性を備えたスターター展開テンプレート",
    "TF_DEFAULT_DEPLOYMENT_FOR_TWO_CC_TEMPLATE": "高可用性を備えたスターター展開テンプレート",
    "TF_DEFAULT_DEPLOYMENT_TEMPLATE": "スターター展開テンプレート",
    "TF_DEFAULT_DEPLOYMENT_WITH_R53_TEMPLATE": "ZPA付きスターター展開テンプレート",
    "TF_STARTER_DEPLOYMENT_GWLB_TEMPLATE": "ゲートウェイ ロード バランサー(GWLB)付きスターター展開テンプレート",
    "TFTP_DESC": "Trivial File Transfer Protocol (TFTP)は非常にシンプルなファイル転送プロトコルです",
    "TFTP": "TFTP",
    "THAILAND_ASIA_BANGKOK": "アジア/バンコク",
    "THAILAND": "タイ王国",
    "THE_BASE_URL_FOR_YOUR_API": "APIのベースURL:",
    "THE_FOLLOW_REGIONS_ARE_PENDING": "次のリージョンは保留中です ",
    "THE_FOLLOWING_STATIC_LEASE_IP_IS_NOT_INCLUDED_ON_THE_ADDRESS_RANGE": "次の静的リースIPはアドレス範囲に含まれていません。",
    "THE_GAMBIA": "ガンビア共和国",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_SUBNET": " ゲートウェイIPはLANサブネット内にある必要があります。",
    "THE_GATEWAY_SHOULD_BELONG_TO_A_LAN_WAN_SUBNET": " ゲートウェイIPはLANまたはWANサブネット内にある必要があります。",
    "THE_NETHERLANDS": "オランダ",
    "THERE_IS_A_PROBLEM_SAVING_PROVISIONING_TEMPLATE": "プロビジョニング テンプレートの保存に問題があります。後でもう一度お試しください。",
    "THERE_IS_A_PROBLEM_SAVING_VDI_TEMPLATE": "VDIテンプレートの保存に問題があります。後でもう一度お試しください。",
    "THREAT_LIBRARY": "脅威ライブラリー",
    "THROUGHPUT_ACROSS_SERVICES": "サービス横断的なスループット",
    "THROUGHPUT_KBPS_PER_SESSION": "スループット(kbps)/ セッション",
    "THROUGHPUT_SESSION": "[スループット | セッション]",
    "THURSDAY": "木曜日",
    "TIME_FRAME": "時間枠",
    "TIME_ZONE": "タイム ゾーン",
    "TIMESTAMP": "タイムスタンプ",
    "TIMEZONE": "タイムゾーン",
    "TIMOR_LESTE_ASIA_DILI": "アジア/ディリ",
    "TIMOR_LESTE": "東ティモール民主共和国",
    "TLS": "TLS",
    "TO": "終了",
    "TOGO_AFRICA_LOME": "アフリカ/ロメ",
    "TOGO": "トーゴ共和国",
    "TOKELAU_PACIFIC_FAKAOFO": "太平洋/ファカオフォ",
    "TOKELAU": "トケラウ",
    "TOKEN_VALUE": "トークン値",
    "TOKEN": "トークン",
    "TONGA_PACIFIC_TONGATAPU": "太平洋/トンガタプ島",
    "TONGA": "トンガ王国",
    "TOOLS": "ツール",
    "TOOLTIP_ACCOUNT_GROUP_DESCRIPTION": "グループに関する説明情報を入力します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_AUTH_DEVICES": "Executive Insightsアプリの使用を許可されている管理者のデバイスを表示します。管理者は最大5台のデバイスを登録できます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_COMMENTS": "(省略可)追加のメモまたは情報を入力します。コメントは10,240文字以内にしてください。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_CONFIRM_PASSWORD": "確認のためパスワードを再入力します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EMAIL": "管理者の有効な業務用アドレスを入力します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_EXEC_MOBILE_APP_ENABLE": "管理者がExecutive Insightsアプリにアクセスすることを許可します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_LOGIN_ID": "管理者がSSOプロバイダー ポータルからのログインに使用するログインIDを入力してください。適切なドメイン名を選択してください(Zscalerに提供したドメイン名がドロップダウン メニューに表示されます)。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_MOBILE_APP_ENABLE": "管理者がExecutive Insightsアプリにアクセスできるようにする場合は有効にします。この設定を有効にするには、管理者は[<b>組織</b>]スコープおよび[<b>Executive Insightsアプリのアクセス許可を有効にする</b>]が選択された[<b>管理者ロール</b>]が必要です。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_NAME": "管理者名を入力します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD_BASED_LOGIN": "管理者が管理ポータルに直接ログインすることを許可する場合に有効にします。この設定は、[<b>管理者用のSAML SSOの設定</b>]の有効化に加えて行うことができます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PASSWORD": "管理者のパスワードを入力します。8～100文字の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_PRODUCT_UPDATES": "サービスの重要な変更や更新に関するメールを管理者が受信できるようにする場合は有効にします。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_ROLE": "[{0}ロール{1}]を選択して管理ポータルへの管理者アクセスのレベルを指定します。構成したロールはドロップダウン メニューに表示されます。ロールを検索したり、[{2}追加{3}]アイコンをクリックして新しいロールを追加したりすることもできます。[{4}管理者ランク{5}]を有効にしている場合、選択できるロールは割り当てられた管理者ランクによって決まります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_DEPARTMENTS": "管理者が管理ポータルで管理できる部署を選択します。 ",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATION_GROUPS": "管理者が管理ポータルで管理できるロケーション グループを選択します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE_LOCATIONS": "管理者が管理ポータルで管理できるロケーションを選択します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SCOPE": "[{0}スコープ{1}]を選択して、管理者が管理ポータルで管理できる組織の領域を指定します。割り当てられたスコープによって、選択できるスコープが決まります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SECURITY_UPDATES": "組織に影響を与える可能性のある脆弱性や脅威に関するメールを管理者が受信できるようにする場合は有効にします。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_SERVICE_UPDATES": "新しいデータ センターの通知やクラウド リリース情報などを含む、新しいサービスや製品の機能強化に関するメールを管理者が受信できるようにする場合は有効にします。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_STATUS": "管理者を有効化または無効化します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ADMINISTRATORS_UNAUTH_DEVICE": "承認を解除します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_ALLOW_TO_CREATE_NEW_LOCATION": "ユーザーが新しいロケーションを作成することを許可します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_COMMENTS": "(省略可)追加のメモまたは情報を入力します。コメントは10,240文字以内にしてください。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_CONFIRM_PASSWORD": "確認のためパスワードを再入力します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_LOGIN_ID": "[<b>監査役</b>]のログインIDを入力して、適切なドメイン名を選択します(Zscalerに提供したドメイン名がドロップダウン メニューに表示されます)。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NAME": "[<b>監査役</b>]の名前を入力します。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_AUDITORS_NEW_PASSWORD": "監査役のパスワードを入力します。8～100文字の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_EMAIL": "パートナー管理者のメール アドレスを入力して、適切なドメイン名を選択します。Zscalerに提供したドメイン名がドロップダウン メニューに表示されます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_LOGIN_ID": "パートナー管理者がSSOプロバイダー ポータルへのログインに使用するログインIDを入力し、適切なドメイン名を選択します。Zscalerに提供したドメイン名がドロップダウン メニューに表示されます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_NAME": "パートナー管理者名を入力します",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_PASSWORD": "パートナー管理者が管理ポータルに直接ログインすることを許可する場合、パスワードを入力します。8～100文字の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。これは、パートナー管理者のSAMLシングル サインオンを有効にすることに加えて行うことができます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_PARTNER_ROLE": "[<b>パートナー ロール</b>]を選択して、管理ポータルへのパートナー管理者のアクセス レベルを指定します。構成したパートナー ロールはドロップダウン メニューに表示されます。ロールを検索したり、[<b>追加</b>] アイコンをクリックして新しいロールを追加したりすることもできます。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_DOWNLOAD_XML_METADATA": "[{0}ダウンロード{1}]をクリックして、ZscalerサービスのXMLメタデータをエクスポートします。Zscaler SAMLの機能の詳細を含むメタデータは自動構成に使用されます。一部の{2}IdPs{3}では、サービス プロバイダーの構成にメタデータが必要になります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ENABLE_SAML_AUTH": "管理者が{0}SSOプロバイダー ポータル{1}から管理ポータルに直接ログインすることを許可する場合に有効にします。組織で{2}IdP{3}(ADFSやOktaなど)がすでに構成されている必要があります。また、(自動プロビジョニングではなく)管理ポータルで{4}管理者アカウントを追加{5}する必要があります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_ISSUERS": "Zscalerサービスに関連付けられているIdP発行者を入力します(省略可)。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML_UPLOAD_SSL_CERTIFICATE": "[{0}アップロード{1}]をクリックして、IdPのデジタル署名の検証に使用するSSLパブリック証明書をアップロードします。これは、IdPからダウンロードしたPEM形式をbase-64でエンコードしたものです。ファイル拡張子は.pemまたは.cerで、ファイル名には他のピリオド(.)が含まれていない必要があります。",
    "TOOLTIP_ADMINISTRATION_ADMINISTRATOR_MANAGEMENT_SAML": "管理者がパスワードを使用してCloud Connector管理ポータルに直接ログインすることを許可する場合は有効にします。この認証方法はSAML SSOで使用できます。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_ADMIN_EDGE_CONNECTOR_TRAFFIC_FORWARDING_DNS": "管理者に付与する転送(トラフィック、DNSとログ)へのアクセス([フル]、[表示のみ]、[なし])を選択します",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT": " 管理者に付与する[APIキーの管理]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT": " 管理者に付与する[リモート アシスタンス管理]へのアクセス([フル]または[表示のみ])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT": " 管理者に付与する[管理コントロール]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING": " 管理者に付与する[Cloud Connectorのプロビジョニング]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD": " 管理者に付与する[ダッシュボード]へのアクセス([表示のみ]または[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_LOCATION_MANAGEMENT": " 管理者に付与する[ロケーションの管理]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION": " 管理者に付与する[NSS]へのアクセス([フル]または[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_POLICY_CONFIGURATION": " 管理者に付与する[ポリシーと管理]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT": " 管理者に付与する[パブリック クラウド構成管理]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE": " 管理者に付与する[ロケーションとプロビジョニング テンプレート]へのアクセス([フル]、[表示のみ]、[なし])を選択します。",
    "TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME": "{0}ロール{1}の名前を入力します。",
    "TOOLTIP_ALL_APP_SEGMENTS_ONLY": "ZPA Edge App Segmentは、[すべてのApp Segmentsに適用]が有効の場合にのみ使用できます。",
    "TOOLTIP_ALLOW_AWS_ACCOUNT_ID": "これを有効にすると、AWSアカウントIDをZscalerにインポートして、ポータルで表示できます",
    "TOOLTIP_ALLOW_AZURE_SUBSCRIPTION_ID": "これを有効にするとAzureサブスクリプションIDをZscalerにインポートして、ポータルで表示できます",
    "TOOLTIP_ALLOW_GCP_PROJECT_ID": "これを有効にすると、GCPプロジェクトIDをZscalerにインポートして、ポータルで表示できます",
    "TOOLTIP_AWS_CONFIG_NAME": "AWSアカウントの名前。",
    "TOOLTIP_AWS_GROUP_NAME": "このグループに使用する名前を入力します",
    "TOOLTIP_AWS_ROLE_NAME": "以前に入力したAWSアカウントのAWSロールの名前で、Zscalerが想定するものです。",
    "TOOLTIP_BC_BC_GROUP": "ブランチ プロビジョニング テンプレート用の既存のBranch Connectorグループを選択します。",
    "TOOLTIP_BC_COUNTRY": "新しいロケーションの国を選択します。",
    "TOOLTIP_BC_DNS_SERVER": "DNSサーバーIPアドレスを入力します。",
    "TOOLTIP_BC_FORWARDING_NET_MASK": "内部ゲートウェイIPアドレスのネットマスクを入力します。",
    "TOOLTIP_BC_GROUP_5G": "このコネクター グループは、この展開構成に関連付けられます。",
    "TOOLTIP_BC_GROUP_NAME": "追加する新しいBranch Connectorグループの名前を入力します。",
    "TOOLTIP_BC_HARDWARE_DEVICE": "Branch Connectorのハードウェア デバイスを選択します。",
    "TOOLTIP_BC_HYPERVISOR": "Branch Connectorのハイパーバイザーを選択します。",
    "TOOLTIP_BC_INTERNAL_GATEWAY_IP_ADDRESS": "内部ゲートウェイのIPアドレスを入力します。",
    "TOOLTIP_BC_IP_ADDRESS": "Branch ConnectorのIPアドレスを入力します。",
    "TOOLTIP_BC_LOAD_BALANCER_IP_ADDRESS": "ロード バランサーのIPアドレスを入力します。",
    "TOOLTIP_BC_LOCATION_NAME": "追加する新しいロケーションの名前を入力します。",
    "TOOLTIP_BC_LOCATION_TEMPLATE": "プロビジョニングURLのデフォルトまたは構成済みのロケーション テンプレートを選択します。",
    "TOOLTIP_BC_LOCATION": "プロビジョニングURLの既存のロケーションを選択します。",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_1": "プライマリーDNSサーバーのIPアドレスを入力します。これは、負荷分散に使用される2つのDNSサーバーの1つです。",
    "TOOLTIP_BC_MANAGEMENT_DNS_SERVER_IP_2": "セカンダリーDNSサーバーのIPアドレスを入力します。これは、負荷分散に使用される2つのDNSサーバーの1つです。",
    "TOOLTIP_BC_MANAGEMENT_NET_MASK": "Branch ConnectorのIPアドレスのネットマスクを入力します。",
    "TOOLTIP_BC_PROVISIONING_NAME": "ブランチ プロビジョニング テンプレートの名前を入力します。",
    "TOOLTIP_BC_SERVER_IP_ADDRESS": "サービスのIPアドレスを入力します。",
    "TOOLTIP_BC_VM_SIZE": "このフィールドはデフォルトで[小]に設定されています",
    "TOOLTIP_BLOCK_INTERNET_ACCESS": "有効にすると、ユーザーが利用規定に同意するまで、HTTP以外のトラフィックを含むインターネットへのすべてのアクセスが無効になります",
    "TOOLTIP_BW_DOWNLOAD": "ダウンロードの最大帯域幅制限を指定します(Mbps)。",
    "TOOLTIP_BW_UPLOAD": "アップロードの最大帯域幅制限を指定します(Mbps)。",
    "TOOLTIP_CC_ROLE_NAME": "Cloud Connectorのツールヒント テキスト：この名前は、そのアカウントのすべてのCloud Connectorに関連付ける必要があります",
    "TOOLTIP_CLOUD_COONECTOR_GROUP": "このアカウント グループに関連付けるCloud Connectorグループを選択します",
    "TOOLTIP_CLOUD_NSS_HTTP_HEADERS": "",
    "TOOLTIP_CLOUD_PROVIDER": "Cloud Connectorのクラウド プロバイダーを選択します。",
    "TOOLTIP_CPU": "ハイパーバイザーに推奨されるCPU。",
    "TOOLTIP_CUSTOM_AUP_FREQUENCY": "",
    "TOOLTIP_DEDICATED_BANDWIDTH": "これは、ZscalerクラウドのNanologからログをダウンロードするために必要なピーク帯域幅です。NSSに必要な帯域幅が割り当てられていない場合、ログがNanologに蓄積される可能性があります。その結果、接続が頻繁にリセットされ、ログがNSSにストリーミングされなくなる可能性があります。",
    "TOOLTIP_DEFAULT_GATEWAY_IP_ADDRESS": "デフォルト ゲートウェイのIPアドレスを入力します。",
    "TOOLTIP_DEFAULT_GATEWAY": "有効なデフォルト ゲートウェイのIPアドレスを入力します。",
    "TOOLTIP_DEFAULT_LEASE_TIME": "デフォルトのリース時間を秒単位で入力します。",
    "TOOLTIP_DEPLOY_AS_GATEWAY": "[はい]または[いいえ]を選択して、ハードウェア デバイスをゲートウェイとして展開するかどうかを決定します。",
    "TOOLTIP_DESCRIPTION": "(省略可)追加のメモまたは情報を入力します。",
    "TOOLTIP_DESTINATION_IP_ADDRESS": "IPアドレスを入力します。個々のIPアドレス、サブネット、またはアドレス範囲を入力できます。複数の項目を追加する場合は、各エントリーの後でEnterキーを押します。",
    "TOOLTIP_DESTINATION_IP_COUNTRIES": "サーバーのロケーションに基づいて宛先を識別するには、[任意]を選択してグループ内のすべての国を含めるか、特定の国を選択します。",
    "TOOLTIP_DESTINATION_IP_DOMAIN": "完全修飾ドメイン名(FQDN)またはワイルドカードFQDNを入力します。ワイルドカード文字にはドット(「.」)を使用します。複数の項目を追加する場合は、各エントリーの後でEnterキーを押します。",
    "TOOLTIP_DESTINATION_IP_FQDN": "完全修飾ドメイン名(FQDN)を入力します。複数の項目を追加する場合は、各エントリーの後でEnterキーを押します。",
    "TOOLTIP_DESTINATION_IP_NAME": "IPアドレス、サーバーが配置されている国、URLカテゴリーを指定して、ファイアウォール ルールで制御する宛先をグループ化します。",
    "TOOLTIP_DESTINATION_TYPE": "宛先グループのタイプを選択します。",
    "TOOLTIP_DEVICE_SN": "デバイスのシリアル番号を選択します。",
    "TOOLTIP_DHCP_OPTIONS": "DHCPの条件として、デフォルト ゲートウェイとドメイン名を作成できます。",
    "TOOLTIP_DHCP": "[有効]を選択してDNSサーバーの詳細を入力するか、[無効]を選択して動的ホスト構成プロトコル(DHCP)を無効にします。",
    "TOOLTIP_DISK_STORAGE": "組織のワークロードに推奨されるディスク ストレージを表示します。",
    "TOOLTIP_DNS_SERVER_IP_1": "プライマリーDNSサーバーのIPアドレスを入力します。",
    "TOOLTIP_DNS_SERVER_IP_2": "セカンダリーDNSサーバーのIPアドレスを入力します。",
    "TOOLTIP_DNS_SERVER": "有効なDNSサーバーのIPアドレスを入力します。",
    "TOOLTIP_DOMAIN_NAME": "有効なドメイン名を入力します。",
    "TOOLTIP_EBS_STORAGE": "推奨ストレージ",
    "TOOLTIP_EC2_INSTANCE_TYPE": "推奨されるEC2インスタンス タイプ",
    "TOOLTIP_EDIT_ORGANIZATION_API_KEY_NEW_KEY": "新しいAPIキーは、英数字(A～Z、a～z、0～9)で正確に12文字にする必要があります。",
    "TOOLTIP_ENABLE_AUP": "認証されていないトラフィックに対して利用規定を表示し、ユーザーに同意を求めるようにする場合は有効にします",
    "TOOLTIP_ENABLE_CAUTION": "認証されていないトラフィックに対して警告ポリシー アクションを施行し、エンド ユーザー通知を表示する場合は有効にします。無効にすると、アクションは許可ポリシーとして扱われます。",
    "TOOLTIP_ENABLE_IPS_CONTROL": "管理者がIPSコントロールにアクセスできるようにする場合は有効にします。",
    "TOOLTIP_ENABLE_SURROGATE_BROWSER": "IPとユーザーのマッピングが存在する場合に有効にすると、既知のブラウザーからのトラフィックにもサロゲート ユーザーIDが使用されます。無効にすると、既知のブラウザーからのトラフィックは常に構成された認証メカニズムを使用してチャレンジされ、サロゲート ユーザーIDは無視されます。",
    "TOOLTIP_ENABLE_SURROGATE_REFRESH_TIME": "これは、ブラウザーの表示を更新して、構成された認証メカニズムでサロゲート ユーザーIDが再検証されるまでに既知のブラウザーからのトラフィックにサロゲート ユーザーIDを使用できる時間の長さです。{0}{1}注記：IPサロゲートの再検証のための更新時間は、DHCPリース時間よりも短くする必要があります。短くしない場合、間違ったユーザー ポリシーが適用されることがあります。",
    "TOOLTIP_ENABLE_SURROGATE": "内部IPアドレスをパブリックIPアドレスと区別できる場合に、ユーザーとデバイス間のマッピングを有効にします。これは、Cookieと互換性のないトラフィックにユーザー ポリシーを施行するために使用されます。詳細については、ヘルプを参照してください。",
    "TOOLTIP_ENABLE_XFF_FORWARDING": "オンプレミスのプロキシ サーバーがアウトバウンドHTTPリクエストに挿入するX-Forwarded-For (XFF)ヘッダーをZscalerサービスで使用する場合、クライアント リクエストからのXFFを有効にします。サービスがトラフィックを宛先に転送すると、この元のXFFヘッダーが削除され、クライアント ゲートウェイのIPアドレス(組織のパブリックIPアドレス)を含むXFFヘッダーに置き換えられます。これにより、組織の内部IPアドレスが外部に公開されなくなります。",
    "TOOLTIP_ENFORCE_AUTHENTICATION": "構成済みのユーザー認証メカニズムを適用して、個々のユーザー トラフィックの識別を強制する場合は、認証を有効にします。",
    "TOOLTIP_ENFORCE_BAND_WIDTH_CONTROL": "[有効化]を選択して、ロケーションの帯域幅コントロールを適用します。",
    "TOOLTIP_ENFORCE_FIREWALL_CONTROL": "[ファイアウォール コントロールを強制]を選択して、ロケーションのファイアウォールを有効にします。",
    "TOOLTIP_ENTER_AWS_ACCOUNT_ID": "ワークロードが展開されるAWSアカウントID。",
    "TOOLTIP_EVENT_BUS_NAME": "イベントブリッジ イベント バスは、Zscalerイベント バスにリアルタイム通知を送信するために使用されます。イベントブリッジのルールを使用して、リアルタイムのリソース変更通知をZscalerに送信できます。これは、ポリシーのリアルタイム更新を有効にするために必要です。ZscalerはVMの作成に関する通知を受け取ります。",
    "TOOLTIP_EXTERNAL_ID": "Zscalerは、タグ情報を取得する際にAWSへのAPIコールでこの外部IDを使用します。外部IDは作成されたアカウントごとに一意です。このIDは、AWS IAMロール構成に追加する必要があります。このIDを再生成する場合は、AWSアカウントでも更新してください。",
    "TOOLTIP_FAILURE_BEHAVIOR": "失敗時の動作を選択してください。",
    "TOOLTIP_FORCE_SSL_INTERCEPTION": "SSLインターセプトがHTTPSトラフィックに対して利用規定を施行できるようにする場合は有効にします",
    "TOOLTIP_GATEWAY_FAIL_CLOSE": "このオプションでは、このゲートウェイで定義されているプライマリー プロキシとセカンダリー プロキシの両方が到達できない場合にトラフィックをどのように処理するかを指定します。このオプションを有効にするとトラフィックはドロップされ、無効にすると許可されます。デフォルトでは、有効に設定されています。",
    "TOOLTIP_GATEWAY_NAME": "サードパーティーのプロキシ サービス用に作成するゲートウェイの名前を入力します。",
    "TOOLTIP_GENERAL_DESCRIPTION": "(省略可)追加のメモまたは情報を入力します。説明は10,240文字以内にしてください。",
    "TOOLTIP_HA_ID": "HAデバイスのIDを入力します。",
    "TOOLTIP_HELP_BLACKLISTED_IP_COMMENTS": "このフィールドには、入力されたIPアドレスに関するコメントが表示される場合があります。 ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_CHECK": "IPアドレスを入力して、拒否リストに登録されているかどうかを確認できます。 ",
    "TOOLTIP_HELP_BLACKLISTED_IP_IP_ADDRESS_RESULTS": "このフィールドは、入力されたIPアドレスが拒否リストに登録されているかどうかを示します。 ",
    "TOOLTIP_HELP_ENABLE_FULL_ACCESS_REMOTE_ASSISTANCE": "Zscalerサポートのエンジニアが管理者のフルアクセス許可で管理ポータルにリモートでログインすることを許可します。アクセスを有効にするためにアカウントを作成したり、パスワードを共有したりする必要はありません。",
    "TOOLTIP_HELP_ENABLE_VIEW_ONLY_REMOTE_ASSISTANCE": "承認されたZscalerの従業員が表示専用アクセスで管理ポータルにアクセスすることを許可します。ポータルへのアクセスは、カスタマー サクセス コンテンツとレポートの作成、リモート アシスタンスの提供、レポートと構成の表示に使用され、Zscalerの製品とサービスの向上に役立ちます。",
    "TOOLTIP_HELP_LOOKUP_URL_ENTER_URL": "URLが属するカテゴリーを検索するには、URLを入力して[{0}URLのルックアップ{1}]をクリックします。サービスは、URLの定義済みカテゴリーまたはスーパーカテゴリー{2}を表示し、URLに関連付けられているセキュリティ アラートがあるかどうかを示します。",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_SECURITY_ALERT": "このフィールドは、URLに関連付けられているセキュリティ アラートがあるかどうかを示します。",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL_CLASSIFICATIONS": "このフィールドには、URLが属する{0}定義済みのカテゴリーまたはスーパーカテゴリー{1}が表示されます。 ",
    "TOOLTIP_HELP_LOOKUP_URL_RESULTS_URL": "このフィールドには、検索したURLが表示されます。",
    "TOOLTIP_HELP_REMOTE_FULL_ACCESS_ENABLED_UNTIL": "少なくとも2日間はアクセスを許可することをお勧めします。",
    "TOOLTIP_HELP_REMOTE_VIEW_ONLY_ACCESS_ENABLED_UNTIL": "少なくとも1年間はアクセスを許可することをお勧めします。",
    "TOOLTIP_HW_DEVICE_NAME": "テンプレートの名前を入力します。",
    "TOOLTIP_HW_SUBINTERFACE_SHUTDOWN": "アップリンク モードとして[アクティブ]または[スタンバイ]を選択します。",
    "TOOLTIP_HW_VLAN_ID": "VLANのIDを入力します。",
    "TOOLTIP_HW_VLAN": "仮想ローカル エリア ネットワーク(VLAN)の[タグ付き]または[タグなし]を選択します。",
    "TOOLTIP_INTERFACE_SHUTDOWN": "[はい]または[いいえ]を選択して、インターフェイスのシャットダウン動作を決定します。",
    "TOOLTIP_IP_ADDRESS_RANGE": "デバイスのIPアドレス範囲を入力します。",
    "TOOLTIP_IP_ADDRESS_WITH_NETMASK": "IPアドレスを「a.b.c.d/マスク」の形式で入力します",
    "TOOLTIP_IP_POOL_NAME": "IPプールの名前。",
    "TOOLTIP_LIST_CUSTOM_OPTION_CODE": "IANAで定義されている有効なDHCPオプション コードを入力してください。定義済みオプションは許可されていません",
    "TOOLTIP_LIST_CUSTOM_OPTION_NAME": "このカスタム オプションの名前を入力してください - 解析されません",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_IP": "IPアドレスのデータ タイプは、明示的なIPアドレスとして入力する必要があります。最大4つのIPアドレスをコンマで区切って定義できます。\n例：*************、************",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE_STRING": "文字列データ タイプは、NVT ASCII形式の文字列または16進数で指定されたオクテット列のいずれかを指定します。\n例：'********:/var/tmp/rootfs'または43:4c:49:45:54:2d:46:4f:4f",
    "TOOLTIP_LIST_CUSTOM_OPTION_TYPE": "IPまたは文字列のいずれかのタイプを選択してください",
    "TOOLTIP_LIST_CUSTOM_OPTION": "[カスタムDHCPオプション]では、ドロップダウン リストに事前定義されていないDHCPオプションを設定できます。",
    "TOOLTIP_LIST_DNS_SERVER": "有効なDNSサーバーのIPアドレスを1つまたはリスト形式で入力します。",
    "TOOLTIP_LIST_DOMAIN_NAME": "有効なドメイン名を1つまたはリスト形式で入力します。",
    "TOOLTIP_LOCATION_CREATION": "このフィールドは、デフォルトで[自動]に設定されています。",
    "TOOLTIP_LOCATION_TEMPLATE_NAME": "追加するロケーション テンプレートの名前を入力します。",
    "TOOLTIP_LOCATION_TEMPLATE": "プロビジョニングURLのデフォルトまたは構成済みのロケーション テンプレートを選択します。",
    "TOOLTIP_LOCATIONS_CUSTOM_AUP_FREQUENCY_TEXT": "利用規定がユーザーに表示される頻度(日数)を入力します",
    "TOOLTIP_LOCATIONS_ENFORCE_SURROGATE_IP_FOR_KNOWN_BROWSERS": "IPとユーザーのマッピングが存在する場合に有効にすると、既知のブラウザーからのトラフィックにもサロゲート ユーザーIDが使用されます。無効にすると、既知のブラウザーからのトラフィックは常に構成された認証メカニズムを使用してチャレンジされ、サロゲート ユーザーIDは無視されます。",
    "TOOLTIP_LOCATIONS_IDLE_TIME_DISASSOCIATION": "IPサロゲートを有効にした場合、[関連付け解除までのアイドル時間]で、トランザクションの完了後にサービスがIPアドレスとユーザーのマッピングを保持する時間を指定します。",
    "TOOLTIP_MAX_LEASE_TIME": "最大リース時間を秒単位で入力します。",
    "TOOLTIP_MTU_1500": "バイトの最大伝送単位(MTU)。",
    "TOOLTIP_MTU": "バイトの最大伝送単位(MTU)。デフォルトは1400に設定されています",
    "TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD": "有効にすると、ダッシュボードは15分ごとに自動的に更新されます。",
    "TOOLTIP_MY_PROFILE_CONFIRM_NEW_PASSWORD": "新しいパスワードを再入力します。これは、[{0}新しいパスワード{1}]フィールドに入力したパスワードと同じである必要があります。",
    "TOOLTIP_MY_PROFILE_LANGUAGE": "管理ポータルは、デフォルトで英語表示に設定されています。スペイン語、フランス語、繁体字中国語、日本語を選択することもできます。",
    "TOOLTIP_MY_PROFILE_NEW_PASSWORD": "新しいパスワードを入力します。8文字以上の長さで、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。使用できるのはASCII文字のみです。",
    "TOOLTIP_MY_PROFILE_OLD_PASSWORD": "現在のパスワードを入力します。",
    "TOOLTIP_MY_PROFILE_PASSWORD": "パスワードは8文字以上で、数字、大文字、特殊文字をそれぞれ1つ以上含める必要があります。使用できるのはASCII文字のみです。",
    "TOOLTIP_MY_PROFILE_POLICY_INFORMATION": "有効にするとポリシー情報が表示されます。",
    "TOOLTIP_MY_PROFILE_TIMEZONE": "サービスでは、トランザクションを保存するときにUTCが使用されます。ログを表示するときは、指定されたタイム ゾーンが使用されます。",
    "TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME": "管理者アカウントの作成時に割り当てられた管理者のログイン ID。",
    "TOOLTIP_NETWORK_SERVICE_GROUP_DESCRIPTION": "(省略可)追加のメモまたは情報を入力します。説明は10,240文字以内にしてください。",
    "TOOLTIP_NETWORK_SERVICE_GROUP_NAME": "{0}ネットワーク サービス グループ{1}の名前を入力します。文字とスペースを含めることができます。",
    "TOOLTIP_NETWORK_SERVICE_GROUP_SERVICES": "グループに含めるカスタムおよび定義済みのサービスを選択します。いくつでも選択できます。",
    "TOOLTIP_NETWORK_SERVICES_DEFINITION": "管理者が定義したサービスである場合、[{0}カスタム{1}]が表示されます。",
    "TOOLTIP_NETWORK_SERVICES_DESCRIPTION": "(省略可)追加のメモまたは情報を入力します。説明は10,240文字以内にしてください。",
    "TOOLTIP_NETWORK_SERVICES_NAME": "制御する{0}アプリケーション レイヤー サービス{1}の名前を入力します。文字とスペースを含めることができます。",
    "TOOLTIP_NETWORK_SERVICES_SCTP_DESTINATION_PORTS": "ネットワーク サービスによって使用されるSCTP宛先ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NETWORK_SERVICES_SCTP_SOURCE_PORTS": "ネットワーク サービスによって使用されるSCTP送信元ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NETWORK_SERVICES_TCP_DESTINATION_PORTS": "ネットワーク サービスによって使用されるTCP宛先ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NETWORK_SERVICES_TCP_SOURCE_PORTS": "ネットワーク サービスによって使用されるTCP送信元ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NETWORK_SERVICES_UDP_DESTINATION_PORTS": "ネットワーク サービスによって使用されるUDP宛先ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NETWORK_SERVICES_UDP_SOURCE_PORTS": "ネットワーク サービスによって使用されるUDP送信元ポート番号(50など)またはポート番号の範囲(1000～1050など)。",
    "TOOLTIP_NSS_CLOUD_FEED_API_URL": "SIEMログ収集APIエンドポイントのHTTPS URL。",
    "TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL": "Azureで生成されたディレクトリー(テナント) IDを持つ承認URLを入力します",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID": "AWSで作成したユーザーのアクセス キーIDを入力します",
    "TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY": "AWSで作成したユーザーのシークレット アクセス キーを入力します",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID": "Azureで生成されたアプリケーション(クライアント) IDを入力します",
    "TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET": "Azureで生成されたアプリケーション クライアント シークレット値を入力します",
    "TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE": "次の文字列を入力します：client_credentials",
    "TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION": "JSON配列形式でログをストリーミングします(例：[{JSON1},{JSON2}])",
    "TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "個々のHTTPリクエスト ペイロードのサイズをSIEMのベスト プラクティスに制限します",
    "TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION": "デフォルトで有効になっており、編集できません",
    "TOOLTIP_NSS_CLOUD_FEED_SCOPE": "次の文字列を入力します：https://monitor.azure.com//.default",
    "TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE": "クラウドベースのSIEMを選択します",
    "TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL": "S3バケットに作成したフォルダーのURLを入力します",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR_GROUPS": "このフィルターを使用して、ログを特定のCloud/Branch Connectorグループに制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_BRANCH_CONNECTOR": "このフィルターを使用して、ログを特定のCloud/Branch Connectorに制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_CLIENT_SOURCE_IPS": "このフィルターを使用して、クライアントのプライベートIPアドレスに基づいてログを制限します。以下を入力できます。{0}**************などのIPアドレス{1}*********-*********0などのIPアドレスの範囲{2}***********/24などのネットマスク付きのIPアドレス{3}各入力の後にEnterキーを押します。{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_REQUEST_TYPES": "このフィルターを使用して、特定のDNSリクエスト タイプに関連付けられたセッションにログを制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_CODES": "このフィルターを使用して、特定のDNSレスポンス コードに関連付けられたセッションにログを制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSE_TYPES": "このフィルターを使用して、特定のDNSレスポンス コードに関連付けられたセッションにログを制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DNS_RESPONSES": "DNSレスポンスの特定のデータを含むセッションにログを制限します。ドメイン名、IPv4アドレス、およびIPv6アドレスを指定できます。IPv4アドレスの場合は、IPアドレス、IPアドレスの範囲、またはネットマスク付きのIPアドレスを入力できます。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DOMAINS": "このフィルターを使用して、特定のドメインに関連付けられたセッションにログを制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_DURATIONS": "このフィルターを使用して、セッションの期間(秒)に基づいてログを制限します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_LOCATIONS": "このフィルターを使用して、トランザクションが生成された特定のロケーションにログを制限します。ロケーションは検索可能です。選択できるロケーションの数に制限はありません。選択後に検出されたロケーションには取り消し線が表示されます。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_POLICY_ACTION": "特定のDNSポリシー アクションに基づいてログを制限します",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_RULE_NAME": "このフィルターを使用して、DNS制御ポリシーの特定のルールに基づいてログを制限します。リストからルールを選択します。",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_ADDRESS": "このフィルターを使用して、特定のサーバーIPアドレスにログを制限します。以下を入力できます。{0}**************などのIPアドレス{1}*********-*********0などのIPアドレスの範囲{2}***********/24などのネットマスク付きのIPアドレス{3}各入力の後にEnterキーを押します。{4}",
    "TOOLTIP_NSS_FEED_DNS_FILTERS_SERVICE_IP_PORTS": "このフィルターを使用して、特定のサーバー ポートにログを制限します。個々のポートおよびポートの範囲を入力できます。",
    "TOOLTIP_NSS_FEED_DUPLICATE_LOGS": "ダウンタイム中にログがスキップされないようにするには、NSSが重複ログを送信する時間(分)を指定します。",
    "TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE": "メトリクス レコード タイプに基づいてログを制限します",
    "TOOLTIP_NSS_FEED_ESCAPE_CHARACTER": "URL、ホスト、またはリンク元に表示されたときに16進エンコードする文字を入力します(省略可)。たとえば、コンマ「,」を入力すると%2Cとしてエンコードされます。これは、この文字を区切り文字として使用し、誤った区切りが生じないようにする場合に便利です。レコードに対してカスタム エンコードが行われた場合、そのレコードの{eedone}フィールドは「Yes」になります。",
    "TOOLTIP_NSS_FEED_LOG_TYPE": "ストリーミングするログの種類を選択します。",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_BRANCH_CONNECTOR_VM": "このフィルターを使用して、ログを特定のCloud/Branch Connectorに制限します。",
    "TOOLTIP_NSS_FEED_METRICS_AND_EVENT_FILTERS_CLOUD_CONNECTOR_VM": "このフィルターを使用して、ログを特定のBranch Connector VMに制限します。",
    "TOOLTIP_NSS_FEED_NAME": "各フィードは、NSSとSIEM間の接続です。フィードの名前を入力します。",
    "TOOLTIP_NSS_FEED_OUTPUT_FORMAT": "これらは、出力に表示されるフィールドです。デフォルトのリストを編集できます。[フィールド出力タイプ]に[カスタム]を選択した場合は、区切り文字も変更できます。使用可能なフィールドとその構文については、NSSフィード出力形式を参照してください。",
    "TOOLTIP_NSS_FEED_OUTPUT_TYPE": "デフォルトでは、出力はコンマ区切り(CSV)リストです。タブ区切りリストを作成するには、[タブ区切り]を選択します。ダッシュなどの別の区切り文字を使用するには、[カスタム]を選択し、フィード出力タイプを指定するときに区切り文字を入力します。ここには、特定のSIEMのフィード出力タイプもリストされます。",
    "TOOLTIP_NSS_FEED_SERVER": "リストからNSSを選択します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR_GROUPS": "このフィルターを使用して、ログを特定のCloud/Branch Connectorグループに制限します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_BRANCH_CONNECTOR": "このフィルターを使用して、ログを特定のCloud/Branch Connectorに制限します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IP": "",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_CLIENT_SOURCE_IPS": "このフィルターを使用して、クライアントのプライベートIPアドレスに基づいてログを制限します。以下を入力できます。{0}**************などのIPアドレス{1}*********-*********0などのIPアドレスの範囲{2}***********/24などのネットマスク付きのIPアドレス{3}各入力の後にEnterキーを押します。{4}",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_GATEWAY": "このフィルターを使用して、特定のゲートウェイにログを制限します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_LOCATIONS": "このフィルターを使用して、トランザクションが生成された特定のロケーションにログを制限します。ロケーションは検索可能です。選択できるロケーションの数に制限はありません。選択後に検出されたロケーションには取り消し線が表示されます。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_NETWORK_SERVICES": "[任意]を選択してNSSフィードをすべてのネットワーク サービスに適用するか、特定のネットワーク サービスを選択します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION": "このフィルターを使用して、転送制御ポリシーのルールに従ってサービスで実行されたアクションに基づいてログを制限します。",
    "TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME": "このフィルターを使用して、転送制御ポリシーの特定のルールに基づいてログを制限します。リストからルールを選択します。",
    "TOOLTIP_NSS_FEED_SESSION_LOG_TYPE": "セッション ログ タイプを選択します",
    "TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE": "ログのストリーミング先のSIEMの宛先タイプとして、IPアドレスまたはFQDNのいずれかを選択します。",
    "TOOLTIP_NSS_FEED_SIEM_FQDN": "ログのストリーミング先のSIEMのFQDNを入力します。SIEMがNSSからのフィードを受け入れるように構成されていることを確認してください。",
    "TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS": "ログのストリーミング先のSIEMのIPアドレスを入力します。SIEMがNSSからのフィードを受け入れるように構成されていることを確認してください。",
    "TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT": "SIEMにストリーミングする1秒あたりのイベントの適切なレート制限を入力します。トラフィック量に対して制限が小さすぎると、ログの損失が発生します。",
    "TOOLTIP_NSS_FEED_SIEM_RATE": "SIEMライセンスなどの制約で出力ストリームのスロットリングを行う必要がある場合を除き、[無制限]のままにします。",
    "TOOLTIP_NSS_FEED_SIEM_TCP_PORT": "ログのストリーミング先のSIEMのポート番号を入力します。SIEMがNSSからのフィードを受け入れるように構成されていることを確認してください。",
    "TOOLTIP_NSS_FEED_STATUS": "NSSフィードはデフォルトで有効になっています。後でアクティブ化する場合は、[無効]を選択します。",
    "TOOLTIP_NSS_FEED_TIMEZONE": "デフォルトでは、これは組織のタイム ゾーンです。設定したタイム ゾーンは、出力ファイルの時間フィールドに適用されます。タイ ムゾーンは、特定のタイム ゾーンの夏時間に合わせて自動的に調整されます。構成したタイム ゾーンは、個別のフィールドとしてログに出力できます。タイム ゾーンのリストは、IANA Time Zoneデータベースから取得されます。GMTオフセットを直接指定することもできます。",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS": "ユーザー数を指定します(省略可)",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR": "このデータは、DNS概要ダッシュボードで取得できます。これは、VMの仕様を組織のワークロードに合わせて微調整する場合に推奨されます。",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR": "このデータを取得するには、[ファイアウォールの概要]ダッシュボードに移動します。これは、VMの仕様を組織のワークロードに合わせて微調整する場合に推奨されます。",
    "TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM": "NSSの展開に使用するプラットフォームを示します。",
    "TOOLTIP_NSS_SERVER_NAME": "NSSサーバーの名前を入力します。",
    "TOOLTIP_NSS_SERVER_SSL_CERTIFICATE": "",
    "TOOLTIP_NSS_SERVER_STATE": "NSSサーバーの正常性。",
    "TOOLTIP_NSS_SERVER_STATUS": "NSSはデフォルトで有効になっています。",
    "TOOLTIP_NSS_TYPE": "このフィールドは読み取り専用です。",
    "TOOLTIP_NSS_VIRTUAL_MACHINE": "クリックしてNSS OVAファイルをダウンロードします。",
    "TOOLTIP_NUMBER_OF_CORES": "推奨されるハイパーバイザーのコアの数。",
    "TOOLTIP_PASSPHRASE": "デバイスのパスフレーズを入力します。",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRATION_EC": "ZIA、Cloud Connector、ZDXの管理ポータルにログインするすべての管理者のパスワードに有効期限を設定する場合は有効にします。無効にした場合、パスワードの有効期限は無期限に設定されます。",
    "TOOLTIP_PASSWORD_EXPIRY_EXPIRES_AFTER": "ZIA管理者およびZDX管理者のパスワードの有効期限(日)を入力します。有効な範囲は15～365です。",
    "TOOLTIP_PEER_DHCP": "ピアDHCPのIPアドレスを入力します。",
    "TOOLTIP_POLICY_APP_SEGMENT_GROUP": "トラフィック転送ルールに適用するセグメント グループを最大600個まで選択します。グループが選択されていない場合、ルールはどのグループにも適用されません",
    "TOOLTIP_POLICY_APP_SEGMENT": "トラフィック転送ルールに適用するアプリケーション セグメントを最大50kまで選択します。セグメントが選択されていない場合、ルールはどのセグメントにも適用されません。",
    "TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS": "任意の数の定義済みまたはカスタムのネットワーク サービス グループを選択します。ネットワーク サービス グループが選択されていない場合、ルールはすべてのネットワーク サービス グループに適用されます。",
    "TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES": "ワイルドカードと完全修飾ドメイン名(FQDN)を入力します。複数の項目を追加する場合は、各エントリーの後でEnterキーを押します。",
    "TOOLTIP_POLICY_DNS_DESTINATION_GROUPS": "任意の数の宛先グループを選択します。宛先グループが選択されていない場合、ルールはすべての宛先グループに適用されます。",
    "TOOLTIP_POLICY_DNS_GATEWAY": "DNSゲートウェイを選択します。",
    "TOOLTIP_POLICY_DNS_RULE_ORDER": "ポリシー ルールは数値の昇順で評価され(ルール1の次はルール2など)、[ルールの順序]はこのルールの順序を反映します。",
    "TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT": "この設定を有効にすると、既存および今後作成されるすべてのApp Segmentにトラフィック転送ルールが適用されます。特定のApplication Segmentまたはセグメント グループを選択するには、この設定を無効化します。",
    "TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC": "最大32個のグループを選択できます。グループが選択されていない場合、ルールはすべてのグループに適用されます",
    "TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES": "任意の数のネットワーク サービスを選択します。ネットワーク サービスが選択されていない場合、ルールはすべてのネットワーク サービスに適用されます。Zscalerファイアウォールには事前定義されたサービスがあり、最大1,024の追加のカスタム サービスを構成できます。",
    "TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC": "次から選択します。[{0}許可{1}]: DNSリクエストとレスポンスを許可します。[{2}ブロック{3}]:すべてのDNSリクエストとレスポンスをサイレントにブロックします。[{4}ZPAによる解決{5}]: IPプールを使用してDNSリクエストを解決するようにCloud Connectorにリクエストします。このオプションを使用するには、IPプールが使用可能である必要があります。[{6}リダイレクト リクエスト{7}]:すべてのDNSリクエストとレスポンスをDNSゲートウェイにリダイレクトします。このオプションを使用するには、DNSゲートウェイが使用可能である必要があります。{8}",
    "TOOLTIP_POLICY_FIREWALL_DESCRIPTION": "(省略可)追加のメモまたは情報を入力します。説明は10,240文字以内にしてください。",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY": "サーバーの場所に基づいて宛先を識別するには、任意の数の国を選択します。国が選択されていない場合、ルールはすべての国に適用されます。",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES": "ドメインに複数の宛先IPアドレスがある場合、またはそのIPアドレスが変更される可能性がある場合は、IPアドレスと完全修飾ドメイン名(FQDN)を入力します。IPアドレスには、個々のIPアドレス、サブネット、またはアドレス範囲を入力できます。複数の項目を追加する場合は、各エントリーの後でEnterキーを押します。",
    "TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS": "任意の数の宛先IPアドレス グループを選択します。宛先IPアドレス グループが選択されていない場合、ルールはすべての宛先IPアドレス グループに適用されます。",
    "TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD": "このルールに、次のいずれかの転送方法を選択します。[{0}ダイレクト{1}]: Zscaler Internet Access (ZIA)またはZscaler Private Access (ZPA)をバイパスし、ZscalerサービスのIPアドレスを使用してトラフィックを宛先サーバーに直接転送します。[{2}SCTP変換を使用したダイレクト{3}]: UDP経由でSCTPトラフィックをトンネリングしながら、トラフィックを宛先に直接転送します(逆も同様)。[{4}ZIA{5}]: ZIAゲートウェイ経由でZIAにトラフィックを転送します。[{6}ZPA{7}]: ZPAクラウド経由でZPAにトラフィックを転送します。[{8}SCTP変換を使用したZPA{9}]: SCTPトラフィックをUDPでトンネリングしながら、Zscaler Private Access (ZPA)にZPAクラウド経由でトラフィックを転送します(逆も同様)。[{0}ドロップ{1}]:トラフィック転送ルールに一致するすべてのパケットを破棄します。{2}",
    "TOOLTIP_POLICY_FIREWALL_GATEWAY": "ゲートウェイを選択",
    "TOOLTIP_POLICY_FIREWALL_IPPOOL": "IPプールを選択",
    "TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP": "最大32のロケーション グループを選択できます。ロケーション グループが選択されていない場合、ルールはすべてのロケーション グループに適用されます。",
    "TOOLTIP_POLICY_FIREWALL_LOCATION": "最大8のロケーションを選択できます。ロケーションが選択されていない場合、ルールはすべてのロケーションに適用されます。",
    "TOOLTIP_POLICY_FIREWALL_RULE_MSFT_OFFICE_365": "このルールは、クラウド ファイアウォール製品のすべてのOffice 365トラフィックに対してローカル ブレーク アウトを許可するために[Microsoftが推奨するOffice 365のワン クリック構成]を有効にした場合に自動的に作成されます。",
    "TOOLTIP_POLICY_FIREWALL_RULE_NAME": "DNSは、変更可能なルール名を自動的に作成します。最大長は31文字です。",
    "TOOLTIP_POLICY_FIREWALL_RULE_ORDER": "ポリシー ルールは数値の昇順で評価され(ルール1の次はルール2など)、[ルールの順序]はこのルールの順序を反映します。",
    "TOOLTIP_POLICY_FIREWALL_RULE_STATUS": "有効化されたルールがアクティブに施行されます。無効化されたルールはアクティブに施行されませんが、[ルールの順序]での位置は保持されます。サービスではスキップされ、次のルールが適用されます。",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER_WARNING": "このルールは、トラフィックをZPAにリダイレクトするために自動的に作成されます。ZPAの定義済みDNSルールは、順序1または2のいずれかに移動することが推奨されます",
    "TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER": "このルールは、トラフィックをZPAにリダイレクトするために自動的に作成されます",
    "TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS": "任意の数の送信元IPアドレス グループを選択します。送信元IPアドレス グループが選択されていない場合、ルールはすべての送信元IPアドレス グループに適用されます。",
    "TOOLTIP_POLICY_FIREWALL_UCAAS": "このルールは、1つまたはすべてのUCaaSアプリでクラウド ファイアウォールからのトラフィックを許可すると自動的に作成されます",
    "TOOLTIP_PORT_NO": "ドロップダウン メニューからポート番号を選択します。",
    "TOOLTIP_PRIMARY_DNS_SERVER": "プライマリーDNSサーバーのIPアドレスを入力します。",
    "TOOLTIP_PRIMARY_PROXY": "ゲートウェイのプライマリー プロキシを選択します。",
    "TOOLTIP_PROV_TEMPLATE_NAME": "クラウド プロビジョニング テンプレートの名前を入力します。",
    "TOOLTIP_REGION": "Zscalerでタグを検出するリージョンを選択します。Zscalerはリージョン レベルでタグを検出します。",
    "TOOLTIP_SECONDARY_DNS_SERVER": "セカンダリーDNSサーバーのIPアドレスを入力します。",
    "TOOLTIP_SECONDARY_PROXY": "ゲートウェイのセカンダリー プロキシを選択します。これは、プライマリー プロキシが到達できない場合に使用されます。",
    "TOOLTIP_SESSIONS_ACROSS_SERVICES": "過去24時間にすべてのCloud/Branch Connectorでログに記録されたセッションの合計数",
    "TOOLTIP_SHUTDOWN": "[はい]または[いいえ]を選択して、シャットダウン動作を決定します。",
    "TOOLTIP_SOURCE_IP_ADDRESSES": "任意の数のIPアドレスを入力します。以下を入力できます。{0}**************などのIPアドレス{1}*********-*********0などのIPアドレスの範囲{2}***********/24などのネットマスク付きのIPアドレス{3}各エントリーの後で{4}Enter{5}キーを押します。",
    "TOOLTIP_SOURCE_IP_GROUP_NAME": "送信元IPアドレス グループの名前(ソーシャル メディアなど)。送信元IPをグループ化すると、ファイアウォール ポリシーでの更新が容易になります。",
    "TOOLTIP_SOURCE_IP_NAME": "送信元IPの名前",
    "TOOLTIP_STATIC_LEASE": "DHCP用のMACアドレスとIPアドレスを入力します。",
    "TOOLTIP_STATIC_ROUTE": "ルートとゲートウェイの詳細を入力します。",
    "TOOLTIP_SUBSCRIPTIONS": "グループ化するサブスクリプションを選択します。",
    "TOOLTIP_TEMPLATE_PREFIX": "ロケーション テンプレート名のプレフィックスを入力します。",
    "TOOLTIP_THROUGHPUT_ACROSS_DIRECT": "平均ダイレクト トラフィック スループット使用率(kbps)\n\n過去24時間のCloud/Branch Connectorごとにログに記録されたセッションの合計数",
    "TOOLTIP_THROUGHPUT_ACROSS_SERVICES": "過去24時間のすべてのCloud/Branch Connectorの平均トラフィック スループット使用率",
    "TOOLTIP_THROUGHPUT_ACROSS_ZIA": "平均ZIAトラフィック スループット使用率(kbps)\n\n過去24時間のCloud/Branch Connectorごとにログに記録されたセッションの合計数",
    "TOOLTIP_THROUGHPUT_ACROSS_ZPA": "平均ZPAトラフィック スループット使用率(kbps)\n\n過去24時間のCloud/Branch Connectorごとにログに記録されたセッションの合計数",
    "TOOLTIP_TRAFFIC_DISTRIBUTION": "[バランス]または[最適なリンク]を選択して、トラフィックの分散方法を指定します。",
    "TOOLTIP_UPGRADE_WINDOW": "アップグレードは、サービスに影響を与えることなく段階的に行われます",
    "TOOLTIP_USE_WAN_DNS_SERVER": "WAN DNSサーバーを使用する場合は[はい]を、LAN DNSサーバーの詳細を手動で入力する場合は[いいえ]を選択します。 ",
    "TOOLTIP_VDI_AGENT_DESCRIPTION": "エージェントを識別するための情報を入力します。",
    "TOOLTIP_VDI_AGENT_PROFILE_NAME": "追加するVDIプロファイルの名前を入力します。",
    "TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE": "認証タイプとして[IdP]または[ホスト型DB]を選択します。",
    "TOOLTIP_VDI_FORWRDING_PROFILE_IPS": "このフィルターを使用して、クライアントのプライベートIPアドレスに基づいてログを制限します。以下の形式で入力できます。{0}IPアドレス(例：**************){1}IPアドレス:ポート範囲(例：*********:80または********* (すべての範囲)){3}IPアドレス:ポート:プロトコル(例：*********:80:TCP、*********:80-100:UDP、または*********:80 (すべてのプロトコル)){4}各エントリーを入力した後にEnterキーを押します。",
    "TOOLTIP_VDI_GROUP_DESCRIPTION": "グループとその目的を識別するための情報を入力します",
    "TOOLTIP_VDI_GROUP_NAME": "VDIグループの名前。",
    "TOOLTIP_VDI_HOSTNAME_PREFIX": "VDIデバイスのグループ化に使用するホスト名プレフィックス。これは、VDIのZscaler Client Connectorによって検出されたホスト名です。",
    "TOOLTIP_VDI_LOCATION": "このVDIグループに関連付けられるZscalerロケーション。これはクラウドと同じロケーションです。",
    "TOOLTIP_VDI_MTU": "これはVDIグループ内のデバイスの最大伝送単位(MTU)値です。デフォルト値は1400バイトです。この値が正しく設定されていない場合、VDIエージェントのネットワーク パフォーマンスに影響する可能性があります。VDIグループ内で同じMTU値を持つデバイスをグループ化してください。",
    "TOOLTIP_VDI_OS_TYPE": "このVDIグループに関連付けられるVDIデバイスのオペレーティング システム(OS)のタイプ。VDI用Zscaler Client Connectorによって検出されたOSタイプは、デバイスをグループに追加するために使用されます。",
    "TOOLTIP_VDI_TEMPLATE_IDP_NAME": "IdP名を検索または選択します",
    "TOOLTIP_VDI_TEMPLATE_NAME": "VDIテンプレート名の名前を入力します。",
    "TOOLTIP_VDI_TEMPLATE_SYSTEM_USER": "システム ユーザーを検索または選択します。",
    "TOOLTIP_VDI_ZPA_USER_TUNNEL_FALLBACK": "コネクターは、ここで指定したユーザー トンネル数を上限として、各VDIユーザーに対してZPAへのユーザー トンネルを作成します。コネクターからのVDI ZPAユーザー トンネルの数がこの数値を超えると、後続のすべてのトランザクションはコネクターベースのトンネル経由でZPAに転送されます。このコネクターベースのトンネルでは、ユーザーはCloud Connectorグループとして認識されます。",
    "TOOLTIP_WAN_SELECTION": "WANの選択により、トラフィックが複数のWANリンク間で転送される方法が決まります。[バランス]に設定すると、トラフィックは均等に分散されます。[最適なリンク]に設定すると、トラフィックは常に最もパフォーマンスの高いWANリンクに転送されます。",
    "TOOLTIP_ZIA_TUNNEL_MODEL": "ZIAへのトンネルを作成するときに使用する暗号化タイプ。",
    "TOPIC_STATUS": "トピックのステータス",
    "TOTAL_CC_DEPLOYED": "展開されたCloud Connectorsの合計",
    "TOTAL_DEPLOYED": "展開済みの合計",
    "TOTAL_ENTITLED": "付与済み権限の合計",
    "TOTAL_LATENCY": "レイテンシーの合計",
    "TOTAL_TRAFFIC": "トラフィックの合計",
    "TOTAL_TRANSACTIONS": "トランザクションの合計",
    "TOTAL": "合計",
    "TRACE": "トレースルート",
    "TRACEROUTE_DESC": "トレース ルートは特定のホストにアクセスするパスを示すユーティリティーです。",
    "TRACEROUTE": "トレースルート",
    "TRADING_BROKARAGE_INSURANCE": "オンライン取引、仲介、保険",
    "TRADITIONAL_RELIGION": "伝統的宗教",
    "TRAFFIC_DIRECTON": "トラフィックの方向",
    "TRAFFIC_DISTRIBUTION": "トラフィックの分散",
    "TRAFFIC_FLOW": "トラフィック フロー",
    "TRAFFIC_FORWARDING_METHOD": "転送方法",
    "TRAFFIC_FORWARDING_RESOURCE": "トラフィック転送のリソース",
    "TRAFFIC_FORWARDING": "トラフィック転送",
    "TRAFFIC_FORWRDING_DNS": "転送(トラフィック、DNSとログ)",
    "TRAFFIC_MONITORING": "トラフィック監視",
    "TRAFFIC_OVERVIEW": "トラフィックの概要",
    "TRAFFIC_TEST": "トラフィック テスト",
    "TRAFFIC_TREND": "トラフィックの傾向",
    "TRAFFIC_TYPE": "トラフィック タイプ",
    "TRANSACTIONS": "トランザクション",
    "TRANSLATORS": "その他のインフォメーション テクノロジー",
    "TRAVEL": "旅行",
    "TRINIDAD_AND_TOBAGO_AMERICA_PORT_OF_SPAIN": "アメリカ/ポートオブスペイン",
    "TRINIDAD_AND_TOBAGO": "トリニダード・トバゴ",
    "TROUBLESHOOTING_LOGGING": "ログ記録のトラブルシューティング",
    "TRUE": "True",
    "TRUSTED_ACCOUNT_ID": "信頼されたアカウントID",
    "TRUSTED_ROLE": "信頼されたロール",
    "TS_DIRECTON": "TSの方向",
    "TUESDAY": "火曜日",
    "TUNISIA_AFRICA_TUNIS": "アフリカ/チュニス",
    "TUNISIA": "チュニジア共和国",
    "TUNNEL_AUTH_ALGORITHM": "認証アルゴリズム",
    "TUNNEL_AUTH_TYPE": "認証タイプ",
    "TUNNEL_DEAD_PEER_DETECTION": "キープアライブ パケット",
    "TUNNEL_DESTINATION_IP_END": "P2ポリシーの宛先IP - 終了",
    "TUNNEL_DESTINATION_IP_START": "P2ポリシーの宛先IP - 開始",
    "TUNNEL_DESTINATION_IP": "トンネルの宛先IP",
    "TUNNEL_DESTINATION_PORT_END": "P2ポリシーの宛先ポート - 終了",
    "TUNNEL_ENCRYPTION_ALGORITHM": "暗号化アルゴリズム",
    "TUNNEL_EVENT_REASON": "イベントの理由",
    "TUNNEL_INFORMATION": "トンネル情報",
    "TUNNEL_INITIATOR_COOKIE": "イニシエーターCookie",
    "TUNNEL_INSIGHTS": "トンネル インサイト",
    "TUNNEL_IP": "トンネルIP",
    "TUNNEL_IPSEC_PHASE2_SPI": "IKEフェーズ2 SPI",
    "TUNNEL_LIFEBYTES": "ライフバイト数",
    "TUNNEL_LIFETIME": "トンネルのライフタイム",
    "TUNNEL_LOG_TYPE": "ログ タイプ",
    "TUNNEL_LOGS": "トンネル ログ",
    "TUNNEL_POLICY_DIRECTION": "ポリシーの方向",
    "TUNNEL_PROTOCOL_NAME": "P2ポリシー プロトコル",
    "TUNNEL_PROTOCOL": "IPSecプロトコル",
    "TUNNEL_RECEIVED_PACKETS": "受信したパケット",
    "TUNNEL_RESPONDER_COOKIE": "レスポンダーCookie",
    "TUNNEL_SENT_PACKETS": "送信済みパケット",
    "TUNNEL_SOURCE_IP_END": "P2ポリシー送信元IP - 終了",
    "TUNNEL_SOURCE_IP_START": "P2ポリシー送信元IP - 開始",
    "TUNNEL_SOURCE_IP": "トンネルの送信元IP",
    "TUNNEL_SOURCE_PORT_START": "P2ポリシー送信元ポート - 開始",
    "TUNNEL_STATUS": "トンネルのステータス",
    "TUNNEL_TYPE": "トンネル タイプ",
    "TUNNEL_VENDOR_ID": "ベンダーID",
    "TUNNEL_VPN_CREDENTIAL": "VPN資格情報",
    "TURKEY_EUROPE_ISTANBUL": "ヨーロッパ/イスタンブール",
    "TURKEY": "トルコ共和国",
    "TURKMENISTAN_ASIA_ASHGABAT": "アジア/アシガバード",
    "TURKMENISTAN": "トルクメニスタン",
    "TURKS_AND_CAICOS_ISLANDS_AMERICA_GRAND_TURK": "アメリカ/グランドターク",
    "TURKS_AND_CAICOS_ISLANDS": "タークス・カイコス諸島",
    "TUVALU_PACIFIC_FUNAFUTI": "太平洋/フナフティ",
    "TUVALU": "ツバル",
    "TWO_CLOUD_CONNECTOR_TEMPLATE": "高可用性を備えたアドオン テンプレート",
    "TX_BYTES": "送信バイト数",
    "TX_PACKETS": "送信済みパケット",
    "TX_RX_BYTES": " 送信 | 受信バイト数",
    "TX_RX_PACKETS": " 送信 | 受信パケット数",
    "TYPE_ACCOUNT_ID": "アカウントIDを入力",
    "TYPE_ACCOUNT_NAME": "アカウント名を入力",
    "TYPE_APPLICATION_ID": "アプリケーションIDを入力",
    "TYPE_APPLICATION_KEY": "アプリケーション キーを入力",
    "TYPE_AWS_ACCESS_KEY_ID": "AWSアクセス キーIDを入力",
    "TYPE_AWS_SECRET_ACCESS_KEY": "AWSシークレット アクセス キーを入力",
    "TYPE_BASE_URL": "ベースURLを入力",
    "TYPE_CLIENT_SECRET": "クライアント シークレットを入力",
    "TYPE_DESCRIPTION_HERE": "ここに説明を入力",
    "TYPE_DOWNLOAD_MBPS": "ダウンロード(Mbps)を入力",
    "TYPE_DSTN_IP_NAME": "宛先IP名を入力",
    "TYPE_FEED_ESCAPE_CHARACTER": "テキストを入力",
    "TYPE_GATEWAY_NAME": "ここにゲートウェイ名を入力",
    "TYPE_GROUP_NAME_HERE": "ここにグループ名を入力",
    "TYPE_IP_ADDRESS_HERE": "ここにIPアドレスを入力",
    "TYPE_IP_ADDRESSESS_HERE": "IPアドレスを入力",
    "TYPE_IP_POOL_NAME": "IPプール名を入力",
    "TYPE_KEY": "キーを入力",
    "TYPE_LOCATION_TEMPLATE_NAME": "ロケーション テンプレート名を入力",
    "TYPE_NSS_CLOUD_FEED_API_URL": "API URLを入力",
    "TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL": "承認URLを入力",
    "TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID": "アクセス キーIDを入力",
    "TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY": "シークレット アクセス キーを入力",
    "TYPE_NSS_CLOUD_FEED_CLIENT_ID": "アプリケーション クライアントIDを入力",
    "TYPE_NSS_CLOUD_FEED_CLIENT_SECRET": "アプリケーション クライアント シークレットを入力",
    "TYPE_NSS_CLOUD_FEED_GRANT_TYPE": "client_credentialsを入力",
    "TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE": "テキストを入力",
    "TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL": "S3フォルダーのURLを入力",
    "TYPE_NSS_CLOUD_FEED_SCOPE": "https://monitor.azure.com//.defaultを入力",
    "TYPE_NSS_FEED_NAME": "テキストを入力",
    "TYPE_NSS_SERVER_NAME": "テキストを入力",
    "TYPE_SIEM_FQDN": "テキストを入力",
    "TYPE_SIEM_IP_ADDRESS": "テキストを入力",
    "TYPE_SIEM_RATE_LIMIT": "テキストを入力",
    "TYPE_SIEM_TCP_PORT": "テキストを入力",
    "TYPE_SOURCE_IP_NAME": "送信元IPグループ名を入力",
    "TYPE_SUBSCRIPTION_ID": "サブスクリプションIDを入力",
    "TYPE_TEMPLATE_PREFIX": "テンプレート プレフィックスを入力",
    "TYPE_TENANT_ID": "テナントIDを入力",
    "TYPE_UPLOAD_MBPS": "アップロード(Mbps)を入力",
    "TYPE_VALUE": "値を入力",
    "TYPE_ZS_TAG_OPTIONAL": "ZSタグを入力(省略可)",
    "TYPE": "タイプ",
    "UAE": "アラブ首長国連邦",
    "UAECENTRAL": "(中東)アラブ首長国連邦中部",
    "UAENORTH": "(中東)アラブ首長国連邦北部",
    "UBUNTU_LINUX": "Ubuntu LINUX",
    "UDP_ANY_DESC": "User Datagram Protocol (UDP)は、インターネット プロトコル スイート(インターネットで利用されるネットワーク プロトコルの集まり)の中でも核となるプロトコルです",
    "UDP_ANY": "UDP",
    "UDP_DESC": " User Datagram Protocol (UDP)は、インターネット プロトコル スイート(インターネットで利用されるネットワーク プロトコルの集まり)の中でも核となるプロトコルです",
    "UDP_DEST_PORTS": "UDP宛先ポート",
    "UDP_PORTS": "UDPポート",
    "UDP_SRC_PORTS": "UDP送信元ポート",
    "UDP_UNKNOWN_DESC": " これにより、より詳細なアプリを決定できないUDPプロキシ/ファイアウォール トラフィックが特定されます",
    "UDP_UNKNOWN": "不明なUDP",
    "UDP": "UDP",
    "UGANDA_AFRICA_KAMPALA": "アフリカ/カンパラ",
    "UGANDA": "ウガンダ共和国",
    "UK": "英国",
    "UKRAINE_EUROPE_KIEV": "ヨーロッパ/キエフ",
    "UKRAINE_EUROPE_SIMFEROPOL": "ヨーロッパ/シンフェロポリ",
    "UKRAINE_EUROPE_UZHGOROD": "ヨーロッパ/ウージュホロド",
    "UKRAINE_EUROPE_ZAPOROZHYE": "ヨーロッパ/ザポリージャ",
    "UKRAINE": "ウクライナ",
    "UKSOUTH": "(ヨーロッパ)英国南部",
    "UKWEST": "(ヨーロッパ)英国西部",
    "UNABLE_TO_LOGIN_TRY_AGAIN": "ログインできません。後でもう一度お試しください",
    "UNAUTHORIZED_COMMUNICATION": "未承認の通信",
    "UNENCRYPTED": "未暗号化",
    "UNEXPECTED_ERROR": "予期せぬエラーが発生しました",
    "UNHEALTHY": "異常",
    "UNITED_ARAB_EMIRATES_ASIA_DUBAI": "アジア/ドバイ",
    "UNITED_ARAB_EMIRATES": "アラブ首長国連邦",
    "UNITED_KINGDOM_EUROPE_LONDON": "ヨーロッパ/ロンドン",
    "UNITED_KINGDOM": "英国",
    "UNITED_STATES_AMERICA_ADAK": "アメリカ/アダック",
    "UNITED_STATES_AMERICA_ANCHORAGE": "アメリカ/アンカレッジ",
    "UNITED_STATES_AMERICA_BOISE": "アメリカ/ボイシ",
    "UNITED_STATES_AMERICA_CHICAGO": "アメリカ/シカゴ",
    "UNITED_STATES_AMERICA_DENVER": "アメリカ/デンバー",
    "UNITED_STATES_AMERICA_DETROIT": "アメリカ/デトロイト",
    "UNITED_STATES_AMERICA_INDIANA_INDIANAPOLIS": "アメリカ/インディアナ州/インディアナポリス",
    "UNITED_STATES_AMERICA_INDIANA_KNOX": "アメリカ/インディアナ州/ノックス",
    "UNITED_STATES_AMERICA_INDIANA_MARENGO": "アメリカ/インディアナ州/マレンゴ",
    "UNITED_STATES_AMERICA_INDIANA_PETERSBURG": "アメリカ/インディアナ州/ピーターズバーグ",
    "UNITED_STATES_AMERICA_INDIANA_TELL_CITY": "アメリカ/インディアナ州/テル・シティ",
    "UNITED_STATES_AMERICA_INDIANA_VEVAY": "アメリカ/インディアナ州/ベベイ",
    "UNITED_STATES_AMERICA_INDIANA_VINCENNES": "アメリカ/インディアナ州/ビンセンズ",
    "UNITED_STATES_AMERICA_INDIANA_WINAMAC": "アメリカ/インディアナ州/ウィナマク",
    "UNITED_STATES_AMERICA_JUNEAU": "アメリカ/ジュノー",
    "UNITED_STATES_AMERICA_KENTUCKY_LOUISVILLE": "アメリカ/ケンタッキー州/ルイビル",
    "UNITED_STATES_AMERICA_KENTUCKY_MONTICELLO": "アメリカ/ケンタッキー州/モンティチェロ",
    "UNITED_STATES_AMERICA_LOS_ANGELES": "アメリカ/ロサンゼルス",
    "UNITED_STATES_AMERICA_MENOMINEE": "アメリカ/メノミニー",
    "UNITED_STATES_AMERICA_NEW_YORK": "アメリカ/ニューヨーク",
    "UNITED_STATES_AMERICA_NOME": "アメリカ/ノーム",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_CENTER": "アメリカ/ノースダコタ州/センター",
    "UNITED_STATES_AMERICA_NORTH_DAKOTA_NEW_SALEM": "アメリカ/ノースダコタ州/ニュー・セーラム",
    "UNITED_STATES_AMERICA_PHOENIX": "アメリカ/フェニックス",
    "UNITED_STATES_AMERICA_SHIPROCK": "アメリカ/シップロック",
    "UNITED_STATES_AMERICA_YAKUTAT": "アメリカ/ヤクタト",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_JOHNSTON": "太平洋/ジョンストン島",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_MIDWAY": "太平洋/ミッドウェー島",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS_PACIFIC_WAKE": "太平洋/ウェーク島",
    "UNITED_STATES_MINOR_OUTLYING_ISLANDS": "合衆国領有小離島",
    "UNITED_STATES_PACIFIC_HONOLULU": "太平洋/ホノルル",
    "UNITED_STATES": "アメリカ合衆国",
    "UNITEDSTATES": "アメリカ合衆国",
    "UNITEDSTATESEUAP": "アメリカ合衆国EUAP",
    "UNITS": "ユニット",
    "UNKNOWN_ERROR_CODE": "不明なエラー コード",
    "UNKNOWN_HOPS": "不明なホップ",
    "UNLIMITED": "無制限",
    "UNREGISTERED": "未登録",
    "UNSELECTED_ITEMS": "選択されていない項目",
    "UNSELECTED": "未選択",
    "UNTAGGED": "タグなし",
    "UP": "アップ",
    "UPDATE": "アップデート",
    "UPDATED": "更新済み",
    "UPF_IP_CIDR": "ユーザー プレーン機能IP/CIDR",
    "UPF_NAME": "ユーザー プレーン機能名",
    "UPGRADE_ON": "アップグレード日",
    "UPGRADE_SCHEDULE": "アップグレード スケジュール",
    "UPGRADE_STATUS": "アップグレード ステータス",
    "UPGRADE_WILL_BE_SCHEDULED": "アップグレードは、Cloud Connectorのローカル タイム ゾーンに従ってスケジュールされます",
    "UPGRADE_WINDOW": "アップグレード ウィンドウ",
    "UPLINK_MODE": "アップリンク モード",
    "UPLOAD_MBPS": "アップロード(Mbps)",
    "UPLOAD": "アップロード",
    "UPTIME": "アップタイム",
    "URL_LOOKUP": "URL検索",
    "URUGUAY_AMERICA_MONTEVIDEO": "アメリカ/モンテビデオ",
    "URUGUAY": "ウルグアイ東方共和国",
    "US_CENTRAL1_A": "us-central1-a",
    "US_CENTRAL1_B": "us-central1-b",
    "US_CENTRAL1_C": "us-central1-c",
    "US_CENTRAL1_F": "us-central1-f",
    "US_CENTRAL1": "us-central1",
    "US_EAST_1": "us-east-1 (バージニア北部)",
    "US_EAST_1A": "us-east-1a",
    "US_EAST_1B": "us-east-1b",
    "US_EAST_1C": "us-east-1c",
    "US_EAST_1D": "us-east-1d",
    "US_EAST_1E": "us-east-1e",
    "US_EAST_1F": "us-east-1f",
    "US_EAST_2": "us-east-2 (オハイオ)",
    "US_EAST_2A": "us-east-2a",
    "US_EAST_2B": "us-east-2b",
    "US_EAST_2C": "us-east-2c",
    "US_EAST1_B": "us-east1-b",
    "US_EAST1_C": "us-east1-c",
    "US_EAST1_D": "us-east1-d",
    "US_EAST1": "us-east1",
    "US_EAST4_A": "us-east4-a",
    "US_EAST4_B": "us-east4-b",
    "US_EAST4_C": "us-east4-c",
    "US_EAST4": "us-east4",
    "US_EAST5_A": "us-east5-a",
    "US_EAST5_B": "us-east5-b",
    "US_EAST5_C": "us-east5-c",
    "US_EAST5": "us-east5",
    "US_GOV_EAST_1": "AWS GovCloud (米国東部)",
    "US_GOV_EAST_1A": "us-gov-east-1a",
    "US_GOV_EAST_1B": "us-gov-east-1b",
    "US_GOV_EAST_1C": "us-gov-east-1c",
    "US_GOV_WEST_1": "AWS GovCloud (米国西部)",
    "US_GOV_WEST_1A": "us-gov-west-1a",
    "US_GOV_WEST_1B": "us-gov-west-1b",
    "US_GOV_WEST_1C": "us-gov-west-1c",
    "US_OUTLYING_ISLANDS": "合衆国領有小離島",
    "US_SOUTH1_A": "us-south1-a",
    "US_SOUTH1_B": "us-south1-b",
    "US_SOUTH1_C": "us-south1-c",
    "US_SOUTH1": "us-south1",
    "US_VIRGIN_ISLANDS": "米領ヴァージン諸島",
    "US_WEST_1": "us-west-1 (カリフォルニア北部)",
    "US_WEST_1A": "us-west-1a",
    "US_WEST_1B": "us-west-1b",
    "US_WEST_1C": "us-west-1c",
    "US_WEST_2_LAX_1A": "us-west-2-lax-1a",
    "US_WEST_2": "us-west-2 (オレゴン)",
    "US_WEST_2A": "us-west-2a",
    "US_WEST_2B": "us-west-2b",
    "US_WEST_2C": "us-west-2c",
    "US_WEST_2D": "us-west-2d",
    "US_WEST1_A": "us-west1-a",
    "US_WEST1_B": "us-west1-b",
    "US_WEST1_C": "us-west1-c",
    "US_WEST1": "us-west1",
    "US_WEST2_A": "us-west2-a",
    "US_WEST2_B": "us-west2-b",
    "US_WEST2_C": "us-west2-c",
    "US_WEST2": "us-west2",
    "US_WEST3_A": "us-west3-a",
    "US_WEST3_B": "us-west3-b",
    "US_WEST3_C": "us-west3-c",
    "US_WEST3": "us-west3",
    "US_WEST4_A": "us-west4-a",
    "US_WEST4_B": "us-west4-b",
    "US_WEST4_C": "us-west4-c",
    "US_WEST4": "us-west4",
    "USDODCENTRAL": "US DoD Central",
    "USDODEAST": "US DoD East",
    "USE_WAN_DNS_SERVER": "WAN DNSサーバーを使用",
    "USE_WAN_DNS_SEVER": "WAN DNSサーバーを使用",
    "USER_ACCOUNT_LOCKED": "ログインの失敗が多すぎるため、アカウントが一時的にロックされました。後でもう一度お試しください。",
    "USER_DEFINED_TAGS": "ユーザー定義タグ",
    "USER_DEFINED": "ユーザー定義",
    "USER_ID": "ユーザーID",
    "USER_MANAGEMENT": "ユーザー管理",
    "USER_NAME_VISIBILITY": "ユーザー名の可視性",
    "USER_NAME": "ユーザー表示名",
    "USER_NAMES": "ユーザー名",
    "USER_PLANE_FUNCTION": "ユーザー プレーン機能",
    "USER": "ユーザー",
    "USERNAMES": "ユーザー名",
    "USGOVARIZONA": "USGovアリゾナ",
    "USGOVIOWA": "USGovアイオワ",
    "USGOVTEXAS": "USGovテキサス",
    "USGOVVIRGINIA": "USGovバージニア",
    "USSECEAST": "USSec East",
    "USSECWEST": "USSec West",
    "USSECWESTCENTRAL": "USSec West Central",
    "UZBEKISTAN_ASIA_SAMARKAND": "アジア/サマルカンド",
    "UZBEKISTAN_ASIA_TASHKENT": "アジア/タシュケント",
    "UZBEKISTAN": "ウズベキスタン共和国",
    "VALIDATION_ERROR_ARRAY_SIZE_OUT_OF_RANGE": "最大{0}の項目が許可されます。",
    "VALIDATION_ERROR_EMPTY_PROTOCOL": "プロトコルを入力してください。",
    "VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS": "このリストに無効な項目が含まれています。",
    "VALIDATION_ERROR_INVALID_AWS_ROLENAME": "有効なAWSロール名を入力してください。英数字と「+=,.@-」のみを使用してください。",
    "VALIDATION_ERROR_INVALID_DOMAIN": "有効なドメイン名を入力してください。",
    "VALIDATION_ERROR_INVALID_END_PORT_RANGE": "終了ポートは1～65535の間で、開始ポートより大きい値を指定する必要があります。",
    "VALIDATION_ERROR_INVALID_FQDN_OR_IP_ADDRESS": "有効なFQDN、IPアドレス、IPアドレス範囲、またはIP CIDRブロックを入力してください。",
    "VALIDATION_ERROR_INVALID_IP_ADDRESS": "有効なIPアドレス、IPアドレス範囲、またはIP CIDRブロックを入力してください。",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK_IS_BROADCAST": "有効なIPを入力してください。これはサブネット用のブロードキャストIPです。",
    "VALIDATION_ERROR_INVALID_IP_FOR_SUBNET_MASK": "IPアドレスが同じサブネット内にあることを確認してください。",
    "VALIDATION_ERROR_INVALID_IP_PORT": "有効なTCPポート(0～65535)を入力してください",
    "VALIDATION_ERROR_INVALID_IP_WITH_CIDR": "CIDRで有効なIPアドレスを入力してください。",
    "VALIDATION_ERROR_INVALID_IP": "有効なIPを入力してください",
    "VALIDATION_ERROR_INVALID_LOOSE_URL_SCHEMELESS": "有効なURLアドレスをhttp://またはhttps://のプレフィックスなしで入力してください。URLには少なくとも修飾するhost.domainのパターンが必要です。",
    "VALIDATION_ERROR_INVALID_MAC_ADDRESS": "有効なMACアドレスを入力してください。",
    "VALIDATION_ERROR_INVALID_NAME": "有効な名前を入力してください",
    "VALIDATION_ERROR_INVALID_PORT_STRING": "有効なポート番号またはポート番号の範囲(587、1～65535 など) を入力します。",
    "VALIDATION_ERROR_INVALID_PROTOCOL": "有効なプロトコルを入力してください。",
    "VALIDATION_ERROR_INVALID_SECONDARY_FIELD": "セカンダリー フィールド(手動)を空にすることはできません",
    "VALIDATION_ERROR_INVALID_SERVICE_IP_MASK": "すべてのサービスIPは、同じサブネット マスクを持つ必要があります。",
    "VALIDATION_ERROR_INVALID_START_PORT_RANGE": "ポート番号は1～65535の間にする必要があります。",
    "VALIDATION_ERROR_INVALID_URL": "有効なURLアドレスをhttp://またはhttps://のプレフィックス付きで入力してください。",
    "VALIDATION_ERROR_IP_INSIDE_INVALID_RANGE": "このIPは以下の無効な範囲のいずれかに含まれています。\n\n[0.0.0.0-*************]\n [**********-***************]\n [*********-***************]\n [***********-***************]\n [240.0.0.0-***************] ",
    "VALIDATION_ERROR_MS_SENTINEL_MAX_BATCH_SIZE_OUT_OF_RANGE": "この数値は128 KBから1 MBの間にする必要があります",
    "VALIDATION_ERROR_SAME_IP": "IPは異なる必要があります。",
    "VALIDATION_ERROR_SAME_SERVICE_IP": "サービスIPは異なる必要があります。",
    "VALIDATION_ERROR_SAME_START_END_PORT_RANGE": "開始ポートと終了ポートを同じにすることはできません。",
    "VALIDATION_NETWORK_SERVICE_GROUP_NAME_REQUIRED": "ネットワーク サービス グループ名を空にすることはできません。",
    "VALIDATION_NETWORK_SERVICE_GROUP_SERIVCE_REQUIRED": "少なくとも1つのタイプのサービスを指定する必要があります。",
    "VALIDATION_NETWORK_SERVICE_MIN_DEST_PORT": "少なくとも1つのTCPまたはUDP宛先ポートを入力してください。送信元ポートには対応する宛先ポートが必要です。",
    "VALIDATION_NETWORK_SERVICE_MIN_PORT_REQUIRED": "少なくとも1つのタイプのポートを指定する必要があります。",
    "VALIDATION_NETWORK_SERVICE_NAME_REQUIRED": "ネットワーク サービス名を空にすることはできません。",
    "VALUE": "値",
    "VANUATU_PACIFIC_EFATE": "太平洋/エファテ島",
    "VANUATU": "バヌアツ共和国",
    "VATICAN_CITY_STATE_EUROPE_VATICAN": "ヨーロッパ/バチカン市国",
    "VATICAN_CITY_STATE": "バチカン市国",
    "VATICAN_CITY": "バチカン市国",
    "VDI_AGENT_APP": "VDIアプリ ストア",
    "VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT": "詳細なプレースホルダー テキスト...",
    "VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT": "この設定は、トンネルからCloudまたはBranch Connectorへのトラフィックを含めるか除外するために、VDI用Zscaler Client Connectorによって使用されます。場合によっては、CloudまたはBranch Connectorで包含リストと除外リストも構成する必要があります。",
    "VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT": "テキストをリターンで区切って入力してください。",
    "VDI_AGENT_FORWARDING_PROFILE": "VDI転送プロファイル",
    "VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT": "この構成は、エンド ユーザー認証とシステム ユーザーに関連付けられたユーザーIDに使用されます。",
    "VDI_AGENT_TEMPLATE": "VDIテンプレート",
    "VDI_AGENT_TEMPLATES": "VDIテンプレート",
    "VDI_DEVICE_GENERAL_TEXT": "特定のグループを識別するための情報を入力します。",
    "VDI_DEVICE_MANAGEMENT": "VDIデバイス管理",
    "VDI_DEVICES": "VDIデバイス",
    "VDI_FORWARDING_PROFILE_TEXT": "このVDIグループに関連付けられる転送プロファイル。選択した転送プロファイルの包含または除外ポリシーは、VDI用Zscaler Client Connectorによって適用され、このVDIグループに含まれるデバイスにインストールされます。",
    "VDI_FORWARDING_PROFILE": "VDI転送プロファイル",
    "VDI_GROUPS": "VDIグループ",
    "VDI_MANAGEMENT": "VDI管理",
    "VDI_REVIEW_TEXT": "このグループを追加する前に、以下の情報がすべて正しいことを確認してください。",
    "VDI_ZPA_USER_TUNNEL_FALLBACK": "VDI ZPAユーザー トンネルのフォールバック",
    "VDO_LIVE_DESC": "VDOLiveは拡張可能な動画配信技術です",
    "VDO_LIVE": "VDOLive",
    "VEHICLES": "車/乗り物",
    "VENDOR": "ベンダー",
    "VENEZUELA_AMERICA_CARACAS": "アメリカ/カラカス",
    "VENEZUELA": "ベネズエラ・ボリバル共和国",
    "VERBOSE": "冗長",
    "VERIFY_CURRENT_PASSWORD": "現在のパスワードを検証",
    "VERSION_PROFILE": "バージョン プロファイル",
    "VERSION": "バージョン",
    "VIET_NAM_ASIA_SAIGON": "アジア/ホーチミン市",
    "VIET_NAM": "ベトナム(_N)",
    "VIETNAM": "ベトナム社会主義共和国",
    "VIEW_5G_DEPLOYMENT": "展開構成を表示",
    "VIEW_APPLIANCE": "アプライアンスを表示",
    "VIEW_AWS_ACCOUNT": "AWSアカウントを表示",
    "VIEW_AWS_GROUP": "AWSグループを表示",
    "VIEW_AZURE_TENANT": "Azureテナントを表示",
    "VIEW_BRANCH_PROVISIONING_TEMPLATE": "Branch Connectorのプロビジョニング テンプレートを表示",
    "VIEW_CLOUD_CONNECTOR_ADMIN": "Cloud Connector管理者を表示",
    "VIEW_CLOUD_CONNECTOR_ROLE": "Cloud Connectorロールを表示",
    "VIEW_CLOUD_CONNECTORS": "コネクターを表示",
    "VIEW_CLOUD_NSS_FEED": "クラウドNSSフィードを表示",
    "VIEW_CLOUD_PROVIDER_AWS": "AWSクラウド アカウントを表示",
    "VIEW_CLOUD_PROVIDER_AZURE": "Azureクラウド アカウントを表示",
    "VIEW_CONNECTORS": "コネクターを表示",
    "VIEW_DESTINATION_IP_GROUP": "宛先IPグループを表示",
    "VIEW_DNS_GATEWAYS": "DNSゲートウェイの表示",
    "VIEW_DNS_POLICIES": "DNSフィルタリング ルールを表示",
    "VIEW_DYNAMIC_VDI_GROUP": "動的VDIグループを表示",
    "VIEW_GATEWAYS": "ゲートウェイを表示",
    "VIEW_INFO": "情報を表示",
    "VIEW_IP_POOL_GROUP": "IPプールを表示",
    "VIEW_LOCATION_TEMPLATE": "ロケーション テンプレートを表示",
    "VIEW_LOCATIONS": "ロケーションを表示",
    "VIEW_LOG_AND_CONTROL_FORWARDING_RULE": "ログとコントロール転送ルールを表示",
    "VIEW_NETWORK_SERVICE_GROUP": "ネットワーク サービス グループを表示",
    "VIEW_NETWORK_SERVICE": "ネットワーク サービスを表示",
    "VIEW_NSS_FEEDS": "NSSフィードを表示",
    "VIEW_NSS_SERVER": "NSSサーバーを表示",
    "VIEW_ONLY_ACCESS": "表示専用アクセス",
    "VIEW_ONLY_ENABLED_UNTIL": "表示専用アクセスの有効期限",
    "VIEW_ONLY": "表示専用",
    "VIEW_PHYSICAL_BRANCH_DEVICE": "物理ブランチ デバイスを表示",
    "VIEW_PROVISIONING_TEMPLATE": "Cloud Connectorのプロビジョニング テンプレートを表示",
    "VIEW_SOURCE_IP_GROUP": "送信元IPグループを表示",
    "VIEW_SUB_LOCATIONS": "サブロケーションを表示",
    "VIEW_SUBLOCATIONS": "サブロケーションを表示",
    "VIEW_TRAFFIC_FWD_POLICIES": "トラフィック転送ルールを表示",
    "VIEW_UPF": "ユーザー プレーン機能を表示",
    "VIEW_VDI_AGENT_FORWARDING_PROFILE": "VDI転送プロファイルを表示",
    "VIEW_VDI_TEMPLATE": "VDIテンプレートを表示",
    "VIEW_VIRTUAL_BRANCH_DEVICE": "仮想ブランチ デバイスを表示",
    "VIEW_ZERO_TRUST_GATEWAY": "ゼロトラスト ゲートウェイを表示",
    "VIEW_ZT_DEVICE": "ZTデバイスを表示",
    "VIEW": "ビュー",
    "VIOLENCE": "暴力",
    "VIRGIN_ISLANDS_BRITISH_AMERICA_TORTOLA": "アメリカ/トルトラ島",
    "VIRGIN_ISLANDS_BRITISH": "ヴァージン諸島(イギリス領)",
    "VIRGIN_ISLANDS_US_AMERICA_ST_THOMAS": "アメリカ/セント・トーマス島",
    "VIRGIN_ISLANDS_US": "ヴァージン諸島(アメリカ領)",
    "VIRTUAL_IP_ADDRESS": "仮想IPアドレス",
    "VIRTUAL_IP_AND_LAN_IP_MUST_BE_DIFFERENT": "仮想IPとLAN IPアドレスは異なる必要があります。",
    "VIRTUAL_IP_AND_PEER_DHCP_MUST_BE_DIFFERENT": "仮想IPとピアDHCPアドレスは異なる必要があります。",
    "VIRTUAL_SERVICE_EDGE_ID": "仮想サービス エッジID",
    "VIRTUAL": "仮想",
    "VISIBLE": "表示",
    "VLAN_ID": "VLAN ID",
    "VM_GBL_METRICS": "VM",
    "VM_HEALTH_FETCH_API_ERROR": "現在、VM正常性をフェッチできません。後でもう一度お試しください。",
    "VM_ID": "VM ID",
    "VM_NAME": "VM名",
    "VM_SIZE": "VMサイズ",
    "VMWARE_ESXI": "VMware ESXi",
    "VMWARE": "VMware",
    "VNC_DESC": "仮想ネットワーク コンピューティング",
    "VNC": "VNC",
    "VPC_ID": "VPC ID",
    "VPC_NAME": "VPC名",
    "VPC_VNET_NAME": "VPC/VNET名",
    "VPN_CREDENTIAL_DROPDOWN": "VPN資格情報ドロップダウン",
    "VPN_CREDENTIAL": "VPN資格情報",
    "VPN_CREDENTIALS": "VPN資格情報",
    "VSE_CLUSTERS": "仮想サービス エッジ クラスター",
    "VSE_NODES": "仮想サービス エッジ",
    "WALLIS_AND_FUTUNA_ISLANDS_PACIFIC_WALLIS": "太平洋/ウォリス",
    "WALLIS_AND_FUTUNA_ISLANDS": "ウォリス・フツナ諸島",
    "WALLIS_AND_FUTUNA": "ウォリス・フツナ",
    "WAN_DESTINATIONS_GROUP": "WAN宛先グループ",
    "WAN_NEED_AT_LEAST_ONE_ACTIVE_INTERFACE": "WANには、少なくとも1つのアクティブ インターフェイスが必要です。",
    "WAN_PRI_DNS": "WANプライマリーDNSサーバー",
    "WAN_SEC_DNS": "WANセカンダリーDNSサーバー",
    "WAN_SELECTION": "WANの選択",
    "WAN": "WAN",
    "WEAPONS_AND_BOMBS": "武器/爆弾",
    "WEB_BANNERS": "広告",
    "WEB_CONFERENCING": "Web会議",
    "WEB_HOST": "Webホスト",
    "WEB_SEARCH": "Web検索",
    "WEB_SPAM": "Webスパム",
    "WEDNESDAY": "水曜日",
    "WESTCENTRALUS": "(米国)米国中西部",
    "WESTERN_SAHARA_AFRICA_EL_AAIUN": "アフリカ/アイウン",
    "WESTERN_SAHARA": "西サハラ",
    "WESTEUROPE": "(ヨーロッパ)西ヨーロッパ",
    "WESTINDIA": "(アジア太平洋)インド西部",
    "WESTUS": "(米国)米国西部",
    "WESTUS2": "(米国)米国西部2",
    "WESTUS2STAGE": "(米国)米国西部2 (ステージ)",
    "WESTUS3": "(米国)米国西部3",
    "WESTUSSTAGE": "(米国)米国西部(ステージ)",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_PLACEHOLDER": "クライアント/アプリケーションIDを入力",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS_TOOLTIP": "クライアント/アプリケーションIDを入力します。",
    "WHAT_ARE_THE_SERVICE_ACCESS_DETAILS": "クライアント/アプリケーションID",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_PLACEHOLDER": "クライアント/アプリケーション シークレットを入力",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET_TOOLTIP": "クライアント/アプリケーション シークレットを入力します。",
    "WHAT_ARE_THE_SERVICE_ACCESS_SECRET": "クライアント/アプリケーション シークレット",
    "WHAT_DO_YOU_CALL_THIS_TENANT_PLACEHOLDER": "Azureアカウントの名前を入力",
    "WHAT_DO_YOU_CALL_THIS_TENANT_TOOLTIP": "アカウントの管理に使用する名前を入力します。",
    "WHAT_DO_YOU_CALL_THIS_TENANT": "アカウント名",
    "WHAT_IS_THE_DIRECTORY_ID_PLACEHOLDER": "ディレクトリーIDを入力",
    "WHAT_IS_THE_DIRECTORY_ID_TOOLTIP": "Azureアカウントのオンボーディングに使用されるAzureサービス プリンシパルに関連付けられているディレクトリー(テナント) IDを入力します。",
    "WHAT_IS_THE_DIRECTORY_ID": "ディレクトリー(テナント)ID",
    "WHITESPACES_ARE_NOT_ALLOWED": "空白は使用できません",
    "WHOIS_DESC": "ネットワーク ディレクトリー サービス プロトコル",
    "WHOIS": "WHOIS",
    "WINDOWS_OS": "Windows OS",
    "WORKLOAD_SERVICE_REQUIRED_MESSAGE": "パートナー統合には、組織が現在所有するものとは別のサブスクリプションが必要です。Zscalerサポートに連絡して基本プランを有効にしてください。追加費用は発生しません。パートナー統合により、Zscalerはお客様のアカウントまたはサブスクリプションに紐づけられたサービスをパブリック クラウド上で実行および保守できるようになります。Zscalerは、お客様のアカウントを当社の容量に基づいてサービスに割り当てます。",
    "WORKLOAD_SERVICE_REQUIRED": "ワークロード サービスを構成する必要があります",
    "WORKLOAD": "ワークロード トラフィック タイプ",
    "WORKLOADS": "ワークロード",
    "XSS": "クロスサイト スクリプティング",
    "YEMEN_ASIA_ADEN": "アジア/アデン",
    "YEMEN": "イエメン共和国",
    "YES": "はい",
    "YESKY_DESC": " このプロトコル プラグインは、ホストyesky.comへのhttpトラフィックを分類します",
    "YESKY": "yesky",
    "YIHAODIAN_DESC": " 中国のオンライン ショッピング",
    "YIHAODIAN": "Yihaodian",
    "YMAIL_CLASSIC_DESC": " Yahoo Mail ClassicはYahoo! Mailの元のインターフェイスです",
    "YMAIL_CLASSIC": "Yahoo Mail Classic",
    "YMAIL_MOBILE_DESC": " (非推奨) Yahoo Mail (モバイル)はyahoo.comのWebメールをモバイル用に対応させたものです",
    "YMAIL_MOBILE_NEW_DESC": " Yahoo Mail (モバイル)は、新しいyahoo.comのWebメールをモバイル用に対応させたものです",
    "YMAIL_MOBILE_NEW": "Yahoo Mail (モバイル)",
    "YMAIL_MOBILE": "Ymail (モバイル)",
    "YMAIL2_DESC": " このプロトコルは、WebメールYahooのajaxベースのバージョンです",
    "YMAIL2": "Ymail2",
    "YMSG_CONF_DESC": " このプロトコルは会議のシグナリングの際に使用されます",
    "YMSG_CONF": "Yahoo Messenger Conference",
    "YMSG_DESC": " Yahoo Messengerは、Yahoo Instant Messengerアプリケーションで使用され、ユーザー間でインスタント メッセージ、ファイル、メールを送信します",
    "YMSG_TRANSFER_DESC": " このプロトコルはYahoo Messenger上でファイル転送を行う際に使用されます",
    "YMSG_TRANSFER": "Yahoo Messenger File Transfer",
    "YMSG_VIDEO_DESC": " (10.0.0.270より前のバージョン)このプロトコルはYahoo Messengerのビデオ通話に使用されます",
    "YMSG_VIDEO": "Yahoo Messenger Video",
    "YMSG_WEBMESSENGER_DESC": " Yahoo Messenger for the Web",
    "YMSG_WEBMESSENGER": "Yahoo Messenger for the Web",
    "YMSG": "Yahoo Messenger",
    "YOU_DO_NOT_HAVE_THE_NECESSARY_PERMISSION": "このページを表示するために必要な権限がありません",
    "YOUR_ACCOUNT_INFORMATION_WAS_SAVED_BUT_SOME_REGIONS_FAILED": "アカウント情報は保存されましたが、次のリージョンは保存できませんでした。",
    "ZAMBIA_AFRICA_LUSAKA": "アフリカ/ルサカ",
    "ZAMBIA": "ザンビア共和国",
    "ZDX_UI": "ZDX UI",
    "ZERO_TRUST_GATEWAY": "ゼロトラスト ゲートウェイ",
    "ZIA_GATEWAY": "ZIAゲートウェイ",
    "ZIA_GW_AUTH_FAIL": "ZIAゲートウェイでの認証に失敗しました。",
    "ZIA_GW_CONN_SETUP_FAIL": "ZIAゲートウェイ接続のセットアップに失敗しました(内部エラー)。",
    "ZIA_GW_CONNECT_FAIL": "ZIAゲートウェイ接続に失敗しました(ネットワーク エラー)。",
    "ZIA_GW_CTL_CONN_CLOSE": "ZIAゲートウェイのアクティブなコントロール接続がクローズされました。",
    "ZIA_GW_CTL_KA_FAIL": "ZIAゲートウェイ コントロール接続のキープアライブに失敗しました。",
    "ZIA_GW_DATA_CONN_CLOSE": "ZIAゲートウェイのアクティブなデータ接続がクローズされました。",
    "ZIA_GW_DATA_KA_FAIL": "ZIAゲートウェイ データ接続のキープアライブに失敗しました。",
    "ZIA_GW_DNS_RESOLVE_FAIL": "ZIAゲートウェイの DNS解決に失敗しました。",
    "ZIA_GW_PAC_RESOLVE_FAIL": "ZIAゲートウェイのPAC解決に失敗しました。",
    "ZIA_GW_PAC_RESOLVE_NOIP": "ZIAゲートウェイのPAC解決でIPSが返されませんでした。",
    "ZIA_GW_PROTO_MSG_ERROR": "ZIA GWコントロール/データ チャネルのメッセージ形式エラー。",
    "ZIA_GW_PROTO_VER_ERROR": "ZIAプロトコルのバージョンが一致しません。",
    "ZIA_GW_SSL_ERROR": "ZIA GWコントロール/データ チャネルのSSLエラー。",
    "ZIA_GW_UNHEALTHY": "ZIAゲートウェイが異常です(一時的な状態)。",
    "ZIA_THROUGHPUT_KBPS_SESSION": "ZIA (スループットkbps/セッション)",
    "ZIA_TUNNEL_MODEL": "ZIAトンネル モード",
    "ZIA_TUNNEL": "ZIAトンネル",
    "ZIA": "ZIA",
    "ZIMBABWE_AFRICA_HARARE": "アフリカ/ハラレ",
    "ZIMBABWE": "ジンバブエ共和国",
    "ZONE": "ゾーン",
    "ZPA_BROKER": "ZPAブローカー",
    "ZPA_EDGE_APP_SEGMENT": "ZPA Edge App Segment",
    "ZPA_IP_POOL": "ZPA IPプール",
    "ZPA_POLICY_VIOLATION_INDICATOR": "ZPAポリシー違反インジケーター",
    "ZPA_THROUGHPUT_KBPS_SESSION": "ZPA (スループットkbps/セッション)",
    "ZPA_TUNNEL": "ZPAトンネル",
    "ZPA": "ZPA",
    "ZS_TAG_OPTIONAL": "ZSタグ(省略可)",
    "ZSCALER_ANALYZER": "Zscaler Analyzer",
    "ZSCALER_CLOUD_ENDPOINTS": "Zscalerクラウド エンドポイント",
    "ZSCALER_DOMAINS": "Zscalerドメイン",
    "ZSCALER_ESTABLISH_SUPPORT_TUNNEL": "Zscalerが開始したオンデマンド サポート トンネル",
    "ZSCALER_GATEWAY_DETAILS": "Zscalerゲートウェイの詳細",
    "ZSCALER_HELP_PORTAL": "Zscalerヘルプ ポータル",
    "ZSCALER_INC_ALL_RIGHTS_RESERVED": "Zscaler Inc. All rights reserved.",
    "ZSCALER_INTERFACE_NAME": "Zscalerインターフェイス名",
    "ZSCALER_IP": "Zscaler IP",
    "ZSCALER_IPS": "Zscaler IP",
    "ZSCALER_PROXY_NW_SERVICES_DESC": "このネットワーク サービスには、顧客固有のDPPCポートを始めとするZscaler固有のすべてのWebプロキシ ポートが含まれています。",
    "ZSCALER_PROXY_NW_SERVICES": "Zscaler Proxy Network Services",
    "ZSLOGIN_ADMINISTRATION": "ZIdentity管理",
    "ZSPROXY_IPS": "Zscaler Proxy IP",
    "ZT_DEVICES": "ZTデバイス",
    "ZT_GATEWAY": "ゼロトラスト ゲートウェイ",
    "ZTG_ACCOUNT_TEXT": "ゲートウェイは、入力されたアカウントのリストから受信したエンドポイントのリクエストを受け入れます。[パートナー統合]ページでオンボーディングされたAWSアカウントとアカウント グループを選択できます。[パートナー統合]ページでオンボーディングされていないアカウントの場合は、12桁のAWSアカウントIDを手動で入力します。パートナー統合の詳細については、{0}Cloud Connectorパートナー統合のドキュメント{1}を参照してください。",
    "ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP": "AWSアカウントが[パートナー統合]ページでオンボーディングされていない場合は、12桁のAWSアカウントIDを入力します。ゲートウェイは、ゲートウェイと同じリージョンのエンドポイントから送信されたリクエストを受け入れます。",
    "ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP": "このゲートウェイへの接続を許可するAWSアカウント グループを選択します。ゲートウェイは、ゲートウェイと同じリージョンのエンドポイントから送信されたリクエストを受け入れます。アカウント グループは[パートナー統合]ページで作成されます。",
    "ZTG_ALLOWED_ACCOUNTS_TOOLTIP": "このゲートウェイへの接続を許可するAWSアカウントを選択します。ゲートウェイは、ゲートウェイと同じリージョンのエンドポイントから送信されたリクエストを受け入れます。アカウントは[パートナー統合]ページで追加されます。",
    "ZTG_AVIABILITY_ZONE_TOOLTIP": "ゲートウェイ コンポーネントを作成するアベイラビリティー ゾーンを選択します。少なくとも2つのアベイラビリティー ゾーンを選択する必要があります。これらはAWSのアベイラビリティー ゾーンIDであり、AWSアカウントに表示される名前ではありません。IDが表示されるのは、同じアベイラビリティー ゾーン名が異なるAWSアカウントの異なるアベイラビリティー ゾーンIDにマッピングされる可能性があるためです。アベイラビリティー ゾーン名とIDのマッピング、およびアカウントのアベイラビリティー ゾーンIDの検索については、{0}AWSのドキュメント{1}を参照してください。",
    "ZTG_CONFIGURATION_TEXT": "このゲートウェイの構成を入力します。この構成により、サービスが利用可能なAWSリージョンとアベイラビリティー ゾーンが決まります。",
    "ZTG_ID": "ゼロトラスト ゲートウェイID",
    "ZTG_LOCATION_TEMPLATE_TOOLTIP": "ゲートウェイに関連付けるロケーションを作成するために使用するロケーション テンプレートを選択します。",
    "ZTG_LOCATION_TOOLTIP": "このゲートウェイに関連付けるロケーションの名前を入力します。この名前は、ロケーション オブジェクトが利用可能なすべてのポリシーに表示されます。",
    "ZTG_NAME_TOOLTIP": "ゲートウェイの名前を入力します。この名前はゲートウェイに関連付けられます。",
    "ZTG_REGION_TOOLTIP": "ゲートウェイを作成するリージョンを選択します。ゲートウェイはリージョン限定であり、1つのリージョンにのみ展開できます。ゲートウェイの作成後にリージョンを変更することはできません。",
    "ZTG_REVIEW_TEXT": "ゲートウェイを作成する前に、すべての情報が正しいことを確認してください。ゲートウェイが作成されると、コンポーネントの展開には数分かかる場合があります。ゲートウェイのステータスを確認するには、[ゼロトラスト ゲートウェイ]ページをご覧ください。",
    "ZTGW_GROUP": "ゼロトラスト ゲートウェイ グループ",
    "ZTGW_VM": "ゼロトラスト ゲートウェイVM",
    "ZULU": "ズールー",
  },
};
