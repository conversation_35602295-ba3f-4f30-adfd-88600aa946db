import {
  resetStore, sessionTimeout, saveCsrfToken,
} from 'ducks/login';
import { get } from 'utils/lodash';
import { isOneUI } from 'config';

import PersistentStorage, {
  LS_PATH,
  LS_ERR_MESSAGE,
} from 'utils/persistentStorage';
import { store } from '../../App';

const isLoginPage = () => (/\/login$/.test(window.location.href));

const redirectToLogin = () => {
  const { pathname } = window.location;
  Promise.resolve(store.dispatch(resetStore())).then(() => {
    PersistentStorage.clearSessionItems();
    // localStorage.clear();
    const reactPathName = pathname && pathname.replace(process.env.REACT_APP_API_BASE_PATH, '');
    PersistentStorage.setItem({ [LS_PATH]: reactPathName });
  }).then(() => {
    store.dispatch(sessionTimeout());
  });
};

export const handleError = (error) => {
  // we will make this more robust as we go along.
  // we need to show 401, 403, 406, 415, 404, 500, 503, etc.
  const STATUS_REDIRECT_LOGIN = [401];
  const SAVE_CSRF = [401];
  const MAINTENANCE_STATUS = 503;
  const errorStatus = get(error, 'response.status', 0);
  // const url = get(error, 'config.url', '');
  let url = window.location.href;
  const method = get(error, 'config.method', '');
  const errMessage = get(error, 'response.data.message', '');
  // Mapper which maps API error invalid login messages to i18n messages for translation
  const API_ERROR_MESSAGES_MAPPER = {
    'Authentication failed': 'INVALID_INPUT_ARGUMENT',
    'Invalid Username/Password or User not subscribed': 'INVALID_CREDS_SUBSCRIPTION',
    'Your account has been temporarily locked due to too many login failures. Try again later.': 'ACCOUNT_TEMPORARY_LOCKED',
    'CSRF nonce validation failed': 'UNABLE_TO_LOGIN_TRY_AGAIN',
  };

  // sepcial case for GET /auth 401 status: this response contains CSRF token in headers
  const isContainsCSRFRequest = /\/auth$/.test(url) && method === 'get';

  const isContainsCSRF = isContainsCSRFRequest && SAVE_CSRF.indexOf(errorStatus) !== -1;

  if (isContainsCSRFRequest && errorStatus === MAINTENANCE_STATUS) {
    window.location.href = '/ec/maintenance';
    return Promise.reject(error);
  }

  if (isContainsCSRF) {
    saveCsrfToken(error.response);
  }
  if (errorStatus && STATUS_REDIRECT_LOGIN.indexOf(errorStatus) !== -1 && isLoginPage()) {
    localStorage.setItem('errMessage', API_ERROR_MESSAGES_MAPPER[errMessage] || 'INVALID_INPUT_ARGUMENT');
    PersistentStorage.setItem({ [LS_ERR_MESSAGE]: API_ERROR_MESSAGES_MAPPER[errMessage] || 'INVALID_INPUT_ARGUMENT' });
  }

  if (errorStatus && STATUS_REDIRECT_LOGIN.indexOf(errorStatus) !== -1 && !isLoginPage()) {
    localStorage.removeItem('authenticated');
    localStorage.removeItem('apiPermissions');
    if (isOneUI) {
      return window.handleLogout();
    }

    store.dispatch(sessionTimeout());

    if (url.indexOf('/ec') > -1) {
      url = url.substring(0, (url.lastIndexOf('ec/admin') + 2));
      window.location = url + '/login';
      // window.location = window.location.origin + '/ec/login';
    } else {
      window.location = window.location.origin + '/login';
    }
    return redirectToLogin();
  }

  if (isContainsCSRF && isLoginPage()) {
    return Promise.resolve({});
  }
  
  return Promise.reject(error);
};

export default handleError;
