/**
 * Description.
   We add flags for hiding certain features from users.
 * Value type
    Boolean
 * */
export const LS_FLAGS = 'flags';

/**
 * Description.
    We add csrf Token programmatically, after user logged in.
 * Value type
    String
 * */
export const LS_CSRF_TOKEN = 'csrfToken';

/**
 * Description.
    Login flag is boolean which shows whether user logged in or not.
 * Value type
    String
 * */
export const LS_LOGIN = 'login';

/**
 * Description.
    In login page user can choose the language.
    After user choose the language we keep it in the storage and always show the chosen language.
    By default value is en-US
 * Value type
    String
 * */
export const LS_LOCALE = 'locale';

/**
 * Description.
    We add orgId programmatically, after user logged in.
    It is information about user.
 * Value type
    String
 * */
export const LS_ORG_ID = 'orgId';

/**
 * Description.
    We add usernameToBeRemembered  programmatically, when user trying to log in.
    We remember the username and adding it in the login form.
 * Value type
    String
 * */
export const LS_USERNAME_TO_BE_REMEMBERED = 'usernameToBeRemembered';

/**
 * Description.
    In navigation menu we have user search where we search users,
    We store result in the local storage and show as a search history.
 * Value type
    Object
 * */
export const LS_SEARCH_HISTORY = 'searchHistory';

/**
 * Description.
    We add path programmatically, during user log in.
 * Value type
    String
 * */
export const LS_PATH = 'path';

/**
 * Description.
    We add error message programmatically, when user trying to log in with wrong credentials.
 * Value type
    String
 * */
export const LS_ERR_MESSAGE = 'errMessage';

/**
 * Description.

 * Value type

 * */
export const LS_HELP_ARTICLE = 'helpArticle';

/**
 * Description.

 * Value type

 * */
export const LS_LOGOUT = 'logout';

/**
 * Description.
    Track if SKU banner is closed by the user
 * Value type
    Boolean
 * */
export const LS_IS_SKU_BANNER_CLOSED = 'isSkuBannerClosed';

/**
 * Description.
   Using for version hash of application
 * Value type
   String
 * */
export const VERSION_HASH = 'versionHash';

/**
 * Description.
 Track if password expiry notification is closed by the user
 * Value type
 Boolean
 * */
export const LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED = 'isPasswordExpiryNotificationClosed';

/**
 * Description.
 Track username
 * Value type
 String
 * */
export const LS_USERNAME = 'username';

/**
 * Description.
 Track has Policy Tips enabled or disabled
 * Value type
 Boolean
 * */
export const LS_HAS_POLICY_TIPS = 'hasPolicyTipsLocalStorage';

/**
 * Description.
 Track if EUSA notification is closed by the user
 * Value type
 Boolean
 * */
export const LS_IS_EUSA_NOT_ACCEPTED = 'isEUSANotAccepted';

/**
 * Description.
 Track the login method type:LOGIN (ADMIN_LOGIN)
 * or Remote Assistance (SUPPORT_ACCESS_READ_ONLY,SUPPORT_ACCESS_PARTIAL, SUPPORT_ACCESS_FULL))
 * Value type
 STRING
 * */
export const LS_AUTH_TYPE = 'authType';
