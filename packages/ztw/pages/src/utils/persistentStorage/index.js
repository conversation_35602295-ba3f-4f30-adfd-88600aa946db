import {
  LS_FLAGS,
  LS_CSRF_TOKEN,
  LS_LOGIN,
  LS_LOCALE,
  LS_ORG_ID,
  LS_USERNAME_TO_BE_REMEMBERED,
  LS_SEARCH_HISTORY,
  LS_PATH,
  LS_ERR_MESSAGE,
  LS_HELP_ARTICLE,
  LS_LOGOUT,
  LS_IS_SKU_BANNER_CLOSED,
  VERSION_HASH,
  LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED,
  LS_USERNAME,
  LS_AUTH_TYPE,
  LS_HAS_POLICY_TIPS,
  LS_IS_EUSA_NOT_ACCEPTED,
} from './storageKeys';

const sessionItems = [
  LS_CSRF_TOKEN,
  LS_LOGIN,
  LS_ORG_ID,
  LS_SEARCH_HISTORY,
  LS_ERR_MESSAGE,
  LS_HELP_ARTICLE,
  LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED,
];

class PersistentStorage {
  getItem = (...keys) => {
    const result = [];
    keys.forEach((key) => {
      let resultItem;
      try {
        resultItem = JSON.parse(localStorage.getItem(key));
      } catch (e) {
        if (typeof localStorage.getItem(key) === 'string') {
          resultItem = localStorage.getItem(key);
        }
      }
      if (resultItem) {
        result.push(resultItem);
      }
    });
    if (!result.length) {
      return null;
    }

    return result.length === 1 ? result[0] : result;
  };

  setItem = (data) => {
    const keys = Object.keys(data);
    keys.forEach((key) => {
      let result = data[key];
      if (typeof result === 'object') {
        result = JSON.stringify(result);
      }
      localStorage.setItem(key, result);
    });
  };

  clearSessionItems = () => {
    this.removeItem(...sessionItems);
    this.setItem({ [LS_LOGOUT]: true });
  };

  removeItem = (...keys) => {
    keys.forEach((key) => {
      localStorage.removeItem(key);
    });
  };

  clear = () => {
    // localStorage.clear();
  };
}

export default new PersistentStorage();
export {
  LS_FLAGS,
  LS_CSRF_TOKEN,
  LS_LOGIN,
  LS_LOCALE,
  LS_ORG_ID,
  LS_USERNAME_TO_BE_REMEMBERED,
  LS_SEARCH_HISTORY,
  LS_PATH,
  LS_ERR_MESSAGE,
  LS_HELP_ARTICLE,
  LS_LOGOUT,
  LS_IS_SKU_BANNER_CLOSED,
  VERSION_HASH,
  LS_IS_PASSWORD_EXPIRY_NOTIFICATION_CLOSED,
  LS_USERNAME,
  LS_AUTH_TYPE,
  LS_HAS_POLICY_TIPS,
  LS_IS_EUSA_NOT_ACCEPTED,
};
