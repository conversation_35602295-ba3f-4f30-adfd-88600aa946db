import set from './set';

describe('set function', () => {
  it('should return null if object is null', () => {
    expect(set(null, 'path', 'value')).toBeNull();
  });

  it('should set value at specified path in object', () => {
    const obj = {};
    set(obj, 'path', 'value');
    expect(obj.path).toBe('value');
  });

  it('should create nested objects if path is a string', () => {
    const obj = {};
    set(obj, 'path.to.value', 'value');
    expect(obj.path.to.value).toBe('value');
  });

  it('should create nested arrays if path is a string with array indices', () => {
    const obj = {};
    set(obj, 'path[0].to.value', 'value');
    expect(obj.path[0].to.value).toBe('value');
  });

  it('should set value at specified path in object if path is an array', () => {
    const obj = {};
    set(obj, ['path', 'to', 'value'], 'value');
    expect(obj.path.to.value).toBe('value');
  });

  it('should create nested objects if path is an array', () => {
    const obj = {};
    set(obj, ['path', 'to', 'value'], 'value');
    expect(obj.path.to.value).toBe('value');
  });

  it('should create nested arrays if path is an array with array indices', () => {
    const obj = {};
    set(obj, ['path', '0', 'to', 'value'], 'value');
    expect(obj.path[0].to.value).toBe('value');
  });

  it('should handle edge cases with empty path', () => {
    const obj = {};
    set(obj, '', 'value');
    expect(obj).toEqual({});
  });
});
