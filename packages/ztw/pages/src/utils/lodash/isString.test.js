/* eslint-disable no-new-wrappers */
// tests/isString.test.js

import isString from './isString';

describe('isString function', () => {
  it('returns true for string literals', () => {
    expect(isString('hello')).toBe(true);
  });

  it('returns true for String objects', () => {
    expect(isString(new String('hello'))).toBe(true);
  });

  it('returns false for numbers', () => {
    expect(isString(123)).toBe(false);
  });

  it('returns false for booleans', () => {
    expect(isString(true)).toBe(false);
  });

  it('returns false for arrays', () => {
    expect(isString([1, 2, 3])).toBe(false);
  });

  it('returns false for objects', () => {
    expect(isString({})).toBe(false);
  });

  it('returns false for null', () => {
    expect(isString(null)).toBe(false);
  });

  it('returns false for undefined', () => {
    expect(isString(undefined)).toBe(false);
  });

  it('returns false for functions', () => {
    expect(isString(() => {})).toBe(false);
  });

  it('returns false for symbols', () => {
    expect(isString(Symbol('hello'))).toBe(false);
  });
});
