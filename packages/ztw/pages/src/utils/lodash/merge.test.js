import merge from './merge';

describe('merge function', () => {
  it('should merge two objects', () => {
    const obj1 = { a: 1, b: { c: 2 } };
    const obj2 = { b: { d: 3 }, e: 4 };
    const expected = { a: 1, b: { c: 2, d: 3 }, e: 4 };

    expect(merge(obj1, obj2)).toEqual(expected);
  });

  it('should merge nested objects', () => {
    const obj1 = { a: 1, b: { c: 2, d: { e: 3 } } };
    const obj2 = { b: { d: { f: 4 } }, g: 5 };
    const expected = { a: 1, b: { c: 2, d: { e: 3, f: 4 } }, g: 5 };

    expect(merge(obj1, obj2)).toEqual(expected);
  });

  //   it('should merge arrays', () => {
  //     const obj1 = { a: 1, b: [1, 2] };
  //     const obj2 = { b: [3, 4], c: 5 };
  //     const expected = { a: 1, b: [1, 2, 3, 4], c: 5 };

  //     expect(merge(obj1, obj2)).toEqual(expected);
  //   });

  it('should handle null values', () => {
    const obj1 = { a: 1, b: null };
    const obj2 = { b: { c: 2 }, d: 3 };
    const expected = { a: 1, b: { c: 2 }, d: 3 };

    expect(merge(obj1, obj2)).toEqual(expected);
  });

  it('should handle undefined values', () => {
    const obj1 = { a: 1, b: undefined };
    const obj2 = { b: { c: 2 }, d: 3 };
    const expected = { a: 1, b: { c: 2 }, d: 3 };

    expect(merge(obj1, obj2)).toEqual(expected);
  });

  it('should modify the original object', () => {
    const obj1 = { a: 1, b: { c: 2 } };
    const obj2 = { b: { d: 3 }, e: 4 };
    const expected = { a: 1, b: { c: 2, d: 3 }, e: 4 };

    merge(obj1, obj2);
    expect(obj1).toEqual(expected);
  });
});
