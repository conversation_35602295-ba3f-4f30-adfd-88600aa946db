/* eslint-disable no-array-constructor */
// isEqual.test.js
import isEqual from './isEqual';

describe('isEqual function', () => {
  it('should return true for the same object reference', () => {
    const obj = { a: 1, b: 2 };
    expect(isEqual(obj, obj)).toBe(true);
  });

  it('should return false for different object references', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1, b: 2 };
    expect(isEqual(obj1, obj2)).toBe(true);
  });

  it('should return false for non-object values', () => {
    expect(isEqual(1, 2)).toBe(false);
    expect(isEqual('a', 'b')).toBe(false);
    expect(isEqual(true, false)).toBe(false);
  });

  it('should return false for objects with different prototypes', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = new Array(1, 2);
    expect(isEqual(obj1, obj2)).toBe(false);
  });

  it('should return true for objects with the same properties', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1, b: 2 };
    expect(isEqual(obj1, obj2)).toBe(true);
  });

  it('should return false for objects with different properties', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1, b: 3 };
    expect(isEqual(obj1, obj2)).toBe(false);
  });

  it('should return false for objects with missing properties', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { a: 1 };
    expect(isEqual(obj1, obj2)).toBe(false);
  });

  it('should return true for arrays with the same elements', () => {
    const arr1 = [1, 2, 3];
    const arr2 = [1, 2, 3];
    expect(isEqual(arr1, arr2)).toBe(true);
  });

  it('should return false for arrays with different elements', () => {
    const arr1 = [1, 2, 3];
    const arr2 = [1, 2, 4];
    expect(isEqual(arr1, arr2)).toBe(false);
  });

  it('should return false for arrays with different lengths', () => {
    const arr1 = [1, 2, 3];
    const arr2 = [1, 2];
    expect(isEqual(arr1, arr2)).toBe(false);
  });

  it('should handle nested objects correctly', () => {
    const obj1 = { a: 1, b: { c: 2, d: 3 } };
    const obj2 = { a: 1, b: { c: 2, d: 3 } };
    expect(isEqual(obj1, obj2)).toBe(true);
  });

  it('should handle nested arrays correctly', () => {
    const arr1 = [1, 2, [3, 4]];
    const arr2 = [1, 2, [3, 4]];
    expect(isEqual(arr1, arr2)).toBe(true);
  });
});
