// keyBy.test.js
import keyBy from './keyBy';

describe('keyBy function', () => {
  it('should return an object with keys generated by the iteratee function', () => {
    const array = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ];

    const result = keyBy(array, 'id');
    expect(result).toEqual({
      1: { id: 1, name: '<PERSON>' },
      2: { id: 2, name: '<PERSON>' },
      3: { id: 3, name: '<PERSON>' },
    });
  });

  it('should return an object with keys generated by the iteratee function when iteratee is a function', () => {
    const array = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ];

    const result = keyBy(array, (item) => `user_${item.id}`);
    expect(result).toEqual({
      user_1: { id: 1, name: '<PERSON>' },
      user_2: { id: 2, name: '<PERSON>' },
      user_3: { id: 3, name: '<PERSON>' },
    });
  });

  it('should return an empty object when the input array is empty', () => {
    const array = [];
    const result = keyBy(array, 'id');
    expect(result).toEqual({});
  });
});
