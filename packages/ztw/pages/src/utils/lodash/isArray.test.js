import isArray from './isArray';

describe('isArray function', () => {
  it('should return true for arrays', () => {
    expect(isArray([1, 2, 3])).toBe(true);
    expect(isArray([])).toBe(true);
    expect(isArray(new Array(5))).toBe(true);
  });

  it('should return false for non-arrays', () => {
    expect(isArray('hello')).toBe(false);
    expect(isArray(123)).toBe(false);
    expect(isArray({})).toBe(false);
    expect(isArray(null)).toBe(false);
    expect(isArray(undefined)).toBe(false);
  });

  it('should handle edge cases', () => {
    expect(isArray(Object.prototype.toString.call([]))).toBe(false);
    expect(isArray(Object.prototype.toString.call({}))).toBe(false);
  });
});
