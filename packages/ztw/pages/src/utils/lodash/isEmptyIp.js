const checkEmptyBraces = (str) => {
  const regex = /\{([\s\n\t]*)\}/; // matches "{ }" with optional whitespace and newline characters inside
  const match = str.match(regex);
  if (match && match[1].trim().length === 0) {
    return true;
  }
  return false;
};

const isEmptyIp = (value) => {
  if (value == null) {
    return true;
  }
  if (typeof value === 'function') {
    return checkEmptyBraces(value.toString());
  }
  if (Array.isArray(value) || typeof value === 'string' || value instanceof String) {
    return value.length === 0 || value === '0.0.0.0';
  }
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
};

export default isEmptyIp;
