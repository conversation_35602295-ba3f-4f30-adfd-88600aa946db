function includes(collection, value, fromIndex = 0) {
  if (fromIndex < 0) return false;
  if (!Array.isArray(collection) && typeof collection !== 'string') {
    throw new TypeError('Expected an array or string');
  }
  for (let i = fromIndex; i < collection.length; i += 1) {
    if (Object.prototype.hasOwnProperty.call(collection, i) && collection[i] === value) {
      return true;
    }
  }
  return false;
}

export default includes;
