// cloneDeep.test.js
import cloneDeep from './cloneDeep';

describe('cloneDeep function', () => {
  it('should return the same value for non-objects', () => {
    expect(cloneDeep(null)).toBe(null);
    expect(cloneDeep(undefined)).toBe(undefined);
    expect(cloneDeep('string')).toBe('string');
    expect(cloneDeep(123)).toBe(123);
    expect(cloneDeep(true)).toBe(true);
  });

  it('should clone objects', () => {
    const original = { a: 1, b: { c: 2 } };
    const cloned = cloneDeep(original);
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned.b).not.toBe(original.b);
  });

  it('should clone arrays', () => {
    const original = [1, 2, { a: 3 }];
    const cloned = cloneDeep(original);
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned[2]).not.toBe(original[2]);
  });

  it('should clone arrays of objects', () => {
    const original = [{ name: 'a' }, { name: 'b' }, { name: 'c' }];
    const cloned = cloneDeep(original);
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned[2]).not.toBe(original[2]);
  });

  it('should handle circular references', () => {
    const original = { a: 1 };
    original.b = original;
    const cloned = cloneDeep(original);
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned.b).toBe(cloned);
  });

  it('should handle nested circular references', () => {
    const original = { a: 1, b: { c: 2 } };
    original.b.d = original;
    const cloned = cloneDeep(original);
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned.b).not.toBe(original.b);
    expect(cloned.b.d).toBe(cloned);
  });
});
