import forEach from './forEach';

describe('forEach function', () => {
  it('should return undefined when collection is null', () => {
    expect(forEach(null, () => {})).toBeUndefined();
  });

  it('should return undefined when collection is undefined', () => {
    expect(forEach(undefined, () => {})).toBeUndefined();
  });

  it('should throw TypeError when iteratee is not a function', () => {
    expect(() => forEach({}, 'not a function')).toThrow(TypeError);
    expect(() => forEach({}, 123)).toThrow(TypeError);
    expect(() => forEach({}, {})).toThrow(TypeError);
    expect(() => forEach({}, [])).toThrow(TypeError);
  });

  it('should call iteratee for each key in the collection', () => {
    const collection = { a: 1, b: 2, c: 3 };
    const iteratee = jest.fn();
    forEach(collection, iteratee);
    expect(iteratee).toHaveBeenCalledTimes(3);
    expect(iteratee).toHaveBeenNthCalledWith(1, 1, 'a', collection);
    expect(iteratee).toHaveBeenNthCalledWith(2, 2, 'b', collection);
    expect(iteratee).toHaveBeenNthCalledWith(3, 3, 'c', collection);
  });

  it('should work with arrays', () => {
    const collection = ['a', 'b', 'c'];
    const iteratee = jest.fn();
    forEach(collection, iteratee);
    expect(iteratee).toHaveBeenCalledTimes(3);
    expect(iteratee).toHaveBeenNthCalledWith(1, 'a', '0', collection);
    expect(iteratee).toHaveBeenNthCalledWith(2, 'b', '1', collection);
    expect(iteratee).toHaveBeenNthCalledWith(3, 'c', '2', collection);
  });
});
