// map.test.js
import map from './map';

describe('map function', () => {
  it('should return an empty array when given an empty array', () => {
    const result = map([], (item) => item);
    expect(result).toEqual([]);
  });

  it('should return a new array with the same length as the original array', () => {
    const originalArray = [1, 2, 3, 4, 5];
    const result = map(originalArray, (item) => item);
    expect(result.length).toBe(originalArray.length);
  });

  it('should apply the iteratee function to each item in the array', () => {
    const originalArray = [1, 2, 3, 4, 5];
    const result = map(originalArray, (item) => item * 2);
    expect(result).toEqual([2, 4, 6, 8, 10]);
  });

  it('should pass the item, index, and array to the iteratee function', () => {
    const originalArray = [1, 2, 3, 4, 5];
    const result = map(originalArray, (item, index, array) => `${item} at index ${index} in array ${array.join(',')}`);
    expect(result).toEqual([
      '1 at index 0 in array 1,2,3,4,5',
      '2 at index 1 in array 1,2,3,4,5',
      '3 at index 2 in array 1,2,3,4,5',
      '4 at index 3 in array 1,2,3,4,5',
      '5 at index 4 in array 1,2,3,4,5',
    ]);
  });
});
