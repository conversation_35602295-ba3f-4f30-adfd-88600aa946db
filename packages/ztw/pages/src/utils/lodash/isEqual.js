/* eslint-disable no-unused-vars */
/* eslint-disable no-restricted-syntax */
function isEqual(value, other) {
  // Check if value and other are the same object reference
  if (value === other) {
    return true;
  }
  // Check if value and other are both objects or arrays
  if (!(value instanceof Object) || !(other instanceof Object)) {
    return false;
  }
  // Check if value and other have the same prototype
  if (value.constructor !== other.constructor) {
    return false;
  }
  // Recursively compare each property of value and other
  for (const prop in value) {
    if (Object.prototype.hasOwnProperty.call(value, prop)) {
      if (!Object.prototype.hasOwnProperty.call(other, prop)) {
        return false;
      }
      if (!isEqual(value[prop], other[prop])) {
        return false;
      }
    }
  }
  for (const prop in other) {
    if (Object.prototype.hasOwnProperty.call(other, prop)
        && !Object.prototype.hasOwnProperty.call(value, prop)) {
      return false;
    }
  }
  return true;
}
  
export default isEqual;
