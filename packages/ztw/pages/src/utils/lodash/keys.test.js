// keys.test.js
import keys from './keys';

describe('keys function', () => {
  it('returns an empty array for null or undefined', () => {
    expect(keys(null)).toEqual([]);
    expect(keys(undefined)).toEqual([]);
  });

  it('convert to object an non-object values', () => {
    expect(keys('string')).toEqual(['0', '1', '2', '3', '4', '5']);
    expect(keys(123)).toEqual([]);
    expect(keys(true)).toEqual([]);
  });

  it('returns keys for object literals', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(keys(obj)).toEqual(['a', 'b', 'c']);
  });

  it('returns keys for objects with inherited properties', () => {
    const parent = { a: 1, b: 2 };
    const child = Object.create(parent);
    child.c = 3;
    expect(keys(child)).toEqual(['c']);
  });

  it('returns keys for objects with Symbol properties', () => {
    const obj = { [Symbol('a')]: 1, [Symbol('b')]: 2 };
    expect(keys(obj)).toEqual([]);
  });

  it('returns keys for objects with numeric properties', () => {
    const obj = { 0: 'a', 1: 'b', 2: 'c' };
    expect(keys(obj)).toEqual(['0', '1', '2']);
  });
});
