import get from './get';

describe('get function', () => {
  it('should return the value at the given path in the object', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(get(object, 'a.b.c')).toBe('value');
  });

  it('should return the default value if the path does not exist in the object', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(get(object, 'a.b.d', 'default')).toBe('default');
  });

  it('should return the default value if the object is not an object', () => {
    const object = 'tring';
    expect(get(object, 'a.b.c', 'default')).toBe('default');
  });

  it('should return the default value if the path is an empty string', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(get(object, '', 'default')).toBe('default');
  });

  it('should return the default value if the path is an empty array', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(get(object, [], 'default')).toBe('default');
  });

  it('should return the value at the given path in the object when the path is an array', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(get(object, ['a', 'b', 'c'])).toBe('value');
  });

  it('should return the value at the given path in the object when the path contains brackets', () => {
    const object = { a: { b: [{ c: 'value' }] } };
    expect(get(object, 'a.b[0].c')).toBe('value');
  });
});
