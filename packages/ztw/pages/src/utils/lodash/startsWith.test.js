// tests/startsWith.test.js

import startsWith from './startsWith';

describe('startsWith function', () => {
  it('should return true when the string starts with the target', () => {
    expect(startsWith('Hello World', 'Hello')).toBe(true);
  });

  it('should return false when the string does not start with the target', () => {
    expect(startsWith('Hello World', 'World')).toBe(false);
  });

  it('should return true when the string starts with the target at a specified position', () => {
    expect(startsWith('Hello World', 'World', 6)).toBe(true);
  });

  it('should return false when the string does not start with the target at a specified position', () => {
    expect(startsWith('Hello World', 'World', 7)).toBe(false);
  });

  it('should return true when the target is an empty string', () => {
    expect(startsWith('Hello World', '')).toBe(true);
  });

  it('should return false when the string is empty and the target is not', () => {
    expect(startsWith('', 'Hello')).toBe(false);
  });

  it('should return true when both the string and the target are empty', () => {
    expect(startsWith('', '')).toBe(true);
  });

  it('should throw an error when the position is negative', () => {
    expect(() => startsWith('Hello World', 'Hello', -1)).toThrowError();
  });

  it('should throw an error when the position is greater than the string length', () => {
    expect(() => startsWith('Hello World', 'Hello', 12)).toThrowError();
  });
});
