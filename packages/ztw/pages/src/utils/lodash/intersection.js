/* eslint-disable no-continue */
function intersection(...arrays) {
  if (arrays.length === 0) {
    return [];
  }
  const result = [];
  const firstArray = arrays[0];
  for (let i = 0; i < firstArray.length; i += 1) {
    const value = firstArray[i];
    if (result.includes(value)) {
      continue;
    }
    let foundInAllArrays = true;
    for (let j = 1; j < arrays.length; j += 1) {
      if (!arrays[j].includes(value)) {
        foundInAllArrays = false;
        break;
      }
    }
    if (foundInAllArrays) {
      result.push(value);
    }
  }
  return result;
}
  
export default intersection;
