import concat from './concat';

describe('concat function', () => {
  it('should concatenate multiple arrays', () => {
    const array1 = [1, 2, 3];
    const array2 = [4, 5, 6];
    const array3 = [7, 8, 9];
    const result = concat(array1, array2, array3);
    expect(result).toEqual([1, 2, 3, 4, 5, 6, 7, 8, 9]);
  });

  it('should concatenate arrays with non-array elements', () => {
    const array1 = [1, 2, 3];
    const array2 = [4, 5, 6];
    const result = concat(array1, array2, 'hello', 10, true);
    expect(result).toEqual([1, 2, 3, 4, 5, 6, 'hello', 10, true]);
  });

  it('should handle empty arrays', () => {
    const array1 = [1, 2, 3];
    const array2 = [];
    const result = concat(array1, array2);
    expect(result).toEqual([1, 2, 3]);
  });

  it('should handle no arguments', () => {
    const result = concat();
    expect(result).toEqual([]);
  });

  it('should handle non-array arguments', () => {
    const result = concat('hello', 10, true);
    expect(result).toEqual(['hello', 10, true]);
  });
});
