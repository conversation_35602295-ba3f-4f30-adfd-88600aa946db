const checkEmptyBraces = (str) => {
  const regex = /\{([\s\n\t]*)\}/; // matches "{ }" with optional whitespace and newline characters inside
  const match = str.match(regex);
  if ((match && match[1].trim().length === 0) || (str.includes('function noop()')) || (str.includes('function i()'))) {
    return true;
  }
  return false;
};

const isEmpty = (value) => {
  if (value == null) {
    return true;
  }
  if (typeof value === 'function') {
    return checkEmptyBraces(value.toString());
  }
  if (Array.isArray(value) || typeof value === 'string' || value instanceof String) {
    return value.length === 0;
  }
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
};

export default isEmpty;
