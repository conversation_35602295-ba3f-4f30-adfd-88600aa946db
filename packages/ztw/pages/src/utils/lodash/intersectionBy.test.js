import intersectionBy from './intersectionBy';

describe('intersectionBy function', () => {
  it('should throw an error if the last argument is not a function', () => {
    expect(() => intersectionBy([1, 2, 3], [2, 3, 4], 'not a function')).toThrowError('Expected a function');
  });

  it('should return an empty array if the first array is empty', () => {
    expect(intersectionBy([], [2, 3, 4], (x) => x)).toEqual([]);
  });

  it('should return an empty array if the second array is empty', () => {
    expect(intersectionBy([1, 2, 3], [], (x) => x)).toEqual([]);
  });

  it('should return an empty array if there are no common elements', () => {
    expect(intersectionBy([1, 2, 3], [4, 5, 6], (x) => x)).toEqual([]);
  });

  it('should return the common elements if they exist', () => {
    expect(intersectionBy([1, 2, 3], [2, 3, 4], (x) => x)).toEqual([2, 3]);
  });
});
