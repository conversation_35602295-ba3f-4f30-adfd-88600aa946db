// isNull.test.js
import isNull from './isNull';

describe('isNull function', () => {
  it('returns true for null values', () => {
    expect(isNull(null)).toBe(true);
  });

  it('returns true for undefined values', () => {
    expect(isNull(undefined)).toBe(true);
  });

  it('returns false for defined values', () => {
    expect(isNull('')).toBe(false);
    expect(isNull(0)).toBe(false);
    expect(isNull(false)).toBe(false);
    expect(isNull({})).toBe(false);
    expect(isNull([])).toBe(false);
  });

  it('returns false for NaN values', () => {
    expect(isNull(NaN)).toBe(false);
  });

  it('returns false for function values', () => {
    expect(isNull(() => {})).toBe(false);
  });
});
