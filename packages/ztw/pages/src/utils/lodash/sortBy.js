/* eslint-disable array-callback-return */
/* eslint-disable no-param-reassign */
function sortBy(arr, key) {
  // Check if the input array is valid
  if (!Array.isArray(arr)) {
    throw new Error('Input is not an array');
  }

  // Check if the key is a string or a function
  if (typeof key !== 'string' && typeof key !== 'function') {
    throw new Error('Key must be a string or a function');
  }

  // Use the Array.prototype.sort() method to sort the array
  return arr.sort((a, b) => {
    // If the key is a string, use it to access the property of the objects
    if (typeof key === 'string') {
      const aValue = a[key];
      const bValue = b[key];

      // If the values are equal, return 0
      if (aValue === bValue) {
        return 0;
      }

      // If the values are not equal, return -1 or 1 based on their order
      return aValue < bValue ? -1 : 1;
    }

    // If the key is a function, use it to compute the values to compare
    if (typeof key === 'function') {
      const aValue = key(a);
      const bValue = key(b);

      // If the values are equal, return 0
      if (aValue === bValue) {
        return 0;
      }

      // If the values are not equal, return -1 or 1 based on their order
      return aValue < bValue ? -1 : 1;
    }
  });
}
export default sortBy;
