import sortBy from './sortBy';

describe('sortBy function', () => {
  it('should throw an error if the input is not an array', () => {
    expect(() => sortBy('not an array', 'key')).toThrowError('Input is not an array');
    expect(() => sortBy({}, 'key')).toThrowError('Input is not an array');
    expect(() => sortBy(null, 'key')).toThrowError('Input is not an array');
    expect(() => sortBy(undefined, 'key')).toThrowError('Input is not an array');
  });

  it('should throw an error if the key is not a string or a function', () => {
    expect(() => sortBy([], 123)).toThrowError('Key must be a string or a function');
    expect(() => sortBy([], true)).toThrowError('Key must be a string or a function');
    expect(() => sortBy([], null)).toThrowError('Key must be a string or a function');
    expect(() => sortBy([], undefined)).toThrowError('Key must be a string or a function');
  });

  it('should sort an array of objects by a string key', () => {
    const arr = [
      { name: 'John', age: 30 },
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 35 },
    ];

    const sortedArr = sortBy(arr, 'age');

    expect(sortedArr).toEqual([
      { name: 'Alice', age: 25 },
      { name: 'John', age: 30 },
      { name: 'Bob', age: 35 },
    ]);
  });

  it('should sort an array of objects by a function key', () => {
    const arr = [
      { name: 'John', age: 30 },
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 35 },
    ];

    const sortedArr = sortBy(arr, (obj) => obj.age);

    expect(sortedArr).toEqual([
      { name: 'Alice', age: 25 },
      { name: 'John', age: 30 },
      { name: 'Bob', age: 35 },
    ]);
  });

  it('should sort an array of primitive values by a function key', () => {
    const arr = [3, 1, 2];

    const sortedArr = sortBy(arr, (val) => val);

    expect(sortedArr).toEqual([1, 2, 3]);
  });

  it('should return the original array if it is already sorted', () => {
    const arr = [
      { name: 'Alice', age: 25 },
      { name: 'John', age: 30 },
      { name: 'Bob', age: 35 },
    ];

    const sortedArr = sortBy(arr, 'age');

    expect(sortedArr).toBe(arr);
  });
});
