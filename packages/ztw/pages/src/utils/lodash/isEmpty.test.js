/* eslint-disable func-names */
import isEmpty from './isEmpty';

describe('isEmpty function', () => {
  it('should return true for null values', () => {
    expect(isEmpty(null)).toBe(true);
  });

  it('should return true for undefined values', () => {
    expect(isEmpty(undefined)).toBe(true);
  });

  it('should return true for empty strings', () => {
    expect(isEmpty('')).toBe(true);
  });

  it('should return true for empty arrays', () => {
    expect(isEmpty([])).toBe(true);
  });

  it('should return true for empty objects', () => {
    expect(isEmpty({})).toBe(true);
  });

  it('should return true for functions with empty braces', () => {
    const func = function () {};
    expect(isEmpty(func)).toBe(true);
  });

  it('should return true for functions with whitespace inside braces', () => {
    const func = function noop() {};
    expect(isEmpty(func)).toBe(true);
  });

  it('should return true for functions with newline characters inside braces', () => {
    const func = function noop() {
      '\n';
    };
    expect(isEmpty(func)).toBe(true);
  });

  it('should return true for functions with noop', () => {
    const func = function noop() {};
    expect(isEmpty(func)).toBe(true);
  });

  it('should return false for non-empty strings', () => {
    expect(isEmpty('hello')).toBe(false);
  });

  it('should return false for non-empty arrays', () => {
    expect(isEmpty([1, 2, 3])).toBe(false);
  });

  it('should return false for non-empty objects', () => {
    expect(isEmpty({ a: 1, b: 2 })).toBe(false);
  });

  it('should return false for functions with non-empty braces', () => {
    const func = function () { return 'hello'; };
    expect(isEmpty(func)).toBe(false);
  });
});
