// range.test.js
import range from './range';

describe('range function', () => {
  it('should return an array of numbers from start to end with a step of 1', () => {
    expect(range(1, 5)).toEqual([1, 2, 3, 4]);
  });

  it('should return an array of numbers from 0 to end with a step of 1 when start is not provided', () => {
    expect(range(5)).toEqual([0, 1, 2, 3, 4]);
  });

  it('should return an array of numbers from start to end with a custom step', () => {
    expect(range(1, 10, 2)).toEqual([1, 3, 5, 7, 9]);
  });

  it('should return an empty array when start is greater than end', () => {
    expect(range(5, 1)).toEqual([]);
  });

  it('should return an empty array when start and end are equal', () => {
    expect(range(5, 5)).toEqual([]);
  });

  it('should return an array with a single element when start and end are equal and step is greater than 1', () => {
    expect(range(5, 5, 2)).toEqual([]);
  });
});
