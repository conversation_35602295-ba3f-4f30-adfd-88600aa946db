// each.test.js
import each from './each';

describe('each function', () => {
  it('should iterate over an array', () => {
    const array = [1, 2, 3];
    const iteratee = jest.fn();
    each(array, iteratee);
    expect(iteratee).toHaveBeenCalledTimes(3);
    expect(iteratee).toHaveBeenNthCalledWith(1, 1, 0, array);
    expect(iteratee).toHaveBeenNthCalledWith(2, 2, 1, array);
    expect(iteratee).toHaveBeenNthCalledWith(3, 3, 2, array);
  });

  it('should iterate over an object', () => {
    const object = { a: 1, b: 2, c: 3 };
    const iteratee = jest.fn();
    each(object, iteratee);
    expect(iteratee).toHaveBeenCalledTimes(3);
    expect(iteratee).toHaveBeenNthCalledWith(1, 1, 'a', object);
    expect(iteratee).toHaveBeenNthCalledWith(2, 2, 'b', object);
    expect(iteratee).toHaveBeenNthCalledWith(3, 3, 'c', object);
  });

  it('should throw an error for non-array and non-object', () => {
    expect(() => each('string', () => {})).toThrowError('Expected an array or object');
    expect(() => each(123, () => {})).toThrowError('Expected an array or object');
    expect(() => each(null, () => {})).toThrowError('Expected an array or object');
    expect(() => each(undefined, () => {})).toThrowError('Expected an array or object');
  });

  it('should return the original collection', () => {
    const array = [1, 2, 3];
    const object = { a: 1, b: 2, c: 3 };
    expect(each(array, () => {})).toBe(array);
    expect(each(object, () => {})).toBe(object);
  });
});
