// tests/isNil.test.js

import isNil from './isNil';

describe('isNil function', () => {
  it('returns true for null', () => {
    expect(isNil(null)).toBe(true);
  });

  it('returns true for undefined', () => {
    expect(isNil(undefined)).toBe(true);
  });

  it('returns false for a number', () => {
    expect(isNil(42)).toBe(false);
  });

  it('returns false for a string', () => {
    expect(isNil('hello')).toBe(false);
  });

  it('returns false for an object', () => {
    expect(isNil({})).toBe(false);
  });

  it('returns false for an array', () => {
    expect(isNil([])).toBe(false);
  });

  it('returns false for a boolean', () => {
    expect(isNil(true)).toBe(false);
  });

  it('returns false for NaN', () => {
    expect(isNil(NaN)).toBe(false);
  });

  it('returns false for a function', () => {
    expect(isNil(() => {})).toBe(false);
  });
});
