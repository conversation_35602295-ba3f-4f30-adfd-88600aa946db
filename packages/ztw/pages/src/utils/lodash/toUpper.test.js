// toUpper.test.js

import toUpper from './toUpper';

describe('toUpper function', () => {
  it('should return an empty string when input is null', () => {
    expect(toUpper(null)).toBe('');
  });

  it('should return an empty string when input is undefined', () => {
    expect(toUpper(undefined)).toBe('');
  });

  it('should convert a string to uppercase', () => {
    expect(toUpper('hello world')).toBe('HELLO WORLD');
  });

  it('should convert a number to uppercase string', () => {
    expect(toUpper(123)).toBe('123');
  });

  it('should convert a boolean to uppercase string', () => {
    expect(toUpper(true)).toBe('TRUE');
  });

  it('should convert an object to uppercase string', () => {
    expect(toUpper({})).toBe('[OBJECT OBJECT]');
  });

  it('should convert an array to uppercase string', () => {
    expect(toUpper([1, 2, 3])).toBe('1,2,3');
  });
});
