/* eslint-disable no-param-reassign */
const get = (object, path = '', defaultValue) => {
  path = Array.isArray(path) ? path : path.replace(/(\[(\d+)\])/g, '.$2').replace(/^\./, '').split('.');
  if (typeof object !== 'object') {
    return defaultValue;
  }
  if (!(path[0] in object)) {
    return defaultValue;
  }
  object = object[path[0]];
  if (object && path.length > 1) {
    return get(object, path.slice(1), defaultValue);
  }
  return object;
};

export default get;
