import orderBy from './orderBy';

describe('orderBy function', () => {
  it('should return an empty array when the collection is not an array', () => {
    expect(orderBy('not an array', 'iteratee')).toEqual([]);
    expect(orderBy({}, 'iteratee')).toEqual([]);
    expect(orderBy(null, 'iteratee')).toEqual([]);
    expect(orderBy(undefined, 'iteratee')).toEqual([]);
  });

  it('should sort the collection in ascending order by default', () => {
    const collection = [
      { name: '<PERSON>', age: 30 },
      { name: '<PERSON>', age: 25 },
      { name: '<PERSON>', age: 35 },
    ];
    const result = orderBy(collection, 'age');
    expect(result).toEqual([
      { name: '<PERSON>', age: 25 },
      { name: '<PERSON>', age: 30 },
      { name: '<PERSON>', age: 35 },
    ]);
  });

  it('should sort the collection in descending order when specified', () => {
    const collection = [
      { name: '<PERSON>', age: 30 },
      { name: '<PERSON>', age: 25 },
      { name: '<PERSON>', age: 35 },
    ];
    const result = orderBy(collection, 'age', 'desc');
    expect(result).toEqual([
      { name: '<PERSON>', age: 35 },
      { name: '<PERSON>', age: 30 },
      { name: 'Alice', age: 25 },
    ]);
  });
  // Not supported
  //   it('should sort the collection by multiple iteratees', () => {
  //     const collection = [
  //       { name: 'John', age: 30, country: 'USA' },
  //       { name: 'Alice', age: 25, country: 'USA' },
  //       { name: 'Bob', age: 35, country: 'Canada' },
  //       { name: 'Charlie', age: 30, country: 'Canada' },
  //     ];
  //     const result = orderBy(collection, ['country', 'age']);
  //     expect(result).toEqual([
  //       { name: 'Alice', age: 25, country: 'USA' },
  //       { name: 'John', age: 30, country: 'USA' },
  //       { name: 'Charlie', age: 30, country: 'Canada' },
  //       { name: 'Bob', age: 35, country: 'Canada' },
  //     ]);
  //   });

  // Not supported
  //   it('should sort the collection by multiple iteratees with different orders', () => {
  //     const collection = [
  //       { name: 'John', age: 30, country: 'USA' },
  //       { name: 'Alice', age: 25, country: 'USA' },
  //       { name: 'Bob', age: 35, country: 'Canada' },
  //       { name: 'Charlie', age: 30, country: 'Canada' },
  //     ];
  //     const result = orderBy(collection, ['country', 'age'], ['asc', 'desc']);
  //     expect(result).toEqual([
  //       { name: 'Alice', age: 25, country: 'USA' },
  //       { name: 'John', age: 30, country: 'USA' },
  //       { name: 'Bob', age: 35, country: 'Canada' },
  //       { name: 'Charlie', age: 30, country: 'Canada' },
  //     ]);
  //   });

  it('should sort the collection by nested properties', () => {
    const collection = [
      { user: { name: 'John', age: 30 } },
      { user: { name: 'Alice', age: 25 } },
      { user: { name: 'Bob', age: 35 } },
    ];
    const result = orderBy(collection, 'user.age');
    expect(result).toEqual([
      { user: { name: 'Alice', age: 25 } },
      { user: { name: 'John', age: 30 } },
      { user: { name: 'Bob', age: 35 } },
    ]);
  });
});
