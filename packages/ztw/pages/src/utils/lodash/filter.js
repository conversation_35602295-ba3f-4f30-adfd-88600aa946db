/* eslint-disable no-unused-vars */
/* eslint-disable no-restricted-syntax */
function filter(collection, predicate) {
  if (!Array.isArray(collection) && typeof collection !== 'object') {
    return [];
  }
  const result = [];
  for (const key in collection) {
    if (Object.prototype.hasOwnProperty.call(collection, key)) {
      const value = collection[key];
      if (predicate(value, key, collection)) {
        result.push(value);
      }
    }
  }
  return result;
}

export default filter;
