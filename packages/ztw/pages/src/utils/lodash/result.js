function result(object, path, defaultValue) {
  // Convert path to an array if it's a string
  const keys = Array.isArray(path) ? path : path.split('.');
    
  // Navigate through the object using the keys
  let value = object;
  // eslint-disable-next-line no-restricted-syntax
  for (const key of keys) {
    if (value == null) {
      return defaultValue; // Return default if value is null or undefined
    }
    value = value[key];
  }
    
  // If the resolved value is a function, call it; otherwise, return the value
  // eslint-disable-next-line no-nested-ternary
  return typeof value === 'function' ? value.call(object) : value === undefined ? defaultValue : value;
}
  
export default result;
