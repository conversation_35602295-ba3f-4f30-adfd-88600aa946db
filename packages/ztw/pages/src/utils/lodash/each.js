/* eslint-disable no-unused-vars */
/* eslint-disable no-restricted-syntax */
function each(collection, iteratee) {
  if (Array.isArray(collection)) {
    for (let i = 0; i < collection.length; i += 1) {
      iteratee(collection[i], i, collection);
    }
  } else if (typeof collection === 'object' && collection !== null) {
    for (const key in collection) {
      if (Object.prototype.hasOwnProperty.call(collection, key)) {
        iteratee(collection[key], key, collection);
      }
    }
  } else {
    throw new TypeError('Expected an array or object');
  }
  return collection;
}

export default each;
