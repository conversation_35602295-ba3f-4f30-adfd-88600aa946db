// times.test.js

import times from './times';

describe('times function', () => {
  it('should return an array of the correct length', () => {
    const result = times(5, () => {});
    expect(result.length).toBe(5);
  });

  it('should call the iteratee function the correct number of times', () => {
    const iteratee = jest.fn();
    times(3, iteratee);
    expect(iteratee).toHaveBeenCalledTimes(3);
  });

  it('should pass the correct index to the iteratee function', () => {
    const iteratee = jest.fn();
    times(3, iteratee);
    expect(iteratee).toHaveBeenNthCalledWith(1, 0);
    expect(iteratee).toHaveBeenNthCalledWith(2, 1);
    expect(iteratee).toHaveBeenNthCalledWith(3, 2);
  });

  it('should return an array with the correct values', () => {
    const result = times(3, (i) => i * 2);
    expect(result).toEqual([0, 2, 4]);
  });

  it('should handle edge cases', () => {
    expect(times(0, () => {})).toEqual([]);
    expect(times(-1, () => {})).toEqual([]);
  });
});
