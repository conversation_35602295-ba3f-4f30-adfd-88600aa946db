function set(object, path, value) {
  if (object == null) {
    return object;
  }

  const pathArray = Array.isArray(path) ? path : path.split(/[[\].]/).filter(Boolean);
  const lastIndex = pathArray.length - 1;

  pathArray.reduce((acc, key, index) => {
    if (index === lastIndex) {
      acc[key] = value;
    } else {
      const nextKey = pathArray[index + 1];
      const isArrayIndex = /^\d+$/.test(nextKey);
      if (Array.isArray(acc[key])) {
        if (isArrayIndex) {
          acc[key][nextKey] = {};
        }
      } else if (typeof acc[key] !== 'object' || acc[key] === null) {
        acc[key] = isArrayIndex ? [] : {};
      }
    }
    return acc[key];
  }, object);

  return object;
}
  
export default set;
