/* eslint-disable no-unused-vars */
/* eslint-disable no-restricted-syntax */

function intersectionBy(...arrays) {
  const iteratee = arrays.pop();
  if (typeof iteratee !== 'function') {
    throw new TypeError('Expected a function');
  }
  const uniqueValues = new Set();
  const result = [];
  for (const value of arrays[0]) {
    const computedValue = iteratee(value);
    if (!uniqueValues.has(computedValue)) {
      uniqueValues.add(computedValue);
    }
  }
  for (let i = 1; i < arrays.length; i += 1) {
    for (const value of arrays[i]) {
      const computedValue = iteratee(value);
      if (uniqueValues.has(computedValue)) {
        result.push(value);
        uniqueValues.delete(computedValue);
      }
    }
  }
  return result;
}
  
export default intersectionBy;
