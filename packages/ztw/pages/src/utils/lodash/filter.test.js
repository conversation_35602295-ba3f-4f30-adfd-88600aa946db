/* eslint-disable no-shadow */
// filter.test.js
import filter from './filter';

describe('filter function', () => {
  it('should return an empty array when the collection is not an array or object', () => {
    expect(filter(null, () => true)).toEqual([]);
    expect(filter(undefined, () => true)).toEqual([]);
    expect(filter('string', () => true)).toEqual([]);
    expect(filter(123, () => true)).toEqual([]);
  });

  it('should return an empty array when the predicate is always false', () => {
    const collection = { a: 1, b: 2, c: 3 };
    expect(filter(collection, () => false)).toEqual([]);
  });

  it('should return the original collection when the predicate is always true', () => {
    const collection = { a: 1, b: 2, c: 3 };
    expect(filter(collection, () => true)).toEqual([1, 2, 3]);
  });

  it('should filter the collection based on the predicate', () => {
    const collection = {
      a: 1, b: 2, c: 3, d: 4,
    };
    expect(filter(collection, (value) => value % 2 === 0)).toEqual([2, 4]);
  });

  it('should filter the collection based on the key', () => {
    const collection = {
      a: 1, b: 2, c: 3, d: 4,
    };
    expect(filter(collection, (value, key) => key === 'a' || key === 'c')).toEqual([1, 3]);
  });

  it('should filter the collection based on the collection itself', () => {
    const collection = {
      a: 1, b: 2, c: 3, d: 4,
    };
    expect(filter(collection, (value, key, collection) => collection[key] === value)).toEqual([1, 2, 3, 4]);
  });

  it('should work with arrays', () => {
    const collection = [1, 2, 3, 4];
    expect(filter(collection, (value) => value % 2 === 0)).toEqual([2, 4]);
  });
});
