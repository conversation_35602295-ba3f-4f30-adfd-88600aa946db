/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */

function orderBy(collection, iteratees, orders) {
  if (!Array.isArray(collection)) {
    return [];
  }
  if (!Array.isArray(iteratees)) {
    iteratees = [iteratees];
  }
  if (!Array.isArray(orders)) {
    orders = orders ? [orders] : iteratees.map(() => 'asc');
  }
  const compare = (a, b, iteratees, orders, index = 0) => {
    const iteratee = iteratees[index];
    const order = orders[index];
    const aProperties = iteratee && iteratee.split('.'); // split the property string by '.' to get nested properties
    const bProperties = iteratee && iteratee.split('.');
    let aValue = a;
    let bValue = b;
    for (let i = 0; i < (aProperties && aProperties.length); i += 1) {
      aValue = aValue[aProperties[i]] || ''; // access nested properties using the array notation
      bValue = bValue[bProperties[i]] || '';
    }
    if (aValue === bValue) {
      if (index + 1 < (iteratee && iteratees.length)) {
        return compare(a, b, iteratees, orders, index + 1);
      }
      return 0;
    }
    const result = aValue < bValue ? -1 : 1;
    return order === 'desc' ? -result : result;
  };
  
  return collection.slice().sort((a, b) => compare(a, b, iteratees, orders));
}
  
export default orderBy;
