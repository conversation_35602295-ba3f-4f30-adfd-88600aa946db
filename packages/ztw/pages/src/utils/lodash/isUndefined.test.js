// tests/isUndefined.test.js

import isUndefined from './isUndefined';

describe('isUndefined function', () => {
  it('should return true for undefined values', () => {
    expect(isUndefined(undefined)).toBe(true);
  });

  it('should return false for defined values', () => {
    expect(isUndefined(null)).toBe(false);
    expect(isUndefined(true)).toBe(false);
    expect(isUndefined(false)).toBe(false);
    expect(isUndefined('')).toBe(false);
    expect(isUndefined(0)).toBe(false);
    expect(isUndefined({})).toBe(false);
    expect(isUndefined([])).toBe(false);
  });

  it('should return false for NaN', () => {
    expect(isUndefined(NaN)).toBe(false);
  });

  it('should return false for Infinity', () => {
    expect(isUndefined(Infinity)).toBe(false);
  });

  it('should return false for -Infinity', () => {
    expect(isUndefined(-Infinity)).toBe(false);
  });
});
