/* eslint-disable no-console */
// tests/isEmptyIp.test.js

import isEmptyIp from './isEmptyIp';

describe('isEmptyIp function', () => {
  it('returns true for null values', () => {
    expect(isEmptyIp(null)).toBe(true);
  });

  it('returns true for undefined values', () => {
    expect(isEmptyIp(undefined)).toBe(true);
  });

  it('returns true for empty strings', () => {
    expect(isEmptyIp('')).toBe(true);
  });

  it('returns true for "0.0.0.0" string', () => {
    expect(isEmptyIp('0.0.0.0')).toBe(true);
  });

  it('returns true for empty arrays', () => {
    expect(isEmptyIp([])).toBe(true);
  });

  it('returns true for empty objects', () => {
    expect(isEmptyIp({})).toBe(true);
  });

  it('returns true for functions with empty braces', () => {
    const func = () => {};
    expect(isEmptyIp(func)).toBe(true);
  });

  it('returns false for non-empty strings', () => {
    expect(isEmptyIp('hello')).toBe(false);
  });

  it('returns false for non-empty arrays', () => {
    expect(isEmptyIp([1, 2, 3])).toBe(false);
  });

  it('returns false for non-empty objects', () => {
    expect(isEmptyIp({ a: 1 })).toBe(false);
  });

  it('returns false for functions with non-empty braces', () => {
    const func = () => { console.log('hello'); };
    expect(isEmptyIp(func)).toBe(false);
  });

  it('returns false for numbers', () => {
    expect(isEmptyIp(123)).toBe(false);
  });

  it('returns false for booleans', () => {
    expect(isEmptyIp(true)).toBe(false);
  });
});
