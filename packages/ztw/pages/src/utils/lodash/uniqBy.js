/* eslint-disable arrow-parens */
function uniqBy(array, iteratee) {
  if (array == null || !Array.isArray(array)) {
    return [];
  }

  let fn;
  if (typeof iteratee === 'function') {
    fn = iteratee;
  } else if (typeof iteratee === 'string') {
    fn = (value) => value[iteratee];
  } else {
    fn = (value) => value;
  }

  const seen = new Set();
  return array.filter((value) => {
    const computed = fn(value);
    if (seen.has(computed)) {
      return false;
    }
    seen.add(computed);
    return true;
  });
}

export default uniqBy;
