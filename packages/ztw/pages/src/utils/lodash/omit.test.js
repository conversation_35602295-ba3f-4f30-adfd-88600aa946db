// omit.test.js

import omit from './omit';

describe('omit function', () => {
  it('should return the original value if it is not an object', () => {
    expect(omit('hello')).toBe('hello');
    expect(omit(123)).toBe(123);
    expect(omit(true)).toBe(true);
    // expect(omit(null)).toBe(null);
    expect(omit(undefined)).toBe(undefined);
  });

  it('should return an empty object if all keys are omitted', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(omit(obj, 'a', 'b', 'c')).toEqual({});
  });

  it('should omit the specified keys from the object', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(omit(obj, 'a', 'c')).toEqual({ b: 2 });
  });

  // NESTED is NOT supported

  // it('should omit nested keys from the object', () => {
  //   const obj = { a: 1, b: { c: 2, d: 3 } };
  //   expect(omit(obj, 'b.c')).toEqual({ a: 1, b: { d: 3 } });
  // });

  // it('should handle multiple levels of nested keys', () => {
  //   const obj = { a: 1, b: { c: 2, d: { e: 3, f: 4 } } };
  //   expect(omit(obj, 'a', 'b.c', 'b.d.e')).toEqual({ b: { d: { f: 4 } } });
  // });

  it('should handle arrays of keys to omit', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(omit(obj, ['a', 'c'])).toEqual({ b: 2 });
  });

  it('should handle multiple arrays of keys to omit', () => {
    const obj = {
      a: 1, b: 2, c: 3, d: 4,
    };
    expect(omit(obj, ['a', 'c'], ['d'])).toEqual({ b: 2 });
  });
});
