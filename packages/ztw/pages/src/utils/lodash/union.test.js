import union from './union';

describe('union function', () => {
  it('should return an empty array when no arguments are provided', () => {
    expect(union()).toEqual([]);
  });

  it('should return a single array when only one array is provided', () => {
    expect(union([1, 2, 3])).toEqual([1, 2, 3]);
  });

  it('should return a union of two arrays', () => {
    expect(union([1, 2, 3], [3, 4, 5])).toEqual([1, 2, 3, 4, 5]);
  });

  it('should return a union of multiple arrays', () => {
    expect(union([1, 2, 3], [3, 4, 5], [5, 6, 7])).toEqual([1, 2, 3, 4, 5, 6, 7]);
  });

  it('should ignore non-array arguments', () => {
    expect(union([1, 2, 3], 'hello', [3, 4, 5], null, [5, 6, 7])).toEqual([1, 2, 3, 4, 5, 6, 7]);
  });

  it('should handle arrays with duplicate values', () => {
    expect(union([1, 2, 2, 3], [3, 3, 4, 5])).toEqual([1, 2, 3, 4, 5]);
  });

  it('should handle arrays with different data types', () => {
    expect(union([1, 'a', true], [true, 'b', 2])).toEqual([1, 'a', true, 'b', 2]);
  });
});
