import intersection from './intersection';

describe('intersection function', () => {
  it('should return an empty array when no input arrays are provided', () => {
    expect(intersection()).toEqual([]);
  });

  it('should return an empty array when input arrays are empty', () => {
    expect(intersection([], [])).toEqual([]);
  });

  it('should return the intersection of two arrays', () => {
    expect(intersection([1, 2, 3], [2, 3, 4])).toEqual([2, 3]);
  });

  it('should return the intersection of multiple arrays', () => {
    expect(intersection([1, 2, 3], [2, 3, 4], [2, 4, 5])).toEqual([2]);
  });

  it('should return the intersection of arrays with duplicate values', () => {
    expect(intersection([1, 2, 2, 3], [2, 2, 3, 4])).toEqual([2, 3]);
  });

  it('should return the intersection of arrays with non-numeric values', () => {
    expect(intersection(['a', 'b', 'c'], ['b', 'c', 'd'])).toEqual(['b', 'c']);
  });

  it('should return the intersection of arrays with mixed data types', () => {
    expect(intersection([1, 'a', true], [1, 'a', false])).toEqual([1, 'a']);
  });
});
