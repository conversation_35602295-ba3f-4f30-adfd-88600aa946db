// without.test.js
import without from './without';

describe('without function', () => {
  it('should return an empty array when the input array is empty', () => {
    expect(without([])).toEqual([]);
  });

  it('should return the original array when no values are provided', () => {
    const originalArray = [1, 2, 3, 4, 5];
    expect(without(originalArray)).toEqual(originalArray);
  });

  it('should remove a single value from the array', () => {
    const originalArray = [1, 2, 3, 4, 5];
    expect(without(originalArray, 3)).toEqual([1, 2, 4, 5]);
  });

  it('should remove multiple values from the array', () => {
    const originalArray = [1, 2, 3, 4, 5];
    expect(without(originalArray, 2, 4)).toEqual([1, 3, 5]);
  });

  it('should not modify the original array', () => {
    const originalArray = [1, 2, 3, 4, 5];
    const result = without(originalArray, 2, 4);
    expect(originalArray).toEqual([1, 2, 3, 4, 5]);
    expect(result).toEqual([1, 3, 5]);
  });

  it('should handle duplicate values in the input array', () => {
    const originalArray = [1, 2, 2, 3, 4, 4, 5];
    expect(without(originalArray, 2, 4)).toEqual([1, 3, 5]);
  });

  it('should handle duplicate values in the values to remove', () => {
    const originalArray = [1, 2, 3, 4, 5];
    expect(without(originalArray, 2, 2, 4, 4)).toEqual([1, 3, 5]);
  });
});
