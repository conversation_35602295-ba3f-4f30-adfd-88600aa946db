/* eslint-disable no-unused-vars */
/* eslint-disable no-restricted-syntax */
function cloneDeep(value, hash = new WeakMap()) {
  if (typeof value !== 'object' || value === null) {
    return value;
  }
  if (hash.has(value)) {
    return hash.get(value);
  }
  const clone = Array.isArray(value) ? [] : {};
  hash.set(value, clone);
  for (const key in value) {
    if (Object.prototype.hasOwnProperty.call(value, key)) {
      clone[key] = cloneDeep(value[key], hash);
    }
  }
  return clone;
}

export default cloneDeep;
