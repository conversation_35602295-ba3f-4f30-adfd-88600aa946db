// debounce.test.js
import debounce from './debounce';

describe('debounce function', () => {
  jest.useFakeTimers();

  it('should call the function after the specified wait time', () => {
    const func = jest.fn();
    const debouncedFunc = debounce(func, 100);

    debouncedFunc('arg1', 'arg2');

    expect(func).not.toHaveBeenCalled();

    jest.advanceTimersByTime(99);
    expect(func).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1);
    expect(func).toHaveBeenCalledTimes(1);
    expect(func).toHaveBeenCalledWith('arg1', 'arg2');
  });

  it('should not call the function multiple times if called multiple times within the wait time', () => {
    const func = jest.fn();
    const debouncedFunc = debounce(func, 100);

    debouncedFunc('arg1', 'arg2');
    debouncedFunc('arg3', 'arg4');

    jest.advanceTimersByTime(100);
    expect(func).toHaveBeenCalledTimes(1);
    expect(func).toHaveBeenCalledWith('arg3', 'arg4');
  });

  it('should call the function with the correct context', () => {
    const func = jest.fn();
    const context = { foo: 'bar' };
    const debouncedFunc = debounce(func, 100);

    debouncedFunc.call(context, 'arg1', 'arg2');

    jest.advanceTimersByTime(100);
    expect(func).toHaveBeenCalledTimes(1);
    expect(func.mock.instances[0]).toBe(context);
  });

  afterEach(() => {
    jest.clearAllTimers();
  });
});
