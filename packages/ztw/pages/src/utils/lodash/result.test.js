// result.test.js
import result from './result';

describe('result function', () => {
  it('should return the value at the specified path in the object', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(result(object, 'a.b.c')).toBe('value');
  });

  it('should return the default value if the path does not exist in the object', () => {
    const object = { a: { b: {} } };
    expect(result(object, 'a.b.c', 'default')).toBe('default');
  });

  it('should call the function at the specified path and return its result', () => {
    const object = { a: { b: () => 'function result' } };
    expect(result(object, 'a.b')).toBe('function result');
  });

  it('should work with array paths', () => {
    const object = { a: { b: { c: 'value' } } };
    expect(result(object, ['a', 'b', 'c'])).toBe('value');
  });
});
