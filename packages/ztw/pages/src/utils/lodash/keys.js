/* eslint-disable eqeqeq */
/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */

function keys(obj) {
  if (typeof obj !== 'object') {
    obj = Object(obj);
  }
  if (obj == null || obj == undefined) {
    return [];
  }
  const keys = [];
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      keys.push(key);
    }
  }
  return keys;
}

export default keys;
