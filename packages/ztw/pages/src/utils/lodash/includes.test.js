// includes.test.js
import includes from './includes';

describe('includes function', () => {
  it('should return true if the value is found in the array', () => {
    expect(includes([1, 2, 3], 2)).toBe(true);
  });

  it('should return false if the value is not found in the array', () => {
    expect(includes([1, 2, 3], 4)).toBe(false);
  });

  it('should return true if the value is found in the string', () => {
    expect(includes('hello', 'l')).toBe(true);
  });

  it('should return false if the value is not found in the string', () => {
    expect(includes('hello', 'x')).toBe(false);
  });

  it('should throw an error if the collection is not an array or string', () => {
    expect(() => includes({}, 'a')).toThrowError('Expected an array or string');
  });

  it('should return true if the value is found in the array from the specified index', () => {
    expect(includes([1, 2, 3], 3, 2)).toBe(true);
  });

  it('should return false if the value is not found in the array from the specified index', () => {
    expect(includes([1, 2, 3], 1, 2)).toBe(false);
  });

  it('should return true if the value is found in the string from the specified index', () => {
    expect(includes('hello', 'l', 2)).toBe(true);
  });

  it('should return false if the value is not found in the string from the specified index', () => {
    expect(includes('hello', 'h', 1)).toBe(false);
  });

  it('should return false if the fromIndex is greater than the length of the collection', () => {
    expect(includes([1, 2, 3], 1, 5)).toBe(false);
  });

  it('should return false if the fromIndex is negative', () => {
    expect(includes([1, 2, 3], 1, -1)).toBe(false);
  });
});
