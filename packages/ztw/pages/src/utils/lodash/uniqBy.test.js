import uniqBy from './uniqBy';

describe('uniqBy function', () => {
  it('should return an empty array when input is null or not an array', () => {
    expect(uniqBy(null, 'id')).toEqual([]);
    expect(uniqBy({}, 'id')).toEqual([]);
    expect(uniqBy('string', 'id')).toEqual([]);
    expect(uniqBy(123, 'id')).toEqual([]);
  });

  it('should return the original array when iteratee is not provided', () => {
    const array = [1, 2, 3, 4, 5];
    expect(uniqBy(array)).toEqual(array);
  });

  it('should return unique values when iteratee is a function', () => {
    const array = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 1, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ];
    const result = uniqBy(array, (item) => item.id);
    expect(result).toEqual([
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 3, name: '<PERSON>' },
    ]);
  });

  it('should return unique values when iteratee is a string', () => {
    const array = [
      { id: 1, name: '<PERSON>' },
      { id: 2, name: '<PERSON>' },
      { id: 1, name: 'John' },
      { id: 3, name: 'Bob' },
    ];
    const result = uniqBy(array, 'id');
    expect(result).toEqual([
      { id: 1, name: 'John' },
      { id: 2, name: 'Jane' },
      { id: 3, name: 'Bob' },
    ]);
  });

  it('should return unique values when iteratee is not provided and array contains primitive values', () => {
    const array = [1, 2, 2, 3, 3, 3, 4, 5];
    const result = uniqBy(array);
    expect(result).toEqual([1, 2, 3, 4, 5]);
  });
});
