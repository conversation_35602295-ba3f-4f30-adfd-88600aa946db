.ec-root-page {
.content-tabs-container {
  padding-top: 24px;
}

.config-table-container {
//   border: 1px solid #e0e1e3;
//   border-radius: 5px;
  border: none;
  position: relative;
  height: auto;
  max-height: calc(100% - 102px);
  width: 100%;
  & .table-container {
      overflow: auto;
      border-radius: inherit;
      & .head {
          & .row {
              & > div:last-child {
                  & .cell {
                      & .column {
                          & .right {
                              padding-right: 50px;
                          }
                      }
                  }
              }
              & .disable-ordering {
                  cursor: auto;
              }
          }
      }
      & .content {
          height: auto;
          & .table-row-menu-container {
              position: relative;
              top: -10px;
              left: -4px;
              & .table-row-menu {
                color: var(--semantic-color-content-accent-blue-secondary);
                cursor: pointer;
              }
          }
      }
  }
  & .column-layout-config-container {
      width: 60px;
      border-radius: inherit;
      & .config-container {
          max-height: 250px;
          min-width: 180px;
          top: 38px;
          background-color: var(--semantic-color-surface-table-header-default);
      }
      & .table-column-menu-header {
          & .fa-undo {
              color: var(--semantic-color-content-status-info-primary);
              vertical-align: middle;
              cursor: pointer;
          }
      }
      & .table-column-menu-header-text {
          display: inline-block;
          text-align: left;
          width: 148px;
          vertical-align: middle;
      }
  }
}

// .controls-container {
//   & .search-container {
//       display: flex;
//       justify-content: space-between;
//       right: 24px;
//   }
// }

.dialog-footer {
  display: flex;
}

.rdg-selected {
    border: none!important;
}
}
