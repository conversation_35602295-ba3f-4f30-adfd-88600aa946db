.ec-root-page{
.table-container {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: scroll;

  &.has-fixed-columns {
    padding: 8px 16px;
    // border: 1px solid #e0e1e3;

    & .head {
      & .row {
        width: 100% !important;
        border: 1px solid var(--semantic-color-border-base-primary);

        & .column-cell-container {
          width: 100% !important;

          & .cell {
            width: 100% !important;
            font-weight: bold;
            color: var(--semantic-color-content-base-primary);
          }
        }
      }
    }

    & .content {
      & > div {
        width: 100% !important;

        & .row {
          border-left: 1px solid var(--semantic-color-border-base-primary);
          border-bottom: 1px solid var(--semantic-color-border-base-primary);
        }
      }
    }
  }

  & .head,
  .content {
    display: flex;
    flex-direction: column;

    position: relative;
    height: 38px;

    & .row {
      display: flex;
      flex-direction: row;
      flex-grow: 1;
      background-color: var(--semantic-color-surface-table-row-default);
      &.actions-on-hover {
        .cell-actions {
          visibility: hidden;
        }
        &:hover {
          background-color: var(--semantic-color-surface-table-row-hover);
          .cell-actions {
            visibility: visible;
          }
        }
      }
    }

    & .cell {
      flex-grow: 1;
    }
  }

  & .head {
    & .row {
      border-right: none;
      cursor: move;

      & .cell {
        background-color: var(--semantic-color-surface-table-header-default);
        color: var(--semantic-color-content-base-primary);
        font-weight: 500;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);    

        vertical-align: top;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px;
        height: 38px;
        line-height: 28px;
        padding: 4px 15px;
        position: relative;
        text-align: left;
      }
    }
  }

  & .content {
    span:nth-of-type(even) {
      & .cell-container {
        background: transparent;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);
        &.filtered {
          background: var(--semantic-color-surface-table-row-default);
        }
      }
    }
    span:nth-of-type(odd) {
      & .cell-container {
        background: transparent;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);

        &.filtered {
          background-color: #e9f3f6;
        }
      }
    }
    & .child-row {
      background: var(--semantic-color-surface-table-row-default);
      border-bottom: 1px solid var(--semantic-color-border-base-primary);
    }
    & .row {
      &:last-child {
        border-right: none; 
      }
      &.selected {
        & .cell {
          & .cell-container {
            background: var(--semantic-color-surface-table-row-default);
          }
        }
      }
      & .cell {
        position: relative;
      }
    }
  }
}
}