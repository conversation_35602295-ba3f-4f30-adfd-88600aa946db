.ec-root-page{
.table-container .content .cell-container {
  color: var(--semantic-color-content-base-primary);
  padding: 11px 15px;
  cursor: pointer;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 28px;
  // border-right: 1px solid #e0e1e3;
  border-right: none;

  & > * {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  span.vm-name {
    display: block;
    padding-bottom: 2px;
  }

  .policy-action {
    width: 100%;
    height: 25px;
    border-radius: 5px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 4px;

    background-color: rgba(253, 66, 57, 0.1);
    color: #d13932;

    &.allowed {
      background-color: rgba(115, 161, 60, 0.1);
      color: #73a13c;
    }
  }
}
}