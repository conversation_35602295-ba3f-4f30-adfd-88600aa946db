.column-layout-config-container {
  display: inline-block;
  position: absolute;
  top: 0px;
  right: 0px;
  height: 38px;
  width: 30px;
  z-index: 1;
  background: var(--semantic-color-surface-table-header-default);
  border-bottom: 1px solid var(--semantic-color-border-base-primary);
  &:hover {
    & .config-container {
      display: none;
    }
  }

  & .icon {
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
    padding: 11px 0px;
    text-align: center;
  }

  & .config-container {
    display: none;
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);    
    font-size: 13px;
    right: -1px;
    max-height: 200px;
    min-width: 300px;
    width: auto;
    overflow-y: auto;
    overflow-x: hidden;
    position: absolute;
    text-align: left;
    text-transform: none;
    top: 30px;
    white-space: nowrap;
    z-index: 9999;
    border-radius: 5px 0 5px 5px;
    color: var(--semantic-color-content-base-primary);
    cursor: default;
    &.display-block {
      display: block;
    }

    & .actions {
      font-size: 11px;
      padding: 8px;
      cursor: default;
      border-bottom: 1px solid var(--semantic-color-border-base-primary);      
      display: flex;

      & .action {
        color: var(--semantic-color-content-status-info-primary);
        cursor: pointer;
        font-size: 13px;
        margin-right: 25px;
        position: static;
        top: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }

        & .action-icon {
          font-size: 16px;
          margin-right: 5px;
          position: relative;
          top: 1px;
        }
      }
    }
  }

  & .config {
    & .column-config {
      padding: 3px 12px;
      position: relative;
      align-items: center;
      font-size: 13px;
      color: var(--semantic-color-content-interactive-primary-default);
      background-color: var(--semantic-color-surface-table-row-default);
      cursor: default;
      white-space: nowrap;

      display: flex;
      justify-content: space-between;

      &:hover {
        cursor: move;
        background-color: var(--semantic-color-surface-table-row-default);
      }

      & .checkbox {
        height: 30px;
        line-height: 30px;
        margin-right: 8px;
        vertical-align: top;
        cursor: pointer;
        font-size: 16px;
      }

      & .name {
        height: 30px;
        line-height: 30px;
        flex-grow: 1;
      }

      & .icon {
        font-size: 10px;
        color: #747272;
      }
    }
  }
}
