@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/pages.scss';
/*
    Common Styles:

    -   Grid layout
    -   Generic classes
    -   UI elements (e.g., buttons, inputs)
*/
.ec-root-page {
/* Column Layout */
.column {
    display: inline-block;
    vertical-align: top;

    &::-moz-selection {
        background: transparent;
    }

    &::selection {
        background: transparent;
    }
}

.column.column-last {
    margin-right: 0;
}

.column.column-padded {
    padding-right: 40px;
}

.column-2 {
    margin-right: 1%;
    width: 49%;
}

.column-2 + .column-2 {
    margin-left: 1%;
}

/* Generic Classes */
.hidden {
    display: none;
}

.invisible {
    visibility: hidden;
}

.max-width {
    width: 100%;
}

.vcentered {
    bottom: 0;
    margin-bottom: auto;
    margin-top: auto;
    position: absolute;
    top: 0;
}

.disable-user-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Icons */
[class^="icon-"], [class*=" icon-"] {
    font-size: 16px;
    background-image: none;
}

.button.button-short [class^="icon-"], .button.button-short [class*=" icon-"] {
    font-size: 12px;
    margin-right: 3px;
}

.button.button-working [class^="icon-"], .button.button-working [class*=" icon-"] {
    font-size: 0;
}

.button [class^="sprite-"], .button [class*=" sprite-"] {
    margin-right: 5px;
    vertical-align: middle;
}

.icon-inline {
    position: relative;
    top: -1px;
    vertical-align: middle;
}

.notification-icon {
    font-size: 14px;
    vertical-align: middle;
}

/* Edit Menu */
.edit-menu {
    // top and left positions must be set dynamically at runtime
    margin-top: -5px;
    padding: 5px 0;
    position: fixed;
    z-index: 99999999;
}

/* Scrollbar */
.scrollbar-inner {
    height: 200px;
    width: 100%;
}

.scrollbar-outer {
    height: 150px;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    visibility: hidden;
    width: 200px;
}

/* Input Container */
.input-container {

}

.input-container.input-container-selector {

}

.input-container.input-container-selector .button-radio {

}

/* loading throbber */
.load-container {
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    background-color: #F6F5F5;
    border-radius: 0 0 3px 3px;
    height: 18px;
    left: 40%;
    position: absolute;
    top: 0;
    vertical-align: middle;
    width: 20%;
    z-index: 150000;
}

.load-title {
    color: #333;
    font-size: 10px;
    display: inline-block;
    margin-right: 5px;
    position: relative;
    width: 100%;
    text-align: center;
    @include no-wrapping;
}

.load-mask {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
    background: var(--semantic-color-surface-base-primary);
    opacity: 0.4;
    height: 100%;
    width: 100%;
    z-index: 1000;
}

.tooltip-cue,
.popup-cue-text {
    @include no-wrapping;
    display: inline-block;
    font-size: 13px;
    max-width: 100%;
    vertical-align: top;

    &.active {
        color: var(--semantic-color-content-interactive-primary-default);
    }

    &:hover {
        color: var(--semantic-color-content-interactive-primary-default);
        cursor: pointer;
    }
}

.help-text-bold {
    font-weight: bold;
}

/* Text Area*/
.text-area {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    background: var(--semantic-color-surface-base-primary);
    box-sizing: border-box;
    color: #666;
    display: inline-block;
    font-size: 13px;
    padding: 2px 5px 0 5px;
    border: 1px solid #ccc;
}

/* Text Input */
.text-input.text-input-invalid, .text-area.text-input-invalid, .text-input-invalid {
    border: 1px solid var(--semantic-color-border-status-danger-active);
}

.text-input.text-input-invalid:focus, .text-area.text-input-invalid:focus {
    border: 1px solid var(--semantic-color-border-status-danger-active);
}

.text-input-invalid-container {
    color: var(--semantic-color-background-primary);
    height: 16px;
    line-height: 16px;
    width: auto;
    background-color: var(--semantic-color-content-status-danger-primary);
    display: inline-block;
    max-width: 350px;
}

.text-input-invalid-container-top {
    -webkit-border-top-left-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    -moz-border-radius-topleft: 3px;
    -moz-border-radius-topright: 3px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.text-input-invalid-container-left {
    -webkit-border-top-left-radius: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -moz-border-radius-topleft: 3px;
    -moz-border-radius-bottomleft: 3px;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.text-input-invalid-main-container {
    color: red;
    height: 15px;
    line-height: 12px;
    width: 100%;
}

.text-input-invalid-message {
    font-size: 10px;
    padding: 0 2px;
    display: inline-block;
    max-width: 330px;
    width: auto;
    height: 15px;
    vertical-align: top;
    @include no-wrapping;
}

.text-input.text-input-wide {
    width: 100%;
}

.text-input-invalid-icon {
    padding-left: 2px;
    display: inline-block;
    line-height: 14px;
    height: 14px;
    font-size: 12px;
    vertical-align: middle;
}

.text-label {
    color: $text-input-color;
    font-weight: 500;
    display: inline-block;
    font-size: 13px;
    margin-bottom: 12px;
}

.text-label:after {
    content: ":";
}

.text-information {
    color: #333;
    display: inline-block;
    font-size: 13px;
    margin-bottom: 2px;
    vertical-align: top;
}

.text-label.text-label-wide {
    width: 100%;
}

/* Tooltip */
.tooltip {
    background: var(--semantic-color-background-primary);
    border: 1px solid $default-border-color;
    border-radius: 3px;
    box-shadow: 0 7px 7px -5px $default-border-color;
    color: $tooltip-text-color;
    font-size: 13px;
    // position: absolute;
    text-align: left;
    // Root Context
    z-index: 10000;
    /* general/shared styles for tooltip arrow */
    &::before {
        content: "";
        border: 6px solid transparent;
        height: 0;
        position: absolute;
        width: 0;
    }
    &::after {
        content: "";
        border: 5px solid transparent;
        height: 0;
        position: absolute;
        width: 0;
    }
    /* default tooltip is on the right and text goes down, arrow points to the left */
    &.right-down {
        &::before {
            border-left: none;
            border-right-color: $default-border-color;
            left: -6px;
            top: 11px;
        }
        &::after {
            border-left: none;
            border-right-color: $tooltip-background-color;
            left: -5px;
            top: 12px;
        }

        &.user-menu-tooltip {
            background-color: $tooltip-background-color;
            .ec-tooltip-container{
                color: $tooltip-text-color;
                font-size: 13px;
                height: 66px!important;
                min-width: 180px;
                
                .tooltip-top {
                    height: 50%;
                    padding: 0 0 6px 0;

                    span {
                        @include no-wrapping;
                        width: 100%;
                        display: block;
                    }
                }

                .tooltip-bottom{
                    color: $tooltip-text-color;
                    font-size: 13px;
                    height: 50%;
                    padding: 6px 0 0 0;
                }
            }
            &::before {
                left: -6px;
                top: 23px;
            }
            &::after {
                border-right-color: $tooltip-background-color;
                left: -5px;
                top: 24px;
            }
        }
    }
    /* bottom edge detected */
    &.right-up {
        &::before {
            border-left: none;
            border-right-color: $default-border-color;
            left: -6px;
            bottom: 24px;
        }
        &::after {
            border-left: none;
            border-right-color: $tooltip-background-color;
            left: -5px;
            bottom: 25px;
        }
    }
    /* right edge detected */
    &.left-down {
        &::before {
            border-left-color: $default-border-color;
            border-right: none;
            right: -6px;
            top: 11px;
        }
        &::after {
            border-left-color: $tooltip-background-color;
            border-right: none;
            right: -5px;
            top: 12px;
        }
    }
    /* right and bottom edge detected */
    &.left-up {
        &::before {
            border-left-color: $default-border-color;
            border-right: none;
            bottom: 11px;
            right: -6px;
        }
        &::after {
            border-left-color: $tooltip-background-color;
            border-right: none;
            bottom: 12px;
            right: -5px;
        }
    }
    /* validation: default - on top and to the right, arrow points down */
    &.top-right {
        &::before {
            border-top-color: $default-border-color;
            border-bottom: none;
            bottom: -6px;
            left: 11px;
        }
        &::after {
            border-top-color: $tooltip-background-color;
            border-bottom: none;
            bottom: -5px;
            left: 12px;
        }
    }
    /* validation: right edge detected */
    &.top-left {
        &::before {
            border-top-color: $default-border-color;
            border-bottom: none;
            bottom: -6px;
            right: 11px;
        }
        &::after {
            border-top-color: $tooltip-background-color;
            border-bottom: none;
            bottom: -5px;
            right: 12px;
        }
    }
    /* validation: top edge detected */
    &.bottom-right {
        &::before {
            border-bottom-color: $default-border-color;
            border-top: none;
            left: 11px;
            top: -6px;
        }
        &::after {
            border-bottom-color: $tooltip-background-color;
            border-top: none;
            left: 12px;
            top: -5px;
        }
    }
    /* validation: right and top edge detected */
    &.bottom-left {
        z-index: 1000;
        &::before {
            border-bottom-color: $default-border-color;
            border-top: none;
            right: 11px;
            top: -6px;
        }
        &::after {
            border-bottom-color: $tooltip-background-color;
            border-top: none;
            right: 12px;
            top: -5px;
        }
    }

    &.bottom-center {
        top: 73px!important;
        right: 20px!important;
        left: inherit!important;
        &::before {
            border-bottom-color: $default-border-color;
            border-top: none;
            right: 60px;
            top: -6px;
        }
        &::after {
            border-bottom-color: $tooltip-background-color;
            border-top: none;
            right: 61px;
            top: -5px;
        }
    }

    &.unsubscribed {
        border-color: $slider-color-moderate;
        color: $slider-color-moderate;
        &::before {
            border-right-color: $slider-color-moderate;
        }
    }

    &.error {
        border-color: $slider-color-high;
        color: $slider-color-high;
        &::before {
            border-right-color: $slider-color-high;
        }
    }

    &.peer-comparison-tooltip {
        .ec-tooltip-container {
            padding: 0;
        }

        &.right-down {
            &::before {
                border-right-color: $default-border-color;
            }

            &::after {
                border-right-color: $button-bkgrd-color;
            }
        }
    }

    .ba-tooltip-container {
        margin: -4px 0px;
        .tooltip-content {
            // background-color: var(--semantic-color-content-interactive-primary-default);
            width: 117.5%;
            margin-left: -9%;
            padding-left: 8%;
            cursor: pointer;
            line-height: 23px;
        }
        // .tooltip-content-ba {
        //     padding-bottom: 4px;
        // }
        .tooltip-content-active {
            background-color: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-surface-base-primary);
        }
    }

    &.saml-expiry-tooltip {
        border: 1px solid $fa-exclamation-triangle-color;
        .ec-tooltip-container {
            color: $fa-exclamation-triangle-color;
            height: 40px;
            line-height: 15px;
        }
        &.right-down {
            &::before {
                border-right-color: $fa-exclamation-triangle-color;
            }
        }
    }
}

.ba-tooltip {
    z-index: 100000;
}

.ec-tooltip-container {
    max-height: 300px;
    max-width: 550px;
    overflow: auto;
    padding: 12px 16px;
    word-wrap: break-word;

    .tooltip-header {
        font-weight: 600;
        margin-bottom: 2px;
        height: 20px;
    }

    .help-link-external {
        color: $anchor-color;
        cursor: pointer;
        &:hover {
            color: $anchor-hover-color;
        }
    }
    
    p {
        margin: 0;
    }
}

.tooltip-bottom-text {
    font-size: 11px;
}

.tooltip-text-bold {
    font-weight: 600;
}

.tooltip-icon {
    cursor: pointer;
    font-size: 16px!important;
    margin-left: 5px;
    margin-top: -1px;
    vertical-align: top;

    &.is-page-help {
        margin: 0;
        display: inline-block;
        vertical-align: top;
    }

    &.has-link {
        cursor: pointer;
    }

    &.invalid {
        color: var(--semantic-color-content-status-danger-primary);
    }
}

.tooltip-list-item {
    &:after {
        content: ", ";
    }

    &:last-child:after {
        content: "";
    }
}

.help-error-text {
    color: var(--semantic-color-content-status-danger-primary);

    &.has-info-text {
        margin-bottom: 15px;
    }
}

/**
Open close containers
*/

.disclosure-icon {
    color: $button-text-color;
    cursor: pointer;
    display: inline-block;
    font-size: 13px;
    margin-right: 8px;

    &:hover {
        color: $anchor-hover-color;
    }
}

/* Notifications */
.ec-notification-container {
    left: 50%;
    margin-left: -360px;
    position: fixed;
    top: 10px;
    width: 720px;
    // Root Context
    z-index: 10000000;

    &.poc-class {
        left: 0;
        top: 97px;
        width: 100%;
        padding: 0 24px;
        margin: 0px;

        &.pocnotice {
            width: 100%;
            background: var(--semantic-color-surface-base-primary)ced;
            border: 1px solid var(--semantic-color-surface-base-primary)ced;

            .notification-message-content {
                display: block;
                color: var(--semantic-color-content-base-primary);

                .form-link-text {
                    display: inline-block;
                    line-height: normal;
                    vertical-align: inherit;
                    padding: 0;
                    margin: 0px;
                }
            }
        }
    }

    &.exec-report-deprecate-warning {
        z-index: 1000000;

        .notification {
            padding: 12px;
        }
    }
}

.notification {
    background: var(--semantic-color-background-primary);
    border-top-left-radius: 0rem;
    border-bottom-left-radius: 0rem;
    color: var(--semantic-color-content-base-primary);
    display: table;
    font-size: 13px;
    margin: 0 auto;
    text-align: left;
    width: 720px;
    max-height: 220px;
    overflow-y: auto;

    &.warning {
        border-color: $slider-color-moderate;

        .notification-type {
            background: $slider-color-moderate;
        }

        .notification-message-container .notification-close-icon {
            color: $slider-color-moderate;
        }
    }

    &.error {
        border-color: $slider-color-high;

        .notification-type {
            background: $slider-color-high;
        }

        .notification-message-container .notification-close-icon {
            color: $slider-color-high;
        }
    }

    &.notice {
        border-color: var(--semantic-color-content-status-info-primary);

        .notification-type {
            background: var(--semantic-color-content-interactive-primary-default);
        }

        .notification-message-container .notification-close-icon {
            color: var(--semantic-color-content-status-info-primary);
        }
    }

    & + & {
        margin-top: 5px;
    }

    &.pocnotice {
        width: 100%;
        background: var(--semantic-color-surface-base-primary)ced;
        border: 1px solid var(--semantic-color-surface-base-primary)ced;
        position: absolute;
        top: 87px;
        width: 100%;
        box-shadow: 0 2px 4px -1px rgba(0,0,0,0.7);

        .notification-message-content {
            display: block;
            color: var(--semantic-color-content-base-primary);
            line-height: 20px;

            &:first-child {
                line-height: inherit;
            }

            .form-link-text {
                display: inline-block;
                line-height: normal;
                vertical-align: inherit;
                padding: 0;
                margin: 0px;
            }
        }
    }

    .notification-type {
        width: 50px;
        background: $slider-color-low;
        color: var(--semantic-color-surface-base-primary);
        font-size: 24px;
        padding: 12px;
        line-height: 25px;
        text-align: center;
        display: table-cell;
        vertical-align: middle;
    }

    .notification-message-container {
        display: table-cell;
        vertical-align: middle;
        width: calc(100% - 50px);
        padding: 12px 16px;
        max-width: 720px;

        .notification-message {
            display: inline-block;
            overflow-y: auto;
            vertical-align: middle;
            width: calc(100% - 24px);
            word-break: normal;
            max-height: 150px;
        }

        .notification-close-icon {
            background-color: var(--semantic-color-surface-base-primary);
            cursor: pointer;
            display: inline-block;
            vertical-align: middle;
            font-size: 20px!important;
            line-height: 100%;
            padding-left: 8px;
            width: 24px;
            text-align: center;

            &:hover {
                opacity: 0.8;
            }
        }
    }
}

/* Tab */
.tab {
    @include inline-block;
    color: $tab-header-title-color;
    cursor: pointer;
    font-weight: 500;
    font-size: 16px;
    min-width: 120px;
    padding: 0 16px 8px 16px;
    text-align: center;
    max-width: 50%;
    @include no-wrapping;

    &:hover {
        color: $tab-header-title-hover-color;
    }

    &.active,
    &.active:hover {
        border-bottom: 3px solid var(--semantic-color-content-interactive-primary-default);
        color:  var(--semantic-color-content-base-primary);
        cursor: default;
    }

    &.hidden {
        display: none;
    }

}

.tab-active {
    @extend .tab;
    border-bottom: 3px solid var(--semantic-color-content-interactive-primary-default);
    color:  var(--semantic-color-content-base-primary);
}

.tab-container {
    max-height: 28px;
    width: 100%;
}

.content-tabs-container {
    width: 100%;
    height: 100%;
}

.content-tabs {
    @include nav-list-reset;
    position: relative;
    width: 100%;
    top: 1px;
    z-index: 1;
    border-bottom: 1px solid var(--semantic-color-border-base-primary);
    height: 32px;
    margin-bottom: 15px;

    .form-section + & {
        margin-top: 10px;
    }

    &.tab-backup-view{
        border-bottom: 1px solid #E3E5E6;

    }
    .vmware-redirect {
        float: right;
        margin-top: 3px;
        i.fa-external-link {
            margin-left: 5px;
        }
    }
}

.content-tab {
    @extend .tab;
    text-overflow: inherit;
    &.active {
        @extend .tab-active;
        text-overflow: inherit;
    }

    .content-tab-title {
        @include inline-block(middle);
        color: var(--semantic-color-content-base-primary);
    }

    .content-tab-count {
        @include inline-block(middle);
        margin-left: 8px;
        margin-top: -1px;

        &::before {
            content: "(";
        }

        &::after {
            content: ")";
        }

        &.hidden {
            display: none;
        }
    }
}

.content-tabs-content {
    height: calc(100% - 47px);
    width: 100%;
}

.content-tabs-content-item {
    display: none;
    height: 100%;
    width: 100%;

    &.active {
        display: block;
    }
}

.content-tab-panel {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0 0 3px 3px;
    padding: 10px;
    width: 100%;

    .content-tabs.hidden + & {
        border-radius: 3px;
        margin-top: 10px;
    }
}

/* Grid Toolbar */
.grid-toolbar {
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    height: 34px;
    line-height: 32px;
    padding: 0 5px;
    width: 100%;
}

.grid-toolbar-left {
    @include inline-block;
    height: 28px;
    text-align: left;
    // width: 50%;
    white-space: nowrap;
    overflow: visible;

    span.dropdown{
        width: 280px;
    }

    .exec-filters {
        span.dropdown{
            width: auto;
        }
    }

    &.audit-logs-grid-toolbar-left {
        width: calc(100% - 360px);
        height: auto;
        vertical-align: middle;
    }

    &.location-grid-toolbar-left {
        width: 65%;
    }

    &.azure-wan-location-grid-toolbar-left {
        width: 30%;
    }

    .status-message {
        display: block;
        padding: 6px 0;
    }
}

.grid-toolbar-right {
    @include inline-block;
    height: 100%;
    text-align: right;
    // width: 50%;
    &.display-inline-flex {
        display: inline-flex;
        justify-content: end;
        align-items: center;
    }
    .search-input{
        background: none;
        padding: 0;
    }
    .search-icon-button{
        cursor:pointer;
    }

    .dropdown{
        width: 195px;
        background: $page-content-bkgrd-alt-color;
    }
    .search-input, .search-input-text{
        width: 195px;
    }

    &.location-grid-toolbar-right {
        width: 35%;

        .locations-group-name-filter {
            position: absolute;
            top: -50px;
            right: -16px;
            z-index: 2;

            .dropdown {
                width: auto;

                .search-input{
                    background: $login-footer-bkgrd;
                    padding: 12px 16px;
                }

                .dropdown-button-label {
                    max-width: 375px;
                }
            }
        }
    }

    &.azure-wan-location-grid-toolbar-right {
        width: 70%;
        .sync-refresh-time {
            color:  var(--semantic-color-content-base-primary);
            font-size: 11px;
            margin: 0 16px;
            @include inline-block(middle);

            &.last-sync-time {
                margin: 0;
            }

            span {
                font-weight: 500;
            }

            @media (max-width: 1400px) {
                max-width: 220px;
            }
        }
    }

    @media (max-width: 1280px) {
        .dropdown{
            width: 125px;
        }
        .search-input, .search-input-text{
            width: 125px;
        }
    }

    &.audit-logs-grid-toolbar-right {
        width: 360px;
        height: auto;
        vertical-align: middle;

        .dropdown{
            width: 125px;
        }
        .search-input, .search-input-text{
            width: 125px;
        }
    }

    .url-categories-search-container {
        @include inline-block(middle);
    }

}

.grid-toolbar-container {
    height: 100%;
    width: 100%;

    .grid-toolbar-left {
        width: 40%;
    }
    .grid-toolbar-right {
        width: 60%;
    }
}

// Grid Control COntainer buttons
.control-button{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
    font-size: 13px;
    margin-right: 32px;
    position: relative;
    top: 5px;
    @include no-wrapping;

    i{
        font-size: 16px;
        margin-right: 5px;
        position: relative;
        top: 1px;

        &.fa-sort-amount-desc {
            font-size: 13px;
        }
    }

    &.table-layout-control-buttons {
        @include inline-block;
    }

    &:hover{
        color: var(--semantic-color-content-interactive-primary-hover);
    }

    &.locations-control-button {
        color: $anchor-color;
        width: auto!important;

        &.ungroup-selection-button {
            i.fa-sitemap:after {
                content: "";
                position: absolute;
                width: 3px;
                height: 20px;
                top: 0px;
                display: block;
                background: $anchor-color;
                left: 48%;
                transform: rotate(135deg);
            }
        }

        &:hover {
            color: var(--semantic-color-content-interactive-primary-hover);

            .dropdown-button-label {
                color: var(--semantic-color-content-interactive-primary-hover) !important;                
            }
        }

        &.disabled {
            background: var(—surface-fields-disabled);
            color: var(--semantic-color-content-interactive-primary-disabled);
            cursor: not-allowed;

            &:hover {
                color: var(--semantic-color-content-interactive-primary-disabled);
            }

            &.ungroup-selection-button {
                i.fa-sitemap:after {
                    background: var(--semantic-color-content-interactive-primary-disabled);
                }
            }

            .dropdown {
                .dropdown-button {
                    cursor: default;

                    .dropdown-button-label {
                        color: var(--semantic-color-content-interactive-primary-disabled) !important;
                    }                    
                }
            }
        }

        i.fas.fa-table {
            font-size: 18px;
        }

        .control-button-container {
            position: absolute;
            right: -3px;
            top: 6px;
            width: 16px;
            height: 16px;
            background: $page-content-bkgrd-color;
            border-radius: 8px;
            text-align: center;

            i {
                top: -1px;
                left: 0px;
                font-size: 16px;
                margin: 0;
            }
        }

        .add-to-group-icon {
            @include inline-block(top);
            position: static;
        }

        .add-to-group-dropdown {
            position: static;
            margin: 0;
            width: auto!important;

            .dropdown {
                background: none;
                width: auto;

                .dropdown-button {
                    padding: 0px!important;
                    border: none;
                    height: 24px;

                    .dropdown-button-label {
                        font-weight: 400;
                        width: auto;
                        color: $button-bkgrd-color;
                        vertical-align: middle;
                    }

                    .dropdown-icon {
                        display: none;
                    }
                }
            }
        }

        &.filters-button {
            margin-top: -1px;

            &.active {
                text-shadow: 0px 1px 6px $anchor-hover-color;
                overflow: visible;
            }

            i.fa-filter {
                font-size: 18px;
            }
        }
    }

    &.audit-log-export-button {
        margin: 0 0 0 16px;
        width: auto!important;
    }

    &.disabled {
        background: var(—surface-fields-disabled);
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: not-allowed;
        &:hover {
            color: var(--semantic-color-content-interactive-primary-disabled);
        }
    }
}

.control-button-with-labels {
    @include inline-block;
    margin-right: 32px;

    &.audit-log-control-buttons {
        max-width: calc(16.66% - 32px);
    }

    .control-button-label {
        width: 100%;
        display: block;
        color: $text-input-disabled-color;
        font-weight: 400;
        font-size: 11px;
        text-transform: uppercase;
        padding-bottom: 6px;
    }

    .control-button-filter {
        display: block;
        width: 100%;
    }

    &.hidden {
        display: none;
    }

    &:last-child {
        margin-right: 0px;
    }

    .dropdown-button {
        padding: 0px!important;

        .dropdown-button-label {
            max-width: calc(100% - 20px);
        }
    }

    .report-time-range-filter {
        width: 100%;
        margin: 0px;
        span.dropdown {
            width: 100%;
        }
    }
}

.error-details {
    margin-right: 2px;
}

// Waiting Spinner
.waiting-container {
    height:auto;//min-height: 68px !important;

    &.page-dialog {
        height: 100%;
    }
}

.ecui-waiting-overlay  {
    background: var(--semantic-color-surface-base-primary);
    opacity: 0.8;
    height: 100%;
    left: 0;
    position: absolute;
    text-align: center;
    top: 0;
    right: 0;
    z-index: 9;
    min-height: 3rem;

    &.MR .waiting-spinner {
        margin-left: 4.75rem;
    }
    &.MR-DT .waiting-spinner {
        margin-left: 4.9375rem;
    }
    &.inline-spinner {
        position: relative !important;
    }
}

.waiting-spinner {
    &.export-csv-waiting-spinner {
        position: absolute;
        top: 0;
        left: 0;
        height: 96px;
        width: 96px;
    }
}

@-webkit-keyframes spinner {
    to {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

.import-dialog-failed-record {
    margin-bottom: 7px;
    white-space: normal;

    &:first-child {
        margin-top: 7px;
    }
}

.is-invalid-attribute { // used in several features to identify deleted/deprecated values
    text-decoration: line-through;
}

.vertical-align-middle-anchor {
    height: 100%;
    content: ' ';
    width: 0;
    vertical-align: middle;
    display: inline-block;
}

//  Export To CSV In Progress/Complete styles

.export-to-csv-container {
    text-align: center;

    .export-csv-icon-container {
        width: 96px;
        height: 96px;
        position: relative;
        text-align: center;
        margin: 0 auto;

        &.complete {
            width: auto;
            height: auto;
        }

        .export-icon {
            position: relative;
            display: inline-block;
            top: calc(50% - 24px);

            .file-icon {
                width: 40px;
                height: 48px;
                border-radius: 5px;
                background: #e0e0e2;
                position: relative;

                .file-icon-partial {
                    position: absolute;
                    right: -2px;
                    top: 6px;
                    border-radius: 7px;
                    width: 0;
                    height: 0;
                    border-left: 16px solid transparent;
                    border-right: 16px solid transparent;
                    border-top: 16px solid #c6c5ca;
                    transform: rotate(45deg);
                }

                .file-icon-partial-2 {
                    position: absolute;
                    right: -12px;
                    top: -4px;
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-top: 15px solid var(--semantic-color-surface-base-primary);
                    transform: rotate(225deg);
                }
            }

            .arrow-icon {
                position: absolute;
                left: 60%;
                top: 30px;

                .arrow-icon-partial-1 {
                    width: 30px;
                    height: 4px;
                    background: $button-bkgrd-color;
                    border-radius: 7px;
                }

                .arrow-icon-partial-2 {
                    height: 4px;
                    background: $button-bkgrd-color;
                    border-radius: 7px;
                    width: 12px;
                    transform: rotate(45deg);
                    position: absolute;
                    right: -1px;
                    top: -3px;
                }

                .arrow-icon-partial-3 {
                    height: 4px;
                    background: $button-bkgrd-color;
                    border-radius: 7px;
                    width: 12px;
                    transform: rotate(135deg);
                    position: absolute;
                    right: -1px;
                    top: 3px;
                }
            }

            .status-complete-icon {
                position: absolute;
                left: 24px;
                top: 30px;
                background: var(--semantic-color-surface-base-primary);
                border-radius: 26px;
                width: 26px;
                height: 26px;
                text-align: center;
                padding-top: 1px;

                .fa-check-circle {
                    font-size: 24px;
                    color: #399c1d;
                }
            }
        }
    }
    .export-to-csv-message {
        margin-top: 16px;
        font-size: 16px;
        line-height: 24px;
    }
}

.row-title {	
    color:  var(--semantic-color-content-base-primary);	
    font-size: 13px;	
    font-weight: 500;	
    line-height: 25px;
}

.form-input-validator {
    padding-bottom: 15px; 

    .form-input {
        display: inline-block;
        width: 190px;
        padding-right: 10px;

        .form-input-text {
            min-width: 100%;
        }
    }
}

.primary-btn {	
    padding: 10px;
    margin-top: 10px;
    display: inline-block;	
    border-radius: 5px;	
    color: var(--semantic-color-surface-base-primary);
    background-color: var(--semantic-color-content-interactive-primary-default);
    &:hover {
        color: var(--semantic-color-surface-base-primary);
        background-color: #00bce4;
    }
}

a.primary-btn.transparent {
    background-color: transparent;
    color: var(--semantic-color-content-interactive-primary-default);
    padding: 10px 0;
}

.form-input-row.width30 {
    width: 30%;
}

.casb-add-tenant-container {
    .numbered-content-item-content {
        display: none;
    }
    .numbered-content-item.active, .numbered-content-item.completed {
        .numbered-content-item-content {
            display: block;
        }
    }
}

.casb-container {
    .form-input-rows {
        background-color: transparent;
        box-shadow: none;
        .form-input-row {
            padding: 15px 0;
        }
        .form-input.app-id-provider{
            width: auto;
        }
    }
}
.form-input-copy {
    padding: 9px 15px;	
    cursor: pointer;
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px;	
    margin-right: 15px;
    background-color: var(--semantic-color-surface-base-primary);
}
.copy-text {
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 13px;	
    font-weight: 500;
    padding-left: 30px;
}
.copy-gsuite {
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    padding: 5px;
    float: right;
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px;
}
.casb-add-tenant-container {
    position: relative;
    border: 1px solid var(--semantic-color-border-base-primary);
    height: 100%;
    .numbered-content-region {
        height: calc(100% - 63px);
        overflow: auto;
        background: #efeff0;

        .numbered-content-container {
            border: none;
            height: auto;
        }
    }
}

.pad-b-60 {
    padding-bottom: 60px;
}

.pad-b-40 {
    padding-bottom: 40px;
}

.hint-label {	
    color: var(--semantic-color-content-base-primary);	
    font-size: 11px;
}
.error-msg-title {
    padding-left: 15px;
    cursor: pointer;
    &:before {
        content: '\f071';
        position: absolute;
        color: #CC0800;
        left: 14px;
    }
}
.small-13 {
    font-size: 13px;
    font-weight: normal;
}
.uppercase {
    text-transform: uppercase;
}
.hint-message {
    color:  var(--semantic-color-content-base-primary);
    margin-top: 10px;
    font-size: 11px;
    display: block;
}
ul.error-group{
 padding: 0 0 0 15px;
 list-style: none;
 li {
    padding: 3px 0;
 }
}
.alert-red {
    color: var(--semantic-color-content-status-danger-primary);
}
.app-blue {
    color: #009CDA;
}
.p-left3 {
    padding-left: 3px;
}
.p-left5 {
    padding-left: 5px;
}
.p-right3 {
    padding-right: 3px;
}
.p-right5 {
    padding-right: 5px;
}
.p-top5 {
    padding-top: 5px;
}
.m-left15 {
    margin-left: 15px !important;
}
.m-bottom15 {
    margin-bottom: 15px !important;
}
.overflow-visible {
    overflow: visible !important;
}
.base-button {
    background: transparent;
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px;
    box-shadow: 0 0 8px rgba(0, 157, 208, 0.4);
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
    display: inline-block;
    vertical-align: top;
    height: 28px;
    font-size: 13px;
    line-height: 10px;
    max-width: 480px;
    min-width: 81px;
    padding: 8px 16px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.no-bgColor {
    background-color: transparent !important;
}

.m-top30 {
    margin-top: 30px;
}
//Search dropdown css
.form-input {
    &.-js-saas-list {
        width: 100%;
    }
}
.search-dropdown {
    max-width: 830px;
    .search-input {
        background: $text-input-bkgrd-color;
        border: 1px solid $text-input-bkgrd-color;
        padding: 3px 16px;
        margin-bottom: 30px;
        border-radius: 5px;
        box-shadow: none;
    }
    .search-input-text-container {
        width: 100%;
        .search-input-text {
            box-shadow: none;
            width: 97%;
        }
        .search-clear-icon {
            right: 26px;
        }
        .search-icon {
            position: absolute;
            cursor: pointer;
            top: 6px;
            right: -3px;
        }
    }
}
.-js-dropdown-list-panel {
    background: var(--semantic-color-surface-base-primary);
    display: none;
    position: absolute;
    z-index: 111;
    border: 1px solid #747272;
    margin-top: -33px !important;
    border-radius: 0 0 5px 5px;
    &.open {
        display: block;
    }
    .search-dropdown-list-item {
        display: flex;
        .glyph-img {
            margin: auto 0;
            img { max-width: 30px; }
        }
        .app-name {
            padding: 10px 15px 5px;
        }
    }
}

// Pagination Footer

.pagination-footer {
    color: $default-text-color;
    font-size: 13px;

    .pagination-footer-left {
        @include inline-block(middle);
        width: 33%;
    }

    .pagination-footer-center {
        @include inline-block(middle);
        width: 33.33%;
        text-align: center;

        .page-navigation {
            font-size: 16px;
            font-weight: bold;
            color: $button-bkgrd-color;
            padding: 0 8px;
            @include inline-block(middle);
            cursor: pointer;

            &.disabled {
                color: var(--semantic-color-content-interactive-primary-disabled);
                cursor: default;
                font-weight: normal;
            }

            &:hover {
                color: var(--semantic-color-content-interactive-primary-hover);

                &.disabled {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                }
            }
        }

        .page-number {
            border: 0px;
            background: $form-section-bkgrd-color;
            border-radius: 8px;
            color: $anchor-color;
            width: 32px;
            padding: 2px 0 2px 4px;
            text-align: center;
            @include inline-block(middle);

            &:focus {
                outline: none;
            }

            &::-webkit-inner-spin-button, &::-webkit-outer-spin-button { 
              -webkit-appearance: none; 
              margin: 0; 
            }
        }

        span.page-number-text {
            @include inline-block(middle);
        }

        .page-number-separator {
            @include inline-block(middle);
            padding: 4px 8px;
        }
    }
    .pagination-footer-right {
        @include inline-block(middle);
        width: 33.33%;
        text-align: right;

        .remove-dropdown-container {
            padding-right: 4px;

            .dropdown.dropdown-no-bkgrd {
                width: auto;

                .dropdown-button-label {
                    width: calc(100% - 20px);
                }

                .dropdown-icon {
                    width: 14px;
                    padding-left: 4px;
                }
            }
        }

        .remove-button {
            min-width: 0;
            padding: 4px;
            height: auto;
            line-height: normal;
            width: 32px;
        }
    }
}


.button, .icon-button, .slider-button {
    background:  var(--semantic-color-content-interactive-primary-default);
    border: none;
    border-radius: 0.25rem;
    color: var(--semantic-color-content-inverted-base-primary);
    // font-size: var(--paragraph-1-font-size);
    font-weight: var(--font-weight-normal);
    cursor: pointer;
    display: inline-block;
    vertical-align: top;
    height: 36px;
    font-size: 13px;
    line-height: 18px;
    max-width: 480px;
    min-width: 81px;
    padding: 8px 20px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .button:focus, .icon-button:focus, .slider-button:focus {
      outline: none; }
    .button:hover, .icon-button:hover, .slider-button:hover, .button:focus, .icon-button:focus, .slider-button:focus {
        background-color: var(--semantic-color-content-interactive-primary-hover);}
    .button::-moz-selection, .icon-button::-moz-selection, .slider-button::-moz-selection {
      background: transparent; }
    .button::selection, .icon-button::selection, .slider-button::selection {
      background: transparent; }
    .button.small, .small.icon-button, .small.slider-button {
      font-size: 12px;
      min-width: 60px; }
    .button.big, .big.icon-button, .big.slider-button {
      font-size: 16px; }
      .button.big.delete, .big.delete.icon-button, .big.delete.slider-button {
        background: none;
        border: 1px solid var(--semantic-color-border-interactive-danger-default);
        color: var(--semantic-color-border-interactive-danger-default);
        // box-shadow: 0 0 8px rgba(231, 76, 60, 0.4);
        }
        .button.big.delete:hover, .big.delete.icon-button:hover, .big.delete.slider-button:hover {
            border: 1px solid var(--semantic-color-surface-interactive-danger-hover); }
    .button.disabled, .disabled.icon-button, .disabled.slider-button {
        box-sizing: border-box;
        border: 1px solid #CACBCC;
        border-radius: 5px;
        background-color: #F7F9FA;
    }
      .button.disabled:active, .disabled.icon-button:active, .disabled.slider-button:active {
        background: #f7f9fa; }
    .button.success, .success.icon-button, .success.slider-button {
      background: #77B347;
      border-color: #77B347; }
    .button.error, .error.icon-button, .error.slider-button {
      background: #d81903;
      border-color: var(--semantic-color-content-status-danger-primary); }
    .button.no-bkgrd, .no-bkgrd.icon-button, .no-bkgrd.slider-button {
      border: 0px;
      background: none;
      color: var(--semantic-color-content-status-info-primary);
      box-shadow: none; }
      .button.no-bkgrd.disabled, .no-bkgrd.disabled.icon-button, .no-bkgrd.disabled.slider-button {
        color: #6e6e6e; }
        .button.no-bkgrd.disabled:hover, .no-bkgrd.disabled.icon-button:hover, .no-bkgrd.disabled.slider-button:hover {
          color: #6e6e6e; }
      .button.no-bkgrd:hover, .no-bkgrd.icon-button:hover, .no-bkgrd.slider-button:hover {
        color: var(--semantic-color-content-interactive-primary-default); }
      .button.no-bkgrd:active, .no-bkgrd.icon-button:active, .no-bkgrd.slider-button:active {
        background: none; }
    .button.no-bkgrd-link, .no-bkgrd-link.icon-button, .no-bkgrd-link.slider-button {
      border: 0px;
      background: none;
      color: var(--semantic-color-content-status-info-primary);
      padding-right: 0px; }
      .button.no-bkgrd-link:hover, .no-bkgrd-link.icon-button:hover, .no-bkgrd-link.slider-button:hover {
        color: var(--semantic-color-content-interactive-primary-default); }
    .button + .button, .icon-button + .button, .slider-button + .button, .button + .icon-button, .icon-button + .icon-button, .slider-button + .icon-button, .button + .slider-button, .icon-button + .slider-button, .slider-button + .slider-button {
      margin-left: 5px; }
    .button.hidden, .hidden.icon-button, .hidden.slider-button {
      display: none; }
    .button.-js-force-logout-all, .-js-force-logout-all.icon-button, .-js-force-logout-all.slider-button {
      max-width: 280px; }
    .button .fas, .icon-button .fas, .slider-button .fas {
      margin-right: 5px; }
  
  .dialog-footer {
    display: flex;
    align-content: space-between;
    background: var(--semantic-color-background-primary);
    border-top: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0 0 5px 5px;
    padding: 16px;
    width: 100%;
  }
  
  .dialog-footer  .dialog-footer-left {
    display: inline-block;
    height: 100%;
    text-align: left;
    vertical-align: top;
    width: 75%;
  }
  
  .dialog-footer  .dialog-footer-right {
    text-align: right;
    width: 25%;
  }
  
  .tooltip-navlink {
    text-decoration: none;
    color: $anchor-color;
  }
.break-spaces {
    white-space: break-spaces;
}

// IN-STYLES remove CSS codes
.ec-root-page {
    position: relative;
}
    .padding-0-2em { 
        padding: 2px !important;
    };

    .padding-1em { 
        padding: 10px !important;
    };

    .inline-line-chart-color-blue {color:'#00B5BF'}
    .inline-line-chart-color-purple {color:'#8884d8'}
    .inline-line-chart-color-green {color:'#82ca9d'}
    .inline-line-chart-color-light-blue {color:'#8dd1e1'}
    .inline-line-chart-color-yellow-green {color:'#a4de6c'}

    .padding-right-1em { 
        padding-right: 10px !important;
    };

    .color-content-default-main {
        color: var(--content-default-main, #005F99 ) !important;
    }

    .color-error-content-default {
        color: var(--status-error-content-default, #8B1A4B ) !important;
    }

    .color-status-neutral-secondary {
        color: var(--content-status-neutral-secondary, #69696A ) !important;
    }

    .color-inherit {
        color: inherit !important;
    } 

    .font-size-x-small {
        font-size: 'x-small' !important;
    }
    .font-size-12px {
        font-size: 12px !important;
    }
    .font-size-13px {  
        font-size: 13px !important;
    }
    .font-size-14px {  
        font-size: 14px !important;
    }


    .opacity-0 {
        opacity: 0 !important;
    }
    
    .opacity-0-5 {
        opacity: 0.5 !important;
    }
    
    .opacity-1 {
        opacity: 1 !important;
    }

    .height-100 { 
        height: 100% !important;
    }
    .height-21em { 
        height: 21em !important;
    }
    .height-40em { 
        height: 40em !important;
    }
    .height-41em { 
        height: 41em !important;
    }
    .height-525px { 
        height: 525px !important;
    }
    .circle-outline-0 {
        outline: 0 !important;
    }
    .side-widget-inline-style { 
        width: 243.75px !important;
        height: 300px !important;
        float: 'left' !important;
    };
    .side-widget-inline-style-w-34 { 
        width: 243.75px !important;
        height: 340px !important;
        float: left !important;
    };

    .inline-print-view-button-container {
        height: 60px;
        padding-top: 16px;
        padding-bottom: 16px;
    }
    .inline-print-slick-arrow-slick-next {
        display: block; 
        height: 98.45px;
        padding-top: 40px;
        padding-right: 20px;
    }

    .inline-print-slick-arrow-slick-prev {
        display: block; 
        height: 98.45px;
        padding-top: 40px;
        padding-left: 10px;
    }
    .inline-height-auto-padding-bottom-0-75em {
        height: auto;
        padding-bottom: 7.5px;
    }


    .fontWeight-8-fontSize-8 {
        font-weight: 8 !important;
        font-size: 8px !important;
    }

    .fontSize-13 {
        font-size: 13px !important;
    }

    .fontWeight-10-fontSize-10 {
        font-weight: 10 !important;
        font-size: 10px !important;
    }

    .inline-style-chart-container {
        height: var(--chart-height) !important;
        /* Add any additional styles here */
    }
    .height-351px {
        height: 351px !important;
    }
    .min-height-33px {
        height: 33px !important;
    }

    .font-weight-600 {  
        font-weight: 600 !important;
    }
    .font-weight-400 {  
        font-weight: 400 !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }
    .justify-content-space-between {
        justify-content: space-between !important;
    }

    .inline-style-dropdown-multiselect-wrapper {
        width: var(--dropdown-width) !important;
        max-width: var(--dropdown-max-width) !important;
    }
    .inline-style-var-chart-height {
        height:  var(--chart-height)  !important;
    }
    .inline-style-var-outlineStyle {
        outline:  var(--outlineStyle)  !important;
    }
    .inline-style-dropdown-multiselect-wrappe-width {
        width: var(--dropdown-width) !important;
    }

    .margin-left-0 { 
        margin-left: 0 !important;
    }
    .margin-left-4px { 
        margin-left: 4px !important;
    }
    .margin-right-4px { 
        margin-right: 4px !important;
    }
    .cursor-pointer { 
        cursor: 'pointer'  !important;
    }; 
    .cursor-not-allowed { 
        cursor: 'not-allowed'  !important;
    }; 
    .inline-border-default {
        border: 1px solid var(--semantic-color-border-base-primary)
    }
    .inline-border-none {
        border: none !important;
    }
    .inline-style-height-100 {
        height: 100% !important;
    }
    .inline-style-width-auto {
        width: auto !important;
    }
    .inline-style-width-100 {
        width: 100% !important;
    }
    .inline-style-width-100-px {
        width: 100px;
    }
    .inline-style-width-1025-px {
        width: 1025px;
    }
    .inline-style-width-145em {
        width: 1450px !important;
    }
    .inline-style-width-380px {
        width: 380px !important;
    }
    .inline-style-word-wrap-break-word {
        word-wrap: break-word !important;
    }
    .inline-style-min-height-48 {
        min-height: 48px !important;
    }
    .inline-style-min-height-480 {
        min-height: 480px !important;
    }
    .inline-style-min-width-360 {
        min-width: 360px !important;
    }
    .inline-style-min-width-724 {
        min-width: 724px !important;
    }
    .inline-style-width-724 {
        width: 724px !important;
    }
    .inline-style-float-left {
        float: left !important;
    }
    .inline-style-float-right {
        float: right !important;
    }

    .inline-style-display-contents {
        display: contents !important;
    }
    .inline-style-display-inline {
        display: inline !important;
    }
    .inline-style-display-inline-block {
        display: inline-block !important;
    }
    .inline-style-display-block {
        display: block !important;
    }
    .inline-style-display-none {
        display: none !important;
    }
    .inline-style-position-relative {
        position: relative !important;
    }
    .inline-style-position-absolute {
        position: absolute !important;
    }
    .inline-style-z-index-999999 {
        z-index: 999999 !important;
    }
    .inline-style-opacity-0 {
        opacity: 0 !important;
    }
    .margin-bottom-4-8px { 
        margin-bottom: 4.8px !important;
    }
    .margin-top-0px { 
        margin-top: 0 !important;
    }
    .margin-top-3-2px { 
        margin-top: 3.2px !important;
    }

    .margin-auto {  
        margin: auto !important;
    }
    .padding-top-7px { 
        padding-top: 7px !important;
    }
    .padding-top-61px { 
        padding-top: 61px !important;
    }
    .padding-bottom-7px { 
        padding-bottom: 7px !important;
    }

    .padding-left-4-8px { 
        padding-left: 4.8px !important;
    }
    .padding-0-2em { 
        padding: 0.2em !important;
    }
    .padding-1em { 
        padding: 10px !important;
    }
    .padding-16px { 
        padding: 16px !important;
    }
    .padding-top-0 { 
        padding-top: 0 !important;
    }


    .inline-style-top-0 { 
        top: 0 !important;
    }

    .inline-style-width-50px { 
        width: 50px !important;
    }
    .inline-style-width-50-percent { 
        width: 50% !important;
    }

    .inline-style-margin-right-0 { 
        margin-right: 0 !important;
    }
    .inline-style-margin-right-100 { 
        margin-right: 100 !important;
    }
    .inline-style-height-40px { 
        width: 40px !important;
    }
    .inline-style-overflow-y-hidden { 
        overflow-y: hidden !important;
    }

    .width-calc-100-minus-50px {
        width: calc(100% - 50px);
    }

    .width-calc-15-minus-0px {
        width: calc(15% - 0px);
    }

    .width-calc-16-minus-0px {
        width: calc(16% - 0px); 
    }

    .width-calc-19-minus-0px {
        width: calc(19% - 0px);
    }

    .width-calc-25-minus-0px {
        width: calc(25% - 0px);
    }

    .inline-style-width-calc-100-minus-30px {
        width: calc(100% - 30px);
    }

    .inline-help-iframes-container-outer-width-height {
        width: var(--inline-help-width);
        height: var(--inline-help-height);
    }

    .inline-ellipsis-left-top {
        left: var(--inline-x-ellipsis);
        top: var(--inline-y-ellipsis);
    }

    .inline-var-color {
        color: var(--inline-color);
    }
    .inline-var-opacity {
        opacity: var(--inline-opacity);
    }

    .inline-var-height {
        height: var(--inline-height);
    }



    @for $i from 1 through 10 {
        .inline-filler:nth-child(#{$i}) {
        background-color: var(--filler-color-#{$i - 1});
        }
    }

    .svg-inline--fa {
        display: var(--fa-display, inline-block);
        height: 1em;
        overflow: visible;
        vertical-align: -0.125em;
    }

    svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
        overflow: visible;
        box-sizing: content-box;
    }

    .svg-inline--fa.fa-pull-right {
        margin-left: var(--fa-pull-margin, 0.3em);
            width: auto;
    }
}