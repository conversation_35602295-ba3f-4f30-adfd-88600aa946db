@mixin DisplayFlex {
    display: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */
    display: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */
    display: -ms-flexbox;      /* TWEENER - IE 10 */
    display: -webkit-flex;     /* NEW - Chrome */
    display: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */
  }
  
  @mixin inline-block($vertical-align: top) {
      display: inline-block;
      vertical-align: $vertical-align;
    }
    
  @mixin addColorToChart($solid, $opacity) {
    .recharts-layer {
        path:nth-child(1) {
            fill: $opacity,
        }
  
        path:nth-child(2) {
            stroke: $solid,
        }
  
        &.recharts-active-dot>.recharts-dot {
            fill: $solid;
        }
    }
  
    .recharts-curve.recharts-tooltip-cursor {
        stroke: $solid;
    }
  
    .recharts-tooltip-wrapper {
        color: $solid;
    }
  }
  
  @mixin addColorToCheckbox($solid) {
    input:checked {
        ~.label-text {
            color: $solid;
        }
  
        ~.checkmark {
            border: 1px solid $solid;
  
            span {
                border: solid $solid;
                border-width: 0 2.5px 2.5px 0;
  
            }
        }
    }
  }
  
  /* Display as inline-block */
  @mixin inline-block($vertical-align: top) {
      display: inline-block;
      vertical-align: $vertical-align;
  }
  
  /* remove browser defaults for list tags */
  @mixin nav-list-reset {
      list-style: none;
      margin: 0;
      padding: 0;
  }
  
  /* Prevent text overflow */
  @mixin no-wrapping {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }


    /* Display as inline-block */
    @mixin inline-block($vertical-align: top) {
        display: inline-block;
        vertical-align: $vertical-align;
    }

    /* remove browser defaults for list tags */
    @mixin nav-list-reset {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    /* Prevent text overflow */
    @mixin no-wrapping {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
