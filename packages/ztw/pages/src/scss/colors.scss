
//Primary Colors	
$white: var(--semantic-color-surface-base-primary);
$light-blue: #99D8FF;
$medium-blue: #0076BE;
$standard-blue: var(--semantic-color-content-interactive-primary-default);
$dark-blue: #003F66;
$black: #000000;

//Text Colors
$medium-gray-disabled:#CACBCC;
$dark-gray-light-text: #979899;
$darker-gray-text:#656666;
$darkest-gray-text:#19191A;

//Background Colors
$lightest-gray-background: var(--semantic-color-surface-base-secondary);
$lighter-gray-background:#F1F2F3;
$lightGray-border: #E3E5E6;
$lightest-blue: #F2FAFF;
$lightest-blue2: #388ECF;
$lightest-teal:#EBF9FA;
$lightest-green: #F2FAEB;
$lightest-yellow: #FFF8E6;
$lightest-orange: #FFEFE6;
$lightest-red: #FFE7E6;
$lightest-purple: #F3E6FF;
$lightest-indigo:#E6E9FF;

//Extensive Colors
$light-teal:#A1E2E6;
$medium-teal:#00B5BF;
$standard-teal:#007980;
$dark-teal: #003C40;
$light-green: #BFE6A1;
$medium-green: #77B347;
$standard-green: #398000;
$dark-green: #1C4000;
$light-yellow: #FFDB80;
$medium-yellow: #FFB700;
$standard-yellow: #CC9200;
$dark-yellow: #996E00;
$light-orange: #FFB080;
$medium-orange: #FF8133;
$standard-orange: #CC6729;
$dark-orange: #993B00;
$light-red: #FF8680;
$medium-red: #FF4A40;
$standard-red: #BF0A00;
$dark-red: #660500;
$light-purple: #C580FF;
$medium-purple: #A940FF;
$standard-purple: #8C00FF;
$dark-purple: #460080;
$light-indigo: #808EFF;
$medium-indigo: #4056FF;
$standard-indigo: #001EFF;
$dark-indigo: #001299;


//Dark Mode
$dark-mode-blue-blue-1-primary: #61BBF2;
$dark-mode-blue-blue-2: #4792C1;
$dark-mode-blue-blue-3: #144766;
$dark-mode-green-green-1: #9ECC7A;
$dark-mode-green-green-2: #668B4A;
$dark-mode-green-green-3: #1C330A;
$dark-mode-indigo-indigo-1: #808FFF;
$dark-mode-indigo-indigo-2: #555FBF;
$dark-mode-indigo-indigo-3: #141E66;
$dark-mode-orange-orange-1: #F2A879;
$dark-mode-orange-orange-2: #B8774E;
$dark-mode-orange-orange-3: #663414;
$dark-mode-purple-purple-1: #C680FF;
$dark-mode-purple-purple-2: #8E53BF;
$dark-mode-purple-purple-3: #411466;
$dark-mode-red-red-1: #E68E8A;
$dark-mode-red-red-2: #B25E57;
$dark-mode-red-red-3: #661914;
$dark-mode-teal-teal-1: #7AC8CC;
$dark-mode-teal-teal-2: #4B878B;
$dark-mode-teal-teal-3: #0A3133;
$dark-mode-yellow-yellow-1: #FFDA80;
$dark-mode-yellow-yellow-2: #BF9F53;
$dark-mode-yellow-yellow-3: #664F14;
$dark-mode-gray-gray-1-text: #CACBCC;
$dark-mode-gray-gray-2-text: #979899;
$dark-mode-gray-gray-3-disabled: #656666;
$dark-mode-gray-gray-4-border: #323333;
$dark-mode-gray-gray-5-background: #262626;
$dark-mode-gray-gray-6-background: var(--semantic-color-content-base-primary);
$dark-mode-gray-gray-7-background: #0D0D0D;


$black: #000000;
// $black2: rgba(0, 0, 0, 0.4);
// $black3: rgba(255,255,255,0.9);
$black4: rgba(0,0,0,0.05);
// $blackShadow1:  rgba(195, 202, 213, 0.5);
$blackShadow2: rgba(176,186,197,0.6);

// $greyShadow1: rgba(174,176,176,0.69);
// $greyShadow1:rgba(208, 212, 232, 0.91);

$blue1: #002F4B;
$blue2: var(--semantic-color-content-interactive-primary-default);
$blue3: #E4FAFF;
$blue4: #007ea8;
$blue4: #D1DCF3;
$blue5: #638294;
$blue6: #6572FF;
$blue7: #FBFBFB;
$blue8: #009CDA;
$blue9: #F1F7FD;
$blue10: rgba(1, 61, 97, 0.4);
// $blue11: rgba(0,118,190,0.25);
// $blue12: #F2FAFD;
$blue13: #00B6FF;
$blue14: #00B6FF;
$blue15: var(--semantic-color-content-interactive-primary-default);
$blueShadow1: rgba(1, 61, 97, 0.4);
$blueShadow2: rgba(0, 83, 159, 0.5);
// $blueShadow3: rgba(1,61,97,0.4);
$blue16: #E0EDFB;
$blue17: #f1f7fd8f;
$blue18: #142342;
$blue19: #CDDDFE;
$blue20: #17819b;
$blue21: #0176BE;
$blue22: #F2FAFF;
$blue23: #0275be;
$blue24: #0f7cc1;
$blue25: #79B4D8;
$blue26: #E5F1F8;
$blue27: #00bce4;
$blue28: #216ba5;
$blue29: #f2faff;
$blue30: #0077c0;
$blue31: #67acd7;
$blue32: var(--semantic-color-content-interactive-primary-default);
$blue33: #dcedf9;
$blue34: #3498db;
$blue35: #4056ff;
$blue36: #394af5;
$blueBG: #216ba5;
$bluebb: var(--semantic-color-content-interactive-primary-default);


$green1: #5B8C20;
$green2: #73A13C;
$green3: #329F3E;
$green4: #209835;
$green5: #4ABF07;
$green6: rgba(150,226,60,0.2);
$green7: #EEFBEA;
$green8: #64A424;
$green9: #AACF32;
$green10: #6AB4A3;
$green11: #5a9809;
$green12: rgba(115,161,60,0.6);
$green13: #77A440;
$green14: #1C4000;
$green15: #19d219;
$green16: #20923b;
$green17: #77B347;
$green18: #377a00;
$green19: var(--semantic-color-surface-interactive-primary-default);



$grey1: var(--semantic-color-content-base-secondary);
$grey3: #C8CEDA;
$grey4: #6b6d70;
$grey5: #FDFDFD;
$grey6: #FAFAFA;
$grey7: #747272;
$grey8: #CCCCCC;
$grey9: #E6ECF0;
$grey10: #939393;
$grey11: #C5C5C7;
$grey12: #D0D1D3;
$grey13: var(--semantic-color-content-inverted-base-secondary);
$grey14: #E0E1E3;
$grey15: #566C79;
$grey16: rgba(0, 0, 0, 0.4);
$grey17: rgba(255,255,255,0.9);
$grey18: rgba(174,176,176,0.69);
$grey19: rgba(42,44,48,0.6);
$grey20: rgba(85,107,120,0.7);
$grey21: rgba(176, 186, 197, 0.6);
$grey22: rgba(119,121,124,0.35);
$grey23: #313541;
$grey24: #F7F9FF;
$grey25: #D8D8D8;
$grey26: #d0d1d391;
$grey27: #DDDDDD;
$grey28: #B6B7BA;
$grey29: #D6D7DB;
$grey30: #2B2C30;
$grey31: #1D1E22;
$grey32: #efeff1;
$grey33: #F5F6F7;
$grey34: #DAE2EA80;
$grey35: #F1F2F3;
$grey36: #36393F;
$grey37: #D6D7DB;
$grey38: #656666;
$grey39: #A9A9A9;
$grey40: rgba(0,0,0,0.65);
$grey41: #eef6fc;
$grey42: #929292;
$grey43: var(--semantic-color-content-base-primary);
$grey44: #1E1F22;
$grey45: #2A2C31;
$grey46: #232426;
$grey47: #6D7278;
$grey48: #979798;
$grey49: #E9E9E9;
$grey50: #69696A;




$orange1: #EB9E0B;
$orange2: #FF8C00;
$orange3: #E47600;
$orange4: #EAB20D;
$orange5: #E86C00;

$red1: #FFF5F5;
$red2: #FD4239;
$red3: #D13932;
$red4: var(--semantic-color-border-status-danger-active);
$red5: #CC3A33;
$red6: #74100B;
$red7: rgba(206,13,0,0.7);
$red8: rgba(122,19,0,0.75);
$red9: rgba(156,0,0,0.7);
$red11: rgba(138,19,13,0.7);
$red12: #ac1212;

$purple1: #645BC0;
$purple2: #645BC080;

$transparent: transparent;

$yellow1: #FFF47E;
$yellow2: #fffced;
$yellow3: #fef7ec;
$yellow4: #fce7c5;
$yellow5: #fdeeed;
$yellow6: #fadedb;
$yellow7: #ffb700;

$white: var(--semantic-color-surface-base-primary);
$white1: #F3F3F3;
$white2: #F3F6FF;
$white3: #f9fbff;

// Main navigation
$left-nav-bkgrd-color: var(--semantic-color-content-base-primary);
$left-nav-menu-item-bkgrd-selected-color: var(--semantic-color-content-interactive-primary-default);
$left-nav-menu-item-bkgrd-hover-color: #393b3f;
$left-nav-menu-item-hover-border-color: var(--semantic-color-content-interactive-primary-default);
$left-nav-menu-item-icon-color: #f7f9fa;
$left-nav-menu-item-icon-hover-selected-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-separator-color: #656666;
$left-nav-panel-section-header-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-sub-header-color: #6e6e6e;
$left-nav-panel-section-menu-item-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-menu-item-hover-color: #009cff;
$left-nav-panel-section-menu-item-selected-color: #009cff;
$left-nav-panel-section-menu-item-selected-bkgrd-color: $left-nav-bkgrd-color;
$page-footer-bkgrd-color: #0D0D0D;
$page-footer-text-color: var(--semantic-color-surface-base-primary);

//Colors
$body-background-color: #393b3f;
$border-color:  var(--semantic-color-content-base-primary);
$tooltip-popup-border-color: #939393;

//Common
$anchor-color: var(--semantic-color-content-status-info-primary);
$anchor-hover-color: var(--semantic-color-content-interactive-primary-default);
$anchor-disabled-color: #656666;

// Page Content
$page-content-bkgrd-color: #F5F5F6;
$page-content-bkgrd-alt-color: var(--semantic-color-surface-base-primary);
$page-content-footer-border-color: var(--semantic-color-content-base-primary);
$header-title-color: #1E1F22;
$header-title-icon-color: #007980;
$default-text-color: var(--semantic-color-content-base-primary);
$default-border-color: #e0e1e3;
$default-box-shadow: 0 0 8px 0 rgba(42,44,48,0.25);

// Insights
$analytics-left-nav-panel-bkgrd-color: var(--semantic-color-content-base-primary);
$analytics-left-nav-panel-border-color: #393b3f;
$analytics-left-nav-panel-separator-color: #656666;
$analytics-left-nav-panel-tab-selected-border-color: #99D8FF;
$analytics-left-label-color: var(--semantic-color-surface-base-primary);
$analytics-data-border-color: #e0e1e3;
$analytics-left-nav-tab-header-title-color: #99D8FF;
$analytics-left-nav-tab-header-title-selected-color: var(--semantic-color-surface-base-primary);

// Tab
$tab-header-title-color: #005E9B;
$tab-header-title-hover-color: var(--semantic-color-content-interactive-primary-default);
$tab-header-title-selected-color:var(--semantic-color-content-base-primary);
$wizard-tab-text-color: var(--semantic-color-content-base-primary);
$wizard-tab-bkgrd-color: #e0e1e3;
$wizard-tab-active-bkgrd-color: var(--semantic-color-surface-base-primary);
$wizard-tab-complete-bkgrd-color: #9ECC7A;
$wizard-tab-border-color: var(--semantic-color-surface-base-primary);

// Drop Down
$dropdown-bkgrd-color: #f1f2f3;
$dropdown-panel-bkgrd-color: var(--semantic-color-surface-base-primary);
$dropdown-text-color: var(--semantic-color-content-status-info-primary);
$dropdown-text-hover-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-border-color : #e0e1e3;
$dropdown-list-item-text-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-text-hover-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-bkgrd-hover-color : #e4faff;
$dropdown-list-item-bkgrd-active-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-text-active-color : var(--semantic-color-surface-base-primary);

$dropdown-list-item-text-disabled-color : #656666;
$dropdown-list-item-bkgrd-disabled-color : #656666;
$list-builder-footer-box-shadow: rgba(42, 44, 8, 0.15);

// Form
$form-bkgrd-color: #f1f2f3;
$form-section-bkgrd-color: var(--semantic-color-surface-base-primary);
$form-section-label-color: #656666;
$form-input-label-color: var(--semantic-color-content-base-primary);
$form-input-label-alt-color:  var(--semantic-color-content-base-primary);
$form-section-separator: #e0e1e3;
$text-input-bkgrd-color: #f1f2f3;
$text-input-color: #656666;
$text-input-hover-color: var(--semantic-color-content-status-info-primary);
$text-input-disabled-color: #656666;
$checkbox-input-tick-color: var(--semantic-color-surface-base-primary);

//Button
$button-bkgrd-color: var(--semantic-color-content-status-info-primary);
$button-text-color: var(--semantic-color-surface-base-primary);
$button-hover-text-color: #005C9D;
$button-hover-background-color: #006aab;
$button-disabled-background-color: #f7f9fa;
$button-disabled-text-color: #6e6e6e;
$button-no-bkgrd-color:  var(--semantic-color-content-base-primary);

// Radio Button
$radio-button-bkgrd-color: var(--semantic-color-surface-base-primary);
$radio-button-text-color: var(--semantic-color-content-status-info-primary);
$radio-button-hover-bkgrd-color: #e4faff;
$radio-button-active-bkgrd-color: var(--semantic-color-content-status-info-primary);
$radio-button-active-text-color: var(--semantic-color-surface-base-primary);
$radio-button-border-color: var(--semantic-color-content-status-info-primary);


// Table Style
$table-header-cell-text-color: var(--semantic-color-content-base-primary);
$table-header-cell-text-alt-color: #656666;
$table-header-cell-border-color: #e0e1e3;
$table-header-cell-alt-border-color: var(--semantic-color-surface-base-primary);
$table-header-row-bkgrd-color: #f1f2f3;
$table-header-row-alt-bkgrd-color: var(--semantic-color-surface-base-primary);
$table-body-row-bkgrd-color: #f1f2f3;
$table-body-row-alt-bkgrd-color: var(--semantic-color-surface-base-primary);
$table-body-border-color: #e0e1e3;
$table-body-row-expanded-bkgrd-color: #f2fafd;
$table-body-row-expanded-alt-bkgrd-color: #e9f3f6;
$table-body-row-expanded-shadow-color: rgba(0, 90, 119, 0.25);
$datagrid-header-height: 28px;
$datagrid-expanded-table-bkgrd-color: #eef8ff;

// Tooltip
$tooltip-background-color: var(--semantic-color-surface-base-primary);
$tooltip-text-color: var(--semantic-color-content-base-primary);
$tooltip-text-alt-color: #999;

// Widget Styles
$widget-bkgrd-color: var(--semantic-color-surface-base-primary);
$widget-text-color: var(--semantic-color-content-base-primary);
$widget-header-text-hover-color: var(--semantic-color-content-interactive-primary-default);
$widget-header-units-text-color: var(--semantic-color-content-interactive-primary-default);

// Slider
$slider-color-high: #bf0a00;    //red
$slider-color-moderate: #cc6729; //orange
$slider-color-low: #377a00;  //green

//error page text
$error-page-text-color: #13C7F1;

// Print view
$print-banner-background: #f3f3f3;

//Saml error view
$fa-exclamation-circle-color: #fd4239;
$fa-exclamation-triangle-color: #ff8c00;

// Beta feature background color
$beta-feature-background-color: #F4AA00;

// Extras
$input-label-color: var(--semantic-color-content-base-primary);
$login-footer-bkgrd: #f3f3f3;
$report-left-control-background-color: #f3f3f3;

//anomaly-trend-label
$anomaly-trend-chart-user-color: #645BC0;
$anomaly-trend-chart-org-color: #4285c0;

//anomaly-user-timeline-colors
$anomaly-timeline-yellow-color: #fff8e6;
$anomaly-timeline-indigo-color: #E6E9FF;

//indicator-color
$indicator-positive-color: #377a00;
$indicator-negative-color: #bf0a00;

//severity-color
$severity-critical-color: #af2718;
$severity-high-color: #eb594a;
$severity-medium-color: #f08747;
$severity-low-color: #f5b940;

//filter-section
$filter-section-background-color: #F2FAFF;
$calendarWeekColor: #f4f7fa;
$dayInRangeColor: #8897a8;
$anchor-color: var(--semantic-color-content-status-info-primary);
$form-bkgrd-color: #f1f2f3;
// Main navigation
$left-nav-bkgrd-color: var(--semantic-color-content-base-primary);
$left-nav-menu-item-bkgrd-selected-color: var(--semantic-color-content-interactive-primary-default);
$left-nav-menu-item-bkgrd-hover-color: #393b3f;
$left-nav-menu-item-hover-border-color: var(--semantic-color-content-interactive-primary-default);
$left-nav-menu-item-icon-color: #f7f9fa;
$left-nav-menu-item-icon-hover-selected-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-separator-color: #656666;
$left-nav-panel-section-header-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-sub-header-color: #6e6e6e;
$left-nav-panel-section-menu-item-color: var(--semantic-color-surface-base-primary);
$left-nav-panel-section-menu-item-hover-color: #009cff;
$left-nav-panel-section-menu-item-selected-color: #009cff;
$left-nav-panel-section-menu-item-selected-bkgrd-color: $left-nav-bkgrd-color;
$page-footer-bkgrd-color: #0D0D0D;
$page-footer-text-color: var(--semantic-color-surface-base-primary);

//Colors
$body-background-color: #393b3f;
$border-color:  var(--semantic-color-content-base-primary);
$tooltip-popup-border-color: #939393;

//Common
$anchor-color: var(--semantic-color-content-status-info-primary);
$anchor-hover-color: var(--semantic-color-content-interactive-primary-default);
$anchor-disabled-color: #656666;

// Page Content
$page-content-bkgrd-color: #F5F5F6;
$page-content-bkgrd-alt-color: var(--semantic-color-surface-base-primary);
$page-content-footer-border-color: var(--semantic-color-content-base-primary);
$header-title-color: #1E1F22;
$header-title-icon-color: #007980;
$default-text-color: var(--semantic-color-content-base-primary);
$default-border-color: #e0e1e3;
$default-box-shadow: 0 0 8px 0 rgba(42,44,48,0.25);

// Insights
$analytics-left-nav-panel-bkgrd-color: var(--semantic-color-content-base-primary);
$analytics-left-nav-panel-border-color: #393b3f;
$analytics-left-nav-panel-separator-color: #656666;
$analytics-left-nav-panel-tab-selected-border-color: #99D8FF;
$analytics-left-label-color: var(--semantic-color-surface-base-primary);
$analytics-data-border-color: #e0e1e3;
$analytics-left-nav-tab-header-title-color: #99D8FF;
$analytics-left-nav-tab-header-title-selected-color: var(--semantic-color-surface-base-primary);

// Tab
$tab-header-title-color: #005E9B;
$tab-header-title-hover-color: var(--semantic-color-content-interactive-primary-default);
$tab-header-title-selected-color: var(--semantic-color-content-base-primary);
$wizard-tab-text-color: var(--semantic-color-content-base-primary);
$wizard-tab-bkgrd-color: #e0e1e3;
$wizard-tab-active-bkgrd-color: var(--semantic-color-surface-base-primary);
$wizard-tab-complete-bkgrd-color: #9ECC7A;
$wizard-tab-border-color: var(--semantic-color-surface-base-primary);

// Drop Down
$dropdown-bkgrd-color: #f1f2f3;
$dropdown-panel-bkgrd-color: var(--semantic-color-surface-base-primary);
$dropdown-text-color: var(--semantic-color-content-status-info-primary);
$dropdown-text-hover-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-border-color : #e0e1e3;
$dropdown-list-item-text-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-text-hover-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-bkgrd-hover-color : #e4faff;
$dropdown-list-item-bkgrd-active-color : var(--semantic-color-content-interactive-primary-default);
$dropdown-list-item-text-active-color : var(--semantic-color-surface-base-primary);

$dropdown-list-item-text-disabled-color : #656666;
$dropdown-list-item-bkgrd-disabled-color : #656666;
$list-builder-footer-box-shadow: rgba(42, 44, 8, 0.15);

// Form
$form-bkgrd-color: #f1f2f3;
$form-section-bkgrd-color: var(--semantic-color-surface-base-primary);
$form-section-label-color: #656666;
$form-input-label-color: var(--semantic-color-content-base-primary);
$form-input-label-alt-color:  var(--semantic-color-content-base-primary);
$form-section-separator: #e0e1e3;
$text-input-bkgrd-color: #f1f2f3;
$text-input-color: #656666;
$text-input-hover-color: var(--semantic-color-content-status-info-primary);
$text-input-disabled-color: #656666;
$checkbox-input-tick-color: var(--semantic-color-surface-base-primary);

//Button
$button-bkgrd-color: var(--semantic-color-content-status-info-primary);
$button-text-color: var(--semantic-color-surface-base-primary);
$button-hover-text-color: #005C9D;
$button-hover-background-color: var(--semantic-color-content-interactive-primary-default);
$button-disabled-background-color: #f7f9fa;
$button-disabled-text-color: #6e6e6e;
$button-no-bkgrd-color:  var(--semantic-color-content-base-primary);

// Radio Button
$radio-button-bkgrd-color: var(--semantic-color-surface-base-primary);
$radio-button-text-color: var(--semantic-color-content-status-info-primary);
$radio-button-hover-bkgrd-color: #e4faff;
$radio-button-active-bkgrd-color: var(--semantic-color-content-status-info-primary);
$radio-button-active-text-color: var(--semantic-color-surface-base-primary);
$radio-button-border-color: var(--semantic-color-content-status-info-primary);


// Table Style
$table-header-cell-text-color: var(--semantic-color-content-base-primary);
$table-header-cell-text-alt-color: #656666;
$table-header-cell-border-color: #e0e1e3;
$table-header-cell-alt-border-color: var(--semantic-color-surface-base-primary);
$table-header-row-bkgrd-color: #f1f2f3;
$table-header-row-alt-bkgrd-color: var(--semantic-color-surface-base-primary);
$table-body-row-bkgrd-color: #f1f2f3;
$table-body-row-alt-bkgrd-color: var(--semantic-color-surface-base-primary);
$table-body-border-color: #e0e1e3;
$table-body-row-expanded-bkgrd-color: #f2fafd;
$table-body-row-expanded-alt-bkgrd-color: #e9f3f6;
$table-body-row-expanded-shadow-color: rgba(0, 90, 119, 0.25);
$datagrid-header-height: 28px;

// Tooltip
$tooltip-background-color: var(--semantic-color-surface-base-primary);
$tooltip-text-color: var(--semantic-color-content-base-primary);
$tooltip-text-alt-color: #999;

// Widget Styles
$widget-bkgrd-color: var(--semantic-color-surface-base-primary);
$widget-text-color: var(--semantic-color-content-base-primary);
$widget-header-text-hover-color: var(--semantic-color-content-interactive-primary-default);
$widget-header-units-text-color: var(--semantic-color-content-interactive-primary-default);

// Slider
$slider-color-high: #bf0a00;    //red
$slider-color-moderate: #cc6729; //orange
$slider-color-low: #377a00;  //green

//error page text
$error-page-text-color: #13C7F1;

// Print view
$print-banner-background: #f3f3f3;

//Saml error view
$fa-exclamation-circle-color: #fd4239;
$fa-exclamation-triangle-color: #ff8c00;

// Beta feature background color
$beta-feature-background-color: #F4AA00;

// Extras
$input-label-color: var(--semantic-color-content-base-primary);
$login-footer-bkgrd: #f3f3f3;
$report-left-control-background-color: #f3f3f3;

//anomaly-trend-label
$anomaly-trend-chart-user-color: #645BC0;
$anomaly-trend-chart-org-color: #4285c0;

//anomaly-user-timeline-colors
$anomaly-timeline-yellow-color: #fff8e6;
$anomaly-timeline-indigo-color: #E6E9FF;

//indicator-color
$indicator-positive-color: #377a00;
$indicator-negative-color: #bf0a00;

//severity-color
$severity-critical-color: #af2718;
$severity-high-color: #eb594a;
$severity-medium-color: #f08747;
$severity-low-color: #f5b940;

//filter-section
$filter-section-background-color: #F2FAFF;
$clr-stroke-disabled: #DDE3EA;
$deep-blue-content-default-subdued: #3F3F40;
//should be changed
// :export {
//   blue2: $blue2;
//   blue12: $blue12;
//   blackShadow2: $blackShadow2;
//   blue6: $blue6;
//   blue16: $blue16;
//   green2: $green2;
//   green3: $green3;
//   green8: $green8;
//   green9: $green9;
//   grey27: $grey27;
//   grey28: $grey28;
//   green10: $green10;
//   grey4: $grey4;
//   grey7: $grey7;
//   grey11: $grey11;
//   grey14: $grey14;
//   orange1: $orange1;
//   orange2: $orange2;
//   orange3: $orange3;
//   orange4: $orange4;
//   orange5: $orange5;
//   grey26: $grey26;
//   red2: $red2;
//   red3: $red3;
//   white: $white;
//   red6: $red6;
//   red7: $red7;
//   red8: $red8;
//   red9: $red9;
//   red11: $red11;
//   white: $white;
//   blue20: $blue20;
//   transparent: $transparent;
//   calendarWeekColor:$calendarWeekColor;
//   dayInRangeColor: $dayInRangeColor;
// }
