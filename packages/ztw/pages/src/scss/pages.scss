/*
    Main Styles:

    -   Page header, footer, and main content area
    -   Page navigation
*/
@import 'scss/mixins.scss';
@import 'scss/colors.scss';

@import 'scss/admin.scss';
@import 'scss/table/_all.scss';

//Fonts

/* Logout Button */
.ec-root-page {
    .logout-button {
        color: white;
        line-height: 20px;
        padding: 0 5px 0 10px;
    }

    /* Main */
    .main {
        height: 100%;
        //padding: 24px;
        position: relative;
        width: 100%;
        overflow-y: auto;
    }

    #main-page {
        height: 100%;
    }

    /* Main Content */
    .main-content {
        width: 100%;
        height: calc(100% - 80px);
        padding: 0 24px 24px;
        overflow-y: auto;
    }

    .main-report-content {
        width: 100%;
        height: calc(100% - 60px);
        overflow-y: auto;
    }

    /* Main Header */
    .main-header {
        padding: 24px;
        width: 100%;
        height: 80px;

        .column {
            height: 31px;
            padding: 1px 0;
        }
    }

    .dashboard-header-left {
        height: 100%;
        width: 33.33%;
    }

    .dashboard-header-center {
        text-align: center;
        width: 33.33%;
        &.column {
            padding: 0;
        }
    }

    .dashboard-header-right {
        text-align: right;
        width: 33.34%;

        &.column {
            padding: 5px 0;
        }
    }

    .main-header-left {
        width: 50%;
    }

    .main-header-right {
        width: 50%;
        text-align: right;
        .control-button-reports {
            margin-right: 25px;
            position: static;
            max-width: 25px;
            /*display: inline-block;*/
            vertical-align: middle;
            i {
                font-size: 16px;
                position: relative;
                top: 1px;
                margin-right: 0px;
            }
        }
    }

    .main-header-right-icons {
        font-size: 8px;
        margin-left: 32px;
        color: $button-bkgrd-color;
        cursor: pointer;
        display: inline-block;
        position: relative;
        vertical-align: middle;

        &:hover {
            color: $button-hover-background-color;
        }

        &:first-child {
            margin: 0px;
        }

        &.add-note .fa-plus-circle {
            font-size: 12px;
            position: absolute;
            top: 12px;
            left: -5px;
        }

        &.reset-dashboard-icon {
            color:  var(--semantic-color-content-base-primary);
            &:hover {
                color: #939393;
            }
        }

        &.hidden {
            display: none;
        }
    }

    .main-header-title {
        font-size: 24px;
        color: $header-title-color;
    }

    .dashboard-header {
        .main-header-title {
            display: inline-block;
            width: auto;
            max-width: 100%;
            padding: 0px 8px 4px 0;
            @include no-wrapping;

            &:active, &:focus{
                border:none;
                outline:none;
                border-bottom: 1px solid $dropdown-border-color!important;
                text-overflow: initial;
            }

            &:hover {
                border-bottom: 1px solid $button-bkgrd-color;
            }

            &.disable-edit {
                &:active, &:focus, &:hover {
                    border-bottom: 0px!important;
                }
            }
        }
    }

    /* Page */
    .page {
        height: 100vh;
        min-height: 720px;
        min-width: 1060px;
        position: absolute;
        width: 100%;
    }

    /* Page Content */
    .page-content {
        background: $page-content-bkgrd-color;
        bottom: 37px;
        //min-height: calc(100% - 73px - 37px);
        overflow: hidden;
        position: absolute;
        left: 90px;
        top: 0px;
        width: calc(100% - 90px);
    }

    .page-scrollable {
        overflow: auto;
    }

    .page-layout {
        height: 100%;
        width: 100%;
    }

    .page-sidebar {
        position: absolute;
        z-index: 1;
        top: 21px;
        left: 24px;

        .page-sidebar-back-button {
            padding: 0px;
            
            &:hover {
                text-decoration: none;
            }

            .sidebar-back-button {
                padding-left: 8px;
            }
        }
    }

    .page-main {
        bottom: 0;
        left: 0px;
        overflow: hidden;
        position: absolute;
        right: 0px;
        top: 0;
    }

    .page-title {
        color: var(--semantic-color-content-base-primary);
        padding: 24px;
        font-size: 24px;
        font-weight: 500;
        line-height: 24px;

        &.back-sidebar-present {
            padding-left: 100px;
        }

        .new-label {
            display: inline-block;
            vertical-align: top;
            margin-left: 8px;
            color: $anchor-color;
        }
    }

    .page-main-content {
        bottom: 0;
        left: 0;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 24px 24px 24px;
        //position: absolute; // inline style added to PageLayoutTemplate to fix IE: BUG-20815
        right: 0;
        top: 80px;
    }

    .page-sub-header {
        color: var(--semantic-color-content-interactive-primary-default);
        font-size: 16px;
        height: 28px;
        line-height: 28px;
        margin-top: 10px;
        text-align: left;
        width: 100%;

        &:first-child {
            margin-top: 0;
        }
    }

    /* Page Footer */
    .page-footer {
        background: $page-footer-bkgrd-color;
        bottom: 0;
        color: $page-footer-text-color;
        font-size: 13px;
        height: 37px;
        line-height: 18px;
        position: absolute;
        width: 100%;
        padding: 8px 24px;
        border-top: 1px solid $default-border-color;
    }

    .page-footer-left {
        display: inline-block;
        height: 100%;
        text-align: left;
        vertical-align: top;
        width: 50%;
        @include no-wrapping;

        .copyright-container {
            display: inline-block;
            vertical-align: middle;
            padding-right: 8px;
        }

        .cloud-version-container {
            display: none;
            vertical-align: middle;
            border-left: 1px solid $page-footer-text-color;
            padding-left: 8px;
            padding-right: 8px;
        }

        .patents-container {
            @include inline-block(middle);
            border-left: 1px solid $page-footer-text-color;
            padding-left: 8px;

            .patents-link-container{
                color: $anchor-color;

                &:hover {
                    color: $anchor-hover-color;
                }
            }
        }

        .powered-by {
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
        }

        .zscaler-logo {
            max-height: 14px;
            display: inline-block;
            vertical-align: middle;
        }
    }

    .page-footer-right {
        @extend .page-footer-left;
        text-align: right;

        .weblog-time-container {
            color: $page-footer-text-color;
            padding-right: 8px;
        }

        .last-updated-time-container {
            padding-left: 8px;
            border-left: 1px solid $page-footer-text-color;
        }
    }

    /* Page Header */

    .page-header {
        background: $left-nav-bkgrd-color;
        bottom: 37px;
        position: absolute;
        top: 0px;
        width: 90px;
    }

    .page-header-logo-container {
        background: $left-nav-bkgrd-color;
        display: inline-block;
        height: 103px;
        padding: 30px 16px 11px 16px;
        text-align: center;
        width: 100%;
        vertical-align: middle;

        .product-text {
            height: 20px;
            color: var(--semantic-color-surface-base-primary);
            font-weight: 500;
            font-size: 11px;
            letter-spacing: 0;
            line-height: 20px;
        }
    }

    .login-page-header-logo-container {
        margin: auto;
    }

    .page-header-logo {
        height: auto;
        max-height: calc(100% - 20px);
        width: 100%;

        &.animate {
            transition: all .1s ease-in-out;
        }

        &:active {
            transform: scale(0.95);
        }
    }

    /* Navigation Menus */
    .page-header-navbar {
        height: 100%;
        width: 100%;
    }

    .page-header-contents {
        height: 100%;
        width: 100%;
    }

    // body {
    //     .ecui-block-eusa-notification {
    //         position: absolute;
    //         left: calc(50% - 136px);
    //         top: 8px;
    //         z-index: 10;

    //         > span {
    //             display: inline-block;
    //             cursor: pointer;
    //             color: #f4aa00;
    //             font-size: 11px;

    //             span {
    //                 padding-left: 5px;
    //                 font-weight: 500;
    //             }
    //         }
    //     }
    // }

    .tooltip {
        &.bottom-middle-pending-eusa {
            &::before {
                border-bottom-color: $tooltip-popup-border-color;
                border-top: none;
                right: 250px;
                top: -6px;
            }
            &::after {
                border-bottom-color: var(--semantic-color-surface-base-primary);
                border-top: none;
                right: 251px;
                top: -5px;
            }
        }
    }

    .POCAccount {
        .page-header-navbar-top {
            width: calc(100% - 290px);
        }
        .page-header-navbar-down {
            width: 290px;
        }
    }

    .page-header-navbar-top {
        display: inline-block;
        min-height: calc(100% - 155px);
        text-align: left;
        vertical-align: top;
        width: 100%;
    }

    .page-header-navbar-down {
        display: block;
        height: 114px;
        vertical-align: top;
        width: 100%;

        .fas {
            font-size: 16px !important;
        }

        .user-logout-menu {
            margin-bottom: 6px;
        }

        //to position the tooltip
        .user-logout-position {
            width: 90px;
            margin-left: -6px;
        }
    }

    .nav-menus {
        height: calc(100% - 52px);
        display: block;
        vertical-align: middle;
    }

    .nav-menu {
        display: block;
        font-size: 11px;
        height: 72px;
        line-height: 20px;
        vertical-align: top;
        margin-bottom: 1px;
    }

    .nav-menu-header {
        color: $left-nav-menu-item-icon-color;
        cursor: pointer;
        display: block;
        height: 100%;
        padding-top: 18px;
        text-align: center;

        text-decoration: none;
        white-space: nowrap;
        width: 100%;
        border-left: 4px solid $left-nav-bkgrd-color;
        border-right: 4px solid $left-nav-bkgrd-color;

        &.selected {
            border: none !important;
            background: $left-nav-menu-item-bkgrd-selected-color !important ;
            color: $left-nav-menu-item-icon-hover-selected-color !important;
        }

        &.active {
            text-decoration: none;
            border-left: 4px solid $left-nav-menu-item-hover-border-color;
            border-right: 4px solid $left-nav-menu-item-bkgrd-hover-color;
            color: $left-nav-menu-item-icon-hover-selected-color;
            background: $left-nav-menu-item-bkgrd-hover-color;
        }
    }

    .nav-menu-header-label {
        position: relative;
        padding-bottom: 15px;
        padding-top: 4px;
        font-weight: 500;
    }

    .nav-menu-icon {
        font-size: 24px !important;
        display: block;
    }

    .nav-menu-panel {
        background: $left-nav-menu-item-bkgrd-hover-color;
        border-top: 0px;
        -webkit-box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
        -moz-box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
        box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
        display: none;
        margin: 0px;
        width: 250px;
        list-style: none;
        padding: 4px 12px 4px 0px;
        position: absolute;
        top: 0px;
        bottom: 0px;
        -webkit-transition: all .2s ease-in-out;
        transition: all .2s ease-in-out;
        text-align: left;
        z-index: 9998;

        &.large {
            width: 480px;
        }

        &.dashboard-panel {
            padding: 16px 8px 0 8px;

            .nav-menu-section {
                width: 100%;
            }
        }

        &.analytics-panel {
            width: 300px;
            padding: 24px 8px 0 0;
            
            .nav-menu-section {
                width: 100%;
            }
        }

        &.policy-panel {

            .nav-menu-panel-item {
                border-bottom: 1px solid $left-nav-panel-section-separator-color;

                &:last-child {
                    border-bottom: none;
                }
            }
            
            .nav-menu-panel-item-header {
                width: 100%;
            }
        }

        &.admin-panel {
            @extend .policy-panel;
            width: 550px;
        }

        &.help-panel {
            width: 280px;

            li.nav-menu-section{
                padding-bottom: 26px;
            }
        }
    }


    .nav-menu-section, .nav-menu-list, .nav-menu-list-item {
        display: block;
    }

    .nav-menu-section {
        width: 50%;
        display: inline-block;
        vertical-align: top;
        padding: 0 0 20px 8px;

        &.hidden {
            display: none;
        }
    }
    .nav-menu-error-message {
        cursor: pointer;
        margin-right: 5px;
    }

    .nav-menu-header-section {
        color: #939393;
        font-size: 12px;
        font-weight: normal;
        line-height: 16px;
        padding: 0px 16px;
        width: 100%;
        margin: 0px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .nav-menu-panel-item {
        display: inline-block;
        vertical-align: top;
        width: 100%;
        padding-top: 20px;
    }

    .nav-menu-panel-item-header {
        color: $left-nav-panel-section-header-color;
        font-size: 16px;
        font-weight: 500;
        line-height: 16px;
        white-space: nowrap;
        margin: 0px;
        width: 100%;
        padding: 0 0px 16px 20px;

        i.fa-angle {
            display: none;
        }

        i.fas {
            font-size: 13px;
            line-height: 13px;
        }
    }

    .nav-menu-panel-item-header-label {
        margin-left: 8px;
        vertical-align: top;
    }

    .nav-menu-list {
        @include nav-list-reset;
    }

    .nav-menu-section-header {
        color: $left-nav-panel-section-sub-header-color;
        font-size: 11px;
        text-transform: uppercase;
        font-weight: 500;
        line-height: 16px;
        width: 100%;
        margin: 0px;
        padding: 0 0 4px 12px;
        @include no-wrapping;

        .nav-menu-section-header-icon {
            margin-right: 5px;
            font-size: 16px;
        }

        &.auth-config-overflow {
            cursor: default;
        }

        .nav-menu-section-header-text {
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            @include no-wrapping;

            &.new-label-text {
                width: auto;
                max-width: calc(100% - 62px);
            }
        }
    }

    .nav-menu-list-item {
        @include no-wrapping;
        color: $left-nav-panel-section-menu-item-color;
        cursor: pointer;
        display: block;
        font-size: 13px;
        font-weight: 500;
        text-align: left;
        width: 100%;
        height: 36px;
        padding: 0 12px;

        .fa-external-link {
            font-size: 12px;
            line-height: 12px;
            padding-left: 4px;        
            margin: 0px;
        }

        .nav-menu-list-item-text {
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            @include no-wrapping;
            // line-height: 24px;

            &.new-label-text {
                width: auto;
                max-width: calc(100% - 46px);
            }
        }

        &:hover {
            color: $left-nav-panel-section-menu-item-hover-color;
        }
        &.active {
            color: $left-nav-panel-section-menu-item-selected-color;
            background-color: $left-nav-panel-section-menu-item-selected-bkgrd-color;
            border-radius: 5px;
        }
        &.no-indent {
            padding: 0 10px 0 15px;
        }
        &.first-no-header {
            margin-top: 20px;
        }

        .nav-menu-link {
            color: $left-nav-panel-section-menu-item-color;
            .fa-external-link {
                font-size: 12px;
                line-height: 12px;
                padding-left: 4px;
            }
        }
        .nav-menu-link:hover {
            color: $left-nav-panel-section-menu-item-hover-color;
        }
    }

    .nav-menu-list-item-content {
        color: $left-nav-panel-section-menu-item-color;
        display: block;
        font-size: 13px;
        text-align: left;
        width: 100%;
        padding: 8px 12px;

        &:last-child {
            margin-bottom: 12px;
        }

        .fas {
            font-size: 13px !important;
        }
        &.hidden {
            display: none;
        }
    }

    .-js-nav-menu-all-error-list {
        padding-top: 10px;
        .fa-exclamation-triangle {
            color: #FD4239;
        }
        .nav-menu-list-item-error {
            color: var(--semantic-color-surface-base-primary);
        }
        .-js-CASB-error-details {
            color: #939393;
            padding-left: 22px;
        }
        .reauth-req {
            color: var(--semantic-color-surface-base-primary);
            font-size: 12px;
            font-weight: 700;
        }
    }

    .nav-menu-list-item-error {
        color: #FD4239;
    }

    .nav-menu-super-label {
        background: $slider-color-high !important;
        border: 1px solid var(--semantic-color-surface-base-primary) !important;
        border-radius: 12px;
        color: var(--semantic-color-surface-base-primary) !important;
        font-size: 13px;
        height: 20px;
        line-height: 12px;
        min-width: 20px;
        padding-top: 4px;
        position: absolute;
        left: 47px;
        text-align: center;
        top: 13px;
        z-index: 10;
    }

    .nav-menu-link {
        text-decoration: none;
        color: $left-nav-panel-section-menu-item-color;
        display: inline-block;
        width: 100%;
        @include no-wrapping;

        &:hover {
            text-decoration: none;
            color: $left-nav-panel-section-menu-item-hover-color;
        }
    }

    /*Top Menu*/
    .page-header-right-menu {
        display: inline-block;
        float: right;
        height: 100%;
        position: relative;
        // Level 2
        color: var(--semantic-color-surface-base-primary);
        font-size: 12px;
    }

    /*Support Menu*/
    .help-dialog-mask {
        z-index: 9999;

        &.submit-ticket-dialog {
            z-index: 999999;
        }
    }

    /* Down Nav Menu */
    .down-nav-menu {
        display: block;
        font-size: 16px;
        height: 36px;
        width: 100%;
        vertical-align: top;
        cursor: pointer;
        border-left: 4px solid $left-nav-bkgrd-color;
        border-right: 4px solid $left-nav-bkgrd-color;

        &:hover, &.active {
            border-left: 4px solid $left-nav-menu-item-hover-border-color;
            border-right: 4px solid $left-nav-menu-item-bkgrd-hover-color;
            color: $left-nav-menu-item-icon-hover-selected-color;
            background: $left-nav-menu-item-bkgrd-hover-color;

            .down-nav-menu-header {
                color: $left-nav-menu-item-icon-hover-selected-color;
            }
        }
    }

    .down-nav-menu-header {
        color: $left-nav-menu-item-icon-color;
        display: block;
        padding: 10px 0;
        text-align: center;
        font-size: 0px;
        height: 100%;
        white-space: nowrap;
        width: 100%;

        span.down-nav-menu-header-label {
            display: block;
            font-size: 11px;
            left: 8px;
            line-height: 22px;
            position: absolute;

            &.activation-label {
                left: 5px;
            }

            &.help-label {
                left: 21px;
            }

            &.profile-label {
                left: 6px;
            }
        }

        &.poc-header {
            color: #00bce4;
            font-size: 12px;
            line-height: 36px;
            i.fas.fa-bell {
                text-shadow: 0 0 5px #00bce4;
            }

            &.poc-steps-completed {
                color: $left-nav-panel-section-sub-header-color;
                i.fas.fa-bell {
                    text-shadow: none;
                }
                &.active {
                    color: var(--semantic-color-surface-base-primary);
                }
            }
        }
    }

    .down-nav-menu-panel {
        @extend .nav-menu-panel;
        width: 270px;
        cursor: default;
        padding: 24px 8px 0 0;

        .nav-menu-section {
            width: 100%;
        }
    }

    .user-menu-icon {
        font-size: 12px;
        margin-right: 5px;
    }

    .user-menu-icon-error {
        position: relative;
        display: inline-block;
        width: 6px;
        height: 6px;
        top: -8px;
        left: 2px;
        background: #FD4239;
        border-radius: 50%;
        margin-left: -6px;

        &.hidden {
            display: none;
        }
    }

    // Activate Menu
    .org-edit-nav-menu {
        @extend .nav-menu;
    }

    .org-edit-nav-menu-header-label {
        @extend .nav-menu-header-label;
        display: block;
        padding-bottom: 13px;
        padding-top: 0px;
    }

    .org-edit-nav-menu-icon {
        font-size: 16px;
    }

    .org-edit-nav-menu-header {
        @extend .nav-menu-header;
        position: relative;
        padding-top: 16px;

        &:hover, &.active, &.selected{
            .upload-border {
                border: 2px solid $left-nav-menu-item-icon-color;
            }
        }

        .upload-border {
            border: 2px solid $left-nav-menu-item-icon-color;
            border-radius: 29px;
            padding: 4px;
            font-size: 14px !important;
        }
    }

    .org-edit-item {
        margin-bottom: 16px;

        .org-active-button{
            width: 202px;
            height: 32px;
            padding: 7px 16px;
            line-height: 16px;
        }
    }

    .org-edit-label {
        color: $left-nav-panel-section-sub-header-color;
        display: block;
        font-size: 11px;
        line-height: 16px;
        padding-bottom: 4px;
        text-transform: uppercase;
        width: 100%;
        @include no-wrapping;
    }

    .org-edit-nav-menu-panel {
        @extend .nav-menu-panel;
        cursor: default;
        padding: 24px 20px;
    }

    .org-edit-value {
        color: $left-nav-panel-section-menu-item-color;
        font-size: 13px;
        line-height: 16px;
        display: block;
        padding: 8px 0px;
        @include no-wrapping;

        &.edit {
            color: $slider-color-high;
        }

        &.queue {
            color: #009cff;
        }
    }

    .org-edit-label-count {
        &:before {
            content: " (";
        }

        &:after {
            content: ")";
        }
    }

    .org-edit-force-activate {
        @extend .org-edit-value;
        padding-top: 0px;
        padding-bottom: 16px;

        [type="checkbox"] {
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;

            &:disabled {
                cursor: default !important;
            }
        }

        &.hidden {
            display: none;
        }
    }

    /* Accordion Menu */

    .accordion-nav-menu-panel-item-header {
        @extend .nav-menu-panel-item-header;
        background: none;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        margin: 0px;
        padding: 16px;
        width: 100%;
        height: auto;

        i.fas {
            font-size: 24px;

            &.fa-tablet {
                font-size: 28px;
            }
        }

        i.fa-angle {
            display: inline-block;
            font-size: 14px;
            margin-right: 8px;
            position: relative;
            top: -4px;
        }

        span {
            margin-left: 12px;
            position: relative;
            top: 3px;
        }

        .nav-menu-panel-item-icon {
            color: inherit;
        }
    }

    .accordion-nav-menu-list {
        @include nav-list-reset;
        overflow: hidden;
        transition: all .2s ease-in;

        h3.nav-menu-section-header {
            margin-bottom: 7px;
            color: $left-nav-panel-section-sub-header-color;
        }

        ul.nav-menu-list {
            margin-bottom: 20px;

            li {
                padding: 5px 24px;

                &.nav-menu-list-item {
                    &.active:hover {
                        color: $left-nav-panel-section-menu-item-hover-color;
                    }

                    &:hover{
                        color: $left-nav-panel-section-menu-item-selected-color;
                    }
                }

                .nav-menu-list-item-text {
                    width: calc(100% - 14px);
                }
            }
        }
    }

    /*weblog time*/
    .weblog-label {
        color: #008bc9;
    }

    .weblog-time-label:after {
        content: ": ";
    }

    /*copyright*/
    .copyright-statement:before {
        content: " ";
    }

    /* Empty Report */

    .view-empty-badge {
        display: inline-block;
        vertical-align: middle;
    }

    .view-empty-badge-icon {
        margin-bottom: 2px;
        font-size: 46px !important;
        color: $default-text-color;
    }

    .view-empty-badge-label {
        color: $default-text-color;
        font-size: 14px;
        font-weight: 300;
    }

    .view-empty-text {
        color: $default-text-color;
        font-size: 13px;
        margin-top: 5px;
        max-width: 200px;
    }

    .view-empty {
        height: 100%;
        text-align: center;
        width: 100%;
        border: 1px solid $default-border-color;

        &.insights-empty-color {
            border: 0px;
        }
    }

    .view-empty:before {
        content: "";
        display: inline-block;
        height: 100%;
        vertical-align: middle;
    }

    .page-title.sub {
        font-size: 14px;
        position: relative;
    }

    // Report Layout
    .report-layout {
        height: 100%;
        position: relative;
        padding: 24px;
        overflow-y: auto;

        &.system-audit-report {
            .report-layout-body {
                height: auto;
            }
        }

        .report-layout-actions {
            line-height: 20px;
            font-size: 13px;
            color: #009CDA;
            font-weight: 400;
            cursor: pointer;
            float: right;

            .report-layout-action {
                margin-right: 15px;

                &:last-child {
                    margin-right: 0px;
                }
                i {
                    margin-right: 5px;
                }
            }
        }
    }

    .report-layout-header {
        padding-bottom: 24px;
        white-space: nowrap;
    }

    .report-layout-body {
        height: calc(100% - 48px);
    }

    .report-layout-title {
        color: $header-title-color;
        font-size: 24px;
        @include inline-block(middle);
    }

    .beta-feature {
        height: 20px;
        color: var(--semantic-color-surface-base-primary);
        font-size: 11px;
        text-transform: uppercase;
        line-height: 20px;
        position: absolute;
        transform: rotate(-45deg);
        left: -16px;
        top: 4px;
        width: 60px;
        text-align: center;
        background: $beta-feature-background-color;
    }

    .ace_scrollbar-v {
        cursor: default !important;
    }

    .font16 {
        font-size: 16px;
    }

    //Search Panel
    .search-nav-menu-panel {
        @extend .down-nav-menu-panel;

        .search-header {
            border-bottom: 2px solid $left-nav-panel-section-separator-color;
            padding-bottom: 16px;
            margin: 0px 16px;
            width: calc(100% - 32px);
            height: 36px;

            .fa-search {
                font-size: 16px;
                color: $left-nav-panel-section-menu-item-color;
            }
            .search-input {
                background: $left-nav-menu-item-bkgrd-hover-color;
                border: none;
                box-sizing: border-box;
                box-shadow: none;
                color: $left-nav-panel-section-menu-item-color;
                font-size: 13px;
                line-height: 16px;
                margin-left: 8px;
                width: calc(100% - 48px);

            }
            .search-header-input-clear-icon {
                color: $left-nav-panel-section-menu-item-color;
                cursor: pointer;
                font-size: 16px;
                margin-left: 8px;
            }
        }

        .search-result-panel {
            background: var(--semantic-color-surface-base-primary);
            border-bottom: 12px solid $left-nav-panel-section-separator-color;
            border-radius: 5px;
            -webkit-box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
            -moz-box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
            box-shadow: 4px 0px 4px 0px rgba(42,44,48,0.5);
            display: block;
            list-style: none;
            overflow-y: auto;
            margin: 0px;
            max-height: calc(100% - 84px);
            position: absolute;
            padding-top: 6px;
            text-align: left;
            top: 52px;
            width: 100%;
            -webkit-transition: all .2s ease-in-out;
            transition: all .2s ease-in-out;
            z-index: 9998;


            .search-menu-list-item {
                @include no-wrapping;
                color: $dropdown-text-color;
                cursor: pointer;
                display: block;
                font-size: 13px;
                height: auto;
                line-height: 16px;
                padding: 6px 16px 6px 16px;
                text-align: left;
                width: 100%;

                .search-icon {
                    color: $dropdown-text-color;
                    display: inline-block;
                    font-size: 12px !important;
                    line-height: 12px;
                    margin: 0px;
                    margin-right: 8px;
                    padding-top: 3px;
                    vertical-align: top;
                }

                .search-options-text {
                    display: inline-block;
                    white-space: normal;
                    width: calc(100% - 24px);
                    word-wrap: break-word;
                }

                &:hover {
                    background: $dropdown-list-item-bkgrd-hover-color;
                }
                .seperator {
                    color: $text-input-disabled-color;
                    font-size: 11px;
                    line-height: 11px;
                    margin: 0px 4px;
                }

            }
            .no-search-results {
                @include no-wrapping;
                color: $text-input-disabled-color;
                display: block;
                font-size: 13px;
                font-style: italic;
                height: auto;
                line-height: 16px;
                padding-top: 12px;
                text-align: center;
                width: 100%;
            }
        }

        .search-recent-items-panel {
            padding-top: 10px;
            height: calc(100% - 36px);
            overflow-y: auto;

            .search-menu-list-item {
                @include no-wrapping;
                color: $anchor-color;
                cursor: pointer;
                display: block;
                font-size: 13px;
                height: auto;
                line-height: 20px;
                padding: 10px 16px 10px 16px;
                text-align: left;
                width: 100%;

                &:hover {
                    color: $anchor-hover-color;
                }

                .seperator {
                    color: $text-input-disabled-color;
                    font-size: 11px;
                    line-height: 11px;
                    margin: 0px 4px;
                }
            }
            .clock-icon {
                color: $dropdown-text-color;
                display: inline-block;
                font-size: 14px !important;
                line-height: 14px;
                margin: 0px;
                margin-right: 8px;
                padding-top: 3px;
                vertical-align: top;
            }

            .search-options-text {
                display: inline-block;
                white-space: normal;
                width: calc(100% - 24px);
                word-wrap: break-word;

            }

            .search-input-panel {
                padding: 0px;
            }

            .search-history-panel {
                padding: 0px;
            }

            .search-input-list-item {
                @extend .search-menu-list-item;

                &:last-child {
                    border-bottom: 1px solid $dropdown-text-color;
                }

                .search-options-text {
                    width: calc(100% - 22px);
                }
            }
        }
    }

    .new-label {
        @include inline-block(middle);
        background: transparent;
        color: $anchor-color;
        font-size: 11px;
        margin-left: 8px;
        text-transform: uppercase;
    }

    .theme-selection-container {
        border-top: 1px solid $left-nav-panel-section-separator-color;
        padding: 24px 8px;

        .nav-menu-section-header {
            padding-bottom: 8px;
        }

        .themes {
            padding: 12px 16px;
            color: $left-nav-menu-item-icon-color;
            cursor: pointer;

            &:hover {

                &.default, &.blue, &.dark-blue {
                    .theme-preview-container {
                        border: 2px solid #99D8FF;
                    }
                }            
            }

            &.active {
                .theme-preview-container {
                    border: 2px solid #99D8FF!important;
                }
            }

            .theme-text-container {
                padding-bottom: 8px;

                .theme-radio-button {
                    @include inline-block(middle);
                }

                .theme-text {
                    @include inline-block(middle);
                    padding-left: 8px;
                    font-weight: 500;
                    font-size: 13px;
                }
            }

            .theme-preview-container {
                height: 42px;
                width: 180px;
                border-radius: 5px;

                .left-nav {
                    width: 16px;
                    height: 100%;
                    @include inline-block(middle);
                    border-radius: 3px 0 0 3px;
                }

                .page-content-container {
                    @include inline-block(middle);
                    border-radius: 0 3px 3px 0;
                    padding: 8px;
                    width: calc(100% - 16px);
                    height: 100%;

                    .page-widgets {
                        @include inline-block(middle);
                        width: 42px;
                        height: 24px;
                        margin-right: 8px;
                        border-radius: 5px;
                        // box-shadow: 0 0 12px 0 rgba(42,44,48,0.15);

                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }

            &.default {
                .theme-preview-container {
                    border: 2px solid var(--semantic-color-content-base-secondary);
                }

                .left-nav {
                    background: var(--semantic-color-content-base-secondary);
                }            

                .page-content-container {
                    background: #F5F5F6;

                    .page-widgets {
                        background: var(--semantic-color-background-primary);
                    }
                }
            }
            
            &.blue {
                .theme-preview-container {
                    border: 2px solid var(--semantic-color-content-interactive-primary-default);
                }

                .left-nav {
                    background: var(--semantic-color-content-interactive-primary-default);
                }            

                .page-content-container {
                    background: #F7F9FA;

                    .page-widgets {
                        background: var(--semantic-color-background-primary);
                    }
                }
            }

            &.dark-blue {
                .theme-preview-container {
                    border: 2px solid #1f253d;
                }

                .left-nav {
                    background: #1f253d;
                }            

                .page-content-container {
                    background: #0A0D1A;

                    .page-widgets {
                        background: #1f253d;
                    }
                }
            }
        }
    }



    .controls-container {  
        // width: 100%;
        margin-bottom: 0.425rem;
        display: flex;
        justify-content: space-between;

        .controls-left {
            float: none;
        }
        .controls-right {
            display: flex;
        }

        &.audit-logs-controls-container {
            padding: 0 0 16px 0;
        }

        &.azure-wan-controls-container {
            font-size: 13px
        }

        &.location-group {
            .grid-toolbar-right {
                .filter-value-container {
                    width: 220px;

                    .dropdown {
                        width: 220px;
                    }
                }
            }
        }

        .selected-location-count {
            display: block;
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-weight: 500;
            letter-spacing: 0.4px;
            text-transform: uppercase;
            line-height: 28px;
        }
    }

    .grid-container {
        bottom: 0;
        position: absolute;
        top: 0;
        right:0;
        left: 0;
    }

    .grid-wrapper {
        height: 100%;
        width: 100%;
    }

    // .form-sections-container {
    //     padding: 19px 25px;
    //     background: #f1f2f3;
    //     border: 1px solid #e0e1e3;
    //     padding: 16px 24px;
    //     width: 100%;
    //     max-height: 630px;
    //     overflow: auto;
    //     .form-section-label {
    //       padding: 0;
    //       overflow: hidden;
    //       text-overflow: ellipsis;
    //       white-space: nowrap;
    //       color: #656666;
    //       font-size: 13px;
    //       letter-spacing: 0.5px;
    //       margin-bottom: 8px;
    //       text-transform: uppercase;
    //       width: 100%;
    //     }
    //     .form-section {
    //       flex-direction: column;
    //       background: var(--semantic-color-background-primary);
    //       box-shadow: 0 0 8px 0 rgba(42, 44, 48, 0.25);
    //       border-radius: 5px;
    //       width: 100%;
    //       margin-bottom: 24px;
    //     }
    //   }

    .form-tab-container { padding: 3px 2px; }
    .hide {  display: none; }
    .unhide { display: block; }

    .form-textarea{
        border-radius: 0.3125rem;
        background-color: var(--semantic-color-surface-base-primary);
        color: var(--semantic-color-content-base-primary);
        border-color: var(--semantic-color-border-base-primary);
        margin: 0;
        padding: 0.4375rem;
        box-sizing: border-box;
        height: 90px;
        width:  625px;
        border: 1px solid var(--semantic-color-border-base-primary);
        resize: none;
        &:focus-visible {
            outline-offset: 0px;
            outline: var(--semantic-color-border-base-primary) auto 1px;
        }
    }
    .form-textarea.error {   
        border-color: var(--semantic-color-border-status-danger-active);
    }

    .add-custom-app-form {
        .container {
            padding: 10px 15px;

            .container-fields {
                flex-direction: column;
                padding: 0.5em;
                overflow-y: scroll;
                .custom-input{
                    color: var(--semantic-color-content-base-primary);
                    margin: 0.5em;
                    font-weight: 500;
                    text-transform: capitalize;
                } 
                
            }
            .input-container {
                flex-direction: column;
                padding: 0.5em;
            }
            p.title {
                padding-bottom: 10px;
                color: $grey4;
                font-size: 13px;
                text-transform: uppercase;
            }
            
            .radio-button-container{
                padding: 0 5px;
            }
        
            &-fields {
                @include DisplayFlex;
                background: $white;
                border-radius: 5px;
                background-color: $white;
                // box-shadow: 0 0 12px 0 $grey14;

                >div {
                    // flex: 1 1 1px;
                    flex: 0.5;
                }
            }
        }

        .form-footer {
            height: 60px;
            border-radius: 5px 5px 0 0;
            border-top: 1px solid var(--semantic-color-border-base-primary);
            background-color: var(--semantic-color-background-primary) !important;
            padding-left: 10px;
            line-height: 60px;

            .cancel {
                margin-left: 10px;
            }
        }

    .form-sections-container {
        max-height: 70vh;
        overflow: auto;
        padding-bottom:  22px;

        .form-section {
        @include DisplayFlex;
        border: none;
        border-radius: 5px;
        background-color: var(--semantic-color-surface-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        margin: 0  24px;
        padding: 16px;
        align-items: flex-start;
        flex-wrap: wrap;
        justify-content: space-between;

        .input-container,
        .radio-button-container,
        .select-item,
        .dropdown-container,
        .disabled-input {
            padding: 0;
            margin: 0;
            width: 260px
        }
        .form-left{
            float: left;
        }
        .form-right{
            float: right;
            padding-left: 5.3em;
        }
        .radio-buttons {
            white-space: nowrap;
        }
        .no-wrap{
            white-space: nowrap;
        }
        .radio-child {
            margin-right: 6em;
            width: 260px;
            margin-top: 30px;
        }
        
        .dropdown-container-child-row {
            padding: 0;
            width: 260px;
            margin-top: 90px;
        }
        // .form-top-margin {
        //   margin-top: 32px;
        // }

        .full-width {
            width:  585px;

            .select-item {
            width: 100%;
            }
        }

        .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            cursor: not-allowed;
            margin-top: 4px;
        }
        
        .data-error {
            div {
            width: 100%;
            }
            .text {
            text-align: left;
            color: $red2;
            }
        }
        }
    }
    
    .modal-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
    }
    
    &.configure-monitor {
        .input-container:first-of-type .input-label,
        .radio-button-container p {
        margin-top: 0;
        }
    }
    }
    .policy-table-wrapper {
        height: 100%;
    }

    .policy-table-layout {
        height: 100%;
        width: 100%;
    }

    .policy-table-container {
        width: 100%;
        height: calc(100% - 41px);
        position: relative;
    }

    .policy-table {
        font-size: 13px;
        height: 100%;
        width: 100%;
        border-radius: 5px;
    }

    .policy-table-header-viewport {
        border: 1px solid $table-body-border-color;
        background: $table-header-row-bkgrd-color;
        border-radius: 5px 5px 0 0;
        width: 100%;
    }

    .policy-table-header {
        color: $table-header-cell-text-color;
        font-weight: 500;
        width: calc(100% - 15px);

        .policy-table-type-cell {
            @extend .policy-table-cell;
            border-right: none;
        }
    }

    .policy-table-header-cell {
        display: inline-block;
        vertical-align: middle;
        padding: 0;
        border: 0;

        .table-header-cell-container {
            width: 100%;
            height: 100%;
            border-right: 1px solid $table-header-cell-border-color;
            padding: 12px 8px;

            .table-text {
                display: inline-block;
                vertical-align: top;
                width: calc(100% - 20px);
            }

            &:hover {
                .sort-table {
                    display: inline-block;
                }
            }
        }

        &.ecui-sortable-handle {
            cursor: move;
        }

        &.ui-sortable-placeholder {
            position: relative;
            font-size: 16px;
            overflow: visible;
            color: #939393;
            border-right: 1px solid $table-header-cell-border-color;

            .placeholder-icon {
                position: absolute;
                top: -18px;
                left: -4px;
            }
        }

        &.ui-sortable-helper {
            border-left: 1px solid $table-body-border-color;
            border-right: 1px solid $table-body-border-color;
            text-shadow: 0 0 7px rgba(0,0,0,0.5);
            box-shadow: 0 0 7px rgba(0,0,0,0.5);
        }

        &.hidden {
            display: none;
        }
    }

    .policy-table-body-viewport {
        border: 1px solid $table-body-border-color;
        border-top: 0px;
        border-radius: 0 0 5px 5px;
        height: calc(100% - 46px);
        width: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
    }

    .policy-table-body{
        width: 100%;
    }

    .policy-table-type-row {
        background-color: $table-body-row-expanded-bkgrd-color;
        color: $table-header-cell-text-color;
        text-transform: uppercase;
        font-size: 11px;
        font-weight: 500;
        position: relative;
        z-index: 1;
        box-shadow: 0px 3px 12px $table-body-row-expanded-shadow-color;
        -webkit-print-color-adjust:exact;
        -webkit-filter:opacity(1);
    }

    .policy-table-row {
        background-color: $table-body-row-bkgrd-color;
        color: $table-header-cell-text-color;
        position: relative;
        white-space: nowrap;
        
        &.even {
            background-color: $table-body-row-alt-bkgrd-color;
        }
    }

    .policy-table-row-menu-container {
        @extend .policy-table-cell;
    }

    .policy-table-row-menu {
        color: $anchor-color;
    }

    .policy-table-row-menu-button {
        cursor: pointer;
        text-align: center;
        width: 40px;

        &:hover{
            color: $anchor-hover-color;
        }
    }

    .policy-table-cell {
        border-right: 1px solid $table-header-cell-alt-border-color;
        display: inline-block;
        padding: 12px 8px;
        vertical-align: top;
        @include no-wrapping;

        &:last-child{
            border-right: 0!important;
        }

        &.hidden {
            display: none;
        }
    }

    .policy-table-type-cell {
        @extend .policy-table-cell;
        border-right: none;
    }

    .policy-table-cell-text {
        display: inline-block;
        width:100%;
        @include no-wrapping;

        &.highlight{
            background-color: #FFDB80 !important;
            padding: 4px 8px;
            border-radius: 5px;
            color: var(--semantic-color-content-base-primary);

            &.tooltip-cue {
                color: $anchor-color;
            }
        }
    }

    .policy-table-criteria-item {
        color: $table-header-cell-text-color;
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .policy-table-criteria-item-label,
    .policy-table-action-label {
        color: #747272;
        &:after {
            content: ": ";
        }
    }

    .policy-table-name-label {
        color: #747272;
        display: block;
    }

    .policy-table-name-content {
        color: $table-header-cell-text-color;
        margin-left: 4px;
    }

    .policy-table-criteria-item-label {
        color: $table-header-cell-text-alt-color;
        font-size: 11px;
        line-height: 11px;
        letter-spacing: 1px;
        margin-bottom: 0px;
        text-transform : uppercase;
        @include no-wrapping;
    }


    .policy-table-criteria-item-data-item {
        word-break: break-word;

        &:after {
            content: "; ";
        }
        &:last-child:after {
            content: "";
        }
    }

    .policy-table-action-allow {
        color: var(--semantic-color-content-status-success-primary);
        margin-bottom: 8px;
    }

    .policy-table-action-block {
        color: var(--semantic-color-content-status-danger-primary);
        margin-bottom: 8px;
    }

    .policy-table-action-caution {
        color: $slider-color-moderate;
        margin-bottom: 8px;
    }

    .policy-table-action-disabled {
        color: var(--semantic-color-content-interactive-primary-disabled);
        margin-bottom: 8px;
    }

    .policy-table-action-label:after {
        content: ": ";
    }

    .policy-table-cell-data {
        display: block;
        @include no-wrapping;
    }

    .api-disabled {
        color: $grey4;
    }

    .text-align-center {
        text-align: center; 
    }

    @keyframes spinning {
        from { transform: rotate(0deg) }
        to { transform: rotate(360deg) }
    }
    .spin {
        animation-name: spinning;
        animation-duration: 3s;
        animation-iteration-count: infinite;
        /* linear | ease | ease-in | ease-out | ease-in-out */
        animation-timing-function: linear;
    }

    .inline-block {
        display: inline-block;
    }
    .margin-right-4px {
        margin-right: 4px !important;
    }
}