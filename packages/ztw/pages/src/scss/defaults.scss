@import './colors.scss';
@import 'scss/mixins.scss';
@import 'scss/fonts.scss';
@import "@zs-nimbus/foundations/css";
// @import "@zs-nimbus/dataviz-colors/styles.css";

.ec-root-page {
  @import "@zs-nimbus/foundations/css";
}

// html {
//   // font-size: 62.5%; /* 1 rem = 10 px */
// }

// html, body, #root, main, main > div {
//   // height: 100%;
// }

body {
  height: 100%;
  font-size: 13px; // default font-size
  padding: 0;
  margin: 0;
  background: var(--semantic-color-background-primary);
  counter-reset: chapter;
  
  display: flex;
  flex-direction: column;

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  button {
    cursor: pointer;

    &.blue-button {
      color: $white;
      border: none;
      border-radius: 4px;
      background-color: var(--semantic-color-content-interactive-primary-default);
      font-size: 13px;
      height: 32px;
      margin-top: 32px;
      padding: 6px 20px;
    }

    &.submit {
      color: var(--semantic-color-content-immutable-white);
      border: none;
      padding: 6px 15px;
      font-size: 16px;
      border-radius: 5px;
      background-color: var(--semantic-color-content-interactive-primary-default);
      // box-shadow: 0 2px 10px 0 $blue10;
    }
  
    &.cancel {
      color: var(--semantic-color-content-interactive-primary-default);
      border: none;
      font-size: 16px;
      padding: 6px 15px;
      border-radius: 5px;
      background: var(--semantic-color-surface-base-primary);
    }
  
    &.disabled, &:disabled {
      box-sizing: border-box;
      border: none;
      border-radius: 5px;      
      background-color: var(--semantic-color-border-interactive-primary-disabled);
      box-shadow: none;
      color: var(--semantic-color-content-interactive-primary-disabled);
      cursor: not-allowed;
    }
  }
  .disabled-section {
    pointer-events: none;
    opacity: 0.4;
  }
  .uppercase {
    text-transform: uppercase;
  }
  
  .no-margin-top {
    margin-top: 0 !important;
  }
  
}

.separator-line {
  height: 1px;
  background-color: var(--semantic-color-border-base-primary);
  margin: 20px 0;
}

.strike-through {
  text-decoration: line-through;
}

.header-1 {
  color: var(--semantic-color-content-base-primary);
}
.header-2 {
  color: var(--semantic-color-content-base-primary);
}
.header-3 {
  color: var(--semantic-color-content-base-primary);
}
.header-4 {
  color: var(--semantic-color-content-base-primary);
}
.header-5 {
  color: var(--semantic-color-content-base-primary);
}