// Import core styles first (outside of scoped containers to avoid conflicts)
@import '../scss/variables.scss';
@import '../scss/mixins.scss';
@import '../scss/colors.scss';

// Container scoping for library mode to prevent CSS conflicts
#edge-ux-library-container,
#edge-ux-library-portal {

  // Ensure proper scoping and CSS custom properties
  color: var(--semantic-color-content-base-primary, #333333);
  background-color: var(--semantic-color-background-primary, #ffffff);
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);

  // Reset any global styles that might conflict with consumer app
  * {
    box-sizing: border-box;
  }

  // Override potentially conflicting global styles
  body, html {
    margin: unset !important;
    padding: unset !important;
    font-family: unset !important;
    background: unset !important;
  }

  // Ensure all component styles are scoped
  .ec-root-page {
    // Scoped app styles
    position: relative;
    width: 100%;
    height: 100%;
  }

  .dashboard {
    // Scoped dashboard styles
  }

  .administration {
    // Scoped administration styles
  }

  .analytics {
    // Scoped analytics styles
  }

  .policy {
    // Scoped policy styles
  }

  .profile {
    // Scoped profile styles
  }

  .help {
    // Scoped help styles
  }

  .login {
    // Scoped login styles
  }

  // Define CSS custom properties for theming
  :root {
    --edge-ux-primary-color: var(--semantic-color-content-interactive-primary-default, #007bff);
    --edge-ux-secondary-color: var(--semantic-color-content-base-secondary, #6c757d);
    --edge-ux-success-color: var(--semantic-color-content-status-success, #28a745);
    --edge-ux-danger-color: var(--semantic-color-content-status-danger, #dc3545);
    --edge-ux-warning-color: var(--semantic-color-content-status-warning, #ffc107);
    --edge-ux-info-color: var(--semantic-color-content-status-info, #17a2b8);
    --edge-ux-light-color: var(--semantic-color-surface-base-secondary, #f8f9fa);
    --edge-ux-dark-color: var(--semantic-color-content-base-primary, #343a40);

    --edge-ux-font-size-sm: 0.875rem;
    --edge-ux-font-size-base: 1rem;
    --edge-ux-font-size-lg: 1.25rem;
    --edge-ux-font-size-xl: 1.5rem;

    --edge-ux-border-radius: 0.375rem;
    --edge-ux-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

    --edge-ux-spacing-xs: 0.25rem;
    --edge-ux-spacing-sm: 0.5rem;
    --edge-ux-spacing-md: 1rem;
    --edge-ux-spacing-lg: 1.5rem;
    --edge-ux-spacing-xl: 3rem;
  }

  // Ensure proper z-index stacking for library components
  .modal {
    z-index: 10000;
  }

  .dropdown-menu {
    z-index: 9999;
  }

  .tooltip {
    z-index: 9998;
  }

  .notification {
    z-index: 9997;
  }

  // Prevent library styles from affecting parent application
  .edge-ux-isolated {
    all: initial;
    font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif);
  }
}
