import React from 'react';

function Banner() {
  const getCloudName = () => {
    const { hostname } = window.location;

    if (hostname.indexOf('connectorfedcloudnet') !== -1) {
      return 'connectorfedcloudnet';
    }

    return 'connectorcloudnet';
  };

  const openHelpArticle = (article) => {
    window.open(article, '_blank');
  };

  const bannerHeroImgAlt = 'bannerHeroImgAlt';
  const cloudName = getCloudName();
  const imgURL = `https://www.zscaler.com/api/admin-ui-pre-login/${cloudName}/`;

  return (
    <div id="login-banner-container" className="login-banner-container">
      <div
        className="login-banner login-banner-big"
        onClick={() => openHelpArticle(`${imgURL}1.html`)}
        onKeyPress={() => openHelpArticle(`${imgURL}1.html`)}
        role="button"
        tabIndex={0}>
        <img alt={bannerHeroImgAlt} src={`${imgURL}1.jpg`} />
      </div>
      <div className="login-banner-small-container">
        <div
          className="login-banner login-banner-small"
          onClick={() => openHelpArticle(`${imgURL}2.html`)}
          onKeyPress={() => openHelpArticle(`${imgURL}2.html`)}
          role="button"
          tabIndex={-1}>
          <img alt={bannerHeroImgAlt} src={`${imgURL}2.jpg`} />
        </div>
        <div
          className="login-banner login-banner-small"
          onClick={() => openHelpArticle(`${imgURL}3.html`)}
          onKeyPress={() => openHelpArticle(`${imgURL}3.html`)}
          role="button"
          tabIndex={-2}>
          <img alt={bannerHeroImgAlt} src={`${imgURL}3.jpg`} />
        </div>
        <div
          className="login-banner login-banner-small"
          onClick={() => openHelpArticle(`${imgURL}4.html`)}
          onKeyPress={() => openHelpArticle(`${imgURL}4.html`)}
          role="button"
          tabIndex={-3}>
          <img alt={bannerHeroImgAlt} src={`${imgURL}4.jpg`} />
        </div>
        <div
          className="login-banner login-banner-small"
          onClick={() => openHelpArticle(`${imgURL}5.html`)}
          onKeyPress={() => openHelpArticle(`${imgURL}5.html`)}
          role="button"
          tabIndex={-4}>
          <img alt={bannerHeroImgAlt} src={`${imgURL}5.jpg`} />
        </div>
      </div>
    </div>
  );
}

export default Banner;
