import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faEyeSlash } from '@fortawesome/pro-solid-svg-icons';

export function InputContainer(props) {
  const {
    id,
    inputLabel,
    inputType,
    placeholder,
    updateTextbox,
    inputVal,
    name,
    hasObfuscation,
  } = props;

  const [isSecretObfuscated, setIsSecretObfuscated] = useState(true);

  const handleShowValue = () => setIsSecretObfuscated(false);
  const handleHideValue = () => setIsSecretObfuscated(true);

  return (
    <div className="input-container">
      <span
        id={`${id}-label-${inputType}`}
        className="login-text-label">
        {inputLabel}
      </span>
      <div className="login-text-input">
        <input
          id={`${id}-input-${inputType}`}
          className="login-text-input-text"
          type={(hasObfuscation && !isSecretObfuscated) ? 'text' : inputType}
          placeholder={placeholder}
          value={inputVal}
          onChange={updateTextbox(name)} />

        { hasObfuscation && isSecretObfuscated && (
          <span className="">
            <FontAwesomeIcon
              icon={faEye}
              className="obsfuscation-icon "
              onClick={handleShowValue} />
          </span>
        )}
        { hasObfuscation && !isSecretObfuscated && (
          <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-hide">
            <FontAwesomeIcon
              icon={faEyeSlash}
              className="obsfuscation-icon "
              onClick={handleHideValue} />
          </span>
        )}
      </div>
    </div>
  );
}

InputContainer.propTypes = {
  id: PropTypes.string,
  inputLabel: PropTypes.string,
  inputType: PropTypes.string,
  placeholder: PropTypes.string,
  updateTextbox: PropTypes.func,
  inputVal: PropTypes.string,
  name: PropTypes.string,
  hasObfuscation: PropTypes.bool,
};
    
InputContainer.defaultProps = {
  id: 'login-panel',
  inputLabel: null,
  inputType: 'text',
  placeholder: null,
  updateTextbox: null,
  inputVal: '',
  name: '',
  hasObfuscation: false,
};

export default InputContainer;
