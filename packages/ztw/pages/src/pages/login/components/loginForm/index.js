import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { noop } from 'utils/lodash';
import FormErrorSummary from 'components/formErrorSummary';
import { ErrorLabel } from 'components/label';
import DropDown from 'components/dropDown';
import Spinner from 'components/spinner';
import withRouter from 'layout/withRouter';
import { BASE_LAYOUT } from 'config';
import { getDefaultLandingPages } from 'utils/helpers';

import * as constants from 'ducks/login/constants';
import * as loginSelectors from 'ducks/login/selectors';

import { faGlobe } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  checkSession,
  login,
  setLocale,
  setRememberUser,
} from 'ducks/login';
import { togglePasswordExpiryModal } from 'ducks/passwordExpiry';

import { withTranslation } from 'react-i18next';

import InputContainer from './InputContainer';
import './index.scss';
import 'scss/defaults.scss';

class LoginForm extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      checkSession: PropTypes.func,
      login: PropTypes.func,
      setPageLocale: PropTypes.func,
      setRememberUser: PropTypes.func,
    }),
    rememberUser: PropTypes.bool,
    data: PropTypes.shape(),
    error: PropTypes.string,
    t: PropTypes.func,
    locale: PropTypes.shape(),
    router: PropTypes.shape({}),
    accessPrivileges: PropTypes.shape({}),
  };

  static defaultProps = {
    actions: {
      checkSession: noop,
      login: noop,
      setPageLocale: noop,
      setRememberUser: noop,
    },
    rememberUser: null,
    data: {},
    error: '',
    t: (str) => str,
    locale: null,
    // redirect: noop,
  };

  state = {
    username: '',
    password: '',
    formErrorSummaryText: null,
    usernameErrorText: null,
    passwordErrorText: null,
    loading: false,
  };

  componentDidMount() {
    const {
      actions: {
        checkSession, // eslint-disable-line no-shadow
        login, // eslint-disable-line
      },
    } = this.props;
    
    const usernameFS = localStorage.getItem('username');

    if (usernameFS !== '' && typeof usernameFS !== 'undefined' && usernameFS !== null) {
      this.setState({ username: usernameFS });
    }

    checkSession();
  }

  updateTextbox = (key) => (event) => {
    const { value } = event.target;
    this.setState({ [key]: value });
  };

  handleRememberUsername = (isRemember) => {
    const { username } = this.state;
    if (isRemember) {
      localStorage.setItem('username', username);
    } else {
      localStorage.removeItem('username');
    }
  };

  redirectExpiredPassword = () => {
    const { router: { navigate }, accessPrivileges } = this.props;
    navigate(`${BASE_LAYOUT}/administration/loading`, {
      state: {
        path: getDefaultLandingPages(accessPrivileges),
        accessPrivileges,
      },
    });
  };

  redirect = () => {
    const { router: { navigate }, accessPrivileges } = this.props;
    navigate(`${BASE_LAYOUT}/dashboard/connector-monitoring`, {
      state: {
        path: '/dashboard/connector-monitoring',
        accessPrivileges,
      },
    });
  };
  
  handleLogin = (event) => {
    event.preventDefault();

    const { username, password } = this.state;
    const {
      actions,
      locale,
      t,
      rememberUser,
    } = this.props;
    let hasError = false;
    const localizationText = locale;
    const rememberUserID = JSON.parse(localStorage.getItem('rememberUser')) || rememberUser;
    
    if (!username || !password) {
      this.setState({ formErrorSummaryText: t(localizationText.INVALID_CREDENTIALS_MESSAGE) });
      hasError = true;
    } else {
      this.setState({ loading: true });
      hasError = false;
    }

    if (!hasError) {
      this.setState({ formErrorSummaryText: null });
      this.handleRememberUsername(rememberUserID);

      actions.login(username, password)
        .then(() => {
          const { data: { isAuthenticated, isPasswordExpired } } = this.props;
          this.setState({ loading: false });
          if (isAuthenticated) {
            const remainingDays = constants.START_ALERT_IN_DAYS;
            if (isPasswordExpired || remainingDays < constants.START_ALERT_IN_DAYS) {
              actions.showPasswordExpiryModal(true);
              this.redirectExpiredPassword();
            } else {
              this.redirect();
            }
          }
        });
    }
  };

  render() {
    const {
      error: loginError,
      t,
      actions: { setPageLocale, setUserId },
      locale,
    } = this.props;
    const {
      username,
      usernameErrorText,
      password,
      passwordErrorText,
      formErrorSummaryText,
      loading,
    } = this.state;

    const localizationText = locale;
    const rememberUser = JSON.parse(localStorage.getItem('rememberUser'));

    return (
      <div id="login-content" className="login-content ec-login">
        <div className={loading ? '' : 'hide'}>
          <Spinner />
        </div>
        <div className="login-input-container">
          <form onSubmit={this.handleLogin}>
            <div className="login-button-container">
              <InputContainer
                id="login-text"
                inputLabel={t(localizationText.LOGIN_ID_LABEL)}
                placeholder={t(localizationText.LOGIN_ID_PLACEHOLDER)}
                inputType="text"
                updateTextbox={this.updateTextbox}
                inputVal={username}
                name="username" />
              <ErrorLabel text={usernameErrorText} />
              <InputContainer
                id="login-pwd"
                inputLabel={t(localizationText.PASSWORD_LABEL)}
                placeholder={t(localizationText.LOGIN_PASSWORD_PLACEHOLDER)}
                inputType="password"
                updateTextbox={this.updateTextbox}
                inputVal={password}
                hasObfuscation
                name="password" />
              <ErrorLabel text={passwordErrorText} />
              <div className="input-container login-signin-button">
                <button
                  type="submit"
                  id="login-panel-signin-button"
                  className="login-btn submit">
                  {t(localizationText.SIGN_IN_LABEL)}
                </button>
              </div>
            </div>
            <div className="login-remember-me">
              <label htmlFor="input-remember-username" className="rember-my-login">
                <input
                  onClick={setUserId}
                  type="checkbox"
                  id="input-remember-username"
                  defaultChecked={rememberUser}
                  className="login-remember-username" />
                <span>{t(localizationText.REMEMBER_ME)}</span>
              </label>
            </div>
            <div className="language-selector">
              <div className="login-language-container">
                <div className="icon-container">
                  <FontAwesomeIcon icon={faGlobe} />
                </div>
                <div className="login-dropdown">
                  <DropDown
                    items={constants.DROPDOWN_LANG_DATA}
                    setValue={setPageLocale}
                    defaultValue={{
                      value: localizationText.LOCALE,
                      label: localizationText.language,
                    }} />
                </div>
              </div>
            </div>
          </form>
          {formErrorSummaryText || loginError ? (
            <FormErrorSummary summaryText={t(formErrorSummaryText || loginError)} />
          ) : null}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.login,
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    checkSession,
    login,
    setPageLocale: setLocale,
    setUserId: setRememberUser,
    showPasswordExpiryModal: togglePasswordExpiryModal,
  }, dispatch);
  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(withRouter(LoginForm)));
