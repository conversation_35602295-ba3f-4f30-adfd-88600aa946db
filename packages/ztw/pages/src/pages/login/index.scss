.login-page-container {
  background: var(--semantic-color-background-primary);
  width: 100%;
  height: 100%;
}
.login-page-hidden {
  display: none !important;
}
.login-page {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  min-height: 667px;
  max-height: 870px;
  min-width: 1060px;
  max-width: 1280px;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);

  @media (max-height: 870px) {
    box-shadow: 0 0 0 0;
    position: static;
    transform: none;
    margin: 0 auto;
  }

  .login-page-header-container {
    height: 120px;
    background: var(--semantic-color-background-primary);
    font-size: 0;
    border-bottom: 1px solid var(--semantic-color-border-base-primary);

    .login-page-header-logo-container {
      display: inline-block;
      vertical-align: middle;
      width: 344px;
      height: 100%;
      padding: 30px 36px;

      @media (max-width: 1095px) {
        padding: 30px 24px;
        width: 320px;
      }

      .login-page-header-logo {
        height: 60px;
        width: auto;
      }
    }
      
    .not-supported-container{
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 344px);
        height: 100%;
        padding: 22px 36px 22px 0;

        @media (max-width: 1095px) {
          padding-right: 16px;
          width: calc(100% - 320px);
        }

        .cookie-disabled-panel-content, .browser-not-supported-content {
          background: #fdeeed;
          border: 1px solid #d81903;
          color: var(--semantic-color-content-base-primary);
          border-radius: 5px;
          text-align: center;
          padding: 16px;
          height: 100%;
          width: 100%;

          &.browser-not-supported-content {
            padding: 8px 0;
            .no-script-header, .no-script-content {
              margin-bottom: 4px;
              padding: 0 8px;
            }
          }

          .no-script-header {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
          }

          .no-script-content {
            font-size: 12px;
          }
        }
    }
          
    .login-content {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 344px);
      height: 100%;
      padding: 16px 36px 16px 0;
      position: relative;

      @media (max-width: 1095px) {
        padding-right: 16px;
        width: calc(100% - 320px);
      }

      .input-container {
          display: inline-block;
          margin-right: 12px;
          vertical-align: bottom;
          height: auto;
          width: auto;
          padding: 0;
          input {
            height: 100%;
          }
          .login-text-label {
            color: var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            display: block;
            margin-bottom: 8px;
          }
          
        &.login-remember-me {
          color: var(--semantic-color-content-base-primary);
          .login-text-label {
            cursor: pointer;
            text-transform: none;
            display: inline-block;
            vertical-align: top;
            margin: 0;
            padding-left: 7px;
          }
          .login-remember-username {
            font-size: 16px;
            margin: 0;
            cursor: pointer;
          }
        }

        &.login-signin-button {
          margin-right: 0;
        }
      }

      .login-input-container {
        float: right;
        padding-right: 60px;

        @media (max-width: 1160px) {
          padding-right: 0;
        }
      }

      .login-remember-container {
        margin-top: 12px;
        .input-container {
          width: 280px;
          vertical-align: top;				
        }
      }
    }
    .login-error-container {
      background: #d81903;
      top: 120px;
      padding: 8px 16px;
      position: absolute;
      width: 572px;
      border-radius: 0 0 5px 5px;
      color: var(--semantic-color-surface-base-primary);
      font-size: 14px;
      z-index: 1;
      height: 36px;
    }
  }

  .login-banner-loader {
    height: calc(100% - 120px);
    width: 100%;
    text-align: center;
  }

  .login-page-banner-content {
    width: 100%;
    height: calc(100% - 120px);
    position: relative;
    
    .login-banner-container {
      width: 100%;
      height: calc(100% - 40px);

      .login-banner {
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          text-align: center;
          width: 100%;
          height: calc(100% - 150px);
          overflow: hidden;
          position: relative;

          &:hover {
            opacity: 0.95;
          }
          
          &.login-banner-small {
            min-width: 0;
            max-width: none;
            width: 25%;
            height: 100%;
            border-left: 1px solid var(--semantic-color-border-base-primary);
            border-top: 1px solid var(--semantic-color-border-base-primary);
            
            &:last-child {
                border-right: 0;
            }

            &:first-child {
                border-left: 0px;
            }
          }

          img {
            position: absolute;
            bottom: 0;
            left: 0;
          }
        }
        .login-banner-small-container {
            font-size: 0;
            height: 150px;
            position: relative;
            width: 100%;
        }
    }

    .login-page-footer-container {
      background: var(--semantic-color-content-immutable-black);
      color: var(--semantic-color-content-immutable-white);
      height: 40px;
      width: 100%;
      padding: 12px 36px;
      font-size: 14px;
      text-align: left;

      @media (max-width: 1095px) {
        padding: 12px 24px;
      }
    }
  }
}