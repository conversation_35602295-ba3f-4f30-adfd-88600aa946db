import React from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import Spinner from 'components/spinner';
import PasswordExpiryModal from 'components/PasswordExpiryModal';

import './index.scss';

class ChangePassword extends React.Component {
  state = {
    loading: false,
  };

  componentDidMount() {
    window.addEventListener('load', this.handleLoad);
  }

  render() {
    const { loading } = this.state;

    if (loading) return <Spinner />;
  
    return (
      <PasswordExpiryModal callTogglePasswordExpiryChangeListener={(str) => str} />
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.login,
  cloudData: state.cloud,
  isAuthenticated: Boolean(localStorage.getItem('authenticated')),
  isPasswordExpired: Boolean(localStorage.getItem('passwordExpired')),
});

export default connect(mapStateToProps)(withTranslation()(ChangePassword));
