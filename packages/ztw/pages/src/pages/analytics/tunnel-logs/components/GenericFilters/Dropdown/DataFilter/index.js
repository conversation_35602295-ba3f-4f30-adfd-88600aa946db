/* eslint-disable react/jsx-handler-names */
// @flow

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import {
  reduxForm,
} from 'redux-form';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import DropdownWithSearchInsights from 'components/dropDown/DropdownWithSearchInsights';
import * as TunnelInsightsSelectors from 'ducks/tunnelInsights/selectors';
import { withTranslation } from 'react-i18next';
import {
  applyDataType,
  getDataTypeFilters,
  setDataTypeSearchString,
  handleSelectedDataTypeFilter,
} from 'ducks/tunnelInsights';

const localize = (data, t) => {
  const localizedData = data;
  localizedData.label = t(localizedData.label);
  return localizedData;
};

export class DataFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions: { load } } = this.props;
    load();
  }

  render() {
    const {
      dataTypeFilters, defaultDataTypeValue, currentDataTypeFilters, actions, t,
    } = this.props;

    return (
      <div className="entity-dropdown-container">
        <Loading {...this.props}>
          <ServerError {...this.props} size="small">
            <form onSubmit={actions.applyDataType}>
              <DropdownWithSearchInsights
                {...this.props}
                items={dataTypeFilters}
                currentItems={currentDataTypeFilters}
                setValue={(str) => str}
                defaultValue={localize(defaultDataTypeValue, t)}
                searchParamName="dataTypeFiltersSearch"
                setSearchString={actions.setSearchString}
                onClickCb={actions.handleSelectedDataTypeFilter}
                searchString="" />
            </form>
          </ServerError>
        </Loading>
      </div>
    );
  }
}

DataFiltersForm.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
  }),
  defaultDataTypeValue: PropTypes.arrayOf(PropTypes.shape()),
  dataTypeFilters: PropTypes.arrayOf(PropTypes.shape()),
  currentDataTypeFilters: PropTypes.arrayOf(PropTypes.shape()),
  loading: PropTypes.bool,
  t: PropTypes.func,
};

DataFiltersForm.defaultProps = {
  actions: {
    load: noop,
  },
  defaultDataTypeValue: [],
  dataTypeFilters: [],
  currentDataTypeFilters: [],
  loading: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...TunnelInsightsSelectors.default(state),
  // filterFormValues: getFormValues('tunnelInsightsFiltersForm')(state),
  // defaultDataTypeValue: TunnelInsightsSelectors.defaultDataTypValueSelector(state),
  dataTypeFilters: TunnelInsightsSelectors.currentDataTypeDropdownSelector(state),
  initialValues: {},
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    applyDataType,
    load: getDataTypeFilters,
    setSearchString: setDataTypeSearchString,
    handleSelectedDataTypeFilter,
  }, dispatch);

  return {
    actions,
  };
};

const DataFilter = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'tunnelInsightsFiltersForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {},
  asyncBlurFields: [],
})(withTranslation()(DataFiltersForm)));

export default DataFilter;
