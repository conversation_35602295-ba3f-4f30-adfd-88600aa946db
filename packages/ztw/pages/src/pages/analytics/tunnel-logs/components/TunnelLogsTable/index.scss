@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.main-container {
  padding: 19px 25px;

  .container-row{
    margin: 30px 0;
  }
  
}
.no-overflow-text {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.display-flex-space-between {
  display: flex;
  justify-content: space-between;
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}

