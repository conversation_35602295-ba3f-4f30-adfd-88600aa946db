// @flow

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import i18n from 'utils/i18n';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAdd } from '@fortawesome/pro-light-svg-icons';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import AdvDropdownWithSearch from 'components/dropDown/AdvDropdownWithSearch';
import * as TunnelLogsSelectors from 'ducks/tunnelLogs/selectors';
import {
  getFilters,
  setSearchString,
  handleSelectedAddFilter,
} from 'ducks/tunnelLogs';

export class AddFilter extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    filters: PropTypes.arrayOf(PropTypes.shape()),
    currentFilters: PropTypes.arrayOf(PropTypes.shape()),
    loading: PropTypes.bool,
  };

  static defaultProps = {
    actions: {
      load: noop,
    },
    filters: [],
    currentFilters: [],
    loading: false,
  };

  componentDidMount() {
    const { actions: { load } } = this.props;
    load();
  }

  render() {
    const { filters, currentFilters, actions } = this.props;

    return (
      <div className="entity-dropdown-container add-dropdown">
        <Loading {...this.props}>
          <ServerError {...this.props} size="small">
            <AdvDropdownWithSearch
              {...this.props}
              items={filters}
              currentItems={currentFilters}
              setValue={(str) => str}
              defaultValue={{
                value: '0',
                label:
  <>
    <FontAwesomeIcon icon={faAdd} className="interactive-icon" />
    {' '}
    {i18n.t('ADD')}
  </>,
              }}
              searchParamName="addFiltersSearch"
              setSearchString={actions.setSearchString}
              onClickCb={actions.handleSelectedAddFilter}
              searchString="" />
          </ServerError>
        </Loading>
      </div>
    );
  }
}

export const mapStateToProps = (state) => ({
  filters: TunnelLogsSelectors.addFilterCurrentDropdownSelector(state),
});

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: getFilters,
    setSearchString,
    handleSelectedAddFilter,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(AddFilter);
