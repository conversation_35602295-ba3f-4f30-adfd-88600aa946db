import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import * as TunnelLogsSelectors from 'ducks/tunnelLogs/selectors';
import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import HeaderRowSearchTool from 'components/tablePro/HeaderRowSearchTool';
import ReactTooltip from 'react-tooltip';
import {
  updateMenu,
  handleOnSearchFilter,
  handleOnClearFilter,
  handleOnSortClick,
} from 'ducks/tunnelLogs';

import './index.scss';

class TunnelLogsTable extends Component {
  static propTypes = {
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    showForm: PropTypes.bool,
    tunnelColumns: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
    searchData: PropTypes.string,
    showSearch: PropTypes.bool,
    sortable: PropTypes.number,
  };

  static defaultProps = {
    tableData: [],
    actions: {
      load: null,
    },
    showForm: null,
    tunnelColumns: [],
    t: (str) => str,
    searchData: '',
    showSearch: false,
  };

  state = {
    showComponent: false,
    searchField: '',
  };

  componentDidUpdate() {
    ReactTooltip.rebuild();
  }

  handleOnClickColumnsHeader = () => {
    this.setState(() => ({ showComponent: true }));
  };

  onClickOutSide = () => {
    this.setState({ showComponent: false });
  };

  handleSearchField = (value) => {
    this.setState({ searchField: value });
  };

  getColumns = () => {
    const {
      tunnelColumns,
      actions,
      t,
      searchData,
      showSearch,
    } = this.props;
    const {
      editRowHandler,
    } = actions;
    const {
      searchField,
    } = this.state;

    tunnelColumns.map((i) => {
      // eslint-disable-next-line no-param-reassign
      i.headerRenderer = i.searchable ? (
        <span className="display-flex-space-between" data-tip={t(i.name)} data-for="header-tooltip">
          <span className="no-overflow-text">
            {t(i.name)}
          </span>
          <HeaderRowSearchTool
            fieldName={i.key}
            showSearchBox={showSearch}
            searchField={searchField}
            searchData={searchData}
            onStartSearch={this.handleSearchField}
            onClearFilter={actions.handleOnClearFilter}
            onSearchFilter={actions.handleOnSearchFilter} />
        </span>
      ) : (
        <span className="display-flex-space-between" data-tip={t(i.name)} data-for="header-tooltip">
          {t(i.name)}
        </span>
      );
      return i;
    });
    const lastColumn = tunnelColumns[tunnelColumns.length - 1];
    const { showComponent } = this.state;
    const lastColWithElipsis = {
      ...lastColumn,
      name: '',
      visible: true,
      draggable: false,
      resizable: false,
      checkbox: false,
      sortable: false,
      frozen: false,
      width: 40,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          handleLeave={this.onClickOutSide}
          showComponent={showComponent}
          onMouseOverCb={this.handleOnClickColumnsHeader} />
      ),
      // formatter: <RowActions showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    tunnelColumns[tunnelColumns.length - 1] = lastColWithElipsis;
    return [...tunnelColumns];
  };

  render() {
    const {
      tableData,
      searchData,
      actions,
      sortable,
    } = this.props;

    const { showComponent, searchField } = this.state;
    const columns = this.getColumns();
    const filteredData = (searchField !== '' && searchData !== '')
      ? tableData.filter((x) => x[searchField].includes(searchData))
      : tableData;

    return (
      <Loading {...this.props}>
        <div className="table-layout-header">
          <ReactTooltip place="top" type="light" effect="solid" data-multiline="true" id="header-tooltip" className="react-tooltip" />
          <TableWrapper
            key={sortable}
            initialRows={filteredData}
            initialColumns={columns}
            formatTimeStamp={['eventTimestamp', 'logTimestamp']}
            updateMenu={actions.updateMenu}
            // eslint-disable-next-line react/jsx-handler-names
            handleOnSortClick={actions.handleOnSortClick}
            showNoDataValue />
          <CustomizeColumns
            // hasSelectAll
            initialItems={columns}
            handleLeave={this.onClickOutSide}
            showComponent={showComponent}
            dragnDrop={actions.updateMenu} />
        </div>
      </Loading>
    );
  }
}

const mapStateToProps = (state) => ({ ...TunnelLogsSelectors.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    handleOnSearchFilter,
    handleOnClearFilter,
    handleOnSortClick,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(TunnelLogsTable));
