.main-container {
  padding: 19px 25px;
  .component-header {	
    height: 24px;
    width: fit-content;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    white-space: nowrap;
  }
  .header {
    width: 100%;
    float: left;
    display: flex;
    justify-content: space-between;
  }
  .download {
    float: left;
    margin: 1em;
    cursor: default;
    display: flex;
  }
  .download-button {
    margin-left: 8px;
  }
}

.accordion-container {
  width: 100%;
  height: 100vh;
  // background-color: whitesmoke;
  position: relative;
  .ecui-waiting-overlay  {
    background: none;
  }
}

.outer-layer {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  // overflow: hidden;
  .knob-holder{
    transform: scaleX(-1);
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
    height: 35px;
    width: 23px;
    // border-radius: 0 5px 5px 0; //opposite box
    border-radius: 5px 0px 0px 5px;
    background: var(--semantic-color-background-pale);
    z-index: 0;
    padding-left: 1.5em;
  }
  .knob{
    height: 27px;
    width: 11px;
    transform: scaleX(-1);
    color:  var(--semantic-color-content-base-primary);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
  }
  .slide {
    height: 100%;
    position: relative;
    text-align: center;
    // transition: 0.1s;
  }
  
  .filter{
      width: 1px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
  }
 
  .filter-expand {
    // width: 25%;
    width:  400px;
    // background-color: #000000;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    min-height: max-content;
    height: max-content;
  }

  .tablesec{
      width: 98%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: left;
  }
  .tablesec-shrink {
    width: calc(100% - 32rem);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
    overflow-y: auto;
  }

  .logs-insights-tooltip-container {
		min-height: 30px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
	}
}

