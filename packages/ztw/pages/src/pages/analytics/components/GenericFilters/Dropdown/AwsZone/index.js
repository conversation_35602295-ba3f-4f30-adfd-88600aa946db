// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import ZoneDropdown from 'commonConnectedComponents/dropdown/ZoneDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function Zone(props) {
  const {
    showFilters, actions, t, availabilityZoneType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'availabilityZoneType', 'INCLUDE'));
  }, [showFilters.availabilityZone]);
  
  if (!showFilters.availabilityZone) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('AWS_AVAILABILITY_ZONE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('availabilityZone')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('availabilityZone')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="availabilityZoneType"
        name="availabilityZoneType"
        options={[{
          name: 'availabilityZoneType', value: 'INCLUDE', checked: availabilityZoneType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'availabilityZoneType', value: 'EXCLUDE', checked: availabilityZoneType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="availabilityZone"
        name="availabilityZone"
        component={ZoneDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Zone.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  availabilityZoneType: PropTypes.string,
};
  
Zone.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  availabilityZoneType: 'INCLUDE',
};

export default (withTranslation()(Zone));
