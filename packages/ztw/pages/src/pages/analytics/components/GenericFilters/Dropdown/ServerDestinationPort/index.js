// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  isInteger,
  required,
} from 'utils/validations';

export function ServerDestinationPort(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.serverDestinationPortFrom || formSyncErrors.serverDestinationPortTo;
  const metaFrom = formMeta.serverDestinationPortFrom || {};
  const metaTo = formMeta.serverDestinationPortTo || {};
  const hasError = (metaFrom.touched || metaTo.touched) && !!error;

  if (!showFilters.serverDestinationPort) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="serverDestinationPortLabel"
          text={(
            <>
              {t('SERVER_DESTINATION_PORT')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ENTER_BELOW_VALUES')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('serverDestinationPort')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('serverDestinationPort')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div className="couple-filters">
        <Field
          name="serverDestinationPortFrom"
          id="serverDestinationPortFrom"
          component={Input}
          type="number"
          label={t('FROM')}
          validate={[
            isInteger,
            actions.validateServerDestinationPortFrom,
            required,
          ]} />
      </div>
      <div className="couple-filters couple-filters-bottom">
        <Field
          name="serverDestinationPortTo"
          id="serverDestinationPortTo"
          component={Input}
          type="number"
          label={t('TO')}
          validate={[
            isInteger,
            actions.validateServerDestinationPortTo,
            required,
          ]} />
      </div>
    </div>
  );
}

ServerDestinationPort.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
ServerDestinationPort.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(ServerDestinationPort));
