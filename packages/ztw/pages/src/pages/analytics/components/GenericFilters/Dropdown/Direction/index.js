// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { EntityDropdown } from 'components/entityDropdown';

// import { EC_DIRECTION } from 'config';
import EcDirection from '@zscaler/ec-domain/json/EcDirection.json';

function Types(props) {
  const enums = Object.keys(EcDirection)
    .sort()
    .map((key) => ({
      id: key,
      name: key,
    }));

  return (
    <EntityDropdown
      data={enums}
      {...props} />
  );
}
  
export function Direction(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.direction) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('DIRECTION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('direction')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('direction')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="direction"
        name="direction"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

Direction.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
Direction.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(Direction));
