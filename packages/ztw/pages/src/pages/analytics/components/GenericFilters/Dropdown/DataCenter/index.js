// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Suffix from './Suffix';
import MatchType from '../MatchType';

export function DataCenter(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.dataCenter) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('DATA_CENTER')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('dataCenter')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('dataCenter')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div>
        <div className="prefix">
          <Field
            id="dataCenterMatchType"
            name="dataCenterMatchType"
            component={MatchType}
            parse={(value) => value.id} />
        </div>
        <Suffix {...props} />
      </div>
    </div>
  );
}

DataCenter.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DataCenter.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DataCenter));
