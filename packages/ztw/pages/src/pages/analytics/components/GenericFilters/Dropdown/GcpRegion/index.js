// @flow
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import GcpRegionsDropdown from 'commonConnectedComponents/dropdown/GcpRegionsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function GcpRegion(props) {
  const {
    showFilters, actions, t, gcpRegionType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'gcpRegionType', 'INCLUDE'));
  }, [showFilters.gcpRegion]);
  
  if (!showFilters.gcpRegion) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('GCP_REGION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('gcpRegion')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('gcpRegion')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="gcpRegionType"
        name="gcpRegionType"
        options={[{
          name: 'gcpRegionType', value: 'INCLUDE', checked: gcpRegionType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'gcpRegionType', value: 'EXCLUDE', checked: gcpRegionType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="gcpRegion"
        name="gcpRegion"
        component={GcpRegionsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

GcpRegion.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  gcpRegionType: PropTypes.string,
};
  
GcpRegion.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  gcpRegionType: 'INCLUDE',
};

export default (withTranslation()(GcpRegion));
