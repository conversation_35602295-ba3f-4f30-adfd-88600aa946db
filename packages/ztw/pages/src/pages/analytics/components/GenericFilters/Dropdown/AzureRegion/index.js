// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import AzureRegionsDropdown from 'commonConnectedComponents/dropdown/AzureRegionsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AzureRegion(props) {
  const {
    showFilters, actions, t, azureRegionType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'azureRegionType', 'INCLUDE'));
  }, [showFilters.azureRegion]);
  
  if (!showFilters.azureRegion) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('AZURE_REGION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('azureRegion')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('azureRegion')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="azureRegionType"
        name="azureRegionType"
        options={[{
          name: 'azureRegionType', value: 'INCLUDE', checked: azureRegionType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'azureRegionType', value: 'EXCLUDE', checked: azureRegionType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="azureRegion"
        name="azureRegion"
        component={AzureRegionsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AzureRegion.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  azureRegionType: PropTypes.string,
};
  
AzureRegion.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  azureRegionType: 'INCLUDE',
};

export default (withTranslation()(AzureRegion));
