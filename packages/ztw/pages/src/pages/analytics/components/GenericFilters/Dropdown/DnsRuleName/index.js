// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import DnsRuleDropdown from 'commonConnectedComponents/dropdown/DnsRuleDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function DnsRuleName(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.dnsRuleName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('DNS_RULE_NAME')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('dnsRuleName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('dnsRuleName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="dnsRuleName"
        name="dnsRuleName"
        component={DnsRuleDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

DnsRuleName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DnsRuleName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DnsRuleName));
