// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import AwsRegionsDropdown from 'commonConnectedComponents/dropdown/AwsRegionsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AwsRegion(props) {
  const {
    showFilters, actions, t, awsRegionType, form,
  } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(change(form, 'awsRegionType', 'INCLUDE'));
  }, [showFilters.awsRegion]);

  if (!showFilters.awsRegion) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('AWS_REGION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('awsRegion')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('awsRegion')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="awsRegionType"
        name="awsRegionType"
        options={[{
          name: 'awsRegionType', value: 'INCLUDE', checked: awsRegionType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'awsRegionType', value: 'EXCLUDE', checked: awsRegionType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="awsRegion"
        name="awsRegion"
        component={AwsRegionsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AwsRegion.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  awsRegionType: PropTypes.string,
  form: PropTypes.string,
};
  
AwsRegion.defaultProps = {
  t: (str) => str,
  showFilters: {},
  awsRegionType: 'INCLUDE',
};

export default (withTranslation()(AwsRegion));
