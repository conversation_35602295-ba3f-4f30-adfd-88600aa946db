// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';

import { EntityDropdown } from 'components/entityDropdown';
import { PROTOCOL_TYPES } from 'config';

function Types(props) {
  return (
    <EntityDropdown
      data={PROTOCOL_TYPES}
      {...props} />
  );
}

export function ProtocolType(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.protocolType) return null;
  return (
    <div className="filter-container simple-dropdown">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('PROTOCOL_TYPE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('protocolType')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('protocolType')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="protocolType"
        name="protocolType"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

ProtocolType.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
ProtocolType.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(ProtocolType));
