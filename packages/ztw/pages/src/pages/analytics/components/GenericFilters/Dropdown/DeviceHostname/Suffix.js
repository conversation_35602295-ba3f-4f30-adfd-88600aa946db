// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { Field } from 'redux-form';
import Input from 'components/Input';

import {
  required,
} from 'utils/validations';

export function Suffix(props) {
  const { filterFormValues } = props;
  const { deviceHostnameMatchType } = filterFormValues;

  if (deviceHostnameMatchType && (deviceHostnameMatchType === 'NOT_NULL' || deviceHostnameMatchType === 'IS_NULL')) {
    return (
      <div className="empty-suffix">
        <Field
          name="deviceHostnameEmpty"
          id="deviceHostnameEmpty"
          isDisabled
          input={{ value: 'yes' }}
          component={Input}
          validate={[]} />
      </div>
    );
  }

  return (
    <div className="suffix">
      <Field
        name="deviceHostname"
        id="deviceHostname"
        component={Input}
        validate={[
          required,
        ]} />
    </div>
  );
}

Suffix.propTypes = {
  filterFormValues: PropTypes.shape({}),
};
  
Suffix.defaultProps = {
  filterFormValues: {},
};

export default Suffix;
