// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import NetworkServiceDropdown from 'commonConnectedComponents/dropdown/NetworkServiceDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function NetworkService(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.networkService) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Network Service')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('networkService')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('networkService')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="networkService"
        name="networkService"
        component={NetworkServiceDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

NetworkService.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
NetworkService.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(NetworkService));
