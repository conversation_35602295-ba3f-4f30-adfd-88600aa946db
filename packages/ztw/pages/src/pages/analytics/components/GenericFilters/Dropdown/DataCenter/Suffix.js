// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { Field } from 'redux-form';
import Input from 'components/Input';

import {
  required,
} from 'utils/validations';

export function Suffix(props) {
  const { filterFormValues } = props;
  const { dataCenterMatchType } = filterFormValues;

  if (dataCenterMatchType && (dataCenterMatchType === 'NOT_NULL' || dataCenterMatchType === 'IS_NULL')) {
    return (
      <div className="empty-suffix">
        <Field
          name="dataCenterEmpty"
          id="dataCenterEmpty"
          isDisabled
          input={{ value: 'yes' }}
          component={Input}
          validate={[]} />
      </div>
    );
  }

  return (
    <div className="suffix">
      <Field
        name="dataCenter"
        id="dataCenter"
        component={Input}
        validate={[
          required,
        ]} />
    </div>
  );
}

Suffix.propTypes = {
  filterFormValues: PropTypes.shape({}),
};
  
Suffix.defaultProps = {
  filterFormValues: {},
};

export default Suffix;
