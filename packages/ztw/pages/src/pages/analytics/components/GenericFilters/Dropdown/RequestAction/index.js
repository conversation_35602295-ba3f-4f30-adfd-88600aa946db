// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';

import { EntityDropdown } from 'components/entityDropdown';
import { REQ_ACTION } from 'config';

function Types(props) {
  return (
    <EntityDropdown
      data={REQ_ACTION.filter((x) => x.id !== 'RESOLVED_BY_ZPA')}
      {...props} />
  );
}

export function RequestAction(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.requestAction) return null;
  return (
    <div className="filter-container simple-dropdown">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('ACTION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('requestAction')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('requestAction')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="requestAction"
        name="requestAction"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

RequestAction.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
RequestAction.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(RequestAction));
