// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';

import { EntityDropdown } from 'components/entityDropdown';
import { EC_TRAFFIC_TYPE } from 'config';

function Types(props) {
  return (
    <EntityDropdown
      data={EC_TRAFFIC_TYPE}
      {...props} />
  );
}

export function TrafficType(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.trafficType) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('TRAFFIC_TYPE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('trafficType')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('trafficType')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="trafficType"
        name="trafficType"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

TrafficType.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
TrafficType.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(TrafficType));
