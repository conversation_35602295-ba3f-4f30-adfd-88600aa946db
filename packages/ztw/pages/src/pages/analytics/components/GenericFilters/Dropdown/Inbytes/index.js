// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  isInteger,
  required,
} from 'utils/validations';

export function Inbytes(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.inbytesFrom || formSyncErrors.inbytesTo;
  const metaFrom = formMeta.inbytesFrom || {};
  const metaTo = formMeta.inbytesTo || {};
  const hasError = (metaFrom.touched || metaTo.touched) && !!error;

  if (!showFilters.inbytes) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="inbytesLabel"
          text={(
            <>
              {t('INBYTES')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ENTER_BELOW_VALUES')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('inbytes')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('inbytes')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div className="couple-filters">
        <Field
          name="inbytesFrom"
          id="inbytesFrom"
          component={Input}
          type="number"
          label={t('FROM')}
          validate={[
            isInteger,
            actions.validateInbytesFrom,
            required,
          ]} />
      </div>
      <div className="couple-filters couple-filters-bottom">
        <Field
          name="inbytesTo"
          id="inbytesTo"
          component={Input}
          type="number"
          label={t('TO')}
          validate={[
            isInteger,
            actions.validateInbytesTo,
            required,
          ]} />
      </div>
    </div>
  );
}

Inbytes.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
Inbytes.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(Inbytes));
