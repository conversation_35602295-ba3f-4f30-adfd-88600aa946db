// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import CountryDropdown from 'commonConnectedComponents/dropdown/CountryDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function Country(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.serverCountryCode) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Country')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('serverCountryCode')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('serverCountryCode')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="serverCountryCode"
        name="serverCountryCode"
        component={CountryDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Country.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
Country.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(Country));
