// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import SubscriptionIdsDropdown from 'commonConnectedComponents/dropdown/SubscriptionIdsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AzureSubscriptionId(props) {
  const {
    showFilters, actions, t, subscriptionIdType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'subscriptionIdType', 'INCLUDE'));
  }, [showFilters.subscriptionId]);

  if (!showFilters.subscriptionId) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('SUBSCRIPTION_ID')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('subscriptionId')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('subscriptionId')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="subscriptionIdType"
        name="subscriptionIdType"
        options={[{
          name: 'subscriptionIdType', value: 'INCLUDE', checked: subscriptionIdType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'subscriptionIdType', value: 'EXCLUDE', checked: subscriptionIdType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="subscriptionId"
        name="subscriptionId"
        component={SubscriptionIdsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AzureSubscriptionId.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  subscriptionIdType: PropTypes.string,
};
  
AzureSubscriptionId.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  form: '',
  subscriptionIdType: 'INCLUDE',
};

export default (withTranslation()(AzureSubscriptionId));
