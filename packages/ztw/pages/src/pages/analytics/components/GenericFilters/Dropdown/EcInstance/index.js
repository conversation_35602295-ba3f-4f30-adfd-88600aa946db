// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import EcInstanceDropdown from 'commonConnectedComponents/dropdown/EcInstanceDropdownReportFilter';

const parseDropdownValues = (value) => get(value, 'original', value);

export function EcInstance(props) {
  const {
    showFilters, actions, t, ecInstanceType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'ecInstanceType', 'INCLUDE'));
  }, [showFilters.ecInstance]);

  if (!showFilters.ecInstance) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_INSTANCE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ecInstance')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ecInstance')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="ecInstanceType"
        name="ecInstanceType"
        options={[{
          name: 'ecInstanceType', value: 'INCLUDE', checked: ecInstanceType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'ecInstanceType', value: 'EXCLUDE', checked: ecInstanceType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="ecInstance"
        name="ecInstance"
        component={EcInstanceDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

EcInstance.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  ecInstanceType: PropTypes.string,
};
  
EcInstance.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  ecInstanceType: 'INCLUDE',
};

export default (withTranslation()(EcInstance));
