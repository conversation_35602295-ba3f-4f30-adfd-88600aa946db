// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import DnsRequestTypeDropdown from 'commonConnectedComponents/dropdown/DnsRequestTypeDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function DnsRequestType(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.dnsRequestType) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('DNS_REQ_TYPE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('dnsRequestType')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('dnsRequestType')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="dnsRequestType"
        name="dnsRequestType"
        component={DnsRequestTypeDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

DnsRequestType.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DnsRequestType.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DnsRequestType));
