// @flow

import React from 'react';
import { change, getFormValues } from 'redux-form';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import DateDropdown from 'components/dropDown/DateDropdown';
import moment from 'moment';
import { LOGS_TIME_FRAME_DATA } from 'config';
import {
  convertStartTime,
  convertEndTime,
} from 'utils/helpers';

function TimeFrame({ meta, id }) {
  const { form } = meta;
  const dispatch = useDispatch();
  const options = LOGS_TIME_FRAME_DATA.map((item) => ({
    ...item,
    value: item.id,
    startDate: moment.unix((convertStartTime(item.id) / 1000)).format(),
    endDate: moment.unix((convertEndTime(item.id) / 1000)).format(),
    label: item.name,
  }));
  const { timeFrame = {} } = useSelector((state) => getFormValues(form)(state)) || {};

  const onValueChange = (input) => {
    dispatch(change(form, id, input));
  };
  if (timeFrame.value !== 'custom') {
    timeFrame.startDate = moment(convertStartTime(timeFrame.value)).format('MM/DD/YYYY hh:mm:ss');
    timeFrame.endDate = moment(convertEndTime(timeFrame.value)).format('MM/DD/YYYY hh:mm:ss');
    timeFrame.startTime = convertStartTime(timeFrame.value);
    timeFrame.endTime = convertEndTime(timeFrame.value);
  } else {
    timeFrame.startTime = timeFrame.startDate.unix() * 1000;
    timeFrame.endTime = timeFrame.endDate.unix() * 1000;
  }

  return (
    <div className="time-filter">
      <div className="time-filter-dropdown">
        <DateDropdown
          items={options}
          dateRange={180}
          maxDateSelectable={90}
          defaultValue={options[8]}
          currentValue={timeFrame}
          setValue={onValueChange} />
      </div>
    </div>
  );
}

TimeFrame.propTypes = {
  meta: PropTypes.shape({}),
  id: PropTypes.string,
};

TimeFrame.defaultProps = {
  meta: {},
  id: '',
};

export default TimeFrame;
