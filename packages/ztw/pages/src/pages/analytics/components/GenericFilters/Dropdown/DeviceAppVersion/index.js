// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Suffix from './Suffix';
import MatchType from '../MatchType';

export function DeviceAppVersion(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.deviceAppVersion;
  const meta = formMeta.deviceAppVersion || {};
  const hasError = meta.touched && !!error;

  if (!showFilters.deviceAppVersion) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="deviceAppVersionLabel"
          text={(
            <>
              {t('EC_DEVICE_APP_VERSION')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ADD_EC_DEVICE_APP_VERSION')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('deviceAppVersion')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('deviceAppVersion')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div>
        <div className="prefix">
          <Field
            id="deviceAppVersionMatchType"
            name="deviceAppVersionMatchType"
            component={MatchType}
            parse={(value) => value.id} />
        </div>
        <Suffix {...props} />
      </div>
    </div>
  );
}

DeviceAppVersion.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
DeviceAppVersion.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(DeviceAppVersion));
