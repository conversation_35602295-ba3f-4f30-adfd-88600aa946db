// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { EntityDropdown } from 'components/entityDropdown';

import { RESOLVER } from 'config';

function Types(props) {
  return (
    <EntityDropdown
      data={RESOLVER}
      {...props} />
  );
}
  
export function Resolver(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.dnsResolver) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('RESOLVER')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('dnsResolver')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('dnsResolver')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="dnsResolver"
        name="dnsResolver"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

Resolver.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
Resolver.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(Resolver));
