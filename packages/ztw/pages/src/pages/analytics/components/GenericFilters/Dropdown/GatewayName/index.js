// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import GatewayNameDropdown from 'commonConnectedComponents/dropdown/GatewayNameDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function GatewayName(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.gatewayName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Gateway Name')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('gatewayName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('gatewayName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="gatewayName"
        name="gatewayName"
        component={GatewayNameDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

GatewayName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
GatewayName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(GatewayName));
