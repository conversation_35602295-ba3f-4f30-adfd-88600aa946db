// @flow
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import GcpZoneDropdown from 'commonConnectedComponents/dropdown/GcpZoneDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function GcpZone(props) {
  const {
    showFilters, actions, t, gcpAvailabilityZoneType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'gcpAvailabilityZoneType', 'INCLUDE'));
  }, [showFilters.gcpAvailabilityZone]);
  
  if (!showFilters.gcpAvailabilityZone) return null;
  
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('GCP_AVAILABILITY_ZONE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('gcpAvailabilityZone')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('gcpAvailabilityZone')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="gcpAvailabilityZoneType"
        name="gcpAvailabilityZoneType"
        options={[{
          name: 'gcpAvailabilityZoneType', value: 'INCLUDE', checked: gcpAvailabilityZoneType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'locatiogcpAvailabilityZoneTypenType', value: 'EXCLUDE', checked: gcpAvailabilityZoneType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="gcpAvailabilityZone"
        name="gcpAvailabilityZone"
        component={GcpZoneDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

GcpZone.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  gcpAvailabilityZoneType: PropTypes.string,
};
  
GcpZone.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  gcpAvailabilityZoneType: 'INCLUDE',
};

export default (withTranslation()(GcpZone));
