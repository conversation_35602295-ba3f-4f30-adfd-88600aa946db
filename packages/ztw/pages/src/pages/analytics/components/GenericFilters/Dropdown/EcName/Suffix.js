// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { Field } from 'redux-form';
import Input from 'components/Input';

import {
  required,
} from 'utils/validations';

export function Suffix(props) {
  const { filterFormValues } = props;
  const { ecNameMatchType } = filterFormValues;

  if (ecNameMatchType && (ecNameMatchType === 'NOT_NULL' || ecNameMatchType === 'IS_NULL')) {
    return (
      <div className="empty-suffix">
        <Field
          name="ecNameEmpty"
          id="ecNameEmpty"
          isDisabled
          input={{ value: 'yes' }}
          component={Input}
          validate={[]} />
      </div>
    );
  }

  return (
    <div className="suffix">
      <Field
        name="ecName"
        id="ecName"
        component={Input}
        validate={[
          required,
        ]} />
    </div>
  );
}

Suffix.propTypes = {
  filterFormValues: PropTypes.shape({}),
};
  
Suffix.defaultProps = {
  filterFormValues: {},
};

export default Suffix;
