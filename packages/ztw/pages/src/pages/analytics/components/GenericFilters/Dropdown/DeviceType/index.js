// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  omit,
} from 'utils/lodash';
import { EntityDropdown } from 'components/entityDropdown';
// import { EC_TRAFFIC_TYPE } from 'config';
import DeviceTypes from '@zscaler/ec-domain/json/DeviceTypes.json';

function Types(props) {
  const enums = Object.keys(omit(DeviceTypes, 'NONE'))
    .sort()
    // .filter(item => item.name !== 'NONE')
    .map((key) => ({
      id: key,
      name: key,
    }));

  return (
    <EntityDropdown
      data={enums}
      {...props} />
  );
}

export function DeviceType(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.deviceType) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_DEVICE_TYPE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('deviceType')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('deviceType')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="deviceType"
        name="deviceType"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

DeviceType.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DeviceType.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DeviceType));
