// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import LocationsDropdown from 'commonConnectedComponents/dropdown/LocationsDropdownInsights';

const parseDropdownValues = (value) => get(value, 'original', value);

export function Location(props) {
  const {
    showFilters, actions, t, locationType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'locationType', 'INCLUDE'));
  }, [showFilters.locationName]);

  if (!showFilters.locationName) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('LOCATION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('locationName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('locationName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="locationType"
        name="locationType"
        options={[{
          name: 'locationType', value: 'INCLUDE', checked: locationType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'locationType', value: 'EXCLUDE', checked: locationType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="locationName"
        name="locationName"
        component={LocationsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Location.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  locationType: PropTypes.string,
};
  
Location.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  form: '',
  locationType: 'INCLUDE',
};

export default (withTranslation()(Location));
