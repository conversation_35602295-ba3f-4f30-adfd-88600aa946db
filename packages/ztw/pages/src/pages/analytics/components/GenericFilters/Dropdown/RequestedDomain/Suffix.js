// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { Field } from 'redux-form';
import Input from 'components/Input';

import {
  required,
} from 'utils/validations';

export function Suffix(props) {
  const { filterFormValues } = props;
  const { requestedDomainMatchType } = filterFormValues;

  if (requestedDomainMatchType && (requestedDomainMatchType === 'NOT_NULL' || requestedDomainMatchType === 'IS_NULL')) {
    return (
      <div className="empty-suffix">
        <Field
          name="requestedDomainEmpty"
          id="requestedDomainEmpty"
          isDisabled
          input={{ value: 'yes' }}
          component={Input}
          validate={[]} />
      </div>
    );
  }

  return (
    <div className="suffix">
      <Field
        name="requestedDomain"
        id="requestedDomain"
        component={Input}
        validate={[
          required,
        ]} />
    </div>
  );
}

Suffix.propTypes = {
  filterFormValues: PropTypes.shape({}),
};
  
Suffix.defaultProps = {
  filterFormValues: {},
};

export default Suffix;
