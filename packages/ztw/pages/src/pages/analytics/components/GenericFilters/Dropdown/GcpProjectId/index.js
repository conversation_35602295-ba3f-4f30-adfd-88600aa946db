// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import GcpProjectIdsDropdown from 'commonConnectedComponents/dropdown/GcpProjectIdsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function GcpProjectId(props) {
  const {
    showFilters, actions, t, projectIdType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'projectIdType', 'INCLUDE'));
  }, [showFilters.projectId]);

  if (!showFilters.projectId) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_PROJECT_ID')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('projectId')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('projectId')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="projectIdType"
        name="projectIdType"
        options={[{
          name: 'projectIdType', value: 'INCLUDE', checked: projectIdType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'projectIdType', value: 'EXCLUDE', checked: projectIdType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="projectId"
        name="projectId"
        component={GcpProjectIdsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

GcpProjectId.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  projectIdType: PropTypes.string,
};
  
GcpProjectId.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  form: '',
  projectIdType: 'INCLUDE',
};

export default (withTranslation()(GcpProjectId));
