// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import NetworkProtocolDropdown from 'commonConnectedComponents/dropdown/NetworkProtocolDropdown';

export function NetworkProtocol(props) {
  const {
    showFilters, actions, t, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'networkProtocolType', 'INCLUDE'));
  }, [showFilters.cltNwProtocol]);

  if (!showFilters.cltNwProtocol) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('CLIENT_NETWORK_PROTOCOL')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('cltNwProtocol')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('cltNwProtocol')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>

      <Field
        id="cltNwProtocolDropdown"
        name="cltNwProtocolDropdown"
        component={NetworkProtocolDropdown} />
    </div>
  );
}

NetworkProtocol.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
};
  
NetworkProtocol.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(NetworkProtocol));
