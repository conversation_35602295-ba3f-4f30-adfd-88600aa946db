// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import ForwardingRuleDropdown from 'commonConnectedComponents/dropdown/ForwardingRuleDropdown';

export function ForwardingRule(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.ruleNames) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('FWD_RULE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ruleNames')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ruleNames')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="ruleNames"
        name="ruleNames"
        component={ForwardingRuleDropdown}
        onChange={null} />
    </div>
  );
}

ForwardingRule.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
ForwardingRule.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(ForwardingRule));
