// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';

import { EntityDropdown } from 'components/entityDropdown';
import { TRAFFIC_DIRECTION } from 'config';

function Types(props) {
  return (
    <EntityDropdown
      data={TRAFFIC_DIRECTION}
      {...props} />
  );
}

export function FwdTrafficDirection(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.fwTrafficDirection) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('FWD_TRAFFIC_DIRECTION')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('fwTrafficDirection')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('fwTrafficDirection')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="fwTrafficDirection"
        name="fwTrafficDirection"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

FwdTrafficDirection.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
FwdTrafficDirection.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(FwdTrafficDirection));
