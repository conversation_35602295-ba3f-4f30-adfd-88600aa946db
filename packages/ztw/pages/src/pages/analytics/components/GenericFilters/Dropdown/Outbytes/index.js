// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  isInteger,
  required,
} from 'utils/validations';

export function Outbytes(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.outbytesFrom || formSyncErrors.outbytesTo;
  const metaFrom = formMeta.outbytesFrom || {};
  const metaTo = formMeta.outbytesTo || {};
  const hasError = (metaFrom.touched || metaTo.touched) && !!error;

  if (!showFilters.outbytes) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="outbytesLabel"
          text={(
            <>
              {t('OUTBYTES')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ENTER_BELOW_VALUES')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('outbytes')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('outbytes')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div className="couple-filters">
        <Field
          name="outbytesFrom"
          id="outbytesFrom"
          component={Input}
          type="number"
          label={t('FROM')}
          validate={[
            isInteger,
            actions.validateOutbytesFrom,
            required,
          ]} />
      </div>
      <div className="couple-filters couple-filters-bottom">
        <Field
          name="outbytesTo"
          id="outbytesTo"
          component={Input}
          type="number"
          label={t('TO')}
          validate={[
            isInteger,
            actions.validateOutbytesTo,
            required,
          ]} />
      </div>
    </div>
  );
}

Outbytes.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
Outbytes.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(Outbytes));
