// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import ApplicationSegmentDropdown from 'commonConnectedComponents/dropdown/ApplicationSegmentDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function ApplicationSegment(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.zpaAppSegment) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Application Segment')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('zpaAppSegment')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('zpaAppSegment')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="zpaAppSegment"
        name="zpaAppSegment"
        component={ApplicationSegmentDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

ApplicationSegment.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
ApplicationSegment.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(ApplicationSegment));
