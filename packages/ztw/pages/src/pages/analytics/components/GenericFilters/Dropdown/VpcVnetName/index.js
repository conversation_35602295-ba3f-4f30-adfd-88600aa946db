// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  required,
} from 'utils/validations';

import MatchType from '../MatchType';

export function VpcVnetName(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.vpcVnetName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Vpc Vnet Name')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('vpcVnetName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('vpcVnetName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div>
        <div className="prefix">
          <Field
            id="vpcVnetNameMatchType"
            name="vpcVnetNameMatchType"
            component={MatchType}
            parse={(value) => value.id} />
        </div>
        <div className="suffix">
          <Field
            name="vpcVnetName"
            id="vpcVnetName"
            component={Input}
            validate={[
              required,
            ]} />
        </div>
      </div>
      
    </div>
  );
}

VpcVnetName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
VpcVnetName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(VpcVnetName));
