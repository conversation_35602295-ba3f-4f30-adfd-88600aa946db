// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Suffix from './Suffix';
import MatchType from '../MatchType';

export function EcName(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.ecName;
  const meta = formMeta.ecName || {};
  const hasError = meta.touched && !!error;

  if (!showFilters.ecName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="ecNameLabel"
          text={(
            <>
              {t('CLOUD_CONNECTOR_NAME')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ADD_CLOUD_CONNECTOR_NAME')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ecName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ecName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div>
        <div className="prefix">
          <Field
            id="ecNameMatchType"
            name="ecNameMatchType"
            component={MatchType}
            parse={(value) => value.id} />
        </div>
        <Suffix {...props} />
      </div>
    </div>
  );
}

EcName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
EcName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(EcName));
