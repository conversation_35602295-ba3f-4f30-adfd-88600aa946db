// @flow
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import AzureZoneDropdown from 'commonConnectedComponents/dropdown/AzureZoneDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AzureZone(props) {
  const {
    showFilters, actions, t, azureAvailabilityZoneType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'azureAvailabilityZoneType', 'INCLUDE'));
  }, [showFilters.azureAvailabilityZone]);
  
  if (!showFilters.azureAvailabilityZone) return null;
  
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('AZURE_AVAILABILITY_ZONE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('azureAvailabilityZone')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('azureAvailabilityZone')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="azureAvailabilityZoneType"
        name="azureAvailabilityZoneType"
        options={[{
          name: 'azureAvailabilityZoneType', value: 'INCLUDE', checked: azureAvailabilityZoneType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'locatioazureAvailabilityZoneTypenType', value: 'EXCLUDE', checked: azureAvailabilityZoneType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="azureAvailabilityZone"
        name="azureAvailabilityZone"
        component={AzureZoneDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AzureZone.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  azureAvailabilityZoneType: PropTypes.string,
};
  
AzureZone.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  azureAvailabilityZoneType: 'INCLUDE',
};

export default (withTranslation()(AzureZone));
