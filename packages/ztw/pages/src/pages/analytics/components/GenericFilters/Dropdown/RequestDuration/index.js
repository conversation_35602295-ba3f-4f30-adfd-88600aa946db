// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  isInteger,
  required,
} from 'utils/validations';

export function RequestDuration(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.requestDurationFrom || formSyncErrors.requestDurationTo;
  const metaFrom = formMeta.requestDurationFrom || {};
  const metaTo = formMeta.requestDurationTo || {};
  const hasError = (metaFrom.touched || metaTo.touched) && !!error;

  if (!showFilters.requestDuration) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="requestDurationLabel"
          text={(
            <>
              {t('REQ_DURATION')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ENTER_BELOW_VALUES')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('requestDuration')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('requestDuration')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div className="couple-filters">
        <Field
          name="requestDurationFrom"
          id="requestDurationFrom"
          component={Input}
          type="number"
          label={t('FROM')}
          validate={[
            isInteger,
            actions.validateRequestDurationFrom,
            required,
          ]} />
      </div>
      <div className="couple-filters couple-filters-bottom">
        <Field
          name="requestDurationTo"
          id="requestDurationTo"
          component={Input}
          type="number"
          label={t('TO')}
          validate={[
            isInteger,
            actions.validateRequestDurationTo,
            required,
          ]} />
      </div>
    </div>
  );
}

RequestDuration.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
RequestDuration.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(RequestDuration));
