// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  omit,
} from 'utils/lodash';
import { EntityDropdown } from 'components/entityDropdown';
// import { EC_TRAFFIC_TYPE } from 'config';
import DeviceOSTypes from '@zscaler/ec-domain/json/DeviceOSType.json';

function Types(props) {
  const enums = Object.keys(omit(DeviceOSTypes, 'ANY'))
    .sort()
    .map((key) => ({
      id: key,
      name: key,
    }));

  return (
    <EntityDropdown
      data={enums}
      {...props} />
  );
}

export function DeviceOsType(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.deviceOsType) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_DEVICE_OS_TYPE')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('deviceOsType')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('deviceOsType')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="deviceOsType"
        name="deviceOsType"
        component={Types}
        parse={(value) => value.id} />
    </div>
  );
}

DeviceOsType.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DeviceOsType.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DeviceOsType));
