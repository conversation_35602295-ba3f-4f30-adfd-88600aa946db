// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Suffix from './Suffix';
import MatchType from '../MatchType';

export function ClientDestinationName(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.clientDestName;
  const meta = formMeta.clientDestName || {};
  const hasError = meta.touched && !!error;

  if (!showFilters.clientDestName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="clientDestNameLabel"
          text={(
            <>
              {t('CLIENT_DEST_NAME')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ADD_CLOUD_CONNECTOR_NAME')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('clientDestName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('clientDestName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div>
        <div className="prefix">
          <Field
            id="clientDestinationNameMatchType"
            name="clientDestinationNameMatchType"
            component={MatchType}
            parse={(value) => value.id} />
        </div>
        <Suffix {...props} />
      </div>
    </div>
  );
}

ClientDestinationName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
ClientDestinationName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(ClientDestinationName));
