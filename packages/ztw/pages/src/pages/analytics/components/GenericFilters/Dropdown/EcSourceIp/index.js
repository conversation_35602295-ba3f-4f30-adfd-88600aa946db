// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  ipAddressesOrRanges,
  required,
} from 'utils/validations';

export function EcSourceIp(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.ecSourceIp;
  const meta = formMeta.ecSourceIp || {};
  const hasError = meta.touched && !!error;

  if (!showFilters.ecSourceIp) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="ecSourceIpLabel"
          text={(
            <>
              {t('CC_SOURCE_IP')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('IP_EXAMPLE_WITH_RANGE_CIDR')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ecSourceIp')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ecSourceIp')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        name="ecSourceIp"
        id="ecSourceIp"
        placeholder={t('IP_EXAMPLE_WITH_RANGE_CIDR')}
        component={Input}
        validate={[
          ipAddressesOrRanges,
          required,
        ]} />
    </div>
  );
}

EcSourceIp.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
EcSourceIp.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(EcSourceIp));
