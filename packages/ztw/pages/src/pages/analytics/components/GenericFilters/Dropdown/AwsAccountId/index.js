// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import AccountIdsDropdown from 'commonConnectedComponents/dropdown/AccountIdsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AwsAccountId(props) {
  const {
    showFilters, actions, t, accountIdType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'accountIdType', 'INCLUDE'));
  }, [showFilters.accountId]);

  if (!showFilters.accountId) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('ACCOUNT_ID')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('accountId')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('accountId')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="accountIdType"
        name="accountIdType"
        options={[{
          name: 'accountIdType', value: 'INCLUDE', checked: accountIdType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'accountIdType', value: 'EXCLUDE', checked: accountIdType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="accountId"
        name="accountId"
        component={AccountIdsDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AwsAccountId.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  accountIdType: PropTypes.string,
};
  
AwsAccountId.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  form: '',
  accountIdType: 'INCLUDE',
};

export default (withTranslation()(AwsAccountId));
