// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faQuestionCircle } from '@fortawesome/pro-solid-svg-icons';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import Input from 'components/Input';

import {
  isInteger,
  required,
} from 'utils/validations';

export function GatewayDestinationPort(props) {
  const {
    showFilters, actions, t, formSyncErrors, formMeta,
  } = props;
  const error = formSyncErrors.gatewayDestinationPortFrom
      || formSyncErrors.gatewayDestinationPortTo;
  const metaFrom = formMeta.gatewayDestinationPortFrom || {};
  const metaTo = formMeta.gatewayDestinationPortTo || {};
  const hasError = (metaFrom.touched || metaTo.touched) && !!error;

  if (!showFilters.gatewayDestinationPort) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <FormFieldLabel
          id="gatewayDestinationPortLabel"
          text={(
            <>
              {t('GATEWAY_DEST_PORT')}
              {' '}
              {hasError ? <FontAwesomeIcon icon={faQuestionCircle} /> : '' }
            </>
          )}
          styleClass={`filter-card-label ${hasError ? 'invalid' : ''}`}
          tooltip={t('PLEASE_ENTER_BELOW_VALUES')}
          error={hasError ? t(error) : null} />
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('gatewayDestinationPort')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('gatewayDestinationPort')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <div className="couple-filters">
        <Field
          name="gatewayDestinationPortFrom"
          id="gatewayDestinationPortFrom"
          component={Input}
          type="number"
          label={t('FROM')}
          // onKeyDown={actions.validatePortFrom}
          validate={[
            isInteger,
            actions.validatePortFrom,
            required,
          ]} />
      </div>
      <div className="couple-filters couple-filters-bottom">
        <Field
          name="gatewayDestinationPortTo"
          id="gatewayDestinationPortTo"
          component={Input}
          type="number"
          label={t('TO')}
          // onKeyDown={actions.validatePortTo}
          validate={[
            isInteger,
            actions.validatePortTo,
            required,
          ]} />
      </div>
    </div>
  );
}

GatewayDestinationPort.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
};
  
GatewayDestinationPort.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  formSyncErrors: {},
  formMeta: {},
};

export default (withTranslation()(GatewayDestinationPort));
