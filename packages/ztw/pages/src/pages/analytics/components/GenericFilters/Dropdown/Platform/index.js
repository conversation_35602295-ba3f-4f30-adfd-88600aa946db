// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import PlatformDropdown from 'commonConnectedComponents/dropdown/PlatformDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function Platform(props) {
  const {
    showFilters, actions, t, platformType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'platformType', 'INCLUDE'));
  }, [showFilters.platform]);

  if (!showFilters.platform) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('Platform')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('platform')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('platform')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="platformType"
        name="platformType"
        options={[{
          name: 'platformType', value: 'INCLUDE', checked: platformType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'platformType', value: 'EXCLUDE', checked: platformType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="platform"
        name="platform"
        component={PlatformDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Platform.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  platformType: PropTypes.string,
};
  
Platform.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  platformType: 'INCLUDE',
};

export default (withTranslation()(Platform));
