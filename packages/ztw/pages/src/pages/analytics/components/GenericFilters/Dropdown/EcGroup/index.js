// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import EcGroupDropdown from 'commonConnectedComponents/dropdown/EcGroupDropdownReportFilter';

const parseDropdownValues = (value) => get(value, 'original', value);

export function EcGroup(props) {
  const {
    showFilters, actions, t, ecGroupType, form,
  } = props;
  const dispatch = useDispatch();
  
  useEffect(() => {
    dispatch(change(form, 'ecGroupType', 'INCLUDE'));
  }, [showFilters.ecGroup]);
  
  if (!showFilters.ecGroup) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_GROUP')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ecGroup')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ecGroup')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="ecGroupType"
        name="ecGroupType"
        options={[{
          name: 'ecGroupType', value: 'INCLUDE', checked: ecGroupType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'ecGroupType', value: 'EXCLUDE', checked: ecGroupType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="ecGroup"
        name="ecGroup"
        component={EcGroupDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

EcGroup.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  ecGroupType: PropTypes.string,
};
  
EcGroup.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  ecGroupType: 'INCLUDE',
};

export default (withTranslation()(EcGroup));
