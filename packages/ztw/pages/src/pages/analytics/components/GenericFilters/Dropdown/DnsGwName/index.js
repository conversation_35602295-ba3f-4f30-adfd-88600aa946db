// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import DnsRuleDropdown from 'commonConnectedComponents/dropdown/DnsGwNameDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function DnsGwName(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.dnsGwName) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_DNS_GW_NAME')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('dnsGwName')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('dnsGwName')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="dnsGwName"
        name="dnsGwName"
        component={DnsRuleDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

DnsGwName.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
DnsGwName.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(DnsGwName));
