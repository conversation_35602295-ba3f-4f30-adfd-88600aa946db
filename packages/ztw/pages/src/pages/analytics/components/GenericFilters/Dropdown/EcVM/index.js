// @flow

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import ECRadioGroup from 'components/ecRadioGroup';
import { get } from 'utils/lodash';
import { Field, change } from 'redux-form';
import { withTranslation } from 'react-i18next';
import EcVmDropdown from 'commonConnectedComponents/dropdown/EcVmDropdownReportFilter';

const parseDropdownValues = (value) => get(value, 'original', value);

export function EcVM(props) {
  const {
    showFilters, actions, t, ecVmType, form,
  } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(change(form, 'ecVmType', 'INCLUDE'));
  }, [showFilters.ecVm]);

  if (!showFilters.ecVm) return null;

  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('EC_VM')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('ecVm')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('ecVm')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <ECRadioGroup
        id="ecVmType"
        name="ecVmType"
        options={[{
          name: 'ecVmType', value: 'INCLUDE', checked: ecVmType === 'INCLUDE', label: t('INCLUDE'),
        },
        {
          name: 'ecVmType', value: 'EXCLUDE', checked: ecVmType === 'EXCLUDE', label: t('EXCLUDE'),
        },
        ]} />
      <Field
        id="ecVm"
        name="ecVm"
        component={EcVmDropdown}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

EcVM.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  form: PropTypes.string,
  ecVmType: PropTypes.string,
};
  
EcVM.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  ecVmType: 'INCLUDE',
};

export default (withTranslation()(EcVM));
