// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';

import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import ForwardingMethodDropdown from 'commonConnectedComponents/dropdown/ForwardingMethodDropdown';

export function ForwardingMethod(props) {
  const { showFilters, actions, t } = props;

  if (!showFilters.forwardingTypes) return null;
  return (
    <div className="filter-container">
      <div className="filter-card-header">
        <div className="filter-card-label">
          <span>{t('FWD_METHOD')}</span>
        </div>
        <div
          className="filter-clear"
          onClick={() => actions.handleRemoveFilter('forwardingTypes')}
          role="button"
          aria-label="Remove Filter"
          tabIndex="0"
          onKeyPress={() => actions.handleRemoveFilter('forwardingTypes')}>
          <FontAwesomeIcon icon={faTimes} />
        </div>
      </div>
      <Field
        id="forwardingTypes"
        name="forwardingTypes"
        component={ForwardingMethodDropdown}
        onChange={null} />
    </div>
  );
}

ForwardingMethod.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
};
  
ForwardingMethod.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
};

export default (withTranslation()(ForwardingMethod));
