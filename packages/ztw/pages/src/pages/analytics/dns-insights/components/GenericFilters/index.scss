// @import 'scss/mixins.scss';
// @import "scss/colors.scss";

// .insights-filters-container {
//   padding: 15px;
//   height: 100%;
//   margin-bottom: 350px;
//   .tabPactive {
//     color: var(--semantic-color-surface-base-primary);
//     // z-index: 2;
//   }
//   .tabs-highlrighte {
//     background: var(--semantic-color-background-primary);
//     // z-index: 1;
//   }
//   label {
//     span {
//       margin-left: 0px;
//     }
//   }
//   .header{
//     padding-bottom: 2em;
//   }
//   .title{
//     height: 19px;
//     width: 37px;
//     color: var(--semantic-color-surface-base-primary);
//     font-size: 16px;
//     font-weight: 500;
//     letter-spacing: 0;
//     line-height: 19px;
//     float: left;
//   }
//   .clear {
//     height: 16px;
//     color: #4FB4FC;
//     font-size: 12px;
//     letter-spacing: 0;
//     line-height: 16px;
//     white-space: nowrap;
//     cursor: pointer;
//     float: right;
//   }
//   .refresh-text{
//     padding-left: 0.75em;
//   }
//   .filter-box {
//     min-height: 100px;
//     padding: 1em 0em 1em 0em;
//     white-space: nowrap;
//   }
//   .side-header{
//     padding-bottom: 2em;
//   }
//   .filter-sideheader {
//     height: 16px;
//     // width: 62px;
//     color: #949494;
//     font-size: 13px;
//     letter-spacing: 0;
//     line-height: 15px;
//     float: left;
//   }
//   .filter-card-small {
//     height: 49px;
//     width: 320px;
//     border-radius: 5px;
//     background-color: var(--semantic-color-surface-base-primary);
//     margin-top: 2em;
//     .radio-button-container.radio-group-container {
//       padding: 0 15px;
//       height: 100%;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//     }
//     .react-select__menu {
//       height: 28em;
//       text-align: left;
//       .react-select__menu-list {
//         min-height: 20em;
//         overflow: visible;
//       }
//     }
//     .single {
//       width: 100%;
//     }
//     .drop-down-container{
//       width: 95%
//     }
//     .drop-down-selected-value{
//       border: 0;
//       border-radius: 2px;
//       border-bottom: 2px solid $blue2;
//       margin: 2px 1.5px 0   1.5px;
//     }
//     .radio-button-container .radio-buttons .radio-button label{
//      padding: 4px 15px 4px 0;
//     }
//     .check-circle {
//       padding-left: 6px;
//     }
//     .select-item {
//       width: 250px
//       margin-left: 10px;
//       .css-1szy77t-control {
//         border-left: 0;
//         border-right: 0;
//         border-top: 0;
//         border-radius: 0;
//         box-shadow: none;
//        &:hover {
//         box-shadow: none;
//         }
//       }
//     }
//   }
//   .expander {
//     max-height: 100%;
//   }
//   .filter-card-large {
//     // max-height: 55em;
//     width: 100%;
//     border-radius: 5px;
//     background-color: var(--semantic-color-surface-base-primary);
//     .drop-down-container{
//       width: 94%
//     }
//     .drop-down-selected-value{
//       border: 0;
//       border-radius: 2px;
//       border-bottom: 2px solid $blue2;
//       margin: 2px 1.5px 0   1.5px;
//     }
//     .filter-container{
//       .select-item {
//         width: 250px
//         margin-left: 10px;
//       }
//       padding: 5px 0  5px 0;
//       .filter-card-header{
//         padding-bottom: 2em;
//       }
//       .filter-card-label{
//         height: 16px;
//         color: #1E1F22;
//         font-weight: 500;
//         font-size: 13px;
//         letter-spacing: 0;
//         line-height: 15px;
//         float: left;
//         padding: 10px;
//         &.invalid {
//           color: var(--semantic-color-content-status-danger-primary);
//         }
//       }
//       .filter-clear{
//         height: 16px;
//         color:  var(--semantic-color-content-base-primary);
//         font-size: 12px;
//         letter-spacing: 0;
//         line-height: 16px;
//         white-space: nowrap;
//         padding: 10px 20px 0  0;
//         float: right;
//         cursor: pointer;
//       }
//       .couple-filters{
//         padding-top: 10px;
//         background-color: #F3F4F3;
//       }
//       .couple-filters-bottom{
//         padding-bottom: 20px;
//       }
//       .dropdown{
//         width: 90%;
//         margin-left: 10px;
//       }
//       .input-label {
//         margin-top: 10px;
//         text-align: left;
//         font-size: 13px;
//         font-weight: 500;
//         letter-spacing: 0;
//         line-height: 16px;
//       }
//       .input-container input{
//         background-color: #F3F4F3;
//       }
//       // added for AddFilter Dropdown
//       .drop-down-container {
//         .drop-down-list li { 
//           border-radius: 0px;
//           button { text-align: left; }
//         }
//         .dropdown-search {
//           height: 32px;
//           // width: 18em;
//           border-radius: 8px;
//           background-color: #F1F2F3;
//           margin: 10px;
//           display: flex;
//           max-width: 85%;
//           margin-left: auto;
//           margin-right: auto;
//         }
//         .dropdown-search-text {
//           border: none;
//           border-radius: 8px;
//           background: inherit;
//           width: 85%;
//           padding: 10px 10px 10px 10px;
//           border-radius: 8px;
//         }
//         .clear-button {
//           width: auto;
//         }
//         .action-buttons{
//           width: 10px;
//         }
//       }
     
//       .drop-down-container ul.drop-down-list.open {
//         visibility: visible;
//         opacity: 1;
//         .items-container {
//           max-height: 300px;
//           overflow-x: hidden;
//           overflow-y: auto;
//           width: 100%;
//         }
//       }
//       .prefix {
//         float: left;
//         .select-item{
//           width: 100px;
//           margin-left: 10px;
//         }
//         .react-select__menu {
//           width: 14em;
//         }
//       }
//       .suffix {
//         .input-container {
//           padding: 0.6em 0.5em 0em 0.1em;
//           width: 11em;
//         }
//         .empty-container {
//           padding: 0.6em 0.5em 0em 0.1em;
//           width: 11em;
//           height: 3em;
//           border: none;
//         }
//       }
//     }
//     .entity-dropdown-container{
//       .select-item{
//         width: 90%;
//         margin-left: 12px;
//         // remove outline for react-select
//         // none of the below css works - need to fix
//         .css-1szy77t-control :hover{
//           border-color: var(--semantic-color-surface-base-primary);
//           outline: 0 !important;
//         }
//         .react-select__control{
//           outline: 0 !important;
//         }
//         // react-select__control--is-focused
//         .react-select__control--menu-is-open{
//           outline: 0 !important;
//           // border-color: var(--semantic-color-surface-base-primary);
//         }
//         .react-select__control--is-focused{
//           outline: 0 !important;
//           }
//         // till here
//       }

//     }
//   }
//   .apply-filters{
//     width: 20.5em;
//     margin-top: 0.25em;
//   }

// }
