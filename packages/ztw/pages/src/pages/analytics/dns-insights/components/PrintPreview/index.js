// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons';
import html2canvas from 'html2canvas';
import JSpdf from 'jspdf';
import * as DnsInsightsSelectors from 'ducks/dnsInsights/selectors';
import {
  togglePrintPreview,
  getPreselectedFilters, updateMenu,
} from 'ducks/dnsInsights';
import PageHeader from './PageHeader';
import PageFooter from './PageFooter';
import PrintWidget from '../PrintWidget';
import './index.scss';

export class PrintPreview extends Component {
  static propTypes = {
    showPreview: PropTypes.bool,
    t: PropTypes.func,
    actions: PropTypes.shape(),
    history: PropTypes.arrayOf(PropTypes.shape({})),
  };

  static defaultProps = {
    showPreview: false,
    t: (str) => str,
    actions: {},
    history: [],
  };

  handleExportPDF = () => {
    const pdfFilename = 'Zscaler-Insights.pdf';
    const input = document.getElementById('print');

    html2canvas(input)
      .then((canvas) => {
        const canvasAspectRatio = canvas.width / canvas.height;
        const imgData = canvas.toDataURL('image/png');

        const pdf = new JSpdf('p', 'mm', 'a4'); // portrait mode, measured in millimeters, A4 size
        const imageWidth = 210 - 20; // std. A4 size width = 210mm, minus 20 for margin
        const imageHeight = imageWidth / canvasAspectRatio;

        pdf.addImage(imgData, 'PNG', 10, 10, imageWidth, imageHeight);
        pdf.save(pdfFilename);
      });
  };

  render() {
    const {
      showPreview, t, actions, history,
    } = this.props;

    if (!showPreview) {
      return null;
    }

    return (
      <div className="print" style={showPreview ? { display: 'block' } : { display: 'none' }}>
        <div className="print-view-button-wrapper">
          <div className="print-view-button-container" style={{ height: '60px', paddingTop: '16px', paddingBottom: '16px' }}>
            <div className="exit-print-view print-column">
              <div className="control-button-reports" id="exit-print-view">
                <FontAwesomeIcon
                  className="far title-back"
                  icon={faArrowLeft}
                  size="lg"
                  onClick={() => actions.togglePrintPreview(false)}
                  onKeyPress={() => actions.togglePrintPreview(false)}
                  role="button"
                  aria-label="Print Preview"
                  tabIndex="0" />
                <span className="back">
                  {t('GO_BACK')}
                </span>
              </div>
            </div>
            <div className="print-column">
              <span className="print-view-title">{t('PRINT_VIEW')}</span>
            </div>
            <div className="print-view-btn print-column">
              <button type="button" className="submit" onClick={() => this.handleExportPDF()}>{t('PRINT')}</button>
            </div>
          </div>
        </div>
        <div className="print-page-container" id="print" style={{ paddingTop: '61px' }}>
          {history && (history.map((card, idx) => (
            // eslint-disable-next-line react/no-array-index-key
            <div className="print-page" key={idx}>
              <PageHeader />
              <div className="print-view-content">
                <div className="print-view-information">
                  <div className="print-view-information-panel fullwidth">
                    <div className="print-view-filters">
                      <div className="print-report-filter-label">
                        {t('DATA_TYPE')}
                      </div>
                      <div className="print-report-filter-label-value">
                        {card.dataType ? t(card.dataType) : ''}
                      </div>
                    </div>
                    <div className="print-view-filters">
                      <div className="print-report-filter-label">
                        {t('UNITS')}
                      </div>
                      <div className="print-report-filter-label-value">
                        {t(card.units)}
                      </div>
                    </div>
                    <div className="print-view-filters print-view-filter-columns TIME_RANGE">
                      <div className="print-report-filter-label">
                        {t('TIME_FRAME')}
                      </div>
                      <div className="print-report-filter-label-value">
                        {card.formFiltersValues.timeFrame.label === 'CUSTOM'
                          ? `${t(card.formFiltersValues.timeFrame.label)} [${card.formFiltersValues.timeFrame.startDate.format('MMM DD h:mm A')} - ${card.formFiltersValues.timeFrame.endDate.format('MMM DD h:mm A')}]`
                          : t(card.formFiltersValues.timeFrame.label)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="no-pointers">
                <PrintWidget history={history} printIdx={idx} />
              </div>
              <PageFooter />
            </div>
          )))}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...DnsInsightsSelectors.default(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    togglePrintPreview,
    getPreselectedFilters,
    updateMenu,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PrintPreview));
