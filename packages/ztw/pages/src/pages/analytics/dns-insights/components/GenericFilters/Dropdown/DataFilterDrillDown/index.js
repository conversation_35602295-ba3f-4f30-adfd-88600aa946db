/* eslint-disable react/jsx-handler-names */
// @flow

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import {
  reduxForm,
} from 'redux-form';
import ServerError from 'components/errors/ServerError';
import ListDropdownWithSearchInsights from 'components/dropDown/ListDropdownWithSearchInsights';
import * as DnsInsightsSelectors from 'ducks/dnsInsights/selectors';
import { withTranslation } from 'react-i18next';
import {
  applyDataType,
  drilldownSessionLogs,
  getDataTypeFilters,
  setDataTypeSearchString,
  handleSelectedDataTypeFilter,
} from 'ducks/dnsInsights';

const localize = (data, t) => {
  const localizedData = data;
  localizedData.label = t(localizedData.label);
  return localizedData;
};

export class DataFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions: { load } } = this.props;
    load();
  }

  render() {
    const {
      dataType, dataTypeFilters, defaultDataTypeValue, currentDataTypeFilters, actions, t, filter,
    } = this.props;

    return (
      <ServerError {...this.props} size="small">
        <form onSubmit={actions.applyDataType}>
          <ListDropdownWithSearchInsights
            {...this.props}
            items={dataTypeFilters.filter((x) => x.value !== dataType)}
            currentItems={currentDataTypeFilters}
            setValue={(str) => str}
            defaultValue={localize(defaultDataTypeValue, t)}
            selectedValue={filter}
            searchParamName="dataTypeFiltersSearch"
            setSearchString={actions.setSearchString}
            onClickCb={async (e) => {
              await actions.drilldownSessionLogs(filter);
              await actions.handleSelectedDataTypeFilter(e);
            }}
            searchString="" />
        </form>
      </ServerError>
    );
  }
}

DataFiltersForm.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
  }),
  dataType: PropTypes.string,
  defaultDataTypeValue: PropTypes.shape({}),
  dataTypeFilters: PropTypes.arrayOf(PropTypes.shape()),
  currentDataTypeFilters: PropTypes.arrayOf(PropTypes.shape()),
  loading: PropTypes.bool,
  t: PropTypes.func,
  filter: PropTypes.string,
};

DataFiltersForm.defaultProps = {
  actions: {
    load: noop,
  },
  dataType: '',
  defaultDataTypeValue: {},
  dataTypeFilters: [],
  currentDataTypeFilters: [],
  loading: false,
  t: (str) => str,
  filter: '',
};

const mapStateToProps = (state) => ({
  ...DnsInsightsSelectors.default(state),
  // filterFormValues: getFormValues('tunnelInsightsFiltersForm')(state),
  // defaultDataTypeValue: TunnelInsightsSelectors.defaultDataTypValueSelector(state),
  dataTypeFilters: DnsInsightsSelectors.currentDataTypeDropdownSelector(state),
  initialValues: {},
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    applyDataType,
    drilldownSessionLogs,
    load: getDataTypeFilters,
    setSearchString: setDataTypeSearchString,
    handleSelectedDataTypeFilter,
  }, dispatch);

  return {
    actions,
  };
};

const DataFilterDrillDown = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'tunnelInsightsFiltersForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {},
  asyncBlurFields: [],
})(withTranslation()(DataFiltersForm)));

export default DataFilterDrillDown;
