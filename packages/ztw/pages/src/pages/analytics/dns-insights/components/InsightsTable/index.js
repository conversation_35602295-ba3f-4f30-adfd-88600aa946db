import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import * as DnsInsightsSelectors from 'ducks/dnsInsights/selectors';
import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';
import RowActions from 'components/tablePro/RowActions';
import { updateMenu } from 'ducks/dnsInsights';
import { isEmpty } from 'utils/lodash';
import './index.scss';

class InsightsTable extends Component {
  static propTypes = {
    reportData: PropTypes.arrayOf(PropTypes.shape()),
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    showForm: PropTypes.bool,
    columns: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
    DrillDownAction: PropTypes.shape({}),
  };

  static defaultProps = {
    reportData: [],
    actions: {
      load: null,
    },
    showForm: null,
    columns: [],
    t: (str) => str,
    DrillDownAction: null,
  };

  state = {
    showComponent: false,
  };

  handleOnMouseOverCb = () => this.setState(() => ({ showComponent: true }));
  
  onLeave = () => {
    this.setState({ showComponent: false });
  };

  getColumns = () => {
    const {
      columns,
      actions,
      t,
    } = this.props;
    const {
      editRowHandler,
    } = actions;

    columns.map((i) => {
      // eslint-disable-next-line no-param-reassign
      i.name = t(i.name);
      return i;
    });
    const lastColumn = columns[columns.length - 1];
    const { showComponent } = this.state;
    const lastColWithElipsis = {
      ...lastColumn,
      visible: true,
      frozen: false,
      width: 40,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          handleLeave={this.onLeave}
          showComponent={showComponent}
          onMouseOverCb={this.handleOnMouseOverCb} />
      ),
      formatter: <RowActions showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    columns[columns.length - 1] = lastColWithElipsis;
    return [...columns];
  };

  render() {
    const {
      // showAccordion,
      reportData,
      columns,
      actions,
      DrillDownAction,
    } = this.props;

    const { showComponent } = this.state;
    // const columns = this.getColumns();
    if (isEmpty(reportData)) return <GenericErrorMessage {...this.props} />;
    
    return (
      <Loading {...this.props}>
        <div
          className="insights-layout-header">
          <TableWrapper
            initialRows={reportData}
            initialColumns={columns}
            height={525}
            DrillDownAction={DrillDownAction}
            updateMenu={actions.updateMenu} />
          <CustomizeColumns
            initialItems={columns}
            handleLeave={this.onLeave}
            showComponent={showComponent}
            dragnDrop={actions.updateMenu} />
        </div>
      </Loading>
    );
  }
}

const mapStateToProps = (state) => ({ ...DnsInsightsSelectors.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(InsightsTable));
