/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as DnsInsightsSelectors from 'ducks/dnsInsights/selectors';
import { updateMenu, restoreHistory, removeHistoryCard } from 'ducks/dnsInsights';
import Slider from 'react-slick';
import ServerError from 'components/errors/ServerError';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronLeft, faChartBar, faChartPie, faChartLine, faTable, faTimes,
} from '@fortawesome/pro-solid-svg-icons';

import './index.scss';

function NextArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div className="slick-arrow">
      {/* <div style={{ position: 'absolute' }}>
        <FontAwesomeIcon className="fad fa-stack-2x arrows" icon={faChevronRight} size="lg" />
      </div> */}
      <div
        className={className}
        // className="slick-arrow slick-next slick-disabled"
        // style={{
        //   ...style, display: 'block', background: '#E3E5E6', height: '98.45px', paddingTop: '40px', paddingRight: '20px',
        // }}
        onClick={onClick}>
      </div>
    </div>
  );
}

function PrevArrow(props) {
  const { className, style, onClick } = props;
  return (
    <div className="slick-arrow">
      {/* <div style={{ position: 'absolute' }}>
        <FontAwesomeIcon className="fad fa-stack-2x arrows" icon={faChevronLeft} size="lg" />
      </div> */}
      <div
        className={className}
        // className="slick-arrow slick-prev slick-disabled"
        // style={{
        //   ...style, display: 'block', background: '#E3E5E6', height: '98.45px', paddingTop: '40px', paddingLeft: '10px',
        // }}
        onClick={onClick}>
        <FontAwesomeIcon className="fad fa-stack-2x arrows" icon={faChevronLeft} size="lg" />
      </div>
    </div>
  );
}

const getChartIcon = (name) => {
  switch (true) {
  case name === 'bar':
    return faChartBar;
  case name === 'pie':
    return faChartPie;
  case name === 'line':
    return faChartLine;
  case name === 'table':
    return faTable;
  default:
    return <GenericErrorMessage />;
  }
};

export function History(props) {
  const {
    history, actions,
  } = props;
  
  const settings = {
    className: 'center',
    infinite: false,
    centerPadding: '60px',
    slidesToShow: 9,
    swipeToSlide: true,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    afterChange(index) {
      // eslint-disable-next-line no-console
      console.log(
        `Slider Changed to: ${index + 1}, background: #222; color: #bada55`,
      );
    },
  };
  return (
    <ServerError {...props}>
      <div className="history">
        <Slider {...settings}>
          {history && history.map((card, idx) => (
            <div
              key={card.chart}>
              <div>
                <div className={`element history-element history-thumbnail-element ${card.current ? 'history-thumbnail-selected' : ''}`}>
                  <div className={`history-thumbnail-element-header ${card.current ? 'history-thumbnail-selected' : ''}`}>
                    <span className={`history-thumbnail-element-header-number ${card.current ? 'history-thumbnail-selected' : ''}`}>{idx + 1}</span>
                    <div
                      className={`history-thumbnail-element-header-delete fa filter-clear ${card.current ? 'history-thumbnail-selected' : ''}`}
                      onClick={() => actions.removeHistoryCard(idx)}
                      aria-label="Remove Card"
                      role="button"
                      tabIndex="0"
                      onKeyPress={() => actions.removeHistoryCard(idx)}>
                      <FontAwesomeIcon icon={faTimes} />
                    </div>
                  </div>
                  <div
                    className={`history-thumbnail-element-body ${card.current ? 'history-thumbnail-selected' : ''}`}
                    onClick={() => actions.restoreHistory(idx)}
                    aria-label="Restore History"
                    role="button"
                    tabIndex="0"
                    onKeyPress={() => actions.restoreHistory(idx)}>
                    <FontAwesomeIcon
                      className={`fad fa-stack-1x element-icon history-thumbnail-element-icon insights-chart-icon ${card.current ? 'history-thumbnail-selected' : ''}`}
                      icon={getChartIcon(card.chart)}
                      // icon={faChartLine}
                      size="lg" />
                    <i className={`history-thumbnail-element-icon fa insights-chart-icon ${card.current ? 'history-thumbnail-selected' : ''}`}></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </ServerError>
  );
}

NextArrow.propTypes = {
  className: PropTypes.string,
  style: PropTypes.shape({}),
  onClick: PropTypes.func,
};
NextArrow.defaultProps = {
  className: '',
  style: {},
  onClick: (t) => t,
};

PrevArrow.propTypes = {
  className: PropTypes.string,
  style: PropTypes.shape({}),
  onClick: PropTypes.func,
};
PrevArrow.defaultProps = {
  className: '',
  style: {},
  onClick: (t) => t,
};

History.propTypes = {
  t: PropTypes.func,
  history: PropTypes.arrayOf(PropTypes.shape({})),
  actions: PropTypes.shape({}),
};
  
History.defaultProps = {
  t: (str) => str,
  history: [],
  actions: {},
};

const mapStateToProps = (state) => ({ ...DnsInsightsSelectors.default(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    restoreHistory,
    removeHistoryCard,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(History));
