@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.insights-container {
  background: var(--semantic-color-background-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  margin-top: 1em;

  .container-row{
    margin: 30px 0;
  }
  
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}

