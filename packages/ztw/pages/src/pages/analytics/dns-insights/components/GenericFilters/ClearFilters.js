import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { withTranslation } from 'react-i18next';
import './index.scss';

function ClearFilters(props) {
  const { showFilters, actions, t } = props;
  const isSelected = Object.values(showFilters).find((i) => i);
  if (!isSelected) return null;
  return (
    <div
      className="clear"
      onClick={actions.handleClearFilters}
      role="button"
      aria-label={t('CLEAR_FILTERS')}
      tabIndex="0"
      onKeyPress={actions.handleClearFilters}>
      <FontAwesomeIcon icon={faTimes} />
      <span className="refresh-text">{t('CLEAR_FILTERS')}</span>
    </div>
  );
}

ClearFilters.propTypes = {
  showFilters: PropTypes.shape(),
  actions: PropTypes.shape(),
  t: PropTypes.func,
};
  
ClearFilters.defaultProps = {
  showFilters: {},
  actions: {},
  t: (str) => str,
};

export default withTranslation()(ClearFilters);
