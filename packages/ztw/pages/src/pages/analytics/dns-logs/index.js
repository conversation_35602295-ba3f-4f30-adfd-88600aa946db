import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import moment from 'moment-timezone';
import ReactTooltip from 'react-tooltip';
import PageLoader from 'components/spinner/PageLoader';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import ApplyFilterFirst from 'components/infoApplyFilterFirst';
import TotalTransactionsInsights from 'components/infoTotalTransactionsInsights';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as DNSLogsSelectors from 'ducks/dnsLogs/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { faAngleLeft, faAngleRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import ClearSearchAndSort from 'components/button/ClearSearchAndSort';
import {
  loader,
  // getDNSLogsData,
  toggleAccordion,
  getPreselectedFilters,
  handleNoOfRecords,
  applyFilters,
  updateMenu,
  clearData,
  handleCancelAPI,
  handleOnResetFilter,
} from 'ducks/dnsLogs';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { DNSLogsTable, GenericFilters } from './components';

import './index.scss';

export class DNSLogs extends Component {
  static propTypes = {
    t: PropTypes.func,
    dnslogstabledata: PropTypes.arrayOf(PropTypes.shape()),
    load: PropTypes.func,
    showAccordion: PropTypes.bool,
    handleAccordion: PropTypes.func,
    removeData: PropTypes.func,
    actions: PropTypes.shape(),
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    progress: PropTypes.shape({}),
    timeFrame: PropTypes.shape({}),
    searchData: PropTypes.string,
    isSorted: PropTypes.bool,
    onhandleOnResetFilter: PropTypes.func,
    downloadCSV: PropTypes.string,
  };

  static defaultProps = {
    t: (str) => str,
    dnslogstabledata: null,
    load: null,
    showAccordion: false,
    handleAccordion: null,
    removeData: null,
    actions: {},
    accessPermissions: {},
    progress: {},
    timeFrame: {},
    accessSubscriptions: [],
    searchData: '',
    isSorted: false,
    onhandleOnResetFilter: null,
    downloadCSV: '',
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ANALYTICS_LOGS,
    });
    // const { load, accessPermissions } = this.props;
    // if (accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') {
    //   return null;
    // }
    // return load();
  }

  componentWillUnmount() {
    const { removeData } = this.props;
    removeData();
  }

  render() {
    const {
      t,
      dnslogstabledata,
      showAccordion,
      handleAccordion,
      accessPermissions,
      accessSubscriptions,
      progress,
      timeFrame,
      searchData,
      isSorted,
      onhandleOnResetFilter,
      downloadCSV,
    } = this.props;

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ANALYTICS_LOGS} />
        <div className="accordion-container">
          <div className="outer-layer">
            <div
              className={`slide ${showAccordion ? 'filter-expand' : 'filter'}`}>
              <div className="filter-expand-width">
                <GenericFilters {...this.props} />
              </div>
            </div>
            <div className={`${showAccordion ? 'filter-expand-space ' : 'hidden'}`} />
            <div
              className="knob-holder"
              onClick={() => handleAccordion()}
              onKeyPress={() => handleAccordion()}
              type="button"
              role="presentation">
              <FontAwesomeIcon
                className={`fad fa-stack-1x knob ${
                  showAccordion ? 'hide' : ''
                }`}
                icon={faAngleRight}
                size="lg" />
              <FontAwesomeIcon
                className={`fad fa-stack-1x knob ${
                  showAccordion ? '' : 'hide'
                }`}
                icon={faAngleLeft}
                size="lg" />
            </div>
            <div
              className={`slide ${
                showAccordion ? 'tablesec-shrink' : 'tablesec'
              }`}>
              <div className="main-container">
                <div className="header">
                  <span className="component-header">{t('DNSLOG')}</span>
                  {!isEmpty(progress) && progress.status === 'COMPLETE' && (
                    <div>
                      <TotalTransactionsInsights
                        startDate={moment(timeFrame.startTime).format('MMM. DD, YYYY hh:mm:ss A')}
                        endDate={moment(timeFrame.endTime).format('MMM. DD, YYYY hh:mm:ss A')}
                        recordLength={progress.progressItemsComplete} />
                    </div>
                  )}
                  <div className="download">
                    <ClearSearchAndSort
                      tooltip={t('CLEAR_SEARCH_AND_SORT')}
                      onActionCb={() => onhandleOnResetFilter()}
                      isVisible={(searchData || isSorted)} />
                    <ReactTooltip
                      id="TunnelLogsReactTooltip"
                      clickable
                      place="left"
                      type="light"
                      offset={{ top: -10 }}
                      effect="solid"
                      border
                      borderColor="#939393"
                      className="logs-insights-tooltip-container" />
                  </div>
                </div>
                <div className="table-wrapper">
                  <PageLoader {...this.props}>
                    <ServerError {...this.props}>
                      {downloadCSV === 'DOWNLOAD' || isEmpty(progress) ? <ApplyFilterFirst /> : (
                        <DNSLogsTable
                          tableData={dnslogstabledata}
                          {...this.props} />
                      )}
                    </ServerError>
                  </PageLoader>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  ...DNSLogsSelectors.baseSelector(state),
  ...DNSLogsSelectors.formValuesSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators(
    {
      load: loader,
      handleAccordion: toggleAccordion,
      getPreselectedFilters,
      handleNoOfRecords,
      applyFilters,
      updateMenu,
      removeData: clearData,
      onCancel: handleCancelAPI,
      onhandleOnResetFilter: handleOnResetFilter,
    },
    dispatch,
  );
  return actions;
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(DNSLogs));
