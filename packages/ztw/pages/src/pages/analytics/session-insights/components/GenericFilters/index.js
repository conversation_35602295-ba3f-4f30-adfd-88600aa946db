/* eslint-disable react/jsx-handler-names */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRedo } from '@fortawesome/pro-solid-svg-icons';
import { faChartLine, faFilter, faChartMixed } from '@fortawesome/pro-light-svg-icons';
import {
  Field,
  getFormValues,
  reduxForm,
} from 'redux-form';
import NavTabs from 'components/navTabs';
import { BASE_LAYOUT } from 'config';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';
import * as SessionInsightsSelectors from 'ducks/sessionInsights/selectors';
import {
  handleStartOver, removeHistoryCard, handleChartSelection,
  getPreselectedFilters, handleUnits,
  handleClearFilters, handleRemoveFilter, handleSelectedAddFilter, applyFilters,
} from 'ducks/sessionInsights';

import {
  AddFilter,
  AwsAccountId,
  AwsRegion,
  AwsZone,
  AzureRegion,
  AzureSubscriptionId,
  AzureZone,
  Direction,
  EcGroup,
  EcInstance,
  EcVM,
  ForwardingMethod,
  ForwardingRule,
  FwdTrafficDirection,
  GcpProjectId,
  GcpRegion,
  GcpZone,
  Location,
  Platform,
  TimeFrame,
  TrafficType,
} from './Dropdown';
import ClearFilters from './ClearFilters';

import './index.scss';

class GenericFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions, initialValues } = this.props;
    actions.getPreselectedFilters(initialValues);
  }

  render() {
    const {
      showAccordion,
      actions,
      units,
      chartType,
      t,
    } = this.props;

    if (!showAccordion) return null;

    return (
      <div className="expander">
        <div className="insights-filters-container">
          <form onSubmit={actions.applyFilters}>
            {/* Header */}
            <div className="header">
              <div className="title">
                <span>
                  <NavTabs
                    tabConfiguration={[
                      {
                        id: 'session-insights',
                        title: t('INSIGHTS'),
                        to: `${BASE_LAYOUT}/analytics/sessioninsights`,
                      },
                      {
                        id: 'session-logs',
                        title: t('LOGS'),
                        to: `${BASE_LAYOUT}/analytics/sessionlogs`,
                      }]} />
                </span>
              </div>
            </div>
            <div className="separator-line-header" />
            {/* TimeFrame */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <span>{t('TIME_FRAME')}</span>
              </div>
              <div className="filter-card-small">
                <Field
                  id="timeFrame"
                  name="timeFrame"
                  component={TimeFrame}
                  parse={(value) => value.id} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Records Displayed */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <FontAwesomeIcon icon={faChartLine} />
                {' '}
                <span>{t('SELECT_CHART_TYPE')}</span>
              </div>
              <div className="filter-card-small">
                <ECRadioGroup
                  id="chartType"
                  name="chartType"
                  styleClass="full-width"
                  onChange={(e, value) => actions.handleChartSelection(value)}
                  charts
                  options={[{
                    name: 'chartType', value: 'bar', checked: chartType === 'bar', label: 'bar',
                  },
                  {
                    name: 'chartType', value: 'pie', checked: chartType === 'pie', label: 'pie',
                  },
                  {
                    name: 'chartType', value: 'line', checked: chartType === 'line', label: 'line',
                  },
                  {
                    name: 'chartType', value: 'table', checked: chartType === 'table', label: 'table',
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* UNITS */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <FontAwesomeIcon icon={faChartMixed} />
                {' '}
                <span>{t('UNITS')}</span>
              </div>
              <div className="filter-card-small">
                <ECRadioGroup
                  id="units"
                  name="units"
                  styleClass="filter-card-small-units full-width"
                  onChange={actions.handleUnits}
                  options={[{
                    name: 'units', value: 'SESSIONS', checked: units === 'SESSIONS', label: t('SESSIONS'),
                  },
                  {
                    name: 'units', value: 'BYTES', checked: units === 'BYTES', label: t('BYTES'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* Select Filters */}
            <div className="filter-box">
              <div className="side-header">
                <div className="filter-sideheader">
                  <FontAwesomeIcon icon={faFilter} />
                  {' '}
                  <span>{t('FILTERS')}</span>
                </div>
                <div className="filter-card-large">
                  <div className="filter-container">
                    <Field
                      id="addFilter"
                      name="addFilter"
                      component={AddFilter}
                      show />
                  </div>
                </div>
              </div>
              <div className="filter-card-large">
                <AwsAccountId {...this.props} />
                <AwsZone {...this.props} />
                <AwsRegion {...this.props} />
                <AzureZone {...this.props} />
                <AzureRegion {...this.props} />
                <AzureSubscriptionId {...this.props} />
                <EcGroup {...this.props} />
                <EcInstance {...this.props} />
                <EcVM {...this.props} />
                <ForwardingRule {...this.props} />
                <ForwardingMethod {...this.props} />
                <GcpZone {...this.props} />
                <GcpProjectId {...this.props} />
                <GcpRegion {...this.props} />
                <Location {...this.props} />
                <Platform {...this.props} />
                <Direction {...this.props} />
                <FwdTrafficDirection {...this.props} />
                <TrafficType {...this.props} />
              </div>
            </div>
            {/* Apply */}
            <div className="external-id-buttons apply-filters">
              <button
                type="button"
                className="primary-button"
                onClick={actions.applyFilters}>
                {t('APPLY_FILTERS')}
              </button>
              <button
                type="button"
                onClick={actions.handleStartOver}
                className="secondary-button">
                <FontAwesomeIcon icon={faRedo} />
                <span className="refresh-text">{t('RESET')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }
}

GenericFiltersForm.propTypes = {
  showAccordion: PropTypes.bool,
  actions: PropTypes.shape({}),
  units: PropTypes.string,
  chartType: PropTypes.string,
  initialValues: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  filterFormValues: PropTypes.shape(),
  t: PropTypes.func,
};

GenericFiltersForm.defaultProps = {
  showAccordion: false,
  actions: {},
  units: '',
  chartType: '',
  initialValues: {},
  showFilters: {},
  filterFormValues: {},
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...SessionInsightsSelectors.default(state),
  filterFormValues: getFormValues('sessionInsightsFiltersForm')(state),
  filters: SessionInsightsSelectors.addFilterDropdownSelector(state),
  initialValues: {
    timeFrame: { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
    chartType: 'line',
    units: 'SESSIONS',
  },
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleStartOver,
    getPreselectedFilters,
    removeHistoryCard,
    handleChartSelection,
    handleUnits,
    handleClearFilters,
    handleRemoveFilter,
    handleSelectedAddFilter,
    applyFilters,
  }, dispatch);

  return {
    actions,
  };
};

const GenericFilters = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'sessionInsightsFiltersForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {
    timeFrame: { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
    chartType: 'line',
    units: 'SESSIONS',
  },
  asyncBlurFields: [],
})(withTranslation()(GenericFiltersForm)));

export default GenericFilters;
