import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import './index.scss';

export function PageFooter(props) {
  const { t } = props;
  return (
    <div className="print-view-footer">
      <div className="print-view-left-footer column">
        <div className="print-view-footer-content">
          <span>{t('COPY_RIGHT')}</span>
        </div>
      </div>
      <div className="print-view-right-footer column">
        <div className="print-view-header-index"></div>
      </div>
    </div>
  );
}

PageFooter.propTypes = {
  t: PropTypes.func,
};

PageFooter.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(PageFooter);
