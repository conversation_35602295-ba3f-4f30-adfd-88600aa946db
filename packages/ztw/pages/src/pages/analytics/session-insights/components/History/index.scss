// @import 'scss/colors.scss';
// @import 'scss/mixins.scss';
// @import 'scss/variables.scss';

// .history {
//   background: #E3E5E6;
//   .arrows {
//     color: #656666;
//   }
//   .slick-slider{box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;-ms-touch-action:pan-y;touch-action:pan-y;-webkit-tap-highlight-color:transparent;}
//   .slick-list,.slick-slider{position:relative;display:block;}
//   .slick-list{
//     overflow:hidden;
//     // margin:0;
//     padding:0;
//     margin-right: 1em;
//     margin-left: 1em;
//   }
//   .slick-list:focus{outline:none;}
//   .slick-slider .slick-list,.slick-slider .slick-track{-ms-transform:translateZ(0);transform:translateZ(0);}
//   .slick-track{position:relative;top:0;left:0;display:block;margin-left:auto;margin-right:auto;}
//   .slick-track:after,.slick-track:before{display:table;content:"";}
//   .slick-track:after{clear:both;}
//   .slick-slide{
//     display:none;float:left;height:100%;min-height:1px;
//     width: 80px !important;
//   }
//   .slick-initialized .slick-slide{display:block;}
//   .slick-next,.slick-prev{font-size:0;line-height:0;position:absolute;top:50%;display:block;width:20px;height:20px;padding:0;-ms-transform:translateY(-50%);transform:translateY(-50%);cursor:pointer;border:none;}
//   .slick-next,.slick-next:focus,.slick-next:hover,.slick-prev,.slick-prev:focus,.slick-prev:hover{color:transparent;outline:none;background:transparent;}
//   .slick-next:focus:before,.slick-next:hover:before,.slick-prev:focus:before,.slick-prev:hover:before{opacity:1;}
//   .slick-next:before,.slick-prev:before{font-family:slick;font-size:20px;line-height:1;opacity:.75;color:var(--semantic-color-surface-base-primary);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}
//   .slick-prev{left:-20px;}
//   .slick-prev:before{
//     // content:"\2190";
//     content:"\003C";
//     margin-top: 2em;
//     height: 24px;
//     width: 12px;
//     color: #656666;
//     font-family: "Font Awesome 5 Pro";
//     font-size: 24px;
//     letter-spacing: 0;
//     line-height: 24px;
//   }
//   .slick-next{right:-20px;}
//   .slick-next:before{
//     content:"\003E";
//     margin-top: 2em;
//     height: 24px;
//     width: 12px;
//     color: #656666;
//     font-family: "Font Awesome 5 Pro";
//     font-size: 24px;
//     letter-spacing: 0;
//     line-height: 24px;
//   }
//   .slick-dots{position:absolute;bottom:-25px;display:block;width:100%;padding:0;margin:0;list-style:none;text-align:center;}
//   .slick-dots li{position:relative;display:inline-block;margin:0 5px;padding:0;}
//   .slick-dots li,.slick-dots li button{width:20px;height:20px;cursor:pointer;}
//   .slick-dots li button{font-size:0;line-height:0;display:block;padding:5px;color:transparent;border:0;outline:none;background:transparent;}
//   .slick-dots li button:focus,.slick-dots li button:hover{outline:none;}
//   .slick-dots li button:focus:before,.slick-dots li button:hover:before{opacity:1;}
//   .slick-dots li button:before{font-family:slick;font-size:6px;line-height:20px;position:absolute;top:0;left:0;width:20px;height:20px;content:"\2022";text-align:center;opacity:.25;color:#000;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}
//   .slick-dots li.slick-active button:before{opacity:.75;color:#000;}
//   h3{
//     background: #6b6d70;color:var(--semantic-color-surface-base-primary);font-size:36px;line-height:100px;margin:10px;padding:2%;position:relative;text-align:center;
//   }
//   .element {
//     background: #6b6d70;
//     color:var(--semantic-color-surface-base-primary);
//     // font-size:36px;
//     line-height:100px;
//     // margin:10px;
//     margin: 0.7em 0.7em 2em 0.7em;
//     padding:2%;
//     position:relative;
//     text-align:left;
//   }
//   .element-icon {
//     margin-top: 24px;
//   }
//   .insights-chart-icon{font-size:24px!important;}
//   .history-thumbnail-element-icon{line-height:44px!important;}
//   .history-thumbnail-belt{top:0;left:0;bottom:0;margin:0;padding:0;white-space:nowrap;position:absolute;}
//   .history-element{display:inline-block;position:relative;top:8px;border:1px solid var(--semantic-color-content-base-secondary);}
//   .history-element.history-thumbnail-element{background:var(--semantic-color-surface-base-primary);cursor:pointer;display:inline-block;font-size:13px;height:62px;width:70px;cursor:pointer;margin-right:5px;}
//   .history-element.history-thumbnail-element:first-child{margin-left:5px;}
//   .history-element.history-thumbnail-element:hover{background:#f3f3f3;}
//   .history-element.history-thumbnail-element.history-thumbnail-selected{background:#6b6d70;color:var(--semantic-color-surface-base-primary);}
//   .history-thumbnail-element-header{color:#6b6d70;border-bottom:1px solid var(--semantic-color-content-base-secondary);height:16px;padding:0 4px;}
//   .history-thumbnail-element-header.history-thumbnail-selected{color:var(--semantic-color-surface-base-primary);}
//   .history-thumbnail-element-header .history-thumbnail-element-header-number{color:#6b6d70;display:inline-block;vertical-align:top;width:50%;height:100%;line-height:15px;}
//   .history-thumbnail-element-header .history-thumbnail-element-header-number.history-thumbnail-selected{color:var(--semantic-color-surface-base-primary);}
//   .history-thumbnail-element-header .history-thumbnail-element-header-delete{color:#6b6d70;display:inline-block;text-align:right;vertical-align:top;line-height:14px;width:50%;}
//   .history-thumbnail-element-header .history-thumbnail-element-header-delete:hover{color:var(--semantic-color-content-base-secondary);}
//   .history-thumbnail-element-header .history-thumbnail-element-header-delete.history-thumbnail-selected{color:var(--semantic-color-surface-base-primary);}
//   .history-thumbnail-element-header .history-thumbnail-element-header-delete.history-thumbnail-selected:hover{color:var(--semantic-color-content-base-secondary);}
//   .history-thumbnail-element-body{text-align:center;color:#007ea8;width:100%;height:44px;}
//   .history-thumbnail-element-body.history-thumbnail-selected{color:var(--semantic-color-surface-base-primary);}

//   .slick-slider{
//     margin:30px auto 50px;
//     background: var(--semantic-color-surface-base-primary);
//     border: 1px solid var(--semantic-color-border-base-primary);
//   }
//   .slick-dots{margin-left:0;}
//   @media (max-width:768px){
//   h3{font-size:24px;padding:0;}
//   .slick-arrow{display:none!important;}
//   }
//   .slick-next:before,.slick-prev:before{color:#000;}
// }

// @media screen and (max-width: $templateMediaSize) {
//   .main-container>.pie-container {
//     flex-direction: column;
//     width: 100%;
//   }
// }



