

.ec-root-page {
.title-back{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
  }
.no-pointers {
  pointer-events: none;
  height: 7.5in;
  margin-bottom: 2em;
}
/*print button container*/
.print-view-button-wrapper {
    top: 57px;
    width: 100%;
    margin: 0 auto;
    background: var(--semantic-color-surface-base-primary);
    border-bottom: 1px solid var(--semantic-color-border-base-primary);
    position: relative;
    z-index: 1000;
    .back {
        padding-left: 5px;
        color: var(--semantic-color-content-base-primary);
    }
}
.print-widget-filter-label-value, .print-report-filter-label-value {
  display: inline-block;
  font-weight:600;
  color: var(--semantic-color-content-base-primary);
  max-width: 565px;
  vertical-align: bottom;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
  .print-view-button-container {
    display: block;
    margin: 0 auto;
    width: 9.5in;
    height: 128px;
    padding: 50px 0; }
  
    .print-view-widget-filters {
      display: inline-block;
      vertical-align: top;
      position: absolute;
      right: 40px;
  }
  .exit-print-view {
    text-align: left !important; }
    .exit-print-view .control-button-reports {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      color: var(--semantic-color-content-base-primary);
    }
  
  .print-column {
    width: 2.83in;
    display: inline-block;
    text-align: center;
    vertical-align: top;
    height: 28px; }
    .print-column.exit-print-view {
      padding-left: 24px; }
    .print-column.print-view-btn {
      padding-right: 24px; }
    .print-column .print-view-title {
      font-size: 16px;
      color: var(--semantic-color-content-base-primary);
      position: relative;
      top: 5px; }
  
  .print-view-btn {
    text-align: right; }
  
  .print-page-container {
    padding-top: 130px;
    padding-bottom: 1px;
    height: auto;
    background: var(--semantic-color-surface-base-secondary); }
  
    .print-report-overflow-container {
      display: none;
      padding: 16px 0 0;
      color: #939393;
  }

img{border:0;}
:-moz-placeholder{font-weight:300;}
::-webkit-input-placeholder{font-weight:300;}
::-ms-clear{display:none;}
::-ms-reveal{display:none;}
.column{display:inline-block;vertical-align:top;}
.column::-moz-selection{background:transparent;}
.column::selection{background:transparent;}
.copyright-statement:before{content:" ";}
@media print{
.print .print-page{margin:0;margin-bottom:0;-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;}
.print .print-page .print-view-content{height:9.25in!important;font-family:"arial";}
.print .print-page .print-view-content .print-report-filter-label{font-family:"arial";}
.print .print-page .print-view-content .print-report-filter-label-value{font-family:"arial";font-weight:bold;}
.print .print-page .print-view-footer,.print .print-page .print-view-header{font-family:"arial";}
}
.print-view-header{width:100%;height:0.75in;}
.print-view-content{
  // height:calc(100% - 1.25in);
  width:100%;
}
.print-view-footer{color:#939393;font-size:13px;height:0.5in;}
.print-page{background:var(--semantic-color-surface-base-primary);height:10.5in;width:8.5in;margin:0 auto;margin-bottom:0.40in;padding:0.25in;page-break-after:always;page-break-inside:avoid!important;position:relative;-webkit-box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);-moz-box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);}
.print-view-left-footer{width:80%;vertical-align:middle;}
.print-view-right-footer{width:20%;vertical-align:middle;}
.print-view-footer-content{display:inline-block;vertical-align:middle;}

.print-view-logo{width:50%;display:inline-block;vertical-align:middle;}
.print-view-header-text{width:50%;display:inline-block;text-align:right;vertical-align:middle;}
.print-view-header-title{font-size:16px;color:#00bce4;}
.print-view-header-index{text-align:right;}
.print-view-header-index:after{
  content: "Page " counter(chapter);
  counter-increment: chapter;
  padding-left: 5px;
}
.print-report-filter-label{display:inline-block;padding-right:10px;color:#6b6d70;vertical-align:bottom;}
.print-report-filter-label-value{display:inline-block;font-weight:600;color:var(--semantic-color-content-base-secondary);max-width:565px;vertical-align:bottom;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.print-view-information-panel{display:inline-block;vertical-align:top;width:100%;}
.print-view-information-panel.fullwidth{width:100%;}
.print-report-filter-label:after{content:":";}
.print-view-information{border:1px solid var(--semantic-color-border-base-primary);border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;background-color:var(--semantic-color-background-primary);height:auto;padding:16px;}
.print-view-filters{padding-bottom:16px;font-size:13px;}
.print-view-filters:last-child{padding-bottom:0px;}
}