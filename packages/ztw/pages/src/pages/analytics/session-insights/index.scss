.main-insights-container {
  padding: 19px 25px;
  .component-header {	
    height: 24px;
    width: fit-content;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    white-space: nowrap;
  }
  .header {
    width: 80%;
    float: left;
  }
  .printView {
    float: right;
    // margin: 1em 1em 1em 0em;
    cursor: pointer;
  }
  .table-wrapper {
    padding-top: 0em;
  }
  .data-filter-container {
    width: 25%;

    .drop-down-selected-value {
      border: none;
      border-bottom: 1px solid #3669d6;
      border-radius: 0;
      background: none;
    }

    .drop-down-container {
      .drop-down-list li { 
        border-radius: 0px;
        button { text-align: left; }
      }
      .drop-down-list > li:first-child:hover {
				background-color: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-interactive-primary-default);
			}
      .items-container > li:first-child:hover {
				background: var(--semantic-color-content-interactive-primary-default);
        color: var(--semantic-color-background-primary);
			}
      .dropdown-search {
        height: 32px;
        border-radius: 8px;
        background-color: var(--semantic-color-background-pale);
        margin: 10px;
        display: flex;
        max-width: 85%;
        margin-left: auto;
        margin-right: auto;
      }
      .dropdown-search-text {
        border: none;
        color: var(--semantic-color-content-base-primary);
        border-radius: 8px;
        background: inherit;
        width: 85%;
        padding: 10px 10px 10px 10px;
      }
      .clear-button {
        width: auto;
      }
      .action-buttons{
        float: right;
        white-space: nowrap;
      }
    }


    .drop-down-container ul.drop-down-list.open {
      visibility: visible;
      opacity: 1;
      .items-container {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto;
        width: 100%;
      }
    }
  }
  .remove-button {
    width: 10%;
  }
}

.accordion-container {
  width: 100%;
  height: 100vh;
  // background-color: whitesmoke;
  position: relative;
}

.outer-layer {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  // overflow: hidden;
  .knob-holder{
    transform: scaleX(-1);
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
    height: 35px;
    width: 23px;
    // border-radius: 0 5px 5px 0; //opposite box
    border-radius: 5px 0px 0px 5px;
    background-color: var(--semantic-color-content-interactive-primary-default);
    z-index: 0;
    padding-left: 1.5em;
  }
  .knob{
    height: 27px;
    width: 11px;
    transform: scaleX(-1);
    color: var(--semantic-color-surface-base-primary);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
  }
  .slide {
    height: 100%;
    position: relative;
    text-align: center;
  }
  
  .filter{
      width: 1px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
  }
 
  // .filter-expand {
  //   // width: 25%;
  //   width: 350px;
  //   background-color: #000000;
  //   background-size: cover;
  //   background-repeat: no-repeat;
  //   background-position: left;
  //   height: 100vh;
  //   min-height: 100vh;
  //   max-height: 100vh;
  // }
  .tablesec {
    width: 98%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
  }
  .tablesec-shrink {
    width: calc(100% - 32rem);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
    overflow-y: auto;
  }
}
