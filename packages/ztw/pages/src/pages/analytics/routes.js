import { BASE_LAYOUT } from 'config';
import DNSLogs from './dns-logs';
import DnsInsights from './dns-insights';
import SessionLogs from './session-logs';
import SessionInsights from './session-insights';
import TunnelLogs from './tunnel-logs';
import TunnelInsights from './tunnel-insights';

export const routes = [
  {
    isNested: false,
    path: '/analytics/dnslogs',
    name: null,
    component: DNSLogs,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/analytics/dnsinsights',
    name: null,
    component: DnsInsights,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/analytics/sessionlogs',
    name: null,
    component: SessionLogs,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/analytics/sessioninsights',
    name: null,
    component: SessionInsights,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/analytics/tunnellogs',
    name: null,
    component: TunnelLogs,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/analytics/tunnelinsights',
    name: null,
    component: TunnelInsights,
    layout: `${BASE_LAYOUT}`,
  },
];

export default routes;
