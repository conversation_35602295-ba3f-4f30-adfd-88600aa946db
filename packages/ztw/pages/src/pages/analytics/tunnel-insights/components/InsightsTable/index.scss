@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

// .main-container {
//   padding: 19px 25px;
//   // .container-row{
//   //   margin: 30px 0;
//   // }
  
// }

// @media screen and (max-width: $templateMediaSize) {
//   .main-container>.pie-container {
//     flex-direction: column;
//   }
//   .main-container>.pie-container> {
//     width: 100%;
//   }
// }

