@import 'scss/mixins.scss';
@import "scss/colors.scss";

.ec-root-page {
.insights-filters-container {
  padding: 24px;
  height: 100%;
  .tabs-items div a {
    font-size: 24px;
  }
  .time-filter .time-filter-dropdown {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 4px;
  }
  .separator-line {
    margin: 0;
  }
  .separator-line-header {
    margin: 30px 0;
  }
  .tabPactive {
    color: var(--semantic-color-content-base-primary);
    // z-index: 2;
  }
  .tabs-items div div.highlighter {
    z-index: 2;
    position: relative;
  }
  .tabs-highlrighte {
    background: var(--semantic-color-border-base-primary);
    z-index: 1;
  }
  label {
    span {
      margin-left: 0px;
    }
  }
  .header{
    padding-bottom: 2em;
    margin-top: 19px;
  }
  .title{
    height: 19px;
    width: 37px;
    color: var(--semantic-color-surface-base-primary);
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 19px;
    float: left;
  }
  .clear {
    height: 16px;
    color: var(--semantic-color-content-interactive-primary-default);
    font-family: "Font Awesome 5 Pro";
    font-size: 12px;
    letter-spacing: 0;
    line-height: 16px;
    white-space: nowrap;
    cursor: pointer;
    float: right;
  }
  .refresh-text{
    padding-left: 0.25em;
  }
  .filter-box {
    min-height: 100px;
    padding: 1em 0em 1em 0em;
    white-space: nowrap;
    .checkbox-container.unselected {
      display: flex;
    }
  }
  .side-header{
    padding-bottom: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .filter-sideheader {
    height: 16px;
    // width: 62px;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0;
    line-height: 15px;
    float: left;
    svg {
      color: var(--semantic-color-content-base-tertiary);
      width: 23px;
      height: 16px;
    }
  }
  .filter-card-small {
    height: 49px;
    width: 320px;
    border-radius: 5px;
    background-color: var(--semantic-color-background-primary);    
    margin-top: 2em;
    .radio-button-container.radio-group-container {
      padding: 0;
      height: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    .select-item {
      .react-select__control {
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 4px;
      }
    }
    .react-select__menu {      
      height: 28em;
      text-align: left;
      .react-select__menu-list {
        min-height: 20em;
        overflow: visible;
      }
    }
    .single {
      width: 100%;
    }
    .drop-down-container{
      width: 90%!important;
    }
    button.drop-down-selected-value {
      border: none !important;
      box-shadow: none !important;
      // border-radius: 8px;
      // border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    // .radio-button-container .radio-buttons .radio-button  {
    //   margin-right: 3px;
    //   label{
    //   padding: 4px 15px 4px 0;
    //   border-radius: 4px;
    //   }
    // }
    .check-circle {
      padding-left: 6px;
    }
    .select-item {
      width: 250px;
      margin-left: 0px;
      // .css-1szy77t-control {
      //   border-left: 0;
      //   border-right: 0;
      //   border-top: 0;
      //   border-radius: 0;
      //   box-shadow: none;
      //  &:hover {
      //   box-shadow: none;
      //   }
      // }
    }
  }
  .expander {
    max-height: 100%;
  }
  .filter-card-large {
    // max-height: 55em;
    width: 100%;
    border: none;
    .radio-button-container  .radio-buttons {
      border: 1px solid var(--semantic-color-border-base-primary);
      padding: 4px 6px;
      background-color: var(--semantic-color-background-pale);
      border-radius: 10px;
      .radio-button {
        .chart-label {
          width: fit-content;
        }
        label {
          padding: 8px 0;
          min-height: 20px;
          border: none;
        }
      }
    }

    .add-dropdown {
      .fa-angle-down, .fa-angle-up  {
        display: none;
      }
      .drop-down-selected-value {
        text-align: right;      
        border: none;
      }
    }
    .drop-down-container{
      width: 94%
    }
    .drop-down-selected-value{
      padding: 3px;
      border: none !important;
      box-shadow: none !important;
      // border-radius: 8px;
      // border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    .filter-container{
      padding: 5px 0  5px 0;
      .fa-xmark {
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .select-item {
        width: 280px;
        margin-left: 10px;
      }
      .filter-card-header{
        padding-bottom: 2em;
      }
      .filter-card-label{
        height: 16px;
        color: var(--semantic-color-content-base-primary); 
        font-weight: 500;
        font-size: 13px;
        letter-spacing: 0;
        line-height: 15px;
        float: left;
        padding: 10px;
        &.invalid {
          color: var(--semantic-color-content-status-danger-primary);
        }
      }
      .filter-clear{
        height: 16px;
        color:  var(--semantic-color-content-base-primary);
        font-family: "Font Awesome 5 Pro";
        font-size: 12px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        padding: 10px 20px 0  0;
        float: right;
        cursor: pointer;
      }
      .couple-filters{
        padding-top: 10px;
        background-color: var(--semantic-color-background-primary);
        .input-container .input-wrapper {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
      }
      .couple-filters-bottom{
        padding-bottom: 20px;
      }
      .dropdown{
        width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 34px
      }
      .radio-button-container.radio-group-container {
        padding: 0 10px;
      }
      #requestedDomainMatchType .entity-dropdown .react-select__value-container, .entity-dropdown .react-select__indicator {
        min-width: fit-content;
      }
      .react-select__control {
        min-width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 36px;
      }
      .input-label {
        margin-top: 10px;
        text-align: left;
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 16px;
      }
      .input-container input{
        background-color: var(--semantic-color-background-pale);
      }
      // added for AddFilter Dropdown
      .drop-down-container {
        .drop-down-list li { 
          border-radius: 0px;
          button { text-align: left; }
        }
        .drop-down-list > li:first-child:hover {
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-interactive-primary-default);
        }
        .items-container > li:first-child:hover {
          background: var(--semantic-color-content-interactive-primary-default);
          color: var(--semantic-color-background-primary);
        }
        .dropdown-search {
          height: 32px;
          // width: 18em;
          border-radius: 8px;
          background-color: var(--semantic-color-background-pale);
          margin: 10px;
          display: flex;
          max-width: 85%;
          margin-left: auto;
          margin-right: auto;
        }
        .dropdown-search-text {
          border: none;
          color: var(--semantic-color-content-base-primary);
          border-radius: 8px;
          background: inherit;
          width: 85%;
          padding: 10px 10px 10px 10px;
          border-radius: 8px;
        }
        .clear-button {
          width: auto;
        }
        .action-buttons{
          width: 10px;
        }
      }
     
      .drop-down-container ul.drop-down-list.open {
        visibility: visible;
        opacity: 1;
        .items-container {
          max-height: 300px;
          overflow-x: hidden;
          overflow-y: auto;
          width: 100%;
        }
      }
      .prefix {
        float: left;
        .select-item{
          width: 165px;
          margin-left: 0;
        }
        .react-select__menu {
          width: 14em;
        }
      }
      .suffix {
        .input-container {
          padding: 0.6em 0.5em 0em 0.1em;
        }
        .input-container .input-wrapper input {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
        .empty-container {
          padding: 0.6em 0.5em 0em 0.1em;
          width: 11em;
          height: 3em;
          border: none;
        }
      }
    }
    .entity-dropdown-container{
      .select-item{
        width: 90%;
        margin-left: 12px;
        // remove outline for react-select
        // none of the below css works - need to fix
        .css-1szy77t-control :hover{
          border-color: var(--semantic-color-surface-base-primary);
          outline: 0 !important;
        }
        .react-select__control{
          outline: 0 !important;
        }
        // react-select__control--is-focused
        .react-select__control--menu-is-open{
          border: 1px solid var(--semantic-color-border-base-primary) !important;
        }
        .react-select__control--is-focused{
          outline: 0 !important;
          }
        // till here
      }

    }
  }
  .apply-filters{
    width: 20.5em;
    margin-top: 3em;
    margin-bottom: 30em;
    .svg-inline--fa.fa-arrow-rotate-right  {
      transform: rotateY(180deg);
    }
  }

}

.filter-card-small {
  color:  var(--semantic-color-content-base-primary);
  .css-dvua67-singleValue {
    color:  var(--semantic-color-content-base-primary);
  } 
  .filter-card-small-units {
    margin-top: 0.0em;
  }
  .metric-dropdown {
    height: 49px;
    width: 250px;
    border-radius: 5px; 
    min-width: 322px;   
    background-color: var(--semantic-color-background-primary);
    margin-top: 2em;
    .react-select__menu {
      height: 13em;
      text-align: left;
      .react-select__menu-list {
        min-height: 11em;
        overflow: visible;
      }
    }
  }
}

.filter-card-small-chart {
  height: 44px;
  width: 268px;
  border-radius: 5px;
  background-color: var(--semantic-color-surface-base-primary);
  margin-top: 2em;
  p {
    display: none;
  }
  .radio-button-container {
    padding: 0;
    .charts {
      height: 0;
      margin: 0;
    }
  }
}

.radio-button-container  .radio-buttons .radio-button {
  .chart-label {
    width: fit-content;
  }
  label {
    padding: 8px 0;
  }
}
}