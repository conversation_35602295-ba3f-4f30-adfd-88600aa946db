// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';
import HorizontalBar from 'components/barChart/HorizontalBar';
import InsightsPieWidget from 'components/pieChart/InsightsPieWidget';
import LineChartInsights from 'components/lineChartInsights';

import * as TunnelInsightsSelectors from 'ducks/tunnelInsights/selectors';
import { updateMenu, drilldownSessionLogs } from 'ducks/tunnelInsights';
import InsightsTable from '../InsightsTable';

import './index.scss';

const getWidget = (label, props) => {
  const {
    actions, printIdx,
    history,
  } = props;
  const { sessionHistory } = history[printIdx];
  const {
    reportData, trendData,
    totalTrendDataKeys,
  } = sessionHistory;

  const htData = reportData;
  let ht;
  if (htData.length === 0) {
    ht = '40em';
  } else if (htData.length > 5) {
    ht = 10 + (htData.length * 2) + 'em';
  } else {
    ht = 10 + (htData.length) + 'em';
  }
    
  switch (true) {
  case label === 'bar':
    return (
      <HorizontalBar
        {...props}
        height={ht}
        handleAssetDetails={actions.updateMenu}
        dataSequence={reportData}
        barKey={['total']} />
    );
  case label === 'pie':
    return (
      <div style={{ height: '525px' }}>
        <InsightsPieWidget
          data={reportData}
          colors={['#90D9F4', '#9FD67F', '#FFD155', '#D16464', '#C1BD82', '#DA80BC']}
          innerText="KB"
          legendsPos="bottom-left"
          clickMoreInfo={actions.drilldownSessionLogs} />
      </div>
    );
  case label === 'line':
    return (
      <LineChartInsights
        {...props}
        data={trendData}
        dataKeys={totalTrendDataKeys}
        height="525"
        clickMoreInfo={actions.drilldownSessionLogs} />
    );
  case label === 'table':
    return <InsightsTable {...props} />;
  default:
    return <GenericErrorMessage {...props} />;
  }
};

export function PrintWidget(props) {
  const { history, printIdx } = props;

  if (!history.length) {
    return (
      <div className="insights-container">
        <div style={{ height: '40em' }}>
          <GenericErrorMessage {...props} />
        </div>
      </div>
    );
  }
  // eslint-disable-next-line prefer-destructuring
  const chart = history[printIdx].chart;
  return (
    <Loading {...props}>
      <ServerError {...props}>
        <div className="insights-container">
          <div style={{ height: '40em' }}>
            {getWidget(chart, props)}
          </div>
        </div>
      </ServerError>
    </Loading>
  );
}

PrintWidget.propTypes = {
  t: PropTypes.func,
  printIdx: PropTypes.number,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  history: PropTypes.arrayOf(PropTypes.shape({})),
};
  
PrintWidget.defaultProps = {
  t: (str) => str,
  printIdx: 0,
  showFilters: {},
  actions: {},
  history: [],
};

const mapStateToProps = (state) => ({ ...TunnelInsightsSelectors.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    drilldownSessionLogs,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PrintWidget));
