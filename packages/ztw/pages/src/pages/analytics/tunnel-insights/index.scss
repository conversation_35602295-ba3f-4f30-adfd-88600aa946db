.main-insights-container {
  padding: 19px 25px;
  .rdg-wrapper .react-grid-Canvas {
    background-color: var(--semantic-color-background-primary);
  }
  .component-header {	
    height: 24px;
    width: fit-content;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    white-space: nowrap;
  }
  .header {
    width: 80%;
    float: left;
  }
  .printView {
    float: right;
    // margin: 1em 1em 1em 0em;
    cursor: pointer;
  }
  .table-wrapper {
    padding-top: 0em;
  }
  .data-filter-container {
    width: 25%;

    .drop-down-selected-value {
      border: none;
      border-bottom: 1px solid #3669d6;
      border-radius: 0;
      background: none;
    }

    .drop-down-container {
      .drop-down-list li { 
        border-radius: 0px;
        button { text-align: left; }
      }
      .dropdown-search {
        height: 32px;
        border-radius: 8px;
        background-color: var(--semantic-color-background-pale);
        margin: 10px;
        display: flex;
        max-width: 85%;
        margin-left: auto;
        margin-right: auto;
      }
      .dropdown-search-text {
        border: none;
        color: var(--semantic-color-content-base-primary);
        border-radius: 8px;
        background: inherit;
        width: 85%;
        padding: 10px 10px 10px 10px;
      }
      .clear-button {
        width: auto;
      }
      .action-buttons{
        float: right;
        white-space: nowrap;
      }
    }


    .drop-down-container ul.drop-down-list.open {
      visibility: visible;
      opacity: 1;
      .items-container {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto;
        width: 100%;
      }
    }
  }
  .remove-button {
    width: 10%;
  }
}

.accordion-container {
  width: 100%;
  height: 100vh;
  // background-color: whitesmoke;
  position: relative;
}

.outer-layer {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  // overflow: hidden;
  .knob-holder{
    transform: scaleX(-1);
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
    height: 35px;
    width: 23px;
    // border-radius: 0 5px 5px 0; //opposite box
    border-radius: 5px 0px 0px 5px;
    background: var(--semantic-color-background-pale);
    z-index: 0;
    padding-left: 1.5em;
  }
  .knob{
    height: 27px;
    width: 11px;
    transform: scaleX(-1);
    color: var(--semantic-color-content-base-primary);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
  }
  .slide {
    height: 100%;
    position: relative;
    text-align: center;
    // transition: 0.1s;
  }
  
  .filter{
      // width: 0.1%;
      width: 1px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
  }
 
  .filter-expand {
    width:  400px;
    direction:rtl; 
    // background-color: burlywood; // change
    background-color: var(--semantic-color-background-primary);
    height: 100%;
    // overflow-y: auto;
    padding: 0;
    margin: 0;
    position: absolute;
    .filter-expand-width {
      width:  400px;
      float: left;
      direction:ltr; 
      // width:  400px;
      // overflow: visible;
      height: 100%;
      // background-color: #000000;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
      // overflow-x: visible;
      .expander {
        border: 1px solid var(--semantic-color-border-base-primary);
      }
    }
  }
  .filter-expand-space {
    float: left;
    direction:ltr; 
    width:  400px;
    overflow: visible;
    height: 100%;
    // background-color: burlywood;//change
    // background-color: var(--semantic-color-background-primary);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    z-index: -1;
    // overflow-x: visible;
  }

  .tablesec{
    width: 98%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
  }
  .tablesec-shrink {
    width: calc(100% - 32rem);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
    overflow-y: auto;
  }
}

div.drop-down-container ul.drop-down-list li {
  width: 100%;
}

