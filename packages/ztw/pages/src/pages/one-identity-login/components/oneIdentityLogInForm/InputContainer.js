import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

export default class InputContainer extends PureComponent {
  static propTypes = {
    id: PropTypes.string,
    inputLabel: PropTypes.string,
    inputType: PropTypes.string,
    placeholder: PropTypes.string,
    updateTextbox: PropTypes.func,
    inputVal: PropTypes.string,
    name: PropTypes.string,
    username: PropTypes.string,
  };
    
  static defaultProps = {
    id: 'login-panel',
    inputLabel: null,
    inputType: 'text',
    placeholder: null,
    updateTextbox: null,
    inputVal: '',
    name: '',
    username: null,
  };

  render() {
    const {
      id,
      inputLabel,
      inputType,
      placeholder,
      updateTextbox,
      inputVal,
      name,
      username,
    } = this.props;
    return (
      <div className="input-container">
        <div>
          <span
            id={`${id}-label-${inputType}`}
            className="login-text-label">
            {inputLabel}
          </span>
          {
            username
              ? <span className="login-id">{username}</span>
              : ''
          }
        </div>
        <div className="login-text-input">
          <input
            id={`${id}-input-${inputType}`}
            className="login-text-input-text"
            type={inputType}
            placeholder={placeholder}
            value={inputVal}
            onChange={updateTextbox(name)} />
        </div>
      </div>
    );
  }
}
