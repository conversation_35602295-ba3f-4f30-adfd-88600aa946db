.ec-login {
  .input-container {
    display: inline-block;
    margin-right: 12px;
    vertical-align: bottom;
    .login-text-label {
      font-size: 13px;
      font-weight: 500;
      text-transform: uppercase;
      display: block;
      margin-bottom: 8px;
    }
  }
  button {
    &.login-btn {
      &.submit {
        padding:  9px 15px;
      }
    }
  }
  label {
    &.rember-my-login {
      color: var(--semantic-color-content-base-primary);
      font-size: 13px;
      margin-top: 10px;
      display: inline-block;
      cursor: pointer;
    }
    span {
      margin-left: 7px;
    }
  }
  
  .language-selector {
      width: auto;
      vertical-align: top;
      display: inline-block;
      min-width: 110px;
      font-size: 12px;
      .login-language-container {
        display: flex;
        position: relative;
        color: var(--semantic-color-content-base-primary);
        &:hover {
          color: var(--semantic-color-content-interactive-primary-default);
        }
        .icon-container {
          display: inline-block;
          top: 7px;
          position: absolute;
          left: 0;
        }
        .login-dropdown {
          width: 100%;
          //drop overridden styles
          .drop-down-container {
            font-size: 13px;
            vertical-align: top;
            white-space: nowrap;
            color: var(--semantic-color-content-base-primary);
            &:hover {
              color: var(--semantic-color-content-interactive-primary-default);
            }
            button {
              font-size: 13px;
              background: none;
              border: none;
              text-align: left;
              display: inline-block;
              padding-left: 20px;
              
              span {
                padding-right: 7px;
              }
            }
            .drop-down-list {
              button {
                padding: 8px 16px;
                &:hover {
                  background-color: #e4faff;
                }
              }
            }
          }
        }
      }
      .login-language {
        margin-top: 10px;
      }
  }
  
  .login-remember-me {
    display: inline-block;
    margin-right: 10px;
  }
  
  .error-summary {
    position: relative;
    top: 12px;
    z-index: 1;
    font-size: 13px;
  }
  
  .change-password {
    background: rgba(38, 38, 38, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    
  }
  
  .dialog-mask, .nested-dialog-mask {
    background: rgba(38, 38, 38, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
  }
  
  .dialog, .-js-nested-dialog, .dialog-box {
    border-radius: 5px;
    margin: 10px auto;
    opacity: 0.5;
    position: relative;
    width: 700px;
    z-index: 1002;
    opacity: 1;
    background-color: rgb(255, 255, 255);
    margin: 339px auto;
  }
  
  .dialog-header {
    padding: 16px;
    background: var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px 5px 0 0;
  }
  .dialog-header-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    font-size: 16px;
    color: var(--semantic-color-surface-base-primary);
    position: relative;
    vertical-align: top;
    width: 100%;
  }
  .dialog-header-close {
    cursor: pointer;
    color: var(--semantic-color-surface-base-primary);
    position: absolute;
    right: 0px;
  }
  
  .dialog-body, .confirm-dialog-body {
    background: var(--semantic-color-background-pale);
    border: 1px solid var(--semantic-color-border-base-primary);
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 16px 24px;
    width: 100%;
  }
  
  .form-section:last-child, .form-tab-section:last-child, .reports-tab-section:last-child {
    padding-bottom: 8px;
  }
  .dialog .form-input-rows, .-js-nested-dialog .form-input-rows, .dialog-box .form-input-rows, .dialog .form-tab-input-rows, .-js-nested-dialog .form-tab-input-rows, .dialog-box .form-tab-input-rows {
    overflow: visible;
  }
  .password-change-container .form-input-row, .password-change-container .filters-list-item, .password-change-container .filters-add-filter-container {
    width: 100% !important;
    position: relative;
  }
  .password-change-container .password-expired-header {
    border: 1px solid #f7cb8a;
    border-radius: 5px;
    background-color: #fbedd9;
  }
  .password-change-container .password-expired-header .form-input-label, .password-change-container .password-expired-header .filter-item-header, .password-change-container .password-expired-header .form-input-label-text, .password-change-container .password-expired-header .free-form-listbox-label, .password-change-container .password-expired-header .multi-select-listbox-label, .password-change-container .password-expired-header .insights-filter-label, .password-change-container .password-expired-header .data-loss-msg {
    color: #f19409;
  }
  .form-input-label span, .filter-item-header span, .form-input-label-text span, .free-form-listbox-label span, .multi-select-listbox-label span, .insights-filter-label span, .data-loss-msg span {
    cursor: help;
    display: inline-block;
    margin-right: 10px;
    width: auto;
  }
  
  .form-input-label, .filter-item-header, .form-input-label-text, .free-form-listbox-label, .multi-select-listbox-label, .insights-filter-label, .data-loss-msg {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
    width: 100%;
  }
  
  .password-change-container .show-password {
    position: absolute;
    right: 16px;
    bottom: 20px;
    cursor: pointer;
    color: #ccc;
  }
  
  .dialog-mask.password-expiry-view div.dialog-body.-js-content-body, .password-expiry-view.nested-dialog-mask div.dialog-body.-js-content-body, .dialog-mask.password-expiry-view div.-js-content-body.confirm-dialog-body, .password-expiry-view.nested-dialog-mask div.-js-content-body.confirm-dialog-body {
    overflow-y: auto;
  }
  
  .dialog-footer {
    background: var(--semantic-color-background-primary);
    border-top: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0 0 5px 5px;
    padding: 16px;
    width: 100%;
  }
  
  .form-field-label.invalid,.form-field-label-wrapper.invalid, .form-input-label.invalid, .invalid.filter-item-header, .invalid.form-input-label-text, .invalid.free-form-listbox-label, .invalid.multi-select-listbox-label, .invalid.insights-filter-label, .invalid.data-loss-msg {
    color: var(--semantic-color-content-status-danger-primary);
  }
  .list-builder-input-textarea.invalid, .form-input-text.list-builder-input.invalid, .list-builder-input.invalid.form-input-text-check-box, .list-builder-input.invalid.number-input-text, .list-builder-input.invalid.form-comments-input-text, .list-builder-input.invalid.multiple-element-search-input, .list-builder-input.invalid.insights-input-text, .list-builder-input.invalid.reports-input-text {
    border: 1px solid #e74c3c;
    box-shadow: none;
  }
  .login-button-container {
   &.large-width {
    .login-text-input {
      background: var(--semantic-color-background-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      display: inline-block;
      padding: 8px 12px;
      width: 480px;
      height: 36px;
      .login-text-input-text {
        border: none;
        background: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-base-primary);
        font-size: 14px;
        padding: 0;
        width: 100%;
      }
      input:required,
      input:invalid {
        box-shadow: none;
      }
      input:focus {
        outline: none;
      }
    }
   }
  }  
}
