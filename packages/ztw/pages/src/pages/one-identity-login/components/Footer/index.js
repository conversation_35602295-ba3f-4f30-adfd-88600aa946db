import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

export default class Footer extends PureComponent {
  static propTypes = {
    copyrightText: PropTypes.string,
    copyrightStatement: PropTypes.string,
    year: PropTypes.string,
  };

  static defaultProps = {
    copyrightText: '',
    copyrightStatement: '',
    year: '',
  };

  render() {
    const { copyrightText, copyrightStatement, year } = this.props;
    
    return (
      <div id="login-page-footer" className="login-page-footer-container">
        <span id="copyright">{copyrightText}</span>
        <span id="company-info">{` ©2007-${year} Zscaler Inc. `}</span>
        <span id="copyright-statement" className="copyright-statement">{copyrightStatement}</span>
      </div>
    );
  }
}
