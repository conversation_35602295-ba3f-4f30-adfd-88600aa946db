import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';

import Spinner from 'components/spinner';
import * as loginSelectors from 'ducks/login/selectors';
import withRouter from 'layout/withRouter';
import Zscaler<PERSON><PERSON> from '../../images/cloud_connector_logo.png';
import OneIdentityLoginForm from './components/oneIdentityLogInForm';
import Banner from './components/Banner';
import Footer from './components/Footer';
import './index.scss';

class OneIdentityLogin extends React.Component {
  static propTypes = {
    locale: PropTypes.shape(),
    history: PropTypes.shape(),
    t: PropTypes.func,
    isLoggedIn: PropTypes.bool,
    accessPrivileges: PropTypes.shape({}),
  };

  static defaultProps = {
    locale: null,
    history: null,
    t: (str) => str,
    isLoggedIn: false,
    accessPrivileges: {},
  };

  state = {
    loading: true,
  };

  componentDidMount() {
    window.addEventListener('load', this.handleLoad);
  }

  handleLoad = () => {
    this.setState({ loading: false });
  };

  render() {
    const {
      locale, t,
    } = this.props;
    const { loading } = this.state;
    const localizationText = locale;
    const year = new Date().getFullYear().toString();
    if (loading) return <Spinner />;

    return (
      <div id="login-page-container" className="login-page-container">
        <div id="login-page" className="one-identity-login-page">
          <div id="login-page-header" className="login-page-header-container">
            <div className="login-page-header-logo-container">
              <img className="login-page-header-logo" src={ZscalerLogo} alt="" />
            </div>
            <OneIdentityLoginForm {...this.state} {...this.props} />
          </div>
          <div className="login-page-banner-content">
            <Banner />
            <Footer
              copyrightText={t(localizationText.COPYRIGHT)}
              copyrightStatement={t(localizationText.COPYRIGHT_STATEMENT)}
              year={year} />
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.login,
  ...state.passwordExpiry,
  cloudData: state.cloud,
  isLoggedIn: loginSelectors.isLoggedIn(state),
});
 
export default connect(mapStateToProps)(withTranslation()(withRouter(OneIdentityLogin)));
