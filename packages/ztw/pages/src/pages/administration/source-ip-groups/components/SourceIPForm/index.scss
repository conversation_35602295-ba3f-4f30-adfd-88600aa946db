.add-edit-source-ip {
  &.add-custom-app-form {
    overflow: hidden;
    .form-sections-container {
      .form-section { 
        margin-top: 10px;
      }
    }
  }
  .form-section {
    .sip-name,
    .sip-ip-address {
      width: 100%;
      float: left;
      .input-container {
        width: 100%;
      }
    }
    .dropdown-container,
    .checkbox-container {
      padding: 12px 0;
    }
    .bw-control-child {
      padding: 12px 0;
      .input-container {
        height: auto;
      }
    }
    .form-textarea {
      margin: 0;
      border-color: var(--semantic-color-border-base-primary);
      border-radius: .25rem;
      border-style: solid;
      border-width: .0625rem;
      color: var(--semantic-color-content-base-secondary);
      outline: none;
      padding: 5px;
      resize: none;
    }
    .source-ip-row {
      margin-bottom: 12px;
      &.full-width {
        .half-width {
          width: 50% !important;
        }
        .input-container {
          .input-wrapper {
            width: auto;
          }
        }
      }
      .list-builder {
        .list-builder-header {
            .list-builder-input-textarea {
              width: 100%;
            }
          }
        .list-builder-body  {
          width: calc(100% - 110px);
        }
      }
    }
  }
  
}
.tooltip-text-list {
  padding: 15px;
}
