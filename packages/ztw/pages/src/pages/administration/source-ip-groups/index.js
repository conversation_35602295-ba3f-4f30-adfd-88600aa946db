import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import NavTabs from 'components/navTabs';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as SourceIPSelectors from 'ducks/sourceIPGroups/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { SOURCE_IP_TABLE_CONFIGS } from 'ducks/sourceIPGroups/constants';
import ConfigTable from 'components/configTable';

import {
  toggleForm,
  loadSourceIPData,
  toggleDeleteForm,
  deleteSourceIP,
  toggleViewModal,
} from 'ducks/sourceIPGroups';
import {
  SourceIPForm,
  SourceIPViewModal,
} from './components';

import './index.scss';

export class SourceIPGroups extends Component {
  static propTypes = {
    t: PropTypes.func,
    sourceIPTableData: PropTypes.arrayOf(PropTypes.shape()),
    toggleSourceIPForm: PropTypes.func,
    showForm: PropTypes.bool,
    load: PropTypes.func,
    formTitle: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleSourceIPView: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    ipAddressesList: PropTypes.arrayOf(PropTypes.string),
    showViewForm: PropTypes.bool,
    authType: PropTypes.string,
    formMode: PropTypes.string,
  };

  static defaultProps = {
    t: (str) => str,
    sourceIPTableData: null,
    toggleSourceIPForm: noop,
    showForm: false,
    load: noop,
    formTitle: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    handleSourceIPView: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
    ipAddressesList: [],
    showViewForm: false,
    authType: '',
    formMode: '',
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.SRC_IP_GROUP });
    const { load } = this.props;
    load();
  }

  getTableData = () => {
    const {
      sourceIPTableData, accessPrivileges, authType,
    } = this.props;
    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING;
    const readOnly = getReadOnly(accessPrivileges[permKey], authType);
    const tableData = sourceIPTableData.map((row) => {
      return {
        id: row.id,
        description: row.description,
        ipAddresses: row.ipAddresses,
        name: row.name,
        isDeletable: !(row.isNonEditable || row.isPredefined || row.name === 'App Connector Source IP Group') && !readOnly && row.creatorContext === 'EC',
        isEditable: !(row.isNonEditable || row.isPredefined || row.name === 'App Connector Source IP Group') && !readOnly && row.creatorContext === 'EC',
        isReadOnly: (row.isNonEditable || row.isPredefined || row.name === 'App Connector Source IP Group') || readOnly || row.creatorContext !== 'EC',
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      toggleSourceIPForm,
      showForm,
      formTitle,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      handleSourceIPView,
      accessPrivileges,
      accessSubscriptions,
      formMode,
      ipAddressesList,
      showViewForm,
      authType,
    } = this.props;

    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING;
    const isReadOnly = getReadOnly(accessPrivileges[permKey], authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_FORWARDING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="main-container">
        <div className="page-title">
          {t('IP_FQDN_GROUPS')}
        </div>
        <div className="configuration-nav-tab">
          <NavTabs
            tabConfiguration={[{
              id: 'source-ip-groups',
              title: t('SOURCE_IP_GROUPS'),
              to: `${BASE_LAYOUT}/administration/source-ip-groups`,
            },
            // {
            //   id: 'destination-ip-groups',
            //   title: t('DESTINATION_IP_GROUPS'),
            //   to: `${BASE_LAYOUT}/administration/destination-ip-groups`,
            // },
            // {
            //   id: 'ip-pool',
            //   title: t('IP_POOL'),
            //   to: `${BASE_LAYOUT}/administration/ip-pool`,
            // },
            ]} />
        </div>
        <div className={`sip-add-new-btn ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
          {!isReadOnly && <AddNewButton label={t('ADD_SOURCE_IP_GROUP')} clickCallback={() => toggleSourceIPForm(null, true, 'ADD_SOURCE_IP')} />}
        </div>
        <div className="cloud-provider-wrapper location-templates ip-fqdn-src-ip-groups">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.SRC_IP_GROUP} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...SOURCE_IP_TABLE_CONFIGS}
                  permission={accessPrivileges[permKey]}
                  onHandleRowEdit={toggleSourceIPForm}
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={handleSourceIPView}
                  data={this.getTableData()} />
                <Modal
                  title={t(formTitle)}
                  isOpen={showForm}
                  closeModal={() => toggleSourceIPForm(null, false)}>
                  <SourceIPForm formMode={formMode} ipAddressesList={ipAddressesList} />
                </Modal>
                <Modal
                  title={t('VIEW_SOURCE_IP_GROUP')}
                  isOpen={showViewForm}
                  closeModal={() => handleSourceIPView(null, false)}>
                  <SourceIPViewModal />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...SourceIPSelectors.baseSelector(state),
  modalLoading: SourceIPSelectors.modalLoadingSelector(state),
  selectedRowID: SourceIPSelectors.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  ipAddressesList: SourceIPSelectors.ipAddressesSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadSourceIPData,
    toggleSourceIPForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteSourceIP,
    handleSourceIPView: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(SourceIPGroups));
