/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import { Field, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PageTabs from 'components/navTabs/PageTabs';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku,
} from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as partnerIntegrationsAwsSelector from 'ducks/partnerIntegrationsAws/selectors';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  handlePagetype,
  handleSearchText,
  tabConfiguration,
} from 'ducks/partnerIntegrationsAws';
import { toggleAdminPanel } from 'ducks/adminNav';
import PartnerIntegrationsAwsTable from '../components/PartnerIntegrationsAwsTable';

import { ActionBar, Modals } from '../components';

let currentLocation;

function BasicCustomAppForm(props) {
  const {
    actions,
    t,
    accessPrivileges,
    accessSubscriptions,
    bulkUpdate,
  } = props;
  const location = useLocation();
  const { pathname } = location;
  currentLocation = pathname;

  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS });
    actions.toggleAdminPanel(false);
    actions.handlePagetype(pathname);
    // return () => ();
  }, [pathname]);
    
  return (
    <ServerError {...props}>
      <div className="main-container cc-group">
        <HelpArticle article={HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS} />
        <div className="header">
          <span className="component-header-cc-group">
            {t('PARTNER_INTEGRATIONS')}
          </span>
        </div>
        <Field
          id="pageTabs"
          name="pageTabs"
          component={PageTabs} />
        <div className="actions-row">
          {bulkUpdate && (
            <ActionBar
              t={t}
              handleScheduleUpgrade={() => actions.handleToggleForm('BULK_CHANGE', true)}
              handleDisable={() => actions.handleToggleDisableForm(true)}
              handleEnable={() => actions.handleToggleEnableForm(true)} />
          )}
          <div className="search-container-cc-group">
            <SearchBox
              placeholder={t('SEARCH')}
              clickCallback={(value) => actions.handleSearchText(value)} />
          </div>
        </div>

        <div className="container-row-cc-group partner-integration-table-container">
          <PartnerIntegrationsAwsTable />
          <Modals pathname={pathname} />
        </div>
          
      </div>
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    deleteBCGroupRowData: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    handlePagetype: PropTypes.func,
    handleSearchText: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    handleToggleForm: PropTypes.func,
    load: PropTypes.func,
    toggleAdminPanel: PropTypes.func,
  }),
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  bulkUpdate: PropTypes.bool,

};

BasicCustomAppForm.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleDeleteConfirmationForm: null,
    deleteBCGroupRowData: null,
  },
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
};

const BasicCustomForm = reduxForm({
  form: 'partnerIntegrationsAwsForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const PartnerIntegrationsAws = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  return ({
    initialValues: {
      pageTabs: currentLocation,
      tabConfiguration: tabConfiguration(hasBSubscription, hasCSubscription),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  bulkUpdate: partnerIntegrationsAwsSelector.bulkUpdateSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handlePagetype,
    handleSearchText,
    toggleAdminPanel,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PartnerIntegrationsAws));
