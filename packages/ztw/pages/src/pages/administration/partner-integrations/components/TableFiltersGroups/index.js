import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { uniqBy } from 'utils/lodash';
import CommonDropdown from 'components/commonDropdown';
import * as partnerIntegrationsGroupsSelector from 'ducks/partnerIntegrationsAwsGroups/selectors';
import * as partnerIntegrationsAwsSelector from 'ducks/partnerIntegrationsAws/selectors';

import {
  handleSetFilter,
} from 'ducks/partnerIntegrationsAwsGroups';

function TableFilters() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector(partnerIntegrationsGroupsSelector.baseSelector);
  const baseSelectorAccount = useSelector(partnerIntegrationsAwsSelector.baseSelector);
  const { allPages } = baseSelector;
  const { allPages: allPagesAwsAccounts = [] } = baseSelectorAccount || {};
  const publicCloudAccounts = allPages?.map((x) => x?.publicCloudAccounts)?.flat().filter((x) => x);
  const cloudConnectorGroups = allPages?.map((x) => x?.cloudConnectorGroups)?.flat().filter((x) => x);
  // Need to change ID for AccountId

  const mapAwsAccounts = new Map();
  allPagesAwsAccounts.forEach((element) => {
    mapAwsAccounts.set(element.id, element.accountDetails.awsAccountId);
  });
  const idList = mapAwsAccounts.size === 0
    ? []
    : uniqBy(publicCloudAccounts, (x) => x?.id).map(
      (x) => ({
        id: x?.id,
        label: String(mapAwsAccounts.get(x?.id) || x?.id),
        value: x?.id,
      }),
    );
  const nameList = uniqBy(publicCloudAccounts, (x) => x?.name).map((x) => ({
    id: x?.id, value: x?.name, label: x?.name,
  }));

  const cloudConnectorList = uniqBy(cloudConnectorGroups, (x) => x?.id).map((x) => (x && {
    id: x?.id, value: x?.name, label: x?.name,
  }));

  const [selectedId, setSelectedId] = useState([]);
  const [selectedName, setSelectedName] = useState([]);
  const [selectCloudConnectorGroups, setSelectCloudConnectorGroups] = useState([]);

  useEffect(() => {
    setSelectedId([]);
    setSelectedName([]);
    setSelectCloudConnectorGroups([]);
    return () => {
      setSelectedId([]);
      setSelectedName([]);
      setSelectCloudConnectorGroups([]);
    };
  }, []);

  const handleSelectedId = (value) => {
    setSelectedId(value);
    dispatch(handleSetFilter('accountId', value || []));
  };
  const handleSelectedName = (value) => {
    setSelectedName(value);
    dispatch(handleSetFilter('name', value || []));
  };
  const handleSelectedCloudConnectorGroups = (value) => {
    setSelectCloudConnectorGroups(value);
    dispatch(handleSetFilter('cloudConnectorGroups', value || []));
  };
  
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">
          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('ACCOUNT_ID_ONLY')}
            labelClassName="vpc-id-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={idList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedId !== e) {
                handleSelectedId(e);
              }
            }}
            value={selectedId}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('ACCOUNT_NAME')}
            labelClassName="namespace-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={nameList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedName !== e) {
                handleSelectedName(e);
              }
            }}
            value={selectedName}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('CLOUD_CONNECTOR_GROUP')}
            labelClassName="ip-address-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={cloudConnectorList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectCloudConnectorGroups !== e) {
                handleSelectedCloudConnectorGroups(e);
              }
            }}
            value={selectCloudConnectorGroups}
            width="224px" />
        </div>
      </div>
    </div>
  );
}

export default (TableFilters);
