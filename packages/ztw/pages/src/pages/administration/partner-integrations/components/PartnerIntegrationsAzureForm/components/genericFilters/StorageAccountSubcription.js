/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';
import AzureSubscriptionsEntity from 'commonConnectedComponents/dropdown/AzureSubscriptionsEntity';
import { handleSelectStorageAccountSubscription } from 'ducks/partnerIntegrationsAzure';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  requiredId,
  required,
} from 'utils/validations';

export function StorageAccountSubcription(props) {
  const dispatch = useDispatch();
  const {
    formValues, t, isReadOnly,
    fieldNameChecked, fieldNameStorageAccount, fieldName, fieldNameSubscription,
    groupIndex, supportedRegionIndex, fieldNameStoragePrefix,
  } = props;
  const itemName = get(formValues, fieldName, null);
  
  const valueChecked = get(formValues, fieldNameChecked, null);
   
  return valueChecked ? (
    <div className="input-container half-width">
      <Field
        id={fieldNameSubscription}
        name={fieldNameSubscription}
        component={AzureSubscriptionsEntity}
        placeholder={t('SELECT_SUBSCRIPTION')}
        props={{
          isViewOnly: isReadOnly,
          defaultDisplayLabel: 'SELECT_SUBSCRIPTION',
        }}
        onChange={(value) => {
          dispatch(handleSelectStorageAccountSubscription(
            itemName,
            value,
            formValues,
            fieldNameStorageAccount,
            groupIndex,
            supportedRegionIndex,
            fieldNameStoragePrefix,
          ));
        }}
        validate={[
          requiredId,
          required,
        ]}
        styleClass="full-width" />
    </div>
  ) : <></>;
}

StorageAccountSubcription.propTypes = {
  actions: PropTypes.shape({}),
  fieldName: PropTypes.string,
  fieldNameChecked: PropTypes.string,
  fieldNameStorageAccount: PropTypes.string,
  fieldNameStoragePrefix: PropTypes.string,
  fieldNameSubscription: PropTypes.string,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  groupIndex: PropTypes.number,
  isReadOnly: PropTypes.bool,
  itemNameSubscription: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  supportedRegionIndex: PropTypes.number,
  t: PropTypes.func,
};

StorageAccountSubcription.defaultProps = {
  actions: {},
  fieldNameChecked: '',
  fieldNameStorageAccount: '',
  fieldNameStoragePrefix: '',
  fieldName: '',
  fieldNameSubscription: '',
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(StorageAccountSubcription));
