import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { withTranslation } from 'react-i18next';

// import AddFilterDropdown from 'components/filtersMenuNew/AddFilterDropdown';
// import PrimaryButton from 'components/button/ButtonNew';
// import FiltersMenu from 'components/filtersMenuNew';
import SingleDropdown from 'components/dropDown/SingleDropdown';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAws/selectors';

import {
  handleSetFilter,
} from 'ducks/partnerIntegrationsAws';

function TableFilters() {
  const dispatch = useDispatch();
  const baseSelector = useSelector(partnerIntegrationsSelector.baseSelector);
  const { partnerIntegrationsAwsData } = baseSelector;
  const accountIdList = partnerIntegrationsAwsData.map(
    (x) => ({
      id: x.id,
      label: x.accountDetails && x.accountDetails.awsAccountId,
      value: x.accountDetails && x.accountDetails.awsAccountId,
    }),
  );
  const nameList = partnerIntegrationsAwsData.map((x) => ({
    id: x.id, value: x.name, label: x.name,
  }));
  const [selectedAccountId, setSelectedAccountId] = useState('');
  const [selectedName, setSelectedName] = useState('');
  
  const handleSelectedAccountId = (value) => {
    setSelectedAccountId(value);
    dispatch(handleSetFilter('accountId', value));
  };
  const handleSelectedName = (value) => {
    setSelectedName(value);
    dispatch(handleSetFilter('name', value));
  };
  
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">
          <SingleDropdown
            items={accountIdList}
            labelSelectedValue="ACCOUNT_ID"
            selectedValue={selectedAccountId}
            showLabelInSelectedValue
            setValue={handleSelectedAccountId}
            defaultValue={selectedAccountId} />
          <SingleDropdown
            items={nameList}
            labelSelectedValue="NAME"
            selectedValue={selectedName}
            showLabelInSelectedValue
            setValue={handleSelectedName}
            defaultValue={selectedName} />
        </div>
        
      </div>
    </div>
  );
}

export default withTranslation()(TableFilters);
