/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';

import Input from 'components/Input';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  noWhiteSpacesAllowed,
  maxLength,
} from 'utils/validations';

const maxLength128 = maxLength(128);

export function StorageAccountPrefix(props) {
  const {
    formValues, t, isReadOnly,
    fieldNameChecked, fieldNameStoragePrefix,
  } = props;
  const valueChecked = get(formValues, fieldNameChecked, null);

  return valueChecked
    ? (
      <div className="input-container half-width max-width-100px">
        <Field
          id={fieldNameStoragePrefix}
          name={fieldNameStoragePrefix}
          component={Input}
          isDisabled={isReadOnly}
          placeholder={t('DEFAULT_PREFIX')}
          validate={[
            noWhiteSpacesAllowed,
            maxLength128,
          ]}
          inputStyleClass="storage-prefix-max-width-100px" />
      </div>
    ) : <></>;
}

StorageAccountPrefix.propTypes = {
  actions: PropTypes.shape({}),
  fieldNameChecked: PropTypes.string,
  fieldNameStoragePrefix: PropTypes.string,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};

StorageAccountPrefix.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(StorageAccountPrefix));
