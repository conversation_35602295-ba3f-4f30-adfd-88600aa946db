/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';
import Loading from 'components/spinner/Loading';
import { selector as storageAccountsDropdown } from 'ducks/dropdowns/azure-storage-accounts';
import AzureStorageAccountsEntity from 'commonConnectedComponents/dropdown/AzureStorageAccountEntity';
import { handleSelectStorageAccountSubscription } from 'ducks/partnerIntegrationsAzure';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  requiredId,
  required,
} from 'utils/validations';

export function StorageAccountList(props) {
  const dispatch = useDispatch();
  const selector = useSelector((state) => storageAccountsDropdown(state).dropdown);
  const { loading } = selector;
  const {
    formValues, t, isReadOnly,
    fieldNameChecked, fieldNameStorageAccount, fieldName,
    supportedRegionIndex, itemNameSubscription,
  } = props;
  const itemName = get(formValues, fieldName, null);
  const loadingRegionStorage = get(formValues, 'loadingRegionStorage', null);
  const valueChecked = get(formValues, fieldNameChecked, null);
  const isLoading = loadingRegionStorage?.region === supportedRegionIndex
    && loadingRegionStorage?.isloading;

  return valueChecked ? (
    <Loading
      key={itemNameSubscription?.id}
      loading={!!isLoading}>
      <Loading loading={loading && !!itemNameSubscription} />
      <div key={itemNameSubscription?.id} className="input-container  half-width">
        <Field
          id={fieldNameStorageAccount}
          name={fieldNameStorageAccount}
          component={AzureStorageAccountsEntity}
          placeholder={t('SELECT_STORAGE_ACCOUNT')}
          props={{
            isViewOnly: isReadOnly,
            itemNameSubscription,
            defaultDisplayLabel: 'SELECT',
            onPreload: () => {
              dispatch(handleSelectStorageAccountSubscription(itemName, itemNameSubscription, formValues, ''));
            },
          }}
          validate={[
            requiredId,
            required,
          ]}
          styleClass="full-width no-margin-top" />
      </div>
    </Loading>
  )
    : <div className="input-container  half-width" />;
}

StorageAccountList.propTypes = {
  actions: PropTypes.shape({}),
  fieldName: PropTypes.string,
  fieldNameChecked: PropTypes.string,
  fieldNameStorageAccount: PropTypes.string,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  itemNameSubscription: PropTypes.shape({
    id: PropTypes.string,
  }),
  showFilters: PropTypes.shape({}),
  supportedRegionIndex: PropTypes.number,
  t: PropTypes.func,
};

StorageAccountList.defaultProps = {
  actions: {},
  fieldNameChecked: '',
  fieldNameStorageAccount: '',
  fieldName: '',
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(StorageAccountList));
