/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Loading from 'components/spinner/Loading';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import {
  toggleWizard,
  savePartner,
  setWizardActiveNavPage,
  handleDone,
  resetFormValues,
} from 'ducks/partnerIntegrationsAzure';

import * as partnerIntegrationsAzureSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  CCGroupNamespace,
  Credentials,
  EventGrid,
  Review,
  StorageAccount,
  SubscriptionGroup,
  WizardNav,
} from './components';

class partnerIntegrationsAzureForm extends React.Component {
  state = {
    wizardNavConfig: [
      'CREDENTIALS',
      'SUBSCRIPTION_GROUPS',
      'CLOUD_CONNECTORS_GROUP_AND_NAMESPACE',
      'EVENT_GRID',
      'STORAGE_ACCOUNT',
      'REVIEW',
    ],
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_AZURE_ACCOUNT,
    });
    Modal.setAppElement('#r-app');
  }

  componentWillUnmount() {
    const {
      actions: { toggleWizardModal },
      showAddForm,
      showViewForm,
      showEditForm,
    } = this.props;

    if (showAddForm || showViewForm || showEditForm) {
      toggleWizardModal(null, false, null, true);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = this.props;
    toggleWizardModal(null, false, null, true);
    resetReduxFormValues();
  };

  handleSetPage = (pg) => {
    // eslint-disable-next-line
    const { actions: { setPartnerIntegrationsWizardActive } } = this.props;

    setPartnerIntegrationsWizardActive(pg);
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setPartnerIntegrationsWizardActive }, activePage } = this.props;

    setPartnerIntegrationsWizardActive(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setPartnerIntegrationsWizardActive }, activePage } = this.props;

    setPartnerIntegrationsWizardActive(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setPartnerIntegrationsWizardActive }, enableGoToPage } = this.props;

    if (!enableGoToPage) {
      return;
    }

    setPartnerIntegrationsWizardActive(pageNum);
  };

  handleSubmit = async (values) => {
    const {
      actions, mode, showViewForm,
    } = this.props;
    if (!showViewForm) {
      await actions.savePartner(mode, values);
    }
    // await actions.setPartnerIntegrationsWizardActive(activePage + 1);
  };

  // handleFinish = (values) => {
  //   const { actions: { handleDoneClick }, mode } = this.props;

  //   handleDoneClick(mode, values);
  // }
 
  renderForm = (activePage, isReadOnly) => {
    switch (activePage) {
    case 0:
      return (
        <Credentials
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal} />
      );
    case 1:
      return (
        <SubscriptionGroup
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );

    case 2:
      return (
        <CCGroupNamespace
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 3:
      return (
        <EventGrid
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 4:
      return (
        <StorageAccount
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 5:
      return (
        <Review
          isReadOnly={isReadOnly}
          onSubmit={this.handleSubmit}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );

    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      showAddForm,
      showViewForm,
      showEditForm,
      loading,
    } = this.props;
    const { wizardNavConfig } = this.state;

    const title = () => {
      if (showEditForm) return t('EDIT_AZURE_TENNAT');
      if (showViewForm) return t('VIEW_AZURE_TENNAT');
      return t('ADD_AZURE_ACCOUNT');
    };

    if (!showAddForm && !showEditForm && !showViewForm) return <></>;
    return (
      <div className="edgeconnector-page partner-integrations-page  azure-form-page">
        <Loading loading={loading} />
        <div className="back-to-ec back-to-partner">
          <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
          <span className="back-to-partner-label">{title()}</span>
        </div>
        <div className="edgeconnector-modal partner-integrations-form azure-form">
          <div className="modal-content modal-body branch-provisioning-modal-content partner-integrations-modal-content">
            <HelpArticle article={HELP_ARTICLES.ADD_AZURE_ACCOUNT} />
            <WizardNav
              activePage={activePage}
              goToPage={this.goToPage}
              wizardNavConfig={wizardNavConfig} />
            {this.renderForm(activePage, showViewForm)}
          </div>
        </div>
      </div>
    );
  }
}

partnerIntegrationsAzureForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  mode: PropTypes.string,
  loading: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  t: PropTypes.func,
};

partnerIntegrationsAzureForm.defaultProps = {
  actions: null,
  activePage: 0,
  mode: 'NEW',
  showAddForm: false,
  showEditForm: false,
  showViewForm: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...partnerIntegrationsAzureSelector.baseSelector(state),
  ...getFormValues('partnerIntegrationsAzureForm')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    savePartner,
    setPartnerIntegrationsWizardActive: setWizardActiveNavPage,
    handleDoneClick: handleDone,
    resetReduxFormValues: resetFormValues,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(partnerIntegrationsAzureForm)));
