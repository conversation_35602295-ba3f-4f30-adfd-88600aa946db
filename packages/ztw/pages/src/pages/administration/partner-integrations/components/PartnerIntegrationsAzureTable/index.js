/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { PARTNER_INTEGRATIONS_TABLE_CONFIGS } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handleCheck,
  handleCheckLink,
  handleLinkSelf,
  handlePageNumber,
  handlePageSize,
  handleRefreshForm,
  handleShowRowEllipsis,
  handleToggleDeleteForm,
  handleToggleEditForm,
  handleToggleViewForm,
  loader,
  // handleSortBy,
} from 'ducks/partnerIntegrationsAzure';

import { isEmpty } from 'lodash';

function PartnerIntegrationsAzureTable(props) {
  const {
    accessPrivileges,
    authType,
    partnerIntegrationsAzureData,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    checkAll,
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};

  useEffect(() => {
    dispatch(loader(true, 1));
  }, []);

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        let mostRecentChange = row.subscriptionGroups[0];
        for (let i = 0; i < row.subscriptionGroups.length; i += 1) {
          if (row.subscriptionGroups[i] > mostRecentChange?.lastModTime) {
            mostRecentChange = row.subscriptionGroups[i];
          }
        }

        return {
          ...row,
          lastModTime: mostRecentChange?.lastModTime,
          lastModUser: mostRecentChange?.lastModUser,
          link: `./azure/details/${row.id}`,
          isEditable: !isReadOnly,
          isRefreshable: false,
          isDeletable: !isReadOnly,
          isReadOnly,
          parentId: null,
          hideIcon: true,
        };
      });
    return tableData;
  };
  
  const data = getTableData(partnerIntegrationsAzureData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(PARTNER_INTEGRATIONS_TABLE_CONFIGS(t))}
      permission={accessPrivileges[permKey]}
      onHandleRowView={(e) => dispatch(handleToggleViewForm(e, true))}
      onHandleRowEdit={async (e) => dispatch(handleToggleEditForm(e, true))}
      onHandleRowDelete={async (e) => dispatch(handleToggleDeleteForm(e, true))}
      onHandleCheck={(id) => dispatch(handleCheck(id, checkAll[pageNumber - 1]))}
      onHandleLink={(appData, name) => dispatch(
        handleCheckLink(appData, { id: appData && appData.id, name }, 1),
      )}
      onHandleLinkSelf={handleLinkSelf}
      onHandleRowRefresh={async (e) => { dispatch(handleRefreshForm(e)); }}
      onHandleRowEllipsis={async (row, position) => {
        dispatch(handleShowRowEllipsis(row, position));
      }}
      checkAll={checkAll}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={loading}
      data={data} />
  );
}

PartnerIntegrationsAzureTable.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

PartnerIntegrationsAzureTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  partnerIntegrationsAzureData: [],

};

export default PartnerIntegrationsAzureTable;
