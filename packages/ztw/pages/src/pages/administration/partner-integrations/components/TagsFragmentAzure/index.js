import React from 'react';
import PropTypes from 'prop-types';
import LineItem from 'components/LineItem';
import { useTranslation } from 'react-i18next';
import { convertWorloadTags } from 'utils/helpers';
import AzureWorkloadTagsTable from '../AzureWorkloadTagsTable';

function TagsFragmentAzure(props) {
  const { tagSetData } = props;
  const { tagSet, resources } = tagSetData || {};
  const { attributes = {}, userTags = [] } = convertWorloadTags(tagSet, resources);
  const { t } = useTranslation();
  return (
    <>
      <div className="content tags-fragment-container">
        <div className="header">{t('ATTRIBUTES')}</div>
        <div className="content-box-flex">
          {/* // Attributes */}
          <div className="left-border">
            <div className="container">
              <LineItem label={t('RESOURCE_GROUP')} value={attributes.resourceGroup || '---'} />
              <LineItem label={t('IMAGE_ID')} value={attributes.imageId || '---'} />
              <LineItem label={t('PLATFORM')} value={attributes.platformDetails || '---'} />
              <LineItem label={t('VM_ID')} value={attributes.vmId || '---'} />
              <LineItem label={t('SECURITY_GROUP_ID')} value={(attributes.groupId && attributes.groupId.join(',')) || '---'} />
            </div>
          </div>
          
          <div className="right-border">
            <div className="container">
              <LineItem label={t('VNET')} value={attributes.vpcId || '---'} />
              <LineItem label={t('SUBNET_ID')} value={attributes.subnetId || '---'} />
              <LineItem label={t('NETWORK_INTERFACE_ID')} value={attributes.eniId || '---'} />
              <LineItem label="" value="" />
              <LineItem label={t('SECURITY_GROUP_NAME')} value={(attributes.groupName && attributes.groupName.join(',')) || '---'} />
            </div>
          </div>
        </div>
      </div>
      <div className="separator" />
      <div className="header">{`${t('USER_DEFINED_TAGS')}  (${userTags.length})`}</div>
      <div className="content tags-fragment-container">
        <AzureWorkloadTagsTable
          {...props}
          workloadsTagsData={userTags} />
      </div>
    </>
  );
}
  
TagsFragmentAzure.propTypes = {
  tagSetData: PropTypes.shape({}),

};
  
TagsFragmentAzure.defaultProps = {
  tagSetData: {},
};

export default TagsFragmentAzure;
