import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { uniqBy } from 'utils/lodash';
import CommonDropdown from 'components/commonDropdown';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handleSetFilter,
} from 'ducks/partnerIntegrationsAzure';

function TableFilters() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector(partnerIntegrationsSelector.baseSelector);
  const { workloadsData: { workloads } = {} } = baseSelector;
  const vcpIdList = uniqBy(workloads, (x) => x.resources.vpcId).map(
    (x) => ({
      id: x.vpcId,
      label: x.resources && x.resources.vpcId,
      value: x.resources && x.resources.vpcId,
    }),
  );
  const namespaceList = uniqBy(workloads, 'namespace').map((x) => ({
    id: x.id, value: x.namespace, label: x.namespace,
  }));

  const ipList = uniqBy(workloads, (x) => x.networkId.ip).map((x) => ({
    id: x.id, value: x.networkId && x.networkId.ip, label: x.networkId && x.networkId.ip,
  }));

  const [selectedVnetId, setSelectedVnetId] = useState([]);
  const [selectedNamespace, setSelectedNamespace] = useState([]);
  const [selectedIp, setSelectedIp] = useState([]);

  useEffect(() => {
    setSelectedVnetId([]);
    setSelectedNamespace([]);
    setSelectedIp([]);
    return () => {
      setSelectedVnetId([]);
      setSelectedNamespace([]);
      setSelectedIp([]);
    };
  }, []);

  const handleSelectedVnetId = (value) => {
    setSelectedVnetId(value);
    dispatch(handleSetFilter('vpcId', value || []));
  };
  const handleSelectedNamespace = (value) => {
    setSelectedNamespace(value);
    dispatch(handleSetFilter('namespace', value || []));
  };
  const handleSelectedIp = (value) => {
    setSelectedIp(value);
    dispatch(handleSetFilter('ip', value || []));
  };
  
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">
          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            // dropdownRef={function dropdownRef() {}}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('VNET_ID')}
            labelClassName="vpc-id-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={vcpIdList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedVnetId !== e) {
                handleSelectedVnetId(e);
              }
            }}
            value={selectedVnetId}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            // dropdownRef={function dropdownRef() {}}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('NAMESPACE')}
            labelClassName="namespace-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={namespaceList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedNamespace !== e) {
                handleSelectedNamespace(e);
              }
            }}
            value={selectedNamespace}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('IP_ADDRESS')}
            labelClassName="ip-address-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={ipList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedIp !== e) {
                handleSelectedIp(e);
              }
            }}
            value={selectedIp}
            width="224px" />
        </div>
      </div>
    </div>
  );
}

export default (TableFilters);
