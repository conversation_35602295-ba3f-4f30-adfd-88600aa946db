import React from 'react';
import PropTypes from 'prop-types';
import LineItem from 'components/LineItem';
import { useTranslation } from 'react-i18next';
import { convertWorloadTags } from 'utils/helpers';
import AwsWorkloadTagsTable from '../AwsWorkloadTagsTable';

function TagsFragment(props) {
  const { tagSetData } = props;
  const { tagSet, resources } = tagSetData || {};
  const { attributes = {}, userTags = [] } = convertWorloadTags(tagSet, resources);
  const { t } = useTranslation();
  return (
    <>
      <div className="content tags-fragment-container">
        <div className="header">{t('ATTRIBUTES')}</div>
        <div className="content-box-flex">
          {/* // Attributes */}
          <div className="left-border">
            <div className="container">
              <LineItem label={t('INSTANCE_ROLE')} value={attributes.iamInstanceProfileArn || '---'} />
              <LineItem label={t('AMI_ID')} value={attributes.imageId || '---'} />
              <LineItem label={t('PLATFORM')} value={attributes.platformDetails || '---'} />
              <LineItem label={t('VM_ID')} value={attributes.vmId || '---'} />
              <LineItem label={t('SECURITY_GROUP_ID')} value={(attributes.groupId && attributes.groupId.join(',')) || '---'} />
            </div>
          </div>
          
          <div className="right-border">
            <div className="container">
              <LineItem label={t('VPC_ID')} value={attributes.vpcId || '---'} />
              <LineItem label={t('SUBNET_ID')} value={attributes.subnetId || '---'} />
              <LineItem label={t('NETWORK_INTERFACE_ID')} value={attributes.eniId || '---'} />
              <LineItem label="" value="" />
              <LineItem label={t('SECURITY_GROUP_NAME')} value={(attributes.groupName && attributes.groupName.join(',')) || '---'} />
            </div>
          </div>
        </div>
      </div>
      <div className="separator" />
      <div className="header">{`${t('USER_DEFINED_TAGS')}  (${userTags.length})`}</div>
      <div className="content tags-fragment-container">
        <AwsWorkloadTagsTable
          {...props}
          workloadsTagsData={userTags} />
      </div>
    </>
  );
}
  
TagsFragment.propTypes = {
  tagSetData: PropTypes.shape({}),

};
  
TagsFragment.defaultProps = {
  tagSetData: {},
};

export default TagsFragment;
