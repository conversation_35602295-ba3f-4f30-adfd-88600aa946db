import { faTachometerAlt } from '@fortawesome/pro-solid-svg-icons';
import { BASE_LAYOUT } from 'config';

import AdminManagement from './admin-management';
import ApiKeyManagement from './api-key-management';
import Appliances from './appliances';
import AuditLogs from './audit-logs';
import BranchCloudConnectorImages from './branch-connector-images';
import BranchConnectors from './branch-connectors';
import CloudConfigAdvancedSettings from './cloud-configuration-advanced-settings';
// import CellularDeploymentConfiguration from './cellular-deployment-configuration';
// import CellularUserPlaneFunction from './cellular-user-plane-function';
import CloudConnectors from './cloud-connectors';
import DeploymentTemplates from './deployment-templates';
import DestinationIpGroups from './destination-ip-groups';
import DnsGateways from './dns-gateways';
import EdgeConnectorGroups from './edgeconnector-groups';
import EdgeConnectors from './edgeconnectors';
import Gateways from './gateways';
import IPGroups from './ip-pool';
import Loading from './loading';
import Locations from './locations';
import LocationTemplates from './location-templates';
import LogAndControlGateways from './log-and-control-gateways';
import NetworkServices from './network-services';
import NssSettingsCloudFeeds from './nss-settings-cloud-feeds';
import NssSettingsFeeds from './nss-settings-feeds';
import NssSettingsServers from './nss-settings-servers';
import ProvisioningTemplates from './provisioning-templates';
import ProvisioningTemplatesBranch from './branch-provisioning-templates';
import RoleManagement from './role-management';
import SourceIpGroups from './source-ip-groups';

import VdiAgentTemplates from './vdi-agent-templates';
import VdiAgentForwardingProfile from './vdi-agent-forwarding-profile';
import VdiDeviceManagement from './vdi-device-management';
import VdiAgentApp from './vdi-agent-app';

import PartnerIntegrationsAws from './partner-integrations/aws';
import PartnerIntegrationsAwsDetails from './partner-integrations/aws/details';
import PartnerIntegrationsAzureDetails from './partner-integrations/azure/details';
import PartnerIntegrationsAzure from './partner-integrations/azure';
import PartnerIntegrationsGcp from './partner-integrations/gcp';

import ZeroTrustGateway from './zero-trust-gateway';
import ZeroTrustGatewayDetails from './zero-trust-gateway/details';

export const routes = [
  {
    isNested: false,
    path: '/administration/admin-management',
    name: 'Administration',
    icon: faTachometerAlt,
    component: AdminManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/cloud-connector-groups',
    name: 'Administration',
    icon: faTachometerAlt,
    component: CloudConnectors,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/branch-devices/physical',
    name: 'Administration',
    icon: faTachometerAlt,
    component: BranchConnectors,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/branch-devices/virtual',
    name: 'Administration',
    icon: faTachometerAlt,
    component: BranchConnectors,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/dns-gateways',
    name: 'Administration',
    icon: faTachometerAlt,
    component: DnsGateways,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/edgeconnectors',
    name: 'Administration',
    icon: faTachometerAlt,
    component: EdgeConnectors,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/edgeconnectorgroups',
    name: 'Administration',
    icon: faTachometerAlt,
    component: EdgeConnectorGroups,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/network-services',
    name: 'Administration',
    icon: faTachometerAlt,
    component: NetworkServices,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/gateways',
    name: 'Administration',
    icon: faTachometerAlt,
    component: Gateways,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/nss-settings-servers',
    name: 'Administration',
    icon: faTachometerAlt,
    component: NssSettingsServers,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/nss-settings-feeds',
    name: 'Administration',
    icon: faTachometerAlt,
    component: NssSettingsFeeds,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/nss-settings-cloud-feeds',
    name: 'Administration',
    icon: faTachometerAlt,
    component: NssSettingsCloudFeeds,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/log-and-control-gateways',
    name: 'Administration',
    icon: faTachometerAlt,
    component: LogAndControlGateways,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/locations',
    name: 'Administration',
    component: Locations,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/role-management',
    name: 'Administration',
    icon: faTachometerAlt,
    component: RoleManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/location-templates',
    name: 'Administration',
    icon: faTachometerAlt,
    component: LocationTemplates,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/provisioning-templates',
    name: 'Administration',
    icon: faTachometerAlt,
    component: ProvisioningTemplates,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/branch-provisioning-templates',
    name: 'Administration',
    icon: faTachometerAlt,
    component: ProvisioningTemplatesBranch,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/deployment-templates',
    name: 'Administration',
    icon: faTachometerAlt,
    component: DeploymentTemplates,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/branch-connector-images',
    name: 'Administration',
    icon: faTachometerAlt,
    component: BranchCloudConnectorImages,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/administration/loading',
    name: 'Administration',
    icon: faTachometerAlt,
    component: Loading,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/audit-logs',
    name: 'Administration',
    icon: faTachometerAlt,
    component: AuditLogs,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/source-ip-groups',
    name: 'Administration',
    icon: faTachometerAlt,
    component: SourceIpGroups,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/destination-ip-groups',
    name: 'Administration',
    icon: faTachometerAlt,
    component: DestinationIpGroups,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/ip-pool',
    name: 'Administration',
    icon: faTachometerAlt,
    component: IPGroups,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/api-key-management',
    name: 'Administration',
    icon: faTachometerAlt,
    component: ApiKeyManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/cloud-configuration-advanced-settings',
    name: 'Administration',
    icon: faTachometerAlt,
    component: CloudConfigAdvancedSettings,
    layout: `${BASE_LAYOUT}`,
  },
  // {
  //   path: '/administration/cellular/deployment-configuration',
  //   name: 'Administration',
  //   icon: faTachometerAlt,
  //   component: CellularDeploymentConfiguration,
  //   layout: `${BASE_LAYOUT}`,
  // },
  // {
  //   path: '/administration/cellular/user-plane-function',
  //   name: 'Administration',
  //   icon: faTachometerAlt,
  //   component: CellularUserPlaneFunction,
  //   layout: `${BASE_LAYOUT}`,
  // },
  {
    path: '/administration/appliance-management/appliances',
    name: 'Administration',
    icon: faTachometerAlt,
    component: Appliances,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/agent-templates',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiAgentTemplates,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/agent-forwarding-profile',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiAgentForwardingProfile,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/device-management/devices',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiDeviceManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/device-management/groups',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiDeviceManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/device-management',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiDeviceManagement,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/agent-app/ga',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiAgentApp,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/agent-app/la',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiAgentApp,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/vdi/agent-app',
    name: 'Administration',
    icon: faTachometerAlt,
    component: VdiAgentApp,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/partner-integrations/aws/details',
    name: 'Administration',
    icon: faTachometerAlt,
    component: PartnerIntegrationsAwsDetails,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/partner-integrations/aws',
    name: 'Administration',
    icon: faTachometerAlt,
    component: PartnerIntegrationsAws,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/partner-integrations/azure/details',
    name: 'Administration',
    icon: faTachometerAlt,
    component: PartnerIntegrationsAzureDetails,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/partner-integrations/azure',
    name: 'Administration',
    icon: faTachometerAlt,
    component: PartnerIntegrationsAzure,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/partner-integrations/gcp',
    name: 'Administration',
    icon: faTachometerAlt,
    component: PartnerIntegrationsGcp,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/zero-trust-gateway/details',
    name: 'Administration',
    icon: faTachometerAlt,
    component: ZeroTrustGatewayDetails,
    layout: `${BASE_LAYOUT}`,
  },
  {
    path: '/administration/zero-trust-gateway',
    name: 'Administration',
    icon: faTachometerAlt,
    component: ZeroTrustGateway,
    layout: `${BASE_LAYOUT}`,
  },
];

export default routes;
