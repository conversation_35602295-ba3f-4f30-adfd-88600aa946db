.ec-root-page {
.main-container {
  padding: 19px 25px;
  .page-title {
    padding-left: 0;
  }
}
.provisioning-template-page-container {
  padding: 24px;
  position: relative;
  .vdi-template-table {
    padding: 12px;
    padding-top: 0;
    margin: 0 19px 25px 25px;
    background: var(--semantic-color-background-primary);
    display: flex;
    flex-direction: column;
    .search-container {
      float: right;
      .search-box input {
        background-color: var(--semantic-color-background-pale);
      }
    }
    .vdi-sliding-drawer {
      .drawer-header {
        max-height: 32px;
        margin: 16px 32px;
        align-items: center;
        .drawer-header-title {
          font-size: 16px;
          font-weight: 500;
          margin-left: 8px;
          line-height: 20px;
          color:  var(--semantic-color-content-base-primary);
        }
        .svg-inline--fa {
          font-size: 20px;
        }
      }
      .drawer-body {
        margin: 0 32px;
        padding: 0;
        .content-tabs-container {
          padding-top: 0;
          .drawer-row {
            .header {
              font-size: 13px;
              font-weight: 500;
              line-height: 20px;
              color: #3F3F40;
            }
            .body {
              font-size: 13px;
              font-weight: 400;
              line-height: 20px;
              color: #000;

              display: flex;
              justify-content: flex-start;
              width: 100%;
              > div {
                margin-right:  24px;
              }
            }
            .table-contents  {
              div {
                display: flex;
                justify-content: space-between;
                width: 100%;
                margin: 7px 0;
                div {
                  display: inline-block;
                }
                .api-key {
                  width: 37%;
                  span {
                    display: inline-block;
                    text-overflow: ellipsis;
                    text-wrap: nowrap;
                    overflow: hidden;
                    padding-right: 20px;
                    width: 80%;
                  }
                }
                .expiry {
                  width: 37%;
                }
                .status {
                  width: 26%;
                }
              }
            }
            .table-body {
              
              display: flex;
              justify-content: space-between;
              width: 80%;
            }

            .value {
              color: #69696A;
            }
            .copy {
              color: var(--semantic-color-content-status-info-primary);
              cursor: pointer;
            }
            .table-row-header {
              font-size: 13px;
              font-style: normal;
              font-weight: 500;
              line-height: 20px;
              letter-spacing: 0.13px;
              color:  var(--semantic-color-content-base-primary);
            }
          }
          .separator {
            height: 1px;
            background-color: #e0e1e3;
            margin-bottom: 16px;
            margin-top: 16px;
          }
        }
      }
    }
    .generate-btn-wrapper {
      display: inline-block;
      margin-top: 20px;
    }
  }
  // add filters
  .filters {
    z-index: 1;
    width: calc( 100% - 44px);
    margin: 0 19px 0px 25px;
    padding: 2px 12px;
    background: var(--semantic-color-background-primary);
    position: relative;
    .filters-section {
      width: 100%;
      min-height: 44px;
      padding: 0px;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -moz-flex;
      display: -webkit-flex;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      > div {
        padding-right: 8px;
      }
      .filter-area {
        width: auto;
        margin:0;

        .add-filters {
          .drop-down-container{
            width:28px;
            height:28px;
            border-radius: 50%;
            padding:5px;
            background-color:var(--semantic-color-surface-base-primary);
            border-color:var(--semantic-color-border-base-primary);
            .fa-plus{
              position: relative;
              left: -4px;
              top: -5px;
              color: var(--semantic-color-content-interactive-primary-hover);
              font-size: 16px;
              font-weight: 500;
            }
            .add-filter-list {
              max-width: none;
            }
            .add-filter-value {
              background: none;
              border: none;
            }
          }
        }
      }
    }
    .filter-action-buttons{
        display: flex;
        position: relative;
        right: 0px;
    }
    .reset-link {
        //width: 120px;
        margin-left: 10px;
    }

    .apply-filters {
        box-shadow: none;
        //width: 80px;
        padding:0px;
        height:28px;
    }


      .flex-box {
        justify-content: space-between;
      }
      .new-filter-component{
        .filter-name {
          font-size: 13px;
          float: left;
          padding: 0 10px;
        }
        .remove-filter-icon {
            color: var(--semantic-color-content-interactive-primary-hover);
            font-size: 14px;
            cursor: pointer;
        }
        .multi-select-filters {
            width: 90% !important;
            margin-top: 10px;
        }
        .drop-down-container{
          width:unset;
          border-radius: 40px;
          background-color:var(--semantic-color-surface-base-primary);
          border-color:var(--semantic-color-border-base-primary);
        }
        .drop-down-selected-value{
          width:unset;
          display:inline-block;
          box-shadow:none;
          max-width:280px;
          padding: 3px 2px 2px 2px;
          line-height: 13px;
          background: none;
          border: none;
          .label-selected-items,
          .dropdown-selected-label {
            width: calc(100%);
            padding-left: 8px;
            span {
              font-size: 13px;
              line-height: 20px;
              &.label-selected-value {
                color:  var(--semantic-color-content-base-primary);
              }
            }
          }
          .label-selected-items {
            font-weight: 500;
            font-size: 13px;
            line-height: 20px;
          }
          .dropdown-icon {
            padding-left: 0px;
            line-height: 22px;
            visibility: hidden;
            width: 15px;
          }
      }
      .remove-filter-icon{
          margin-right:5px;
          line-height: 28px;
          display: inline-block;
          vertical-align: top;
      }
      .compliance_check {
        .drop-down-container {
          .drop-down-selected-value {

          }
        }
      }
    }
    .filter-area {
      .vdi-filter {
        display: inline-block;
        padding: 8px 0 10px 10px;
        min-height: 25px;
        .drop-down-selected-value {
          background-color: var(--semantic-color-surface-base-primary);
          padding: 4px 12px;
          border-color: var(--semantic-color-border-base-primary);
          .dropdown-icon {
            float: none;
          }
          .dropdown-selected-label {
            width: auto;
          }
        }
        .drop-down-list.open {
          z-index: 1000;
        }
      }
    }
  }
  .table-filter-label {
    font-weight: 600;
  }
}
}