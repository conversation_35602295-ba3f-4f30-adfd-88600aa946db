import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { uniqBy } from 'utils/lodash';
import CommonDropdown from 'components/commonDropdown';
// import SingleDropdown from 'components/dropDown/SingleDropdown';
// import SingleDropdownPanel from 'components/commonDropdown/SingleDropdownPanel';
import * as vdiTemplatesSelector from 'ducks/vdiTemplates/selectors';

import {
  handleSetFilter,
} from 'ducks/vdiTemplates';

function TableFilters() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector(vdiTemplatesSelector.baseSelector);
  const { vdiTemplatesData } = baseSelector;
  const systemUserList = uniqBy(vdiTemplatesData.filter((item) => item.vdiUser).map(
    (x) => {
      return ({
        id: x.vdiUser && x.vdiUser.id,
        label: x.vdiUser && x.vdiUser.name,
        value: x.vdiUser && x.vdiUser.name,
      });
    },
  ), 'id');

  const lastModifiedByList = uniqBy(vdiTemplatesData.filter((item) => item.lastModifiedBy).map(
    (x) => {
      // need to include all the items in the array
      return ({
        id: x.lastModifiedBy && x.lastModifiedBy.id,
        label: x.lastModifiedBy && x.lastModifiedBy.name,
        value: x.lastModifiedBy && x.lastModifiedBy.name,
      });
    },
  ), 'id');

  // const updatedData = uniqBy(combinedData, 'id');

  const [selectedSystemUser, setSelectedSystemUser] = useState([]);
  const [selectedLastModifiedBy, setselectedLastModifiedBy] = useState([]);

  useEffect(() => {
    setSelectedSystemUser([]);
    setselectedLastModifiedBy([]);
    return () => {
      setSelectedSystemUser([]);
      setselectedLastModifiedBy([]);
    };
  }, []);

  const handleSystemUser = (value) => {
    setSelectedSystemUser(value);
    dispatch(handleSetFilter('vdiUser', value || []));
  };
  const handleselectedLastModifiedBy = (value) => {
    setselectedLastModifiedBy(value);
    dispatch(handleSetFilter('lastModifiedBy', value || []));
  };
  
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('SYSTEM_USER')}
            labelClassName="vpc-id-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={systemUserList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedSystemUser !== e) {
                handleSystemUser(e);
              }
            }}
            value={selectedSystemUser}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('LAST_MODIFIED_BY')}
            labelClassName="namespace-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={lastModifiedByList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedLastModifiedBy !== e) {
                handleselectedLastModifiedBy(e);
              }
            }}
            value={selectedLastModifiedBy}
            width="224px" />
        </div>
        
      </div>
    </div>
  );
}

export default (TableFilters);
