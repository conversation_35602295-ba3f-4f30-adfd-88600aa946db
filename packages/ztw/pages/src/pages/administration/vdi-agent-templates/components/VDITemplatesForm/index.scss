.ec-root-page {
.vdi-form {
  padding: 20px;
  .input-container {
    margin-bottom: 16px;
    max-width: 320px;
  }
  .section-divider {
    margin: 20px 0;
    width:  640px;
    height: auto;
    border-bottom:1px solid #e9e9e9;
  }
  .form-input-row {
    margin-bottom: 16px;
    .radio-group-container {
      padding: 0;
    }
  }
  .idp-dropdown,
  .vdi-user-dropdown {
    width: 320px;
    // .drop-down-list {
    //   .list-item-search {
    //     .dropdown-search {
    //       .action-buttons {
    //         position: relative;
    //         left: -10px;
    //         .clear-button {
    //           padding-right: 8px;
    //           margin-left:  -25px;
    //         }
    //         .remove-button {
    //           margin-left:  -10px;
    //         }
    //       }
    //     }
    //   }
    // }
  }
}
.table-container {
  a {
    color: var(--semantic-color-content-status-info-primary);
  }
}
.edgeconnector-page .edgeconnector-modal {
    &.vdi-template-modal {
         .modal-content {
             min-height: 580px;
             height: 100%;
         }
    }
}
}