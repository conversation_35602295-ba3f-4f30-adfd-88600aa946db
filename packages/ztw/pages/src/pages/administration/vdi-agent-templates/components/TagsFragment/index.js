import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-regular-svg-icons';
import { noop } from 'utils/lodash';
import dayjs from 'dayjs';

import PrimaryButton from 'components/button/ButtonNew';

function TagsFragment(props) {
  const {
    tagSetData,
    handleGenerateToken,
    tokenData,
    handleCopyProvUrl,
  } = props;
  // const { tagSet, resources } = tagSetData || {};
  // const { attributes = {}, userTags = [] } = convertWorloadTags(tagSet, resources);
  const { t } = useTranslation();
  const handleClick = () => {
    handleGenerateToken(tagSetData);
  };

  const handleCopyProvUrlClick = (e) => {
    e.preventDefault();
    // const { actions } = props;
    const currentEl = e.currentTarget;
    const parentElementText = e.currentTarget.parentElement.innerText;
    const isToken = currentEl.classList.contains('token-copy');
    
    handleCopyProvUrl(e.currentTarget.parentElement, parentElementText.trim(), isToken);
  };

  const generateHtml = (data) => {
    return data.map((item) => {
      return (
        <div key={item.apikey}>
          <div className="api-key">
            <span>{item.apikey}</span>
            &nbsp;
            <FontAwesomeIcon onClick={handleCopyProvUrlClick} className="drawer-header-icon copy token-copy" icon={faCopy} />
          </div>
          <div className="expiry">{dayjs(item.expiry).format('MMMM DD, YYYY - hh:mm:ss A')}</div>
          <div className="status">---</div>
        </div>
      );
    });
  };

  return (
    <>
      <div className="content tags-fragment-container">
        <div className="drawer-row">
          <div className="header">{t('GENERAL')}</div>
          <div className="body">
            <div>
              {t('PROVISIONING_URL')}
            </div>
            <div className="value">
              {tagSetData.templateUrl}
              &nbsp;
              <FontAwesomeIcon onClick={handleCopyProvUrlClick} className="drawer-header-icon copy" icon={faCopy} />
            </div>
          </div>
        </div>
      </div>
      <div className="separator" />
      <div className="drawer-row">
        <div className="header">{t('TOKEN')}</div>
        <div className="table-body">
          <div>{t('VALUE')}</div>
          <div>{t('EXPIRY_DATE')}</div>
          <div>{t('STATUS')}</div>
        </div>
        <div className="table-contents">
          {generateHtml(tokenData)}
        </div>
      </div>
      <div className="generate-btn-wrapper">
        <PrimaryButton enable={tokenData.length < 20} label={t('GENERATE_TOKEN')} onActionCb={handleClick} />
      </div>
    </>
  );
}
  
TagsFragment.propTypes = {
  tagSetData: PropTypes.shape({}),
  handleGenerateToken: PropTypes.func,
  tokenData: PropTypes.arrayOf(),
  handleCopyProvUrl: PropTypes.shape(),
};
  
TagsFragment.defaultProps = {
  tagSetData: {},
  handleGenerateToken: noop,
  tokenData: [],
};

export default TagsFragment;
