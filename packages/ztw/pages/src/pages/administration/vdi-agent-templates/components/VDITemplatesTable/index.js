import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { VDI_TEMPLATE_TABLE_CONFIGS } from 'ducks/vdiTemplates/constants';
import ConfigTable from 'components/configTable';
import { getReadOnly } from 'utils/helpers';

class VDITemplatesTable extends PureComponent {
  static propTypes = {
    vdiTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
    handleProvUrlCopyText: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    loadMore: PropTypes.func,
    handleOpenDrawer: PropTypes.func,
  };

  static defaultProps = {
    vdiTemplatesData: null,
    handleProvUrlCopyText: null,
    toggleDeleteConfirmationForm: null,
    toggleProvTemplateWizard: null,
    handleViewProvForm: null,
    accessPrivileges: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
    loadMore: null,
  };

  getTableData = () => {
    const { vdiTemplatesData, accessPrivileges, authType } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);

    const tableData = vdiTemplatesData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        desc: row.desc,
        lastModTime: row.lastModTime,
        lastModifiedBy: row.lastModifiedBy,
        provUrlData: row.provUrlData,
        templateUrl: row.templateUrl,
        provUrlType: row.provUrlType,
        systemUser: row.systemUser,
        isReadOnly,
        isDeletable: !isReadOnly,
        isEditable: !isReadOnly,
        templateData: row.templateData,
        idp: row.idp,
        vdiUser: row.vdiUser,
        authType: row.authType,
        row,
      };
    });
    return tableData;
  };

  render() {
    const {
      toggleProvTemplateWizard,
      toggleDeleteConfirmationForm,
      handleViewProvForm,
      hasNextPage,
      loadMore,
      moreItemsLoading,
      handleOpenDrawer,
    } = this.props;

    return (
      <ConfigTable
        {...VDI_TEMPLATE_TABLE_CONFIGS}
        onHandleRowEdit={(e) => toggleProvTemplateWizard(e, true, 'EDIT')}
        onHandleRowDelete={toggleDeleteConfirmationForm}
        onHandleRowView={handleViewProvForm}
        moreItemsLoading={moreItemsLoading}
        onHandleLink={(appData) => handleOpenDrawer(appData)}
        hasNextPage={hasNextPage}
        loadMore={() => loadMore(false)}
        pagination
        data={this.getTableData()} />
    );
  }
}

export default withTranslation()(VDITemplatesTable);
