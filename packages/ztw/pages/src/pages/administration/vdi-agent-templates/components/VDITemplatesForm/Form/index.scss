@import "scss/colors.scss";

.configure-vdi-template{
  .cc-provisioning-description {
    border-radius: 4px;
    padding: 7px;
    color: var(--semantic-color-content-base-primary);
    background-color: var(--semantic-color-background-primary);
    height: 32px;
    width:  640px;
    resize: none;
    border: 1px solid var(--semantic-color-border-base-primary);
  }
  .form-textarea {
    margin: 10px 0 0;
    padding: 7px;
    box-sizing: border-box;
    height: 32px;
    width:  640px;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 0 2px 0 $grey1;
    resize: none;
  }
  .auth-type {
    padding-left: 0;
    margin-bottom: 20px;
  }
}
