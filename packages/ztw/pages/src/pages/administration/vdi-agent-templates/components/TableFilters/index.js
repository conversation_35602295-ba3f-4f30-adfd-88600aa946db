import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { withTranslation } from 'react-i18next';

import AddFilterDropdown from 'components/filtersMenuNew/AddFilterDropdown';
import PrimaryButton from 'components/button/ButtonNew';
import FiltersMenu from 'components/filtersMenuNew';
import SingleDropdown from 'components/dropDown/SingleDropdown';

import {
  addFilter,
  applyFilters,
  removeFilter,
  resetFilters,
  setFilterValue,
  // fetchFilterDropdownOptions,
  setStatusFilter,
} from 'ducks/vdiTemplates';
import {
  getFiltersArr,
  getEnableApplyFilter,
  getFiltersItems,
  getStatusList,
  getStatus,
  // getFilterValues,
  getAppliedFilterValues,
} from 'ducks/vdiTemplates/selectors';

function TableFilters({ t }) {
  const dispatch = useDispatch();
  const filters = useSelector(getFiltersArr);
  const enableApplyFilter = useSelector(getEnableApplyFilter);
  const statusData = useSelector(getStatusList);
  const selectedStatus = useSelector(getStatus);
  const filtersItems = useSelector(getFiltersItems);
  // const filterValues = useSelector(getFilterValues);
  const appliedFilter = useSelector(getAppliedFilterValues);
  const [filterReset, setFilterReset] = useState(false);

  const onCancel = () => {
    dispatch(resetFilters(true));
    setFilterReset(!filterReset);
  };

  const onApplyFilters = () => {
    dispatch(applyFilters());
    // setFilterReset(!filterReset);
  };

  const onFilterReset = () => {
    dispatch(resetFilters());
    setFilterReset(!filterReset);
  };

  const onStatusChange = (value) => {
    dispatch(setStatusFilter(value));
  };
    
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">
          <SingleDropdown
            items={statusData}
            labelSelectedValue="STATUS123"
            selectedValue={selectedStatus}
            showLabelInSelectedValue
            setValue={onStatusChange}
            defaultValue={[]} />
        </div>
        {filters.map((item, index) => (
          <div className="filter-area" key={item.value}>
            {(item.value === 'ADD_FILTER' && !!filtersItems.length)
                && (
                  <div className="add-filters">
                    <AddFilterDropdown
                      label="ADD_FILTER"
                      items={filtersItems}
                      setValue={(filterType) => dispatch(addFilter(filterType))} />
                  </div>
                )}
            
            {(item.value !== 'ADD_FILTER')
              && (
                <FiltersMenu
                  // dropdownOptions={filterDropdownOptions[item.type]}
                  // fetchFilterDropdownOptions={() => dispatch(fetchFilterDropdownOptions(
                  //   item,
                  // ))}
                  setItemCallback={(selectedFilter) => dispatch(setFilterValue(item, selectedFilter))}
                  typeSelected={{ ...item, defaultValue: appliedFilter[item.value] || [] }}
                  removeFilterCallback={() => dispatch(removeFilter(index))}
                  initialValues={appliedFilter[item.value]}
                  reset={filterReset} />
              )}
          </div>
        ))}
        {enableApplyFilter && (
          <div className="filter-action-buttons">
            <PrimaryButton
              label={t('APPLY')}
              cclass={`apply-filters ${enableApplyFilter ? '' : 'disabled preventEvent'}`}
              enable={enableApplyFilter}
              onActionCb={onApplyFilters} />
            <div
              className="reset-link"
              role="button"
              tabIndex="0"
              onKeyPress={() => onCancel()}
              onClick={() => onCancel()}>
              {t('CANCEL')}
            </div>
          </div>
        )}
        {(!enableApplyFilter && filters.length > 1)
          && (
            <div className="filter-action-buttons">
              <div
                className="reset-link"
                role="button"
                tabIndex="0"
                onKeyPress={() => onFilterReset()}
                onClick={() => onFilterReset()}>
                {t('RESET')}
              </div>
            </div>
          )}
      </div>
    </div>
  );
}

TableFilters.propTypes = {
  t: PropTypes.func,
};

TableFilters.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(TableFilters);
