import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import Modal from 'components/modal';
import AddNewButton from 'components/addNewButton';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import ServerError from 'components/errors/ServerError';
import SearchBox from 'components/searchBox';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as VDITemplatesSelectors from 'ducks/vdiTemplates/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import { isEmpty, cloneDeep, noop } from 'utils/lodash';
import Drawer from 'components/Drawer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileAlt } from '@fortawesome/pro-regular-svg-icons';

import {
  loadVDITemplates,
  deleteProvisioningTemplate,
  toggleViewModal,
  toggleDeleteForm,
  toggleWizard,
  handleCloseDrawer,
  handleOpenDrawer,
  generateToken,
  handleCopyText,
} from 'ducks/vdiTemplates';
import {
  VDITemplatesTable,
  VDITemplatesForm,
  VDITemplateViewModal,
} from './components';
import TagsFragment from './components/TagsFragment';
import TableFiltersVDITemplate from './components/TableFiltersVDITemplate';

import './index.scss';

export class VDIAgentTemplate extends Component {
  static propTypes = {
    t: PropTypes.func,
    vdiTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
    load: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    formTitle: PropTypes.string,
    showViewForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    history: PropTypes.shape(),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    loading: PropTypes.bool,
    tableFilter: PropTypes.shape(),
    openDrawer: PropTypes.bool,
    tagSetData: PropTypes.shape(),
    closeDrawerFn: PropTypes.func,
    onHandleCopyProvUrl: PropTypes.func,
  };

  static defaultProps = {
    t: (str) => str,
    vdiTemplatesData: [],
    load: noop,
    toggleProvTemplateWizard: noop,
    handleViewProvForm: noop,
    formTitle: '',
    showViewForm: false,
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
    history: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
    loading: false,
    closeDrawerFn: noop,
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.PROVISIONING_TEMPLATES });
    const { load } = this.props;
    load(true);
  }

  openModal = (event) => {
    event.preventDefault();
    // eslint-disable-next-line
    const { toggleProvTemplateWizard } = this.props;
    toggleProvTemplateWizard(null, true, 'NEW');
  };

  filteredTemplateData = () => {
    const { tableFilter, vdiTemplatesData } = this.props;
    let filteredData = cloneDeep(vdiTemplatesData);
    const { vdiUser = [], lastModifiedBy = [] } = tableFilter || {};
    const systemUserList = vdiUser.map((x) => x.value) || [];
    const lastModifiedByList = lastModifiedBy.map((x) => x.value) || [];

    if (!isEmpty(systemUserList)) {
      filteredData = filteredData.filter(
        (x) => systemUserList.includes(x.vdiUser && x.vdiUser.name),
      );
    }

    if (!isEmpty(lastModifiedByList)) {
      filteredData = filteredData.filter(
        (x) => lastModifiedByList.includes(x.lastModifiedBy && x.lastModifiedBy.name),
      );
    }

    return filteredData;
  };

  drawerHeader = () => {
    const { tagSetData, t } = this.props;
    return (
      <div className="header-details flex-box">
        <FontAwesomeIcon className="drawer-header-icon" icon={faFileAlt} />
        <span className="drawer-header-title">
          {t(tagSetData.name)}
        </span>
      </div>
    );
  };

  handleCloseDrawerModal = () => {
    const { closeDrawerFn } = this.props;
    closeDrawerFn();
  };

  render() {
    const {
      vdiTemplatesData,
      toggleProvTemplateWizard,
      handleViewProvForm,
      showViewForm,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      t,
      accessPrivileges,
      accessSubscriptions,
      authType,
      load,
      moreItemsLoading,
      hasNextPage,
      loading,
      openDrawer,
      tagSetData,
      onHandleCopyProvUrl,
    } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);
    const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE,
      constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING];

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="provisioning-template-page-container">
        <div className="page-title header-3">
          {t('VDI_AGENT_TEMPLATES')}
        </div>
        <div className="filters">
          {/* <TableFilters /> */}
          <TableFiltersVDITemplate />
        </div>
        <div className="main-container vdi-template-table">
                
          <div className={`sipg-fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
            {!isReadOnly && <AddNewButton label={t('ADD_VDI_TEMPLATE')} clickCallback={this.openModal} />}
            <div className="search-container">
              <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(true)} />
            </div>
          </div>
          <div className="cloud-provider-wrapper prov-templates">
            <RBAC privilege={permKey}>
              <Loading {...this.props} loading={loading} />
              <HelpArticle article={HELP_ARTICLES.PROVISIONING_TEMPLATES} />
              <ServerError {...this.props}>
                <VDITemplatesTable
                  authType={authType}
                  accessPrivileges={accessPrivileges}
                  toggleDeleteConfirmationForm={toggleDeleteConfirmationForm}
                  handleViewProvForm={handleViewProvForm}
                  vdiTemplatesData={this.filteredTemplateData(vdiTemplatesData)}
                  toggleProvTemplateWizard={toggleProvTemplateWizard}
                  hasNextPage={hasNextPage}
                  moreItemsLoading={moreItemsLoading}
                  handleOpenDrawer={handleOpenDrawer}
                  loadMore={load} />
                <VDITemplatesForm />
                <Modal
                  title={t('VIEW_VDI_TEMPLATE')}
                  styleClass="view-cloudconnector-provisioning-modal"
                  isOpen={showViewForm}
                  closeModal={() => handleViewProvForm(null, false)}>
                  <VDITemplateViewModal />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>

                <Drawer
                  isOpen={openDrawer}
                  customHeader={this.drawerHeader()}
                  showBackdrop
                  onClose={this.handleCloseDrawerModal}
                  className="sliding-drawer"
                  styleClass="vdi-sliding-drawer">
                  <div className="content-tabs-container">
                    <Loading loading={modalLoading} />
                    <TagsFragment
                      {...this.props}
                      handleCopyProvUrl={onHandleCopyProvUrl}
                      tagSetData={tagSetData} />
                  </div>

                </Drawer>
              </ServerError>
            </RBAC>
          </div>
        </div>
      </div>
    );
  }
}

const stateProps = (state) => {
  return {
    ...VDITemplatesSelectors.baseSelector(state),
    modalLoading: VDITemplatesSelectors.modalLoadingSelector(state),
    selectedRowID: VDITemplatesSelectors.selectedRowIDSelector(state),
    accessPrivileges: loginSelectors.accessPermissionsSelector(state),
    authType: loginSelectors.authTypeSelector(state),
    accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
    tagSetData: VDITemplatesSelectors.tagSetDataSelector(state),
    openDrawer: VDITemplatesSelectors.openDrawerSelector(state),
  };
};

const dispatchProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadVDITemplates,
    toggleProvTemplateWizard: toggleWizard,
    handleViewProvForm: toggleViewModal,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteProvisioningTemplate,
    closeDrawerFn: handleCloseDrawer,
    handleGenerateToken: generateToken,
    onHandleCopyProvUrl: handleCopyText,
  }, dispatch);
  return actions;
};

export default connect(stateProps, dispatchProps)(withTranslation()(VDIAgentTemplate));
