/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { connect, useDispatch } from 'react-redux';
import {
  hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import { HELP_ARTICLES } from 'config';
import { useLocation } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';
import AddNewButton from 'components/addNewButton';
import HelpArticle from 'components/HelpArticle';
import NavTabs from 'components/navTabs';
import PermissionRequired from 'components/PermissionRequired';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import SubscriptionRequired from 'components/subscriptionRequired';
import WorkloadDiscoveryServiceRequired from 'components/workloadDiscoveryServiceRequired';
import { dropdownActions as awsSuportedRegions } from 'ducks/dropdowns/aws-supported-regions';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh } from '@fortawesome/pro-regular-svg-icons';

import {
  addNewPartner,
  handleDone,
  handleOnSearchFilter,
  handlePagetype,
  handleRefreshForm,
  handleSearchText,
  handleToggleDeleteForm,
  handleToggleDisableForm,
  handleToggleEnableForm,
  loader,
  resetFormValues,
  toggleWizard,
} from 'ducks/zeroTrustGateway';
import {
  Modals,
  ZeroTrustGatewayTable,
  ZeroTrustGatewayForm,
  ZeroTrustGatewayViewForm,
  // TableFilters,
} from './components';
import './index.scss';

function BasicCustomAppForm(props) {
  const {
    actions,
    authType,
    t,
    accessPrivileges,
    accessSubscriptions,
    showAddForm,
    showViewForm,
    showEditForm,
    configData,
  } = props;
  const location = useLocation();
  const dispatch = useDispatch();
  const { pathname } = location;
  const hasCSubscription = hasCsku(accessSubscriptions);

  const enableWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableWorkloadDiscoveryService' });

  if (!enableWorkloadDiscoveryService) {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_ZERO_TRUST_GATEWAY} />
        <WorkloadDiscoveryServiceRequired />
      </>
    );
  }

  if (!hasCSubscription) {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_ZERO_TRUST_GATEWAY} />
        <SubscriptionRequired />
      </>
    );
  }
 
  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE'
    || accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT === 'NONE') {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_ZERO_TRUST_GATEWAY} />
        <PermissionRequired />
      </>
    );
  }
  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ANALYZING_ZERO_TRUST_GATEWAY });
    actions.handleOnSearchFilter('');
    dispatch(handlePagetype(pathname));
    dispatch(awsSuportedRegions.load());
    return () => actions.handleDone();
  }, [pathname]);

  return (
    <ServerError {...props}>
      {(!showAddForm && !showEditForm && !showViewForm) && (
        <div className="main-container zero-trust-gateway partner-integrations partner-acc-accounts">
          <HelpArticle article={HELP_ARTICLES.ABOUT_ZERO_TRUST_GATEWAY} />
          <div className="header">
            <span className="component-header-cc-group">
              {t('ZERO_TRUST_GATEWAY')}
            </span>
          </div>
          <NavTabs
            tabConfiguration={[
              {
                id: 'ztg-aws',
                title: 'AWS',
                to: '?',
              }]} />
          <br />
          <br />
          <div className="table-actions-container">
            <div className="actions-row">
              <div className="actions-items">
                <div className={`sipg-fragment ${isReadOnly ? 'hide' : ''}`}>
                  {!isReadOnly && <AddNewButton label={t('ADD_NEW_GATEWAY')} clickCallback={() => actions.addNewPartner()} />}
                </div>
              </div>
              <div className="actions-row refresh">
                <button
                  type="button"
                  className="refresh-button"
                  onClick={() => {
                    actions.handleOnSearchFilter('');
                    actions.loader(true, 1, '');
                  }}>
                  <FontAwesomeIcon className="cloud-formation-options-link-icon fa-external-link fa-xs" icon={faRefresh} size="xs" />
                  {t('REFRESH')}
                </button>

                <div className="search-container">
                  <SearchBox
                    placeholder={t('SEARCH')}
                    clickCallback={(value) => {
                      actions.handleOnSearchFilter(value);
                      actions.loader(true, 1, value);
                    }}
                    onKeyPressCb={actions.handleOnSearchFilter} />
                </div>
              </div>

            </div>

            <div className="container-row-cc-group partner-integration-table-container">
              <ZeroTrustGatewayTable {...props} />
              <Modals {...props} />
            </div>
          </div>
        </div>
      )}
      {(showAddForm || showEditForm) && <ZeroTrustGatewayForm {...props} />}
      {(showViewForm) && <ZeroTrustGatewayViewForm {...props} />}
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    addNewPartner: PropTypes.func,
    awsSuportedRegions: PropTypes.func,
    handleDone: PropTypes.func,
    handleOnSearchFilter: PropTypes.func,
    handlePagetype: PropTypes.func,
    handleRefreshForm: PropTypes.func,
    handleToggleDeleteForm: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    loader: PropTypes.func,
    resetReduxFormValues: PropTypes.func,
    toggleWizardModal: PropTypes.func,
  }),
  authType: PropTypes.string,
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  bulkUpdate: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  configData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    addNewPartner: null,
    awsSuportedRegions: null,
    handlePagetype: null,
    handleRefreshForm: null,
    handleOnSearchFilter: null,
    handleToggleDeleteForm: null,
    handleToggleDisableForm: null,
    handleToggleEnableForm: null,
    loader: null,
  },
  authType: '',
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
  showAddForm: false,
  showViewForm: false,
  showEditForm: false,
  configData: {},
};

const BasicCustomForm = reduxForm({
  form: 'zeroTrustGatewayForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const ZeroTrustGateway = connect(() => {
  return ({
    initialValues: { },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...zeroTrustGatewaySelector.baseSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    addNewPartner,
    awsSuportedRegions,
    handleDone,
    handleOnSearchFilter,
    handlePagetype,
    handleRefreshForm,
    handleSearchText,
    handleToggleDeleteForm,
    handleToggleDisableForm,
    handleToggleEnableForm,
    loader,
    resetReduxFormValues: resetFormValues,
    toggleWizardModal: toggleWizard,

  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(ZeroTrustGateway));
