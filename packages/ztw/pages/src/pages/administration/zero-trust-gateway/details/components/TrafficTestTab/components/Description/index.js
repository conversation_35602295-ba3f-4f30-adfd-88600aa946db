import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import {
  maxLength,
} from 'utils/validations';

const maxLength1024 = maxLength(1024);

function Name({
  t,
  formMeta,
  formSyncErrors,
  isReadOnly,
}) {
  const fieldName = 'description';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  return (
    <div className="input-container full-width  margin-bottom-16px">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        place="right"
        tooltip={t('')}
        text={(
          <>
            {`${t('DESCRIPTION')} (${t('OPTIONAL')})`}
            {/* <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" /> */}
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component="textarea"
        isDisabled={isReadOnly}
        placeholder={t('ENTER_TEXT')}
        className="form-textarea"
        props={{ disabled: isReadOnly }}
        validate={[
          maxLength1024,
        ]} />
    </div>
  );
}
  
Name.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
Name.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(Name);
