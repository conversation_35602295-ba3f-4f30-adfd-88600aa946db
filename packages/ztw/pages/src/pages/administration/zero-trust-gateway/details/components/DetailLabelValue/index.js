import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-regular-svg-icons';

function Details({
  title, value, styleClass, hasCopyButton,
}) {
  const { t } = useTranslation();
  const handleCopy = () => {
    navigator.clipboard.writeText(value);
  };
  return (
    <div className={`info-detail ${styleClass}`}>
      <FormFieldLabel
        id={`label_${title}`}
        tooltip={t('')}
        place="right"
        text={t(title)} />
      <p className="disabled-input">{value || '---'}</p>
      {hasCopyButton && (
        <button
          type="button"
          className="has-copy-button"
          onClick={handleCopy}>
          <FontAwesomeIcon icon={faCopy} className="fa-copy-icon" />
        </button>
      )}
    </div>
  );
}
  
Details.propTypes = {
  title: PropTypes.string,
  value: PropTypes.string,
  styleClass: PropTypes.string,
  hasCopyButton: PropTypes.bool,
};
  
Details.defaultProps = {
  title: '',
  value: '',
  styleClass: '',
  hasCopyButton: false,
};

export default Details;
