// getPeriod.test.js
import getPeriod from './getPeriod';

describe('getPeriod function', () => {
  it('returns 1 for last 5 minutes', () => {
    const timeFrame = { id: 'last_5_mins' };
    expect(getPeriod(timeFrame)).toBe(1);
  });

  it('returns 1 for last 15 minutes', () => {
    const timeFrame = { id: 'last_15_mins' };
    expect(getPeriod(timeFrame)).toBe(1);
  });

  it('returns 2 for last 30 minutes', () => {
    const timeFrame = { id: 'last_30_mins' };
    expect(getPeriod(timeFrame)).toBe(2);
  });

  it('returns 3 for last 1 hour', () => {
    const timeFrame = { id: 'last_1_hour' };
    expect(getPeriod(timeFrame)).toBe(3);
  });

  it('returns 4 for last 2 hours', () => {
    const timeFrame = { id: 'last_2_hours' };
    expect(getPeriod(timeFrame)).toBe(4);
  });

  it('returns 10 for last 5 hours', () => {
    const timeFrame = { id: 'last_5_hours' };
    expect(getPeriod(timeFrame)).toBe(10);
  });

  it('returns 20 for last 10 hours', () => {
    const timeFrame = { id: 'last_10_hours' };
    expect(getPeriod(timeFrame)).toBe(20);
  });

  it('returns 60 for last 24 hours', () => {
    const timeFrame = { id: 'last_24_hrs' };
    expect(getPeriod(timeFrame)).toBe(60);
  });

  it('returns 60 for current day', () => {
    const timeFrame = { id: 'current_day' };
    expect(getPeriod(timeFrame)).toBe(60);
  });

  it('returns 60 for previous day', () => {
    const timeFrame = { id: 'previous_day' };
    expect(getPeriod(timeFrame)).toBe(60);
  });

  it('returns 1440 for current week', () => {
    const timeFrame = { id: 'current_week' };
    expect(getPeriod(timeFrame)).toBe(1440);
  });

  it('returns 1440 for previous week', () => {
    const timeFrame = { id: 'previous_week' };
    expect(getPeriod(timeFrame)).toBe(1440);
  });

  it('returns 1440 for current month', () => {
    const timeFrame = { id: 'current_month' };
    expect(getPeriod(timeFrame)).toBe(1440);
  });

  it('returns 1440 for previous month', () => {
    const timeFrame = { id: 'previous_month' };
    expect(getPeriod(timeFrame)).toBe(1440);
  });

  it('returns 4 for time frame less than or equal to 7.5 minutes', () => {
    const timeFrame = { startTime: new Date().getTime(), endTime: new Date().getTime() + 7500000 };
    expect(getPeriod(timeFrame)).toBe(4);
  });

  it('returns 60 for time frame less than or equal to 6.09 days', () => {
    const timeFrame = { startTime: new Date().getTime(), endTime: new Date().getTime() + 609000000 };
    expect(getPeriod(timeFrame)).toBe(60);
  });

  it('returns 1440 for time frame greater than 6.09 days', () => {
    const timeFrame = { startTime: new Date().getTime(), endTime: new Date().getTime() + 609000001 };
    expect(getPeriod(timeFrame)).toBe(1440);
  });
});
