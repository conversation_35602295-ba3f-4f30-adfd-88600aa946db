import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Loading from 'components/spinner/Loading';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import * as constants from 'ducks/login/constants';
import * as loginSelectors from 'ducks/login/selectors';
import AddNewButton from 'components/button/ButtonNew';
import ConfigTableWithPaginationAndSort from 'components/configTable';
import { CONFIG_TAB_TABLE_CONFIGS } from 'ducks/zeroTrustGateway/constants';
import { isEmpty, orderBy } from 'utils/lodash';
import Modal from 'components/modal';
import {
  configStatusLoader, configStatusCheck, configStatusCompare, handleDone,
} from 'ducks/zeroTrustGateway';
import DiffViewDialog from './diffViewDialog';

function ConfigTab() {
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));

  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  
  const {
    selectedRow, configTabData,
    configTabSelectedVersions,
    showComparison,
    sortField,
    sortDirection,
    configNextPageToken,
    hasNextPage,
    moreItemsLoading,
    loading,
  } = baseSelector || {};
  const {
    id,
  } = selectedRow || {};

  const { t } = useTranslation();

  useEffect(() => {
    dispatch(configStatusLoader(true, id, null));
  }, []);

  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = orderBy(originalData, 'status', 'asc')?.map((row) => {
      return {
        ...row,
        id: row.version,
        name: row.status,
        scheduled: configTabSelectedVersions.includes(row.version),

        // to remove Icon from the line
        parentId: null,
        hideIcon: true,

      };
    });
    return tableData;
  };

  const data = getTableData(configTabData);

  const loadMore = () => {
    dispatch(configStatusLoader(false, id, configNextPageToken));
  };

  return (
    <div className="main-container partner-integrations">
      <div className="table-actions-container">
        <div className="actions-row">
          <div className="actions-items">
            <div className="sipg-fragment">
              <AddNewButton label={t('COMPARE_VERSIONS')} onActionCb={() => dispatch(configStatusCompare())} />
            </div>
          </div>

        </div>

        <div className="main-container partner-integrations">
          <div className="container-row-cc-group partner-integration-table-container">
            <Loading loading={loading} />
            <ConfigTableWithPaginationAndSort
              {...(CONFIG_TAB_TABLE_CONFIGS(t))}
              permission={accessPrivileges[permKey]}
              onHandleCheck={(e) => dispatch(configStatusCheck(e))}
              sortField={sortField}
              sortDirection={sortDirection}
              showColumnLayoutConfigurer={false}
              // loading={loading}
              moreItemsLoading={moreItemsLoading}
              hasNextPage={hasNextPage}
              loadMore={() => loadMore()}
              pagination
              data={data} />
          </div>
        </div>
      </div>

      <Modal
        title={t('COMPARE_VERSIONS')}
        styleClass="view-compare-versions-modal"
        isOpen={showComparison}
        closeModal={() => dispatch(handleDone(selectedRow))}>
        <DiffViewDialog closeModal={() => dispatch(handleDone(selectedRow))} />
      </Modal>
    </div>
  );
}

ConfigTab.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  zeroTrustGatewayData: PropTypes.arrayOf(PropTypes.shape({})),

};

ConfigTab.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  zeroTrustGatewayData: [],

};

export default ConfigTab;
