import React, { useEffect } from 'react';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import * as loginSelectors from 'ducks/login/selectors';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { getReadOnly } from 'utils/helpers';
import { FormSectionLabel } from 'components/label';
import {
  trafficTestClear, trafficTestLoader, trafficTestEnvCreate, showCreateNewTestModal,
} from 'ducks/zeroTrustGateway';
import Modal from 'components/modal';
import { isEmpty } from 'utils/lodash';
import Loading from 'components/spinner/Loading';
import TestsTable from './components/TestsTable';
import TestCreateFragment from './TestCreateFragment';

function TrafficTestTab() {
  const dispatch = useDispatch();
  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  const authType = useSelector((state) => loginSelectors.authTypeSelector(state));
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const {
    selectedRow, loading, trafficTestEnv, showCreateTestModal,
  } = baseSelector || {};
  const { environment } = trafficTestEnv || {};
  const {
    id,
  } = selectedRow || {};
  const { t } = useTranslation();

  useEffect(() => {
    dispatch(trafficTestLoader(id));
    return () => {
      dispatch(trafficTestClear());
    };
  }, []);

  const handleSubmit = (t1) => t1;

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );

  return (
    <form onSubmit={handleSubmit()} className="cloud-connector-group-form configure-provisioning-template">
      <Loading loading={loading} />
      <div className="form-sections-container">
        <FormSectionLabel text={t('TEST_ENVIRONMENT')} tooltip={t('TEST_ENVIRONMENT_TEXT')} />
        <div className="form-section">
          <div className="input-container">
            <br />
            {!isEmpty(environment) && (
              <div className="break-spaces">
                {`${t('EXPIRES')}: ${moment(environment?.endTime * 1000).format('MM/DD/YYYY HH:mm:ss')} \n${t('STATUS')}:  ${t(environment?.status)}\n\n`}
              </div>
            )}
            {!isReadOnly && (
              <button
                type="button"
                // disabled={disableCreate}
                onClick={() => dispatch(trafficTestEnvCreate(id))}
                className="submit">
                {isEmpty(environment) ? t('CREATE') : t('RENEW')}
              </button>
            )}
          </div>
        </div>
      </div>
      <div className="form-sections-container">
        <FormSectionLabel text={t('TESTS')} tooltip={t('CREATE_TEST_TEXT')} />
        <div className="form-section">
          <div className="input-container">
          </div>
          <br />
          {!isReadOnly && (
            <button
              type="button"
              // disabled={disableCreate}
              onClick={() => dispatch(showCreateNewTestModal(true))}
              className="submit">
              {t('CREATE_TEST')}
            </button>
          )}
          <br />
          <br />
          <TestsTable />
        </div>
      </div>
      <Modal
        title={t('CREATE_A_NEW_TEST')}
        styleClass="view-compare-versions-modal"
        isOpen={showCreateTestModal}
        closeModal={() => dispatch(showCreateNewTestModal(false))}>
        <TestCreateFragment closeModal={() => dispatch(showCreateNewTestModal(false))} />
      </Modal>
    </form>
  );
}

export default TrafficTestTab;
