const getPeriod = (timeFrame) => {
  const diffTime = timeFrame?.endTime - timeFrame?.startTime;
  if (timeFrame?.id === 'last_5_mins') return 1;
  if (timeFrame?.id === 'last_15_mins') return 1;
  if (timeFrame?.id === 'last_30_mins') return 2;
  if (timeFrame?.id === 'last_1_hour') return 3;
  if (timeFrame?.id === 'last_2_hours') return 4;
  if (timeFrame?.id === 'last_5_hours') return 10;
  if (timeFrame?.id === 'last_10_hours') return 20;
  if (timeFrame?.id === 'last_24_hrs') return 60;
  if (timeFrame?.id === 'current_day') return 60;
  if (timeFrame?.id === 'previous_day') return 60;
  if (timeFrame?.id === 'current_week') return 1440;
  if (timeFrame?.id === 'previous_week') return 1440;
  if (timeFrame?.id === 'current_month') return 1440;
  if (timeFrame?.id === 'previous_month') return 1440;
  if (diffTime <= 7500000) return 4;
  if (diffTime <= 609000000) return 60;
  return 1440;
};

export default getPeriod;
