import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import {
  noWhiteSpacesAllowed,
  maxLength,
  isURL,
  required,
} from 'utils/validations';

const maxLength128 = maxLength(128);

function Name({
  t, formMeta, formSyncErrors,
  isReadOnly,
}) {
  const fieldName = 'url';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  return (
    <div className="input-container full-width  margin-bottom-16px">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        place="right"
        tooltip={t('')}
        text={(
          <>
            {t('URL')}
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component={Input}
        isDisabled={isReadOnly}
        placeholder={t('ENTER_NAME')}
        styleClass="half-width with-border"
        validate={[
          noWhiteSpacesAllowed,
          maxLength128,
          required,
          isURL,
        ]} />
    </div>
  );
}

Name.propTypes = {
  reviewItems: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};

Name.defaultProps = {
  reviewItems: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(Name);
