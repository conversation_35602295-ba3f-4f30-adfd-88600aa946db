import React from 'react';
import { render } from '@testing-library/react';
import StatusLine from './StatusLine';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

describe('StatusLine component', () => {
  const data = [
    {
      availabilityZoneId: 'zone-1',
      internet: 'Healthy',
      localEgress: 'Unhealthy',
      privateApplications: 'Degraded',
    },
    {
      availabilityZoneId: 'zone-2',
      internet: 'Unhealthy',
      localEgress: 'Degraded',
      privateApplications: 'Healthy',
    },
  ];

  it('renders correctly', () => {
    const { getByText } = render(<StatusLine data={data} />);
    expect(getByText('zone-1')).toBeInTheDocument();
    expect(getByText('zone-2')).toBeInTheDocument();
  });

  it('renders correct status indicators', () => {
    const { container } = render(<StatusLine data={data} />);
    const indicators = container.querySelectorAll('span');
    expect(indicators.length).toBe(20);
    expect(indicators[2]).toHaveClass('healthy');
    expect(indicators[5]).toHaveClass('unhealthy');
    expect(indicators[8]).toHaveClass('degraded');
    expect(indicators[12]).toHaveClass('unhealthy');
    expect(indicators[15]).toHaveClass('degraded');
    expect(indicators[18]).toHaveClass('healthy');
  });

  it('renders correct status icons', () => {
    const { container } = render(<StatusLine data={data} />);
    const icons = container.querySelectorAll('svg');
    expect(icons.length).toBe(6);
    expect(icons[0]).toHaveClass('success-icon');
    expect(icons[1]).toHaveClass('fa-circle-x-mark-icon color-error-content-default');
    expect(icons[2]).toHaveClass('svg-inline--fa fa-triangle-exclamation ');
    expect(icons[3]).toHaveClass('fa-circle-x-mark-icon color-error-content-default');
    expect(icons[4]).toHaveClass('svg-inline--fa fa-triangle-exclamation ');
    expect(icons[5]).toHaveClass('success-icon');
  });

  it('renders correct status labels', () => {
    const { getAllByText } = render(<StatusLine data={data} />);
    const labels = getAllByText(/: /);
    expect(labels.length).toBe(6);
    expect(labels[0]).toHaveTextContent('INTERNET: Healthy');
    expect(labels[1]).toHaveTextContent('LOCAL_EGRESS: Unhealthy');
    expect(labels[2]).toHaveTextContent('PRIVATE_APLICATIONS: Degraded');
    expect(labels[3]).toHaveTextContent('INTERNET: Unhealthy');
    expect(labels[4]).toHaveTextContent('LOCAL_EGRESS: Degraded');
    expect(labels[5]).toHaveTextContent('PRIVATE_APLICATIONS: Healthy');
  });
});
