import React from 'react';
import { render } from '@testing-library/react';
import { createStore } from 'redux';
// import { useTranslation } from 'react-i18next';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { statusLoader } from 'ducks/zeroTrustGateway';
import { Provider, useSelector, useDispatch } from 'react-redux'; // Import useSelector and useDispatch
import StatusTab from './index';

jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway/selectors', () => ({
  baseSelector: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway', () => ({
  statusLoader: jest.fn(),
}));

describe('StatusTab component', () => {
  const mockDispatch = jest.fn();
  const mockUseSelector = jest.fn();
  const mockBaseSelector = jest.fn(() => ({
    selectedRow: { id: 1 },
    loading: false,
    statusTabData: [{
      availabilityZoneId: 'use1-az1',
      internet: 'Unhealthy',
      privateApplications: 'Healthy',
      localEgress: 'Degraded',
    }, {
      availabilityZoneId: 'use1-az2',
      internet: 'Healthy',
      privateApplications: 'Healthy',
      localEgress: 'Degraded',
    }, {
      availabilityZoneId: 'use1-az3',
      internet: 'Degraded',
      privateApplications: 'Unhealthy',
      localEgress: 'Degraded',
    }],
  }));
  const mockStatusLoader = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockDispatch.mockImplementation(() => mockDispatch);
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockBaseSelector.mockImplementation(() => ({
      selectedRow: { id: 1 },
      loading: false,
      statusTabData: [],
    }));
    mockStatusLoader.mockImplementation(() => () => Promise.resolve());
  });

  it('renders the component', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    ztgSelectors.baseSelector.mockImplementation(mockBaseSelector);
    statusLoader.mockImplementation(mockStatusLoader);

    const store = createStore(() => ({}));
    const { container } = render(
      <Provider store={store}>
        <StatusTab />
      </Provider>,
    );
    const divElement = container.querySelector('div');
    expect(divElement).toHaveClass('main-container partner-integrations');
  });

  it('dispatches the statusLoader action on mount', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    ztgSelectors.baseSelector.mockImplementation(mockBaseSelector);
    statusLoader.mockImplementation(mockStatusLoader);

    const store = createStore(() => ({}));
    render(
      <Provider store={store}>
        <StatusTab />
      </Provider>,
    );

    expect(mockDispatch).toHaveBeenCalledTimes(1);
    expect(mockStatusLoader).toHaveBeenCalledTimes(1);
  });

  it('renders the Loading component when loading is true', () => {
    mockBaseSelector.mockImplementation(() => ({
      selectedRow: { id: 1 },
      loading: true,
      statusTabData: [],
    }));

    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    ztgSelectors.baseSelector.mockImplementation(mockBaseSelector);
    statusLoader.mockImplementation(mockStatusLoader);

    const store = createStore(() => ({}));
    const { container } = render(
      <Provider store={store}>
        <StatusTab />
      </Provider>,
    );

    const divElements = container.querySelectorAll('div');
    expect(divElements[2]).toHaveClass('ecui-waiting-overlay');
  });
});
