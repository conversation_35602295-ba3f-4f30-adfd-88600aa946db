import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider, useSelector, useDispatch } from 'react-redux';
import { createStore } from 'redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import DiffViewDialog from './index';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
  withTranslation: () => (Component) => Component,
  Trans: () => <div />,
}));
  
jest.mock('utils/i18n', () => ({
  __esModule: true,
  default: {
    use: jest.fn(),
    init: jest.fn(),
    t: jest.fn(),
  },
}));
  
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

describe('DiffViewDialog component', () => {
  const mockDispatch = jest.fn();
  const mockUseSelector = jest.fn();
  const mockBaseSelector = jest.fn(() => ({}));
  const mockUseSelectorNoShow = jest.fn();
  const mockBaseSelectorNoShow = jest.fn(() => ({}));

  const closeModal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockUseSelectorNoShow.mockImplementation(mockBaseSelectorNoShow);
    mockBaseSelectorNoShow.mockImplementation(() => ({
      loading: false,
      showComparison: false,
    }));
    mockBaseSelector.mockImplementation(() => ({
      loading: false,
      showComparison: true,
      configTabSelectedVersions: ['version1', 'version2'],
      configTabCompareVersions: {
        dataBefore: {
          zia: [{
            id: 446025.0,
            name: 'Default ZIA Gat',
            slot: 1.0,
            flags: 12289.0,
            pri_addr: '147.75.88.36',
            sec_addr: '2.2.2.2',
          }],
          ecself: [{
            id: 446024.0,
            name: 'Default Connect',
            flags: 12.0,
            slot: 1.0,
            pri_addr: 'Automatic Primary Gateway',
            sec_addr: 'Automatic Secondary Gateway',
          }],
        },
        dataAfter: {
          zia: [{
            id: 446025.0,
            name: 'Default ZIA Gat',
            slot: 1.0,
            flags: 12289.0,
            pri_addr: '100.10.10.10',
            sec_addr: '1.1.1.1',
          }],
          eczpa: [{
            id: 446027.0,
            name: 'Default ZPA Gat',
            slot: 0.0,
            tenant_id: 1.44119246819950592E17,
            cloud_id: 8.0,
          }],
        },
      },
      authType: {},
    }));
  });
  const store = createStore(() => ({}));

  it('renders the component', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const { getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <DiffViewDialog closeModal={closeModal} />
        </BrowserRouter>
      </Provider>,
    );
    expect(getByText('VERSION version1')).toBeInTheDocument();
    expect(getByText('VERSION version2')).toBeInTheDocument();
    expect(getByText('CANCEL')).toBeInTheDocument();
  });

  it('calls closeModal when cancel button is clicked', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const { getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <DiffViewDialog closeModal={closeModal} />
        </BrowserRouter>
      </Provider>,
    );
    const cancelButton = getByText('CANCEL');
    fireEvent.click(cancelButton);
    expect(closeModal).toHaveBeenCalledTimes(1);
  });

  it('does not render the component when showComparison is false', () => {
    useSelector.mockImplementation(mockUseSelectorNoShow);
    useDispatch.mockImplementation(() => mockDispatch);
    const { queryByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <DiffViewDialog closeModal={closeModal} />
        </BrowserRouter>
      </Provider>,
    );
    expect(queryByText('VERSION version1')).not.toBeInTheDocument();
    expect(queryByText('VERSION version2')).not.toBeInTheDocument();
    expect(queryByText('CANCEL')).not.toBeInTheDocument();
  });
});
