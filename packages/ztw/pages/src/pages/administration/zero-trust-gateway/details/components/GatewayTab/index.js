import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import Loading from 'components/spinner/Loading';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import DetailLabelValue from '../DetailLabelValue';

function GatewayTab() {
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const { selectedRow, loading } = baseSelector || {};
  const {
    name,
    id,
    endpointServiceName,
    awsRegion,
    availabilityZoneNames,
    provData,
    egressIPs = [],
    operationalStatus,
  } = selectedRow || {};
  const {
    accountGroups,
    allowedAccounts,
    additionalAwsAccounts,
    locationName,
  } = provData || {};
  const { t } = useTranslation();
  return (
    <div className="main-container partner-integrations">
      <Loading loading={loading || !id} />
      <div className="details-container">
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ZTG_NAME" value={name} />
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ZTG_ID" value={id} />
        </div>
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ENDPOINT_SERVICE_NAME" hasCopyButton value={t(endpointServiceName)} />
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ALLOWED_ACCOUNTS" value={allowedAccounts?.map((x) => x.name).join(', ') || '---'} />
        </div>
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="REGION" value={awsRegion} />
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ALLOWED_ACCOUNT_GROUPS" value={accountGroups?.map((x) => x.name).join(', ') || '---'} />
        </div>
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="AVAILABILITY_ZONES" value={availabilityZoneNames?.map((x) => t(x)).join(', ') || '---'} />
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="ACCOUNT_LIST" value={additionalAwsAccounts?.join(', ') || '---'} />
        </div>
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="LOCATION" value={locationName} />
        </div>
        <br />
        <br />
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="PUBLIC_IPS" value="" />
        </div>
        <div className="full-width public-ip-container">
          { Object.entries(egressIPs).map(([key, value]) => {
            return <DetailLabelValue key={key} styleClass="half-width gateway-info-detail" title={key} value={value} />;
          })}
        </div>
        <div className="full-width">
          <DetailLabelValue styleClass="half-width gateway-info-detail" title="OPERATIONAL_STATUS" value={t(operationalStatus)} />
        </div>
      </div>
    </div>
  );
}

export default GatewayTab;
