import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { statusLoader } from 'ducks/zeroTrustGateway';
import Loading from 'components/spinner/Loading';
import StatusLine from './components/StatusLine';

function StatusTab() {
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const { selectedRow, loading, statusTabData } = baseSelector || {};

  const { id } = selectedRow || {};

  useEffect(() => {
    dispatch(statusLoader(id));
  }, []);

  return (
    <div className="main-container partner-integrations">
      <div className="details-container">
        <Loading loading={loading}>
          <StatusLine data={statusTabData} />
        </Loading>
      </div>
    </div>
  );
}

export default StatusTab;
