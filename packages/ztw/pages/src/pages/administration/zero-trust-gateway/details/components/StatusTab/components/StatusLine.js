import React from 'react';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle, faExclamationTriangle, faCircleXmark,
} from '@fortawesome/pro-solid-svg-icons';

const StatusLine = (props) => {
  const { t } = useTranslation();
  const { data = [] } = props;

  const getStatusClass = (status) => {
    switch (status) {
    case 'Healthy':
      return 'healthy';
    case 'Unhealthy':
      return 'unhealthy';
    case 'Degraded':
      return 'degraded';
    default:
      return '';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
    case 'Healthy':
      return (
        <FontAwesomeIcon
          icon={faCheckCircle}
          className="success-icon" />
      );
    case 'Unhealthy':
      return (
        <FontAwesomeIcon
          icon={faCircleXmark}
          className="fa-circle-x-mark-icon color-error-content-default" />
      );
    case 'Degraded':
      return <FontAwesomeIcon icon={faExclamationTriangle} />;
    default:
      return '';
    }
  };

  return (
    data.map((item) => (
      <div key={item.availabilityZoneId} className="status-line">
        <h3 className="title">{item.availabilityZoneId}</h3>
        <span className="status-indicators">
          <span className="status">
            <span className={`indicator ${getStatusClass(item.internet)}`}></span>
            {getStatusIcon(item.internet)}
            <span className="label">
              {`${t('INTERNET')}: `}
              {item.internet}
            </span>
          </span>
          <span className="status">
            <span className={`indicator ${getStatusClass(item.localEgress)}`}></span>
            {getStatusIcon(item.localEgress)}
            <span className="label">
              {`${t('LOCAL_EGRESS')}: `}
              {item.localEgress}
            </span>
          </span>
          <span className="status">
            <span className={`indicator ${getStatusClass(item.privateApplications)}`}></span>
            {getStatusIcon(item.privateApplications)}
            <span className="label">
              {`${t('PRIVATE_APLICATIONS')}: `}
              {item.privateApplications}
            </span>
          </span>
        </span>
      </div>
    ))
  );
};

export default StatusLine;
