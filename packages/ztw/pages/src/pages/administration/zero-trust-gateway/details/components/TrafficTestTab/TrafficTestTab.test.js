import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider, useSelector, useDispatch } from 'react-redux';
import { createStore } from 'redux';
import * as actions from 'ducks/zeroTrustGateway';
import TrafficTestTab from './index';

// eslint-disable-next-line func-names
jest.mock('./components/TestsTable', () => function () {
  return <div>TestsTable</div>;
});

jest.mock('components/modal', () => ({
  __esModule: true,
  default: ({ isOpen, children, ...props }) => (
    <div {...props}>
      {isOpen && children}
    </div>
  ),
}));

jest.mock('moment', () => ({
  __esModule: true,
  // eslint-disable-next-line no-unused-vars
  default: (date) => ({
    format: () => '02/01/2022 12:00:00', // example formatted date
  }),
}));

jest.mock('ducks/generics', () => ({
  createAction: jest.fn((type) => (payload) => ({ type, payload })),
  loading: jest.fn(),
  loadSuccess: jest.fn(),
  loadError: jest.fn(),
  laoding: jest.fn(),
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
  withTranslation: () => (Component) => Component,
  Trans: () => <div />,
}));
      
jest.mock('utils/i18n', () => ({
  __esModule: true,
  default: {
    use: jest.fn(),
    init: jest.fn(),
    t: jest.fn(),
  },
}));

jest.mock('ducks/login/selectors', () => ({
  accessPermissionsSelector: jest.fn(),
  authTypeSelector: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway/selectors', () => ({
  baseSelector: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway', () => ({
  trafficTestClear: jest.fn(),
  trafficTestLoader: jest.fn(),
  trafficTestEnvCreate: jest.fn(),
  showCreateNewTestModal: jest.fn(),
}));

jest.mock('utils/helpers', () => ({
  getReadOnly: jest.fn(),
}));

jest.mock('components/label', () => ({
  FormFieldLabel: jest.fn(() => <div>FormFieldLabel</div>),
  FormSectionLabel: jest.fn(() => <div>FormSectionLabel</div>),
}));

jest.mock('./TestCreateFragment', () => ({
  TestCreateFragment: jest.fn(() => <div>TestCreateFragment</div>),
}));

describe('TrafficTestTab', () => {
  const mockDispatch = jest.fn(() => ({
    actions: {
      trafficTestLoader: jest.fn(() => () => {}),
      trafficTestEnvCreate: jest.fn(() => () => {}),
      showCreateNewTestModal: jest.fn(() => () => {}),
    },
  }));
  const mockUseSelector = jest.fn();
  const mockBaseSelector = jest.fn(() => ({}));

  const initialState = {
    trafficTestEnv: {
      environment: {
        status: 'CREATED',
        endTime: '1733780323',
      },
    },
    TrafficTestData: [{
      name: 'Test123',
      id: '07a3f5c1-8433-4f7f-ab61-2e71d30e5f10',
      type: 'HTTPS',
      headers: "--header 'Content-Type: application/json'",
      url: 'www.yahoo.com',
      options: ['Verbose', ''],
      description: 'test123',
    }, {
      name: 'a',
      id: '203b9325-e56b-4e42-a73b-2b2d48e4888f',
      type: 'HTTP',
      url: 'www.google.com',
      options: ['Verbose', 'IgnoreCertificates'],
      description: '',
    }],
    hasNextPage: false,
    moreItemsLoading: false,
    loading: false,
    showCreateTestModal: false,
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'READ_WRITE',
    authType: {},
    selectedRow: { id: 'testId' },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockBaseSelector.mockImplementation(() => (initialState));
  });
  const store = createStore(() => ({}));
  
  it('renders correctly', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const { getByText } = render(
      <Provider store={store}>
        <TrafficTestTab />
      </Provider>,
    );

    expect(getByText('CREATE_TEST')).toBeInTheDocument();
    expect(getByText('TestsTable')).toBeInTheDocument();
  });

  it('calls trafficTestLoader when component mounts', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const trafficTestLoaderSpy = jest.spyOn(actions, 'trafficTestLoader');
    render(
      <Provider store={store}>
        <TrafficTestTab />
      </Provider>,
    );

    expect(mockDispatch).toHaveBeenCalledTimes(1);
    expect(trafficTestLoaderSpy).toHaveBeenCalledTimes(1);
    expect(trafficTestLoaderSpy).toHaveBeenCalledWith('testId');
  });

  it('calls trafficTestEnvCreate when create button is clicked', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const trafficTestEnvCreateSpy = jest.spyOn(actions, 'trafficTestEnvCreate');
    const { getByText } = render(
      <Provider store={store}>
        <TrafficTestTab />
      </Provider>,
    );

    const createButton = getByText('RENEW');
    fireEvent.click(createButton);

    expect(trafficTestEnvCreateSpy).toHaveBeenCalledTimes(1);
  });

  it('calls showCreateNewTestModal when create test button is clicked', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const showCreateNewTestModalSpy = jest.spyOn(actions, 'showCreateNewTestModal');
    const { getByText } = render(
      <Provider store={store}>
        <TrafficTestTab />
      </Provider>,
    );

    const createTestButton = getByText('CREATE_TEST');
    fireEvent.click(createTestButton);

    expect(showCreateNewTestModalSpy).toHaveBeenCalledTimes(1);
  });
});
