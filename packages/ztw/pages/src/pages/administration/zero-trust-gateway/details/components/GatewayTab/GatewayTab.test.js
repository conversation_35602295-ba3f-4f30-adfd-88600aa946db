import React from 'react';
import { render } from '@testing-library/react';
import { Provider, useSelector } from 'react-redux';
import { createStore } from 'redux';
import GatewayTab from './index';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
  withTranslation: () => (Component) => Component,
  Trans: () => <div />,
}));
    
jest.mock('utils/i18n', () => ({
  __esModule: true,
  default: {
    use: jest.fn(),
    init: jest.fn(),
    t: jest.fn(),
  },
}));
    
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));
  
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

describe('GatewayTab component', () => {
  const mockUseSelector = jest.fn();
  const mockBaseSelector = jest.fn(() => ({}));

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockBaseSelector.mockImplementation(() => ({
      selectedRow: {
        name: 'Test Name',
        id: 'Test ID',
        endpointServiceName: 'Test Endpoint Service Name',
        awsRegion: 'Test AWS Region',
        availabilityZoneNames: ['Test Availability Zone 1', 'Test Availability Zone 2'],
        provData: {
          accountGroups: [{ name: 'Test Account Group' }],
          allowedAccounts: [{ name: 'Test Allowed Account' }],
          additionalAwsAccounts: ['Test Additional AWS Account'],
          locationName: 'Test Location Name',
        },
        egressIPs: {
          'Test Egress IP 1': 'Test Egress IP Value 1',
          'Test Egress IP 2': 'Test Egress IP Value 2',
        },
        operationalStatus: 'Test Operational Status',
      },
      loading: false,
    }));
  });
  const store = createStore(() => ({}));

  it('renders correctly', () => {
    useSelector.mockImplementation(mockUseSelector);
    const { getByText } = render(
      <Provider store={store}>
        <GatewayTab />
      </Provider>,
    );

    expect(getByText('ZTG_NAME')).toBeInTheDocument();
    expect(getByText('Test Name')).toBeInTheDocument();
    expect(getByText('ZTG_ID')).toBeInTheDocument();
    expect(getByText('Test ID')).toBeInTheDocument();
    expect(getByText('ENDPOINT_SERVICE_NAME')).toBeInTheDocument();
    expect(getByText('Test Endpoint Service Name')).toBeInTheDocument();
    expect(getByText('ALLOWED_ACCOUNTS')).toBeInTheDocument();
    expect(getByText('Test Allowed Account')).toBeInTheDocument();
    expect(getByText('REGION')).toBeInTheDocument();
    expect(getByText('Test AWS Region')).toBeInTheDocument();
    expect(getByText('ALLOWED_ACCOUNT_GROUPS')).toBeInTheDocument();
    expect(getByText('Test Account Group')).toBeInTheDocument();
    expect(getByText('AVAILABILITY_ZONES')).toBeInTheDocument();
    expect(getByText('Test Availability Zone 1, Test Availability Zone 2')).toBeInTheDocument();
    expect(getByText('ACCOUNT_LIST')).toBeInTheDocument();
    expect(getByText('Test Additional AWS Account')).toBeInTheDocument();
    expect(getByText('LOCATION')).toBeInTheDocument();
    expect(getByText('Test Location Name')).toBeInTheDocument();
    expect(getByText('PUBLIC_IPS')).toBeInTheDocument();
    expect(getByText('Test Egress IP 1')).toBeInTheDocument();
    expect(getByText('Test Egress IP Value 1')).toBeInTheDocument();
    expect(getByText('Test Egress IP 2')).toBeInTheDocument();
    expect(getByText('Test Egress IP Value 2')).toBeInTheDocument();
    expect(getByText('OPERATIONAL_STATUS')).toBeInTheDocument();
    expect(getByText('Test Operational Status')).toBeInTheDocument();
  });

  it('renders loading state correctly', () => {
    useSelector.mockImplementation((selector) => ({
      ...selector(mockUseSelector),
      loading: true,
    }));

    const { container } = render(
      <Provider store={store}>
        <GatewayTab />
      </Provider>,
    );

    const divElements = container.querySelectorAll('div');
    expect(divElements[1]).toHaveClass('ecui-waiting-overlay');
  });
});
