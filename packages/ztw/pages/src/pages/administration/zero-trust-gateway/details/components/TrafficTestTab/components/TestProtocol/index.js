import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import TestProtocolDropdown from 'commonConnectedComponents/dropdown/ZTGTestProtocols';
import {
  requiredId,
} from 'utils/validations';

function TestProtocol({
  t, formValues, formMeta, formSyncErrors,
  isReadOnly,
}) {
  const fieldName = 'type';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  
  const hasError = meta && meta.touched && !!error;

  return (
    <div className="input-container full-width  margin-bottom-16px">
      <FormFieldLabel
        text={t('PROTOCOL')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('')} />
      {!isReadOnly
        ? (
          <Field
            id={fieldName}
            name={fieldName}
            // onChange={onChange}
            isDisabled={isReadOnly}
            component={TestProtocolDropdown}
            styleClass={`${hasError ? 'invalid' : ''}`}
            validate={[requiredId]} />
        ) : (
          <>
            <p className="disabled-input">{ value }</p>
          </>
        )}
    </div>
  );
}
  
TestProtocol.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
TestProtocol.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(TestProtocol);
