// getDateFormat.test.js
import getDateFormat from './getDateFormat';

describe('getDateFormat', () => {
  it('returns an empty string when timeFrame is empty', () => {
    expect(getDateFormat({})).toBe('');
    expect(getDateFormat(null)).toBe('');
    expect(getDateFormat(undefined)).toBe('');
  });

  it('returns HH:mm when timeFrame duration is less than or equal to 1 hour', () => {
    const timeFrame = {
      startTime: new Date('2022-01-01T00:00:00.000Z'),
      endTime: new Date('2022-01-01T01:00:00.000Z'),
    };
    expect(getDateFormat(timeFrame)).toBe('HH:mm');
  });

  it('returns HH:mm when timeFrame duration is less than or equal to 26 hours', () => {
    const timeFrame = {
      startTime: new Date('2022-01-01T00:00:00.000Z'),
      endTime: new Date('2022-01-02T02:00:00.000Z'),
    };
    expect(getDateFormat(timeFrame)).toBe('HH:mm ');
  });

  it('returns MM/DD when timeFrame duration is more than 26 hours', () => {
    const timeFrame = {
      startTime: new Date('2022-01-01T00:00:00.000Z'),
      endTime: new Date('2022-01-03T00:00:00.000Z'),
    };
    expect(getDateFormat(timeFrame)).toBe('MM/DD');
  });

  it('handles edge cases', () => {
    const timeFrame = {
      startTime: new Date('2022-01-01T00:00:00.000Z'),
      endTime: new Date('2022-01-01T00:00:00.000Z'),
    };
    expect(getDateFormat(timeFrame)).toBe('HH:mm');
  });
});
