import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider, useSelector, useDispatch } from 'react-redux'; // Import useSelector and useDispatch
import { createStore } from 'redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import * as constants from 'ducks/login/constants';
import * as actions from 'ducks/zeroTrustGateway';
import ConfigTab from './index';

jest.mock('ducks/activation', () => ({
  checkActivation: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway', () => ({
  configStatusLoader: jest.fn(() => () => {}),
  configStatusCompare: jest.fn(() => () => {}),
}));

jest.mock('utils/http', () => ({
  genericInterface: jest.fn(() => ({
    create: jest.fn(),
    read: jest.fn(),
    update: jest.fn(),
    del: jest.fn(),
  })),
}));
  
jest.mock('ducks/generics', () => ({
  createAction: jest.fn((type) => (payload) => ({ type, payload })),
  loading: jest.fn(),
  loadSuccess: jest.fn(),
  loadError: jest.fn(),
  laoding: jest.fn(),
}));

jest.mock('components/button/ButtonNew', () => ({
  __esModule: true,
  // eslint-disable-next-line no-unused-vars
  default: ({ label = 'Mock configStatusCompare', onActionCb }) => (
    // eslint-disable-next-line max-len
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
    <div onClick={onActionCb}>Mock configStatusCompare</div>
  ),
}));

jest.mock('components/modal', () => ({
  __esModule: true,
  default: ({ isOpen, children, ...props }) => (
    <div {...props}>
      {isOpen && children}
    </div>
  ),
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
  withTranslation: () => (Component) => Component,
  Trans: () => <div />,
}));
      
jest.mock('utils/i18n', () => ({
  __esModule: true,
  default: {
    use: jest.fn(),
    init: jest.fn(),
    t: jest.fn(),
  },
}));
      
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));
    
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

describe('ConfigTab component', () => {
  const mockDispatch = jest.fn(() => ({
    actions: {
      configStatusLoader: jest.fn(() => () => {}),
      configStatusCompare: jest.fn(() => () => {}),
    },
  }));
  const mockUseSelector = jest.fn();
  const mockLoginSelector = jest.fn(() => ({}));
  const mockBaseSelector = jest.fn(() => ({}));

  const initialState = {
    configTabData: [{
      configState: 'fail',
      reason: 'ZIA Config could not be applied.',
      version: '1727062166757',
      status: 'inactive',
      appliedOn: '2024-09-22T20:29:26.757819',
    }, {
      configState: 'success',
      reason: 'success',
      version: '1727062491690',
      status: 'inactive',
      appliedOn: '2024-09-22T20:34:51.690054',
    }, {
      configState: 'success',
      reason: 'success',
      version: '1727074587704',
      status: 'active',
      appliedOn: '2024-09-22T23:56:27.704378',
    }],
    configTabSelectedVersions: ['1727062166757', '1727074587704'],
    showComparison: false,
    selectedRow: { id: '**********' },
    sortField: 'asc',
    sortDirection: 'version',
    configNextPageToken: '',
    hasNextPage: false,
    moreItemsLoading: false,
    loading: false,
  
    accessPrivileges: {
      [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT]: 'READ_WRITE',
    },
  };
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockLoginSelector.mockImplementation(() => ({
      EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'READ_WRITE',
      authType: {},
    }));

    mockBaseSelector.mockImplementation(() => (initialState));
  });
  const store = createStore(() => ({}));

  it('renders correctly', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const { getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <ConfigTab />
        </BrowserRouter>
      </Provider>,
    );

    expect(getByText('Mock configStatusCompare')).toBeInTheDocument();
    expect(getByText('VERSION')).toBeInTheDocument();
  });

  it('dispatches configStatusLoader on mount', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const configStatusLoaderSpy = jest.spyOn(actions, 'configStatusLoader');

    render(
      <Provider store={store}>
        <BrowserRouter>
          <ConfigTab />
        </BrowserRouter>
      </Provider>,
    );

    expect(mockDispatch).toHaveBeenCalledTimes(1);
    expect(configStatusLoaderSpy).toHaveBeenCalledTimes(1);
    expect(configStatusLoaderSpy).toHaveBeenCalledWith(true, '**********', null);
  });

  it('renders Modal with correct props', () => {
    // eslint-disable-next-line no-unused-vars, no-shadow
    const mockUseSelector = (selector) => ({
      ...initialState,
      ...{
        loading: false,
        showComparison: true,
        configTabSelectedVersions: ['version1', 'version2'],
        configTabCompareVersions: {
          dataBefore: {
            zia: [{
              id: 446025.0,
              name: 'Default ZIA Gat',
              slot: 1.0,
              flags: 12289.0,
              pri_addr: '147.75.88.36',
              sec_addr: '2.2.2.2',
            }],
            ecself: [{
              id: 446024.0,
              name: 'Default Connect',
              flags: 12.0,
              slot: 1.0,
              pri_addr: 'Automatic Primary Gateway',
              sec_addr: 'Automatic Secondary Gateway',
            }],
          },
          dataAfter: {
            zia: [{
              id: 446025.0,
              name: 'Default ZIA Gat',
              slot: 1.0,
              flags: 12289.0,
              pri_addr: '100.10.10.10',
              sec_addr: '1.1.1.1',
            }],
            eczpa: [{
              id: 446027.0,
              name: 'Default ZPA Gat',
              slot: 0.0,
              tenant_id: 1.44119246819950592E17,
              cloud_id: 8.0,
            }],
          },
        },
        authType: {},
      },
    });
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);

    const { getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <ConfigTab />
        </BrowserRouter>
      </Provider>,
    );

    const modal = getByText('VERSION');
    expect(modal).toBeInTheDocument();
  });

  it('calls Compare button is clicked', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const configStatusCompareSpy = jest.spyOn(actions, 'configStatusCompare');
    const { getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <ConfigTab />
        </BrowserRouter>
      </Provider>,
    );

    const configStatusCompareButton = getByText('Mock configStatusCompare');
    fireEvent.click(configStatusCompareButton);

    expect(mockDispatch).toHaveBeenCalledTimes(2);
    expect(configStatusCompareSpy).toHaveBeenCalledTimes(1);
    expect(configStatusCompareSpy).toHaveBeenCalledWith();
  });
});
