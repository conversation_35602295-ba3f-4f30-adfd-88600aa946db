import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';
import { useSelector, useDispatch } from 'react-redux';
import { getReadOnly } from 'utils/helpers';
import { isEmpty } from 'utils/lodash';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { saveTest } from 'ducks/zeroTrustGateway';
import {
  Description,
  Headers,
  InsecureKey,
  Name,
  TestProtocol,
  URL,
  Verbose,
} from '../components';

export function TestCreateFragment(props) {
  const { closeModal } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const formValues = useSelector((state) => ztgSelectors.viewZeroTrustGatewaySelector(state));
  const formMeta = useSelector((state) => ztgSelectors.viewZeroTrustGatewayMetaSelector(state));
  const formSyncErrors = useSelector(
    (state) => ztgSelectors.viewZeroTrustGatewaySyncErrorsSelector(state),
  );
  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  const authType = useSelector((state) => loginSelectors.authTypeSelector(state));

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );

  const disableSave = !isEmpty(formSyncErrors);

  return (
    <>
      <div className="modal-body">
        <form className="cloud-connector-group-form configure-provisioning-template">
          <Name
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <Description
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <TestProtocol
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <URL
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <Headers
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <Verbose
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
          <InsecureKey
            isReadOnly={isReadOnly}
            formValues={formValues}
            formMeta={formMeta}
            formSyncErrors={formSyncErrors} />
        </form>
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={closeModal} className="next primary-button">{t('CANCEL')}</button>
        <button type="submit" onClick={() => dispatch(saveTest(formValues))} disabled={disableSave} className="next primary-button">{t('SAVE')}</button>
      </div>
    </>
  );
}
  
TestCreateFragment.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  closeModal: PropTypes.func,
  showFilters: PropTypes.shape({}),
};
  
TestCreateFragment.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  closeModal: null,
  showFilters: {},
};

export default (TestCreateFragment);
