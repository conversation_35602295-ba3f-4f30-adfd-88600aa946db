import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { change } from 'redux-form';
import { isEmpty } from 'utils/lodash';
import { getUnitRange } from 'utils/helpers';
import moment from 'moment-timezone';
import { useTranslation } from 'react-i18next';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { anlyticsLoader } from 'ducks/zeroTrustGateway';
import { getDateFormat, getPeriod } from './utils';
import AnalyticsStat from './components/AnalyticsStat';
import AnalyticsType from './components/AnalyticsType';
import AnalyticsTimeframe from './components/AnalyticsTimeframe';
import InsightsWidget from './components/InsightsWidget';

const testForm = 'viewZeroTrustGateway';

function AnalyticsTab() {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const formValues = useSelector((state) => ztgSelectors.viewZeroTrustGatewaySelector(state));
  const {
    selectedRow,
    anlyticsTabData,
    loading,
  } = baseSelector || {};
  const { timeFrame, analyticsType, stat } = formValues || {};

  const { id } = selectedRow || {};

  useEffect(() => {
    dispatch(change(testForm, 'stat', { id: 'sum', name: 'SUM' }));
    dispatch(change(testForm, 'analyticsType', { id: 'bytes', name: 'PROCESSED_BYTES' }));
  }, []);

  useEffect(() => {
    if (loading) return;
    if (!!timeFrame?.startTime && !!timeFrame?.endTime && !!stat && !!analyticsType) {
      dispatch(anlyticsLoader(id, timeFrame, analyticsType, stat));
    }
  }, [stat?.id, analyticsType?.id, timeFrame?.id]);

  if (timeFrame) timeFrame.period = getPeriod(timeFrame);

  const reportData = [];
  const valueList = anlyticsTabData?.values || [0];
  const { factor, scaledUnits } = getUnitRange(
    Math.max(valueList),
    analyticsType?.id === 'bytes' ? 'BYTES' : 'NUMBER',
  );
  
  const trendData = {
    // Total: anlyticsTabData?.timestamps?.map((item, i) => (
    [`(${scaledUnits}) ${t(analyticsType?.name)} (${t(stat?.name)})`]: anlyticsTabData?.timestamps?.map((item, i) => (
      {
        name: moment(anlyticsTabData?.timestamps[i] * 1000).format(getDateFormat(timeFrame)),
        orgTime: anlyticsTabData?.timestamps[i] * 1000,
        [`(${scaledUnits}) ${t(analyticsType?.name)} (${t(stat?.name)})`]: anlyticsTabData?.values[i] / factor,
        raw: anlyticsTabData?.values[i] / factor,
        unit: scaledUnits,
        unitLabel: `${stat?.name} (${scaledUnits})  `,
        type: analyticsType,
      })).sort((a, b) => {
      return a.orgTime - b.orgTime;
    }),
  };
  
  return (
    <div className="main-container partner-integrations">
      <div className="details-container">
        <div className="main-container partner-integrations">
          <InsightsWidget
            actions={null}
            reportData={reportData}
            trendData={trendData} />
        </div>
        <div className="filters-container time-frame half-width">
          {/* FILTERS */}
          <AnalyticsTimeframe />
          <AnalyticsStat />
          <AnalyticsType />
        </div>
      </div>
    </div>
  );
}

export default AnalyticsTab;
