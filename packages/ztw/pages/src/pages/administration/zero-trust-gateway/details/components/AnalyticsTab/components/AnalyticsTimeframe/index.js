import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';
import LogsTimeFrame from 'pages/analytics/components/GenericFilters/Dropdown/LogsTimeFrame';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import {
  requiredId,
} from 'utils/validations';

function AnalyticsTimeframe({
  t, formMeta, formSyncErrors,
}) {
  const fieldName = 'timeFrame';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  
  const hasError = meta && meta.touched && !!error;

  return (
    <div className="full-width  margin-bottom-16px">
      <FormFieldLabel
        text={t('TIME_FRAME')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('')} />
      <Field
        id={fieldName}
        name={fieldName}
        component={LogsTimeFrame}
        styleClass={`${hasError ? 'invalid' : ''}`}
        validate={[requiredId]}
        parse={(value) => value.id} />
 
    </div>
  );
}
  
AnalyticsTimeframe.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
AnalyticsTimeframe.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  t: (str) => str,
  actions: {},
};

export default withTranslation()(AnalyticsTimeframe);
