// @flow
import React from 'react';
import PropTypes from 'prop-types';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import LineChartBasic from 'components/lineChartBasic';

import './index.scss';

const getWidget = (props) => {
  const {
    trendData,
    totalTrendDataKeys,
  } = props;

  return (
    <LineChartBasic
      {...props}
      data={trendData}
      dataKeys={totalTrendDataKeys}
      noMoreInfo
      height="525" />
  );
};

export function InsightsWidget(props) {
  // eslint-disable-next-line prefer-destructuring
  return (
    <ServerError {...props}>
      <div className="insights-container">
        <div className="height-40em">
          <Loading {...props}>
            {getWidget(props)}
          </Loading>
        </div>
      </div>
    </ServerError>
  );
}

InsightsWidget.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  history: PropTypes.arrayOf(PropTypes.shape({
    chart: PropTypes.string,
  })),
  cardId: PropTypes.number,
  loading: PropTypes.number,
};

InsightsWidget.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  history: [],
  cardId: 0,
  loading: false,
};

export default InsightsWidget;
