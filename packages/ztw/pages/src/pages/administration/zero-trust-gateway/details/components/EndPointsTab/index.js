import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import * as constants from 'ducks/login/constants';
import * as loginSelectors from 'ducks/login/selectors';
import { getReadOnly } from 'utils/helpers';
import ConfigTableWithPaginationAndSort from 'components/configTable';
import { ENDPOINTS_TAB_TABLE_CONFIGS } from 'ducks/zeroTrustGateway/constants';

function EndPointsTab() {
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  const authType = useSelector((state) => loginSelectors.authTypeSelector(state));
  const {
    selectedRow,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};
  const {
    endpoints,
  } = selectedRow || {};

  const { t } = useTranslation();

  // eslint-disable-next-line no-unused-vars
  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  return (
    <div className="main-container partner-integrations">
      <div className="table-actions-container">
        <div className="container-row-cc-group partner-integration-table-container">
          <ConfigTableWithPaginationAndSort
            {...(ENDPOINTS_TAB_TABLE_CONFIGS(t))}
            permission={accessPrivileges[permKey]}
            tableHeight={20000}
            maxTableHeight="20000px"
            sortField={sortField}
            sortDirection={sortDirection}
            showColumnLayoutConfigurer={false}
            numberOfLines={numberOfLines}
            loading={loading}
            data={endpoints} />
        </div>
      </div>
    </div>
  );
}

export default EndPointsTab;
