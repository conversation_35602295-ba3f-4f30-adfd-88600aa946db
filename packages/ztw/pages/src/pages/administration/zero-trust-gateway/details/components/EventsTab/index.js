import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { Field } from 'redux-form';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import { eventsLoader } from 'ducks/zeroTrustGateway';
import LogsTimeFrame from 'pages/analytics/components/GenericFilters/Dropdown/LogsTimeFrame';
import EventsTabTable from './components/EventsTabTable';

const pageSize = 25;

function EventsTab() {
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const formValues = useSelector((state) => ztgSelectors.viewZeroTrustGatewaySelector(state));
  const {
    selectedRow,
    loading,
  } = baseSelector || {};
  const { timeFrame } = formValues || {};
  const { id: timeframeId } = timeFrame || {};
  const { id } = selectedRow || {};

  const { t } = useTranslation();
  
  useEffect(() => {
    if (loading) return;
    if (timeFrame?.startTime && timeFrame?.endTime) {
      dispatch(eventsLoader(true, id, pageSize, timeFrame?.startTime, timeFrame?.endTime, null));
    }
  }, [timeframeId]);

  return (
    <div className="main-container partner-integrations">
      <div className="details-container">
        <div className="main-container time-frame">
          {/* TimeFrame */}
          <div className="filter-box half-width">
            <div className="filter-sideheader">
              <span>{t('TIME_FRAME')}</span>
            </div>
            <div className="filter-card-small">
              <Field
                id="timeFrame"
                name="timeFrame"
                component={LogsTimeFrame}
                parse={(value) => value.id} />
            </div>
          </div>
        </div>
        <div className="main-container partner-integrations">
          <EventsTabTable />
        </div>
      </div>
    </div>
  );
}

export default EventsTab;
