/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { reduxForm, Field } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { BASE_LAYOUT, HELP_ARTICLES } from 'config';
import PageTabs from 'components/navTabs/PageTabs';
import Loading from 'components/spinner/Loading';
import { useSelector, useDispatch, connect } from 'react-redux';
import { isEmpty } from 'utils/lodash';
import { NavLink, useNavigate } from 'react-router-dom';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import HelpArticle from 'components/HelpArticle';
import ServerError from 'components/errors/ServerError';
import {
  faChevronRight,
} from '@fortawesome/pro-regular-svg-icons';
import {
  handleDone,
  tabConfiguration,
} from 'ducks/zeroTrustGateway';
import {
  ConfigTab,
  EndPointsTab,
  EventsTab,
  GatewayTab,
  StatusTab,
  TrafficTestTab,
} from './components';
import AnalyticsTab from './components/AnalyticsTab';

function BasicCustomForm(props) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const viewZeroTrustGateway = useSelector(
    (state) => ztgSelectors.viewZeroTrustGatewaySelector(state),
  );
  const { selectedRow, zeroTrustGatewayData, loading } = baseSelector || {};
  const {
    name,
  } = selectedRow || {};
  const { pageTabs } = viewZeroTrustGateway || {};
  
  useEffect(() => {
    if (isEmpty(zeroTrustGatewayData)) {
      navigate(`${BASE_LAYOUT}/administration/zero-trust-gateway`);
    }
    return () => dispatch(handleDone());
  }, []);

  return (
    <ServerError {...props}>
      <Loading loading={loading} />
      <div className="main-container zero-trust-gateway-detail-container">
        <HelpArticle article={HELP_ARTICLES.ANALYZING_ZERO_TRUST_GATEWAY_DETAILS} />
        <div className="header">
          <div className="header-ztg-details">
            <NavLink
              className="form-link-text text-decoration-none"
              to={`${BASE_LAYOUT}/administration/zero-trust-gateway`}>
              <span className="partner-label">
                AWS
              </span>
            </NavLink>
            <FontAwesomeIcon className="chevron-label" icon={faChevronRight} />
            <span className="account-label">
              {`${name}`}
            </span>
            
          </div>
        </div>

        <div className="form-tab-group">
          <Field
            id="pageTabs"
            name="pageTabs"
            component={PageTabs} />
        </div>
        {pageTabs === 'ANALYTICS' && <AnalyticsTab />}
        {pageTabs === 'CONFIG' && <ConfigTab />}
        {pageTabs === 'ENDPOINTS' && <EndPointsTab />}
        {pageTabs === 'EVENTS' && <EventsTab />}
        {pageTabs === 'GATEWAY' && <GatewayTab />}
        {pageTabs === 'STATUS' && <StatusTab />}
        {pageTabs === 'TRAFFIC_TEST' && <TrafficTestTab />}
      </div>
    </ServerError>
  );
}

const mapStateToProps = () => ({
  initialValues: {
    pageTabs: 'GATEWAY',
    tabConfiguration,
  },
});

const ZeroTrustGatewayDetails = connect(mapStateToProps, null)(reduxForm({
  form: 'viewZeroTrustGateway',
  destroyOnUnmount: true,
  forceUnregisterOnUnmount: true,
  keepDirtyOnReinitialize: true,
})(BasicCustomForm));

export default ZeroTrustGatewayDetails;
