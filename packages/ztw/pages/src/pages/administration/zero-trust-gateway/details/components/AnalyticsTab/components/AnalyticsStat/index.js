import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import { EntityDropdown } from 'components/entityDropdown';
import {
  requiredId,
} from 'utils/validations';

const TYPES = [
  { id: 'avg', name: 'AVERAGE' },
  { id: 'max', name: 'MAX' },
  { id: 'sum', name: 'SUM' },
];

function ZTGAnalyticsStatDropDown(props) {
  return <EntityDropdown data={TYPES} value={TYPES[2]} {...props} />;
}

function AnalyticsStat({
  t, formMeta, formSyncErrors,
}) {
  const fieldName = 'stat';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  
  const hasError = meta && meta.touched && !!error;

  return (
    <div className="full-width  margin-bottom-16px">
      <FormFieldLabel
        text={t('STAT')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('')} />
      <Field
        id={fieldName}
        name={fieldName}
        component={ZTGAnalyticsStatDropDown}
        styleClass={`${hasError ? 'invalid' : ''}`}
        validate={[requiredId]} />
 
    </div>
  );
}
  
AnalyticsStat.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
AnalyticsStat.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  t: (str) => str,
  actions: {},
};

export default withTranslation()(AnalyticsStat);
