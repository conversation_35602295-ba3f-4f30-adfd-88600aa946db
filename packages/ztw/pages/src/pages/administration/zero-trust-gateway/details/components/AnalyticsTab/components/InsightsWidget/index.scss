@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.insights-container {
  background: #fff;
  border: 1px solid #E0E1E3;
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  margin-top: 1em;

  .container-row{
    margin: 3rem 0;
  }
  
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
  }
  .main-container>.pie-container> {
    width: 100%;
  }
}

