import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { isEmpty } from 'utils/lodash';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import * as constants from 'ducks/login/constants';
import ConfigTable from 'components/configTable';
import { getReadOnly } from 'utils/helpers';
import { runTest, handleDeleteTest, handleEditTest } from 'ducks/zeroTrustGateway';
import { TRAFFIC_TEST_TAB_TABLE_CONFIGS } from 'ducks/zeroTrustGateway/constants';

function TestsTabTable() {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  const authType = useSelector((state) => loginSelectors.authTypeSelector(state));
  const {
    trafficTestsData, trafficTestEnv,
  } = baseSelector || {};
  const { environment } = trafficTestEnv || {};

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData?.map((row) => {
      return {
        ...row,
        isEditable: !isReadOnly,
        isDeletable: !isReadOnly,
        isRunable: !isEmpty(environment),
        // to remove Icon from the line
        parentId: null,
        hideIcon: true,

      };
    });
    return tableData;
  };

  const data = getTableData(trafficTestsData);
  return (
    <div className="partner-integration-table-container">
      <ConfigTable
        {...(TRAFFIC_TEST_TAB_TABLE_CONFIGS(t))}
        permission={accessPrivileges[permKey]}
        onHandleRowRun={(e) => dispatch(runTest(e))}
        onHandleRowEdit={async (e) => { dispatch(handleEditTest(e)); }}
        onHandleRowDelete={async (e) => { dispatch(handleDeleteTest(e)); }}
        // moreItemsLoading={moreItemsLoading}
        // hasNextPage={hasNextPage}
        // loadMore={() => loadMore()}
        // pagination
        data={data} />
    </div>
  );
}

export default TestsTabTable;
