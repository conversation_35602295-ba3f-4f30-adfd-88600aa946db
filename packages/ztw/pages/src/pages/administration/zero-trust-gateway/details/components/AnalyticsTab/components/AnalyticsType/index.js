import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import { EntityDropdown } from 'components/entityDropdown';
import {
  requiredId,
} from 'utils/validations';

const TYPES = [
  { id: 'bytes', name: 'PROCESSED_BYTES' },
  { id: 'flow', name: 'ACTIVE_CONNECTION' },
  { id: 'rst', name: 'RESET_COUNT' },
];

function ZTGAnalyticsTypeDropDown(props) {
  return <EntityDropdown data={TYPES} value={TYPES[0]} {...props} />;
}

function AnalyticsType({
  t, formMeta, formSyncErrors,
}) {
  const fieldName = 'analyticsType';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  
  const hasError = meta && meta.touched && !!error;

  return (
    <div className="full-width  margin-bottom-16px">
      <FormFieldLabel
        text={t('TYPE')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('')} />
      <Field
        id={fieldName}
        name={fieldName}
        component={ZTGAnalyticsTypeDropDown}
        styleClass={`${hasError ? 'invalid' : ''}`}
        validate={[requiredId]} />

    </div>
  );
}
  
AnalyticsType.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
AnalyticsType.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  t: (str) => str,
  actions: {},
};

export default withTranslation()(AnalyticsType);
