import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { isEmpty } from 'utils/lodash';
import * as ztgSelectors from 'ducks/zeroTrustGateway/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import * as constants from 'ducks/login/constants';
import { eventsLoader } from 'ducks/zeroTrustGateway';
import ConfigTable from 'components/configTable';
import { EVENTS_TAB_TABLE_CONFIGS } from 'ducks/zeroTrustGateway/constants';

const pageSize = 25;

function EventsTabTable() {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const baseSelector = useSelector((state) => ztgSelectors.baseSelector(state));
  const accessPrivileges = useSelector((state) => loginSelectors.accessPermissionsSelector(state));
  const formValues = useSelector((state) => ztgSelectors.viewZeroTrustGatewaySelector(state));
  const {
    selectedRow,
    eventsTabData,
    eventsNextPageToken,
    hasNextPage,
    moreItemsLoading,
  } = baseSelector || {};
  const { timeFrame } = formValues || {};
  const { startTime, endTime } = timeFrame || {};
  const { id } = selectedRow || {};

  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData?.map((row) => {
      return {
        ...row,

        // to remove Icon from the line
        parentId: null,
        hideIcon: true,

      };
    });
    return tableData;
  };

  const data = getTableData(eventsTabData);
  const loadMore = () => {
    dispatch(eventsLoader(false, id, pageSize, startTime, endTime, eventsNextPageToken));
  };

  return (
    <div className="container-row-cc-group partner-integration-table-container">
      <ConfigTable
        {...(EVENTS_TAB_TABLE_CONFIGS(t))}
        permission={accessPrivileges[permKey]}
        moreItemsLoading={moreItemsLoading}
        hasNextPage={hasNextPage}
        loadMore={() => loadMore()}
        pagination
        data={data} />
    </div>
  );
}

export default EventsTabTable;
