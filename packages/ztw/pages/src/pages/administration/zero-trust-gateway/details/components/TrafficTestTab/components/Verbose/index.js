// @flow
import React from 'react';
import { Field } from 'redux-form';
import { ToggleCheckBox } from 'components/ecToggle';
import { FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
 
export function Verbose({
  t,
  isReadOnly,
}) {
  const fieldName = 'verbose';
  return (
    <div className="input-container full-width  margin-bottom-16px">
      <FormFieldLabel text={`${t('VERBOSE')} (-v):`} tooltip={t('')} place="right" />
      <Field
        id={fieldName}
        name={fieldName}
        type="checkbox"
        component={ToggleCheckBox}
        props={{ disabled: isReadOnly }}
        styleClass="ec-toggle-checkbox" />
    </div>
  );
}

Verbose.propTypes = {
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};

Verbose.defaultProps = {
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  t: (str) => str,
  actions: {},
};

export default (withTranslation()(Verbose));
