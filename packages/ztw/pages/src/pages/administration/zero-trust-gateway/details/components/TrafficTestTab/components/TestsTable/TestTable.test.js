import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { Provider, useSelector, useDispatch } from 'react-redux'; // Import useSelector and useDispatch
import { createStore } from 'redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { runTest, handleDeleteTest, handleEditTest } from 'ducks/zeroTrustGateway';
import TestsTabTable from './index';

jest.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key) => key }),
  withTranslation: () => (Component) => Component,
  Trans: () => <div />,
}));

jest.mock('utils/i18n', () => ({
  __esModule: true,
  default: {
    use: jest.fn(),
    init: jest.fn(),
    t: jest.fn(),
  },
}));

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

jest.mock('ducks/zeroTrustGateway/selectors', () => ({
  baseSelector: jest.fn(),
}));

jest.mock('ducks/login/selectors', () => ({
  accessPermissionsSelector: jest.fn(() => ({
    accessPrivileges: {
      EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'READ_WRITE',
    },
  })),
  authTypeSelector: jest.fn(() => ({})),
}));

jest.mock('utils/helpers', () => ({
  // ...jest.requireActual('utils/helpers'),
  getReadOnly: jest.fn(() => false),
  has5Gsku: jest.fn(() => true),
  verifyConfigData: jest.fn(() => true),
}));

jest.mock('ducks/zeroTrustGateway', () => ({
  runTest: jest.fn(() => () => {}),
  handleDeleteTest: jest.fn(() => () => {}),
  handleEditTest: jest.fn(() => () => {}),
}));

describe('TestsTabTable component', () => {
  const mockDispatch = jest.fn();
  const mockUseSelector = jest.fn();
  const mockLoginSelector = jest.fn(() => ({}));
  const mockBaseSelector = jest.fn(() => ({}));

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSelector.mockImplementation(mockBaseSelector);
    mockLoginSelector.mockImplementation(() => ({
      EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'READ_WRITE',
      authType: {},
    }));
    mockBaseSelector.mockImplementation(() => ({
      loading: false,
      EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: 'READ_WRITE',
      authType: {},
      trafficTestsData: [{
        name: 'Test123',
        id: '07a3f5c1-8433-4f7f-ab61-2e71d30e5f10',
        type: 'HTTPS',
        url: 'www.yahoo.com',
        options: ['Verbose', ''],
        description: 'test123',
      }],
      trafficTestEnv: {
        environment: {
          status: 'CREATED',
          endTime: '2024',
        },
      },
    }));
  });
  const store = createStore(() => ({}));

  it('renders correctly', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    const { container, getByText } = render(
      <Provider store={store}>
        <BrowserRouter>
          <TestsTabTable />
        </BrowserRouter>
      </Provider>,
    );
    const configTable = getByText('Test123');
    expect(configTable).toBeInTheDocument();
    const divElement = container.querySelector('div');
    expect(divElement).toHaveClass('partner-integration-table-container');
  });
  
  it('calls handleDeleteTest on handleRowDelete', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);

    const handleDeleteTestMock = jest.fn(() => () => {});
    handleDeleteTest.mockImplementation(handleDeleteTestMock);

    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <TestsTabTable />
        </BrowserRouter>
      </Provider>,
    );
    
    const deleteButtons = container.querySelectorAll('svg.delete-icon');
    expect(deleteButtons[0]).toBeInTheDocument();
    fireEvent.click(deleteButtons[0]);
    expect(handleDeleteTestMock).toHaveBeenCalledTimes(1);
  });

  it('calls handleEditTest on handleRowEdit', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    
    const handleEditTestMock = jest.fn(() => () => {});
    handleEditTest.mockImplementation(handleEditTestMock);

    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <TestsTabTable />
        </BrowserRouter>
      </Provider>,
    );

    const editButtons = container.querySelectorAll('svg.fa-pencil');
    expect(editButtons[0]).toBeInTheDocument();
    fireEvent.click(editButtons[0]);
    expect(handleEditTestMock).toHaveBeenCalledTimes(1);
  });

  it('calls runTest on handleRowRun', () => {
    useSelector.mockImplementation(mockUseSelector);
    useDispatch.mockImplementation(() => mockDispatch);
    
    const runTestMock = jest.fn(() => () => {});
    runTest.mockImplementation(runTestMock);

    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <TestsTabTable />
        </BrowserRouter>
      </Provider>,
    );

    const editButtons = container.querySelectorAll('svg.fa-play');
    expect(editButtons[0]).toBeInTheDocument();
    fireEvent.click(editButtons[0]);
    expect(runTestMock).toHaveBeenCalledTimes(1);
  });
});
