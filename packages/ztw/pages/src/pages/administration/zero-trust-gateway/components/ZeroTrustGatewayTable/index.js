/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import { isEmpty } from 'utils/lodash';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { ZERO_TRUST_GATEWAY_TABLE_CONFIGS } from 'ducks/zeroTrustGateway/constants';
import { getReadOnly } from 'utils/helpers';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';

import {
  loader,
  handleToggleDeleteForm,
  handleLink,
  handleToggleEditForm,
  handleToggleViewForm,
  handleShowRowEllipsis,
  handlePageSize,
  handlePageNumber,
  handleRefreshForm,
  // handleSortBy,
} from 'ducks/zeroTrustGateway';

function ZeroTrustGatewayTable(props) {
  const {
    accessPrivileges,
    authType,
    zeroTrustGatewayData,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => zeroTrustGatewaySelector.baseSelector(state));
  const {
    checkAll,
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};

  useEffect(() => {
    dispatch(loader(true, 1));
  }, []);

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          link: './details',
          isEditable: !isReadOnly,
          isEllipsisable: false && !isReadOnly,
          isRefreshable: false && !isReadOnly,
          isDeletable: !isReadOnly,
          isDisable: false && !isReadOnly,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
          
        };
      });
    return tableData;
  };
  
  const filteredData = zeroTrustGatewayData
    .filter((x) => x.name.toUpperCase().includes(searchText.toUpperCase())
    || (t(x.accountDetails.awsAccountId)).toUpperCase().includes(searchText.toUpperCase()));
  const data = getTableData(filteredData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(ZERO_TRUST_GATEWAY_TABLE_CONFIGS(t))}
      permission={accessPrivileges[permKey]}
      onHandleRowView={(e) => dispatch(handleToggleViewForm(e, true))}
      onHandleRowEdit={async (e) => { dispatch(handleToggleEditForm(e, true)); }}
      onHandleRowDelete={async (e) => { dispatch(handleToggleDeleteForm(e, true)); }}
      onHandleLink={async (appData) => dispatch(handleLink(appData))}
      onHandleRowRefresh={async (e) => { dispatch(handleRefreshForm(e)); }}
      onHandleRowEllipsis={async (row, position) => {
        dispatch(handleShowRowEllipsis(row, position));
      }}
      checkAll={checkAll}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={loading}
      data={data} />
  );
}

ZeroTrustGatewayTable.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  zeroTrustGatewayData: PropTypes.arrayOf(PropTypes.shape({})),

};

ZeroTrustGatewayTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  zeroTrustGatewayData: [],

};

export default ZeroTrustGatewayTable;
