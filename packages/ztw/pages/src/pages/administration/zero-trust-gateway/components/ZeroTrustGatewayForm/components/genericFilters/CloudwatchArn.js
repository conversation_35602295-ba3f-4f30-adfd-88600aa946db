import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import {
  noWhiteSpacesAllowed,
  maxLength,
} from 'utils/validations';

const maxLength128 = maxLength(128);

function Name({
  isReview, t, formValues, formMeta, formSyncErrors,
  isReadOnly,
}) {
  const fieldName = 'cloudWatchArn';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          place="right"
          text={t('CLOUDWATCH_ARN_OPTIONAL')} />
        <p className="disabled-input">{value || '---'}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        place="right"
        tooltip={t('')}
        text={(
          <>
            {t('CLOUDWATCH_ARN_OPTIONAL')}
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component={Input}
        isDisabled={isReadOnly}
        placeholder={t('ENTER_NAME')}
        validate={[
          noWhiteSpacesAllowed,
          maxLength128,
        ]}
        styleClass="max-width" />
    </div>
  );
}
  
Name.propTypes = {
  reviewItems: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
Name.defaultProps = {
  reviewItems: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  isReview: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(Name);
