// @flow
import React from 'react';
import {
  reduxForm,
} from 'redux-form';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import ReviewGroup from 'components/reviewGroup';
import { setWizardActiveNavPage } from 'ducks/zeroTrustGateway';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';
import {
  AdditionalAccounts,
  AvailabilityZone,
  AwsAccount,
  AwsAccountGroup,
  Location,
  LocationTemplate,
  Name,
  SupportedRegions,
} from '../genericFilters';

function PartnerIntegrationsReview(props) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const formValues = useSelector((state) => zeroTrustGatewaySelector.formValuesSelector(state));
  const baseSelector = useSelector((state) => zeroTrustGatewaySelector.baseSelector(state));
  const {
    closeModal,
    previousPage,
    handleSubmit,
    submitting,
    formSyncErrors,
    isReadOnly,
  } = props;
  const {
    showAddForm,
  } = baseSelector;
  
  const disableSave = !isEmpty(formSyncErrors);
  const handleSetPage = (page) => dispatch(setWizardActiveNavPage(page));

  return (
    <form onSubmit={handleSubmit} className="wizard-form zero-trust-gateway-configuration review-page">
      <div className="form-sections-container full-width">
        <div className="form-section full-width">
          <div className="bc-provisioning-general-info-title">
            {t('REVIEW')}
            <div className="bc-provisioning-general-info-description">
              {t('ZTG_REVIEW_TEXT')}
            </div>
          </div>
        </div>
        <div className="form-section full-width">
          <ReviewGroup
            styleClass="full-width"
            title={t('CONFIGURATION')}
            handleEdit={() => handleSetPage(0)}>
            <Name {...props} isReview formValues={formValues} />
            <SupportedRegions {...props} isReview formValues={formValues} />
            <AvailabilityZone {...props} isReview formValues={formValues} />
            <Location {...props} isReview formValues={formValues} />
            <LocationTemplate {...props} isReview formValues={formValues} />
          </ReviewGroup>
  
          <div className="separator-line" />

          <ReviewGroup
            styleClass="full-width"
            title={t('ACCOUNTS')}
            handleEdit={() => handleSetPage(1)}>
            <AwsAccount {...props} isReview formValues={formValues} />
            <AwsAccountGroup {...props} isReview formValues={formValues} />
            <AdditionalAccounts {...props} isReview formValues={formValues} />
          </ReviewGroup>

        </div>
      </div>

      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
          {isReadOnly && <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('NEXT')}</button>}
          {!isReadOnly && <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{showAddForm ? t('CREATE') : t('UPDATE')}</button>}
        </div>
      </div>
    </form>
  );
}

PartnerIntegrationsReview.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  previousPage: PropTypes.func,
  initialValues: PropTypes.shape(),
  t: PropTypes.func,
  submitting: PropTypes.bool,
  formValues: PropTypes.shape(),
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  resetData: PropTypes.bool,
  isReadOnly: PropTypes.bool,
};

PartnerIntegrationsReview.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  previousPage: null,
  initialValues: {
    provUrlData: {
      cloudProvider: {},
    },
  },
  t: (str) => str,
  submitting: true,
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  resetData: false,
  isReadOnly: false,
};

const Review = reduxForm({
  form: 'zeroTrustGatewayForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  onSubmitSuccess: null,
})(PartnerIntegrationsReview);

export default Review;
