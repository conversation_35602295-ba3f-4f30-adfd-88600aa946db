import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import LocationTemplatesEntityDropdown from 'commonConnectedComponents/dropdown/LocationTemplatesEntityDropdown';
import {
  requiredId,
} from 'utils/validations';

export function Location(props) {
  const {
    isReview, formValues, formMeta, formSyncErrors, t,
    isReadOnly,
  } = props;
  const fieldName = 'locationTemplate';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;
  
  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          place="right"
          text={t('LOCATION_TEMPLATE')} />
        <p className="disabled-input region break-space">{value?.name}</p>
      </div>
    );
  }

  return (
    <div className="input-container half-width">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('ZTG_LOCATION_TEMPLATE_TOOLTIP')}
        place="right"
        text={(
          <>
            {t('LOCATION_TEMPLATE')}
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </>
        )} />
      {isReadOnly && <p className="disabled-input region break-space">{t(value?.name)}</p>}
      {!isReadOnly && (
        <Field
          id={fieldName}
          name={fieldName}
          component={LocationTemplatesEntityDropdown}
          props={{
            isViewOnly: isReadOnly,
            defaultDisplayLabel: 'SELECT',
          }}
          validate={[requiredId]} />
      )}
    </div>
  );
}
  
Location.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
Location.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(Location);
