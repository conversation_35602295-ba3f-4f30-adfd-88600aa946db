// @flow
import React, { PureComponent } from 'react';
import {
  reduxForm,
  autofill,
} from 'redux-form';
import { isEmpty } from 'utils/lodash';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import {
  setAdditionalAwsAccounts,
} from 'ducks/zeroTrustGateway';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';

import {
  noWhiteSpacesAllowed,
  isNumberOnly,
  exactLength,
} from 'utils/validations';

import AwsAccount from '../genericFilters/AwsAccount';
import AwsAccountGroup from '../genericFilters/AwsAccountGroup';
import AdditionalAccounts from '../genericFilters/AdditionalAccounts';

const exactLength12 = exactLength(12);

class PartnerIntegrationsRegion extends PureComponent {
  render() {
    const {
      closeModal,
      formValues,
      formSyncErrors,
      handleSubmit,
      previousPage,
      submitting,
      t,
    } = this.props;
    
    const errorItem = formValues?.additionalAwsAccounts?.length > 128
    || (!isEmpty(formValues?.additionalAwsAccounts)
    && formValues?.additionalAwsAccounts?.find(
      (x) => (noWhiteSpacesAllowed(x) || isNumberOnly(x) || exactLength12(x)),
    ));

    const disableSave = !isEmpty(formSyncErrors) || errorItem;
    const regionInfo = (t('ZTG_ACCOUNT_TEXT')).split(/{[0-9]}/g);
    const regionInfoJSX = (
      <>
        {regionInfo[0]}
        <a href="https://help.zscaler.com/cloud-branch-connector/administration/cloud-connector-partner-integrations" target="_blank" rel="noopener noreferrer" className="external-link-button">
          {regionInfo[1]}
        </a>
        {regionInfo[2]}
      </>
    );

    return (
      <form onSubmit={handleSubmit} className="wizard-form zero-trust-gateway-configuration configure-provisioning-template">
        <div className="form-sections-container">
          <div className="bc-provisioning-general-info-title">
            {t('ACCOUNTS')}
            <div className="bc-provisioning-general-info-description">
              {regionInfoJSX}
            </div>
          </div>
          <div className="separator-line" />
          <div className="form-section partner-configuration-form full-width">
            <div className="full-width">
              <AwsAccount {...this.props} />
              <AwsAccountGroup {...this.props} />
              <br />
              <br />
              <br />
              <br />
              <br />
              <br />
              <AdditionalAccounts {...this.props} />
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <div>
            <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
            <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('NEXT')}</button>
          </div>
        </div>
      </form>
    );
  }
}

PartnerIntegrationsRegion.propTypes = {
  closeModal: PropTypes.func,
  formSyncErrors: PropTypes.shape(),
  formValues: PropTypes.shape({
    awsAccount: PropTypes.arrayOf(),
    awsAccountGroups: PropTypes.arrayOf(),
    additionalAwsAccounts: PropTypes.arrayOf(),
  }),
  handleSubmit: PropTypes.func,
  previousPage: PropTypes.func,
  submitting: PropTypes.bool,
  t: PropTypes.func,
};

PartnerIntegrationsRegion.defaultProps = {
  closeModal: (str) => str,
  formSyncErrors: {},
  handleSubmit: (str) => str,
  submitting: true,
  t: (str) => str,

};

const mapStateToProps = (state) => {
  return ({
    formSyncErrors: zeroTrustGatewaySelector.formSyncErrorsSelector(state),
    formValues: zeroTrustGatewaySelector.formValuesSelector(state),
  });
};
const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    autoFillReduxForm: autofill,
    setAdditionalAwsAccounts,
  }, dispatch);

  return {
    actions,
  };
};

const Region = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'zeroTrustGatewayForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  onSubmitSuccess: null,
})(withTranslation()(PartnerIntegrationsRegion)));

export default Region;
