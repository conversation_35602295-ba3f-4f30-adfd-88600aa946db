/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get, isEmpty } from 'utils/lodash';
import { Field } from 'redux-form';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import PartnerIntegrationsAccountGroupDropdown from 'commonConnectedComponents/dropdown/PartnerIntegrationsAccountGroupDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AccountGroups(props) {
  const {
    isReview, formValues, formMeta, formSyncErrors, t,
    isReadOnly,
  } = props;
  const fieldName = 'awsAccountGroups';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;
  
  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          place="right"
          text={t('ALLOWED_ACCOUNTS_GROUPS')} />
        <p className="disabled-input region break-space">{isEmpty(value) ? '---' : value.map((x) => `${t(x.name)}`).join('\r\n')}</p>
      </div>
    );
  }

  return (
    <div className="input-container half-width">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('ZTG_ALLOWED_ACCOUNTS_GROUPS_TOOLTIP')}
        place="right"
        text={t('ALLOWED_ACCOUNTS_GROUPS')} />
      <Field
        id={fieldName}
        name={fieldName}
        component={PartnerIntegrationsAccountGroupDropdown}
        props={{
          place: 'right',
          isViewOnly: isReadOnly,
          defaultDisplayLabel: 'SELECT',
        }}
        // validate={[required]}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AccountGroups.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
AccountGroups.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(AccountGroups));
