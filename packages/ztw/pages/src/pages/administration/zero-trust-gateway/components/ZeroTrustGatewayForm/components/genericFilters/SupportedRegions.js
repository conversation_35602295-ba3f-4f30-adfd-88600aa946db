/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';
import { useDispatch } from 'react-redux';
import { Field } from 'redux-form';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import AwsSupportedRegionsEntityDropdown from 'commonConnectedComponents/dropdown/AwsSupportedRegionsEntityDropdown';
import { required } from 'utils/validations';

import {
  updateAvailabilityZone,
} from 'ducks/zeroTrustGateway';

export function SupportedRegions(props) {
  const {
    isReview, formValues, formMeta, formSyncErrors, t,
    isReadOnly,
  } = props;
  const dispatch = useDispatch();
  const fieldName = 'supportedRegions';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;
  
  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          place="right"
          text={t('REGION')} />
        <p className="disabled-input region break-space">{t(value?.name)}</p>
      </div>
    );
  }

  return (
    <div className="input-container half-width">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('ZTG_REGION_TOOLTIP')}
        place="right"
        text={(
          <>
            {t('REGION')}
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </>
        )} />
      {isReadOnly && <p className="disabled-input region break-space">{t(value?.name)}</p>}
      {!isReadOnly && (
        <Field
          id={fieldName}
          name={fieldName}
          component={AwsSupportedRegionsEntityDropdown}
          onChange={(region) => dispatch(updateAvailabilityZone(region))}
          props={{
            isViewOnly: isReadOnly,
            defaultDisplayLabel: 'SELECT',
          }}
          validate={[required]} />
      )}
    </div>
  );
}

SupportedRegions.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
SupportedRegions.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(SupportedRegions));
