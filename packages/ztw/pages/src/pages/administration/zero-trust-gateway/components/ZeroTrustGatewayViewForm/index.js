// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import HelpArticle from 'components/HelpArticle';
import PropTypes from 'prop-types';
import ReviewGroup from 'components/reviewGroup';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';
import {
  AdditionalAccounts,
  AvailabilityZone,
  AwsAccount,
  AwsAccountGroup,
  Location,
  LocationTemplate,
  Name,
  SupportedRegions,
} from '../ZeroTrustGatewayForm/components/genericFilters';

function PartnerIntegrationsReview(props) {
  const { t } = useTranslation();
  const formValues = useSelector((state) => zeroTrustGatewaySelector.formValuesSelector(state));
  const {
    handleSubmit,
    showViewForm,
  } = props;
  
  const closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = props;
    toggleWizardModal(null, false, null, true);
    resetReduxFormValues();
  };

  if (!showViewForm) return <></>;
  return (
    <div className="edgeconnector-page zero-trust-gateway-page">
      <div className="back-to-ec back-to-partner">
        <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={closeModal} />
        <span className="back-to-partner-label">{t('VIEW_ZERO_TRUST_GATEWAY')}</span>
      </div>
      <div className="edgeconnector-modal zero-trust-gateway-form">
        <div className="modal-content modal-body branch-provisioning-modal-content zero-trust-gateway-modal-content">
          <HelpArticle article={HELP_ARTICLES.ADDING_ZERO_TRUST_GATEWAY} />
          <form onSubmit={handleSubmit} className="wizard-form zero-trust-gateway-configuration review-page">
            <div className="form-sections-container full-width">
              <div className="form-section full-width">
                {/* <div className="bc-provisioning-general-info-title">
                  {t('REVIEW')}
                  <div className="bc-provisioning-general-info-description">
                    {t('REVIEW_TEXT')}
                  </div>
                </div> */}
              </div>
              <div className="form-section full-width">
                <ReviewGroup
                  styleClass="full-width"
                  title={t('CONFIGURATION')}>
                  <Name {...props} isReview formValues={formValues} />
                  <SupportedRegions {...props} isReview formValues={formValues} />
                  <AvailabilityZone {...props} isReview formValues={formValues} />
                  <Location {...props} isReview formValues={formValues} />
                  <LocationTemplate {...props} isReview formValues={formValues} />
                </ReviewGroup>
  
                <div className="separator-line" />

                <ReviewGroup
                  styleClass="full-width"
                  title={t('ACCOUNTS')}>
                  <AwsAccount {...props} isReview formValues={formValues} />
                  <AwsAccountGroup {...props} isReview formValues={formValues} />
                  <AdditionalAccounts {...props} isReview formValues={formValues} />
                </ReviewGroup>

              </div>
            </div>

            <div className="modal-footer">
              <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

PartnerIntegrationsReview.propTypes = {
  actions: PropTypes.shape({
    resetReduxFormValues: PropTypes.func,
    toggleWizardModal: PropTypes.func,
  }),
  closeModal: PropTypes.func,
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  formValues: PropTypes.shape(),
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  isReadOnly: PropTypes.bool,
  previousPage: PropTypes.func,
  resetData: PropTypes.bool,
  showViewForm: PropTypes.bool,
  submitting: PropTypes.bool,
  t: PropTypes.func,
};

PartnerIntegrationsReview.defaultProps = {
  actions: {
    resetReduxFormValues: null,
    toggleWizardModal: null,
  },
  closeModal: (str) => str,
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  handleSubmit: (str) => str,
  initialValues: {
    provUrlData: {
      cloudProvider: {},
    },
  },
  isReadOnly: false,
  previousPage: null,
  resetData: false,
  showViewForm: false,
  submitting: true,
  t: (str) => str,
};

const ZeroTrustGatewayViewForm = reduxForm({
  form: 'zeroTrustGatewayForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  keepDirtyOnReinitialize: true,
})(PartnerIntegrationsReview);

export default ZeroTrustGatewayViewForm;
