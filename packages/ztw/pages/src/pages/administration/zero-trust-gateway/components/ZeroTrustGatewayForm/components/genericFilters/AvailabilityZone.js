/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import AvailabilityZoneDropdown from 'commonConnectedComponents/dropdown/ZoneIdDropdown';
import { required } from 'utils/validations';

const parseDropdownValues = (value) => get(value, 'original', value);

export function AvailabilityZone(props) {
  const {
    isReview, formValues, formMeta, formSyncErrors, t,
    isReadOnly,
  } = props;
  const fieldName = 'availabilityZone';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, 'supportedRegions', null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;
  
  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          place="right"
          text={t('AVAILABILITY_ZONE_ID')} />
        <p className="disabled-input region break-space">{value && value.map((x) => `${t(x.name)}`).join('\r\n')}</p>
      </div>
    );
  }

  const toolTipText = t('ZTG_AVIABILITY_ZONE_TOOLTIP').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <a href="https://docs.aws.amazon.com/ram/latest/userguide/working-with-az-ids.html" target="_blank" rel="noopener noreferrer" className="external-link-button">
        <b>{toolTipText[1]}</b>
      </a>
      {toolTipText[2]}
    </>
  );

  return (
    <div className="input-container half-width">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={toolTipJSX}
        place="right"
        text={(
          <>
            {t('AVAILABILITY_ZONE_ID')}
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component={AvailabilityZoneDropdown}
        props={{
          place: 'right',
          isViewOnly: isReadOnly,
          defaultDisplayLabel: 'SELECT',
        }}
        validate={[
          required,
          (zoneIds) => {
            if (zoneIds?.length < 2) return 'AVAILABILITY_ZONE_ID_MINIMUM_2_ZONES';
            return null;
          },
        ]}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

AvailabilityZone.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
AvailabilityZone.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(AvailabilityZone));
