// @flow
import React, { useEffect } from 'react';
import {
  reduxForm,
  autofill,
} from 'redux-form';
import { isEmpty } from 'utils/lodash';
import { connect, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { validateLocation } from 'ducks/zeroTrustGateway';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';

import {
  AvailabilityZone,
  Location,
  LocationTemplate,
  Name,
  SupportedRegions,
} from '../genericFilters';

function PartnerIntegrationsConfiguration(props) {
  const dispatch = useDispatch();
  const {
    closeModal,
    formSyncErrors,
    handleSubmit,
    resetData,
    submitting,
    t,
    showEditForm,
  } = props;

  const disableSave = !isEmpty(formSyncErrors);
  const generalInfo = (t('ZTG_CONFIGURATION_TEXT')).split(/{[0-9]}/g);
  const generalInfoJSX = (
    <>
      {generalInfo[0]}
      <a href="https://help.zscaler.com/cloud-branch-connector/adding-aws-account" target="_blank" rel="noopener noreferrer" className="external-link-button">
        {generalInfo[1]}
      </a>
      {generalInfo[2]}
    </>
  );

  useEffect(() => {
    if (resetData) {
      // actions.setTroubleshooting(troubleShootingLogging || 'ENABLE');
    }
  }, [resetData]);

  const onHandleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    const { showAddForm } = props;
    
    const { formValues } = props || {};
    const { location } = formValues || {};
    const isValidLocation = showAddForm ? await dispatch(validateLocation(location)) : true;

    if (!isValidLocation) return;
    handleSubmit();
  };

  return (
    <form onSubmit={onHandleSubmit} className="wizard-form zero-trust-gateway-configuration configure-provisioning-template">
      <div className="form-sections-container configuration">
        <div className="bc-provisioning-general-info-title">
          {t('CONFIGURATION')}
          <div className="bc-provisioning-general-info-description">
            {generalInfoJSX}
          </div>
        </div>
        <div className="separator-line" />
        <div className="form-section partner-configuration-form full-width">
          <div className="full-width">
            <Name {...props} />
            <br />
            <br />
            <div className="separator-line" />
            <div className="input-container full-width">
              <SupportedRegions {...props} isReadOnly={showEditForm} />
              <AvailabilityZone {...props} isReadOnly={showEditForm} />
            </div>
            <br />
            <br />
            <br />
            <div className="separator-line" />
            <div className="input-container full-width">
              <Location {...props} isReadOnly={showEditForm} />
              <LocationTemplate {...props} isReadOnly={showEditForm} />
            </div>
            <br />
            <br />
            <br />
            <div className="separator-line" />
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={closeModal}>{t('BACK')}</button>
          <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('NEXT')}</button>
        </div>
      </div>
    </form>
  );
}

PartnerIntegrationsConfiguration.propTypes = {
  actions: PropTypes.shape(),
  closeModal: PropTypes.func,
  cloudName: PropTypes.string,
  form: PropTypes.string,
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  formValues: PropTypes.shape(),
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  showEditForm: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.shape(),
  ]),
  onPremise: PropTypes.bool,
  previousPage: PropTypes.func,
  resetData: PropTypes.bool,
  selectedPremise: PropTypes.string,
  submitting: PropTypes.bool,
  showAddForm: PropTypes.bool,
  t: PropTypes.func,
  validateLocation: PropTypes.func,
};

PartnerIntegrationsConfiguration.defaultProps = {
  actions: {},
  closeModal: (str) => str,
  form: '',
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  handleSubmit: (str) => str,
  initialValues: {
    provUrlData: {
      cloudProvider: {},
    },
  },
  onPremise: false,
  previousPage: null,
  resetData: false,
  selectedPremise: '',
  submitting: true,
  showAddForm: true,
  t: (str) => str,
  validateLocation: null,
};

const mapStateToProps = (state) => {
  return ({
    ...zeroTrustGatewaySelector.default(state),
    formValues: zeroTrustGatewaySelector.formValuesSelector(state),
    formMeta: zeroTrustGatewaySelector.formMetaSelector(state),
    formSyncErrors: zeroTrustGatewaySelector.formSyncErrorsSelector(state),
  });
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const Configuration = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'zeroTrustGatewayForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  keepDirtyOnReinitialize: true,
  // onSubmitSuccess: null,
})(withTranslation()(PartnerIntegrationsConfiguration)));

export default Configuration;
