/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get, isEmpty } from 'utils/lodash';
import { Field } from 'redux-form';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import ListBuilder from 'components/listBuilder';
import {
  noWhiteSpacesAllowed,
  isNumberOnly,
  exactLength,
  maxLength,
  validateAwsAccounts,
} from 'utils/validations';

const exactLength12 = exactLength(12);
const maxLength128 = maxLength(128);

export function AccountGroups(props) {
  const {
    isReview, formValues, t, // formMeta, formSyncErrors,
    isReadOnly, actions,
  } = props;
  const fieldName = 'additionalAwsAccounts';
  const value = get(formValues, fieldName, null);
  // const meta = get(formMeta, fieldName, null);
  // const error = get(formSyncErrors, fieldName, null);

  const errorItem = !isEmpty(value)
      && value?.find((x) => (noWhiteSpacesAllowed(x) || isNumberOnly(x) || exactLength12(x)));
  const error = errorItem
      && (noWhiteSpacesAllowed(errorItem) || isNumberOnly(errorItem) || exactLength12(errorItem));
  const hasError = (!isEmpty(value) && Boolean(error)) || maxLength128(value);

  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          error={hasError ? t(hasError) : null}
          place="right"
          text={t('ADDITIONAL_AWS_ACCOUNTS')} />
        <p className="disabled-input region break-space">{isEmpty(value) ? '---' : value.map((x) => `${t(x)}`).join('\r\n')}</p>
      </div>
    );
  }

  return (
    <div className="input-container half-width">
      <FormFieldLabel
        text={t('ADDITIONAL_AWS_ACCOUNTS')}
        styleClass={hasError ? 'error-text' : ''}
        error={maxLength128(value) ? t('ADDITIONAL_AWS_ACCOUNTS_LIMIT_IS_128') : null}
        tooltip={t('ZTG_ADDITIONL_AWS_ACCOUNTS_TOOLTIP')} />
      <Field
        id={fieldName}
        name={fieldName}
        props={{
          t,
          hasSearch: true,
          value,
          action: actions.setAdditionalAwsAccounts,
          disabled: isReadOnly,
          removeItemsText: t('REMOVE_ALL'),
          addButtonText: t('ADD_ITEMS'),
          placeholder: t('ADD_ITEMS'),
          styleConfig: { inputMarginRight: 10 },
          validation: validateAwsAccounts,
        }}
        component={ListBuilder} />
    </div>
  );
}

AccountGroups.propTypes = {
  actions: PropTypes.shape({
    setAdditionalAwsAccounts: PropTypes.func,
  }),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
AccountGroups.defaultProps = {
  actions: {
    setAdditionalAwsAccounts: null,
  },
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  showFilters: {},
  t: (str) => str,
};

export default (withTranslation()(AccountGroups));
