/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasCsku } from 'utils/helpers';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm/partnerDeleteForm';
import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';
import { HELP_ARTICLES } from 'config';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  hanldeExpandClick,
  toggleViewModal,
  deleteAccount,
  handleToggleDeleteForm,
  handleToggleDisableForm,
  toggleEnableForm,
  enableDisableAccount,
  setSearchText,
  toggleSortBy,
  toggleShowHideChilds,
  toggleCheckConnectorGroup,
} from 'ducks/zeroTrustGateway';

export function PartnerIntegrationsModals(props) {
  const {
    actions,
    t,
    showDeleteForm,
    selectedRow,
    accessPrivileges,
    accessSubscriptions,
    modalLoading,
  } = props;
  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ADDING_ZERO_TRUST_GATEWAY });
  }, [showDeleteForm]);

  // const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
  const hasCSubscription = hasCsku(accessSubscriptions);
  
  if (!hasCSubscription) {
    return <SubscriptionRequired />;
  }

  if ((accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE')) {
    return <PermissionRequired />;
  }

  return (
    <>
      <Modal
        title={t('DELETE_ZERO_TRUST_GATEWAY')}
        isOpen={!!showDeleteForm}
        styleClass="delete-accounts"
        closeModal={() => actions.handleDeleteConfirmationForm(false)}>
        <Loading loading={modalLoading} />
        <DeleteConfirmationForm
          message={t('DELETE_ZTG_DESCRIPTION')}
          selectedRowID={selectedRow && selectedRow.id}
          handleCancel={() => actions.handleDeleteConfirmationForm(false)}
          handleDelete={() => actions.deleteAccount(showDeleteForm?.id)} />
      </Modal>
    </>
  );
}

const mapStateToProps = (state) => ({
  ...zeroTrustGatewaySelector.baseSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    hanldeExpandClick,
    handleToggleForm: toggleViewModal,
    handleSortBy: toggleSortBy,
    handleShowHideChilds: toggleShowHideChilds,
    handleCheckConnectorGroup: toggleCheckConnectorGroup,
    deleteAccount,
    handleDeleteConfirmationForm: handleToggleDeleteForm,
    handleDisableConfirmationForm: handleToggleDisableForm,
    handleEnableConfirmationForm: toggleEnableForm,
    enableDisableAccount,
    handleSearchText: setSearchText,
  }, dispatch);
  return { actions };
};

PartnerIntegrationsModals.propTypes = {
  actions: PropTypes.shape({
    deleteAccount: PropTypes.func,
    enableDisableAccount: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    handleDisable: PropTypes.func,
    handleDisableConfirmationForm: PropTypes.func,
    handleEnable: PropTypes.func,
    handleEnableConfirmationForm: PropTypes.func,
    handleToggleForm: PropTypes.func,
    load: PropTypes.func,
  }),
  t: PropTypes.func,
  showDeleteForm: PropTypes.bool,
  modalLoading: PropTypes.bool,
  selectedRow: PropTypes.shape({
    id: PropTypes.string,
  }),
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  disableType: PropTypes.shape({}),
  enableType: PropTypes.shape({}),
};

PartnerIntegrationsModals.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleDeleteConfirmationForm: null,
    handleDisableConfirmationForm: null,
    handleEnableConfirmationForm: null,
    deleteAccount: null,
    handleDisable: null,
    handleEnable: null,
  },
  t: (str) => str,
  showDeleteForm: false,
  modalLoading: false,
  selectedRow: {},
  accessPrivileges: {},
  accessSubscriptions: [],
  disableType: {},
  enableType: {},
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(PartnerIntegrationsModals),
);
