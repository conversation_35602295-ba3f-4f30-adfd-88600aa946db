import React from 'react';
import PropTypes from 'prop-types';
import { EntityDropdown } from 'components/entityDropdown';

const CC_ACTIONS = [
  { id: 'DELETE_ACCOUNTS', name: 'DELETE_ACCOUNTS' },
  { id: 'DISABLE_DATA_COLLECTION', name: 'DISABLE_DATA_COLLECTION' },
  { id: 'ENABLE_DATA_COLLECTION', name: 'ENABLE_DATA_COLLECTION' },
  { id: 'REFRESH', name: 'REFRESH' },
];

function CCActions(props) {
  const {
    handleToggleDeleteForm,
    handleToggleDisableForm,
    handleToggleEnableForm,
    handleRefreshForm,
    t,
  } = props;

  return (

    <EntityDropdown
      id="CCActions"
      name="CCActions"
      data={CC_ACTIONS}
      placeholder={t('ACTIONS')}
      meta={{}}
      input={{
        id: 'CCActions',
        name: 'CCActions',
        onChange: (x) => {
          if (x && x.id === 'DELETE_ACCOUNTS') handleToggleDeleteForm(true);
          if (x && x.id === 'DISABLE_DATA_COLLECTION') handleToggleDisableForm(true);
          if (x && x.id === 'ENABLE_DATA_COLLECTION') handleToggleEnableForm(true);
          if (x && x.id === 'REFRESH') handleRefreshForm('BULK_CHANGE');
        },
        onBlur: (x) => x,
      }} />

  );
}
  
CCActions.propTypes = {
  handleToggleDeleteForm: PropTypes.func,
  handleToggleDisableForm: PropTypes.func,
  handleToggleEnableForm: PropTypes.func,
  handleRefreshForm: PropTypes.func,
  t: PropTypes.func,
};
  
CCActions.defaultProps = {
  handleToggleDeleteForm: null,
  handleToggleDisableForm: null,
  handleToggleEnableForm: null,
  handleRefreshForm: null,
  t: null,
};

export default CCActions;
