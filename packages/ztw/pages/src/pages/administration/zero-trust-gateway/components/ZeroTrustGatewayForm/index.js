/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Loading from 'components/spinner/Loading';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import {
  handleDone,
  resetFormValues,
  saveGateway,
  setWizardActiveNavPage,
  toggleWizard,
} from 'ducks/zeroTrustGateway';

import * as zeroTrustGatewaySelector from 'ducks/zeroTrustGateway/selectors';

import {
  Accounts,
  Configuration,
  Review,
  WizardNav,
} from './components';

class zeroTrustGatewayForm extends React.Component {
  state = {
    wizardNavConfig: [
      'CONFIGURATION',
      'ACCOUNTS',
      'REVIEW',
    ],
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ADDING_ZERO_TRUST_GATEWAY,
    });
    Modal.setAppElement('#r-app');
  }

  componentWillUnmount() {
    const {
      actions: { toggleWizardModal },
      showAddForm,
      showViewForm,
      showEditForm,
    } = this.props;

    if (showAddForm || showViewForm || showEditForm) {
      toggleWizardModal(null, false, null, true);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = this.props;
    toggleWizardModal(null, false, null, true);
    resetReduxFormValues();
  };

  handleSetPage = (pg) => {
    // eslint-disable-next-line
    const { actions: { setZeroTrustGatewayWizardActive } } = this.props;

    setZeroTrustGatewayWizardActive(pg);
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setZeroTrustGatewayWizardActive }, activePage } = this.props;

    setZeroTrustGatewayWizardActive(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setZeroTrustGatewayWizardActive }, activePage } = this.props;

    setZeroTrustGatewayWizardActive(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setZeroTrustGatewayWizardActive }, enableGoToPage } = this.props;

    if (!enableGoToPage) {
      return;
    }

    setZeroTrustGatewayWizardActive(pageNum);
  };

  handleSubmit = async (values) => {
    const {
      actions, mode, showViewForm,
    } = this.props;
    if (!showViewForm) {
      await actions.saveGateway(mode, values);
    }
  };

  handleFinish = (values) => {
    const { actions: { handleDoneClick }, mode } = this.props;

    handleDoneClick(mode, values);
  };

  renderForm = (activePage, isReadOnly) => {
    switch (activePage) {
    case 0:
      return (
        <Configuration
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal} />
      );
    case 1:
      return (
        <Accounts
          isReadOnly={isReadOnly}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 2:
      return (
        <Review
          isReadOnly={isReadOnly}
          onSubmit={this.handleSubmit}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );

    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      showAddForm,
      showViewForm,
      showEditForm,
      loading,
    } = this.props;
    const { wizardNavConfig } = this.state;

    const title = () => {
      if (showEditForm) return t('EDIT_ZERO_TRUST_GATEWAY');
      return t('ADD_ZERO_TRUST_GATEWAY');
    };

    if (!showAddForm && !showEditForm) return <></>;
    return (
      <div className="edgeconnector-page zero-trust-gateway-page">
        <Loading loading={loading} />
        <div className="back-to-ec back-to-partner">
          <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
          <span className="back-to-partner-label">{title()}</span>
        </div>
        <div className="edgeconnector-modal zero-trust-gateway-form">
          <div className="modal-content modal-body branch-provisioning-modal-content zero-trust-gateway-modal-content">
            <HelpArticle article={HELP_ARTICLES.ADDING_ZERO_TRUST_GATEWAY} />
            <WizardNav
              activePage={activePage}
              goToPage={this.goToPage}
              wizardNavConfig={wizardNavConfig} />
            {this.renderForm(activePage, showViewForm)}
          </div>
        </div>
      </div>
    );
  }
}

zeroTrustGatewayForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  mode: PropTypes.string,
  loading: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  t: PropTypes.func,
};

zeroTrustGatewayForm.defaultProps = {
  actions: null,
  activePage: 0,
  mode: 'NEW',
  showAddForm: false,
  showEditForm: false,
  showViewForm: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...zeroTrustGatewaySelector.baseSelector(state),
  ...getFormValues('zeroTrustGatewayForm')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleDoneClick: handleDone,
    resetReduxFormValues: resetFormValues,
    saveGateway,
    setZeroTrustGatewayWizardActive: setWizardActiveNavPage,
    toggleWizardModal: toggleWizard,
    updateFormState: change,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(zeroTrustGatewayForm)));
