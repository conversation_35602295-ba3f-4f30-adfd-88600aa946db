/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationWithoutApi from 'components/configTable/ConfigTableWithPaginationWithoutApi';
import { VDI_AGENT_APP_TABLE_CONFIGS } from 'ducks/vdiAgentApp/constants';
import PropTypes from 'prop-types';
import * as vdiAgentAppSelector from 'ducks/vdiAgentApp/selectors';

import {
  loaderGA,
  handlePageSize,
  handlePageNumber,
  handleSortBy,
} from 'ducks/vdiAgentApp';

import { isEmpty } from 'lodash';

function vdiAgentAppTable(props) {
  const {
    accessPrivileges,
  } = props;
  
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => vdiAgentAppSelector.baseSelector(state));
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
    vdiAgentAppDataGA,
  } = baseSelector || {};

  useEffect(() => {
    dispatch(loaderGA(true));
  }, []);

  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
        };
      });
    return tableData;
  };
  
  const data = getTableData(vdiAgentAppDataGA);

  return (
    <ConfigTableWithPaginationWithoutApi
      {...(VDI_AGENT_APP_TABLE_CONFIGS(t))}
      permission={accessPrivileges[permKey]}
      onHandleSortBy={(e) => dispatch(handleSortBy(e))}
      tableHeight={9999999999}
      maxTableHeight="9999999999px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={loading}
      data={data} />
  );
}

vdiAgentAppTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  vdiAgentAppDataGA: PropTypes.arrayOf(PropTypes.shape({})),

};

vdiAgentAppTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  vdiAgentAppDataGA: [],
};

export default vdiAgentAppTable;
