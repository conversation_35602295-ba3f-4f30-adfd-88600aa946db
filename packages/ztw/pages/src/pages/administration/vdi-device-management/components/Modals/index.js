/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm/partnerDeleteForm';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import { HELP_ARTICLES } from 'config';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  deleteGroup,
  handleToggleDeleteForm,
} from 'ducks/vdiDeviceManagement';

export function PartnerIntegrationsModals(props) {
  const {
    accessPrivileges,
    accessSubscriptions,
    actions,
    modalLoading,
    selectedRow,
    showAddForm,
    showDeleteForm,
    t,
  } = props;
  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_GROUP });
  }, [showAddForm]);

  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);
  
  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }

  return (
    <Modal
      title={t('DELETE_VDI_GROUP')}
      isOpen={showDeleteForm}
      styleClass="delete-accounts"
      closeModal={() => actions.handleToggleDeleteForm(false)}>
      <Loading loading={modalLoading} />
      <DeleteConfirmationForm
        message={t('DELETE_VDI_GROUP_TEXT')}
        selectedRowID={selectedRow && selectedRow.id}
        handleCancel={() => actions.handleToggleDeleteForm(false)}
        handleDelete={() => actions.deleteGroup(selectedRow && selectedRow.id)} />
    </Modal>
  );
}

const mapStateToProps = (state) => ({
  ...vdiDeviceManagementSelector.baseSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    deleteGroup,
    handleToggleDeleteForm,
  }, dispatch);
  return { actions };
};

PartnerIntegrationsModals.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  actions: PropTypes.shape({
    handleToggleDeleteForm: PropTypes.func,
    deleteGroup: PropTypes.func,
  }),
  modalLoading: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  selectedRow: PropTypes.shape({}),
  t: PropTypes.func,
};

PartnerIntegrationsModals.defaultProps = {

  accessPrivileges: {},
  accessSubscriptions: [],
  actions: {
    handleToggleDeleteForm: null,
    deleteGroup: null,
  },
  modalLoading: false,
  showAddForm: false,
  showDeleteForm: false,
  t: (str) => str,
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(PartnerIntegrationsModals),
);
