import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { useDispatch } from 'react-redux';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { handleRemoveDeviceCriteria } from 'ducks/vdiDeviceManagement';
import {
  required,
  requiredId,
} from 'utils/validations';

import vdiDeviceOsTypeEntityDropdown from 'commonConnectedComponents/dropdown/VdiDeviceOsTypeEntityDropdown';

function OsType(props) {
  const dispatch = useDispatch();
  const {
    isReadOnly, isReview, showFilters, // actions,
    t, formValues, formMeta, formSyncErrors,
  } = props;

  const fieldName = 'osType';
  const value = get(formValues, fieldName, {});
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (!(showFilters && showFilters.osType)) return <></>;

  if (isReview) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('OS_TYPE')} />
        <p className="disabled-input">{value.name}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width">
      <div className="input-container review half-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_OS_TYPE')}
          place="right"
          text={(
            <>
              {t('OS_TYPE')}
              <FontAwesomeIcon
                onMouseUp={() => dispatch(handleRemoveDeviceCriteria(fieldName, t))}
                icon={faTimes}
                className="vdi-x-mark-icon" />
            </>
          )} />
        <Field
          id={fieldName}
          name={fieldName}
          component={vdiDeviceOsTypeEntityDropdown}
          className="select-item"
          props={{ disabled: isReadOnly }}
          validate={[
            required,
            requiredId,
          ]} />
      </div>
    </div>
  );
}
  
OsType.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  reviewItems: PropTypes.shape({}),
  showFilters: PropTypes.shape({
    osType: PropTypes.bool,
  }),
  t: PropTypes.func,
};
  
OsType.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  reviewItems: {},
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(OsType);
