import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { useDispatch } from 'react-redux';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { handleRemoveDeviceCriteria } from 'ducks/vdiDeviceManagement';
import {
  ipAddressesOrRanges,
} from 'utils/validations';

function IpAddress(props) {
  const dispatch = useDispatch();
  const {
    isReview, showFilters,
    t, formValues, formMeta, formSyncErrors,
  } = props;

  const fieldName = 'ipRange';
  const value = get(formValues, fieldName, {});
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (!(showFilters && showFilters.ipRange)) return <></>;

  if (isReview) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('IP_ADDRESS_RANCE_CIDR')} />
        <p className="disabled-input">{value.name}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width">
      <div className="input-container review half-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_IP_ADDRESS_RANCE_CIDR')}
          place="right"
          text={(
            <>
              {t('IP_ADDRESS_RANCE_CIDR')}
              <FontAwesomeIcon
                onMouseUp={() => dispatch(handleRemoveDeviceCriteria(fieldName, t))}
                icon={faTimes}
                className="vdi-x-mark-icon" />
            </>
          )} />
        <Field
          id={fieldName}
          name={fieldName}
          component={Input}
          styleClass="zia"
          validate={[
            ipAddressesOrRanges,
          ]}
          placeholder={t('ENTER_IP_RANGE')} />
      </div>
    </div>
  );
}
  
IpAddress.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReview: PropTypes.bool,
  reviewItems: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
IpAddress.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReview: false,
  reviewItems: {},
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(IpAddress);
