/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change, destroy } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import {
  toggleWizard,
  saveVdiDeviceGroup,
  setWizardActiveNavPage,
  handleToggleAddForm,
  resetFormValues,
} from 'ducks/vdiDeviceManagement';

import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';

import {
  VDIForwardingProfile,
  General,
  Devices,
  Review,
  WizardNav,
} from './components';

class vdiDeviceManagementForm extends React.Component {
  state = {
    wizardNavConfig: [
      'GENERAL',
      'DEVICES_CRITERIA',
      'VDI_FORWARDING_PROFILE',
      'REVIEW',
    ],
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_BRANCH_PROVISIONING_TEMPLATE,
    });
    Modal.setAppElement('#r-app');
  }

  componentWillUnmount() {
    const {
      actions: { toggleWizardModal, destroyForm },
      showAddForm,
      showViewForm,
      showEditForm,
    } = this.props;

    if (showAddForm || showViewForm || showEditForm) {
      toggleWizardModal(true);
    }

    destroyForm('vdiDeviceManagementForm');
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues, setToggleAddForm } } = this.props;
    setToggleAddForm(false);
    toggleWizardModal(true);
    resetReduxFormValues();
  };

  handleSetPage = (pg) => {
    // eslint-disable-next-line
    const { actions: { setWizardActive} } = this.props;

    setWizardActive(pg);
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActive }, activePage } = this.props;

    setWizardActive(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActive }, activePage } = this.props;

    setWizardActive(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setWizardActive }, enableGoToPage } = this.props;

    if (!enableGoToPage) {
      return;
    }

    setWizardActive(pageNum);
  };

  handleSubmit = (values) => {
    const {
      actions, mode,
    } = this.props;
    actions.saveVdiDeviceGroup(mode, values);
    // actions.setWizardActive(activePage + 1);
  };
  
  // handleFinish = (values) => {
  //   const { actions: { handleDoneClick }, mode } = this.props;

  //   handleDoneClick(mode, values);
  // }

  renderForm = (activePage, showViewForm) => {
    switch (activePage) {
    case 0:
      return (
        <General
          isReadOnly={showViewForm}
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal} />
      );
    case 1:
      return (
        <Devices
          isReadOnly={showViewForm}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 2:
      return (
        <VDIForwardingProfile
          isReadOnly={showViewForm}
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 3:
      return (
        <Review
          isReadOnly={showViewForm}
          onSubmit={this.handleSubmit}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );

    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      showAddForm,
      showViewForm,
      showEditForm,
    } = this.props;
    const { wizardNavConfig } = this.state;

    const title = () => {
      if (showEditForm) return t('EDIT_DYNAMIC_VDI_GROUP');
      if (showViewForm) return t('VIEW_DYNAMIC_VDI_GROUP');
      return t('ADD_DYNAMIC_VDI_GROUP');
    };

    if (!showAddForm && !showEditForm && !showViewForm) return <></>;
    return (
      <div className="edgeconnector-page partner-integrations-page vdi-device-management">
        <div className="back-to-ec back-to-partner">
          <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
          <span className="back-to-partner-label">{title()}</span>
        </div>
        <div className="edgeconnector-modal partner-integrations-form">
          <div className="modal-content modal-body branch-provisioning-modal-content vdi-modal-content">
            <HelpArticle article={HELP_ARTICLES.ADD_BRANCH_PROVISIONING_TEMPLATE} />
            <WizardNav
              activePage={activePage}
              goToPage={this.goToPage}
              wizardNavConfig={wizardNavConfig} />
            {this.renderForm(activePage, showViewForm)}
          </div>
        </div>
      </div>
    );
  }
}

vdiDeviceManagementForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  mode: PropTypes.string,
  showAddForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  t: PropTypes.func,
};

vdiDeviceManagementForm.defaultProps = {
  actions: null,
  activePage: 0,
  mode: 'NEW',
  showAddForm: false,
  showEditForm: false,
  showViewForm: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...vdiDeviceManagementSelector.baseSelector(state),
  ...getFormValues('vdiDeviceManagementForm')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    setToggleAddForm: handleToggleAddForm,
    updateFormState: change,
    saveVdiDeviceGroup,
    setWizardActive: setWizardActiveNavPage,
    resetReduxFormValues: resetFormValues,
    destroyForm: destroy,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(vdiDeviceManagementForm)));
