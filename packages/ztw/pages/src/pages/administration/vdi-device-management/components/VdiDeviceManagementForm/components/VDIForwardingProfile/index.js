// @flow
import React, { PureComponent } from 'react';
import {
  reduxForm,
  autofill,
} from 'redux-form';
import { isEmpty } from 'utils/lodash';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import {
  VdiForwardingProfile,
} from '../genericFilters';

class PartnerIntegrationsRegion extends PureComponent {
  render() {
    const {
      closeModal,
      previousPage,
      handleSubmit,
      t,
      submitting,
      formSyncErrors,
    } = this.props;

    const disableSave = !isEmpty(formSyncErrors);

    return (
      <form onSubmit={handleSubmit} className="wizard-form vdi-devices-criteria">
        <div className="form-sections-container">
          <div className="form-section">
            <div className="bc-provisioning-general-info-title">
              {t('VDI_FORWARDING_PROFILE')}
              <div className="bc-provisioning-general-info-description">
                {t('VDI_FORWARDING_PROFILE_TEXT')}
              </div>
            </div>
          </div>
          <div className="form-section">
            <VdiForwardingProfile {...this.props} />
          </div>
        </div>
        <div className="modal-footer">
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <div>
            <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
            <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('NEXT')}</button>
          </div>
        </div>
      </form>
    );
  }
}

PartnerIntegrationsRegion.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  actions: PropTypes.shape(),
  previousPage: PropTypes.func,
  initialValues: PropTypes.shape(),
  t: PropTypes.func,
  submitting: PropTypes.bool,
  formValues: PropTypes.shape(),
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  resetData: PropTypes.bool,
};

PartnerIntegrationsRegion.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  actions: {},
  previousPage: null,
  initialValues: {
    provUrlData: {
      cloudProvider: {},
    },
  },
  t: (str) => str,
  submitting: true,
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  resetData: false,
};

const mapStateToProps = (state) => ({
  filters: vdiDeviceManagementSelector.filtersSelector(state),
  showFilters: vdiDeviceManagementSelector.showFiltersSelector(state),
  formMeta: vdiDeviceManagementSelector.formMetaSelector(state),
  formValues: vdiDeviceManagementSelector.formValuesSelector(state),
  formSyncErrors: vdiDeviceManagementSelector.formSyncErrorsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const Region = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'vdiDeviceManagementForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  // onSubmitSuccess: null,
})(withTranslation()(PartnerIntegrationsRegion)));

export default Region;
