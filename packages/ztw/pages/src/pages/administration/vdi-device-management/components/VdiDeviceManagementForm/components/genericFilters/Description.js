import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { maxLength } from 'utils/validations';

const maxLength10240 = maxLength(10240);

function Description({
  isReadOnly, isReview, t, formValues, formMeta, formSyncErrors,
}) {
  const fieldName = 'desc';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  return (isReview
    ? (
      <div className="input-container review full-width">
        
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_GROUP_DESCRIPTION')}
          text={t('DESCRIPTION_OPTIONAL')} />
        <div className="review-item full-width">
          <p className="disabled-input break-spaces">{value || ''}</p>
        </div>
      </div>
    ) : (
      <div className="input-container full-width">
        <FormFieldLabel
          tooltip={t('TOOLTIP_VDI_GROUP_DESCRIPTION')}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('DESCRIPTION')} />
        <Field
          id={fieldName}
          name={fieldName}
          component="textarea"
          disabled={isReadOnly}
          placeholder={t('ENTER_DESCRIPTION_HERE')}
          styleClass="max-width"
          validate={[
            maxLength10240,
          ]}
          className="form-textarea" />
      </div>
    ));
}
  
Description.propTypes = {
  reviewItems: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
Description.defaultProps = {
  reviewItems: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  isReview: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(Description);
