/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { DEVICES_TABLE_CONFIGS } from 'ducks/vdiDeviceManagement/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';

import {
  loaderReview,
  handleToggleEditForm,
  handleToggleViewForm,
  handlePageSize,
  handlePageNumber,
  handleSortBy,
} from 'ducks/vdiDeviceManagement';

import { isEmpty } from 'utils/lodash';

function VdiDeviceReviewTable(props) {
  const {
    accessPrivileges,
    authType,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => vdiDeviceManagementSelector.baseSelector(state));
  const {
    checkAll,
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
    vdiDeviceManagementReviewData,
  } = baseSelector || {};

  useEffect(() => {
    dispatch(loaderReview(true, 1));
  }, []);

  const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          link: `./aws/details/${row.id}`,
          isEditable: !isReadOnly,
          isEllipsisable: !isReadOnly,
          isRefreshable: !isReadOnly,
          isDeletable: false && !isReadOnly,
          isDisable: false && !isReadOnly,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
          
        };
      });
    return tableData;
  };
  
  const data = getTableData(vdiDeviceManagementReviewData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(DEVICES_TABLE_CONFIGS(t))}
      permission={accessPrivileges[permKey]}
      onHandleRowView={(e) => dispatch(handleToggleViewForm(e, true))}
      onHandleRowEdit={async (e) => { dispatch(handleToggleEditForm(e, true)); }}
      onHandleSortBy={(e) => dispatch(handleSortBy(e))}
      checkAll={checkAll}
      tableHeight={9999999999}
      maxTableHeight="9999999999px"
      sortField={sortField}
      sortDirection={sortDirection}
      // isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={loading}
      data={data} />
  );
}

VdiDeviceReviewTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  vdiDeviceManagementData: PropTypes.arrayOf(PropTypes.shape({})),

};

VdiDeviceReviewTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  vdiDeviceManagementData: [],

};

export default VdiDeviceReviewTable;
