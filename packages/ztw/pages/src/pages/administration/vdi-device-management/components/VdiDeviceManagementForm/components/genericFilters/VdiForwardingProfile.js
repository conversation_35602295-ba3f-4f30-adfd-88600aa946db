import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';

import {
  // requiredId,
  required,
} from 'utils/validations';

import VdiForwardingProfileDropdown from 'commonConnectedComponents/dropdown/VdiForwardingProfileDropdown';

function VdiForwardingProfile(props) {
  const {
    isReadOnly, isReview, t, formValues, formMeta, formSyncErrors,
  } = props;
  
  const fieldName = 'forwardingProfile';
  const value = get(formValues, fieldName, []);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (isReview || isReadOnly) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('VDI_FORWARDING_PROFILE')} />
        <p className="disabled-input">{value && value.map((x) => x.name).join(', ')}</p>
      </div>
    );
  }

  return (
    <div className="g-row full-width">
      <div className="g-left">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          // tooltip={t('TOOLTIP_VDI_FORWARDING_PROFILE')}
          place="right"
          text={(
            <>
              {t('VDI_FORWARDING_PROFILE')}
            </>
          )} />
        <Field
          id={fieldName}
          name={fieldName}
          component={VdiForwardingProfileDropdown}
          className="select-item"
          validate={[
            required,
            // requiredId,
          ]} />
      </div>
    </div>
  );
}
  
VdiForwardingProfile.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  reviewItems: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
VdiForwardingProfile.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  reviewItems: {},
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(VdiForwardingProfile);
