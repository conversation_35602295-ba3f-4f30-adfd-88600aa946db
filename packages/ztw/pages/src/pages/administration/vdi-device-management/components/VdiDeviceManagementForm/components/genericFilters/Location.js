import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { useDispatch } from 'react-redux';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { handleRemoveDeviceCriteria } from 'ducks/vdiDeviceManagement';
import {
  required,
} from 'utils/validations';

import LocationsDropdown from 'commonConnectedComponents/dropdown/LocationsDropdown';

function Location(props) {
  const dispatch = useDispatch();
  const {
    isReadOnly, isReview, showFilters, // actions,
    t, formValues, formMeta, formSyncErrors,
  } = props;

  const fieldName = 'locations';
  const value = get(formValues, fieldName, []);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (!(showFilters && showFilters.locations)) return <></>;

  if (isReview) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('LOCATION')} />
        <div className="review-item full-width">
          <p className="disabled-input break-spaces full-width location">{value.map((x) => x.name).join(', ')}</p>
        </div>
      </div>
    );
  }
  if (isReadOnly) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('LOCATION')} />
        <div className="review-item full-width">
          <p className="disabled-input break-spaces">{value.map((x) => x.name).join(', ')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="g-row full-width">
      <div className="g-left">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_LOCATION')}
          place="right"
          text={(
            <>
              {t('LOCATION')}
              <FontAwesomeIcon
                onMouseUp={() => dispatch(handleRemoveDeviceCriteria(fieldName, t))}
                icon={faTimes}
                className="vdi-x-mark-icon" />
            </>
          )} />
        <Field
          id={fieldName}
          name={fieldName}
          component={LocationsDropdown}
          label="SELECT_A_LOCATION"
          className="select-item"
          validate={[
            required,
          ]} />
      </div>
    </div>
  );
}
  
Location.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  reviewItems: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
Location.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  reviewItems: {},
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(Location);
