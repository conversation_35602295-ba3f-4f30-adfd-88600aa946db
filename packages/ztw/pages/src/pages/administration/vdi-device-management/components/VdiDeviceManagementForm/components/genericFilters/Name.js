import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import {
  maxLength,
  noWhiteSpacesAllowed,
  required,
} from 'utils/validations';

const maxLength128 = maxLength(128);

function AwsRoleName({
  isReadOnly, isReview, t, formValues, formMeta, formSyncErrors,
}) {
  const fieldName = 'name';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_GROUP_NAME')}
          place="right"
          text={t('NAME')} />
        <p className="disabled-input">{value}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width  margin-bootom-28px">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('TOOLTIP_VDI_GROUP_NAME')}
        place="right"
        text={(
          <>
            {t('NAME')}
            {/* <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" /> */}
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component={Input}
        placeholder={t('ENTER_NAME')}
        props={{ isDisabled: isReadOnly }}
        validate={[
          maxLength128,
          noWhiteSpacesAllowed,
          required,
        ]}
        styleClass="max-width" />
    </div>
  );
}
  
AwsRoleName.propTypes = {
  reviewItems: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
AwsRoleName.defaultProps = {
  reviewItems: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  isReview: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(AwsRoleName);
