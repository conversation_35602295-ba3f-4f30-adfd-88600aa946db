import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { useDispatch } from 'react-redux';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { handleRemoveDeviceCriteria } from 'ducks/vdiDeviceManagement';
import Input from 'components/Input';

import {
  required,
  noWhiteSpacesAllowed,
} from 'utils/validations';

function Hostname(props) {
  const dispatch = useDispatch();
  const {
    isReadOnly, isReview, showFilters,
    t, formValues, formMeta, formSyncErrors,
  } = props;

  const fieldName = 'hostNamePrefix';
  const value = get(formValues, fieldName, {});
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (!(showFilters && showFilters.hostNamePrefix)) return <></>;

  if (isReview) {
    return (
      <div className="input-container review half-width">
        <FormFieldLabel
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          text={t('HOSTNAME_PREFIX')} />
        <p className="disabled-input">{value}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width">
      <div className="input-container review half-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`hostname-prefix ${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          tooltip={t('TOOLTIP_VDI_HOSTNAME_PREFIX')}
          place="right"
          text={(
            <>
              {t('HOSTNAME_PREFIX')}
              <FontAwesomeIcon
                onMouseUp={() => dispatch(handleRemoveDeviceCriteria(fieldName, t))}
                icon={faTimes}
                className="vdi-x-mark-icon" />
            </>
          )} />
        <Field
          id={fieldName}
          name={fieldName}
          component={Input}
          props={{ isDisabled: isReadOnly }}
          styleClass="zia"
          validate={[
            required,
            noWhiteSpacesAllowed,
          ]}
          placeholder={t('ENTER_HOSTNAME_PREFIX')} />
      </div>
    </div>
  );
}
  
Hostname.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  reviewItems: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
Hostname.defaultProps = {
  actions: {},
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  isReadOnly: false,
  isReview: false,
  reviewItems: {},
  showFilters: {},
  t: (str) => str,
};

export default withTranslation()(Hostname);
