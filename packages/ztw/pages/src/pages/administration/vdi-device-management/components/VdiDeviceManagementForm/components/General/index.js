// @flow
import React, { useEffect } from 'react';
import {
  reduxForm,
  autofill,
} from 'redux-form';
import { useDispatch, connect } from 'react-redux';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import {
  handleEditDeviceCriteria,
} from 'ducks/vdiDeviceManagement';
import {
  Description,
  MTU,
  Name,
} from '../genericFilters';

function VdiDeviceManagementGeneral(props) {
  const {
    // actions,
    closeModal,
    form,
    formSyncErrors,
    formValues,
    handleSubmit,
    // resetData,
    selectedRow,
    submitting,
    t,
  } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    const { locations = [], osType = [], hostNamePrefix } = selectedRow?.deviceCriteria || {};
    dispatch(handleEditDeviceCriteria(locations, osType && osType[0], hostNamePrefix, t));
  }, []);
  
  const disableSave = !isEmpty(formSyncErrors) || isEmpty(formValues.name);
  const generalInfo = (t('VDI_DEVICE_GENERAL_TEXT')).split(/{[0-9]}/g);
  const generalInfoJSX = (
    <>
      {generalInfo[0]}
      <a href="https://help.zscaler.com/cloud-branch-connector/about-branch-provisioning-template" target="_blank" rel="noopener noreferrer" className="external-link-button">
        {generalInfo[1]}
      </a>
      {generalInfo[2]}
    </>
  );

  // useEffect(() => {
  //   if (resetData) {
  //     actions.setTroubleshooting(troubleShootingLogging || 'ENABLE');
  //   }
  // }, [resetData]);

  return (
    <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
      <div className="form-sections-container">
        <div className="bc-provisioning-general-info-title">
          {t('GENERAL')}
          <div className="bc-provisioning-general-info-description">
            {generalInfoJSX}
          </div>
        </div>
        <div className="separator-line" />
        <div className="form-section vdi-device-management-form full-width">
          <div className="full-width">
            <Name {...props} />
            <MTU {...props} />
            <Description {...props} />
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={closeModal}>{t('BACK')}</button>
          <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('NEXT')}</button>
        </div>
      </div>
    </form>
  );
}

VdiDeviceManagementGeneral.propTypes = {
  actions: PropTypes.shape(),
  closeModal: PropTypes.func,
  cloudName: PropTypes.string,
  form: PropTypes.string,
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  formValues: PropTypes.shape(),
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  selectedRow: PropTypes.shape(),
  onPremise: PropTypes.bool,
  previousPage: PropTypes.func,
  resetData: PropTypes.bool,
  selectedPremise: PropTypes.string,
  submitting: PropTypes.bool,
  t: PropTypes.func,
};

VdiDeviceManagementGeneral.defaultProps = {
  actions: {},
  closeModal: (str) => str,
  form: '',
  formMeta: {},
  formSyncErrors: {},
  formValues: {},
  handleSubmit: (str) => str,
  initialValues: {
    name: '',
  },
  selectedRow: {},
  onPremise: false,
  previousPage: null,
  resetData: false,
  selectedPremise: '',
  submitting: true,
  t: (str) => str,
};

const mapStateToProps = (state) => {
  const baseSelector = vdiDeviceManagementSelector.baseSelector(state) || {};
  const { selectedRow, showAddForm } = baseSelector;
  const { locations = [], osType = [], hostNamePrefix } = selectedRow?.deviceCriteria || {};

  return ({
    ...vdiDeviceManagementSelector.default(state),
    formMeta: vdiDeviceManagementSelector.formMetaSelector(state),
    formValues: vdiDeviceManagementSelector.formValuesSelector(state),
    formSyncErrors: vdiDeviceManagementSelector.formSyncErrorsSelector(state),
    initialValues: {
      name: showAddForm ? '' : selectedRow.name,
      mtu: showAddForm ? '1400' : selectedRow.mtu,
      desc: showAddForm ? '' : selectedRow.desc,
      hostNamePrefix: showAddForm ? '' : hostNamePrefix || '',
      osType: showAddForm || isEmpty(osType) ? {} : osType.map((x) => ({ id: x, name: x }))[0],
      locations: showAddForm ? [] : locations?.map((x) => ({ id: x?.id, name: x?.name })),
      // deviceCriteria: showAddForm ? '' : {
      //   ...selectedRow?.deviceCriteria,
      //   osType: ({ id: osType, name: osType }),
      // },
      forwardingProfile: showAddForm ? '' : selectedRow.forwardingProfile,
    },
  });
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const Configuration = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'vdiDeviceManagementForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  // keepDirtyOnReinitialize: true,
  // onSubmitSuccess: null,
})(withTranslation()(VdiDeviceManagementGeneral)));

export default Configuration;
