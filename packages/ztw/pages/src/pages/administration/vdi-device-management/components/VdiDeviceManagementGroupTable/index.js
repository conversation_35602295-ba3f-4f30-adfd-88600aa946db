/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { GROUPS_TABLE_CONFIGS } from 'ducks/vdiDeviceManagement/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';

import {
  handleToggleEditForm,
  handleToggleViewForm,
  handleToggleDeleteForm,
  handlePageSize,
  handlePageNumber,
  handleSearchText,
  handleSortByGroup,
} from 'ducks/vdiDeviceManagement';

import { isEmpty } from 'utils/lodash';

function VdiDeviceManagementGroupTable(props) {
  const {
    accessPrivileges,
    authType,
    vdiDeviceManagementGroupData,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => vdiDeviceManagementSelector.baseSelector(state));
  const {
    checkAll,
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};

  useEffect(() => {
    dispatch(handleSearchText(''));
  }, []);

  const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          isEditable: !(row.nonDeletable || row.nonEditable || isReadOnly),
          isDeletable: !(row.nonDeletable || row.nonEditable || isReadOnly),
          isReadOnly: row.nonDeletable || row.nonEditable || isReadOnly,
          locations: row.deviceCriteria && row.deviceCriteria.locations
          && row.deviceCriteria.locations.map((x) => x?.name).join(', '),
          name: row.name,
          description: row.desc || '---',
          platform: row.platform || '---',
          servers: row.servers || '---',
          // forwardingProfile: (row && row.forwardingProfile
          //  && row.forwardingProfile.map(x => x.name).join(', ')) || '---',
          osType: (row && row.deviceCriteria && row.deviceCriteria.osType) || '---',
        };
      });
    return tableData;
  };
  
  const data = getTableData(vdiDeviceManagementGroupData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(GROUPS_TABLE_CONFIGS(t))}
      permission={accessPrivileges[permKey]}
      onHandleRowEdit={(e) => dispatch(handleToggleEditForm(e, true))}
      onHandleRowView={(e) => dispatch(handleToggleViewForm(e, true))}
      onHandleSortBy={(e) => dispatch(handleSortByGroup(e))}
      onHandleRowDelete={(e) => dispatch(handleToggleDeleteForm(e, true))}
      checkAll={checkAll}
      tableHeight={9999999999}
      maxTableHeight="9999999999px"
      sortField={sortField}
      sortDirection={sortDirection}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={loading}
      data={data} />
  );
}

VdiDeviceManagementGroupTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  vdiDeviceManagementGroupData: PropTypes.arrayOf(PropTypes.shape({})),

};

VdiDeviceManagementGroupTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  vdiDeviceManagementGroupData: [],

};

export default VdiDeviceManagementGroupTable;
