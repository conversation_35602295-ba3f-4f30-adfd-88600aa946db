import React from 'react';
import PropTypes from 'prop-types';

function LinkCriteria(props) {
  const { showFilters, t } = props;
  const numFilters = showFilters.hostNamePrefix
  + showFilters.ipRange
  + showFilters.locations
  + showFilters.osType;

  if (numFilters < 1) return <></>;

  return (
    <div className="link-criteria">
      
      {(numFilters > 1) && (
        <div className="link-criteria-item">
          <div className="link-criteria-item-label">
            {t('AND')}
          </div>
        </div>
      )}
      {(numFilters > 2) && (
        <div className="link-criteria-item">
          <div className="link-criteria-item-label">
            {t('AND')}
          </div>
        </div>
      )}
      {(numFilters > 3) && (
        <div className="link-criteria-item">
          <div className="link-criteria-item-label">
            {t('AND')}
          </div>
        </div>
      )}
   
    </div>
  );
}
LinkCriteria.propTypes = {
  showFilters: PropTypes.shape({}),
  t: PropTypes.func,
};
  
LinkCriteria.defaultProps = {
  showFilters: {},
  t: (str) => str,
};

export default LinkCriteria;
