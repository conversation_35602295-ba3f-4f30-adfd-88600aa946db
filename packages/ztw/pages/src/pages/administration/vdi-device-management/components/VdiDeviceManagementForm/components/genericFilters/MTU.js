import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { get } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import {
  isNumberOnly, minValueLimit, maxValueLimit, required,
} from 'utils/validations';

function awsAccountId(props) {
  const {
    isReadOnly, isReview, t, formValues, formMeta, formSyncErrors,
  } = props;
  const fieldName = 'mtu';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;

  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          styleClass={`${hasError ? 'invalid' : ''}`}
          error={hasError ? t(error) : null}
          // tooltip={t('TOOLTIP_ENTER_MTU')}
          place="right"
          text={t('MTU_TITLE')} />
        <p className="disabled-input">{value}</p>
      </div>
    );
  }

  return (
    <div className="input-container full-width margin-bootom-28px">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('TOOLTIP_VDI_MTU')}
        place="right"
        text={(
          <>
            {t('MTU_TITLE')}
            {/* <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" /> */}
          </>
        )} />
      <Field
        id={fieldName}
        name={fieldName}
        component={Input}
        placeholder={t('ENTER_MTU')}
        props={{ isDisabled: isReadOnly }}
        validate={[
          isNumberOnly,
          required,
          maxValueLimit(1400),
          minValueLimit(1),
        ]}
        styleClass="max-width" />
    </div>
  );
}
  
awsAccountId.propTypes = {
  reviewItems: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  isReadOnly: PropTypes.bool,
  isReview: PropTypes.bool,
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};
  
awsAccountId.defaultProps = {
  reviewItems: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  isReadOnly: false,
  isReview: false,
  t: (str) => str,
  actions: {},
};

export default withTranslation()(awsAccountId);
