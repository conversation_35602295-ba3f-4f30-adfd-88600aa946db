// @flow
import React from 'react';
import {
  reduxForm,
} from 'redux-form';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ReviewGroup from 'components/reviewGroup';
import { setWizardActiveNavPage } from 'ducks/vdiDeviceManagement';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import {
  Description,
  // DeviceCriteria,
  Hostname,
  IpAddress,
  Location,
  MTU,
  Name,
  OsType,
  VdiDeviceReviewTable,
  VdiForwardingProfile,
} from '../genericFilters';

function VdiDeviceGroupReview(props) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const formValues = useSelector((state) => vdiDeviceManagementSelector.formValuesSelector(state));
  const showFilters = useSelector((state) => vdiDeviceManagementSelector.showFiltersSelector(state));
  const showViewForm = useSelector(
    (state) => vdiDeviceManagementSelector.showViewFormSelector(state),
  );
  const {
    closeModal,
    previousPage,
    handleSubmit,
    submitting,
    formSyncErrors,
  } = props;
  
  const disableSave = !isEmpty(formSyncErrors);
  const handleSetPage = (page) => dispatch(setWizardActiveNavPage(page));

  return (
    <form onSubmit={handleSubmit} className="wizard-form review-page">
      <div className="form-sections-container full-width">
        <div className="form-section full-width">
          <div className="bc-provisioning-general-info-title">
            {t('REVIEW')}
            <div className="bc-provisioning-general-info-description">
              {t('VDI_REVIEW_TEXT')}
            </div>
          </div>
        </div>
        <div className="form-section full-width">
          <ReviewGroup
            styleClass="full-width"
            title={t('GENERAL')}
            handleEdit={() => handleSetPage(0)}>
            <Name {...props} isReview formValues={formValues} />
            <MTU {...props} isReview formValues={formValues} />
            <Description {...props} isReview formValues={formValues} />
          </ReviewGroup>
  
          <div className="separator-line" />

          <ReviewGroup
            styleClass="full-width"
            title={t('DEVICE_CRITERIA')}
            handleEdit={() => handleSetPage(1)}>
            <Location {...props} isReview formValues={formValues} showFilters={showFilters} />
            <OsType {...props} isReview formValues={formValues} showFilters={showFilters} />
            <Hostname {...props} isReview formValues={formValues} showFilters={showFilters} />
            <IpAddress {...props} isReview formValues={formValues} showFilters={showFilters} />
          </ReviewGroup>

          <ReviewGroup
            styleClass="full-width"
            title={t('VDI_FORWARDING_PROFILE')}
            handleEdit={() => handleSetPage(2)}>
            <VdiForwardingProfile {...props} isReview formValues={formValues} />
          </ReviewGroup>
          <ReviewGroup
            styleClass="full-width"
            title={t('DEVICES')}
            handleEdit={() => handleSetPage(1)}>
            <div className="main-container">
              <div className="search-container-device-group">
                <SearchBox
                  // clickCallback={value => actions.handleSearchText(value)}
                  placeholder={t('SEARCH')} />
              </div>
              <VdiDeviceReviewTable {...props} isReview formValues={formValues} />
            </div>
          </ReviewGroup>
        </div>
      </div>

      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
          {!showViewForm && <button type="submit" className="next primary-button" disabled={disableSave || submitting}>{t('SAVE')}</button>}
        </div>
      </div>
    </form>
  );
}

VdiDeviceGroupReview.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  previousPage: PropTypes.func,
  initialValues: PropTypes.shape(),
  t: PropTypes.func,
  submitting: PropTypes.bool,
  formValues: PropTypes.shape(),
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  resetData: PropTypes.bool,
};

VdiDeviceGroupReview.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  previousPage: null,
  initialValues: {
    provUrlData: {
      cloudProvider: {},
    },
  },
  t: (str) => str,
  submitting: true,
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  resetData: false,
};

const Review = reduxForm({
  form: 'vdiDeviceManagementForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  // onSubmitSuccess: null,
})(VdiDeviceGroupReview);

export default Review;
