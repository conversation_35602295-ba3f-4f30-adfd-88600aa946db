/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { useDispatch } from 'react-redux';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import DropDown from 'components/dropDown/SelectWithSearch';
import { handleDeviceCriteria } from 'ducks/vdiDeviceManagement';

export function DeviceCriteria(props) {
  const {
    isReview, formValues, formMeta, formSyncErrors, t,
    filters, showFilters,
  } = props;
  const dispatch = useDispatch();
  const fieldName = 'deviceCriteria';
  const value = get(formValues, fieldName, null);
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);

  const hasError = meta && meta.touched && !!error;
  
  if (isReview) {
    return (
      <div className="input-container review full-width">
        <FormFieldLabel
          id={`label_${fieldName}`}
          tooltip={t('')}
          place="right"
          text={t('DEVICE_CRITERIA')} />
        <p className="disabled-input region break-space">{value && value.map((x) => `${t(x.name)}`).join('\r\n')}</p>
      </div>
    );
  }

  return (
    <div className="entity-dropdown-container-criteria">
      <FormFieldLabel
        id={`label_${fieldName}`}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? t(error) : null}
        tooltip={t('TOOLTIP_REGION')}
        place="right"
        text={(
          <>
            {/* {t('REGION')}
            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" /> */}
          </>
        )} />
      <Field
        id="deviceCriteriaOptions"
        name="deviceCriteriaOptions"
        onChange={(e) => dispatch(handleDeviceCriteria(e, t))}
        props={{
          items: filters,
          placeholder: t('ADD_CRITERIA'),
          title: t('ADD_CRITERIA'),
        }}
        validate={[
          () => {
            if (showFilters?.hostNamePrefix || showFilters?.osType || showFilters?.locations) {
              return null;
            }
            return 'PLEASE_SELECT_A_FILTER';
          },
        ]}
        component={DropDown} />
    </div>
  );
}

DeviceCriteria.propTypes = {
  actions: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  isReview: PropTypes.bool,
  filters: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
};
  
DeviceCriteria.defaultProps = {
  actions: {},
  formMeta: {},
  showFilters: {},
  formSyncErrors: {},
  formValues: {},
  isReview: false,
  filters: [],
  t: (str) => str,
};

export default (withTranslation()(DeviceCriteria));
