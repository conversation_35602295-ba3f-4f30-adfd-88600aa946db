import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { VDI_PROFILE_TABLE_CONFIGS } from 'ducks/vdiProfile/constants';
import ConfigTable from 'components/configTable';
import { getReadOnly } from 'utils/helpers';

class VDIProfileTable extends PureComponent {
  static propTypes = {
    vdiTableData: PropTypes.arrayOf(PropTypes.shape()),
    handleProvUrlCopyText: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    loadMore: PropTypes.func,
  };

  static defaultProps = {
    vdiTableData: null,
    handleProvUrlCopyText: null,
    toggleDeleteConfirmationForm: null,
    toggleProvTemplateWizard: null,
    handleViewProvForm: null,
    accessPrivileges: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
    loadMore: null,
  };

  getTableData = () => {
    const { vdiTableData, accessPrivileges, authType } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);

    const tableData = vdiTableData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        desc: row.desc,
        include: row.include,
        exclude: row.exclude,
        isReadOnly: isReadOnly || row.nonEditable,
        isDeletable: !isReadOnly && !row.nonEditable,
        isEditable: !isReadOnly && !row.nonEditable,
      };
    });
    return tableData;
  };

  render() {
    const {
      toggleProvTemplateWizard,
      toggleDeleteConfirmationForm,
      handleViewProvForm,
      hasNextPage,
      loadMore,
      moreItemsLoading,
    } = this.props;
  
    return (
      <ConfigTable
        {...VDI_PROFILE_TABLE_CONFIGS}
        onHandleRowEdit={(e) => toggleProvTemplateWizard(e, true, 'EDIT')}
        onHandleRowDelete={toggleDeleteConfirmationForm}
        onHandleRowView={handleViewProvForm}
        moreItemsLoading={moreItemsLoading}
        hasNextPage={hasNextPage}
        loadMore={() => loadMore(false)}
        pagination
        data={this.getTableData()} />
    );
  }
}

export default withTranslation()(VDIProfileTable);
