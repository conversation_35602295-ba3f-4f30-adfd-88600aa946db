import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { uniqBy } from 'utils/lodash';
import { flattenDeep } from 'lodash';
import CommonDropdown from 'components/commonDropdown';
// import SingleDropdown from 'components/dropDown/SingleDropdown';
// import SingleDropdownPanel from 'components/commonDropdown/SingleDropdownPanel';
import * as vdiProfileSelector from 'ducks/vdiProfile/selectors';

import {
  handleSetFilter,
} from 'ducks/vdiProfile';

function TableFilters() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector(vdiProfileSelector.baseSelector);
  const { vdiTableData } = baseSelector;
  const includeIpList = uniqBy(flattenDeep(vdiTableData.filter((item) => item.include)
    .map(
      (x) => {
        return x.include;
      },
    ))
    .map(
      (x) => {
        return ({
          id: x,
          label: x,
          value: x,
        });
      },
    ), 'id');

  const namespaceList = uniqBy(flattenDeep(vdiTableData.filter((item) => item.exclude)
    .map(
      (x) => {
        return x.exclude;
      },
    ))
    .map(
      (x) => {
        return ({
          id: x,
          label: x,
          value: x,
        });
      },
    ), 'id');

  // const ipList = uniqBy(vdiTableData, x => x.networkId.ip).map(x => ({
  //   id: x.id, value: x.networkId && x.networkId.ip, label: x.networkId && x.networkId.ip,
  // }));

  const [selectedIncludeIp, setSelectedIncludeIp] = useState([]);
  const [selectedExcludeIp, setSelectedExcludeIp] = useState([]);
  // const [selectedIp, setSelectedIp] = useState([]);

  useEffect(() => {
    setSelectedIncludeIp([]);
    setSelectedExcludeIp([]);
    // setSelectedIp([]);
    return () => {
      setSelectedIncludeIp([]);
      setSelectedExcludeIp([]);
      // setSelectedIp([]);
    };
  }, []);

  const handleIncludeIp = (value) => {
    setSelectedIncludeIp(value);
    dispatch(handleSetFilter('includeIps', value || []));
  };
  const handleSelectedExcludeIp = (value) => {
    setSelectedExcludeIp(value);
    dispatch(handleSetFilter('excludeIps', value || []));
  };
  // const handleSelectedIp = (value) => {
  //   setSelectedIp(value);
  //   dispatch(handleSetFilter('ip', value || []));
  // };
  
  return (
    <div className="table-filter">
      <div className="filters-section">
        <div className="sspm-filter new-filter-component tenant-filter">
          {/* <SingleDropdown
            items={vcpIdList}
            labelSelectedValue="VPC_ID"
            selectedValue={selectedVpcId}
            showLabelInSelectedValue
            setValue={handleSelectedVpcId}
            defaultValue={selectedVpcId} />
          <SingleDropdown
            items={namespaceList}
            labelSelectedValue="NAMESPACE"
            selectedValue={selectedNamespace}
            showLabelInSelectedValue
            setValue={handleSelectedNamespace}
            defaultValue={selectedNamespace} />
          <SingleDropdown
            items={ipList}
            labelSelectedValue="IP_ADDRESS"
            selectedValue={selectedIp}
            showLabelInSelectedValue
            setValue={handleSelectedIp}
            defaultValue={selectedIp} /> */}

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            // dropdownRef={function dropdownRef() {}}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('INCLUDE_IP_ADDRESSES')}
            labelClassName="vpc-id-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={includeIpList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedIncludeIp !== e) {
                handleIncludeIp(e);
              }
            }}
            value={selectedIncludeIp}
            width="224px" />

          <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            // dropdownRef={function dropdownRef() {}}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('EXCLUDE_IP_ADDRESSES')}
            labelClassName="namespace-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={namespaceList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedExcludeIp !== e) {
                handleSelectedExcludeIp(e);
              }
            }}
            value={selectedExcludeIp}
            width="224px" />

          {/* <CommonDropdown
            className="entity-dropdown"
            defaultSelected={[]}
            designType="newFilter"
            disabled={false}
            // dropdownRef={function dropdownRef() {}}
            error={{
              errorMessage: '',
              isError: false,
            }}
            id="entity-dropdown"
            isMulti
            isRadio={false}
            isSearchable
            label={t('IP_ADDRESS')}
            labelClassName="ip-address-dropdown-label"
            loading={false}
            maxWidth="400px"
            options={ipList}
            placeholder={t('SELECT')}
            triggerChangeOnSelect
            onChange={(e) => {
              if (selectedIp !== e) {
                setShowApplyButton(true);
                // setValue(e);
                handleSelectedIp(e);
              }
            }}
            value={selectedIp}
            width="224px" /> */}
        </div>
        
      </div>
    </div>
  );
}

export default (TableFilters);

// // import React, { useState } from 'react';
// // import PropTypes from 'prop-types';
// // import { noop } from 'utils/lodash';
// // import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// // import { faPlus } from '@fortawesome/pro-regular-svg-icons';
// // import zraCategoryIcon from 'components/zraCategoryIcon';
// // import { getColorCode } from 'components/contributingFactorsTable/utils';
// // import CommonDropdown from 'components/commonDropdown';

// // import { ENTITIES } from 'components/factorsDistribution/constants.js';
// // import { getEntityLabel } from '../utils';

// // import './index.scss';

// // const arr = [
// //   'External Attack Surface',
// //   'Compromise',
// //   'Lateral Propagation',
// //   'Data Loss',
// // ];

// // const keyMap = {
// //   'External Attack Surface': 'easScore',
// //   Compromise: 'pcScore',
// //   'Lateral Propagation': 'lpScore',
// //   'Data Loss': 'dlScore',
// // };

// // export default function FilterMenu({
// //   t,
// //   orgRiskScore,
// //   setSelectedEntity,
// //   selectedEntity,
// //   setSelectedCatId,
// //   selectedCatId,
// // }) {
// //   const [isFilterOn, setIsFilterOn] = useState(false);
// //   const [showApplyButton, setShowApplyButton] = useState(false);

// //   const setCatId = (index) => {
// //     const catId = index + 1 === selectedCatId ? 0 : index + 1;
// //     setSelectedCatId(catId);
// //   };

// //   return (
// //     <div className="filer-menu">
// //       {arr.map((item, index) => {
// //         return (
// //           <div
// //             className={`filter-item ${
// //               index + 1 === selectedCatId ? 'selected' : ''
// //             }`}
// //             onClick={() => setCatId(index)}
// //           >
// //             <FontAwesomeIcon icon={zraCategoryIcon(item)} />
// //             {item}
// //             <div
// //               className="item-value"
// //               style={{
// //                 backgroundColor: getColorCode(
// //                   orgRiskScore[keyMap[item]] || 0,
// //                   100,
// //                 ),
// //               }}
// //             >
// //               {orgRiskScore[keyMap[item]]
// //                 ? Number.parseFloat(orgRiskScore[keyMap[item]]).toFixed(2)
// //                 : 0}
// //             </div>
// //           </div>
// //         );
// //       })}
// //       <div className="entity-search-dropdown-container">
// //         {(isFilterOn || selectedEntity.length > 0) && (
// //           <div className="entity-search-dropdown">
// //             <CommonDropdown
// //               className="entity-dropdown"
// //               defaultSelected={[]}
// //               designType="noraml"
// //               disabled={false}
// //               dropdownRef={function dropdownRef() {}}
// //               error={{
// //                 errorMessage: '',
// //                 isError: false,
// //               }}
// //               id="entity-dropdown"
// //               isMulti
// //               isRadio={false}
// //               isSearchable
// //               label="Entity"
// //               labelClassName="entity-dropdown-label"
// //               loading={false}
// //               maxWidth="400px"
// //               options={[
// //                 {
// //                   disabled: false,
// //                   label: 'Workforce',
// //                   value: '1',
// //                 },
// //                 {
// //                   disabled: false,
// //                   label: '3rd Parties',
// //                   value: '2',
// //                 },
// //                 {
// //                   disabled: false,
// //                   label: 'Applications',
// //                   value: '3',
// //                 },
// //                 {
// //                   disabled: false,
// //                   label: 'Assets',
// //                   value: '4',
// //                 },
// //               ]}
// //               placeholder="Select"
// //               triggerChangeOnSelect={true}
// //               onChange={(e) => {
// //                 if (selectedEntity !== e) {
// //                   setShowApplyButton(true);
// //                   // setValue(e);
// //                   setSelectedEntity(e);
// //                 }
// //               }}
// //               value={selectedEntity}
// //               width="224px"
// //             />
// //           </div>
// //         )}

// //         {/* {showApplyButton && (
// //           <div className="filter-button-group">
// //             <button
// //               className="filter-button-apply"
// //               onClick={() => {
// //                 setShowApplyButton(false);
// //                 setIsFilterOn(false);
// //                 setSelectedEntity(getEntityLabel(value));
// //               }}
// //             >
// //               {t('APPLY')}
// //             </button>
// //             <button
// //               className="filter-button-cancel"
// //               onClick={() => {
// //                 setShowApplyButton(false);
// //                 setIsFilterOn(false);
// //               }}
// //             >
// //               {t('CANCEL')}
// //             </button>
// //           </div>
// //         )} */}

// //         {selectedEntity.length == 0 && !isFilterOn && (
// //           <div className="plus-container" onClick={() => setIsFilterOn(true)}>
// //             <FontAwesomeIcon icon={faPlus} />
// //           </div>
// //         )}
// //       </div>
// //     </div>
// //   );
// // }

// // FilterMenu.propTypes = {
// //   t: PropTypes.func,
// //   setSelectedEntity: PropTypes.func,
// //   setSelectedCatId: PropTypes.func,
// //   selectedEntity: PropTypes.arrayOf({
// //     value: PropTypes.string,
// //     label: PropTypes.string,
// //   }),
// //   selectedCatId: PropTypes.number,
// //   orgRiskScore: PropTypes.arrayOf(PropTypes.string),
// // };

// // FilterMenu.defaultProps = {
// //   t: (str) => str,
// //   setSelectedEntity: noop,
// //   setSelectedCatId: noop,
// //   selectedCatId: 0,
// //   selectedEntity: [],
// //   orgRiskScore: [],
// // };
