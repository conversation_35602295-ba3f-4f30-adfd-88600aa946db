/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import {
  getFormValues,
  change,
  autofill,
} from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';

import {
  resetFormValues,
  toggleWizard,
  saveVDIProfile,
  handleIncludeIps,
  handleExcludeIps,
} from 'ducks/vdiProfile';

import * as VDIProfileSelectors from 'ducks/vdiProfile/selectors';

import VDIAgentProfileForm from './Form';
import './index.scss';

class VDIProfileForm extends React.Component {
  componentDidMount() {
    // const { actions: { autoFillFormValues }, initialValues, include } = this.props;
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE });
    Modal.setAppElement('#r-app');
    // autoFillFormValues('vdiAgentTemplate', 'include', initialValues.include || include);
  }

  componentWillUnmount() {
    const { actions: { toggleWizardModal }, isProvTemplateWizardOpen } = this.props;

    if (isProvTemplateWizardOpen) {
      toggleWizardModal(false, null, null);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = this.props;
    resetReduxFormValues();
    toggleWizardModal(false, null, null);
  };

  render() {
    const {
      t,
      isProvTemplateWizardOpen,
      mode,
      // enableGoToPage,
      initialValues,
      actions,
      modalLoading,
    } = this.props;
    let label;

    switch (mode) {
    case 'NEW':
    case 'PRESET':
      label = t('ADD_VDI_AGENT_FORWARDING_PROFILE');
      break;

    case 'EDIT':
      label = t('EDIT_VDI_AGENT_FORWARDING_PROFILE');
      break;

    default:
      label = '';
      break;
    }
    if (isProvTemplateWizardOpen) {
      return (
        <div className="edgeconnector-page">
          <div className="back-to-ec">
            {/* <div className={`back-to-ec ${enableGoToPage ? '' : 'hide'}`}> */}
            <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
            {label}
          </div>
          <Loading loading={modalLoading}>
            <HelpArticle article={HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE} />
            <ServerError {...this.props}>
              <VDIAgentProfileForm
                {...this.props}
                initialValues={initialValues}
                saveVDIForwardingProfile={actions.saveVDIForwardingProfile}
                closeModal={this.closeModal} />
            </ServerError>
          </Loading>
        </div>
      );
    }
    return '';
  }
}

VDIProfileForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  history: PropTypes.shape(),
  match: PropTypes.shape(),
  name: PropTypes.string,
  mode: PropTypes.string,
  isProvTemplateWizardOpen: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  initialValues: PropTypes.shape(),
  includeIps: PropTypes.arrayOf(PropTypes.string),
  excludeIps: PropTypes.arrayOf(PropTypes.string),
  modalLoading: PropTypes.bool,
  t: PropTypes.func,
};

VDIProfileForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  mode: 'NEW',
  isProvTemplateWizardOpen: false,
  initialValues: {},
  includeIps: null,
  excludeIps: null,
  t: (str) => str,
  modalLoading: false,
};

const mapStateToProps = (state) => ({
  ...VDIProfileSelectors.default(state),
  ...getFormValues('vdiAgentProfile')(state),
  enableGoToPage: VDIProfileSelectors.enableGoToPageSelector(state),
  // initialValues: VDIProfileSelectors.dataSelector(state),
  initialValues: VDIProfileSelectors.dataSelector(state),
  // wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  formValues: VDIProfileSelectors.formValuesSelector(state),
  formMeta: VDIProfileSelectors.formMetaSelector(state),
  formSyncErrors: VDIProfileSelectors.formSyncErrorsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    saveVDIForwardingProfile: saveVDIProfile,
    resetReduxFormValues: resetFormValues,
    onIncludeIpAddress: handleIncludeIps,
    onExcludeIpAddress: handleExcludeIps,
    autoFillFormValues: autofill,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(VDIProfileForm)));
