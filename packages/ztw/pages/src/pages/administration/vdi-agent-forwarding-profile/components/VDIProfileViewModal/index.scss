@import "scss/colors.scss";


.wizard-form .form-sections-container.cc-provisoning {
  .review-section-title {
    margin-left: 20px;
    &:first-child {
      margin-top: 20px;
    }
    .review-section-title-description {
      margin-left: 20px;
    }
  }
  .form-section {
    width: calc(100% - 36px);
    
    .input-container {
      &.review {
        &.prov-url {
          margin: 10px 0;
          height: auto;
          .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            background-color: var(--semantic-color-background-secondary);
            padding: 14px;
            display: block;
            border-radius: 5px;
            span {
              color: var(--semantic-color-content-interactive-primary-disabled);
            }
            span.copy-prov-url {
              float: right;
              width: 180px;
              cursor: pointer;
              color: var(--semantic-color-content-interactive-primary-default);
            }
          }
          &.desc {
            margin-top: 0;
            .disabled-input {
              color: var(--semantic-color-content-interactive-primary-disabled);
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}
