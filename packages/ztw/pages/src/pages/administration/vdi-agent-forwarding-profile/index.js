import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
// import { noop } from 'utils/lodash';
import Modal from 'components/modal';
import AddNewButton from 'components/addNewButton';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import ServerError from 'components/errors/ServerError';
import SearchBox from 'components/searchBox';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as VDIProfileSelectors from 'ducks/vdiProfile/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import { isEmpty, cloneDeep, noop } from 'utils/lodash';

import {
  loadForwardingProfile,
  deleteVDIProfile,
  toggleViewModal,
  toggleDeleteForm,
  toggleWizard,
  vdilocalData,
} from 'ducks/vdiProfile';
import {
  VDIProfileTable,
  VDIProfileForm,
  VDIProfileViewModal,
} from './components';
// import TableFilters from './components/TableFilters';
import TableFiltersForwardingProfile from './components/TableFiltersForwardingProfile';
// import './index.scss';

export class VDIAgentProfile extends Component {
  static propTypes = {
    t: PropTypes.func,
    vdiTableData: PropTypes.arrayOf(PropTypes.shape()),
    localVDIData: PropTypes.shape(),
    load: PropTypes.func,
    loadLocalData: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    formTitle: PropTypes.string,
    showViewForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    history: PropTypes.shape(),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    tableFilter: PropTypes.shape(),
  };

  static defaultProps = {
    t: (str) => str,
    vdiTableData: [],
    localVDIData: [],
    load: noop,
    loadLocalData: noop,
    toggleProvTemplateWizard: noop,
    handleViewProvForm: noop,
    formTitle: '',
    showViewForm: false,
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: '',
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
    history: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.PROVISIONING_TEMPLATES,
    });
    // need to change
    const { load } = this.props;
    load(true);
  }

  openModal = (event) => {
    event.preventDefault();
    // eslint-disable-next-line
    const { toggleProvTemplateWizard } = this.props;
    toggleProvTemplateWizard(null, true, 'NEW');
  };

  // eslint-disable-next-line no-unused-vars
  filteredProfileData = (data) => {
    const { tableFilter, vdiTableData } = this.props;
    let filteredData = cloneDeep(vdiTableData);
    const { includeIps = [], excludeIps = [] } = tableFilter || {};
    const includeIp = includeIps.map((x) => x.value) || [];
    const excludeIp = excludeIps.map((x) => x.value) || [];
    
    if (!isEmpty(includeIp)) {
      filteredData = filteredData.filter((item) => {
        if (item.include) {
          let isIncludePresent = false;
          item.include.forEach((x) => {
            // add one more loop to go over all the items of includeIps
            if (!isIncludePresent) {
              isIncludePresent = includeIp.indexOf(x) > -1;
            }
          });
          
          return isIncludePresent;
        }
        return false;
      });
    }
    
    if (!isEmpty(excludeIp)) {
      filteredData = filteredData.filter((item) => {
        if (item.exclude) {
          let isExcludePresent = false;
          item.exclude.forEach((x) => {
            // add one more loop to go over all the items of includeIps
            if (!isExcludePresent) {
              isExcludePresent = excludeIp.indexOf(x) > -1;
            }
          });
          
          return isExcludePresent;
        }
        return false;
      });
    }

    return filteredData;
  };

  render() {
    const {
      vdiTableData,
      toggleProvTemplateWizard,
      handleViewProvForm,
      showViewForm,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      t,
      accessPrivileges,
      accessSubscriptions,
      authType,
      load,
      moreItemsLoading,
      hasNextPage,
      // localVDIData,
    } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);
    const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE,
      constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING];
    
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);
  
    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }
    if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="provisioning-template-page-container">
        <div className="page-title header-3">
          {t('VDI_AGENT_FORWARDING_PROFILE')}
        </div>
        <div className="filters">
          {/* <TableFilters /> */}
          <TableFiltersForwardingProfile />
        </div>
        <div className="main-container vdi-template-table">
                
          <div className={`sipg-fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
            {!isReadOnly && <AddNewButton label={t('ADD_VDI_AGENT_FORWARDING_PROFILE')} clickCallback={this.openModal} />}
            <div className="search-container">
              <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(true)} />
            </div>
          </div>
          <div className="cloud-provider-wrapper prov-templates">
            <RBAC privilege={permKey}>
              <Loading {...this.props} />
              <HelpArticle article={HELP_ARTICLES.PROVISIONING_TEMPLATES} />
              <ServerError {...this.props}>
                <VDIProfileTable
                  authType={authType}
                  accessPrivileges={accessPrivileges}
                  toggleDeleteConfirmationForm={toggleDeleteConfirmationForm}
                  handleViewProvForm={handleViewProvForm}
                  vdiTableData={this.filteredProfileData(vdiTableData)}
                  toggleProvTemplateWizard={toggleProvTemplateWizard}
                  hasNextPage={hasNextPage}
                  moreItemsLoading={moreItemsLoading}
                  loadMore={load} />
                <VDIProfileForm modalLoading={modalLoading} />
                <Modal
                  title={t('VIEW_VDI_AGENT_FORWARDING_PROFILE')}
                  styleClass="view-cloudconnector-provisioning-modal"
                  isOpen={showViewForm}
                  closeModal={() => handleViewProvForm(null, false)}>
                  <VDIProfileViewModal />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
              {/* </Loading> */}
            </RBAC>
          </div>
        </div>
      </div>
    );
  }
}

const stateProps = (state) => ({
  ...VDIProfileSelectors.default(state),
  modalLoading: VDIProfileSelectors.modalLoadingSelector(state),
  selectedRowID: VDIProfileSelectors.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  tableFilter: VDIProfileSelectors.tableFilters(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const dispatchProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadForwardingProfile,
    toggleProvTemplateWizard: toggleWizard,
    handleViewProvForm: toggleViewModal,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteVDIProfile,
    loadLocalData: vdilocalData,
  }, dispatch);
  return actions;
};

export default connect(stateProps, dispatchProps)(withTranslation()(VDIAgentProfile));
