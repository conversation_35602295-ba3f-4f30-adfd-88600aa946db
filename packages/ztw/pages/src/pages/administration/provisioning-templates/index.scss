.ec-root-page {
.main-container {
  padding: 19px 25px;  
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.page-title {
  padding-left: 0;
}
.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

// overwrite react table css
.prov-templates {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 28rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.view-cloudconnector-provisioning-modal {
  .wizard-form.configure-provisioning-template {
    .form-sections-container {
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

.provisioning-template-page-container {
  background-color: transparent;
  padding: 24px;
  position: relative;
  .tabs {
    margin-bottom: 0;
  }
  .main-container {
    padding: 0  25px;
    &.provisioning-template-navtabs {
      padding-bottom: 0;
    }
    &.provisioning-template-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      max-height: 42px;
      .search-container {
        max-height: 32px;
      }
      .actions-row.refresh {
        .refresh-button {
          border: none;
          color: var(--semantic-color-content-base-primary);
          background: none;
          color:  var(--semantic-color-content-base-primary);
          margin-right: 8px;
        }
        .fa-arrows-rotate {
          color: var(--semantic-color-content-interactive-primary-default);
          margin-right: 4px;
        }
      }
    }
    &.provisioning-template-table {
      padding: 12px;
      padding-top: 0;
      margin: 0 19px 25px 25px;
      background: var(--semantic-color-background-primary);
      border-radius: 8px;
      // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    }
    .cloud-provider-wrapper {
      background-color: var(--semantic-color-background-primary);
    }
    .configuration-nav-tab {
      background-color: transparent;
    }
    .radio-button-container .radio-buttons {      
      border: none;
      .radio-button.checked-true label {
        border: none;
        min-height: none;
        .check-circle {
          margin-right: 4px;
        }
      }
    }
    .cell-container {
      .prov-status {
        .radio-button-container {
          .radio-buttons {
            min-width: auto;
            .radio-button {
              &:last-child {
                min-width: 125px;
                label {
                  padding-right: 10px;
                }
              }
              &:first-child {
                width: 85px;
                label {
                  padding-right: 5px;
                }
              }
              &.checked-false {
                label {
                  border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
                  cursor: pointer;
                  background: var(--semantic-color-content-interactive-primary-default);
                  color: var(--semantic-color-surface-interactive-secondary-default);
                }
                // label {
                //   border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
                //   cursor: pointer;
                //   color: var(--semantic-color-content-base-subdued);
                // }
                &.disabled {
                  label {
                    cursor: not-allowed;
                    color: var(--semantic-color-content-inverted-interactive-disabled);
                  }
                }
              }
              &.checked-true {
                label {
                  cursor: pointer;
                }
                &.disabled {
                  label {
                    cursor: not-allowed;
                    background: var(--semantic-color-border-interactive-primary-disabled);
                    color: var(--semantic-color-content-base-tertiary);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.wizard-form.configure-provisioning-template {
  .form-sections-container {
    .form-section.flex-direction-column {
      flex-direction: column;
    }

  }

}
}