import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { PROVISIONING_TEMPLATE_TABLE_CONFIGS } from 'ducks/provisioningTemplates/constants';
import ConfigTable from 'components/configTable';
import { getReadOnly } from 'utils/helpers';
import ProvURL from '../ProvURL';
import NukeButton from '../ProvURL/NukeButton';

class ProvisioningTemplatesTable extends PureComponent {
  static propTypes = {
    provisioningTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
    handleProvUrlCopyText: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    loadMore: PropTypes.func,
  };

  static defaultProps = {
    provisioningTemplatesData: null,
    handleProvUrlCopyText: null,
    toggleDeleteConfirmationForm: null,
    toggleProvTemplateWizard: null,
    handleViewProvForm: null,
    accessPrivileges: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
    loadMore: null,
  };

  getTableData = () => {
    const { provisioningTemplatesData, accessPrivileges, authType } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);

    const tableData = provisioningTemplatesData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        desc: row.desc,
        lastModTime: row.lastModTime,
        lastModUid: row.lastModUid,
        provUrlData: row.provUrlData,
        provUrl: row.provUrl,
        provUrlType: row.provUrlType,
        status: row.status,
        isReadOnly: row.status === 'DEPLOYED' || isReadOnly,
        isDeletable: row.status !== 'DEPLOYED' && !isReadOnly,
        isEditable: row.status !== 'DEPLOYED' && !isReadOnly,
      };
    });
    return tableData;
  };

  render() {
    const {
      toggleProvTemplateWizard,
      toggleDeleteConfirmationForm,
      handleViewProvForm,
      provisioningTemplatesData,
      handleProvUrlCopyText,
      hasNextPage,
      loadMore,
      moreItemsLoading,
    } = this.props;
  
    return (
      <ConfigTable
        {...PROVISIONING_TEMPLATE_TABLE_CONFIGS}
        onHandleRowEdit={(e) => toggleProvTemplateWizard(e, true, 'EDIT')}
        onHandleRowDelete={toggleDeleteConfirmationForm}
        onHandleRowView={handleViewProvForm}
        
        // eslint-disable-next-line react/jsx-props-no-multi-spaces
        renderRowSubComponent={(props) => {
          const { row = {} } = props || {};
          const { original = {} } = row || {};
          const { id } = original || {};

          return (
            <ProvURL
              {...props}
              provisioningTemplatesData={provisioningTemplatesData}
              handleClick={handleProvUrlCopyText}>
              <NukeButton
                id={id} />
            </ProvURL>
          );
        }}
        moreItemsLoading={moreItemsLoading}
        hasNextPage={hasNextPage}
        loadMore={() => loadMore(false)}
        pagination
        data={this.getTableData()} />
    );
  }
}

export default withTranslation()(ProvisioningTemplatesTable);
