// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Trans, withTranslation } from 'react-i18next';
import { formValueSelector, isValid } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDotCircle, faCircle, faCircleCheck } from '@fortawesome/pro-solid-svg-icons';

import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';

import './index.scss';

const formValidity = {};
const selector = formValueSelector('provisioningTemplateWizard');
let type;

function WizardNav(props) {
  const {
    activePage,
    typeFromState,
    valid,
    wizardNavConfig,
    cloudName,
    cloud,
    premise,
  } = props;

  // track form validity for each sub-form
  formValidity[activePage] = valid;

  // if a sub-form is invalid, mark all next sub-forms as invalid
  if (!valid) {
    wizardNavConfig.forEach((item, index) => {
      if (index > activePage) {
        formValidity[index] = false;
      }
    });
  }

  // if the entire wizard form is filled out and user comes back to 1st screen and
  // changes the type, unset validity for all other pages to reflect change
  // in Additional params
  if (activePage === 0 && type !== typeFromState) {
    for (let i = 1, l = wizardNavConfig.length; i < l; i += 1) {
      formValidity[i] = false;
    }

    type = typeFromState;
  }
  
  return (
    <div className="wizard-nav">
      <ul>
        {wizardNavConfig.map((item, index) => {
          let widgetTitle = <span className="name"><Trans>{item}</Trans></span>;
          if (index === 1) {
            if (cloud) {
              widgetTitle = <span className="name"><Trans>CLOUD_PROVIDER</Trans></span>;
            } else if (premise) {
              widgetTitle = <span className="name"><Trans>ON_PREMISE</Trans></span>;
            }
          }

          return (
            <li
              className={`${index === activePage ? 'active' : ''} ${index < activePage ? 'valid' : ''}`}
              key={item}>
              <button
                type="button"
                // onClick={() => goToPage(index)}
                disabled={index !== 0 && !formValidity[index - 1]}>
                <span className="fa-layers fa-fw">
                  {index === activePage && <FontAwesomeIcon icon={faDotCircle} size="lg" />}
                  {index > activePage && <FontAwesomeIcon icon={faCircle} size="lg" />}
                  {index < activePage && <FontAwesomeIcon icon={faCircleCheck} className="fa-check-circle-normal" size="lg" />}
                </span>
                {widgetTitle}
                <span className={index === 1 && (cloud === true) ? '' : 'hide'}>: </span>
                <span className={`name ${index === 1 ? '' : 'hide'}`}><Trans>{cloudName}</Trans></span>
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
}

WizardNav.propTypes = {
  activePage: PropTypes.number,
  typeFromState: PropTypes.string,
  valid: PropTypes.bool,
  wizardNavConfig: PropTypes.arrayOf(PropTypes.string),
  cloud: PropTypes.bool,
  premise: PropTypes.bool,
  cloudName: PropTypes.string,
};

WizardNav.defaultProps = {
  activePage: 0,
  typeFromState: '',
  valid: false,
  wizardNavConfig: {},
  cloud: false,
  premise: false,
  cloudName: '',
};

export default connect((state) => ({
  typeFromState: selector(state, 'type'),
  valid: isValid('provisioningTemplateWizard')(state),
  cloud: provisioningTemplatesSelector.cloudSelector(state),
  premise: provisioningTemplatesSelector.premiseSelector(state),
  cloudName: provisioningTemplatesSelector.cloudNameSelector(state),
}))(withTranslation()(WizardNav));

export { WizardNav };
