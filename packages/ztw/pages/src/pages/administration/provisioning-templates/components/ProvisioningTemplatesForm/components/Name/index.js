// @flow

import React from 'react';
import { Field, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { get } from 'utils/lodash';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';
import {
  required,
  noWhiteSpacesAllowed,
  maxLength,
} from 'utils/validations';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';

import './index.scss';

const maxLength128 = maxLength(128);
const maxLength10240 = maxLength(10240);

function ConfigureEdgeconnectorForm(props) {
  const {
    closeModal,
    handleSubmit,
    t,
    formMeta,
    formSyncErrors,
  } = props;

  const fieldName = 'name';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  const hasError = meta && meta.touched && !!error;

  const fieldName2 = 'desc';
  const meta2 = get(formMeta, fieldName2, null);
  const error2 = get(formSyncErrors, fieldName2, null);
  const hasError2 = meta2 && meta2.touched && !!error2;

  const generalInfo = (t('CC_GENERAL_INFORMATION_DESCRIPTION')).split(/{[0-9]}/g);
  const generalInfoJSX = (
    <>
      {generalInfo[0]}
      <a href="https://help.zscaler.com/cloud-connector/about-cloud-provisioning-templates" target="_blank" rel="noopener noreferrer" className="external-link-button">
        {/* //className="tooltip-navlink"> */}
        {generalInfo[1]}
      </a>
      {generalInfo[2]}
    </>
  );

  return (
    <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
      <div className="form-sections-container">
        <div className="form-section">
          <div className="cc-provisioning-general-info-title">
            {t('GENERAL_INFORMATION')}
            <div className="cc-provisioning-general-info-description">
              {generalInfoJSX}
            </div>
          </div>
          <div className="full-width">
            <FormFieldLabel
              styleClass={`${hasError ? 'invalid' : ''}`}
              error={hasError ? t(error) : null}
              text={t('NAME')}
              tooltip={t('TOOLTIP_PROV_TEMPLATE_NAME')} />
            <Field
              id="name"
              name="name"
              component={Input}
              placeholder={t('ENTER_TEXT')}
              validate={[
                required,
                noWhiteSpacesAllowed,
                maxLength128,
              ]}
              styleClass="max-width" />

            <FormFieldLabel
              error={hasError2 ? t(error2) : null}
              styleClass={`${hasError2 ? 'invalid' : ''}`}
              text={t('DESCRIPTION')}
              tooltip={t('TOOLTIP_POLICY_FIREWALL_DESCRIPTION')} />
            <Field
              id="desc"
              name="desc"
              component="textarea"
              placeholder={t('ENTER_DESCRIPTION')}
              validate={[maxLength10240]}
              className="cc-provisioning-description" />
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={closeModal}>{t('BACK')}</button>
          <button type="submit" disabled={hasError || hasError2} className="next primary-button">{t('NEXT')}</button>
        </div>
      </div>
    </form>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
};

ConfigureEdgeconnectorForm.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  t: (str) => str,
  formMeta: {},
  formSyncErrors: {},
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelector(state),
  wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  formValues: provisioningTemplateWizardSelectors.formValuesSelector(state),
  formMeta: provisioningTemplateWizardSelectors.formMetaSelector(state),
  formSyncErrors: provisioningTemplateWizardSelectors.formSyncErrorsSelector(state),
});

const ProvName = connect(mapStateToProps, null)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default ProvName;
