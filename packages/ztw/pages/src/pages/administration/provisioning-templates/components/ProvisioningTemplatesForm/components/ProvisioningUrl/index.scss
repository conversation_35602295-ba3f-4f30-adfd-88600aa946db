@import "scss/colors.scss";

.ec-root-page {
.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section.provisioning-url {
            .form-field-label,
            .disabled-input {
             color: $grey10;
            }
            .input-container {
              &.review {
                &.prov-url {
                  margin: 10px 0;
                  height: auto;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    background-color: var(--semantic-color-background-secondary);
                    padding: 14px;
                    display: block;
                    border-radius: 5px;
                    span {
                      color: $grey4;
                    }
                    span.copy-prov-url {
                      float: right;
                      width: 180px;
                      cursor: pointer;
                      color: var(--semantic-color-content-interactive-primary-default);
                    }
                  }
                  &.desc {
                    margin-top: 0;
                    .disabled-input {
                      color: var(--semantic-color-content-interactive-primary-disabled);
                      margin-top: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
}