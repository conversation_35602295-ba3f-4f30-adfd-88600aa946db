import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import Loading from 'components/spinner/Loading';
import { useTranslation, withTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/pro-solid-svg-icons';
import { verifyNukeButton, handleNuke } from 'ducks/provisioningTemplatesBranch';
import * as selectors from 'ducks/provisioningTemplatesBranch/selectors';

function NukeButton(props) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { id } = props;
  const [isLoading, setIsLoading] = useState(true);
  let isNuke;
  
  isNuke = useSelector((state) => selectors.isNukeSelector(state));
  const isNukeLoading = useSelector((state) => selectors.isNukeLoadingSelector(state));
  useEffect(() => {
    if ((typeof isNuke[id] === 'undefined') && isLoading) {
      dispatch(verifyNukeButton(id));
    }
    setIsLoading(false);
  }, [isNukeLoading]);
  
  isNuke = useSelector((state) => selectors.isNukeSelector(state));

  if (!isNuke[id] || isLoading) return <Loading loading={((typeof isNuke[id] === 'undefined'))} />;

  return (
    <button
      onClick={() => dispatch(handleNuke(id))}
      type="button"
      className="cancel">
      <FontAwesomeIcon icon={faTrash} />
      {' '}
      {t('FORCE_DELETE_VM')}
    </button>
  );
}
  
NukeButton.propTypes = {
  id: PropTypes.number,
};
  
NukeButton.defaultProps = {
  id: null,
};

export default withTranslation()(NukeButton);
