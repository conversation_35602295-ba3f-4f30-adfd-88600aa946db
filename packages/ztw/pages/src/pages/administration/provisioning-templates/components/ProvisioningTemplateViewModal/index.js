// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal } from 'ducks/provisioningTemplates';

import * as ProvisioningTemplatesSelectors from 'ducks/provisioningTemplates/selectors';
import './index.scss';

export function BasicCustomAppForm({
  actions,
  t,
  appData,
}) {
  const {
    name,
    desc,
    provUrl,
    provUrlData: {
      formFactor,
      cloudProvider,
      locationTemplate,
    },
  } = appData;

  const accountName = (cloudProvider && cloudProvider.name) ? cloudProvider.name : '';
  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(appData, false);
  };
  
  return (
    <form className="wizard-form configure-provisioning-template">
      <div className="form-sections-container cc-provisoning">
        {' '}
        <p className="review-section-title">{t('GENERAL_INFORMATION')}</p>
        <div className="form-section provisioning-url">
          <div className="input-container review half-width">
            <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{name}</p>
          </div>
          <div className="input-container review half-width">
            <FormFieldLabel text={t('Account Name')} styleClass="no-margin-top" />
            <p className="disabled-input">{accountName || t('NONE')}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('PROVISIONING_URL')} styleClass="no-margin-top" />
            <p className="disabled-input full-width">{provUrl}</p>
          </div>
        </div>

        <p className="review-section-title">{t('LOCATION_INFORMATION')}</p>
        <div className="form-section provisioning-url">
          <div className="input-container review half-width">
            <FormFieldLabel text={t('LOCATION_TEMPLATE')} styleClass="no-margin-top" />
            <p className="disabled-input">{(locationTemplate && locationTemplate.name) || t('NONE')}</p>
          </div>
          <div className="input-container review half-width">
            <FormFieldLabel text={t('LOCATION_NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{t('AUTOMATIC')}</p>
          </div>
          <div className="input-container review half-width">
            <FormFieldLabel text={t('NO_OF_EDGE_CONNECTOR_GROUPS')} styleClass="no-margin-top" />
            <p className="disabled-input">{t('AUTOMATIC')}</p>
          </div>
          <div className="input-container review half-width">
            <FormFieldLabel text={t('VM_SIZE')} styleClass="no-margin-top" />
            <p className="disabled-input">{formFactor}</p>
          </div>

          <div className="input-container review half-width">
            <FormFieldLabel text={t('DESCRIPTION')} styleClass="no-margin-top" />
            <p className="disabled-input">{desc}</p>
          </div>
        </div>
 
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const ProvisioningTemplateViewModal = reduxForm({
  form: 'ProvisioningTemplateViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...ProvisioningTemplatesSelectors.default(state),
  appData: ProvisioningTemplatesSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ProvisioningTemplateViewModal)));
