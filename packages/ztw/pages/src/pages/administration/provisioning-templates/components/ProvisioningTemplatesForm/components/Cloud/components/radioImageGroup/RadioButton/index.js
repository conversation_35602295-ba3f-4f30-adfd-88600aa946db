/* eslint-disable react/no-unknown-property */
import React from 'react';
import PropTypes from 'prop-types';
import awsLogo from 'images/logo-aws.png';
import azure<PERSON>ogo from 'images/logo-azure.png';
import gcpLogo from 'images/logo-gcp.png';

const IMG_CONFIG = {
  AWS: awsLogo,
  AZURE: azureLogo,
  GCP: gcpLogo,
};

function RadioButton(props = {}) {
  const {
    input,
    disabled,
    checked,
    // preSelect,
  } = props;

  input.checked = checked;

  return (
    <div className={`radio-button checked-${input.checked}`}>
      <label htmlFor={input.checked}>
        <input
          appname={input.value}
          type="radio"
          disabled={disabled}
          checked={checked}
          {...input} />
        <img
          src={IMG_CONFIG[input.value]}
          alt={`${IMG_CONFIG[input.value]} logo`} />
      </label>
    </div>
  );
}

RadioButton.defaultProps = {
  disabled: false,
};

RadioButton.propTypes = {
  disabled: PropTypes.bool,
  appName: PropTypes.string,
  checked: PropTypes.bool,
  preSelect: PropTypes.string,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
    checked: PropTypes.bool,
  }).isRequired,
};

export default RadioButton;
