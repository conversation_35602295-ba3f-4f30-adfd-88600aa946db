// @flow

import React, { PureComponent } from 'react';
import {
  change,
  Field,
  reduxForm,
  autofill,
  getFormValues,
} from 'redux-form';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop, isEmpty } from 'utils/lodash';

import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';

import { FormFieldLabel } from 'components/label';
import {
  required,
} from 'utils/validations';
import LocationTemplatesEntityDropdown from 'commonConnectedComponents/dropdown/LocationTemplatesEntityDropdown';

class ConfigureEdgeconnectorForm extends PureComponent {
  componentDidMount() {
    const { actions, initialValues, wizardFieldValues } = this.props;
    const { locationTemplate = {} } = initialValues.provUrlData;

    if (!isEmpty(locationTemplate)) {
      actions.autoFillReduxForm('provisioningTemplateWizard', 'locationTemplate', locationTemplate);
    } else if (!isEmpty(wizardFieldValues.locationTemplate)) {
      actions.autoFillReduxForm('provisioningTemplateWizard', 'locationTemplate', wizardFieldValues.locationTemplate);
    }
  }

  render() {
    const {
      closeModal,
      handleSubmit,
      t,
      previousPage,
    } = this.props;

    return (
      <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
        <div className="form-sections-container">
          <div className="form-section flex-direction-column">
            <div className="cc-provisioning-general-info-title">
              {t('LOCATION')}
              <div className="cc-provisioning-general-info-description">
                {t('PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW')}
              </div>
            </div>
            <div className="half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_LOCATION_CREATION')}
                text={t('LOCATION_CREATION')} />
              <span className="disabled-input">{t('AUTOMATIC')}</span>
              <FormFieldLabel
                tooltip={t('TOOLTIP_LOCATION_TEMPLATE')}
                text={t('LOCATION_TEMPLATE')} />
              <Field
                id="locationTemplate"
                name="locationTemplate"
                component={LocationTemplatesEntityDropdown}
                onChange={noop}
                validate={[
                  required,
                ]}
                required />
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <div>
            <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
            <button type="submit" className="next primary-button">{t('NEXT')}</button>
          </div>
        </div>
      </form>
    );
  }
}

ConfigureEdgeconnectorForm.propTypes = {
  actions: PropTypes.shape(),
  initialValues: PropTypes.shape(),
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  previousPage: PropTypes.func,
  wizardFieldValues: PropTypes.shape(),
  t: PropTypes.func,
};

ConfigureEdgeconnectorForm.defaultProps = {
  actions: {},
  initialValues: {
    provUrlData: {
      locationTemplate: {},
    },
  },
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  previousPage: null,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelector(state),
  wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateFormState: change,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const Location = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default Location;
