import React, { PureComponent } from 'react';
import {
  change, reduxForm, Field, getFormValues,
} from 'redux-form';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';
import { ToggleCheckBox } from 'components/ecToggle';

import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  handleVMSizeChange,
  setSelectedVMSize,
  handleAutoScalingChange,
} from 'ducks/provisioningTemplates';

import { FormFieldLabel } from 'components/label';
import { verifyConfigData } from 'utils/helpers';

// import {
// // CellularConfiguration,
// // CellularDeploymentMode,
// // CellularDeployment,
// // CapacityField,
// } from './components';

import './index.scss';

class ConfigureEdgeconnectorForm extends PureComponent {
  componentDidMount() {
    const {
      actions, cloudName, formValues, configData,
    } = this.props;

    const enableASG = (verifyConfigData({ configData, key: 'enableASG' }) && cloudName === 'AWS')
    || (verifyConfigData({ configData, key: 'enableAzureASG' }) && cloudName === 'AZURE');

    // Remove to enable Medium and Large to GCP
    const { formFactor } = (formValues && formValues.provUrlData) || {};
    if (cloudName === 'GCP' && formFactor !== 'SMALL') {
      formValues.provUrlData.formFactor = 'SMALL';
    }
    // Remove to enable Medium and Large to GCP

    if (enableASG && !formValues.provUrlData.autoScaleDetails) {
      actions.updateFormState('provisioningTemplateWizard', 'provUrlData.autoScaleDetails.autoScale', false);
    }

    actions.setSelectedVMSize(formValues);
  }

  render() {
    const {
      closeModal,
      handleSubmit,
      t,
      previousPage,
      actions,
      formFactor,
      configData,
      cloudName,
      formValues,
      wizardFieldValues,
      initialValues,
    } = this.props;
    const { provUrlData: { cloudProviderType } = {} } = formValues || {};
    const { provUrlData: { autoScaleDetails } } = initialValues;
    const { autoScale } = autoScaleDetails || {};

    const enableMediumAzure = ((cloudName || cloudProviderType) !== 'AZURE') || ((cloudName || cloudProviderType) === 'AZURE' && verifyConfigData({ configData, key: 'enableMediumCCAzure' }));
    const enableMedium = enableMediumAzure && (cloudName || cloudProviderType) !== 'GCP' && verifyConfigData({ configData, key: 'enableMediumCC' });
    const enableLargeAzure = ((cloudName || cloudProviderType) !== 'AZURE') || ((cloudName || cloudProviderType) === 'AZURE' && verifyConfigData({ configData, key: 'enableLargeCCAzure' }));
    const enableLarge = enableLargeAzure && (cloudName || cloudProviderType) !== 'GCP' && verifyConfigData({ configData, key: 'enableLargeCC' });
    const isASGEnabled = (
      (wizardFieldValues.provUrlData.autoScaleDetails
          && wizardFieldValues.provUrlData.autoScaleDetails.autoScale)
        ? wizardFieldValues.provUrlData.autoScaleDetails.autoScale : autoScale) || false;

    const enableASG = (verifyConfigData({ configData, key: 'enableASG' }) && ((cloudName || cloudProviderType) === 'AWS'))
    || (verifyConfigData({ configData, key: 'enableAzureASG' }) && (cloudName || cloudProviderType) === 'AZURE');

    return (
      <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template cc-group-details">
        <div className="form-sections-container">
          <div className="form-section">
            <div className="cc-provisioning-general-info-title">
              {t('GROUP_INFORMATION')}
              <div className="cc-provisioning-general-info-description">
                {t('PLEASE_ENTER_THE_FOLLOWING_INFORMATION_BELOW')}
              </div>
            </div>
            <div className="full-width">
              <FormFieldLabel text={t('CLOUD_CONNECTOR_GROUP_CREATION')} />
              <span className="disabled-input">{t('AUTOMATIC')}</span>
            </div>
  
            <div className="dropdown-container half-width">
              <FormFieldLabel text={t('EDGE_CONNECTOR_VM_SIZE')} />
              <ECRadioGroup
                id="formFactor"
                name="formFactor"
                styleClass={((!enableMedium && !enableLarge) || (isASGEnabled && enableASG)) ? 'single-option' : ''}
                onChange={actions.handleVMSizeChange}
                options={[{
                  name: 'formFactor', value: 'SMALL', checked: formFactor === 'SMALL', label: t('SMALL'),
                },
                ...(enableMedium && !(isASGEnabled && enableASG) ? [{
                  name: 'formFactor', value: 'MEDIUM', checked: formFactor === 'MEDIUM', label: t('MEDIUM'),
                }] : []),
                ...(enableLarge && !(isASGEnabled && enableASG) ? [{
                  name: 'formFactor', value: 'LARGE', checked: formFactor === 'LARGE', label: t('LARGE'),
                }] : []),
                ]} />
            </div>
            {enableASG && (
              <div className="full-width">
                <FormFieldLabel text={t('AUTO_SCALING')} />
                <Field
                  id="provUrlData.autoScaleDetails.autoScale"
                  name="provUrlData.autoScaleDetails.autoScale"
                  styleClass="ec-toggle-checkbox"
                  disabled={autoScale}
                  onChange={actions.handleAutoScalingChange}
                  component={ToggleCheckBox} />
              </div>
            )}
            {/* {isASGEnabled && (
              <div className="full-width dropdown-container">
                <FormFieldLabel styleClass="auto-scale-options"
                 text={`${cloud} ${t('AUTO_SCALING_OPTIONS')}`} />
                <div className="capacity-field">
                  <CapacityField fieldName="provUrlData.autoScaleDetails.capacity.min"
                   labelName="MIN_CAPACITY" {...this.props} />
                </div>
                <div className="capacity-field">
                  <CapacityField fieldName="provUrlData.autoScaleDetails.capacity.max"
                   labelName="MAX_CAPACITY" {...this.props} />
                </div>
                <div className="capacity-field">
                  <CapacityField fieldName="provUrlData.autoScaleDetails.capacity.desired"
                  labelName="DESIRED_CAPACITY" {...this.props} />
                </div>
              </div>
            )} */}
          </div>
        </div>
        <div className="modal-footer">
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <div>
            <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
            <button type="submit" className="next primary-button">{t('NEXT')}</button>
          </div>
        </div>
      </form>
    );
  }
}

ConfigureEdgeconnectorForm.propTypes = {
  actions: PropTypes.shape(),
  configData: PropTypes.shape(),
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  previousPage: PropTypes.func,
  initialValues: PropTypes.shape(),
  formValues: PropTypes.shape(),
  t: PropTypes.func,
  formFactor: PropTypes.string,
  cloudName: PropTypes.string,
  wizardFieldValues: PropTypes.shape({
    provUrlData: PropTypes.shape({
      autoScaleDetails: PropTypes.shape({
        autoScale: PropTypes.bool,
      }),
    }),
  }),
};

ConfigureEdgeconnectorForm.defaultProps = {
  actions: {},
  configData: {},
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  previousPage: (str) => str,
  initialValues: {},
  formValues: {},
  cloudName: '',
  t: (str) => str,
  formFactor: 'SMALL',
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelector(state),
  formValues: provisioningTemplatesSelector.formValuesSelector(state),
  formMeta: provisioningTemplatesSelector.formMetaSelector(state),
  formSyncErrors: provisioningTemplatesSelector.formSyncErrorsSelector(state),
  wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  configData: loginSelectors.configDataSelector(state),
  subscriptions: loginSelectors.accessSubscriptionSelector(state),
  wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateFormState: change,
    handleVMSizeChange,
    setSelectedVMSize,
    handleAutoScalingChange,
  }, dispatch);

  return {
    actions,
  };
};

const EdgeConnectorGroups = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default EdgeConnectorGroups;
