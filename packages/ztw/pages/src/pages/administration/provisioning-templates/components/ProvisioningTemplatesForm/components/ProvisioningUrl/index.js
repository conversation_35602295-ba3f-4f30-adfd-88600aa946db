// @flow

import React, { PureComponent } from 'react';
import { getFormValues, reduxForm } from 'redux-form';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { toUpper, noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-solid-svg-icons';
import {
  handleCopyText,
} from 'ducks/provisioningTemplates';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';

import './index.scss';

export class ConfigureEdgeconnectorForm extends PureComponent {
  static propTypes = {
    handleSubmit: PropTypes.func,
    t: PropTypes.func,
    wizardFieldValues: PropTypes.shape(),
    cloud: PropTypes.bool,
    cloudName: PropTypes.string,
    provisioningTemplateData: PropTypes.shape(),
    handleCopyText1: PropTypes.func,
  };

  static defaultProps = {
    handleSubmit: (str) => str,
    t: (str) => str,
    wizardFieldValues: {},
    cloud: false,
    cloudName: '',
    provisioningTemplateData: {},
    handleCopyText1: null,
  };

  handleCopyProvUrlClick = (e) => {
    e.preventDefault();
    const { handleCopyText1 } = this.props;
    const parentElementText = e.currentTarget.parentElement.children[0].innerText;
    
    handleCopyText1(e.currentTarget.parentElement, parentElementText);
  };

  render() {
    const {
      handleSubmit,
      t,
      wizardFieldValues,
      cloudName,
      cloud,
      provisioningTemplateData,
    } = this.props;
  
    const { provUrl, provUrlData } = provisioningTemplateData;

    const { autoScaleDetails } = provUrlData;
    // update wizard values based on cloud selection
    wizardFieldValues.platform = cloud ? toUpper(cloudName) : '';
    
    return (
      <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
        <div className="form-sections-container">
          <div className="review-changes-heading">
            <FormSectionLabel text={t('PROVISIONING_URL')} />
          </div>
          <div className="form-section provisioning-url">
            <div className="input-container review prov-url full-width">
              <FormFieldLabel text={t('PROVISIONING_URL')} styleClass="no-margin-top" />
              <p className="disabled-input">
                <span>{provUrl}</span>
                <span className="copy-prov-url" role="button" tabIndex="0" onKeyDown={noop} onClick={this.handleCopyProvUrlClick}>
                  <FontAwesomeIcon icon={faCopy} size="lg" />
                  {t('COPY_PROVISIONING_URL')}
                </span>
              </p>
            </div>
          </div>
          <div className="form-section provisioning-url">
            <div className="input-container review">
              <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
              <p className="disabled-input">{wizardFieldValues.name}</p>
            </div>
            <div className="input-container review">
              <FormFieldLabel text={t('Account Name')} styleClass="no-margin-top" />
              <p className="disabled-input">{wizardFieldValues.platform || t('NONE')}</p>
            </div>
          </div>
          <p className="review-section-title">{t('LOCATION_INFORMATION')}</p>
          <div className="form-section provisioning-url">
            <div className="input-container review">
              <FormFieldLabel text={t('LOCATION_TEMPLATE')} styleClass="no-margin-top" />
              <p className="disabled-input">{wizardFieldValues.locationTemplate ? wizardFieldValues.locationTemplate.name : t('NONE')}</p>
            </div>
            <div className="input-container review">
              <FormFieldLabel text={t('LOCATION_NAME')} styleClass="no-margin-top" />
              <p className="disabled-input">{t('AUTOMATIC')}</p>
            </div>
          </div>
          <p className="review-section-title">{t('GROUP_INFORMATION')}</p>
          <div className="form-section provisioning-url">
            <div className="input-container review">
              <FormFieldLabel text={t('NO_OF_EDGE_CONNECTOR_GROUPS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t('AUTOMATIC')}</p>
            </div>
            <div className="input-container review">
              <FormFieldLabel text={t('VM_SIZE')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(provUrlData.formFactor)}</p>
            </div>
            { autoScaleDetails && (
              <>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('AUTO_SCALING')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t(autoScaleDetails.autoScale)}</p>
                </div>
                {/* <div className="input-container review half-width">
                  <FormFieldLabel text={t('MIN_CAPACITY')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t(autoScaleDetails.capacity.min)}</p>
                </div>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('MAX_CAPACITY')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t(autoScaleDetails.capacity.max)}</p>
                </div>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('DESIRED_CAPACITY')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t(autoScaleDetails.capacity.desired)}</p>
                </div> */}
              </>
            )}
          </div>
          <p className="review-section-title">{t('DESCRIPTION')}</p>
          <div className="form-section provisioning-url">
            <div className="input-container review desc">
              <br />
              <p className="disabled-input">{wizardFieldValues.desc}</p>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button type="submit" className="next primary-button">{t('DONE')}</button>
        </div>
      </form>
    );
  }
}

let ProvisioningUrl = reduxForm({ // eslint-disable-line import/no-mutable-exports
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm));

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleCopyText1: handleCopyText,
  }, dispatch);

  return actions;
};

ProvisioningUrl = connect(
  (state) => ({
    wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
    cloud: provisioningTemplatesSelector.cloudSelector(state),
    cloudName: provisioningTemplatesSelector.cloudNameSelector(state),
    provisioningTemplateData: provisioningTemplatesSelector.provisioningTemplateDataSelector(state),
  }),
  mapDispatchToProps,
)(ProvisioningUrl);

export default ProvisioningUrl;
