@import "scss/colors.scss";

.ec-root-page {
.cc-provisioning-modal-content .wizard-form.configure-provisioning-template .form-sections-container {
  .form-section-label .form-field-label {
    margin-left: 0px;
  }
  .form-section {
    .disabled-input {
      color: var(--semantic-color-content-interactive-primary-disabled);
      margin-left: 0px;
    }
  }
  .review-section-title{
    margin-left: 0px;
    margin-top: 30px;
  }
}

// .review-section-title {
//   text-transform: uppercase;
//   color: $grey10;
//   font-size: 13px;
//   font-weight: 500;
//   letter-spacing: 0;
//   line-height: 16px;
//   border: none;
//   margin: 0px 0px 0px 10px;
// }
.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section {
            .input-container {
              &.review {
                margin: 10px 0;
                height: auto;
                .disabled-input {
                  color: var(--semantic-color-content-interactive-primary-disabled);
                }
                &.desc {
                  margin-top: 0;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    margin-top: 0;
                  }
                } 
              }
            }
          }
        } 
      }
    }
  }
}
}