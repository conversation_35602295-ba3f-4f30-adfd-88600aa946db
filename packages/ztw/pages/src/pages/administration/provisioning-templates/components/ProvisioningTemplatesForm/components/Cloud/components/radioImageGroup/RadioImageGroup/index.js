import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { noop } from 'utils/lodash';

import RadioButton from '../RadioButton';

function RadioImageGroup(props = {}) {
  const {
    name,
    options,
    disabled,
    styleClass,
    id,
    onChange,
  } = props;

  return (
    <div className={`image-radio-button-container ${styleClass}`}>
      <div className={`radio-buttons ${disabled ? 'disabled' : ''}`} id={id}>
        {options.map((item) => (
          <Field
            key={item.value}
            disabled={disabled || item.disabled}
            name={name}
            checked={item.checked}
            component={RadioButton}
            onChange={onChange}
            type="radio"
            value={item.value} />
        ))}
      </div>
    </div>
  );
}

RadioImageGroup.defaultProps = {
  disabled: false,
  styleClass: '',
  id: null,
  onChange: noop,
};

RadioImageGroup.propTypes = {
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    name: PropTypes.string,
    value: PropTypes.bool,
    disabled: PropTypes.bool,
  })).isRequired,
  disabled: PropTypes.bool,
  styleClass: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func,
};

export default RadioImageGroup;
