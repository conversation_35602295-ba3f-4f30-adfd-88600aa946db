@import "scss/colors.scss";
@import "scss/mixins.scss";

.image-radio-button-container {
  .radio-buttons {
    @include DisplayFlex;

    &.disabled {
      .radio-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }

        &.checked-true {
          label {
            background: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-background-primary);
          }
        }
      }
    }

    .radio-button {
      &.checked-true {
        label {
          border: 3px solid var(--semantic-color-content-interactive-primary-default);
          border-radius: 5px;
          margin: 10px;
        }
      }
      &:first-child {
        label {
          margin-left: 0;
        }
      }
      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        border: 1px solid $grey7;
        color: var(--semantic-color-content-interactive-primary-default);
        align-items: center;
        justify-content: center;
        margin: 10px;
        width: 178px;
        height:  106px;
        border-radius: 5px;

        input {
          display: none;
        }
        img {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}
