// @import "scss/colors.scss";
// @import "scss/mixins.scss";
// .wizard-form .form-sections-container .form-section {
//     .cellular-configuration-container {
//         display: flex;
//         flex-direction: column;
//         .cellular-configuration-mode {
//             display: flex;
//             width: fit-content;
//             column-gap: 4px;

//             label.form-field-label-wrapper {
//                 display: inline;

//                 span.form-field-label.disabled-input {
//                     font-weight: normal;
//                     line-height: unset;
//                     width: fit-content;
//                     margin-right: 4px;
//                 }
//             }
//         }
//     }
// }

// .wizard-form.configure-provisioning-template.cc-group-details{
//   .information-icon {
//     color: var(--semantic-color-content-status-info-primary);
//     font-size: initial;
//   } 
// }
// .full-width {
//   width: 100% !important;
//   float: left;
// }
// .half-width {
//   width: 47% !important;
//   float: left;
// }
// .margin-right-2 {
//   margin-right: 2%;
// }
// .margin-left-2 {
//   margin-left: 2%;
// }
// .edgeconnector-modal {
//   // position: absolute;
//   // top: 0;
//   // left: 90px;
//   // background: $white;
//   // height: auto;
//   // min-height: 100%;
//   // width: calc(100vw - 90px);
//   // padding: 25px 50px 100px;
//   // box-sizing: border-box;
//   // z-index: 1;
//   // display: flex;
//   // flex-flow: column;

//   .modal-content {
//     &.cc-provisioning-modal-content{
//       overflow: visible;
//     }
//     margin-top: 30px;
//     display: flex;
//     width: 100%;
//     padding: 0;
//     float: left;
//     align-items: stretch;
//     border: 1px solid $grey29;
//     border-radius: 10px;
//     position: relative;
//     min-height:  500px;
//     .wizard-nav {
//       width: 25%;
//       float: left;
//       border-right: 1px solid var(--semantic-color-border-base-primary);
//       ul {
//         display: -webkit-box;
//         display: -moz-box;
//         display: -ms-flexbox;
//         display: -webkit-flex;
//         display: flex;
//         justify-content: space-between;
//         flex-direction: column;
//         list-style: none;
//         padding-top: 20px;
//         li {
//           width: 100%;
//           background: none;
//           .name {
//             color: $grey30;
//             line-height: 50px;
//           }
//           &.active {
//             &:after {
//               content: " ";
//               display: inline-block;
//               height: 100%;
//               position: absolute;
//               border-width: 0 3px 0 0;
//               border-style: solid;
//               border-color:$blue21;
//               z-index: 1;
//               top: 0;
//               right: 0;
//             }
//             button {
//               background-color: $blue22;
//               color: $blue21;
//               &:after {
//                 border-color: $blue21;
//               }
//               .fa-dot-circle {
//                 font-size: 15px;
//               }
//               .name {
//                 color: $grey31;
//                 font-weight: 500;
//                 line-height: initial;
//               }
//             }
//           }
//           &.valid {
//             button {
//               &.fa-circle {
//                   color: $blue21;
//               }
//               &:after {
//                 border-color: $blue21;
//               }
//               .fa-circle {
//                   color: $blue21;
//               }
//             }
//           }
//           &:last-of-type button:after {
//             border-width: 0;
//           }
//           &:before {
//             display: none;
//           }
//           button {
//             text-align: left;
//             padding-left: 20px;
//             .fa-layers {
//               .fa-circle {
//                 font-size: 15px;
//               }
//             }
//             &:hover {
//               background-color: $white;
//               &:not(:disabled) {
//                 background-color: $blue22;
//               }
//             }
//             &:after {
//               content: " ";
//               display: inline-block;
//               height: 0;
//               width: 0;
//               position: absolute;
//               border-width: 32px 0 0 0;
//               border-style: solid;
//               width: 1px;
//               border-color: black;
//               z-index: 1;
//               top:  33px;
//               left: 28px;
//             }
//           }
//         }
//       }
//     }
//     .wizard-form {
//       width: 75%;
//       float: left;
//       padding: 0;
//       .form-sections-container {
//         max-height: none;
//         border: none;
//         padding: 3% 4%;
//         height: 100%;
//         .form-section-label {
//           padding: 22px 24px 22px 0;
//         }
//         .form-section {
//           box-shadow: none;
//           padding: 0;
//           margin: 0;
//           .dropdown-container {
//             width: 100%;
//             margin-top:  23px;
//             overflow: hidden;
//             .form-field-label {
//               margin-top: 0;
//             }
//           }
//           .image-wrapper {
//             display: flex;
//             flex-direction: row;
//           }
//           .input-container {
//             &.review {
//               .disabled-input {
//                 float: left;
//                 width: 90%;
//               }
//               .svg-inline--fa {
//                 width: 10%;
//                 cursor: pointer;
//                 color: $blue23;
//               }
//             }
//             &.small-width {
//               width: 50px;
//             }
//             &.disabled {
//               input:disabled {
//                 border-bottom-color: $grey8;
//                 cursor: not-allowed;
//               }
//             }
//           }
//           .input-container,
//           .select-item {
//             width: 48%;
//             height: 30px;
//             margin-left: 0;
//             margin-right: 2%;
//           }
          
//           .section-cloud {
//             display: block;
//             padding-bottom: 20px;
//             width: 100%;
//             overflow: hidden;
//             p {
//               font-size: 13px;
//               line-height:  24px;
//               text-align: left;
//               color: $grey1;
//               font-weight: 500;
//             }
//             &.fade-in {
//               .image-radio-button-container .radio-buttons .radio-button label {
//                 opacity: .5;
//               }
//             }
//           }
//         }
//       }
//     }
//   }
// }