// @flow
import React, { PureComponent } from 'react';
import {
  change,
  reduxForm,
  autofill,
} from 'redux-form';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';
import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import { toUpper } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';
import { FormFieldLabel } from 'components/label';
import * as loginSelectors from 'ducks/login/selectors';

import {
  handleCloudChange,
  handlePremiseChange,
  setSelectedCloudValue,
} from 'ducks/provisioningTemplates';
import RadioImageGroup from './components/radioImageGroup';

class ConfigureEdgeconnectorForm extends PureComponent {
  componentDidMount() {
    const {
      actions,
      initialValues,
    } = this.props;

    actions.setSelectedCloudValue(initialValues);
  }

  handleCloudClick = (event) => {
    const { actions } = this.props;
    const appName = event.currentTarget.attributes.appname.value || '';
    actions.handleCloudChange(appName);
    actions.setSelectedCloudValue(appName);
  };

  render() {
    const {
      closeModal,
      handleSubmit,
      onPremise,
      t,
      selectedCloud,
      previousPage,
      initialValues,
      cloudName,
      configData,
    } = this.props;

    let { cloud } = this.props;
    let preSelectedCloud = '';

    if (selectedCloud === 'AWS' || selectedCloud === 'AZURE' || selectedCloud === 'GCP') {
      cloud = true;
    } else if (initialValues
        && initialValues.provUrlData
        && initialValues.provUrlData.cloudProviderType) {
      preSelectedCloud = initialValues.provUrlData.cloudProviderType || '';
      cloud = true;
    }

    const selectedCloudName = toUpper(cloudName) || selectedCloud || preSelectedCloud;

    const enableGcp = verifyConfigData({ configData, key: 'enableGcp' });

    return (
      <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
        <div className="form-sections-container">
          <div className="form-section">
            <div className="cc-provisioning-general-info-title">
              {t('CLOUD_PROVIDER')}
              <div className="cc-provisioning-general-info-description">
                {t('CC_CLOUD_PROVIDER_DESCRIPTION')}
              </div>
            </div>
            <div className="full-width">
              <div className={`${cloud ? '' : 'fade-in'} section-cloud`}>
                <FormFieldLabel
                  tooltip={t('TOOLTIP_CLOUD_PROVIDER')}
                  text={t('CLOUD_PROVIDER')} />
                {/* <p className="form-field-label">{t('CLOUD_PROVIDER')}</p> */}
                <RadioImageGroup
                  id="cloud"
                  name="cloud"
                  styleClass="full-width"
                  onChange={this.handleCloudClick}
                  options={[{
                    name: 'cloudProviderType', value: 'AWS', checked: selectedCloudName === 'AWS',
                  },
                  {
                    name: 'cloudProviderType', value: 'AZURE', checked: selectedCloudName === 'AZURE',
                  },
                  ...(enableGcp ? [{
                    name: 'cloudProviderType', value: 'GCP', checked: selectedCloudName === 'GCP',
                  }] : []),
                  ]} />
              </div>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <div>
            <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
            <button type="submit" className="next primary-button" disabled={cloud || onPremise ? '' : 'disabled'}>{t('NEXT')}</button>
          </div>
        </div>
      </form>
    );
  }
}

ConfigureEdgeconnectorForm.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  actions: PropTypes.shape(),
  cloud: PropTypes.bool,
  onPremise: PropTypes.bool,
  selectedCloud: PropTypes.string,
  cloudName: PropTypes.string,
  previousPage: PropTypes.func,
  initialValues: PropTypes.shape(),
  t: PropTypes.func,
  configData: PropTypes.shape(),
};

ConfigureEdgeconnectorForm.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  actions: {},
  cloud: false,
  onPremise: false,
  selectedCloud: '',
  previousPage: null,
  initialValues: {
    provUrlData: {},
  },
  t: (str) => str,
  configData: {},
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelector(state),
  cloud: provisioningTemplatesSelector.cloudSelector(state),
  selectedCloud: provisioningTemplatesSelector.selectedCloudSelector(state),
  wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateFormState: change,
    autoFillReduxForm: autofill,
    handleCloudChange,
    handlePremiseChange,
    setSelectedCloudValue,
  }, dispatch);

  return {
    actions,
  };
};

const Cloud = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default Cloud;
