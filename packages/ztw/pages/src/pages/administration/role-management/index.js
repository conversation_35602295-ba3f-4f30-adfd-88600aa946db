import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { noop } from 'utils/lodash';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import * as RoleManagementSelectors from 'ducks/roleManagement/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  loader,
  // getRoleManagementData,
  toggleEditForm,
  toggleViewForm,
  toggleDeleteForm,
  toggleClose,
  updateMenu,
  deleteForm,
} from 'ducks/roleManagement';
import {
  RoleManagementTable,
  RoleManagementSearch,
} from './components';
import RoleManagementForm from './components/RoleManagementForm';

import './index.scss';

export class RoleManagement extends Component {
  static propTypes = {
    t: PropTypes.func,
    rbactabledata: PropTypes.arrayOf(PropTypes.shape()),
    handleClose: PropTypes.func,
    handleEditAction: PropTypes.func,
    handleViewAction: PropTypes.func,
    showForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    toggleDeleteConfirmationForm: PropTypes.func,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    load: PropTypes.func,
    modalTitle: PropTypes.string,
    modalLoading: PropTypes.bool,
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    rbactabledata: null,
    handleClose: noop,
    handleEditAction: noop,
    handleViewAction: noop,
    showForm: false,
    showDeleteForm: false,
    toggleDeleteConfirmationForm: noop,
    selectedRowID: null,
    handleDelete: noop,
    load: noop,
    modalTitle: '',
    modalLoading: false,
    accessPermissions: {},
    authType: '',
    accessSubscriptions: [],
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  render() {
    const {
      t,
      rbactabledata,
      handleEditAction,
      handleViewAction,
      handleClose,
      showForm,
      showDeleteForm,
      toggleDeleteConfirmationForm,
      selectedRowID,
      handleDelete,
      modalTitle,
      modalLoading,
      accessPermissions,
      accessSubscriptions,
      authType,
    } = this.props;
    const { searchData } = this.props;
    const filteredData = rbactabledata
      .filter((x) => x.name.toUpperCase().includes(searchData.toUpperCase())
    || (t(x.roleType)).toUpperCase().includes(searchData.toUpperCase()));

    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT, authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }
    if (accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }
  
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ROLE_MANAGEMENT} />
        <div className="roleManagement-main-container">
          <div className="page-title ">
            <span className="source-ip-groups">
              {t('ROLE_MANAGEMENT')}
            </span>
          </div>
          <div className="controls-container">
            <RoleManagementSearch isReadOnly={isReadOnly} />
          </div>
          <div className="source-ip-wrapper">
            <Loading {...this.props}>
              <ServerError {...this.props}>
                <RoleManagementTable
                  tableData={filteredData}
                  handleEditAction={handleEditAction}
                  handleViewAction={handleViewAction}
                  isReadOnly={isReadOnly}
                  onMouseOverCb={() => this.handleOnMouseOverCb} />
                <Modal
                  title={t(modalTitle)}
                  isOpen={showForm}
                  closeModal={handleClose}>
                  <RoleManagementForm />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </div>
        </div>
      </>
    );
  }
}

RoleManagement.propTypes = {
  searchData: PropTypes.string,
};

RoleManagement.defaultProps = {
  searchData: '',
};

const mapStateToProps = (state) => ({
  ...RoleManagementSelectors.baseSelector(state),
  modalLoading: RoleManagementSelectors.modalLoadingSelector(state),
  selectedRowID: RoleManagementSelectors.selectedRowIDSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    // load: getRoleManagementData,
    load: loader,
    handleEditAction: toggleEditForm,
    handleViewAction: toggleViewForm,
    handleClose: toggleClose,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteForm,
    updateMenu,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(RoleManagement));
