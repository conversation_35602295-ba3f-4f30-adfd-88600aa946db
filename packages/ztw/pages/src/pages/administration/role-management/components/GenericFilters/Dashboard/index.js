import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';

function Dashboard({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('DASHBOARD')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_DASHBOARD')} />
      { viewOnly
          && <p className="disabled-input">{['READ_ONLY', 'READ_WRITE'].includes(value) ? t('VIEW_ONLY') : t('NONE')}</p>}
      { !viewOnly
            && (
              <div className="dropdown-container">
                <ECRadioGroup
                  id="EDGE_CONNECTOR_DASHBOARD"
                  name="ED<PERSON>_CONNECTOR_DASHBOARD"
                  styleClass="full-width"
                  onChange={onChange}
                  options={[{
                    name: 'EDGE_CONNECTOR_DASHBOARD', checked: ['READ_ONLY', 'READ_WRITE'].includes(value), value: 'READ_ONLY', label: t('VIEW_ONLY'),
                  },
                  {
                    name: 'EDGE_CONNECTOR_DASHBOARD', checked: value === 'NONE', value: 'NONE', label: t('NONE'),
                  }]} />
              
              </div>
            )}
    </div>
  );
}

Dashboard.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
Dashboard.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default Dashboard;
