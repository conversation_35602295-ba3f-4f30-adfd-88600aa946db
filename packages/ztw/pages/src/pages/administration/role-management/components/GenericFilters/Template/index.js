import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';
import { getPermissionText } from 'utils/helpers';

function Template({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('EDGE_CONNECTOR_TEMPLATE')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_TEMPLATE')} />
      { viewOnly
    && <p className="disabled-input">{ getPermissionText(value)}</p>}
      { !viewOnly
      && (
        <div className="dropdown-container">
          <ECRadioGroup
            id="EDGE_CONNECTOR_TEMPLATE"
            name="EDGE_CONNECTOR_TEMPLATE"
            styleClass="full-width"
            onChange={onChange}
            options={[{
              name: 'EDGE_CONNECTOR_TEMPLATE', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
            },
            {
              name: 'EDGE_CONNECTOR_TEMPLATE', checked: value === 'READ_ONLY', value: 'READ_ONLY', label: t('VIEW_ONLY'),
            },
            {
              name: 'EDGE_CONNECTOR_TEMPLATE', checked: value === 'NONE', value: 'NONE', label: t('NONE'),
            }]} />
        </div>
      )}
    </div>
  );
}

Template.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
Template.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default Template;
