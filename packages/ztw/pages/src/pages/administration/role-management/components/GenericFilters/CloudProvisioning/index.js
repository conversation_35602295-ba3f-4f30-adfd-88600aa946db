import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';
import { getPermissionText } from 'utils/helpers';

function CloudProvisioning({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('CLOUD_CONNECTOR_PROVISIONING')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_CLOUD_PROVISIONING')} />
      { viewOnly
          && <p className="disabled-input">{ getPermissionText(value)}</p>}
      { !viewOnly
            && (
              <div className="dropdown-container">
                <ECRadioGroup
                  id="EDGE_CONNECTOR_CLOUD_PROVISIONING"
                  name="EDGE_CONNECTOR_CLOUD_PROVISIONING"
                  styleClass="full-width"
                  onChange={onChange}
                  options={[{
                    name: 'EDGE_CONNECTOR_CLOUD_PROVISIONING', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
                  },
                  {
                    name: 'EDGE_CONNECTOR_CLOUD_PROVISIONING', checked: value === 'READ_ONLY', value: 'READ_ONLY', label: t('VIEW_ONLY'),
                  },
                  {
                    name: 'EDGE_CONNECTOR_CLOUD_PROVISIONING', checked: value === 'NONE', value: 'NONE', label: t('NONE'),
                  }]} />
              </div>
            )}
    </div>
  );
}

CloudProvisioning.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
CloudProvisioning.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default CloudProvisioning;
