import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';

function NSSConfiguration({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('NSS_LOGGING')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_NSS_CONFIGURATION')} />
      { viewOnly
 && <p className="disabled-input">{ ['READ_ONLY', 'NONE'].includes(value) ? t('NONE') : t('FULL')}</p>}
      { !viewOnly
   && (
     <div className="dropdown-container">
       <ECRadioGroup
         id="EDGE_CONNECTOR_NSS_CONFIGURATION"
         name="EDGE_CONNECTOR_NSS_CONFIGURATION"
         styleClass="full-width"
         onChange={onChange}
         options={[{
           name: 'EDGE_CONNECTOR_NSS_CONFIGURATION', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
         },
         {
           name: 'EDGE_CONNECTOR_NSS_CONFIGURATION', checked: ['READ_ONLY', 'NONE'].includes(value), value: 'NONE', label: t('NONE'),
         }]} />
     </div>
   )}
    </div>
  );
}

NSSConfiguration.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
NSSConfiguration.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default NSSConfiguration;
