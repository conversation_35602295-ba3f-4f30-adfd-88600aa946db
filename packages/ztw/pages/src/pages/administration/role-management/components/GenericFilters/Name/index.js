import React from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { FormFieldLabel } from 'components/label';
import { useTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import { setShowHelp, loadArticle } from 'ducks/help';
import { Field } from 'redux-form';
import { NavLink } from 'react-router-dom';
import Input from 'components/Input';
import {
  required,
  maxLength,
} from 'utils/validations';

const maxLength50 = maxLength(50);

function Name({
  value, addRole, meta, error,
}) {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const hasError = meta.touched && !!error;

  const handleClickRole = () => {
    dispatch(loadArticle(HELP_ARTICLES.ROLE_MANAGEMENT));
    dispatch(setShowHelp(true));
  };

  const toolTipText = t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_NAME').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <NavLink className="tooltip-navlink" to="?" onClick={handleClickRole}>
        <b>{toolTipText[1]}</b>
      </NavLink>
      {toolTipText[2]}
    </>
  );

  return (
    <div className="form-section">
      <div className="g-name">
        {addRole
          ? (
            <>
              <FormFieldLabel
                text={t('NAME')}
                tooltip={toolTipJSX}
                error={hasError ? t(error) : null} />
              <Field
                id="name"
                name="name"
                component={Input}
                placeholder={t('ENTER_TEXT')}
                validate={[
                  required,
                  maxLength50,
                ]}
                styleClass="max-width" />
            </>
          ) : (
            <>
              <FormFieldLabel text="Name" />
              <p className="disabled-input">{value}</p>
            </>
          )}
      </div>
    </div>
  );
}

Name.propTypes = {
  value: PropTypes.string,
  addRole: PropTypes.bool,
  meta: PropTypes.shape({}),
  error: PropTypes.string,
};
  
Name.defaultProps = {
  value: '',
  addRole: false,
  meta: {},
  error: null,
};

export default Name;
