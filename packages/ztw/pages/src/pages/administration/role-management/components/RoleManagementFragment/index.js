import React from 'react';
import { PropTypes } from 'prop-types';
import { connect, useSelector } from 'react-redux';

import { noop } from 'utils/lodash';
import * as roleManagementSelectors from 'ducks/roleManagement/selectors';
import {
  handleDashboardChange,
  handleCloudConnectorProvisioningChange,
  handlePolicyConfigurationChange,
  handleAdminManagementChange,
  handleLocationManagementChange,

  handleTrafficForwardingDNSChange,
  handleApiKeyManagementChange,
  handleRemoteAssistanceManagementChange,
  handleNssLoggingChange,
  handleTemplateChange,
  handlePublicCloudChange,
} from 'ducks/roleManagement';
import { withTranslation, useTranslation } from 'react-i18next';
import { FormSectionLabel } from 'components/label';
import { bindActionCreators } from 'redux';
import { getFormMeta, getFormSyncErrors } from 'redux-form';

import {
  Name,
  Dashboard,
  CloudProvisioning,
  AdminManagement,
  LocationManagement,
  TrafficFowardingDNS,
  APIKeyManagement,
  RemoteAssistanceManagement,
  NSSConfiguration,
  PublicCloud,
  Template,
} from '..';

export function RoleManagementFragment(props) {
  const { addEditRoleManagementForm } = useSelector((state) => state.form);
  const { roleManagement } = useSelector((state) => state);
  const { rbacFragmentSection: formMeta = {} } = useSelector((state) => getFormMeta('addEditRoleManagementForm')(state)) || {};
  const { rbacFragmentSection: formSyncErrors = {} } = useSelector((state) => getFormSyncErrors('addEditRoleManagementForm')(state)) || {};
  
  const { values } = addEditRoleManagementForm || {};
  const { actions } = props;
  const {
    EDGE_CONNECTOR_DASHBOARD,
    EDGE_CONNECTOR_CLOUD_PROVISIONING,
    EDGE_CONNECTOR_ADMIN_MANAGEMENT,
    EDGE_CONNECTOR_LOCATION_MANAGEMENT,
    EDGE_CONNECTOR_FORWARDING,
    APIKEY_MANAGEMENT,
    REMOTE_ASSISTANCE_MANAGEMENT,
    EDGE_CONNECTOR_NSS_CONFIGURATION,
    EDGE_CONNECTOR_TEMPLATE,
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
  } = values.rbacFragmentSection || {};
  const { addRole, appData, viewOnly } = roleManagement || {};
  const { name } = appData || {};
  const { t } = useTranslation();

  return (
    <div>
      <FormSectionLabel text={t('ADMIN_ROLE')} />
      <Name
        value={name}
        addRole={addRole}
        meta={formMeta.name}
        error={formSyncErrors.name}
        onChange={handleDashboardChange} />

      <FormSectionLabel text={t('PERMISSIONS')} />
      <div className="form-section">
        <Dashboard
          value={EDGE_CONNECTOR_DASHBOARD}
          viewOnly={viewOnly}
          onChange={handleDashboardChange} />

        <CloudProvisioning
          value={EDGE_CONNECTOR_CLOUD_PROVISIONING}
          viewOnly={viewOnly}
          onChange={actions.handleCloudConnectorProvisioningChange} />

        <Template
          value={EDGE_CONNECTOR_TEMPLATE}
          viewOnly={viewOnly}
          onChange={actions.handleTemplateChange} />

        <AdminManagement
          value={EDGE_CONNECTOR_ADMIN_MANAGEMENT}
          viewOnly={viewOnly}
          onChange={actions.handleAdminManagementChange} />
        
        <LocationManagement
          value={EDGE_CONNECTOR_LOCATION_MANAGEMENT}
          viewOnly={viewOnly}
          onChange={actions.handleLocationManagementChange} />

        <TrafficFowardingDNS
          value={EDGE_CONNECTOR_FORWARDING}
          viewOnly={viewOnly}
          onChange={actions.handleTrafficForwardingDNSChange} />

        <APIKeyManagement
          value={APIKEY_MANAGEMENT}
          viewOnly={viewOnly}
          onChange={actions.handleApiKeyManagementChange} />

        <RemoteAssistanceManagement
          value={REMOTE_ASSISTANCE_MANAGEMENT}
          viewOnly={viewOnly}
          onChange={actions.handleRemoteAssistanceManagementChange} />

        <NSSConfiguration
          value={EDGE_CONNECTOR_NSS_CONFIGURATION}
          viewOnly={viewOnly}
          onChange={handleNssLoggingChange} />

        <PublicCloud
          value={EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT}
          viewOnly={viewOnly}
          onChange={handlePublicCloudChange} />

      </div>
    </div>
  );
}

RoleManagementFragment.propTypes = {
  // t: PropTypes.func,
  actions: PropTypes.shape({
    handleDashboardChange: PropTypes.func,
    handleCloudConnectorProvisioningChange: PropTypes.func,
    handlePolicyConfigurationChange: PropTypes.func,
    handleAdminManagementChange: PropTypes.func,
    handleLocationManagementChange: PropTypes.func,
    handleTrafficForwardingDNSChange: PropTypes.func,
    handleApiKeyManagementChange: PropTypes.func,
    handleRemoteAssistanceManagementChange: PropTypes.func,
    handleNssLoggingChange: PropTypes.func,
  }),
};
    
RoleManagementFragment.defaultProps = {
  actions: ({
    handleDashboardChange: noop,
    handleCloudConnectorProvisioningChange: noop,
    handlePolicyConfigurationChange: noop,
    handleAdminManagementChange: noop,
    handleLocationManagementChange: noop,
    handleTrafficForwardingDNSChange: noop,
    handleApiKeyManagementChange: noop,
    handleRemoteAssistanceManagementChange: noop,
    handleNssLoggingChange: noop,
    handleTemplateChange: noop,
  }),
};

const mapStateToProps = (state) => ({
  addRole: roleManagementSelectors.addRoleSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleDashboardChange,
    handleCloudConnectorProvisioningChange,
    handlePolicyConfigurationChange,
    handleAdminManagementChange,
    handleLocationManagementChange,
    handleTrafficForwardingDNSChange,
    handleApiKeyManagementChange,
    handleRemoteAssistanceManagementChange,
    handleNssLoggingChange,
    handleTemplateChange,
  }, dispatch);

  return {
    actions,
  };
};

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(RoleManagementFragment));
