import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FormSection, reduxForm } from 'redux-form';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import * as RoleManagementSelectors from 'ducks/roleManagement/selectors';

import {
  saveForm,
  deleteForm,
  toggleClose,
  toggleDeleteForm,
} from 'ducks/roleManagement';
import RoleManagementFragment from '../RoleManagementFragment';
import './index.scss';

export function BasicCustomAppForm(props) {
  const {
    appData, actions, t, viewOnly, addRole,
  } = props;
  const { cancelHandle, saveFormData, handleDeleteAction } = actions;
  
  const handleSave = (event) => {
    event.preventDefault();
    saveFormData(addRole);
  };

  return (
    <form className="add-custom-app-form rbac-form">
      <div className="form-sections-container role-management-modal">
        <FormSection name="rbacFragmentSection">
          <RoleManagementFragment />
        </FormSection>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          {!viewOnly && <button type="submit" className="submit" onClick={handleSave}>{t('SAVE')}</button>}
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
        <div className="dialog-footer-right">
          {!viewOnly && !addRole && <button type="button" className="button big delete" onClick={() => handleDeleteAction(true, appData)}>{t('DELETE')}</button>}
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  addRole: PropTypes.bool,
  appData: PropTypes.shape({}),
  viewOnly: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  addRole: false,
  appData: {},
  viewOnly: true,
};

const BasicCustomForm = reduxForm({
  form: 'addEditRoleManagementForm',
})(BasicCustomAppForm);

const RoleManagementForm = connect(
  (state) => {
    const {
      EDGE_CONNECTOR_DASHBOARD,
      EDGE_CONNECTOR_CLOUD_PROVISIONING,
      EDGE_CONNECTOR_POLICY_CONFIGURATION,
      EDGE_CONNECTOR_ADMIN_MANAGEMENT,
      EDGE_CONNECTOR_LOCATION_MANAGEMENT,
      EDGE_CONNECTOR_FORWARDING,
      APIKEY_MANAGEMENT,
      REMOTE_ASSISTANCE_MANAGEMENT,
      EDGE_CONNECTOR_NSS_CONFIGURATION,
      EDGE_CONNECTOR_TEMPLATE,
      EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    } = RoleManagementSelectors.featurePermissionsSelector(state);

    return ({
      initialValues: {
        rbacFragmentSection: {
          EDGE_CONNECTOR_DASHBOARD: EDGE_CONNECTOR_DASHBOARD || 'READ_ONLY',
          EDGE_CONNECTOR_CLOUD_PROVISIONING: EDGE_CONNECTOR_CLOUD_PROVISIONING || 'READ_WRITE',
          EDGE_CONNECTOR_POLICY_CONFIGURATION: EDGE_CONNECTOR_POLICY_CONFIGURATION || 'READ_WRITE',
          EDGE_CONNECTOR_ADMIN_MANAGEMENT: EDGE_CONNECTOR_ADMIN_MANAGEMENT || 'READ_WRITE',
          EDGE_CONNECTOR_LOCATION_MANAGEMENT: EDGE_CONNECTOR_LOCATION_MANAGEMENT || 'READ_WRITE',
          EDGE_CONNECTOR_FORWARDING: EDGE_CONNECTOR_FORWARDING || 'READ_WRITE',
          APIKEY_MANAGEMENT: APIKEY_MANAGEMENT || 'READ_ONLY',
          REMOTE_ASSISTANCE_MANAGEMENT: REMOTE_ASSISTANCE_MANAGEMENT || 'READ_WRITE',
          EDGE_CONNECTOR_NSS_CONFIGURATION: EDGE_CONNECTOR_NSS_CONFIGURATION || 'READ_WRITE',
          EDGE_CONNECTOR_TEMPLATE: EDGE_CONNECTOR_TEMPLATE || 'NONE',
          EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT || 'NONE',
        },
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveFormData: saveForm,
    deleteData: deleteForm,
    handleDeleteAction: toggleDeleteForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({ ...RoleManagementSelectors.baseSelector(state) });

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(RoleManagementForm));
