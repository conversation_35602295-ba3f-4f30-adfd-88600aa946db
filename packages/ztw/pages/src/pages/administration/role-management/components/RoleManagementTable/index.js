import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { updateMenu, editRow } from 'ducks/roleManagement';
import * as RoleManagementSelectors from 'ducks/roleManagement/selectors';
import {
  ActionCell,
} from 'components/table';
import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';

class RoleManagementTable extends PureComponent {
  state = {
    showComponent: false,
  };

  getColumns = () => {
    const {
      columns,
      isReadOnly,
      handleEditAction,
      handleViewAction,
      t,
    } = this.props;
    // const { editRowHandler } = actions;

    columns.map((i) => {
      // eslint-disable-next-line no-param-reassign
      i.name = t(i.exportId);
      return i;
    });
    const lastColumn = columns[columns.length - 1];
    const { showComponent } = this.state;
    const lastColWithElipsis = {
      ...lastColumn,
      visible: true,
      frozen: false,
      width: 40,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          handleLeave={this.onLeave}
          showComponent={showComponent}
          onMouseOverCb={this.handleOnMouseOverCb} />
      ),
      formatter: ({ row }) => (
        <ActionCell
          isEdit={!row.isNonEditable && !isReadOnly}
          handleViewAction={() => handleViewAction(true, row, 'VIEW_CLOUD_CONNECTOR_ROLE')}
          handleEditAction={() => handleEditAction(true, row, 'EDIT_CLOUD_CONNECTOR_ROLE')}
          isPredefined />
      ),
    };
    columns[columns.length - 1] = lastColWithElipsis;
    return [...columns];
  };

  handleOnMouseOverCb = () => this.setState(() => ({ showComponent: true }));

  onLeave = () => {
    this.setState({ showComponent: false });
  };

  render() {
    const {
      tableData,
      actions,
    } = this.props;
    const { showComponent } = this.state;

    const columns = this.getColumns();

    return (
      <div className="insights-layout-header">
        <TableWrapper
          initialRows={tableData}
          initialColumns={columns}
          updateMenu={actions.updateMenu} />
        <CustomizeColumns
          initialItems={columns}
          handleLeave={this.onLeave}
          showComponent={showComponent}
          dragnDrop={actions.updateMenu} />
      </div>
    );
  }
}

RoleManagementTable.propTypes = {
  actions: PropTypes.shape(),
  columns: PropTypes.arrayOf(PropTypes.shape()),
  tableData: PropTypes.arrayOf(PropTypes.shape()),
  handleEditAction: PropTypes.func,
  handleViewAction: PropTypes.func,
  t: PropTypes.func,
  isReadOnly: PropTypes.bool,
};

RoleManagementTable.defaultProps = {
  actions: {},
  columns: [],
  tableData: [],
  handleEditAction: noop,
  handleViewAction: noop,
  t: (str) => str,
  isReadOnly: true,
};

const mapStateToProps = (state) => ({
  ...state.showComponent,
  ...RoleManagementSelectors.baseSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    editRowHandler: editRow,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(RoleManagementTable));
