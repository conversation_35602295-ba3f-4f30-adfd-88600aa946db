import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';
import { getPermissionText } from 'utils/helpers';

function APIKeyManagement({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('API_KEY_MANAGEMENT')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_APIKEY_MANAGEMENT')} />
      { viewOnly
        && <p className="disabled-input">{ getPermissionText(value)}</p>}
      { !viewOnly
      && (
        <div className="dropdown-container">
          <ECRadioGroup
            id="APIKEY_MANAGEMENT"
            name="APIKEY_MANAGEMENT"
            styleClass="full-width"
            onChange={onChange}
            options={[{
              name: 'APIKEY_MANAGEMENT', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
            },
            {
              name: 'APIKEY_MANAGEMENT', checked: value === 'READ_ONLY', value: 'READ_ONLY', label: t('VIEW_ONLY'),
            },
            {
              name: 'APIKEY_MANAGEMENT', checked: value === 'NONE', value: 'NONE', label: t('NONE'),
            }]} />
        </div>
      )}
    </div>
  );
}

APIKeyManagement.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
APIKeyManagement.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default APIKeyManagement;
