@import 'scss/widgets.scss';
@import 'scss/pages.scss';

.ec-root-page {
.main-container {
  padding: 19px 25px;
}

.source-ip-groups {	
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.source-ip-wrapper {
  margin-bottom: 100px;
  .app-table-container {
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }
    }
  }   
}
}