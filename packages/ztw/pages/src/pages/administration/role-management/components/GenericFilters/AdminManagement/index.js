import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';
import { getPermissionText } from 'utils/helpers';

function AdminManagement({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('ADMIN_MANAGEMENT')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EDGE_CONNECTOR_ADMIN_MANAGEMENT')} />
      { viewOnly
    && <p className="disabled-input">{ getPermissionText(value)}</p>}
      { !viewOnly
      && (
        <div className="dropdown-container">
          <ECRadioGroup
            id="EDGE_CONNECTOR_ADMIN_MANAGEMENT"
            name="EDGE_CONNECTOR_ADMIN_MANAGEMENT"
            styleClass="full-width"
            onChange={onChange}
            options={[{
              name: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
            },
            {
              name: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT', checked: value === 'READ_ONLY', value: 'READ_ONLY', label: t('VIEW_ONLY'),
            },
            {
              name: 'EDGE_CONNECTOR_ADMIN_MANAGEMENT', checked: value === 'NONE', value: 'NONE', label: t('NONE'),
            }]} />
        </div>
      )}
    </div>
  );
}

AdminManagement.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
AdminManagement.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default AdminManagement;
