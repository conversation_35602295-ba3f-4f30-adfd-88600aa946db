import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import ECRadioGroup from 'components/ecRadioGroup';
import { useTranslation } from 'react-i18next';

function RemoteAssistanceManagement({ value, viewOnly, onChange }) {
  const { t } = useTranslation();

  return (
    <div className="g-row">
      <FormFieldLabel
        text={t('REMOTE_ASSISTANCE_MANAGEMENT')}
        tooltip={t('TOOLTIP_ADMINISTRATION_ROLE_MANAGEMENT_EC_REMOTE_ASSISTANCE_MANAGEMENT')} />
      { viewOnly
    && <p className="disabled-input">{ ['READ_ONLY', 'NONE'].includes(value) ? t('VIEW_ONLY') : t('FULL')}</p>}
      { !viewOnly
      && (
        <div className="dropdown-container">
          <ECRadioGroup
            id="REMOTE_ASSISTANCE_MANAGEMENT"
            name="REMOTE_ASSISTANCE_MANAGEMENT"
            styleClass="full-width"
            onChange={onChange}
            options={[{
              name: 'REMOTE_ASSISTANCE_MANAGEMENT', checked: value === 'READ_WRITE', value: 'READ_WRITE', label: t('FULL'),
            },
            {
              name: 'REMOTE_ASSISTANCE_MANAGEMENT', checked: ['READ_ONLY', 'NONE'].includes(value), value: 'READ_ONLY', label: t('VIEW_ONLY'),
            }]} />
        </div>
      )}
    </div>
  );
}

RemoteAssistanceManagement.propTypes = {
  value: PropTypes.string,
  viewOnly: PropTypes.bool,
  onChange: PropTypes.func,
};
  
RemoteAssistanceManagement.defaultProps = {
  value: '',
  viewOnly: true,
  onChange: (str) => str,
};

export default RemoteAssistanceManagement;
