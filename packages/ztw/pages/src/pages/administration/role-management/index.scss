// @import 'scss/widgets.scss';
@import 'scss/pages.scss';

.ec-root-page {
.roleManagement-main-container {
  padding: 19px 25px;
  background: var(--semantic-color-background-primary);
}

.roleManagement-main-container,.roleManagement-form {
  .page-title {
    padding-left: 0px;
  }
  .source-ip-groups {	
    height: 24px;
    width: 147px;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
  }
  .sipg-fragment {
    padding-top: 1.5em;
    padding-bottom: 10px;
  }
  .source-ip-wrapper {
    position: relative;
    margin-bottom: 100px;
    .app-table-container {    
      .ReactTable.app-table.has-nested-true {
        .rt-thead {
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left: -35px;
              position: relative;
              margin-right: -35px;
            }
          }
        }      
      }
      .table-column-selector{
        width: 70px;
        height: 42px;
      }
    }   
  }
  
  .rdg-menu-editor .ui-sortable {
    padding-top: 0; 
  }

    .search-container .search-input, .search-container .autocomplete-input-container {
      box-shadow: none; }
    .dropdown + .search-container, .reports-favorites-list + .search-container {
      margin-left: 5px; }

  .search-input-text-container {
    height: 100%;
    display: inline-block;
    position: relative; }

  .search-input, .autocomplete-input-container {
    border: none;
    padding: 0px 12px 0px 8px;
    border-radius: 8px;
    vertical-align: middle; }

  .search-input-text {
    display: inline-block;
    font-size: 13px;
    height: 32px;
    padding: 6px 26px 6px 0;
    vertical-align: middle;
    width: 280px; }

  .search-icon, .autocomplete-icon {
    color: var(--semantic-color-content-status-info-primary);
    cursor: pointer;
    display: inline-block;
    font-size: 16px !important;
    font-weight: bold;
    height: 16px;
    margin-left: 8px;
    width: 16px;
    vertical-align: middle; }
    .search-icon:hover, .autocomplete-icon:hover {
      color: var(--semantic-color-content-interactive-primary-default); }

  .search-clear-icon {
    color: var(--semantic-color-content-status-info-primary);
    display: none;
    cursor: pointer;
    font-size: 14px !important;
    height: 14px;
    margin-left: 5px;
    width: 12px;
    vertical-align: middle;
    position: absolute;
    top: 9px;
    right: 8px; }
    .search-clear-icon.fas {
      display: none; }
    .search-clear-icon:hover {
      color: var(--semantic-color-content-interactive-primary-default); }
    .search-clear-icon.visible {
      display: inline-block; }


  .radio-button-container p {
  margin-top: 0;
  }

  .rbac-form {
    .g-row {
      margin: 12px 0px;
    }
  }

  .radio-button-container p {
    margin: 4px;
  }
}
}