/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { PageFiltersHOC } from 'components/hoc';
import { BASE_API_PATH } from 'config';
import {
  hasBsku, hasCsku, hasBCsku,
} from 'utils/helpers';
import AWS from 'images/aws.png';
import SubscriptionRequired from 'components/subscriptionRequired';
// Keeping as a comment because they are not sure what use All Regions or only Supported Regions
// eslint-disable-next-line max-len
// import AwsSupportedRegionsDropdown from 'commonConnectedComponents/dropdown/AwsSupportedRegionsDropdown';
import AwsRegionsDropdown from 'commonConnectedComponents/dropdown/AwsRegionsDropdown';

function PageFilters(props) {
  const {
    t, awsRegionsDropdown, accessSubscriptions,
  } = props;
  const hasBCSubscription = hasBCsku(accessSubscriptions);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);
  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired />;
  }

  return (
    <>
      { (hasBCSubscription || hasCSubscription) && (
        <>
          <div className="entity-dropdown-container">
            <label>
              <img src={AWS} className="filter-icon" alt="AWS" />
              {t('AWS_REGIONS')}
            </label>
            <Field
              id="awsRegionsDropdown"
              searchParamName="name"
              name="awsRegionsDropdown"
              label="All Regions"
              defaultValue={awsRegionsDropdown}
              dataSrc={`${BASE_API_PATH}/api/v1/applications`}
              component={AwsRegionsDropdown} />
          </div>
        </>
      )}
    </>
  );
}

PageFilters.propTypes = {
  t: PropTypes.func,
  awsRegionsDropdown: PropTypes.shape({}),
  azureRegionsDropdown: PropTypes.shape({}),
  gcpRegionsDropdown: PropTypes.shape({}),
  regionsDropdown: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
};

PageFilters.defaultProps = {
  t: (str) => str,
  awsRegionsDropdown: {},
  azureRegionsDropdown: {},
  regionsDropdown: {},
  accessSubscriptions: [],
};

export default PageFiltersHOC(withTranslation()(PageFilters));
