import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import {
  Table,
  // TableHeader,
  SimpleHeader,
  ActionCell,
} from 'components/table';

const getLoc = (row) => {
  let city = '-';
  let country = '-';
  if (row) {
    const { cityName, countryName } = row;
    city = cityName;
    country = countryName;
  }
  
  return city + ' , ' + country;
};

class ConnectorTable extends PureComponent {
  static propTypes = {
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    handleViewAction: PropTypes.func,
    isPredefined: PropTypes.bool,
    t: PropTypes.func,
  };

  static defaultProps = {
    tableData: [],
    handleViewAction: (str) => str,
    isPredefined: true,
    t: (str) => str,
  };

  render() {
    const {
      tableData,
      handleViewAction,
      isPredefined,
      t,
    } = this.props;

    const columns = [
      {
        Header: t('NO'),
        id: 'row',
        maxWidth: 50,
        filterable: false,
        Cell: (row) => {
          return <div>{row?.index + 1}</div>;
        },
      },
      {
        Header: <SimpleHeader title={t('NAME')} />,
        accessor: 'ecName',
        sortable: true,
        Aggregated: (row) => row.value,
      },
      {
        Header: <SimpleHeader title={t('GROUP')} />,
        accessor: 'group',
        sortable: true,
        Aggregated: (row) => row.value,
      },
      {
        Header: <SimpleHeader title={t('LOCATION')} />,
        accessor: 'location',
        sortable: true,
        Aggregated: (row) => row.value,
      },
      // {
      //   Header: <TableHeader title={t('DEPLOYMENT_TYPE')} />,
      //   accessor: 'deploymentType',
      //   sortable: true,
      //   Aggregated: row => row.value,
      // },
      {
        Header: <SimpleHeader title={t('GEO_LOCATION')} />,
        accessor: 'geoLocation',
        sortable: true,
        Cell: (row) => getLoc(row.value),
      },
      {
        Header: <SimpleHeader title={t('STATUS')} />,
        accessor: 'status',
        sortable: true,
        Aggregated: (row) => row.value,
      },
      {
        Header: <SimpleHeader title={t('VIEW')} />,
        accessor: 'isEditable',
        width: 70,
        Cell: (row) => (
          <ActionCell
            isEdit={false}
            handleViewAction={() => handleViewAction(row?.original)}
            isPredefined={isPredefined} />
        ),
      },
    ];

    return (
      <Table
        data={tableData}
        columns={columns} />
    );
  }
}

export default withTranslation()(ConnectorTable);
