@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
.dashboard {
  // padding: 19px 25px;

  .appliance-notification-box {
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    padding: 8px;
    background: var(--semantic-color-surface-base-primary);
    color:  var(--semantic-color-content-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    
    border-radius: 4px;
    flex-direction: column;
    // flex: none;
    // order: 1;
    // flex-grow: 0;
    margin-bottom: 16px;
    .appliance-notification-title{
      display: flex;
      justify-content: space-between;
      line-height: 20px;
      width: 100%;
      .left {
        width: fit-content;
        .svg-inline--fa {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          margin-right: 8px;
          // display: flex;
          // align-items: center;
          // text-align: center;
          color: var(--semantic-color-content-interactive-primary-default);
          &.fa-circle-info {
            font-style: normal;
            font-weight: 900;
            font-size: 13px;
            line-height: 20px;
            color: var(--semantic-color-content-interactive-primary-default);
          }
        }
      }
      .right {
        width: fit-content;
        cursor: pointer;
        padding: 0;
        .svg-inline--fa {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          margin-right: 8px;
          color: var(--semantic-color-content-interactive-primary-default);
        }
      }
    }
    .appliance-notification-message {
      padding-left:  36px;
      line-height: 20px;
    }
  }
  .dashboard-row{
    margin: 30px 0;
  }

  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }

  .table-content {
    padding-bottom: 90px;
    border-radius: 5px;
    // background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
  }
}
.entity-dropdown-container {
  .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .filter-icon-disabled {
    height: 15px;
    padding-right: 5px;
    color: $grey21;
  }
  .filter-icon-branch {
    color: #A940FF;
    height: 15px;
    padding-right: 5px;
  }
  label {
    color:  var(--semantic-color-content-base-primary);
    span {
      margin-left: 0;
    }
  }
}
.trafficFlow {
  display: none;
}
.hideVisibility {  visibility: hidden; }

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

.cloud-monitoring-table {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: 300px;
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.operational-staus-icon {
  background-color: transparent;    
  .active{
    color: var(--semantic-color-border-severity-lowest-active);
    margin: 0 4px 0 7px;
  }
  .inactive{
    color: var(--semantic-color-border-severity-high-active);
    margin: 0 4px 0 7px;
  }
  .disabled{
    color: $button-disabled-text-color;
    margin: 0 4px 0 7px;
  }
}
.value.margin-left-0px {
  .operational-staus-icon {
    .active{
      margin-left: 0;
    }
    .inactive{
      margin-left: 0;
    }
    .disabled{
      margin-left: 0;
    }
  }
}
@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}


.container-row-ha-status {
  .os-green-circle,
  .os-yellow-circle,
  .os-gray-circle
   {
    width: 0;
    padding-left: 10px;
    position: relative;
    text-align: left;
    display: inline-block;
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      top: -8px;
      left: -1px;
      border-radius: 4px;
    }
  }
  .os-green-circle {
    &::before {
      background: var(--semantic-color-border-severity-lowest-active);
    }
  }
  .os-yellow-circle {
    &::before {
      background: #F4C351;
    }
  }
  .os-gray-circle {
    &::before {
      background: $button-disabled-text-color;
    }
  }
}
}