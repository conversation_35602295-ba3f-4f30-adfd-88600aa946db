/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { PageFiltersHOC } from 'components/hoc';
import { BASE_ROUTE_PATH } from 'config';
import {
  hasBsku, hasCsku, hasBCsku, verifyConfigData,
} from 'utils/helpers';
import {
  faBuilding,
} from '@fortawesome/pro-solid-svg-icons';
import AWS from 'images/aws.png';
import Azure from 'images/azure.png';
import GCP from 'images/gcp.svg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import SubscriptionRequired from 'components/subscriptionRequired';
import AwsRegionsDropdown from 'commonConnectedComponents/dropdown/AwsRegionsDropdown';
import AzureRegionsDropdown from 'commonConnectedComponents/dropdown/AzureRegionsDropdown';
import GcpRegionsDropdown from 'commonConnectedComponents/dropdown/GcpRegionsDropdown';
import BranchLocationsDropdown from 'commonConnectedComponents/dropdown/BranchLocationsDropdown';

function PageFilters(props) {
  const {
    t, awsRegionsDropdown, azureRegionsDropdown, gcpRegionsDropdown,
    regionsDropdown, accessSubscriptions, configData,
  } = props;
  const hasBCSubscription = hasBCsku(accessSubscriptions);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);
  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  const enableGcp = verifyConfigData({ configData, key: 'enableGcp' });
  
  return (
    <>
      { (hasBCSubscription || hasCSubscription) && (
        <>
          <div className="entity-dropdown-container">
            <label>
              <img src={AWS} className="filter-icon" alt="AWS" />
              {t('AWS_REGIONS')}
            </label>
            <Field
              id="awsRegionsDropdown"
              searchParamName="name"
              name="awsRegionsDropdown"
              label="All Regions"
              defaultValue={awsRegionsDropdown}
              dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
              component={AwsRegionsDropdown} />
          </div>
          <div className="entity-dropdown-container">
            <label>
              <img src={Azure} className="filter-icon-disabled" alt="Azure" />
              {t('AZURE')}
            </label>
            <Field
              id="azureRegionsDropdown"
              searchParamName="name"
              name="azureRegionsDropdown"
              label="None"
              defaultValue={azureRegionsDropdown}
              dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
              component={AzureRegionsDropdown} />
          </div>
          { enableGcp && (
            <div className="entity-dropdown-container">
              <label>
                <img src={GCP} className="filter-icon-gcp" alt="GCP" />
                {t('GCP')}
              </label>
              <Field
                id="gcpRegionsDropdown"
                searchParamName="name"
                name="gcpRegionsDropdown"
                label="None"
                defaultValue={gcpRegionsDropdown}
                dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
                component={GcpRegionsDropdown} />
            </div>
          )}
        </>
      )}
      { (hasBCSubscription || hasBSubscription) && (
        <div className="entity-dropdown-container">
          <label>
            <FontAwesomeIcon className="fas filter-icon-branch filter-icon-disabled" icon={faBuilding} size="lg" alt="Branch" />
            {t('BRANCH_CONNECTOR_LOCS')}
          </label>
          <Field
            id="regionsDropdown"
            searchParamName="name"
            name="regionsDropdown"
            label="None"
            defaultValue={regionsDropdown}
            dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
            component={BranchLocationsDropdown} />
        </div>
      )}
    </>
  );
}

PageFilters.propTypes = {
  t: PropTypes.func,
  awsRegionsDropdown: PropTypes.shape({}),
  azureRegionsDropdown: PropTypes.shape({}),
  gcpRegionsDropdown: PropTypes.shape({}),
  regionsDropdown: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  configData: PropTypes.shape(),
};

PageFilters.defaultProps = {
  t: (str) => str,
  awsRegionsDropdown: {},
  azureRegionsDropdown: {},
  regionsDropdown: {},
  accessSubscriptions: [],
  configData: {},
};

export default PageFiltersHOC(withTranslation()(PageFilters));
