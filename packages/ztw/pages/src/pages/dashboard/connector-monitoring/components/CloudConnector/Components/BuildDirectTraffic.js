import React from 'react';
import PropTypes from 'prop-types';

function BuildDirectTraffic({ data, t }) {
  return data.map((item, idx, self) => {
    const {
      txBytes, rxBytes, interfaceName,
      txPkts, rxPkts,
    } = item;
  
    return (
    // eslint-disable-next-line react/no-array-index-key
      <div key={idx}>
        { (idx === 0 || interfaceName !== self[idx - 1]?.interfaceName) && (
          <div className="first-row-interface full-width">
            <span className="row-item-1">
              {`${t('INTERFACE')} ${interfaceName?.toUpperCase()}`}
            </span>
          </div>
        )}
        <div className="interface-details-window background-white full-width">
          <div className="interface-details-window-row  full-width">
            <span className="row-item-1">
              {t('TX_RX_BYTES')}
            </span>
            <span className="row-item-2">
              {`${txBytes} | ${rxBytes}`}
            </span>
          </div>

          <div className="interface-details-window-row background-white full-width">
            <span className="row-item-1">
              {t('TX_RX_PACKETS')}
            </span>
            <span className="row-item-2">
              {`${txPkts} | ${rxPkts}`}
            </span>
          </div>
        </div>
        
      </div>
    );
  });
}

BuildDirectTraffic.propTypes = {
  network: PropTypes.arrayOf({}),
  t: PropTypes.func,
};
    
BuildDirectTraffic.defaultProps = {
  network: [],
  t: (str) => str,
};

export default BuildDirectTraffic;
