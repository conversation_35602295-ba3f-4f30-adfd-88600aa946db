import React from 'react';
import PropTypes from 'prop-types';
import { getStatusIcon, fromMaskToCIDR } from 'utils/helpers';
import ReviewGroup from 'components/reviewGroup';
import SingleItemView from './SingleItemView';

function BuildWan(networks, cloudConnectorData, t) {
  return networks.map((intf, idx) => {
    const { trafficDistribution } = cloudConnectorData;
    const isPort = !intf?.vlanId || intf?.vlanId === 0 || intf?.vlanId === '0';
  
    return (
      <div key={intf?.portId}>
        {(idx === 0 || (idx > 0 && intf?.portId !== networks[idx - 1]?.portId)) && (
          <div className="first-row-interface full-width">
            <span className="row-item-1">
              {`${t('INTERFACE')} ${intf?.portId}`}
            </span>
            <span className="row-item-2">
              {`${t('STATUS')}:`}
              {getStatusIcon(intf?.portStatusUp ? 'Up' : 'Down')}
            </span>

            <span className="row-item-3" />
            <span className="row-item-4">
              {`${t('MTU')}: ${intf?.mtu}`}
            </span>
          </div>
        )}
        <ReviewGroup
          title={(
            <div className="interface-details full-width">
              <span className="row-item-1">
                <span className="light-color">
                  {isPort ? t('UNTAGGED') : `${t('SUB_INTERFACE_VLAN')} ${intf?.vlanId || ''}`}
                </span>
              </span>
              <span className="row-item-2">
                <SingleItemView value={intf?.description} generalValue={t('NO_DESCRIPTION')} />
              </span>
              <span className="row-item-3">
                <span className="light-color">
                  {`${t('IP_ADDRESS')} `}
                </span>
                {`${intf?.ipInfo?.ipStart}/${fromMaskToCIDR(intf?.ipInfo?.netmask)}`}
              </span>
              <span className="row-item-4">
                <span className="light-color">
                  {isPort ? '' : `${t('MTU')} `}
                </span>
                {isPort ? '' : `${intf?.mtu}`}
              </span>
            </div>
          )}
          styleClass="margin-bottom-0px inline-block full-width"
          styleTitleContainerClass="margin-bottom-0px margin-left-26px position-relative interface-details-review-group">

          <div className="interface-details-window">
            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{`${t('UPLINK_MODE')}`}</span>
                {t(intf?.uplinkActive)}
              </span>
              <span className="row-item-2">
                <span className="light-color">{`${t('CONFIGURED_MODE')} `}</span>
                {t(intf?.configuredUplinkState)}
              </span>
              <span className="row-item-3">
                <span className="light-color">{`${t('CURRENT_MODE')} `}</span>
                {t(intf?.currentUplinkState)}
              </span>
            </div>

            <div className="separator-line-dashboard" />
          
            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{t('DHCP')}</span>
              </span>
              <span className="row-item-2">
                {intf?.dhcpEnabled ? t('ENABLED') : t('DISABLED')}
              </span>
            </div>
          
            <div className="interface-details-window-row line-value-title">
              <span className="row-item-1">
                {' '}
              </span>
              <span className="row-item-2">
                <span className="light-color">
                  {t('IP_ADDRESS')}
                </span>
              </span>
              <span className="row-item-3">
                <span className="light-color">
                  {t('DEFAULT_GW')}
                </span>
              </span>
              <span className="row-item-4">
                <span className="light-color">
                  {t('PRIMARY_DNS_SERVER')}
                </span>
              </span>
              <span className="row-item-5">
                <span className="light-color">
                  {t('SECONDARY_DNS_SERVER')}
                </span>
              </span>
              <span className="row-item-6">
                <span className="light-color">
                  {t('NAT_IP_ADDRESS')}
                </span>
              </span>
            </div>
            <div className="interface-details-window-row line-value">
              <span className="row-item-1">
                {' '}
              </span>
              <span className="row-item-2">
                {`${intf?.ipInfo?.ipStart}/${fromMaskToCIDR(intf?.ipInfo?.netmask)}`}
              </span>
              <span className="row-item-3">
                {intf?.ipInfo?.defaultGateway}
              </span>
              <span className="row-item-4">
                {intf?.ipInfo?.dns?.ips[0]}
              </span>
              <span className="row-item-5">
                {intf?.ipInfo?.dns?.ips[1]}
              </span>
              <span className="row-item-6">
                {intf?.ipInfo?.natIp || 'N/A'}
              </span>
            </div>
            <div className="separator-line-dashboard" />
            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{`${t('LINK_SCORE')}`}</span>
              </span>
              <span className="row-item-2">
                {intf?.linkScore || 'N/A'}
              </span>
            </div>
          </div>
        </ReviewGroup>
        {idx === networks?.length - 1 && (
          <div className="last-row-interface no-backgrond-color full-width">
            <span className="row-item-1">
              {`${t('TRAFFIC_DISTRIBUTION')}`}
            </span>
            <span className="row-item-2">
              {`${t(trafficDistribution || 'N/A')}`}
            </span>
          </div>
        )}
      </div>
    );
  });
}

BuildWan.propTypes = {
  network: PropTypes.arrayOf({}),
  t: PropTypes.func,
};
    
BuildWan.defaultProps = {
  network: [],
  t: (str) => str,
};

export default BuildWan;
