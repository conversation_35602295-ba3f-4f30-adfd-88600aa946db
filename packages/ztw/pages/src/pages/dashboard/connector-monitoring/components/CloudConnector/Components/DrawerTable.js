/* eslint-disable react/jsx-handler-names */
import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import ConfigTableWithPaginationAndSort from 'components/configTable';
import { DRAWER_TABLE_CONFIGS } from 'ducks/connectorMonitoring/constants';

import { isEmpty } from 'utils/lodash';

function DrawerTable(props) {
  const {
    drawerData,
  } = props;
  const { t } = useTranslation();

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          // name: row.networkId.ip,
        };
      });
    return tableData;
  };
  
  const data = getTableData(drawerData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(DRAWER_TABLE_CONFIGS(t))}
      tableHeight={9999999999}
      maxTableHeight="9999999999px"
      showColumnLayoutConfigurer={false}
      data={data} />
  );
}

DrawerTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  partnerIntegrationsAwsData: PropTypes.arrayOf(PropTypes.shape({})),
  workloadsData: PropTypes.arrayOf(PropTypes.shape({})),
  drawerData: PropTypes.arrayOf(PropTypes.shape({})),
  pathname: PropTypes.string,
};

DrawerTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  partnerIntegrationsAwsData: [],
  workloadsData: [],
  drawerData: [],
  pathname: '',
};

export default DrawerTable;
