import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-solid-svg-icons';
import { faTimes, faChevronRight, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { isEmpty, getSubscriptionLicenses } from 'utils/helpers';

export function ApplianceNotification(props) {
  const [isOpen, setIsOpen] = useState(true);
  const handleOpenClose = () => {
    setIsOpen(() => !isOpen);
  };
  const {
    actions,
    hardwareNotifications,
    accessDetailSubscriptions,
  } = props;
  const { t } = useTranslation();
  if (!hardwareNotifications || isEmpty(hardwareNotifications)) return null;

  const infoText = (hardwareNotifications === 1 ? t('SINGLE_APPLIANCE_ADDED_INFO') : t('MULTIPLE_APPLIANCES_ADDED_INFO')).split(/{[0-9]}/g);
  const infoTextJSX = (
    <>
      {infoText[0]}
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://help.zscaler.com/cloud-branch-connector/about-branch-connector-groups"
        className="tooltip-navlink">
        {infoText[1]}
      </a>
    </>
  );

  const bcDevice400Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_400');
  const bcDevice600Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_600');
  const bcDevice800Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_800');
  const hasAtLeastOneSubscrption = bcDevice400Licenses > 0
                              || bcDevice600Licenses > 0
                              || bcDevice800Licenses > 0;

  if (!hasAtLeastOneSubscrption) return <></>;

  return (
    <div className="appliance-notification-box">
      <div className="appliance-notification-title">
        <div className="left">
          <FontAwesomeIcon
            icon={isOpen ? faChevronDown : faChevronRight}
            className="review-title-icon"
            onClick={handleOpenClose} />
          <FontAwesomeIcon icon={faInfoCircle} className="info-circle" />
          {hardwareNotifications === 1 ? t('SINGLE_APPLIANCE_ADDED') : `${hardwareNotifications} ${t('MULTIPLE_APPLIANCES_ADDED')}`}
        </div>
        <div
          className="right"
          role="button"
          aria-label="Close"
          tabIndex="0"
          onKeyPress={() => actions.handleCloseApplianceNotification('PutTheNotificationNumber')}
          onClick={() => actions.handleCloseApplianceNotification('PutTheNotificationNumber')}>
          <FontAwesomeIcon icon={faTimes} className="info-circle" />
        </div>
      </div>
  
      { isOpen && (
        <div className="appliance-notification-message">
          {infoTextJSX}
        </div>
      )}
    </div>
  );
}

ApplianceNotification.propTypes = {
  actions: PropTypes.shape({
    handleCloseApplianceNotification: PropTypes.func,
  }),
  accessDetailSubscriptions: PropTypes.arrayOf(PropTypes.shape()),
  hardwareNotifications: PropTypes.arrayOf(PropTypes.shape()),
};

ApplianceNotification.defaultProps = {
  actions: {},
  accessDetailSubscriptions: [],
  hardwareNotifications: [],
};

export default ApplianceNotification;
