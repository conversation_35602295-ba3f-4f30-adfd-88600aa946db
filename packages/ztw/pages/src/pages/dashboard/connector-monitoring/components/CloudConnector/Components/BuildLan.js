import React from 'react';
import { NavLink } from 'react-router-dom';
import Drawer from 'components/Drawer';
import PropTypes from 'prop-types';
import { getStatusIcon, fromMaskToCIDR, trinary } from 'utils/helpers';
import ReviewGroup from 'components/reviewGroup';
import { useDispatch, useSelector } from 'react-redux';
import * as connectorMonitoringSelector from 'ducks/connectorMonitoring/selectors';
import { handleDrawer } from 'ducks/connectorMonitoring';
import SingleItemView from './SingleItemView';
import DrawerTable from './DrawerTable';

const header = (t) => (
  <div className="header-details flex-box">
    <span className="drawer-header-title">
      {t('STATIC_LEASE')}
    </span>
  </div>
);

function BuildLan(networks, cloudConnectorData, t) {
  return networks.map((intf, idx) => {
    const dispatch = useDispatch();
    const openDrawer = useSelector((state) => connectorMonitoringSelector.openDrawerSelector(state));
    const drawerData = useSelector((state) => connectorMonitoringSelector.drawerDataSelector(state));
  
    const dhcpOptionsLength = Object.keys(intf?.dhcpServer?.options || {}).length;
    const dhcpOptionsDNS = trinary(
      intf?.dhcpServer?.options?.domainNameServersList,
      intf?.dhcpServer?.options?.domainNameServersList?.join(','),
      intf?.dhcpServer?.options?.domainNameServers,
    );
    const dhcpOptionsDomain = trinary(
      intf?.dhcpServer?.options?.domainSearch,
      intf?.dhcpServer?.options?.domainSearch?.join(','),
      intf?.dhcpServer?.options?.domainName,
    );
    const { useWanDns, lanDns } = cloudConnectorData;
    const isPort = !intf?.vlanId || intf?.vlanId === 0 || intf?.vlanId === '0';

    return (
      <div key={intf?.portId}>
        {(idx === 0 || (idx > 0 && intf?.portId !== networks[idx - 1]?.portId)) && (
          <div className="first-row-interface full-width">
            <span className="row-item-1">
              {`${t('INTERFACE')} ${intf?.portId}`}
            </span>
            <span className="row-item-2">
              {`${t('STATUS')}:`}
              {getStatusIcon(trinary(intf?.portStatusUp, 'Up', 'Down'))}
            </span>

            <span className="row-item-3" />
            <span className="row-item-4">
              {`${t('MTU')}: ${intf?.mtu}`}
            </span>
            <span className="row-item-5">
              {`${t('SHUTDOWN')}: ${trinary(intf?.shutdownOn, t('YES'), t('NO'))}`}
            </span>
          </div>
        )}
        <ReviewGroup
          title={(
            <div className="interface-details full-width">
              <span className="row-item-1">
                <span className="light-color">
                  {trinary(isPort, t('UNTAGGED'), `${t('SUB_INTERFACE_VLAN')} ${intf?.vlanId || ''}`)}
                </span>
              </span>
              <span className="row-item-2">
                <SingleItemView value={intf?.description} generalValue={t('NO_DESCRIPTION')} />
              </span>
              <span className="row-item-3">
                <span className="light-color">
                  {`${t('IP_ADDRESS')} `}
                </span>
                {`${intf?.ipInfo?.ipStart}/${fromMaskToCIDR(intf?.ipInfo?.netmask)}`}
              </span>
              <span className="row-item-4">
                <span className="light-color">
                  {trinary(isPort, '', `${t('MTU')} `)}
                </span>
                {trinary(isPort, '', `${intf?.mtu}`)}
              </span>
              <span className="row-item-5">
                <span className="light-color">
                  {trinary(isPort, '', `${t('INTERFACE_SHUTDOWN')} `)}
                </span>
                {trinary(isPort, '', `${trinary(intf?.shutdownOn, t('YES'), t('NO'))}`)}
              </span>
            </div>
          )}
          styleClass="margin-bottom-0px inline-block full-width"
          styleTitleContainerClass="margin-bottom-0px margin-left-26px position-relative interface-details-review-group">

          <div className="interface-details-window">

            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{`${t('IP_ADDRESS')}`}</span>
              </span>
              <span className="row-item-2">
                {`${intf?.ipInfo?.ipStart}/${fromMaskToCIDR(intf?.ipInfo?.netmask)}`}
              </span>
            </div>

            <div className="separator-line-dashboard" />
          
            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{t('HIGH_AVAILABILITY')}</span>
              </span>
              <span className="row-item-2">
                {intf?.carpHAEnabled ? t('ENABLED') : t('DISABLED')}
              </span>
            </div>
            {intf?.carpHAEnabled && (
              <>
                <div className="interface-details-window-row line-value-title">
                  <span className="row-item-1">
                    {' '}
                  </span>
                  <span className="row-item-2">
                    <span className="light-color">
                      {t('HA_STATE')}
                    </span>
                  </span>
                  <span className="row-item-3">
                    <span className="light-color">
                      {t('ID')}
                    </span>
                  </span>
                  <span className="row-item-4">
                    <span className="light-color">
                      {t('VIRTUAL_IP_ADDRESS')}
                    </span>
                  </span>
                  <span className="row-item-5">
                    <span className="light-color">
                      {t('PREFERRED')}
                    </span>
                  </span>
                </div>
                <div className="interface-details-window-row line-value">
                  <span className="row-item-1">
                    {' '}
                  </span>
                  <span className="row-item-2 value margin-left-0px">
                    {getStatusIcon(intf?.carpHAState)}
                  </span>
                  <span className="row-item-3">
                    {intf?.carpId}
                  </span>
                  <span className="row-item-4">
                    {intf?.carpVip}
                  </span>
                  <span className="row-item-5">
                    {trinary(intf?.carpHAPreferredOn, t('ON'), t('OFF'))}
                  </span>
                </div>
              </>
            )}

            <div className="separator-line-dashboard" />

            <div className="interface-details-window-row">
              <span className="row-item-1">
                <span className="light-color">{t('DHCP_SERVER')}</span>
              </span>
              <span className="row-item-2">
                {trinary(intf?.dhcpEnabled, t('ENABLED'), t('DISABLED'))}
              </span>
            </div>
          
            {intf?.dhcpEnabled && (
              <>
                <div className="interface-details-window-row line-value-title">
                  <span className="row-item-1">
                    {' '}
                  </span>
                  <span className="row-item-2">
                    <span className="light-color">
                      {t('INCLUDE_ADDRESS_RANGE')}
                    </span>
                  </span>
                  <span className="row-item-3">
                    <span className="light-color">
                      {t('DEFAULT_LEASE_TIME')}
                    </span>
                  </span>
                  <span className="row-item-4">
                    <span className="light-color">
                      {t('MAX_LEASE_TIME')}
                    </span>
                  </span>
                </div>
                <div className="interface-details-window-row line-value">
                  <span className="row-item-1">
                    {' '}
                  </span>
                  <span className="row-item-2">
                    <SingleItemView value={intf?.dhcpServer?.range?.map((x) => `${x?.start}-${x?.end} `)} generalValue={t('---')} />
                  </span>
                  <span className="row-item-3">
                    {intf?.dhcpServer?.globals?.defaultLeaseTime}
                  </span>
                  <span className="row-item-4">
                    {intf?.dhcpServer?.globals?.maxLeaseTime}
                  </span>
                </div>

                <div className="interface-details-window-row line-value-title">
                  <span className="row-item-2">
                    <span className="light-color">
                      {t('DHCP_OPTIONS')}
                    </span>
                  </span>
                  <span className="row-item-3">
                    <span className="light-color">
                      {t('STATIC_LEASE')}
                    </span>
                  </span>
                </div>
                <div className={`interface-details-window-row ${dhcpOptionsLength > 1 ? 'line-value-multi' : 'line-value'}`}>
                  <span className="row-item-2">
                    <SingleItemView value={`${t('DEFAULT_GW')}: ${intf?.dhcpServer?.options?.routers?.map((x) => x)}`} generalValue={t('---')} />
                  </span>
                  {trinary(
                    intf?.dhcpServer?.fixedHosts?.length > 0,
                    (
                      <NavLink
                        className="row-item-3 view-info"
                        to="?"
                        onClick={() => {
                          dispatch(handleDrawer(true, intf?.dhcpServer?.fixedHosts));
                        }}>
                        {t('VIEW_INFO')}
                      </NavLink>
                    ), (
                      <span className="row-item-3 view-info disabled-input">
                        {t('NO_STATIC_LEASE_CONFIGURED')}
                      </span>
                    ),
                  )}
                </div>
                {dhcpOptionsLength > 1 && (
                  <div className={`interface-details-window-row ${dhcpOptionsLength > 2 ? 'line-value-multi' : 'line-value'}`}>
                    <span className="row-item-2">
                      { dhcpOptionsDNS && (
                        <SingleItemView value={`${t('DNS_SERVER')} ${dhcpOptionsDNS}`} generalValue={t('---')} />
                      )}

                      { dhcpOptionsDomain && !dhcpOptionsDNS && (
                        <SingleItemView value={`${t('DOMAIN')}: ${dhcpOptionsDomain}`} generalValue={t('---')} />
                      )}
                    </span>
                  </div>
                )}
                {dhcpOptionsLength > 2 && (
                  <div className="interface-details-window-row line-value">
                    <span className="row-item-2">
                      <SingleItemView value={`${t('DOMAIN')}: ${dhcpOptionsDomain}`} generalValue={t('---')} />
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </ReviewGroup>
        {idx === networks?.length - 1 && (
          <div className="interfaces">
            <div className="last-row-interface full-width">
              <span className="row-item-1">
                {`${t('LAN_DNS')}`}
              </span>
            </div>
            <div className="last-row-interface min-height-32px no-backgrond-color full-width position-relative">
              <span className="row-item-1">
                <span className="light-color">
                  {t('USE_WAN_DNS_SEVER')}
                </span>
              </span>
              <span className="row-item-2">
                <SingleItemView value={trinary(useWanDns, t('YES'), t('NO'))} generalValue="---" />
              </span>
            </div>
            <div className="last-row-interface min-height-32px no-backgrond-color full-width position-relative ">
              <span className="row-item-1">
                <span className="light-color">
                  {t('PRIMARY_DNS_SERVER')}
                </span>
              </span>
              <span className="row-item-2">
                <SingleItemView value={trinary(lanDns?.length > 0, lanDns && lanDns[0], t('NA'))} generalValue={t('NA')} />
              </span>
            </div>
            <div className="last-row-interface min-height-32px no-backgrond-color full-width position-relative margin-bottom-16px">
              <span className="row-item-1">
                <span className="light-color">
                  {t('SECONDARY_DNS_SERVER')}
                </span>
              </span>
              <span className="row-item-2">
                <SingleItemView value={trinary(lanDns?.length > 1, lanDns && lanDns[1], t('NA'))} generalValue={t('NA')} />
              </span>
            </div>
          </div>
        )}
        <Drawer
          isOpen={openDrawer}
          customHeader={header(t)}
          showBackdrop
          onClose={() => dispatch(handleDrawer(false))}
          className="policy-remediate-main sliding-drawer">
          <div className="content-tabs-container">
            {/* <Loading loading={modalLoading} /> */}
            <DrawerTable drawerData={drawerData} />
          </div>

        </Drawer>
      </div>
    );
  });
}

BuildLan.propTypes = {
  network: PropTypes.arrayOf({}),
  t: PropTypes.func,
};
      
BuildLan.defaultProps = {
  network: [],
  t: (str) => str,
};
export default BuildLan;
