import React from 'react';
import PropTypes from 'prop-types';
import { getStatusIcon, convertSecondsToDayHourMinSec } from 'utils/helpers';
import ReviewGroup from 'components/reviewGroup';

function BuildZiaOrZPAWanTraffic({ data, t, isZPA = false }) {
  return data.map((item, idx, self) => {
    const {
      gatewayIP, status, txBytes, rxBytes,
      interfaceName, gatewayName,
      txPkts, rxPkts, uptime,
      isControl, isData,
    } = item;
  
    return (
    // eslint-disable-next-line react/no-array-index-key
      <div key={idx}>
        { (idx === 0 || interfaceName !== self[idx - 1]?.interfaceName) && (
          <div className="first-row-interface full-width">
            <span className="row-item-1">
              {`${t('INTERFACE')} ${interfaceName?.toUpperCase()}`}
            </span>
          </div>
        )}
        <ReviewGroup
          title={(
            <div className="interface-details full-width">
              <span className="row-item-1">
                <span className="light-color">
                  {gatewayName}
                </span>
              </span>
              <span className="row-item-2">
                <span className="light-color">
                  {`${t('STATUS')}:`}
                  {getStatusIcon(status === 'UP' ? 'Up' : 'Down')}
                </span>
              </span>

              <span className="row-item-3">
                <span className="light-color">
                  {`${t('IP_ADDRESS')}: ${gatewayIP}`}
                </span>
              </span>
            </div>
          )}
          styleClass="margin-bottom-0px inline-block full-width"
          styleTitleContainerClass="margin-bottom-0px margin-left-26px position-relative interface-details-review-group">

          <div className="interface-details-window">
            <div className="interface-details-window-row line-value-title">
              <span className="row-item-1">
                <span className="light-color">
                  {t('TX_RX_BYTES')}
                </span>
              </span>
              <span className="row-item-2">
                <span className="light-color">
                  {t('TX_RX_PACKETS')}
                </span>
              </span>
              <span className="row-item-3">
                <span className="light-color">
                  {t('UPTIME')}
                </span>
              </span>
            </div>
            <div className="interface-details-window-row line-value">
              <span className="row-item-1">
                {`${txBytes} | ${rxBytes}`}
              </span>
              <span className="row-item-2">
                {`${txPkts} | ${rxPkts}`}
              </span>
              <span className="row-item-3">
                {convertSecondsToDayHourMinSec(uptime)}
              </span>
            </div>

            {isZPA && (
              <>
                <div className="separator-line-dashboard" />
          
                <div className="interface-details-window-row">
                  <span className="row-item-1">
                    <span className="light-color">{t('TUNNEL_TYPE')}</span>
                  </span>
                  <span className="row-item-2">
                    {isData && isControl && t('CONTROL/DATA')}
                    {!isData && isControl && t('CONTROL')}
                    {isData && !isControl && t('DATA')}
                    {!isData && !isControl && t('---')}
                  </span>
                </div>
              </>
            )}
          </div>
        </ReviewGroup>
      </div>
    );
  });
}

BuildZiaOrZPAWanTraffic.propTypes = {
  network: PropTypes.arrayOf({}),
  t: PropTypes.func,
};
    
BuildZiaOrZPAWanTraffic.defaultProps = {
  network: [],
  t: (str) => str,
};

export default BuildZiaOrZPAWanTraffic;
