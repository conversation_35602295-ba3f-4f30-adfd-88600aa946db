import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import { useLocation } from 'react-router-dom';
import HelpArticle from 'components/HelpArticle';
import LineItem from 'components/LineItem';
import {
  faArrowLeft,
  faSyncAlt,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import NavTabs from 'components/navTabs';
import {
  convertZeros, getIcon, getStatusIcon, getHaStatusIcon,
  isHardwareAppliance, verifyConfigData,
  getCpuByModel, getMemoryByModel, getPortsByModel,
  fromMaskToCIDR,
} from 'utils/helpers';
import './index.scss';

const getGeoInfo = (geoLocation) => {
  if (!geoLocation) return '';

  if (typeof geoLocation === 'string') {
    return geoLocation;
  } if (typeof geoLocation === 'object') {
    return `${geoLocation.cityName ? geoLocation.cityName : '-'}, ${geoLocation.countryCode ? geoLocation.countryCode : '-'}`;
  }
  return '';
};

export function CloudConnector(props) {
  const {
    accountIdEnabled,
    subIdEnabled,
    projectIdEnabled,
    showCloudConnector,
    cloudConnectorData,
    backToParent,
    refresh,
    deviceStatuslogTime,
    t,
  } = props;
  const queryLocation = useLocation().search;
  const params = new URLSearchParams(queryLocation);
  const filter = params.get('filter');

  const {
    ecName,
    location,
    geoLocation,
    status,
    haStatus = '',
    deploymentType,
    group,
    version,
    autoScale,
    vmSize,
    ziaGw,
    zpaBroker,
    internalGwIpAddr,
    modelType,
    ecInstance,
    managementNw,
    accountId = '',
    subscriptionId = '',
    gcpProjectId = '',

    deviceModelNo = '',
    deviceName = '',
    deviceSerialNo = '',
    deviceType = '',
  } = cloudConnectorData;

  const isAppliance = deviceType === 'PHYSICAL' || isHardwareAppliance(deploymentType) || isHardwareAppliance(modelType);
  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const enableAWSASG = verifyConfigData({ configData, key: 'enableASG' });
  const enableAzureASG = verifyConfigData({ configData, key: 'enableAzureASG' });
  const enableASG = enableAWSASG || enableAzureASG;

  if (!showCloudConnector) return null;
  return (
    <>
      <HelpArticle article={HELP_ARTICLES.CLOUD_CONNECTOR_DETAILS} />
      <div className="dashboard cloudconnector-info">
        {/* navigation header */}
        <div className="title-container">
          <FontAwesomeIcon
            className="far title-back"
            icon={faArrowLeft}
            size="lg"
            onClick={() => backToParent()}
            role="button"
            aria-label="Back to Parent"
            tabIndex="0"
            onKeyPress={() => backToParent()} />
          <div className="title-nav">
            <span>
              {t('BRANCH_AND_CLOUD_MONITORING')}
            </span>
          </div>
          <div className="title-nav-right">
            <span style={{ paddingRight: '1em' }}>
              {t('>')}
            </span>
            <span>
              {getIcon(deploymentType, status, deviceType)}
              {' '}
              {ecName}
            </span>
            <span
              className="refresh space-between"
              onClick={() => refresh()}
              role="button"
              aria-label="Refresh"
              tabIndex="0"
              onKeyPress={() => refresh()}>
              <FontAwesomeIcon
                className="far refresh"
                icon={faSyncAlt}
                size="lg" />
              {' '}
              {t('REFRESH')}
            </span>
          </div>
        </div>
        {/* Tabs Section */}
        <div className="config-navagation-tab">
          {/* Tabs */}
          <div>
            <div className="nav-tabs space-between">
              <NavTabs
                tabConfiguration={[{
                  id: 'connector-monitoring',
                  title: t(['CENTOS', 'REDHAT_LINUX', 'MICROSOFT_HYPER_V', 'VMWARE_ESXI'].includes(deploymentType) || deviceType === 'PHYSICAL' ? 'BC_DETAILS' : 'CC_DETAILS'),
                  to: `${BASE_LAYOUT}/dashboard/connector-monitoring?filter=${filter || ''}`,
                },
                {
                  id: 'traffic-monitoring',
                  title: t('TRAFFIC_FLOW'),
                  to: `${BASE_LAYOUT}/dashboard/traffic-monitoring?filter=${filter || ''}`,
                }]} />
              {/* Status & Action */}
              <div className="status">
                <span>
                  {t('LAST_UPDATE')}
                  {' '}
                  {deviceStatuslogTime}
                </span>
              </div>
            </div>

          </div>
        </div>

        {/* Top Row */}
        <div className="dashboard-detail-panel">
          <div className="side-header">
            {t('GENERAL_INFORMATION')}
          </div>
          <div className="content connector-monitoring">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('DEPLOYMENT_DETAILS')}
                  </div>
                </div>
                <div className="container">
                  {!isAppliance && <LineItem label={t('DEPLOYMENT_TYPE')} value={deploymentType} />}
                  {isAppliance && <LineItem label={t('DEVICE_MODEL')} value={deviceModelNo} />}
                  {isAppliance && <LineItem label={t('SERIAL_NUMBER')} value={deviceSerialNo} />}
                  <LineItem label={t('GROUP')} value={group} />
                  <LineItem label={t('LOCATION')} value={location} />
                  <LineItem label={t('GEO_LOCATION')} value={getGeoInfo(geoLocation)} />
                  {!isAppliance && <LineItem label={t('VM_SIZE')} value={getGeoInfo(vmSize)} />}
                  <LineItem label={t('VERSION')} value={version} />
                  {enableASG && <LineItem label={t('AUTO_SCALING')} value={t(autoScale)} />}
                  {accountIdEnabled && deploymentType === 'AWS' && (
                    <LineItem label={t('ACCOUNT_ID')} value={accountId} />
                  )}
                  {subIdEnabled && deploymentType === 'AZURE' && (
                    <LineItem label={t('SUBSCRIPTION_ID')} value={subscriptionId} />
                  )}
                  {projectIdEnabled && deploymentType === 'GCP' && (
                    <LineItem label={t('EC_PROJECT_ID')} value={gcpProjectId} />
                  )}
                </div>
              </div>
              {/* Middle Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('OPERATIONAL_STATUS')}
                  </div>
                </div>
                <div className="container">
                  <LineItem label={t('STATUS')} value={getStatusIcon(status)} />
                </div>
                {(deploymentType
              && (deploymentType === 'CENTOS'
                || deploymentType === 'REDHAT_LINUX'
                || deploymentType === 'VMWARE_ESXI'))
              && (
                <div className="container">
                  <LineItem label={t('HA_STATUS')} value={getHaStatusIcon(haStatus)} />
                </div>
              )}
              </div>
              {/* Right Segment */}
              <div>
                <div className="content-header">
                  <div>
                    {t('GATEWAY_DETAILS')}
                  </div>
                </div>
                <div className="container">
                  <LineItem label={t('MANAGEMENT_DEFAULT_GATEWAY')} value={(managementNw && managementNw.defaultGw) ? convertZeros(managementNw.defaultGw) : '-'} />
                  <LineItem label={t('ZIA_GATEWAY')} value={convertZeros(ziaGw)} />
                  <LineItem label={t('ZPA_BROKER')} value={convertZeros(zpaBroker)} />
                  <LineItem label={t('INTERNAL_GATEWAY_IP_ADDRESS')} value={convertZeros(internalGwIpAddr)} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {isAppliance && (
          <div className="dashboard-detail-panel">
            <div className="side-header">
              {t('HARDWARE_DEVICE')}
            </div>
            <div className="content connector-monitoring">
              <div className="content-box-flex">
                {/* Left Segment */}
                <div className="right-border">
                  <div className="content-header">
                    <div>
                      {t('DEVICE_DETAILS')}
                    </div>
                  </div>
                  <div className="container">
                    <LineItem label={t('DEVICE_SERIAL_NUMBER')} value={deviceSerialNo} />
                    <LineItem label={t('DEVICE_NAME')} value={deviceName} />
                    <LineItem label={t('DEVICE_TYPE')} value={t('PHYSICAL')} />
                    <LineItem label={t('MODEL_NUMBER')} value={deviceModelNo} />
                  </div>
                </div>
                {/* Middle Segment */}
                <div className="right-border">
                  <div className="content-header">
                    <div>
                      {t('SPECIFICATIONS')}
                    </div>
                  </div>
                  <div className="container">
                    <LineItem label={t('CPU')} value={getCpuByModel(deviceModelNo)} />
                  </div>
                  <div className="container">
                    <LineItem label={t('PORTS')} value={getPortsByModel(deviceModelNo)} />
                  </div>
                  <div className="container">
                    <LineItem label={t('MEMORY')} value={getMemoryByModel(deviceModelNo)} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Middle Row */}
        <div className="dashboard-detail-panel">
          <div className="side-header">
            {t('FORWARDING_INFORMATION')}
          </div>
          <div className="content connector-monitoring">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('DNS_DETAILS')}
                  </div>
                </div>
                {!ecInstance && (
                  <div className="container">
                    <LineItem label={t('DNS_SERVER_ONE')} value="---" />
                    <LineItem label={t('DNS_SERVER_TWO')} value="---" />
                  </div>
                )}
                {ecInstance && [ecInstance.find((x) => x.instanceType === 'SME')].map((ec) => (
                  <div className="container" key={ec && ec.outGwIp}>
                    <LineItem label={t('DNS_SERVER_ONE')} value={(ec && ec.dnsIp && ec.dnsIp.length && ec.dnsIp[0]) ? convertZeros(ec.dnsIp[0]) : '-'} />
                    <LineItem label={t('DNS_SERVER_TWO')} value={(ec && ec.dnsIp && ec.dnsIp.length && ec.dnsIp[1]) ? convertZeros(ec.dnsIp[1]) : '-'} />
                  </div>
                ))}
              </div>
              {/* Middle Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('INGRESS_DETAILS')}
                  </div>
                </div>
                {!ecInstance && (
                  <div className="container">
                    <LineItem label={t('VIRTUAL_IP_ADDRESS')} value="---" />
                    <LineItem label={t('LOAD_BALANCER')} value="---" />
                  </div>
                )}
                {ecInstance && ecInstance.filter((x) => x.instanceType === 'SME').map((ec) => (
                  <div key={ec.natIp}>
                    <div className="container">
                      <LineItem label={t('VIRTUAL_IP_ADDRESS')} value={(ec.virtualIp && ec.virtualIp.ipStart) ? `${convertZeros(ec.virtualIp.ipStart)}/${fromMaskToCIDR(ec.virtualIp?.netmask || '')}` : '---'} />
                    </div>
                  </div>
                ))}
                {ecInstance && ecInstance.filter((x) => x.instanceType !== 'SME').map((ec) => (
                  <div key={ec.natIp}>
                    <div className="container">
                      <LineItem label={t('LOAD_BALANCER')} value={(ec.lbIpAddr && ec.lbIpAddr.ipStart) ? `${convertZeros(ec.lbIpAddr.ipStart)}/${fromMaskToCIDR(ec.lbIpAddr?.netmask || '')}` : '---'} />
                    </div>
                  </div>
                ))}
              </div>

              {/* Right Segment */}
              <div>
                <div className="content-header">
                  <div>
                    {t('EGRESS_DETAILS')}
                  </div>
                </div>
                {!ecInstance && (
                  <div className="container">
                    <LineItem label={t('SERVICE_IP_ADDRESS')} value="---" />
                    <LineItem label={t('NAT_IP_ADDRESS')} value="---" />
                    <LineItem label={t('OUTGOING_GATEWAY_IP_ADDRESS')} value="---" />
                  </div>
                )}
                {ecInstance && (ecInstance.filter((x) => x.instanceType === 'SME').map((ec, e) => (
                  <div className="container" key={ec.natIp}>
                    <LineItem
                      label={`${t('SERVICE_IP_ADDRESS')} ${(e + 1)}`}
                      value={ec.serviceIps && ec.serviceIps.ipStart ? `${convertZeros(ec.serviceIps.ipStart)}/${fromMaskToCIDR(ec.serviceIps?.netmask || '')}` : '---'} />
                    <LineItem label={t('NAT_IP_ADDRESS')} value={ec.natIp ? convertZeros(ec && ec.natIp) : '---'} />
                    <LineItem label={t('OUTGOING_GATEWAY_IP_ADDRESS')} value={convertZeros(ec && ec.outGwIp)} />
                  </div>
                )))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row */}
        <div className="dashboard-detail-panel">
          <div className="side-header">
            {t('MANAGEMENT_INFORMATION')}
          </div>
          <div className="content connector-monitoring">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('MANAGEMENT_DETAILS')}
                  </div>
                </div>
                <div className="container">
                  <LineItem label={t('MANAGEMENT_IP_ADDRESS')} value={(managementNw?.managementIp?.ipStart) ? `${convertZeros(managementNw?.managementIp?.ipStart)}/${fromMaskToCIDR(managementNw?.managementIp?.netmask || '')}` : '---'} />
                  <LineItem label={t('NAT_IP_ADDRESS')} value={(managementNw && managementNw.natIp) ? convertZeros(managementNw.natIp) : '-'} />
                  <LineItem label={t('DEFAUL_GATEWAY_IP_ADDRESS')} value={(managementNw && managementNw.defaultGw) ? convertZeros(managementNw.defaultGw) : '-'} />
                </div>
              </div>
              {/* Middle Segment */}
              <div>
                <div className="content-header">
                  <div>
                    {t('DNS_DETAILS')}
                  </div>
                </div>
                <div className="container">
                  {!managementNw && (
                    <>
                      <LineItem label={t('DNS_SERVER_ONE')} value="---" />
                      <LineItem label={t('DNS_SERVER_TWO')} value="---" />
                    </>
                  )}
                  {(managementNw && managementNw.dnsIp
                && Array.isArray(managementNw.dnsIp) && managementNw.dnsIp.length)
                    ? managementNw.dnsIp.map((ip, e) => (
                      // eslint-disable-next-line react/no-array-index-key
                      <LineItem key={e} label={`${t('DNS_SERVER')} ${(e + 1)}`} value={convertZeros(ip)} />
                    ))
                    : ''}
                </div>
              </div>
              {/* Right Segment */}
              <div>
                <div className="content-header">
                </div>
                <div className="container">
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </>
  );
}

CloudConnector.propTypes = {
  cloudConnectorData: PropTypes.shape({
    ecName: PropTypes.string,
    location: PropTypes.string,
    geoLocation: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    status: PropTypes.string,
    haStatus: PropTypes.string,
    deploymentType: PropTypes.string,
    group: PropTypes.string,
    version: PropTypes.string,
    autoScale: PropTypes.string,
    vmSize: PropTypes.string,
    ziaGw: PropTypes.string,
    zpaBroker: PropTypes.string,
    internalGwIpAddr: PropTypes.string,
    modelType: PropTypes.string,
    ecInstance: PropTypes.arrayOf(PropTypes.shape({})),
    managementNw: {
      managementIp: {
        ipEnd: PropTypes.string,
        ipStart: PropTypes.string,
      },
      defaultGw: PropTypes.string,
    },
    accountId: PropTypes.string,
    subscriptionId: PropTypes.string,
    gcpProjectId: PropTypes.string,
    deviceModelNo: PropTypes.string,
    deviceName: PropTypes.string,
    deviceSerialNo: PropTypes.string,
    deviceType: PropTypes.string,
  }),
  showCloudConnector: PropTypes.bool,
  backToParent: PropTypes.func,
  deviceStatuslogTime: PropTypes.string,
  t: PropTypes.func,
  refresh: PropTypes.func,
  accountIdEnabled: PropTypes.bool,
  subIdEnabled: PropTypes.bool,
  projectIdEnabled: PropTypes.bool,
};

CloudConnector.defaultProps = {
  cloudConnectorData: {
    lastModifiedTime: '',
    managementNw: {
      managementIp: {
        ipEnd: '',
        ipStart: '',
      },
      defaultGw: '',
    },
  },
  showCloudConnector: false,
  backToParent: null,
  deviceStatuslogTime: '',
  t: (str) => str,
  refresh: (str) => str,
  accountIdEnabled: false,
  subIdEnabled: false,
  projectIdEnabled: false,
};

export default withTranslation()(CloudConnector);
