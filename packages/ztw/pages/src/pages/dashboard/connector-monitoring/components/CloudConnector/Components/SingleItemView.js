import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { isEmpty } from 'utils/lodash';
import { useTranslation } from 'react-i18next';

function SingleItemView({ value, generalValue }) {
  // const item = row[field];
  // const value = getValue(item);
  const valueRef = useRef(null);
  const { t } = useTranslation();
  const [isValueEllipsisActive, setIsValueEllipsisActive] = useState(false);

  useEffect(() => {
    if (!valueRef.current) return;

    if (valueRef.current.offsetWidth < valueRef.current.scrollWidth) {
      setIsValueEllipsisActive(true);
    }
  }, [valueRef, value]);
  
  return (
    <div style={{ position: 'relative' }}>
      <div className="policy-table-criteria-item">
        <div className="policy-table-criteria-item-data">
          <span
            ref={valueRef}
            data-for="DashboardTooltip"
            data-tip={isValueEllipsisActive ? value : ''}
            className={`policy-table-cell-data ${isValueEllipsisActive ? 'has-elipsis' : ''}`}>
            <span className="policy-table-criteria-item-data-item">
              {!isEmpty(value) ? value : t(generalValue)}
              <ReactTooltip
                id="DashboardTooltip"
                clickable
                place="top"
                type="light"
                offset={{ top: -10 }}
                effect="solid"
                border
                borderColor="#939393"
                className="form-field-tooltip-container" />
            </span>
          </span>
        </div>
      </div>
    </div>
  );
}

SingleItemView.propTypes = {
  value: PropTypes.string,
  generalValue: PropTypes.string,
};
  
SingleItemView.defaultProps = {
  value: '',
  generalValue: '',
};

export default SingleItemView;
