/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { PageFiltersHOC } from 'components/hoc';
import { BASE_ROUTE_PATH } from 'config';
import {
  faBuilding,
} from '@fortawesome/pro-solid-svg-icons';
import AWS from 'images/aws.png';
import Azure from 'images/azure.png';
import GCP from 'images/gcp.svg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AwsRegionsDropdown from 'commonConnectedComponents/dropdown/AwsRegionsDropdown';
import AzureRegionsDropdown from 'commonConnectedComponents/dropdown/AzureRegionsDropdown';
import GcpRegionsDropdown from 'commonConnectedComponents/dropdown/GcpRegionsDropdown';
import BranchLocationsDropdown from 'commonConnectedComponents/dropdown/BranchLocationsDropdown';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, hasBCsku, verifyConfigData,
} from 'utils/helpers';

function PageFilters(props) {
  const {
    t,
    trafficAwsRegionsDropdown,
    trafficAzureRegionsDropdown,
    trafficGcpRegionsDropdown,
    trafficRegionsDropdown,
    accessSubscriptions,
    configData,
  } = props;
  const hasBCSubscription = hasBCsku(accessSubscriptions);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);
  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  const enableGcp = verifyConfigData({ configData, key: 'enableGcp' });

  return (
    <>
      { (hasBCSubscription || hasCSubscription) && (
        <>
          <div className="entity-dropdown-container">
            <label>
              <img src={AWS} className="filter-icon" alt="AWS" />
              {t('AWS_REGIONS')}
            </label>
            <Field
              id="trafficAwsRegionsDropdown"
              searchParamName="name"
              name="trafficAwsRegionsDropdown"
              label="All Regions"
              defaultValue={trafficAwsRegionsDropdown}
              dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
              component={AwsRegionsDropdown} />
          </div>
          <div className="entity-dropdown-container">
            <label>
              <img src={Azure} className="filter-icon-disabled" alt="Azure" />
              {t('AZURE')}
            </label>
            <Field
              id="trafficAzureRegionsDropdown"
              searchParamName="name"
              name="trafficAzureRegionsDropdown"
              label="None"
              defaultValue={trafficAzureRegionsDropdown}
              dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
              component={AzureRegionsDropdown} />
          </div>
          {enableGcp && (
            <div className="entity-dropdown-container">
              <label>
                <img src={GCP} className="filter-icon-gcp" alt="GCP" />
                {t('GCP')}
              </label>
              <Field
                id="trafficGcpRegionsDropdown"
                searchParamName="name"
                name="trafficGcpRegionsDropdown"
                label="None"
                defaultValue={trafficGcpRegionsDropdown}
                dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
                component={GcpRegionsDropdown} />
            </div>
          )}
        </>
      )}
      { (hasBCSubscription || hasBSubscription) && (
        <div className="entity-dropdown-container">
          <label>
            <FontAwesomeIcon className="fas filter-icon-branch filter-icon-disabled" icon={faBuilding} size="lg" alt="Branch" />
            {t('BRANCH_CONNECTOR_LOCS')}
          </label>
          <Field
            id="trafficRegionsDropdown"
            searchParamName="name"
            name="trafficRegionsDropdown"
            label="None"
            defaultValue={trafficRegionsDropdown}
            dataSrc={`${BASE_ROUTE_PATH}/api/v1/applications`}
            component={BranchLocationsDropdown} />
        </div>
      )}
    </>
  );
}

PageFilters.propTypes = {
  t: PropTypes.func,
  trafficAwsRegionsDropdown: PropTypes.shape({}),
  trafficAzureRegionsDropdown: PropTypes.shape({}),
  trafficGcpRegionsDropdown: PropTypes.shape({}),
  trafficRegionsDropdown: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  configData: PropTypes.shape(),
};

PageFilters.defaultProps = {
  t: (str) => str,
  trafficAwsRegionsDropdown: {},
  trafficAzureRegionsDropdown: {},
  trafficGcpRegionsDropdown: {},
  trafficRegionsDropdown: {},
  accessSubscriptions: [],
  configData: {},
};

export default PageFiltersHOC(withTranslation()(PageFilters));
