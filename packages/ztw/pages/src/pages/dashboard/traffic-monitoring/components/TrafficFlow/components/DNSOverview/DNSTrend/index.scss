@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.chart-container {
  position: relative;
  padding: 1em;
  min-height: 280px;

  .chart-section{
    padding: 0em 0em 0em 0em;
    .chart-sub-header {
      height: 20px;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      padding: 0em 0em 3em 1.5em
    }
  }
  .content{
    padding: 0em;

    .content-box-flex{
      width: 100%;
      height: 30px;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
      border: none;
    }
    .content-box-flex > div{
      width: 30%;
      flex: 1 1 auto;
      -webkit-flex: 1 1 auto;
      transition: width 0.7s ease-out;	
      -webkit-transition: width 0.7s ease-out;	
    }
    .middle-flex{
      margin-left: 10em;
    }
    .last-flex{
      margin-right: 0.25em;
      float: right;
      width: 150px;
    }
    .container {
      width: '60%';
      padding: 0em 3em 0em 3em;
    }
  }
}
