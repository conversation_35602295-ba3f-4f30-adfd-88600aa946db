@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.section-holder {
  position: relative;
  min-height: 280px;

  .sub-header{
    height: 20px;
    // width: 360px;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 20px;
    padding: 1em 0em 2em 0.25em;
    .interval {
      position: absolute;
      margin-left: 25%;
      .middle-flex{
        width: 30%;
        float: right;
      }
    }
    .trendDropDown{
      .drop-down-selected-value {
        border: none;
        border-radius: 0;
        background: none;
      }
    }
  }

  .sub-header-hr{
    box-sizing: border-box;
    height: 1px;
    border: 1px solid var(--semantic-color-border-base-primary);
    margin-bottom: 1.75em;
  }
  .section{
    padding: 1em 0em 0em 0em;

    .section-flip{
      float: left;
      color: var(--semantic-color-content-interactive-primary-default);
    }
    .section-header{
      height: 24px;
      width: 360px;
      color: var(--semantic-color-content-base-primary);
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.32px;
      // line-height: 24px;
      padding: 0em 0em 0em 0.25em;
    }
    .section-table{
      padding: 0em 1em 0em 1.25em;
      .app-table-container{
        margin-top: 1em;
      }
    }
  }

}
