// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import FlipButton from 'components/flipButton';

import { TOTAL_TRAFFIC_TABLE_CONFIGS, SESSION_TABLE_CONFIGS } from 'ducks/trafficMonitoring/constants';
import ConfigTable from 'components/configTable';
import SessionTrend from './SessionTrend';
import TrafficTrend from './TrafficTrend';

import './index.scss';

const getTrafficTableData = (totalTrafficTableData) => {
  const tableData = totalTrafficTableData.filter((x) => x.name.includes('Inbound')).map((row) => {
    return {
      services: row.services,
      zia: row.zia,
      zpa: row.zpa,
      direct: row.direct,
      cloudConnector: row.cloudConnector,
      isReadOnly: false,
      isDeletable: false,
      isEditable: false,
    };
  });
  return tableData;
};

const getSessionTableData = (sessionTableData) => {
  const tableData = sessionTableData.map((row) => {
    return {
      SERVICES: row.SERVICES,
      ZIA: row.ZIA,
      ECZPA: row.ECZPA,
      DIRECT: row.DIRECT,
      ECSELF: row.ECSELF,
      isReadOnly: false,
      isDeletable: false,
      isEditable: false,
    };
  });
  return tableData;
};

export function TrafficOverview(props) {
  const {
    showOverview,
    sessiontabledata,
    totaltraffictabledata,
    showSessionSection,
    showTotalTrafficSection,
    actions,
    t,
  } = props;

  if (!showOverview) return null;
  return (
    <Loading {...props}>
      <ServerError {...props}>
        <div className="section-holder">
          <div className="section">
            <div>
              <span className="section-flip">
                <FlipButton label={t('')} clickCallback={actions.toggleSessionSec} show={showSessionSection} />
              </span>
              <span className="section-header">
                {t('SESSION_COUNT')}
              </span>
            </div>
            <div className="section-table">
              <ConfigTable
                {...SESSION_TABLE_CONFIGS}
                data={getSessionTableData(sessiontabledata)} />
            </div>
          </div>
          <SessionTrend {...props} />

          <div className="section">
            <div>
              <span className="section-flip">
                <FlipButton label={t('')} clickCallback={actions.toggleTotalTrafficSec} show={showTotalTrafficSection} />
              </span>
              <span className="section-header">
                {t('TOTAL_TRAFFIC')}
              </span>
            </div>
            <div className="section-table">
              <ConfigTable
                {...TOTAL_TRAFFIC_TABLE_CONFIGS}
                data={getTrafficTableData(totaltraffictabledata)} />
            </div>
          </div>
          <TrafficTrend {...props} />
        </div>
      </ServerError>
    </Loading>
  );
}

TrafficOverview.propTypes = {
  t: PropTypes.func,
  showOverview: PropTypes.bool,
  sessiontabledata: PropTypes.arrayOf(PropTypes.shape()),
  totaltraffictabledata: PropTypes.arrayOf(PropTypes.shape()),
  showTotalTrafficSection: PropTypes.bool,
  showSessionSection: PropTypes.bool,
  load: PropTypes.func,
  actions: PropTypes.shape({
    load: PropTypes.func,
  }),
};

TrafficOverview.defaultProps = {
  t: (str) => str,
  showOverview: true,
  sessiontabledata: [],
  totaltraffictabledata: [],
  showTotalTrafficSection: true,
  showSessionSection: true,
  load: noop,
  actions: {},
};

export default (withTranslation()(TrafficOverview));
