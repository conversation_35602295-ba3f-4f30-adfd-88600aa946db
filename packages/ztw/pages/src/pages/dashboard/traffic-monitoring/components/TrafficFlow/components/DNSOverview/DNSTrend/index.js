import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import LineChartBasic from 'components/lineChartBasic';
import DropDown from 'components/dropDown';
import {
  TREND_TIME_FRAME_DATA,
  TREND_DETAIL_DATA,
} from 'config';

import './index.scss';

export function DNSTrend(props) {
  const {
    showTotalTrafficSection,
    showTotalDNSChartsMenu,
    dnsTrendData,
    dnsTrendChartData,
    dnsTrendDataKeys,
    actions,
    t,
  } = props;

  if (!showTotalTrafficSection) return null;
  return (
    <div className="chart-container">
      <div>
        <div className="sub-header">
          <div className="content">
            <div className="content-box-flex">
              <div>
                {t('DNS_TRANSACTION_TREND')}
              </div>
              <div className="interval">
                <div className="middle-flex trendDropDown">
                  <DropDown
                    items={TREND_TIME_FRAME_DATA}
                    setValue={(str) => str}
                    name="dnsTrendInterval"
                    onCallBack={actions.handleTrendInterval}
                    defaultValue={{
                      value: '24_HOURS',
                      label: '24_HOURS',
                    }} />
                </div>
              </div>
              <div>
                <div className="last-flex trendDropDown">
                  <DropDown
                    items={TREND_DETAIL_DATA}
                    setValue={(str) => str}
                    name="TrafficTrendDetail"
                    // eslint-disable-next-line react/jsx-handler-names
                    onCallBack={actions.toggleTotalDNSChartsMenu}
                    defaultValue={{
                      value: 'SHOW_DETAILS',
                      label: 'SHOW_DETAILS',
                    }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr className="sub-header-hr" />
      {dnsTrendData
        .filter((x) => (!showTotalDNSChartsMenu || (showTotalDNSChartsMenu && x.key === 'total')))
        .map((index) => (
          index.checkbox
            ? (
              <div className="chart-section" key={'DNS' + index.name}>
                <div className="chart-sub-header">
                  {t(index.name)}
                </div>
                <LineChartBasic
                  data={dnsTrendChartData[index.dataKey]}
                  dataKeys={dnsTrendDataKeys}
                  clickMoreInfo={actions.drilldownDnsLogs} />
              </div>
            )
            : ''
        ))}
    </div>
  );
}

DNSTrend.propTypes = {
  cloudConnectorData: PropTypes.shape({}),
  showTotalTrafficSection: PropTypes.bool,
  showTotalDNSChartsMenu: PropTypes.bool,
  dnsTrendData: PropTypes.arrayOf(PropTypes.shape({})),
  dnsTrendChartData: PropTypes.shape({}),
  dnsTrendDataKeys: PropTypes.arrayOf(PropTypes.shape({})),
  actions: PropTypes.shape({}),
  t: PropTypes.func,
};

DNSTrend.defaultProps = {
  cloudConnectorData: {
    lastUpdated: '',
  },
  showTotalTrafficSection: false,
  showTotalDNSChartsMenu: false,
  dnsTrendData: [{}],
  dnsTrendChartData: {},
  dnsTrendDataKeys: [],
  actions: {},
  t: (str) => str,
};

export default withTranslation()(DNSTrend);
