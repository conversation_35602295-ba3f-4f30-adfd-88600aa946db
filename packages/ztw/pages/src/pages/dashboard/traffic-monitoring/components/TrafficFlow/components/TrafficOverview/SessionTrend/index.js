import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import LineChartBasic from 'components/lineChartBasic';
import DropDown from 'components/dropDown';
import {
  TREND_DETAIL_DATA,
  TREND_TIME_FRAME_DATA,
  // BASE_ROUTE_PATH,
} from 'config';
// import { Redirect, BrowserRouter, Route } from 'react-router-dom';
// import SessionLogs from 'pages/logs/session-logs';

import './index.scss';

export function SessionTrend(props) {
  const {
    showSessionSection,
    showTotalSessionChartsMenu,
    sessionTrendData,
    sessionTrendChartData,
    sessionTrendDataKeys,
    actions,
    // drillDownFilter,
    t,
  } = props;

  if (!showSessionSection) return null;
  // if (drillDownFilter.name) {
  //   return (
  //     <BrowserRouter basename={BASE_ROUTE_PATH} forceRefresh>
  //       <Redirect from="/" to="/admin/logs/sessionlogs" push />
  //       {/* <Route path="/admin" component={SessionLogs} /> */}
  //     </BrowserRouter>
  //   );
  // }
  return (
    <div className="chart-container">
      <div>
        <div className="sub-header">
          <div className="content">
            <div className="content-box-flex">
              <div>
                {t('SESSION_COUNT_TREND')}
              </div>
              <div className="interval">
                <div className="middle-flex trendDropDown">
                  <DropDown
                    items={TREND_TIME_FRAME_DATA}
                    setValue={(str) => str}
                    name="sessionTrendInterval"
                    onCallBack={actions.handleTrendInterval}
                    defaultValue={{
                      value: '24_HOURS',
                      label: '24_HOURS',
                    }} />
                </div>
              </div>
              <div>
                <div className="last-flex trendDropDown">
                  <DropDown
                    items={TREND_DETAIL_DATA}
                    setValue={(str) => str}
                    name="sessionTrendDetail"
                    // eslint-disable-next-line react/jsx-handler-names
                    onCallBack={actions.toggleTotalSessionChartsMenu}
                    defaultValue={{
                      value: 'SHOW_DETAILS',
                      label: 'SHOW_DETAILS',
                    }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr className="sub-header-hr" />
      <div className="chart-section">
        {sessionTrendData
          .filter((x) => (!showTotalSessionChartsMenu || (showTotalSessionChartsMenu && x.key === 'total')))
          .map((index) => (
            index.checkbox
              ? (
                <div className="chart-section" key={'Session' + index.name}>
                  <div className="chart-sub-header">
                    {t(index.name)}
                  </div>
                  <LineChartBasic
                    {...props}
                    data={sessionTrendChartData[index.dataKey]}
                    dataKeys={sessionTrendDataKeys}
                    clickMoreInfo={actions.drilldownSessionLogs} />
                </div>
              )
              : ''
          ))}
      </div>
    </div>
  );
}

SessionTrend.propTypes = {
  cloudConnectorData: PropTypes.shape({}),
  sessionTrendChartData: PropTypes.shape({}),
  showTotalSessionChartsMenu: PropTypes.bool,
  sessionTrendData: PropTypes.arrayOf(PropTypes.shape({})),
  showSessionSection: PropTypes.bool,
  sessionTrendDataKeys: PropTypes.arrayOf(PropTypes.shape({})),
  actions: PropTypes.shape({}),
  drillDownFilter: PropTypes.shape({}),
  t: PropTypes.func,
};

SessionTrend.defaultProps = {
  cloudConnectorData: {
    lastUpdated: '',
  },
  sessionTrendChartData: {},
  showTotalSessionChartsMenu: false,
  sessionTrendData: [{}],
  showSessionSection: false,
  sessionTrendDataKeys: [],
  actions: {},
  drillDownFilter: {},
  t: (str) => str,
};

export default withTranslation()(SessionTrend);
