@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.global-ux-score {
  position: relative;
  min-height: 280px;

  .selected-locations-header {
    width: 100%;
    p{
      float: left;
    }
    .fa-times {
      float: right;
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
  }
}

.cloudconnector-info {
  margin-bottom: 50px;
  .title-back{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
  }
  .title-nav{
   display: initial;
   padding-left: 1em;
   color: #949494;
   font-size: 16px;
   font-weight: 500;
   letter-spacing: 0;
   line-height: 24px;
  }
  .title-nav-right{
    display: initial;
    padding: 0em 1em 0em 1em;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 24px;
   }
   .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .config-nav-tab {
    padding: 1em 2em 0em 2.5em;
  }
  .status{
    display: initial;
    padding-left: 25%;
    color:  var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0.2px;
    line-height: 24px;
    text-align: center;
  }
  .refresh{
    color: var(--semantic-color-content-interactive-primary-default);
    float: right;
  }

  .overview-tabs{
    padding: 0em 3.5em 1em 2.5em;

    .content-box-flex{
      width: 100%;
      height: 200px;
      border: none;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
    }
    .content-box-flex > div{
      width: 30px;
      flex: 1 1 auto;
      -webkit-flex: 1 1 auto;
      transition: width 0.7s ease-out;	
      -webkit-transition: width 0.7s ease-out;	
    }
    .container {
      width: '60%';
      padding: 0em 3em 0em 3em;
    }
    .key{
      float: left;
      min-height: 2em;
      min-width: 20em;
      color: #939393;
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 16px;
    }
    .value{
      float: right;
      min-height: 2em;
      min-width: 10em;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      letter-spacing: 0.2px;
      line-height: 15px;
    }
  }

  .overview-container{
    padding: 0em 3.5em 1em 1.75em;
  }
  
}