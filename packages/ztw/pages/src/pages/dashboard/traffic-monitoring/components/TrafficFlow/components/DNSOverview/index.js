// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import FlipButton from 'components/flipButton';

import { DNS_TABLE_CONFIGS } from 'ducks/trafficMonitoring/constants';
import ConfigTable from 'components/configTable';
import DNSTrend from './DNSTrend';

import './index.scss';

const getDnsTableData = (dnsTableData) => {
  const tableData = dnsTableData.map((row) => {
    return {
      ACTIONS: row.ACTIONS,
      BOTH_REQ_RESP_ALLOW: row.BOTH_REQ_RESP_ALLOW,
      EITHER_REQ_RESP_BLOCK: row.EITHER_REQ_RESP_BLOCK,
      EITHER_REQ_RESP_REDIRECT_NO_BLOCK: row.EITHER_REQ_RESP_REDIRECT_NO_BLOCK,
      isReadOnly: false,
      isDeletable: false,
      isEditable: false,
    };
  });
  return tableData;
};

export function DNSOverview(props) {
  const {
    showOverview,
    dnsmonitortabledata,
    showTotalTrafficSection,
    actions,
    t,
  } = props;

  if (showOverview) return null;
  return (
    <Loading {...props}>
      <ServerError {...props}>
        <div className="section-holder">
          <div className="section">
            <div>
              <span className="section-flip">
                <FlipButton
                  label={t('')}
                  clickCallback={actions.toggleTotalTrafficSec}
                  show={showTotalTrafficSection} />
              </span>
              <span className="section-header">
                {t('DNS_MONITOR')}
              </span>
            </div>
            <div className="section-table">
              {/* <DNSTable tableData={dnsmonitortabledata} {...props} /> */}
              <ConfigTable
                {...DNS_TABLE_CONFIGS}
                data={getDnsTableData(dnsmonitortabledata)} />
            </div>
          </div>
          <DNSTrend {...props} />
        </div>
      </ServerError>
    </Loading>
  );
}

DNSOverview.propTypes = {
  t: PropTypes.func,
  showOverview: PropTypes.bool,
  dnsmonitortabledata: PropTypes.arrayOf(PropTypes.shape()),
  showTotalTrafficSection: PropTypes.bool,
  load: PropTypes.func,
  actions: PropTypes.shape({
    load: PropTypes.func,
  }),
};

DNSOverview.defaultProps = {
  t: (str) => str,
  showOverview: false,
  dnsmonitortabledata: [],
  showTotalTrafficSection: true,
  load: noop,
  actions: {},
};

export default (withTranslation()(DNSOverview));
