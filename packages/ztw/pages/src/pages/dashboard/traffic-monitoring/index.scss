@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
.dashboard {
  // padding: 19px 25px;

  .dashboard-row{
    margin: 30px 0;
  }

  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }

  .table-content {
    padding-bottom: 90px;
    border-radius: 5px;
    // background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
  }
}
.entity-dropdown-container {
  .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .filter-icon-disabled {
    height: 15px;
    padding-right: 5px;
    color: $grey21;
  }
  .filter-icon-branch {
    color: #A940FF;
    height: 15px;
    padding-right: 5px;
  }
  label {
    color:  var(--semantic-color-content-base-primary);
    span {
      margin-left: 0;
    }
  }
}
.trafficFlow {
  display: none;
}
.hideVisibility {  visibility: hidden; }

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

.traffic-monitoring-table {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: 300px;
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}
}