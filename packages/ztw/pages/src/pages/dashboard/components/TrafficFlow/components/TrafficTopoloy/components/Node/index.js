import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export function Child({
  show,
  position,
  container,
  data,
}) {
  if (!show) return null;
  return (
    <div style={{ float: position }}>
      <span className={`${['01', '03'].includes(container) ? 'dotDarkGrey' : 'hide'}`}>{data}</span>
    </div>
  );
}

export function Node({
  nodeClass,
  data,
  family,
  leftChild,
  rightChild,
}) {
  return (
    <div className={nodeClass}>
      {data.map((ele) => (
        <div className={`${family ? ele.class : 'hideVisibility'}`} key={ele.index} id={ele.index}>
          {ele.childNodes.map((child) => (
            <div key={child.value}>
              {/* Subnets Child */}
              <Child show={leftChild} position="left" container={ele.container} data="2" />
              <div className={ele.childClass}>
                <FontAwesomeIcon icon={child.icon} style={{ color: 'grey' }} className="fontStyle" />
                {child.value}
              </div>
              {/* Service Child */}
              <Child show={rightChild} position="right" container={ele.container} data="3" />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}

Node.propTypes = {
  nodeClass: PropTypes.string,
  data: PropTypes.arrayOf(PropTypes.shape({})),
  family: PropTypes.string,
  leftChild: PropTypes.bool,
  rightChild: PropTypes.bool,
};

Node.defaultProps = {
  nodeClass: '',
  data: [{
    childNodes: [],
  }],
  family: '',
  leftChild: false,
  rightChild: false,
};

Child.propTypes = {
  show: PropTypes.bool,
  position: PropTypes.string,
  container: PropTypes.string,
  data: PropTypes.string,
};

Child.defaultProps = {
  show: false,
  position: '',
  container: '',
  data: '',
};

export default Node;
