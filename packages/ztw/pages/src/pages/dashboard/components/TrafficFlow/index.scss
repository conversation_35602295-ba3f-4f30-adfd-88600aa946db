@import 'scss/mixins.scss';
@import 'scss/colors.scss';
.ec-traffic {
  position: relative;
  background: $white;
  height: 100%;
}
.ec-traffic-header{
  height: 42px;	width: 100%;	background-color: var(--semantic-color-content-interactive-primary-default);
}
.ec-traffic-header-content{
  padding: 10px; height: 16px;	min-width: 20em;	color: var(--semantic-color-surface-base-primary);
  font-size: 13px;	font-weight: 500;	line-height: 16px;
}
.ec-traffic-topology{
  position: fixed;
  width: 100%;
  height: 54em;
  overflow: auto;
}