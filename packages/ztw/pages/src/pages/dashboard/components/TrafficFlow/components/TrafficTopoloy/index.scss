@import 'scss/mixins.scss';
@import 'scss/colors.scss';
// html,
// body {
//   width: 100%;
//   padding: 0;
//   margin: 0;
// }

.traffic-content{
  width: 100em;

  .fontStyle{
    height: 22px;
    width: 28px;
    padding: 2px;
  }

  .subnet {
    width: 14em;
    margin-left: 4.2em;
  }
  .service {
    width: 14em;
  }

  .ec-left {
    float:left;
    margin-top: 1em;
    z-index: 1;
    .dotDarkGrey .dotGrey {
      padding: 0.3em 0.3em 0.3em 0.7em;
    }
  }
  
  .ec-right {
    float:right;
    margin-top: 1em;
    z-index: 1;
    margin-left: 0.5em;
    .dotDarkGrey .dotGrey {
      padding: 0.3em 0.3em 0.3em 0.7em;
    }
  }
  .dotDarkGrey {
    height: 25px;
    width: 25px;
    background-color:  var(--semantic-color-content-base-primary);
    border-radius: 50%;
    display: inline-block;
    color: var(--semantic-color-surface-base-primary);
    padding: 0.5em;
    box-shadow: 0 0 2px 0 rgba(30,31,34,0.09);
  }

  .dotGrey {
    height: 25px;
    width: 25px;
    border: 1px solid #D6D7DB;
    background-color: #F5F6F7;
    border-radius: 50%;
    display: inline-block;
    color: #000000;
    padding: 0.5em;
  }

}
.traffic-path-root svg {
  position: fixed;
}
svg .traffic-path-root:hover path {
  stroke: #0082E0;
}

// svg #0010:hover {
//   stroke: #0082E0;
// }

.nodes-holder{
  top: 1em;
}
.node-source {
  top: 1em;
  z-index: 1;
  position: relative;
  float: left;
}
.node-section {
  top: 1em;
  margin-left: 3em;
  z-index: 1;
  float: left;
  position: relative;
}

.node-container{
  color: white;
  text-align: center;
  padding: 10px;

  margin-left: 1em;
  margin-bottom: 2em;
  position: static;
  left: 15em;
}
.node-edge-connector{
  position: sticky;
  width: 10em;
  color: white;
  text-align: center;
  padding: 10px;
  margin-left: 0.5em;
  margin-top: 0.5em;
}

.node {
  box-sizing: border-box;	
  max-height: 19.563em;
  width: 13.063em;	
  border: 1px solid #D6D7DB;	
  border-radius: 5px;
  overflow: auto;
}
.node-content {
  height: 2.750em;	width: 11.25em;	border-radius: 5px;	background-color: var(--semantic-color-surface-base-primary);	color: var(--semantic-color-content-base-primary);  box-shadow: 0 0 4px 0 rgba(42,44,48,0.24);
}
.node-content-max {
  display: inline-block;
  width: 9em;
  height: 2.750em; border-radius: 5px;	background-color: var(--semantic-color-surface-base-primary);	color: var(--semantic-color-content-base-primary);  box-shadow: 0 0 4px 0 rgba(42,44,48,0.24);
}
.nodeXs {
  border: 2px solid #D6D7DB;
  padding: 10px;
  border-radius: 10px;
  color: #D6D7DB;
  height: 20px;
}
.edge-connector-node {
  display: inline-block;
  position: relative;
  height: 2.750em;	width: 170px;	border-radius: 5px;	background-color: var(--semantic-color-content-interactive-primary-default);	box-shadow: 0 2px 8px 0 rgba(0,69,112,0.2);
}
