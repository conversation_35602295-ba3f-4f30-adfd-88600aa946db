/* eslint-disable react/jsx-indent */
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import TrafficMenu from './components/TrafficMenu';
import TrafficInfo from './components/TrafficInfo';
import TrafficTopoloy from './components/TrafficTopoloy';

import './index.scss';

export function TrafficFlow({
  showTrafficFlow,
  ecTrafficData,
  toggleFamily,
  t,
  incomingTraffic,
  outgoingTraffic,
}) {
  if (!showTrafficFlow) return null;
  return (
    <div className="ec-traffic">
      <div className="ec-traffic-header">
        <div className="ec-traffic-header-content">
          {t('CLOUD_CONNECTOR_TRAFFIC_FLOW')}
        </div>
      </div>
      <TrafficMenu />
      <TrafficInfo />
      <div className="ec-traffic-topology">
        <TrafficTopoloy
          ecTrafficData={ecTrafficData}
          toggleFamily={toggleFamily}
          incomingTraffic={incomingTraffic}
          outgoingTraffic={outgoingTraffic} />
      </div>
    </div>
  );
}

TrafficFlow.propTypes = {
  showTrafficFlow: PropTypes.bool,
  ecTrafficData: PropTypes.shape(),
  toggleFamily: PropTypes.func,
  t: PropTypes.func,
  incomingTraffic: PropTypes.bool,
  outgoingTraffic: PropTypes.bool,
};

TrafficFlow.defaultProps = {
  showTrafficFlow: false,
  ecTrafficData: {},
  toggleFamily: (str) => str,
  t: (str) => str,
  incomingTraffic: false,
  outgoingTraffic: false,
};

export default withTranslation()(TrafficFlow);
