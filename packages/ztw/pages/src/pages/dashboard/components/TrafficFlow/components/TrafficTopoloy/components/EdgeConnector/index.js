import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

export function EdgeConnector({
  edgeconnector,
  toggleFamily,
  incomingTraffic,
  outgoingTraffic,
  t,
}) {
  return (
    <div className="node-section">
      {/* Edgeconnector => Subnets - Left */}
      <div className="ec-left" onClick={() => toggleFamily('incomingTraffic')} role="presentation">
        <span className={`${incomingTraffic ? 'dotDarkGrey' : 'dotGrey'}`}>{edgeconnector.subnets}</span>
      </div>
      <div className={edgeconnector.class} key={edgeconnector.index} id={edgeconnector.index}>{t('CLOUD_CONNECTOR')}</div>
      {/* Edgeconnector => Service - Right */}
      <div style={{ float: 'right' }} className="ec-right" onClick={() => toggleFamily('outgoingTraffic')} role="presentation">
        <span className={`${outgoingTraffic ? 'dotDarkGrey' : 'dotGrey'}`}>{edgeconnector.service}</span>
      </div>
    </div>
  );
}

EdgeConnector.propTypes = {
  edgeconnector: PropTypes.shape({}),
  toggleFamily: PropTypes.func,
  incomingTraffic: PropTypes.bool,
  outgoingTraffic: PropTypes.bool,
  t: PropTypes.func,
};

EdgeConnector.defaultProps = {
  edgeconnector: {},
  toggleFamily: (str) => str,
  incomingTraffic: false,
  outgoingTraffic: false,
  t: (str) => str,
};

export default withTranslation()(EdgeConnector);
