import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import {
  Table,
  TableHeader,
} from 'components/table';

class DeviceLogsTable extends PureComponent {
  static propTypes = {
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
  };

  static defaultProps = {
    tableData: [],
    t: (str) => str,
  };

  render() {
    const {
      tableData,
      t,
    } = this.props;

    const columns = [
      {
        Header: <TableHeader title={t('EVENT_TIME')} />,
        accessor: 'eventTime',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('CC_STATUS')} />,
        accessor: 'ecStatus',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('LOCATION')} />,
        accessor: 'location',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('CLOUD_CONNECTOR_GROUP')} />,
        accessor: 'ecGroup',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('CC_VERSION')} />,
        accessor: 'ecVersion',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('CLOUD')} />,
        accessor: 'cloud',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('HYPERVISOR_VERSION')} />,
        accessor: 'hypervisorVersion',
        sortable: true,
        Cell: (row) => row.value,
      },
    ];

    return (
      <Table
        data={tableData}
        columns={columns} />
    );
  }
}

export default withTranslation()(DeviceLogsTable);
