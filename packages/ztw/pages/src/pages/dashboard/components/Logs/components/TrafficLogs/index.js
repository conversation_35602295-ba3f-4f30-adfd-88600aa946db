import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import TrafficLogsTable from './TrafficLogsTable';
import Filters from './Filters';

class DeviceLogs extends PureComponent {
  static propTypes = {
    showTable: PropTypes.bool,
  };

  static defaultProps = {
    showTable: false,
  };

  render() {
    const {
      showTable,
    } = this.props;

    if (!showTable) return null;

    return (
      <>
        <Filters {...this.props} />
        <TrafficLogsTable {...this.props} />
      </>
    );
  }
}

export default withTranslation()(DeviceLogs);
