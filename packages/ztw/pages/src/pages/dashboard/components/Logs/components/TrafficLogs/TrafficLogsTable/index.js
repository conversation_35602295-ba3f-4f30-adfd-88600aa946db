import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import {
  Table,
  TableHeader,
} from 'components/table';

class TrafficLogsTable extends PureComponent {
  static propTypes = {
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
  };

  static defaultProps = {
    tableData: [],
    t: (str) => str,
  };

  render() {
    const {
      tableData,
      t,
    } = this.props;

    const columns = [
      {
        Header: <TableHeader title={t('NAME')} />,
        accessor: 'name',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('FWD_TYPE')} />,
        accessor: 'fwdMethod',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('SOURCE_IP')} />,
        accessor: 'srcIp',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('TUNNEL_IP')} />,
        accessor: 'tunnelSrcIp',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('ZSCALER_IP')} />,
        accessor: 'zIp',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('DESTINATION_IP')} />,
        accessor: 'destIp',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('LOCATION')} />,
        accessor: 'location',
        sortable: true,
        Cell: (row) => row.value,
      },
      {
        Header: <TableHeader title={t('CLOUD_CONNECTOR_GROUP')} />,
        accessor: 'ecGroup',
        sortable: true,
        Cell: (row) => row.value,
      },
    ];

    return (
      <Table
        data={tableData}
        columns={columns} />
    );
  }
}

export default withTranslation()(TrafficLogsTable);
