@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.main-container {
  padding: 19px 25px;

  .container-row{
    margin: 30px 0;
  }
  .logs-header {
    padding-bottom: 1em;

    .logs-tab {
      float: left;
    }
    .logs-filters {
      float: right;
    }
  }
  .logs-filter-container {
    box-sizing: border-box;
    min-height: 11.750em;
    overflow: auto;
    width: 100%;
    border: 1px solid #D6D7DB;
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    margin-top: 2em;

    .left-pane {
      width:  450px;
      margin: 1em;
      float: left;
    }
    .right-pane {
      width: 300px;
      margin: 15px 150px 15px 15px;
      float: right;
    }
    .filter-container {
      width:  450px;
    }
    .filters-section {
      min-height: 10em;
    }
    .filter-container-right {
      width:  400px;
    }
    .filter-label {
      float: left;
      line-height: 2.5em;
      margin-bottom: 1.5em;
      min-width: 10em;
    }
    .filter-label-right {
      line-height: 2.5em;
      margin-bottom: 1.5em;
      float:left;
    }

    .filter-dropdown-left {
      width: 20em;
      float: right;
      margin-bottom: 1.5em;

      .drop-down-selected-value {
        border-radius: 5px;
        background-color: #F5F6F7;
      }
    }
    .buttons {
      width: 20em;
      float: right;
      margin: 0em 0em 1.5em 0em;
    }
    .clear-button {
      height: 1em;
      width: 8em;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 13px;
      line-height: 15px;
      border: none;
      padding-left: 2em;
    }
  }
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}

