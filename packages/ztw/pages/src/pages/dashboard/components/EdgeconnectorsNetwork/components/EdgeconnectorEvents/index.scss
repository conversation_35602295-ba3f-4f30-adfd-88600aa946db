@import 'scss/mixins.scss';
@import 'scss/colors.scss';
.ec-network {
  position: relative;
  background: $white;
  min-height: 280px;
}
.dull {
  fill-opacity: 0.5;
}
.events-container{
  margin: 0;
 font-size: 13px;
 width: 1000px;
}
#container {
 -webkit-box-sizing: border-box;
 -moz-box-sizing: border-box;
 box-sizing: border-box;
 padding: 10px;
 width: 800px;
 height: 800px;
 background-color: var(--semantic-color-surface-base-primary);
}
.line {
 box-sizing: border-box;
 border: 0.05em solid #D6D7DB;
//  margin-bottom: 20px;
}
.recharts-xAxis {
 stroke: #D6D7DB;
 stroke-width: 1;
}
.shadow {
 -webkit-filter: drop-shadow( 3px 3px 2px rgba(0, 0, 0, .7));
 filter: drop-shadow( 3px 3px 2px rgba(0, 0, 0, .7));
}