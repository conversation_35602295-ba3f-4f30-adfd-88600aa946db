import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  Brush,
} from 'recharts';
import './index.scss';

export function CustomizedDot(props) {
  const {
    cx, cy, payload,
  } = props;
        
  switch (payload.info) {
  case 'reboot': {
    return (
      <svg x={cx - 5} y={cy - 33} width={40} height={90} fill="#EC564F" viewBox="0 0 500 500" className="shadow">
        <circle cx="100" cy="100" r="100" />
      </svg>
    );
  }
  case 'restart': {
    return (
      <svg x={cx - 5} y={cy - 33} width={40} height={90} fill="#17819b" viewBox="0 0 500 500" className="shadow">
        <circle cx="100" cy="100" r="100" />
      </svg>
    );
  }
  case 'active': {
    return (
      <svg x={cx - 5} y={cy - 33} width={40} height={90} fill="#6AA920" viewBox="0 0 500 500" className="shadow">
        <circle cx="100" cy="100" r="100" />
      </svg>
    );
  }
  case 'registered': {
    return (
      <svg x={cx - 5} y={cy - 33} width={40} height={90} fill="#FF8620" viewBox="0 0 500 500" className="shadow">
        <circle cx="100" cy="100" r="100" />
      </svg>
    );
  }
  case 'provisioned': {
    return (
      <svg x={cx - 5} y={cy - 33} width={40} height={90} fill="var(--semantic-color-content-interactive-primary-default)" viewBox="0 0 500 500" className="shadow">
        <circle cx="100" cy="100" r="100" />
      </svg>
    );
  }
  default:
    return null;
  }
}

export class EdgeconnectorEvents extends PureComponent {
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({})),
  };

  static defaultProps = {
    data: [],
  };

  render() {
    const { data } = this.props;
    return (
      <div style={{ width: '100%' }}>
        <hr className="line"></hr>
        <LineChart width={document.documentElement.offsetWidth - 150} height={100} data={data}>
          <XAxis dataKey="name" />
          <Line type="monotone" dataKey="pv" stroke="grey" strokeDasharray="0.75 5" dot={<CustomizedDot />} />
          <Brush dataKey="name" className="dull" height={30} strokeWidth="0.2" stroke="#c7edfc" y={35} />
        </LineChart>
      </div>
    );
  }
}

export default EdgeconnectorEvents;
