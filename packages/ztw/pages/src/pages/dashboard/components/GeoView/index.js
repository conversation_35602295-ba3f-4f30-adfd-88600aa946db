import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { LeafletMap } from 'components/worldMap';
import './index.scss';

export class GeoView extends PureComponent {
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({})),
    actions: PropTypes.shape(),
    cloudConnectorData: PropTypes.shape(),
    showConnectorToolTip: PropTypes.bool,
    showTrafficTooltip: PropTypes.bool,
    isZeroTrustGateway: PropTypes.bool,
  };

  static defaultProps = {
    data: [],
    actions: {},
    cloudConnectorData: {},
    showConnectorToolTip: false,
    showTrafficTooltip: false,
    isZeroTrustGateway: false,
  };

  render() {
    const {
      data, actions, showConnectorToolTip,
      showTrafficTooltip, cloudConnectorData, isZeroTrustGateway,
    } = this.props;
    return (
      <div className="ec-network">
        <LeafletMap
          data={data}
          legendTitle="Data Centers"
          legendDescription="Cloud Connectors"
          actions={actions}
          cloudConnectorData={cloudConnectorData}
          showConnectorToolTip={showConnectorToolTip}
          isZeroTrustGateway={isZeroTrustGateway}
          showTrafficTooltip={showTrafficTooltip} />
      </div>
    );
  }
}

export default GeoView;
