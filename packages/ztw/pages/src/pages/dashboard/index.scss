@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
.dashboard {
  padding: 20px 40px 20px 20px;

  .dashboard-row{
    margin: 30px 0;
    .app-table-container {
      margin-bottom: 50px;
      max-height: 38em;
      overflow: scroll;
    }
  }
  .dual-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    .page-title {
      padding: 0 24px;
    }

    .left-section {
      white-space: nowrap;
      span {
        color:  var(--semantic-color-content-base-primary);
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0.2px;
        line-height: 24px;
      }
    }
    .right-section {
      padding-right: 20px;
      vertical-align: middle;
      span {
        color: var(--semantic-color-content-interactive-primary-default);
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0;
        cursor: pointer;
      }
    }
  }
  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;      
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }
  .entity-dropdown-container{  
    .filter-icon-gcp {
      height:  20px;
      margin-right: 5px;
      color: $grey21;
      margin-bottom:  -5px;
    }
    label {
      display: flex;
    }
  }
}
.trafficFlow {
  display: none;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
}
.hideVisibility {  visibility: hidden; }
.filter-icon-disabled {
  height: 15px;
  padding-right: 5px;
  color: $grey21;
}

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}

.totalTxn {
  color:  var(--semantic-color-content-base-primary);
}
}