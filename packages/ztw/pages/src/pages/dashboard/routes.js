import { BASE_LAYOUT } from 'config';
import Dashboard from './index';
import Logs from './components/Logs';
import ConnectorMonitoring from './connector-monitoring';
import TrafficMonitoring from './traffic-monitoring';
import ZeroTrustGateway from './zero-trust-gateway';

export const routes = [
  {
    isNested: false,
    path: '/dashboard/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/dashboard/connector-monitoring',
    name: 'Dashboard',
    component: ConnectorMonitoring,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/dashboard/traffic-monitoring',
    name: 'Dashboard',
    component: TrafficMonitoring,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/dashboard/zero-trust-gateway',
    name: 'Dashboard',
    component: ZeroTrustGateway,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/dashboard/logs',
    name: 'Logs',
    component: Logs,
    layout: `${BASE_LAYOUT}`,
  },
];

export default routes;
