// @flow
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import mixpanel from 'mixpanel-browser';
import { SubHeader } from 'components/label';
import NavTabs from 'components/navTabs';
import { BASE_LAYOUT } from 'config';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { load, handleTrafficFlow, toggleFamily } from 'ducks/dashboard';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import * as loginSelectors from 'ducks/login/selectors';
import * as dashboardSelector from 'ducks/dashboard/selectors';
import Donut from 'components/pieChart/Donut';
import { hasBsku, hasCsku } from 'utils/helpers';
import EdgeconnectorsNetwork from './components/EdgeconnectorsNetwork';
import TrafficFlow from './components/TrafficFlow';

import './index.scss';

const usernameFS = localStorage.getItem('username');

mixpanel.init('522a60a836ecd7834120be8d634f0659');
mixpanel.track('Dashboard');
// temp - need to change
mixpanel.identify('12148');
mixpanel.people.set({
  $email: usernameFS, // only special properties need the $
  'Sign up date': new Date(), // Send dates in ISO timestamp format (e.g. "2020-01-02T21:07:03Z")
  USER_ID: usernameFS, // use human-readable names
  name: usernameFS,
  credits: 150, // ...or numbers
  adminType: null,
  ThemePreference: 'Default Theme',
});

export class Dashboard extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    totalEcData: PropTypes.shape({}),
    activeHealthData: PropTypes.shape({}),
    ecNwData: PropTypes.arrayOf(PropTypes.shape({})),
    t: PropTypes.func,
    showTrafficFlow: PropTypes.bool,
    incomingTraffic: PropTypes.bool,
    outgoingTraffic: PropTypes.bool,
    ecTrafficData: PropTypes.shape({}),
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    actions: {
      load: noop,
    },
    totalEcData: {},
    activeHealthData: {},
    ecNwData: [],
    t: (str) => str,
    showTrafficFlow: false,
    incomingTraffic: false,
    outgoingTraffic: false,
    ecTrafficData: {},
    accessPermissions: {},
    accessSubscriptions: [],
  };

  componentDidMount() {
    const { actions } = this.props;
    actions.load();
  }

  render() {
    const {
      t,
      totalEcData,
      activeHealthData,
      ecNwData,
      showTrafficFlow,
      actions,
      ecTrafficData,
      incomingTraffic,
      outgoingTraffic,
      accessPermissions,
      accessSubscriptions,
    } = this.props;

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }
    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    return (
      <Loading {...this.props}>
        <ServerError {...this.props}>
          <div className={`dashboard ${showTrafficFlow ? 'hide' : 'unhide'}`}>
            <div className="configuration-nav-tab">
              <NavTabs
                tabConfiguration={[{
                  id: 'dashboard',
                  title: 'Dashboard',
                  to: `${BASE_LAYOUT}/dashboard/dashboard`,
                },
                {
                  id: 'logs',
                  title: 'LOGS',
                  to: `${BASE_LAYOUT}/dashboard/logs`,
                },
                {
                  id: 'health',
                  title: 'Health',
                  to: `${BASE_LAYOUT}/dashboard/health`,
                }]} />
            </div>
            <div className="pie-container">
              <Donut data={totalEcData} displayName={t('TOTAL_CC_DEPLOYED')} />
              <Donut data={activeHealthData} displayName={t('HEALTH_MONITORING_CC')} />
            </div>
            <div className="dashboard-row map-content">
              <SubHeader title={t('CC_NW')} />
              <EdgeconnectorsNetwork data={ecNwData} actions={actions} />
            </div>
          </div>
          <TrafficFlow
            showTrafficFlow={showTrafficFlow}
            ecTrafficData={ecTrafficData}
            toggleFamily={actions.toggleFamily}
            incomingTraffic={incomingTraffic}
            outgoingTraffic={outgoingTraffic} />
        </ServerError>
      </Loading>
    );
  }
}

const mapStateToProps = (state) => ({
  ...dashboardSelector.default(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),

});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load,
    handleTrafficFlow,
    toggleFamily,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Dashboard));
