import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';

import { AppHeader } from 'components/header';

export class Help extends React.Component {
  static propTypes = {
    classes: PropTypes.shape({}),
    title: PropTypes.string,
    t: PropTypes.func,
  };

  static defaultProps = {
    classes: {},
    title: 'Help',
    t: (str) => str,
  };

  state = { };

  render() {
    const { title, t } = this.props;
    return (
      <>
        <div>
          <AppHeader title={<Trans>{title}</Trans>} />
        </div>
        <div>{t('Help Content')}</div>
      </>
    );
  }
}

export default withTranslation()(Help);
