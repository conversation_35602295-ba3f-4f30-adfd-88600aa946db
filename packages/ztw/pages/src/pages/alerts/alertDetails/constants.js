import PropTypes from 'prop-types';

const alertPropTypes = PropTypes.shape({
  id: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  ruleName: PropTypes.string,
  type: PropTypes.string,
  enabled: PropTypes.bool,
  endedOn: PropTypes.number,
  severity: PropTypes.number,
  startedOn: PropTypes.number,
  impactedApp: PropTypes.string,
  impactedGeo: PropTypes.arrayOf(PropTypes.shape({})),
  impactedDevices: PropTypes.arrayOf(PropTypes.shape({})),
});

export const ALERT_DETAILS_DEVICES_FILTER = {
  id: 'alertDerailsDevicesFilter',
  dataSrc: '/ec/api/v1/devices',
  name: 'device_id',
};

export const ALERT_DETAILS_DEPARTMENTS_FILTER = {
  id: 'alertDetailsDepartmentsFilter',
  dataSrc: '/ec/api/v1/departments',
  name: 'dept_id',
};

export const ALERT_DETAILS_LOCATIONS_FILTER = {
  id: 'alertDetailsLocationsFilter',
  dataSrc: '/ec/api/v1/locations',
  name: 'loc_id',
};

export const ALERT_DETAILS_GEOLOCATIONS_FILTER = {
  id: 'alertDetailsGeolocationsFilter',
  dataSrc: '/ec/api/v1/departments',
  name: 'geo_id',
};

export const ALERT_DETAILS_DEVICE_OS_VERSIONS_FILTER = {
  id: 'alertDetailsDeviceOsVersionsFilter',
  dataSrc: '/ec/api/v1/departments',
  name: 'device_version',
};

export const filters = [
  ALERT_DETAILS_DEVICES_FILTER,
  ALERT_DETAILS_DEPARTMENTS_FILTER,
  ALERT_DETAILS_LOCATIONS_FILTER,
  ALERT_DETAILS_GEOLOCATIONS_FILTER,
  ALERT_DETAILS_DEVICE_OS_VERSIONS_FILTER,
];

export default {
  alertPropTypes,
};
