// @import 'scss/widgets.scss';
@import 'scss/pages.scss';
@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.myprofile-main-container{
  padding: 24px;
}

.myprofile-main-container {
    .add-custom-app-form.profile {
        position: relative;
    }
    .container {
        padding: 0;
        padding-left: 25px;
    }
    label span {
        margin-left: 0;
    }
    .rdg-menu-editor .ui-sortable {
        padding-top: 0; 
    }
    .page-title {
        padding-left: 0;
        padding-top: 0;  
    }
    .source-ip-groups {	
        height: 24px;
        width: 147px;
        color: var(--semantic-color-content-base-primary);
        font-size: 24px;
        font-weight: 500;
        line-height: 24px;
    }
    .form-section-label {
        padding: 8px 0px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color:  var(--semantic-color-content-base-primary);
        font-size: 13px;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        width: 100%;
    }
    .form-sections-container {
        padding-bottom: 8px;
        &.profile {
            position: relative;
        }
  
        .form-section {
            margin: 0;
            margin-bottom: 8px;
            background-color: var(--semantic-color-surface-base-primary);
            border: 1px solid var(--semantic-color-border-base-primary);
            .disabled-input {
                color: var(--semantic-color-content-interactive-primary-disabled);
                border: none;
                cursor: default;
                padding: 9px 0;
                background: none;
            }
        }
    }
    .g-row {  
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
        padding: 12px 0px;
    
        &:first-child {
          .g-right{
            padding-left: 0;
          }
          .g-left {
            padding-right: 0;
          }
        }
        
        .g-fullwidth{
          padding: 12px 16px;
          width: 100%;
        }
    }

    .select-container {
        background: var(--semantic-color-background-pale);
        display: inline-block;
        outline: none;
        overflow: visible;
        position: relative;
        vertical-align: middle;
        width: 100%;
        border-radius: 8px;
        width: 280px;
        .select-selected-value {
            text-align: left;
        }
        button.select-selected-value {
            background: inherit;
            span {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .search-container {
            display: inline-block;
            vertical-align: middle;
            margin: 12px;
            background: var(--semantic-color-background-pale);
            border-radius: 8px;
        }
    }
    .dropdown-button {
        color: var(--semantic-color-content-status-info-primary);
        cursor: pointer;
        display: block;
        height: 32px;
        padding: 9px 8px;
        text-align: left;
        width: 100%;
        border-radius: 8px;
    }
    .dropdown-button-label {
        display: inline-block;
        font-size: 13px;
        line-height: 15px;
        vertical-align: top;
        width: calc(100% - 20px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .dropdown-icon {
        display: inline-block;
        font-size: 16px !important;
        text-align: center;
        padding-left: 10px;
        vertical-align: top;
        width: 20px;
    }

    .select-list {
        min-width: 280px;
        background-color: var(--semantic-color-surface-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        // display: none;
        line-height: normal;
        // position: fixed;
        max-width: 375px;
        // box-shadow: 0 0 8px 0 rgba(42, 44, 48, 0.25);
        z-index: 100;
        max-height: 300px;
        overflow: auto;
    }
    .select-list-with-search  {
        min-width: 280px;
        background: var(--semantic-color-background-primary);     
        line-height: normal;
        // position: fixed;
        max-width: 348px;
        z-index: 100;
        max-height: 190px;
        overflow: auto;
    }
    .select-container-with-search {
        // position: absolute;
        min-width: 350px;
        background-color: var(--semantic-color-surface-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        line-height: normal;
        // position: fixed;
        // box-shadow: 0 0 8px 0 rgba(42, 44, 48, 0.25);
        z-index: 100;        
        .my-select-with-search__control {
            background-color: var(--semantic-color-background-primary);
            border: 1px solid var(--semantic-color-border-base-primary);
            border-radius: 8px;
            max-height: 32px;
        }
        .my-select-with-search__value-container {
            max-height: 32px;
        }
        .my-select-with-search__placeholder {
            color: var(--semantic-color-content-base-primary);                      
        }
        .my-select-with-search__dropdown-indicator {
            color: var(--semantic-color-content-base-primary);                      
        }
        .my-select-with-search__menu {
            margin-top: 2px;
            .my-select-with-search__menu-list {
                max-height: 200px;
            }
        }
        .react-select__value-container {
            max-height: 20px;
            align-self: baseline;
          }
    }
    
    .select-list-item.active {
        background: var(--semantic-color-content-interactive-primary-default) !important;
        color: var(--semantic-color-surface-base-primary)!important;
        border-radius: 0;
    }
    .select-list-item {
        color: var(--semantic-color-content-status-info-primary);
        cursor: pointer;
        display: block;
        font-size: 13px;
        max-width: 100%;
        line-height: 12px;
        min-width: 100%;
        padding: 8px 16px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        button {
            text-align: left;
            padding: 0!important;
        }     
        
    }
    .radio-buttons {
        width: 190px;
    }
    .radio-button.checked-true label {
        background: var(--semantic-color-content-interactive-primary-default);
        color: var(--semantic-color-background-primary);
    }
    .dialog-footer .dialog-footer-left .submit:disabled {  
        background: #f7f9fa;
        border: 1px solid;
        border-color: #6e6e6e;
        color: #6e6e6e;
        box-shadow: none;
        cursor:default;
        opacity: 0.6;
      }
    .dialog-footer .dialog-footer-left .cancel:disabled {  
      background: none;
      color: #6e6e6e;
      cursor:default;
    }
}
}