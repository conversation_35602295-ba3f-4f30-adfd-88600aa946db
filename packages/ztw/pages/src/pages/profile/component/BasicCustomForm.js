import React from 'react';
import { reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { FormSectionLabel } from 'components/label';
import Loading from 'components/spinner/Loading';
import * as constants from 'ducks/login/constants';
import PersistentStorage, { LS_LOCALE } from 'utils/persistentStorage';
import * as selectors from 'ducks/profile/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import * as adminManagementSelectors from 'ducks/adminManagement/selectors';
import {
  saveForm,
  handleStartOver,
} from 'ducks/profile';

import ProfileFragment from './ProfileFragment';
import {
  UserName,
  Password,
  Language,
  TimeZone,
} from './GenericFilters';

export function BasicCustomAppForm(props) {
  const {
    actions, t, formData, handleSubmit, submitting, initialValues, loading, readOnlyMode,
    isOneIdentityEnabled, savingProfile,
  } = props;
  const {
    autoDashboardRefresh, locale, policyInformation, ccTimeZoneId,
  } = formData || {};
  const { cancelHandle, saveFormData } = actions;
  
  const onSubmit = (event) => {
    event.preventDefault();
    saveFormData();
  };

  const onCancel = (event) => {
    event.preventDefault();
    cancelHandle();
  };

  if (isEmpty(initialValues)) return '';

  const untouched = initialValues && locale
  && autoDashboardRefresh === initialValues.autoDashboardRefresh
  && initialValues.locale && locale.value === initialValues.locale.value
  && policyInformation === initialValues.policyInformation
  && ccTimeZoneId.value === initialValues.ccTimeZoneId.value;

  return (
    <Loading loading={savingProfile || loading}>
      <form onSubmit={handleSubmit(onSubmit)} className="add-custom-app-form profile">
        <div className="add-custom-app-form">
          <FormSectionLabel text={t('MY_PROFILE')} />
          <div className="form-sections-container">
            <div className="form-section">
              <UserName />
              {
                !isOneIdentityEnabled
                && <Password disabled={readOnlyMode} />
              }
              <Language {...props} />
              <TimeZone {...props} />
              <ProfileFragment {...props} disabled={readOnlyMode} />
            </div>
            { !readOnlyMode && (
              <div className="dialog-footer">
                <div className="dialog-footer-left">
                  <button type="submit" disabled={untouched || submitting} className="submit">{t('SAVE')}</button>
                  <button type="button" disabled={untouched} className="cancel" onClick={onCancel}>{t('CANCEL')}</button>
                </div>
              </div>
            )}
          </div>
        </div>
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  loading: PropTypes.bool,
  readOnlyMode: PropTypes.bool,
  initialValues: PropTypes.shape({}),
  formData: PropTypes.shape({}),
  isOneIdentityEnabled: PropTypes.bool,
  savingProfile: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: null,
  },
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
  loading: false,
  readOnlyMode: false,
  formData: {},
  initialValues: {},
  isOneIdentityEnabled: false,
  savingProfile: false,
};

const BasicCustomForm = reduxForm({
  form: 'myProfile',
  enableReinitialize: true,
  //  validate: validatePassword,
})(BasicCustomAppForm);

const ProfileForm = connect(
  (state) => {
    const {
      autoDashboardRefresh,
      ccTimeZoneId,
      timeZones,
      policyInformation,
    } = (state.profile);
    if (ccTimeZoneId === '') return {};
    const timeZoneId = timeZones.find((x) => x.value === ccTimeZoneId);
    const locale = PersistentStorage.getItem(LS_LOCALE) || 'en-US';
 
    return ({
      initialValues: {
        autoDashboardRefresh,
        ccTimeZoneId: timeZoneId,
        policyInformation,
        locale: constants.DROPDOWN_LANG_DATA.find((x) => x.value === locale),
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: handleStartOver,
    saveFormData: saveForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...selectors.baseSelector(state),
  ...loginSelectors.baseSelector(state),
  formData: selectors.formValuesSelector(state),
  isOneIdentityEnabled: adminManagementSelectors.oneIdentityEnabledSelector(state),
});

export default connect(mapStateToProps, mapDispatchToProps)(ProfileForm);
