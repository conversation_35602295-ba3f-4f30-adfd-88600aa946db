import React from 'react';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { getFormValues } from 'redux-form';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { handlePolicyInformationChange } from 'ducks/profile';

import {
  
  // Language,
  // AutoDashboardRefresh,
  PolicyInformation,
  // TimeZone,
} from './GenericFilters';

export function ProfileFragment(props) {
  const { actions, formData, disabled } = props;
  const { policyInformation } = formData || {};

  const onChangePolicyInformation = (e, value) => {
    actions.handlePolicyInformationChange(value);
  };

  return (
    <>
      {/* <Language /> */}
      {/* <AutoDashboardRefresh /> */}
      <PolicyInformation
        value={policyInformation}
        disabled={disabled}
        onChange={onChangePolicyInformation} />
      {/* <TimeZone /> */}
    </>
  );
}

const mapStateToProps = (state) => ({
  formData: getFormValues('myProfile')(state) || 'DISABLE',
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handlePolicyInformationChange,
  }, dispatch);
  return {
    actions,
  };
};

ProfileFragment.propTypes = {
  disabled: PropTypes.bool,
  formData: PropTypes.shape({}),
  actions: PropTypes.shape({
    handlePolicyInformationChange: PropTypes.func,
  }),
};
  
ProfileFragment.defaultProps = {
  disabled: false,
  formData: {},
  actions: ({
    handleDashboardChange: null,
  }),
};

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(ProfileFragment));
