import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import { ToggleCheckBox } from 'components/ecToggle';

function autoDashboardRefresh({ t }) {
  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel text={t('AUTO_REFRESH_DASHBOARD')} tooltip={t('TOOLTIP_MY_PROFILE_AUTO_REFRESH_DASHBOARD')} />
        <Field
          id="autoDashboardRefresh"
          name="autoDashboardRefresh"
          styleClass="ec-toggle-checkbox"
          component={ToggleCheckBox} />
      </div>
    </div>
  );
}

autoDashboardRefresh.propTypes = {
  t: PropTypes.func,
};
  
autoDashboardRefresh.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(autoDashboardRefresh);
