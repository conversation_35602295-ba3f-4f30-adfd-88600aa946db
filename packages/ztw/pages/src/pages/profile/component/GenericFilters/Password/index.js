import React from 'react';
import PropTypes from 'prop-types';
import Button from 'components/button/Button';
import { useDispatch } from 'react-redux';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { togglePasswordExpiryModal } from 'ducks/passwordExpiry';

function UserName({ t, disabled }) {
  const tooltip = t('TOOLTIP_MY_PROFILE_PASSWORD');
  const dispatch = useDispatch();

  const onChangePassword = (event) => {
    event.preventDefault();
    dispatch(togglePasswordExpiryModal(true));
  };

  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel
          text={t('PASSWORD')}
          tooltip={tooltip} />
        <Button enable={!disabled} label={t('CHANGE_PASSWORD')} onActionCb={onChangePassword} />
      </div>
    </div>
  );
}

UserName.propTypes = {
  t: PropTypes.func,
  disabled: PropTypes.bool,
};
  
UserName.defaultProps = {
  t: (str) => str,
  disabled: false,
};

export default (withTranslation()(UserName));
