import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import DropDown from 'components/dropDown/Select';
import * as constants from 'ducks/login/constants';

function Language({ t }) {
  const tooltip = t('TOOLTIP_MY_PROFILE_LANGUAGE');

  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel
          text={t('LANGUAGE')}
          tooltip={tooltip} />
        <div className="language-selector">
          <div className="login-language-container">
            <Field
              id="locale"
              name="locale"
              props={{
                items: constants.DROPDOWN_LANG_DATA,
              }}
              component={DropDown} />
          </div>
        </div>
      </div>
    </div>
  );
}
        
Language.propTypes = {
  t: PropTypes.func,
};
          
Language.defaultProps = {
  t: (str) => str,
};
        
export default (withTranslation()(Language));
