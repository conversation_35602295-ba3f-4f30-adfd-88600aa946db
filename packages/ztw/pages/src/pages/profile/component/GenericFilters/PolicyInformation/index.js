import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';

function PolicyInformation({
  value, t, onChange, disabled,
}) {
  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel text={t('POLICY_INFORMATION')} tooltip={t('TOOLTIP_MY_PROFILE_POLICY_INFORMATION')} />
        <div className="dropdown-container">
          <ECRadioGroup
            id="policyInformation"
            name="policyInformation"
            styleClass="full-width"
            onChange={onChange}
            options={[
              {
                name: 'policyInformation', disabled, checked: value === 'ENABLE', value: 'ENABLE', label: t('ENABLE'),
              }, {
                name: 'policyInformation', disabled, checked: value === 'DISABLE', value: 'DISABLE', label: t('DISABLE'),
              },
            ]} />
        </div>
      </div>
    </div>
  );
}

PolicyInformation.propTypes = {
  t: PropTypes.func,
  value: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};
  
PolicyInformation.defaultProps = {
  t: (str) => str,
  value: 'DISABLE',
  onChange: (str) => str,
  disabled: false,
};

export default withTranslation()(PolicyInformation);
