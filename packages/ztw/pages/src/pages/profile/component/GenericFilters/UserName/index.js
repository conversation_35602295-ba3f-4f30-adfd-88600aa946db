import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';

function UserName({ t }) {
  const tooltip = t('TOOLTIP_MY_PROFILE_USER_DISPLAY_NAME');
  const username = useSelector((state) => loginSelectors.usernameSelector(state));
  
  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel
          text={t('USER_NAME')}
          tooltip={tooltip} />
        <p className="disabled-input">{username}</p>
      </div>
    </div>
  );
}

UserName.propTypes = {
  t: PropTypes.func,
};
  
UserName.defaultProps = {
  t: (str) => str,
};

export default (withTranslation()(UserName));
