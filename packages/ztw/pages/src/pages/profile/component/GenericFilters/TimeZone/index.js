import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { useSelector } from 'react-redux';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import DropDown from 'components/dropDown/SelectWithSearch';
import { orderBy } from 'utils/lodash';

function TimeZone({ t }) {
  const tooltip = t('TOOLTIP_MY_PROFILE_LANGUAGE');
  let { timeZones } = useSelector((state) => state.profile);
  timeZones = orderBy(timeZones.map((x) => ({ ...x, label: x.label.includes(':') ? x.label : t(x.label) })), 'label');

  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel
          text={t('TIMEZONE')}
          tooltip={tooltip} />
        <div className="timezone-selector">
          <div className="timezone-container">
            <Field
              id="ccTimeZoneId"
              name="ccTimeZoneId"
              props={{
                items: timeZones,
              }}
              component={DropDown} />
          </div>
        </div>
      </div>
    </div>
  );
}
        
TimeZone.propTypes = {
  t: PropTypes.func,
};
          
TimeZone.defaultProps = {
  t: (str) => str,
};
        
export default (withTranslation()(TimeZone));
