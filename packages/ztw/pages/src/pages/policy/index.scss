@import 'scss/colors.scss';

.ec-root-page {
.main-container-policies {
  padding: 19px 25px;
  .page-title {
    padding-left: 0px;
  }
  .table-container .content .cell-container {
    border-right: none;
    cursor: default;
  }
  .cc-group-tooltip-container {
    width: fit-content;
  }
  .__react_component_tooltip   {
    background: var(--semantic-color-background-primary);
    color:  var(--semantic-color-content-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    .multi-line {
      text-align: left;
    }
  }

  .delete-icon {
    cursor: pointer;
    text-align: center;
    width: 40px;
  }

  .view-icon {
    cursor: pointer;
    text-align: center;
    width: 40px;
  }
  
  .policy-table-cell-text {
    &.has-elipsis {
      &:hover{
        color: var(--semantic-color-content-interactive-primary-default);
        cursor: pointer;
      }
    }
  }
  .policy-table-cell-data {
    &.has-elipsis {
      &:hover{
        color: var(--semantic-color-content-interactive-primary-default);
        cursor: pointer;
      }
    }
  }

  .policy-table-criteria-item {
    color: $table-header-cell-text-color;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 20px;
    }
}

  .pencil-icon {
    cursor: pointer;
    text-align: center;
    width: 40px;
  }

  .rTooltip {
    display: block;
    position: fixed;
    letter-spacing: 0px;
    text-transform: none;
    right: auto;
    bottom: auto;
    transform: translateX(0%) translateY(-100%);    
    .rTooltip-container {
      white-space: break-spaces;
      height: fit-content;
      max-height: none;
      right: unset;
      bottom: unset;
    }
  }

  .policy-tips-text-container {
    padding: 16px;
    background: var(--semantic-color-background-primary);
    color: $form-input-label-color;
    margin-bottom: 16px;
    font-size: 13px;
    cursor: default;
    border-radius: 8px;
    min-height: 75px;

    .hidden {
        display: none;
    }
  }
  .policy-tips-text-title {
    font-weight: 700;
    margin-bottom: 8px;
  }
  .enable-disable-tips-container {
    line-height: 12px;
  }
  .disable-tips-success-text, .disable-tips-message-text {
    font-style: italic;
  }
  .tips-button.enable-tips-button {
    color: var(--semantic-color-content-interactive-primary-default);
    margin-right: 16px;
    cursor: pointer;
  }
  .tips-button.disable-tips-button {
    color:  var(--semantic-color-content-interactive-primary-disabled);
    cursor: pointer;
  }
  .config-table-container {
    .column-layout-config-container{
      border-left: 0;
    }
  }
}

.source-ip-groups {
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.source-ip-wrapper {
  .app-table-container {
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }
    }
  }
}

.traffic-policies-edit {
  &.modal-body{
    top: 20px;
  }
  .modal-content {
    max-height: none;
    margin-bottom: 150px;
    overflow: visible;
    .react-select__single-value{
      color: var(--semantic-color-content-base-primary);
    }
    .wan-selection {
      margin-left: 12px;
    }
    .tabs {      
      &-items {        
        div {
          margin-right: 5px;
        }
      }
    }
    .disabled-input {
      color:  var(--semantic-color-content-interactive-primary-disabled);
      cursor: not-allowed;
      margin-top: 4px;
    }
    .dialog-footer-left, .dialog-footer-right {
      display: inline-block;
      height: 100%;
      text-align: left;
      vertical-align: top;
      width: 75%;
    }
    .dialog-footer-right {
      text-align: right;
      width: 25%;
    }
    .firewall-control-dns-policy-edit {
      .form-sections-container {
        overflow: visible;
        .form-section {
          .g-row{
            justify-content: flex-start;
            .g-left {
              max-width: 285px;
              padding-right: 0;
            }
            .g-right {
              max-width: 285px;
              padding-right: 0;
              padding-left: 0;
              input {
                background-color: var(--semantic-color-background-primary);
              }
            }
          }
        }
        .form-input-rows, .form-tab-input-rows {
          background-color: var(--semantic-color-surface-base-primary);
          // box-shadow: 0 0 8px 0 rgba(42, 44, 48,  0.25);
          border-radius: 5px;
          width: 100%;
        }
        .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected {
          & .dropdown-search {
            background: var(--semantic-color-background-primary);
            & input {
              // border-radius: 8px;
              padding-left: 12px;
            }
          }
          & .dropdown-search-text-add-button {
            width: 200px
          }        
          & .dropdown-search-text-add-button-icon { 
            color: var(--semantic-color-content-interactive-primary-default);
            font-size: 12px;
            cursor: pointer;
          }
        }
        #countries.dropdown {
          width: 95%;
          .dropdown-selector .dropdown-lists .dropdown-unselected,
          .dropdown-selector .dropdown-lists .dropdown-selected {
            height: 396px;
            .selected-item,
            .unselected-items {
              height: 310px;
              .unselected {
                padding: 5px 18px 5px 16px;
                .container {
                  padding: 0 0 0 30px;
                }
              } 
            } 
          } 
        }
        #forwardingMethod, #networkTraffic {
          .react-select__menu {
            overflow: hidden;
            .react-select__menu-list {
              max-height: fit-content;
              min-height: fit-content;
              overflow: visible;
            }
          }
        }
        
      }
      .form-tab-group {
        color: var(--semantic-color-content-interactive-primary-default);
        border-bottom: 3px solid #e0e1e3;
        height: 26px;
        margin-bottom: 12px;
        .tabs {
          display: inline-block;
          position: relative;
          width: 100%;
          .tabs-items div {
            position: relative;
            .tabP,.tabPactive {
              font-size: 13px;
            }
            .highlighter {
              height: 3px;
              width: 100%;
              color: var(--semantic-color-content-base-primary);
              border-radius: 2.5px;
              padding-right: 10px;
              top: 23px;
              position: absolute;
            }
          }
        }
      }
    }
  }

  .center-logical-label {
    height: 25px;
    width: 4%;
    display: inline-block;
    line-height: 25px;
    margin: 10px;
    margin-bottom: 15px;    
    text-transform: uppercase;
    padding-left: 3px;
    background: var(--semantic-color-background-primary);
    border: 1px solid transparent;
    color: var(--semantic-color-content-base-primary);
    border-radius: 3px;
    &.no-background {
      background-color: transparent;
    }
    &.label-and {
      position: absolute;
      top: -20px;
      width: 36px;
      border-radius: 18px;
    }
  }

}

.firewall-control-dns-policy-edit {
  .dns-source {
    padding-top: 12px;
  }
}
.firewall-control-dns-policy-edit {
  .traffic-source {
    padding-top: 12px;
  }
}

.inline-position-relative {
  position: relative !important;
}

.grid-toolbar-right.display-inline-flex{
  .select-item.entity-dropdown {
    width: 200px; 
    margin-left: 1px;
    padding-top: 2px;
  }
}
#searchParameter, #forwardingMethod, #ruleAction {
  display: inline-block;
  min-width: 200px;
  padding-right: 4px;
  border: 1px solid var(--semantic-color-border-base-primary);
  .react-select__control {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0.3125rem; 
    background: var(--semantic-color-background-primary);            
    color: var(--semantic-color-content-base-primary);
    cursor: pointer;
    text-align: left;
    height: 5px;
    width: 100%;
    min-height: 33px;
  }
  .react-select__menu {
    z-index: 2;
    .react-select__menu-list{
      max-height: fit-content;
    }
    & {
      min-height: fit-content;
    }
  }
  .react-select__option {
    text-align: left;
  }
  .react-select__control {
    border: none;
    box-shadow: none;
  }
  .react-select__single-value{
    color: var(--semantic-color-content-base-primary);
  }
}

.modal-forwarding-method {
  #forwardingMethod {
    min-width: 100%;
    padding: 0;
    .react-select__control {
      background-color: var(--semantic-color-background-primary);
    }
  } 
}
}