import { faUser } from '@fortawesome/pro-solid-svg-icons';
import { BASE_LAYOUT } from 'config';
import DnsControl from './dns-control';
import TrafficForwarding from './traffic-forwarding-control';
import LogAndControlForwarding from './log-and-control-forwarding-control';
import EdgeDnsControl from './edge-dns-control';
import EdgeTrafficForwarding from './edge-traffic-forwarding-control';
import EdgeLogAndControlForwarding from './edge-log-and-control-forwarding-control';

export const routes = [
  {
    isNested: false,
    path: '/policy/cloud-connector-traffic-forwarding-control',
    name: null,
    icon: faUser,
    component: TrafficForwarding,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/policy/cloud-connector-log-and-control-forwarding-control',
    name: null,
    icon: faUser,
    component: LogAndControlForwarding,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/policy/cloud-connector-dns-control',
    name: null,
    icon: faUser,
    component: DnsControl,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/policy/edge-connector-traffic-forwarding-control',
    name: null,
    icon: faUser,
    component: EdgeTrafficForwarding,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/policy/edge-connector-log-and-control-forwarding-control',
    name: null,
    icon: faUser,
    component: EdgeLogAndControlForwarding,
    layout: `${BASE_LAYOUT}`,
  },
  {
    isNested: false,
    path: '/policy/edge-connector-dns-control',
    name: null,
    icon: faUser,
    component: EdgeDnsControl,
    layout: `${BASE_LAYOUT}`,
  },
];

export default routes;
