import React from 'react';
import { EntityDropdown } from 'components/entityDropdown';
import { POLICY_SEARCH_DATA } from 'config';

export function SearchDropdown(props) {
  return (
    <EntityDropdown
      {...props}
      className="search-parameter-entity-dropdown"
      data={POLICY_SEARCH_DATA.filter((x) => x.id !== 'ruleForwardMethod')}
      isSearchable={false} />
  );
}

export default SearchDropdown;
