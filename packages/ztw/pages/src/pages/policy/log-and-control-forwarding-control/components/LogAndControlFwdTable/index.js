import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { getReadOnly } from 'utils/helpers';
import { LOG_CONTROL_POLICIES_TABLE_CONFIGS } from 'ducks/logAndControlFwdPolicies/constants';

import * as loginSelectors from 'ducks/login/selectors';
import * as LogAndControlFwdPoliciesSelector from 'ducks/logAndControlFwdPolicies/selectors';

import {
  loader,
  handleOnSearchFilter,
  handlePageNumber,
  handlePageSize,
  toggleClose,
  toggleSortBy,
  toggleAddForm,
  toggleEditForm,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleViewForm,
  deletePolicy,
} from 'ducks/logAndControlFwdPolicies';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';

function LogAndControlFwdPolicies(props) {
  const {
    accessPermissions,
    authType,
    handleDeleteConfirmationForm,
    handleSearchFilter,
    handleSortBy,
    handleToggleDuplicateForm,
    handleToggleEditCopyForm,
    handleToggleEditForm,
    handleToggleViewForm,
    load,
    loading,
    numberOfLines,
    onHandlePageNumber,
    onHandlePageSize,
    logAndControlFwdTableData,
    pageNumber,
    pageSize,
    searchData,
    sortDirection,
    sortField,
    t,
  } = props;

  const dispatch = useDispatch();
  
  useEffect((e) => {
    dispatch(handleSearchFilter(e, ''));
    load(true, 1);
  }, []);

  useEffect(() => {
    if (loading) return;
    load(true, 1);
  }, [sortField, sortDirection]);

  const getTableData = () => {
    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_FORWARDING, authType);
    const tableData = logAndControlFwdTableData
    //   .filter(
    //     row => row.name.toLowerCase().includes(searchData.toLowerCase())
    //   || row.order.toString().toLowerCase().includes(searchData.toLowerCase()),
    //   )
      .map((row) => {
        const additionalLine = 60;

        return {
          ...row,
          id: row.id,
          isEditable: !isReadOnly,
          isDeletable: !isReadOnly && !row.defaultRule && row.order > 0,
          isCopyable: !isReadOnly && !row.defaultRule && !row.predefined,
          tooltip: row.predefined ? t('TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER') : '',
          isReadOnly,
          rowHeight: additionalLine,
        };
      });
    return tableData;
  };
 
  return (
    <ConfigTableWithPaginationAndSort
      key={searchData}
      {...LOG_CONTROL_POLICIES_TABLE_CONFIGS}
      data={getTableData()}
      tableHeight={20000}
      maxTableHeight="20000px"
      onHandleRowDelete={(e) => handleDeleteConfirmationForm(true, e)}
      onHandleRowView={(e) => handleToggleViewForm(true, e)}
      onHandleRowEdit={(e) => handleToggleEditForm(true, e)}
      onHandleDuplicateRow={(e) => handleToggleDuplicateForm(true, e)}
      handleEditAction={handleToggleEditCopyForm}
      handleCopyAction={handleToggleEditCopyForm}
      onHandleSortBy={handleSortBy}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(x) => onHandlePageNumber(x)}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(x) => onHandlePageSize(x)}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection} />
  );
}

const mapStateToProps = (state) => ({
  ...LogAndControlFwdPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    handleDelete: deletePolicy,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSearchFilter: handleOnSearchFilter,
    handleSortBy: toggleSortBy,
    handleToggleAddForm: toggleAddForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleViewForm: toggleViewForm,
    load: loader,
    onHandlePageNumber: handlePageNumber,
    onHandlePageSize: handlePageSize,
  }, dispatch);
  return actions;
};

LogAndControlFwdPolicies.propTypes = {
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  addPolicy: PropTypes.bool,
  authType: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  cancelHandle: PropTypes.func,
  duplicateRow: PropTypes.bool,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  handleSortBy: PropTypes.func,
  handleToggleDuplicateForm: PropTypes.func,
  handleToggleEditCopyForm: PropTypes.func,
  handleToggleEditForm: PropTypes.func,
  handleToggleForm: PropTypes.func,
  handleToggleViewForm: PropTypes.func,
  load: PropTypes.func,
  loading: PropTypes.bool,
  logAndControlFwdTableData: PropTypes.arrayOf(PropTypes.shape()),
  modalLoading: PropTypes.bool,
  numberOfLines: PropTypes.number,
  onHandlePageNumber: PropTypes.func,
  onHandlePageSize: PropTypes.func,
  pageNumber: PropTypes.number,
  pageSize: PropTypes.number,
  searchData: PropTypes.string,
  selectedRowID: PropTypes.number,
  showDeleteForm: PropTypes.bool,
  showForm: PropTypes.bool,
  sortDirection: PropTypes.string,
  sortField: PropTypes.string,
  t: PropTypes.func,
};

LogAndControlFwdPolicies.defaultProps = {
  accessPermissions: {},
  addPolicy: false,
  authType: '',
  cachedData: [],
  cancelHandle: noop,
  duplicateRow: false,
  handleClearTable: noop,
  handleDelete: noop,
  handleDeleteConfirmationForm: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  handleSortBy: noop,
  handleToggleDuplicateForm: noop,
  handleToggleEditCopyForm: noop,
  handleToggleEditForm: noop,
  handleToggleForm: noop,
  handleToggleViewForm: noop,
  load: noop,
  loading: true,
  logAndControlFwdTableData: [],
  modalLoading: false,
  numberOfLines: 0,
  onHandlePageNumber: noop,
  onHandlePageSize: noop,
  pageNumber: 0,
  pageSize: 0,
  searchData: '',
  selectedRowID: null,
  showDeleteForm: false,
  showForm: false,
  sortDirection: '',
  sortField: '',
  t: (str) => str,
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(LogAndControlFwdPolicies));
