// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { reduxForm, Field, change } from 'redux-form';
import { useDispatch, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { handleOnSearchFilter, toggleAddForm } from 'ducks/logAndControlFwdPolicies';
import { withTranslation } from 'react-i18next';
import AddNewButton from 'components/addNewButton';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import SearchDropdown from './SearchDropdown';

export function BasicCustomAppForm(props) {
  const { t, isReadOnly, actions } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(change('searchParameterForm', 'searchParameter', { id: 'ruleName', name: 'RULE_NAME' }));
  }, []);

  return (
    <div className="table-layout-header">
      <div className="controls-container">
        <div className="grid-toolbar-left">
          {!isReadOnly && <AddNewButton label={t('ADD_LOG_AND_CONTROL_FORWARDING_RULE')} clickCallback={() => actions.toggleAddForm(true, {}, 'ADD_CLOUD_CONNECTOR_ROLE')} />}
        </div>
        <div className="grid-toolbar-right display-inline-flex">
          <span>
            {t('SEARCH_BY')}
          </span>
          <Field
            id="searchParameter"
            name="searchParameter"
            className="search-parameter-dropdown"
            component={SearchDropdown} />
          <span className="min-width-8px" />
          <SimpleSearchInput
            withButton
            showCancel={actions.showCancel}
            onKeyPressCb={actions.handleOnSearchFilter} />
        </div>
      </div>
    </div>
  );
}

BasicCustomAppForm.propTypes = {
  t: PropTypes.func,
  actions: PropTypes.shape(),
  isReadOnly: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  t: null,
  actions: {},
  isReadOnly: true,
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...state.locations,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAddForm,
    handleOnSearchFilter,
  }, dispatch);
  
  return {
    actions,
  };
};

const LogAndControlControlSeach = reduxForm({
  form: 'searchParameterForm',
})(BasicCustomAppForm);

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(LogAndControlControlSeach),
);
