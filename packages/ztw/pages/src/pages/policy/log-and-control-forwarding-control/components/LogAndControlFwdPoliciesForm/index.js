// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import { reduxForm } from 'redux-form';
import Modal from 'components/modal';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop, cloneDeep } from 'utils/lodash';
import * as LogAndControlFwdPoliciesSelectors from 'ducks/logAndControlFwdPolicies/selectors';

import {
  saveForm,
  toggleClose,
  toggleDeleteForm,
  deletePolicy,
} from 'ducks/logAndControlFwdPolicies';
import LogAndControlFwdFragment from '../LogAndControlFwdFragment';
 
export function BasicCustomAppForm(props) {
  const {
    valid,
    actions,
    t,
    handleClose,
    handleSubmit,
    appData,
    addPolicy,
    duplicateRow,
    viewOnly,
    showDeleteForm,
    selectedRowID,
    modalLoading,
  } = props;

  const { order, defaultRule, predefined } = appData;

  const { handleDelete, handleDeleteConfirmationForm } = actions;

  const onSubmit = () => {
    actions.saveDnsPolicy();
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form firewall-control-dns-policy-edit">
        <div className="form-sections-container">
          <Loading {...props} />
          <LogAndControlFwdFragment />
        </div>
        <div className="dialog-footer white-background">
          <div className="dialog-footer-left">
            <button
              type="submit"
              disabled={!valid}
              // onClick={onSubmit}
              className={`submit ${!valid ? 'disabled' : ''}`}>
              {t('SAVE')}
            </button>
            <button
              type="button"
              className="cancel"
              onClick={handleClose}>
              {t('CANCEL')}
            </button>
          </div>
          {/* <div className="dialog-footer-right">
            {!viewOnly && !addPolicy && !duplicateRow && !defaultRule && !predefined && order > 0
           && (
             <button
               type="button"
               className="button big delete"
               onClick={() => handleDeleteConfirmationForm(true, appData)}>
               {t('DELETE')}
             </button>
           )}
          </div> */}
        </div>
      </form>
      <Modal
        title={t('DELETE_CONFIRMATION')}
        isOpen={showDeleteForm}
        closeModal={() => handleDeleteConfirmationForm(false)}>
        <DeleteConfirmationForm
          modalLoading={modalLoading}
          selectedRowID={selectedRowID}
          handleCancel={() => handleDeleteConfirmationForm(false)}
          handleDelete={handleDelete} />
      </Modal>
    </>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    addDestIpGroups: PropTypes.func,
    handleDelete: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    saveDnsPolicy: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  handleClose: PropTypes.func,
  logAndControlFwdPolicies: PropTypes.shape({}),
  appData: PropTypes.shape({
    order: PropTypes.number,
    defaultRule: PropTypes.bool,
    predefined: PropTypes.bool,
  }),
  addPolicy: PropTypes.bool,
  duplicateRow: PropTypes.bool,
  viewOnly: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  selectedRowID: PropTypes.number,
  modalLoading: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    addDestIpGroups: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: null,
  handleClose: null,
  logAndControlFwdPolicies: {},
  appData: {},
  addPolicy: false,
  duplicateRow: false,
  viewOnly: true,
  showDeleteForm: false,
  selectedRowID: null,
  modalLoading: false,
};

const BasicCustomForm = reduxForm({
  form: 'addEditDnsFilteringRules',
})(BasicCustomAppForm);

const LogAndControlFwdPoliciesForm = connect(
  (state) => {
    const {
      numberOfOrderedLines,
      appData,
      addPolicy,
      duplicateRow,
    } = LogAndControlFwdPoliciesSelectors.baseSelector(state);
    const appDataClone = cloneDeep(appData);

    const {
      order,
      name,
      state: status,
      ecGroups = [],
      locationGroups = [],
      locations = [],
      proxyGateway = [],
      description = '',
    } = appDataClone;
    
    const nextRule = addPolicy || duplicateRow
      ? numberOfOrderedLines + 1
      : order;

    const nextName = addPolicy ? `FWD_${numberOfOrderedLines + 1}` : name;
    const isNewRule = addPolicy && !duplicateRow;
    
    return ({
      initialValues: {
        ruleOrder: { id: nextRule, name: nextRule },
        ruleName: nextName,
        ruleStatus: isNewRule
          ? { id: 'enabled', name: 'ENABLED' }
          : { id: status && status.toLowerCase(), name: status },
        locationGroup: isNewRule ? [] : locationGroups,
        locationName: isNewRule ? [] : locations,
        bcGroups: isNewRule ? [] : ecGroups,
        gateway: isNewRule ? [] : proxyGateway,
        description,
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveDnsPolicy: saveForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deletePolicy,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({ ...state.logAndControlFwdPolicies });

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(LogAndControlFwdPoliciesForm));
