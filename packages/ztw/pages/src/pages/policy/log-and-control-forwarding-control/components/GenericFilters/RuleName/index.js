import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import Input from 'components/Input';
import {
  required,
  maxLength,
} from 'utils/validations';

const maxLength31 = maxLength(31);

function RuleName({ value, viewOnly, t }) {
  return (
    <div className="g-right">
      {!viewOnly
        ? (
          <>
            <FormFieldLabel text={t('RULE_NAME')} tooltip={t('TOOLTIP_POLICY_FIREWALL_RULE_NAME')} />
            <Field
              id="ruleName"
              name="ruleName"
              component={Input}
              placeholder={t('ENTER_TEXT')}
              validate={[
                required,
                maxLength31,
              ]}
              styleClass="max-width" />
          </>
        ) : (
          <>
            <FormFieldLabel text={t('RULE_NAME')} tooltip={t('TOOLTIP_POLICY_FIREWALL_RULE_NAME')} />
            <p className="disabled-input">{value}</p>
          </>
        )}
    </div>
  );
}

RuleName.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
RuleName.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
};

export default withTranslation()(RuleName);
