import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import GatewayDropDown from 'commonConnectedComponents/dropdown/GatewayLogAndControlDropdown';
import {
  required,
} from 'utils/validations';

function Gateway({
  value, viewOnly, t, meta, error,
}) {
  const hasError = meta.touched && !!error;

  return (
    <div className="g-left">
      <FormFieldLabel
        text={t('GATEWAY')}
        error={hasError ? t(error) : null}
        tooltip={t('TOOLTIP_POLICY_FIREWALL_GATEWAY')}
        styleClass={`${hasError ? 'invalid' : ''}`} />
      {!viewOnly
        ? (
          <Field
            id="gateway"
            name="gateway"
            component={GatewayDropDown}
            validate={[required]} />
        ) : (
          <p className="disabled-input">{ value || '---' }</p>
        )}
    </div>
  );
}
  
Gateway.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  meta: PropTypes.shape({
    touched: PropTypes.bool,
  }),
  error: PropTypes.string,
};
  
Gateway.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
  meta: {},
  error: null,
};

export default withTranslation()(Gateway);
