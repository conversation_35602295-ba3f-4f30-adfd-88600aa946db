// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import LocationsGroupDropdown from 'commonConnectedComponents/dropdown/LocationsGroupDropDown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function LocationGroup({ viewOnly, t, disabled }) {
  return (
    <div className="g-right">
      <FormFieldLabel text={t('LOCATION_GROUP')} tooltip={t('TOOLTIP_POLICY_FIREWALL_LOCATION_GROUP')} />
      <Field
        id="locationGroup"
        name="locationGroup"
        component={LocationsGroupDropdown}
        props={{
          disabled,
          isViewOnly: viewOnly,
          place: 'right',
          label: t('---'),
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

LocationGroup.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  disabled: PropTypes.bool,
};
  
LocationGroup.defaultProps = {
  viewOnly: false,
  t: (str) => str,
  disabled: false,
};

export default (withTranslation()(LocationGroup));
