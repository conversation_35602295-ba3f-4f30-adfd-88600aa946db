import React from 'react';
import { PropTypes } from 'prop-types';
import { connect, useSelector } from 'react-redux';
import { isEmpty } from 'utils/lodash';
import * as logAndControlFwdPoliciesSelectors from 'ducks/logAndControlFwdPolicies/selectors';
import { withTranslation, useTranslation } from 'react-i18next';
import { FormSectionLabel } from 'components/label';

import {
  RuleOrder,
  RuleName,
  RuleStatus,
  Location,
  BCGroups,
  Gateway,
  Description,
} from '..';

export function LogAndControlFwdPoliciesFragment(props) {
  const { logAndControlFwdPolicies } = useSelector((state) => state);
  const {
    viewOnly, form, formMeta, formSyncErrors,
  } = props;
  const { appData } = logAndControlFwdPolicies || {};
  const {
    name, order, state, proxyGateway, defaultRule,
  } = appData || {};
  const { t } = useTranslation();

  return (
    <div>
      <FormSectionLabel text={t('EC_FW_RULE')} />
      <div className="form-section">
        <div className="g-row">
          <RuleOrder
            value={order}
            viewOnly={viewOnly || defaultRule} />
          <span className="email-input-view-at">{' '}</span>
          <RuleName
            value={name}
            viewOnly={viewOnly || defaultRule} />
        </div>
        <div className="g-row">
          <RuleStatus
            value={state}
            viewOnly={viewOnly || defaultRule} />
        </div>
      </div>

      <FormSectionLabel text={t('SOURCE')} />
      <div className="form-section">
        <div className="g-row">
          <Location
            viewOnly={viewOnly || defaultRule}
            meta={formMeta.locationName}
            disabled={!isEmpty(form.bcGroups)}
            error={formSyncErrors.locationName} />
          {/* <span className="center-logical-label">{t('OR')}</span>
          <LocationGroup
            disabled={true || !isEmpty(form.bcGroups)}
            viewOnly={viewOnly || defaultRule} /> */}
        </div>
        <div className="g-row">
          <span className="center-logical-label">{t('OR')}</span>
          <BCGroups
            disabled={!isEmpty(form.locationName)}
            viewOnly={viewOnly || defaultRule} />
        </div>
      </div>

      <FormSectionLabel text={t('ACTION')} />
      <div className="form-section">
        <div className="g-row">
          <Gateway
            value={proxyGateway ? proxyGateway.name : ''}
            meta={formMeta.gateway}
            error={formSyncErrors.gateway}
            viewOnly={viewOnly} />
        </div>
      </div>

      <Description
        t={t}
        error={formSyncErrors.description}
        viewOnly={viewOnly || defaultRule} />
    </div>
    
  );
}

LogAndControlFwdPoliciesFragment.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.shape({
    bcGroups: PropTypes.arrayOf(),
    locationName: PropTypes.string,
  }),
  formMeta: PropTypes.shape({
    gateway: PropTypes.shape({}),
    locationName: PropTypes.shape({}),
  }),
  formSyncErrors: PropTypes.shape({
    description: PropTypes.string,
    gateway: PropTypes.string,
    locationName: PropTypes.string,
  }),
  actions: PropTypes.shape({
    handleDashboardChange: PropTypes.func,
    handleCloudConnectorProvisioningChange: PropTypes.func,
    handlePolicyConfigurationChange: PropTypes.func,
    handleAdminManagementChange: PropTypes.func,
    handleLocationManagementChange: PropTypes.func,
    handleTrafficForwardingDNSChange: PropTypes.func,
    handleApiKeyManagementChange: PropTypes.func,
    handleRemoteAssistanceManagementChange: PropTypes.func,
    handleNssLoggingChange: PropTypes.func,
  }),
};
    
LogAndControlFwdPoliciesFragment.defaultProps = {
  viewOnly: true,
  form: {},
  formMeta: {},
  formSyncErrors: {},
  actions: ({
    handleDashboardChange: null,
    handleCloudConnectorProvisioningChange: null,
    handlePolicyConfigurationChange: null,
    handleAdminManagementChange: null,
    handleLocationManagementChange: null,
    handleTrafficForwardingDNSChange: null,
    handleApiKeyManagementChange: null,
    handleRemoteAssistanceManagementChange: null,
    handleNssLoggingChange: null,
  }),
};

const mapStateToProps = (state) => ({
  viewOnly: logAndControlFwdPoliciesSelectors.viewOnlySelector(state),
  form: logAndControlFwdPoliciesSelectors.formValuesSelector(state),
  formMeta: logAndControlFwdPoliciesSelectors.formMetaSelector(state),
  formSyncErrors: logAndControlFwdPoliciesSelectors.formSyncErrorsSelector(state),
});

// eslint-disable-next-line max-len
export default connect(mapStateToProps, null)(withTranslation()(LogAndControlFwdPoliciesFragment));
