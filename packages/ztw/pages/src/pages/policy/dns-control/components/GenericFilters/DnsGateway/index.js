import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import DnsGatewayDropdown from 'commonConnectedComponents/dropdown/DnsGatewayDropdown';
import {
  requiredId,
} from 'utils/validations';

function DnsGateway({
  value, viewOnly, error, t,
}) {
  const hasError = !!error;

  return (
    <div className="g-right ">
      <FormFieldLabel
        error={hasError ? t(error) : null}
        styleClass={`${hasError ? 'invalid' : ''}`}
        text={t('DNS_GATEWAY')}
        tooltip={t('TOOLTIP_POLICY_DNS_GATEWAY')} />
      {!viewOnly
        ? (
          <Field
            id="dnsGateway"
            name="dnsGateway"
            component={DnsGatewayDropdown}
            validate={[requiredId]} />
        ) : (
          <p className="disabled-input">{ value || '---' }</p>
        )}
    </div>
  );
}
  
DnsGateway.propTypes = {
  error: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
DnsGateway.defaultProps = {
  error: null,
  value: '',
  viewOnly: false,
  t: (str) => str,
};

export default withTranslation()(DnsGateway);
