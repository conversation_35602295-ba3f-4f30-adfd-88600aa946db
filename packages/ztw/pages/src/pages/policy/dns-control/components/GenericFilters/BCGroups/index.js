// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { checkArraySize } from 'utils/validations';
import EcGroupDropdown from 'commonConnectedComponents/dropdown/EcGroupDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);
const maxArrayOf32 = (value) => checkArraySize(value, 32);

export function BCGroups({
  t, viewOnly, error, disabled,
}) {
  const hasError = !!error;

  return (
    <div className="g-left">
      <FormFieldLabel
        text={t('BRANCH_CLOUD_CONNECTOR_GROUP')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? (t(error)).replace('{0}', '32') : null}
        tooltip={t('TOOLTIP_POLICY_FIREWALL_BRANCH_AND_CC')} />
      <Field
        id="bcGroups"
        name="bcGroups"
        component={EcGroupDropdown}
        props={{
          disabled,
          isViewOnly: viewOnly,
          label: t('---'),
          classes: hasError ? ['error'] : [],
        }}
        validate={[maxArrayOf32]}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

BCGroups.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  error: PropTypes.string,
  disabled: PropTypes.bool,
};

BCGroups.defaultProps = {
  viewOnly: false,
  t: (str) => str,
  error: null,
  disabled: false,
};

export default (withTranslation()(BCGroups));
