import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop, isEmpty } from 'utils/lodash';
import { getReadOnly, verifyConfigData } from 'utils/helpers';
import { DNS_POLICIES_TABLE_CONFIGS } from 'ducks/dnsPolicies/constants';
import * as loginSelectors from 'ducks/login/selectors';
import * as DnsPoliciesSelector from 'ducks/dnsPolicies/selectors';
import {
  deletePolicy,
  handleOnSearchFilter,
  handlePageNumber,
  handlePageSize,
  loader,
  toggleAddForm,
  toggleClose,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleEditForm,
  toggleSortBy,
  toggleViewForm,
} from 'ducks/dnsPolicies';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';

function DnsPoliciesTable(props) {
  const {
    accessPermissions,
    authType,
    dnsPoliciesTableData,
    handleDeleteConfirmationForm,
    handleSearchFilter,
    handleSortBy,
    handleToggleDuplicateForm,
    handleToggleEditCopyForm,
    handleToggleEditForm,
    handleToggleViewForm,
    load,
    loading,
    numberOfLines,
    onHandlePageNumber,
    onHandlePageSize,
    pageNumber,
    pageSize,
    searchData,
    sortDirection,
    sortField,
    t,
  } = props;
  
  const dispatch = useDispatch();

  useEffect((e) => {
    dispatch(handleSearchFilter(e, ''));
    load(true, 1);
  }, []);

  useEffect(() => {
    if (loading) return;
    load(true, 1);
  }, [sortField, sortDirection]);

  const getTableData = () => {
    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_FORWARDING, authType);

    const local = localStorage.getItem('configData');
    const configData = (local && local.length) ? JSON.parse(local) : {};
    const enablePredefinedPolicy = verifyConfigData({ configData, key: 'enablePredefinedPolicy' });

    const tableData = dnsPoliciesTableData
    // .filter(
    //   row => row.name.toLowerCase().includes(searchData.toLowerCase())
    //   || row.order.toString().toLowerCase().includes(searchData.toLowerCase()),
    // )
      .map((row) => {
        const {
          ecGroups, zpaIpGroup, locations, locationGroups,
          dnsGateway,
          destAddresses, destIpGroups,
          srcIps, srcIpGroups,
        } = row || {};
        const additionalLine = 60;
        
        const rowHeight = additionalLine + (isEmpty(ecGroups) ? 0 : additionalLine)
        + (isEmpty(zpaIpGroup) ? 0 : additionalLine)
        + (isEmpty(locations) ? 0 : additionalLine)
        + (isEmpty(locationGroups) ? 0 : additionalLine)
        + (isEmpty(destAddresses) ? 0 : additionalLine)
        + (isEmpty(destIpGroups) ? 0 : additionalLine)
        + (isEmpty(srcIps) ? 0 : additionalLine)
        + (isEmpty(srcIpGroups) ? 0 : additionalLine)
        + (isEmpty(dnsGateway) ? 0 : additionalLine * 1.2);

        return {
          ...row,
          id: row.id,
          isEditable: !isReadOnly && !row.isDisablablePredefinied
            && !(row.isWanCtrRule && !enablePredefinedPolicy),
          isDeletable: !isReadOnly && !row.defaultRule && row.order > 0
                        && !row.isDisablablePredefinied && !row.isWanCtrRule,
          isCopyable: !isReadOnly && !row.defaultRule && !row.predefined && !row.isWanCtrRule,
          tooltip: row.predefined ? t('TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER') : '',
          isReadOnly: isReadOnly || row.isDisablablePredefinied
            || (row.isWanCtrRule && !enablePredefinedPolicy),
          rowHeight: rowHeight > additionalLine ? rowHeight : additionalLine,
        };
      });
    return tableData;
  };

  return (
    <ConfigTableWithPaginationAndSort
      key={searchData}
      {...DNS_POLICIES_TABLE_CONFIGS}
      data={getTableData()}
      tableHeight={20000}
      maxTableHeight="20000px"
      onHandleRowDelete={(e) => handleDeleteConfirmationForm(true, e)}
      onHandleRowView={(e) => handleToggleViewForm(true, e)}
      onHandleRowEdit={(e) => handleToggleEditForm(true, e)}
      onHandleDuplicateRow={(e) => handleToggleDuplicateForm(true, e)}
      handleEditAction={handleToggleEditCopyForm}
      handleCopyAction={handleToggleEditCopyForm}
      onHandleSortBy={handleSortBy}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(x) => onHandlePageNumber(x)}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(x) => onHandlePageSize(x)}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection} />
  );
}

const mapStateToProps = (state) => ({
  ...DnsPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    handleDelete: deletePolicy,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSearchFilter: handleOnSearchFilter,
    handleSortBy: toggleSortBy,
    handleToggleAddForm: toggleAddForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleViewForm: toggleViewForm,
    load: loader,
    onHandlePageNumber: handlePageNumber,
    onHandlePageSize: handlePageSize,
  }, dispatch);
  return actions;
};

DnsPoliciesTable.propTypes = {
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  addPolicy: PropTypes.bool,
  authType: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  cancelHandle: PropTypes.func,
  dnsPoliciesTableData: PropTypes.arrayOf(PropTypes.shape()),
  duplicateRow: PropTypes.bool,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  handleSortBy: PropTypes.func,
  handleToggleDuplicateForm: PropTypes.func,
  handleToggleEditCopyForm: PropTypes.func,
  handleToggleEditForm: PropTypes.func,
  handleToggleForm: PropTypes.func,
  handleToggleViewForm: PropTypes.func,
  load: PropTypes.func,
  loading: PropTypes.bool,
  modalLoading: PropTypes.bool,
  numberOfLines: PropTypes.number,
  onHandlePageNumber: PropTypes.func,
  onHandlePageSize: PropTypes.func,
  pageNumber: PropTypes.number,
  pageSize: PropTypes.number,
  searchData: PropTypes.string,
  selectedRowID: PropTypes.number,
  showDeleteForm: PropTypes.bool,
  showForm: PropTypes.bool,
  sortDirection: PropTypes.string,
  sortField: PropTypes.string,
  t: PropTypes.func,
};

DnsPoliciesTable.defaultProps = {
  accessPermissions: {},
  accessSubscriptions: [],
  addPolicy: false,
  authType: '',
  cachedData: [],
  cancelHandle: noop,
  dnsPoliciesTableData: [],
  duplicateRow: false,
  handleClearTable: noop,
  handleDelete: noop,
  handleDeleteConfirmationForm: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  handleSortBy: noop,
  handleToggleDuplicateForm: noop,
  handleToggleEditCopyForm: noop,
  handleToggleEditForm: noop,
  handleToggleForm: noop,
  handleToggleViewForm: noop,
  load: noop,
  loading: true,
  modalLoading: false,
  numberOfLines: 0,
  onHandlePageNumber: noop,
  onHandlePageSize: noop,
  pageNumber: 0,
  pageSize: 0,
  searchData: '',
  selectedRowID: null,
  showDeleteForm: false,
  showForm: false,
  sortDirection: '',
  sortField: '',
  t: (str) => str,
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(DnsPoliciesTable),
);
