import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';

function ApplicationSegment({ t }) {
  return (
    <div className="g-left">
      <FormFieldLabel text={t('APPLICATION_SEGMENT')} tooltip={t('TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT')} />
      <p className="disabled-input">{t('ALL')}</p>
    </div>
  );
}

ApplicationSegment.propTypes = {
  t: PropTypes.func,
};
  
ApplicationSegment.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(ApplicationSegment);
