// @flow

import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { pureFqdnOrWildcard } from 'utils/validations';
import ListBuilder from 'components/listBuilder';
import * as DnsFwdPoliciesSelectors from 'ducks/dnsPolicies/selectors';
import { isEmpty } from 'utils/lodash';

import {
  handleDestinationIpAddresses,
} from 'ducks/dnsPolicies';

export function IpDestinationList(props) {
  const {
    viewOnly, t, actions, destinationIpAddresses,
  } = props;
  const hasError = !isEmpty(destinationIpAddresses) && destinationIpAddresses.some(
    (x) => pureFqdnOrWildcard(x),
  );
  const maxLimit = destinationIpAddresses?.length > 1000;

  return (
    <div className="g-row">
      <span className="center-logical-label">{t('OR')}</span>
      <div className="g-left">
        <FormFieldLabel
          styleClass={hasError || maxLimit ? 'invalid' : ''}
          error={maxLimit ? t('MAX_NUM_DESINATION_ADRESS_IS_1000') : ''}
          text={t('FQDN_WILDCARD_DOMAINS_ACCDRESSES')}
          tooltip={t('TOOLTIP_POLICY_DNS_DESTINATION_FQDN_ACCDRESSES')} />
        <Field
          id="destinationIpAddresses"
          name="destinationIpAddresses"
          attribute="destinationIpAddresses"
          props={{
            t,
            hasSearch: true,
            value: destinationIpAddresses,
            action: actions.onipAddresses,
            disabled: viewOnly,
            removeItemsText: t('REMOVE_ALL'),
            addButtonText: t('ADD_ITEMS'),
            placeholder: t('ADD_ITEMS'),
            styleConfig: { inputMarginRight: 10 },
            validation: pureFqdnOrWildcard,
          }}
          // validate={[looseurlAddressSchemeless]}
          component={ListBuilder} />
      </div>
    </div>
  );
}

IpDestinationList.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  destinationIpAddresses: PropTypes.arrayOf(PropTypes.string),
  actions: PropTypes.shape({
    onipAddresses: PropTypes.func,
  }),
};

IpDestinationList.defaultProps = {
  viewOnly: false,
  t: (str) => str,
  destinationIpAddresses: [],
  actions: { onipAddresses: null },
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    onipAddresses: handleDestinationIpAddresses,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  // eslint-disable-next-line max-len
  destinationIpAddresses: DnsFwdPoliciesSelectors.formValuesSelector(state).destinationIpAddresses,
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(IpDestinationList));
