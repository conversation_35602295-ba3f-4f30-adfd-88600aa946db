// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import { reduxForm } from 'redux-form';
import Modal from 'components/modal';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { pureFqdnOrWildcard } from 'utils/validations';
import { noop, isEmpty, cloneDeep } from 'utils/lodash';
import * as DnsPoliciesSelectors from 'ducks/dnsPolicies/selectors';
import * as SourceIPSelectors from 'ducks/sourceIPGroups/selectors';
import * as DestinationIPSelectors from 'ducks/destinationIpGroups/selectors';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';

import { updatedCountry, toggleForm as destinationIpGroupsToggleForm } from 'ducks/destinationIpGroups';
import { toggleForm as sourceIpGroupsToggleForm } from 'ducks/sourceIPGroups';
import { toggleClose as networkServiceToggleClose } from 'ducks/networkServices';

import {
  saveForm,
  toggleClose,
  toggleDeleteForm,
  tabConfiguration,
  deletePolicy,
} from 'ducks/dnsPolicies';

import { SourceIPForm } from 'pages/administration/source-ip-groups/components';
import { DestinationIPForm } from 'pages/administration/destination-ip-groups/components';

import {
  NetworkServiceForm,
  NetworkServiceGroupForm,
} from 'pages/administration/network-services/components';

import DnsPoliciesFragment from '../DnsPoliciesFragment';
 
export function BasicCustomAppForm(props) {
  const {
    valid,
    actions,
    t,
    handleClose,
    handleSubmit,
    appData,
    addPolicy,
    duplicateRow,
    viewOnly,
    showDeleteForm,
    selectedRowID,
    modalLoading,

    sourceFormMode,
    sourceIpFormTitle,
    showSourceIpForm,
    destinationFormMode,
    destinationIpFormTitle,
    showDestinationIpForm,
    networkServicesFormTitle,
    showNetworkServicesForm,
    ipAddressesList,
    networkServiceTab,
    updatedCountryValues,
    destinationIpAddresses,
  } = props;

  const {
    order, defaultRule, predefined, isWanCtrRule, isDisableablePredefiniedZTW3626,
  } = appData;
  
  const isNetworkServiceTab = networkServiceTab === 'NETWORK_SERVICES';
  const {
    handleDelete,
    handleDeleteConfirmationForm,
    toggleDestinationIPForm,
    toggleSourceIPForm,
    networkServiceToggleCloseForm,
  } = actions;

  const onSubmit = () => {
    actions.saveDnsPolicy();
  };

  const hasError = !isEmpty(destinationIpAddresses) && destinationIpAddresses.some(
    (x) => pureFqdnOrWildcard(x),
  );

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form firewall-control-dns-policy-edit">
        <div className="form-sections-container">
          <Loading {...props} />
          <DnsPoliciesFragment />
        </div>
        <div className="dialog-footer white-background">
          <div className="dialog-footer-left">
            <button
              type="submit"
              disabled={(!valid || hasError)}
              // onClick={onSubmit}
              className={`submit ${(!valid || hasError) ? 'disabled' : ''}`}>
              {t('SAVE')}
            </button>
            <button
              type="button"
              className="cancel"
              onClick={handleClose}>
              {t('CANCEL')}
            </button>
          </div>
          {/* <div className="dialog-footer-right">
            {!viewOnly && !addPolicy && !duplicateRow && !defaultRule && !predefined
            && !isWanCtrRule && !isDisableablePredefiniedZTW3626
            && order > 0
           && (
             <button
               type="button"
               className="button big delete"
               onClick={() => handleDeleteConfirmationForm(true, appData)}>
               {t('DELETE')}
             </button>
           )}
          </div> */}
        </div>
      </form>
      <Modal
        title={t('DELETE_CONFIRMATION')}
        isOpen={showDeleteForm}
        closeModal={() => handleDeleteConfirmationForm(false)}>
        <DeleteConfirmationForm
          modalLoading={modalLoading}
          selectedRowID={selectedRowID}
          handleCancel={() => handleDeleteConfirmationForm(false)}
          handleDelete={handleDelete} />
      </Modal>

      <Modal
        title={t(networkServicesFormTitle)}
        isOpen={showNetworkServicesForm}
        closeModal={() => networkServiceToggleCloseForm()}>
        {isNetworkServiceTab ? <NetworkServiceForm /> : <NetworkServiceGroupForm />}
      </Modal>

      <Modal
        title={t(destinationIpFormTitle)}
        isOpen={showDestinationIpForm}
        styleClass="destination-ip-form"
        closeModal={() => toggleDestinationIPForm(null, false)}>
        <DestinationIPForm
          formMode={destinationFormMode}
          ipAddressesList={ipAddressesList}
          updatedCountryValues={updatedCountryValues} />
      </Modal>

      <Modal
        title={t(sourceIpFormTitle)}
        isOpen={showSourceIpForm}
        closeModal={() => toggleSourceIPForm(null, false)}>
        <SourceIPForm formMode={sourceFormMode} ipAddressesList={ipAddressesList} />
      </Modal>
    </>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    addDestIpGroups: PropTypes.func,
    cancelHandle: PropTypes.func,
    handleDelete: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    networkServiceToggleCloseForm: PropTypes.func,
    saveDnsPolicy: PropTypes.func,
    toggleDestinationIPForm: PropTypes.func,
    toggleSourceIPForm: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  handleClose: PropTypes.func,
  dnsPolicies: PropTypes.shape({}),
  appData: PropTypes.shape({
    order: PropTypes.number,
    defaultRule: PropTypes.bool,
    predefined: PropTypes.bool,
    isWanCtrRule: PropTypes.bool,
    isDisableablePredefiniedZTW3626: PropTypes.bool,
  }),
  addPolicy: PropTypes.bool,
  duplicateRow: PropTypes.bool,
  viewOnly: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  selectedRowID: PropTypes.number,
  modalLoading: PropTypes.bool,
  sourceFormMode: PropTypes.string,
  sourceIpFormTitle: PropTypes.string,
  showSourceIpForm: PropTypes.bool,
  destinationFormMode: PropTypes.string,
  destinationIpFormTitle: PropTypes.string,
  showDestinationIpForm: PropTypes.bool,
  showNetworkServicesForm: PropTypes.bool,
  networkServicesFormTitle: PropTypes.string,
  ipAddressesList: PropTypes.arrayOf(PropTypes.string),
  networkServiceTab: PropTypes.string,
  updatedCountryValues: PropTypes.func,
  destinationIpAddresses: PropTypes.arrayOf(PropTypes.string),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    addDestIpGroups: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: null,
  handleClose: null,
  dnsPolicies: {},
  appData: {},
  addPolicy: false,
  duplicateRow: false,
  viewOnly: true,
  showDeleteForm: false,
  selectedRowID: null,
  modalLoading: false,
  sourceFormMode: '',
  sourceIpFormTitle: '',
  showSourceIpForm: false,
  destinationFormMode: '',
  destinationIpFormTitle: '',
  showDestinationIpForm: false,
  showNetworkServicesForm: false,
  networkServicesFormTitle: '',
  ipAddressesList: [],
  networkServiceTab: '',
  updatedCountryValues: noop,
  destinationIpAddresses: [],
};

const BasicCustomForm = reduxForm({
  form: 'addEditDnsFilteringRules',
})(BasicCustomAppForm);

const DnsPoliciesForm = connect(
  (state) => {
    const {
      numberOfOrderedLines,
      appData,
      addPolicy,
      duplicateRow,
    } = DnsPoliciesSelectors.baseSelector(state);
    const appDataClone = cloneDeep(appData);

    const { cachedData: ipDestinationGroup } = state.ipDnsDestinationGroupDropdown || [];
    const { data: ipPoolList } = state.ipPoolDropdown;

    const {
      action, // applications
      description = '',
      destAddresses = [],
      destIpGroups = [],
      dnsGateway = {},
      ecGroups = [],
      locationGroups = [],
      locations = [],
      name,
      order,
      sourceIpGroupExclusion,
      srcIpGroups = [],
      srcIps = [],
      state: status,
      zpaIpGroup,
    } = appDataClone;

    const nextRule = addPolicy || duplicateRow
      ? numberOfOrderedLines + 1
      : order;
    const nextName = addPolicy ? `DNS_${numberOfOrderedLines + 1}` : name;
    const isNewRule = addPolicy && !duplicateRow;
    const destIpGroupsId = destIpGroups.map((x) => x.id);
    
    const getNetworkTraffic = (value) => {
      if (value === 'ALLOW') return ({ id: 'ALLOW', name: 'BOTH_REQ_RESP_ALLOW' });
      if (value === 'BLOCK') return ({ id: 'BLOCK', name: 'EITHER_REQ_RESP_BLOCK' });
      if (value === 'REDIR_ZPA') return { id: 'REDIR_ZPA', name: 'DNS_RESOLVED_BY_ZPA' };
      if (value === 'REDIR_REQ') return { id: 'REDIR_REQ', name: 'REDIRECT_REQUEST' };
      return {};
    };

    return ({
      initialValues: {
        ruleOrder: { id: nextRule, name: nextRule },
        ruleName: nextName,
        ruleStatus: isNewRule
          ? { id: 'enabled', name: 'ENABLED' }
          : { id: status && status.toLowerCase(), name: status },
        locationGroup: isNewRule ? [] : locationGroups,
        locationName: isNewRule ? [] : locations,
        bcGroups: isNewRule ? [] : ecGroups,
        // eslint-disable-next-line max-len
        ipDestinationGroup: isNewRule ? [] : ipDestinationGroup.filter((x) => destIpGroupsId.includes(x.id)),
        destinationIpAddresses: isNewRule ? [] : destAddresses,
        networkTraffic: isNewRule ? { id: 'ALLOW', name: 'BOTH_REQ_RESP_ALLOW' } : getNetworkTraffic(action),
        ipPool: isNewRule ? ipPoolList[0] : zpaIpGroup,
        sourceIpAddresses: isNewRule ? [] : srcIps,
        srcIpGroups: isNewRule ? [] : srcIpGroups,
        sourceIpGroupExclusion: isNewRule || !sourceIpGroupExclusion ? 'INCLUDE' : 'EXCLUDE',
        pageTabs: 'GENERAL',
        tabConfiguration,
        isPageTabFullwidth: true,
        dnsGateway,
        description,
      },
      ...DnsPoliciesSelectors.baseSelector(state),
      sourceFormMode: SourceIPSelectors.formModeSelector(state),
      showSourceIpForm: SourceIPSelectors.showFormSelector(state),
      sourceIpFormTitle: SourceIPSelectors.formTitleSelector(state),
      destinationFormMode: DestinationIPSelectors.formModeSelector(state),
      showDestinationIpForm: DestinationIPSelectors.showFormSelector(state),
      destinationIpFormTitle: DestinationIPSelectors.formTitleSelector(state),
      ipAddressesList: DestinationIPSelectors.ipAddressesSelector(state),
      showNetworkServicesForm: NetworkServicesSelectors.showFormSelector(state),
      networkServicesFormTitle: NetworkServicesSelectors.modalTitleSelector(state),
      networkServiceTab: NetworkServicesSelectors.getPageTab(state),
      destinationIpAddresses: DnsPoliciesSelectors.formValuesSelector(
        state,
      )?.destinationIpAddresses,
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveDnsPolicy: saveForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deletePolicy,
    toggleSourceIPForm: sourceIpGroupsToggleForm,
    toggleDestinationIPForm: destinationIpGroupsToggleForm,
    networkServiceToggleCloseForm: networkServiceToggleClose,
    updatedCountryValues: updatedCountry,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({ ...state.dnsPolicies });

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DnsPoliciesForm));
