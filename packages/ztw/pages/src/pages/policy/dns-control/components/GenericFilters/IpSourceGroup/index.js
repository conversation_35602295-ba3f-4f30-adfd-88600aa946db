// @flow

import React from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import IpSourceGroupDropdown from 'commonConnectedComponents/dropdown/IpSourceGroupDropdown';
import { toggleForm } from 'ducks/sourceIPGroups';

const parseDropdownValues = (value) => get(value, 'original', value);

export function IpSourceGroup({ viewOnly, t, isZpa }) {
  const dispatch = useDispatch();
  
  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel text={t('SOURCE_IP_GROUPS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_SOURCE_IP_GROUPS')} />
        <Field
          id="srcIpGroups"
          name="srcIpGroups"
          component={IpSourceGroupDropdown}
          props={{
            isViewOnly: viewOnly,
            label: t('---'),
            addButton: !viewOnly,
            isZpa,
            onAddButtonClick: () => dispatch(toggleForm(null, true, 'ADD_SOURCE_IP')),
          }}
          parse={(values = []) => values.map(parseDropdownValues)} />
        
      </div>
      <span className="center-logical-label">{t('OR')}</span>
    </div>
  );
}

IpSourceGroup.propTypes = {
  isZpa: PropTypes.bool,
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
};

IpSourceGroup.defaultProps = {
  isZpa: false,
  t: (str) => str,
  viewOnly: false,
};

export default (withTranslation()(IpSourceGroup));
