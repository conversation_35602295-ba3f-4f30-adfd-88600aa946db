import React from 'react';
import { PropTypes } from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect, useSelector } from 'react-redux';
import { Field } from 'redux-form';
import PageTabs from 'components/navTabs/PageTabs';
import { isEmpty } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';
import * as dnsPoliciesSelectors from 'ducks/dnsPolicies/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import ECRadioGroup from 'components/ecRadioGroup';
import { withTranslation, useTranslation } from 'react-i18next';
import { FormSectionLabel } from 'components/label';
import {
  handleSourceIpGroupExclusionChange,
} from 'ducks/dnsPolicies';

import {
  ApplicationSegment,
  BCGroups,
  Description,
  DnsGateway,
  IpDestinationGroup,
  IpDestinationList,
  IpPool,
  IpSourceGroup,
  IpSourceList,
  Location,
  NetworkTraffic,
  RuleName,
  RuleOrder,
  RuleStatus,
} from '..';

export function DnsPoliciesFragment(props) {
  const { dnsPolicies } = useSelector((state) => state);
  const configData = useSelector((state) => loginSelectors.configDataSelector(state));
  const {
    viewOnly, form, formMeta, formSyncErrors, actions,
  } = props;
  const {
    pageTabs, networkTraffic = {}, ipPool = {}, sourceIpGroupExclusion, dnsGateway,
  } = form || {};
  const { appData } = dnsPolicies || {};
  const {
    name, order, state, defaultRule,
    isDisablablePredefinied, destIpGroups,
    isWanCtrRule, isDisableablePredefiniedZTW3626,
  } = appData || {};
  const { t } = useTranslation();

  const disableExcludeSourceIp = verifyConfigData({ configData, key: 'disableExcludeSourceIp' });
  const enablePredefinedPolicy = verifyConfigData({ configData, key: 'enablePredefinedPolicy' });

  return (
    <div>
      <FormSectionLabel text={t('DNS_FILTERING_RULE')} />
      <div className="form-section dns-policies">
        <div className="g-row">
          <RuleOrder
            value={order}
            viewOnly={viewOnly || defaultRule} />
          <span className="email-input-view-at">{' '}</span>
          <RuleName
            value={name}
            viewOnly={viewOnly || defaultRule || isWanCtrRule} />
        </div>
        <div className="g-row">
          <RuleStatus
            value={state}
            viewOnly={viewOnly || defaultRule} />
        </div>
      </div>

      <FormSectionLabel text={t('CRITERIA')} />
      <div className="form-tab-group">
        <Field
          id="pageTabs"
          name="pageTabs"
          component={PageTabs} />
      </div>
      <div className="form-section">
        {pageTabs === 'GENERAL' ? (
          <>
            <div className="g-row">
              <Location
                viewOnly={(viewOnly || defaultRule || isWanCtrRule)
                    && (!(isDisableablePredefiniedZTW3626 && enablePredefinedPolicy))}
                meta={formMeta.locationName}
                disabled={!isEmpty(form.bcGroups)}
                error={formSyncErrors.locationName} />
              {/* <span className="center-logical-label">{t('OR')}</span>
              <LocationGroup
                disabled={true || !isEmpty(form.bcGroups)}
                viewOnly={viewOnly} /> */}
            </div>
            <div className="g-row">
              <span className="center-logical-label">{t('OR')}</span>
              <BCGroups
                disabled={!isEmpty(form.locationName)}
                viewOnly={(viewOnly || defaultRule || isWanCtrRule)
                  && (!(isDisableablePredefiniedZTW3626 && enablePredefinedPolicy))} />
            </div>
          </>
        )
          : (<></>)}
        <div className={pageTabs === 'DNS_APPLICATION' ? '' : 'hidden'}>
          {['BLOCK', 'REDIR_ZPA'].includes(networkTraffic?.id) && (
            <div className="g-row">
              <ApplicationSegment />
            </div>
          )}
          {['BLOCK', 'ALLOW', 'REDIR_REQ'].includes(networkTraffic?.id) && (
            <>
              <div className="g-row">
                <IpDestinationGroup
                  destIpGroups={destIpGroups}
                  ruleName={name}
                  viewOnly={viewOnly || defaultRule || isDisablablePredefinied || isWanCtrRule} />
              </div>
              <IpDestinationList
                viewOnly={viewOnly || defaultRule || isDisablablePredefinied || isWanCtrRule} />
            </>
          )}
        </div>
        <div className={pageTabs === 'SOURCE' ? 'dns-source' : 'hidden'}>
          {!disableExcludeSourceIp && (
            <ECRadioGroup
              id="sourceIpGroupExclusion"
              name="sourceIpGroupExclusion"
              disabled={['REDIR_ZPA'].includes(networkTraffic?.id) || isWanCtrRule}
              onChange={actions.handleSourceIpGroupExclusionChange}
              options={[{
                name: 'sourceIpGroupExclusion',
                value: 'INCLUDE',
                checked: sourceIpGroupExclusion === 'INCLUDE',
                label: t('INCLUDE'),
              },
              {
                name: 'sourceIpGroupExclusion',
                value: 'EXCLUDE',
                checked: sourceIpGroupExclusion === 'EXCLUDE',
                label: t('EXCLUDE'),
              },
              ]} />
          )}
          <IpSourceGroup
            isZpa={!disableExcludeSourceIp && ['REDIR_ZPA'].includes(networkTraffic?.id)}
            viewOnly={viewOnly || defaultRule || isDisablablePredefinied || isWanCtrRule} />
          <IpSourceList
            viewOnly={viewOnly || defaultRule || isDisablablePredefinied || isWanCtrRule} />
        </div>
      </div>

      <FormSectionLabel text={t('ACTION')} />
      <div className="form-section">
        <div className="g-row">
          <NetworkTraffic
            value={t(networkTraffic.name)}
            defaultRule={defaultRule}
            viewOnly={viewOnly || isWanCtrRule} />
          <span className="email-input-view-at">{' '}</span>
          {networkTraffic.id === 'REDIR_REQ'
            && (
              <DnsGateway
                value={t(dnsGateway?.name)}
                error={formSyncErrors.dnsGateway}
                viewOnly={viewOnly || isWanCtrRule} />
            )}
          <IpPool value={t(ipPool.name)} viewOnly={viewOnly || isWanCtrRule} />
        </div>
      </div>

      <Description
        t={t}
        error={formSyncErrors.description}
        viewOnly={viewOnly || defaultRule || isWanCtrRule} />
    </div>
    
  );
}

DnsPoliciesFragment.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.shape({
    bcGroups: PropTypes.arrayOf(),
    locationName: PropTypes.string,
  }),
  formMeta: PropTypes.shape({
    locationName: PropTypes.shape({}),
  }),
  formSyncErrors: PropTypes.shape({
    description: PropTypes.string,
    dnsGateway: PropTypes.string,
    locationName: PropTypes.string,
  }),
  actions: PropTypes.shape({
    handleSourceIpGroupExclusionChange: PropTypes.func,
  }),
};
    
DnsPoliciesFragment.defaultProps = {
  viewOnly: true,
  form: {},
  formMeta: {},
  formSyncErrors: {},
  actions: ({
    handleSourceIpGroupExclusionChange: null,
  }),
};

const mapStateToProps = (state) => ({
  viewOnly: dnsPoliciesSelectors.viewOnlySelector(state),
  form: dnsPoliciesSelectors.formValuesSelector(state),
  formMeta: dnsPoliciesSelectors.formMetaSelector(state),
  formSyncErrors: dnsPoliciesSelectors.formSyncErrorsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleSourceIpGroupExclusionChange,
  }, dispatch);
  return { actions };
};

// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DnsPoliciesFragment));
