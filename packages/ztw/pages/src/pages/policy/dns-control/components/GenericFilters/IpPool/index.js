import React from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { Field, change } from 'redux-form';
import { isEmpty } from 'utils/lodash';
import * as dnsPoliciesSelectors from 'ducks/dnsPolicies/selectors';
import IpPoolDropDown from 'commonConnectedComponents/dropdown/IpPoolDropDown';
import { required } from 'utils/validations';

function IpPool({ value, viewOnly, t }) {
  const dispatch = useDispatch();
  const { networkTraffic, ipPool } = useSelector(
    (state) => dnsPoliciesSelectors.formValuesSelector(state),
  ) || {};
  const { data: ipPoolList } = useSelector((state) => state.ipPoolDropdown) || {};
  
  if (isEmpty(ipPoolList)) return <></>;
  if (isEmpty(ipPool) && !isEmpty(ipPoolList)) dispatch(change('addEditDnsFilteringRules', 'ipPool', ipPoolList[0]));

  if (networkTraffic?.id !== 'REDIR_ZPA') return <></>;
  
  return (
    <div className={`g-right ${networkTraffic.id !== 'REDIR_ZPA' ? 'invisible' : ''}`}>
      {!viewOnly
        ? (
          <>
            <FormFieldLabel
              text={t('IP_POOL')}
              tooltip={t('TOOLTIP_POLICY_FIREWALL_IPPOOL')} />
            <Field
              id="ipPool"
              name="ipPool"
              component={IpPoolDropDown}
              validate={[
                required,
              ]}
              styleClass="max-width" />
          </>
        ) : (
          <>
            <FormFieldLabel
              text={t('IP_POOL')}
              tooltip={t('TOOLTIP_POLICY_FIREWALL_IPPOOL')} />
            <p className="disabled-input">{value || '---'}</p>
          </>
        )}
    </div>
  );
}

IpPool.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
IpPool.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
};

export default withTranslation()(IpPool);
