import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import NetworkTrafficDropDown from 'commonConnectedComponents/dropdown/NetworkTrafficDropdown';
import {
  required,
} from 'utils/validations';

function NetworkTraffic({
  value, viewOnly, t, onChange, defaultRule,
}) {
  const toolTipText = t('TOOLTIP_POLICY_FIREWALL_DEFAULT_ACTION_NW_TRAFFIC').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <br />
      <br />
      <b>{toolTipText[1]}</b>
      {toolTipText[2]}
      <br />
      <b>{toolTipText[3]}</b>
      {toolTipText[4]}
      <br />
      <b>{toolTipText[5]}</b>
      {toolTipText[6]}
      <br />
      <b>{toolTipText[7]}</b>
      {toolTipText[8]}
    </>
  );

  return (
    <div className="g-left">
      <FormFieldLabel text={t('NETWORK_TRAFFIC')} tooltip={toolTipJSX} />
      {!viewOnly
        ? (
          <Field
            id="networkTraffic"
            name="networkTraffic"
            onChange={onChange}
            defaultRule={defaultRule}
            component={NetworkTrafficDropDown}
            validate={[required]} />
        ) : (
          <p className="disabled-input">{ value || '---' }</p>
        )}
    </div>
  );
}
  
NetworkTraffic.propTypes = {
  defaultRule: PropTypes.bool,
  onChange: PropTypes.func,
  t: PropTypes.func,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
};
  
NetworkTraffic.defaultProps = {
  defaultRule: false,
  onChange: null,
  t: (str) => str,
  value: '',
  viewOnly: false,
};

export default withTranslation()(NetworkTraffic);
