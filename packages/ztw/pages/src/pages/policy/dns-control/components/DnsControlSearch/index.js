// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  reduxForm, Field, change, getFormValues,
} from 'redux-form';
import { useDispatch, connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { handleOnSearchFilter, toggleAddForm } from 'ducks/dnsPolicies';
import { withTranslation } from 'react-i18next';
import AddNewButton from 'components/addNewButton';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import NetworkTrafficDropDown from 'commonConnectedComponents/dropdown/NetworkTrafficDropdown';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/pro-regular-svg-icons';

import SearchDropdown from './SearchDropdown';

export function BasicCustomAppForm(props) {
  const {
    t, isReadOnly, actions, formValues,
  } = props;
  const { searchParameter } = formValues || {};
  const { id: searchParameterId } = searchParameter || {};
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(change('searchParameterForm', 'searchParameter', { id: 'ruleName', name: 'RULE_NAME' }));
  }, []);

  const handleClear = () => {
    dispatch(change('searchParameterForm', 'searchParameter', { id: 'ruleAction', name: 'ACTION' }));
    dispatch(change('searchParameterForm', 'ruleAction', null));
    actions.handleOnSearchFilter({ keyCode: 13 }, '');
  };

  return (
    <div className="table-layout-header">
      <div className="controls-container">
        <div className="grid-toolbar-left">
          {!isReadOnly && <AddNewButton label={t('ADD_DNS_POLICIES')} clickCallback={() => actions.toggleAddForm(true, {}, 'ADD_CLOUD_CONNECTOR_ROLE')} />}
        </div>
        <div className="grid-toolbar-right display-inline-flex">
          <span>
            {t('SEARCH_BY')}
          </span>
          <Field
            id="searchParameter"
            name="searchParameter"
            className="search-parameter-dropdown"
            component={SearchDropdown} />
          <span className="min-width-8px" />
          {searchParameterId !== 'ruleAction' && (
            <SimpleSearchInput
              withButton
              showCancel={actions.showCancel}
              onKeyPressCb={actions.handleOnSearchFilter} />
          )}
          {searchParameterId === 'ruleAction' && (
            <>
              <Field
                id="ruleAction"
                name="ruleAction"
                onChange={(value) => actions.handleOnSearchFilter({ keyCode: 13 }, value?.id)}
                component={NetworkTrafficDropDown}
                className="search-parameter-dropdown" />
              <FontAwesomeIcon
                icon={faTrash}
                title="clear"
                // eslint-disable-next-line react/jsx-handler-names
                onClick={handleClear}
                className="delete-icon" />
            </>
          )}
        </div>
      </div>
    </div>
  );
}

BasicCustomAppForm.propTypes = {
  t: PropTypes.func,
  actions: PropTypes.shape(),
  formValues: PropTypes.shape(),
  isReadOnly: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  t: null,
  actions: {},
  formValues: {},
  isReadOnly: true,
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...state.locations,
    formValues: getFormValues('searchParameterForm')(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAddForm,
    handleOnSearchFilter,
  }, dispatch);
  
  return {
    actions,
  };
};

const DnsControlSeach = reduxForm({
  form: 'searchParameterForm',
})(BasicCustomAppForm);

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(DnsControlSeach),
);
