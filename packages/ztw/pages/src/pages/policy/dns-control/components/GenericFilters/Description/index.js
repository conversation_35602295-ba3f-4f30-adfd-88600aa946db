import React from 'react';
import { PropTypes } from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FormSectionLabel } from 'components/label';
import { Field } from 'redux-form';
import {
  maxLength,
} from 'utils/validations';

const maxLength10240 = maxLength(10240);

function Description({ viewOnly, error, t }) {
  const hasError = !!error;
  
  return (
    <>
      <FormSectionLabel
        error={hasError ? t(error) : null}
        styleClass={`${hasError ? 'invalid' : ''}`}
        text={t('DESCRIPTION')}
        tooltip={t('TOOLTIP_POLICY_FIREWALL_DESCRIPTION')} />
      <div className="form-section no-padding">
        <div className="g-row">
          <div className="g-fullwidth no-padding">
            <Field
              id="description"
              name="description"
              component="textarea"
              className="form-textarea"
              props={{ disabled: viewOnly }}
              validate={[maxLength10240]} />
          </div>
        </div>
      </div>
    </>
  );
}
Description.propTypes = {
  error: PropTypes.string,
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
};

Description.defaultProps = {
  error: null,
  t: (str) => str,
  viewOnly: false,
};

export default withTranslation()(Description);
