import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { checkArraySize } from 'utils/validations';
import LocationsDropdown from 'commonConnectedComponents/dropdown/LocationsDropdownExcludeDeletingEntries';
import LocationsSublocationsDropdown from 'commonConnectedComponents/dropdown/LocationsSublocationsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);
const maxArrayOf8 = (value) => checkArraySize(value, 8);
const noDeletedLocation = (value) => {
  return value.some((item) => item && item.deleted) ? 'PLEASE_REMOVE_DELETED_LOCATION' : null;
};

export function Location({
  t, viewOnly, error, disabled,
}) {
  const hasError = !!error;

  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const disableSublocation = verifyConfigData({ configData, key: 'disableSublocation' });

  return (
    <div className={`g-left ${hasError ? 'error' : ''} `}>
      <FormFieldLabel
        text={t(disableSublocation ? 'LOCATION' : 'LOCATION_SUBLOCATION')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? (t(error)).replace('{0}', '8') : null}
        tooltip={t('TOOLTIP_POLICY_FIREWALL_LOCATION')} />
      <Field
        id="locationName"
        name="locationName"
        component={disableSublocation ? LocationsDropdown : LocationsSublocationsDropdown}
        props={{
          disabled,
          isViewOnly: viewOnly,
          label: t('---'),
          classes: hasError ? ['error'] : [],
        }}
        validate={[maxArrayOf8, noDeletedLocation]}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Location.propTypes = {
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
  error: PropTypes.string,
  disabled: PropTypes.bool,
};

Location.defaultProps = {
  t: (str) => str,
  viewOnly: false,
  error: null,
  disabled: false,
};

export default (withTranslation()(Location));
