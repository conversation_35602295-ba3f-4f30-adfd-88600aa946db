import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import ForwardingMethodDropdown from 'commonConnectedComponents/dropdown/ForwardingMethodDropdownList';
import {
  requiredId,
} from 'utils/validations';
import { useSelector } from 'react-redux';
import { accessSubscriptionSelector } from 'ducks/login/selectors';
import { has5Gsku } from 'utils/helpers';

function ForwardingMethod({
  value, viewOnly, t, meta, error, onChange,
}) {
  const subscriptions = useSelector(accessSubscriptionSelector);
  const has5G = has5Gsku(subscriptions);
  
  const hasError = meta.touched && !!error;
  const toolTipText = t('TOOLTIP_POLICY_FIREWALL_FORWARDING_METHOD').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <br />
      <br />
      {/* direct */}
      <b>{toolTipText[1]}</b>
      {toolTipText[2]}
      <br />
      <b>{toolTipText[3]}</b>
      {toolTipText[4]}
      <br />
      {has5G && (
        <>
          <b>{toolTipText[5]}</b>
          {toolTipText[6]}
          <br />
        </>
      )}
      {/* zia */}
      <b>{toolTipText[7]}</b>
      {toolTipText[8]}
      <br />
      <b>{toolTipText[9]}</b>
      {toolTipText[10]}
      <br />
      {has5G && (
        <>
          <b>{toolTipText[11]}</b>
          {toolTipText[12]}
          <br />
        </>
      )}
      <b>{toolTipText[13]}</b>
      {toolTipText[14]}
      {toolTipText[15]}
    </>
  );

  return (
    <div className="g-right modal-forwarding-method">
      <FormFieldLabel
        text={t('TRAFFIC_FORWARDING_METHOD')}
        error={hasError ? t(error) : null}
        styleClass={`${hasError ? 'invalid' : ''}`}
        tooltip={toolTipJSX} />
      {!viewOnly
        ? (
          <Field
            id="forwardingMethod"
            name="forwardingMethod"
            onChange={onChange}
            component={ForwardingMethodDropdown}
            styleClass={`${hasError ? 'forwardingMethod invalid' : 'forwardingMethod'}`}
            validate={[requiredId]} />
        ) : (
          <p className="disabled-input">{ value }</p>
        )}
    </div>
  );
}
  
ForwardingMethod.propTypes = {
  actions: PropTypes.shape({}),
  error: PropTypes.string,
  meta: PropTypes.shape({
    touched: PropTypes.bool,
  }),
  onChange: PropTypes.func,
  t: PropTypes.func,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
};
  
ForwardingMethod.defaultProps = {
  actions: {},
  error: null,
  meta: {},
  onChange: null,
  t: (str) => str,
  value: '',
  viewOnly: false,
};

export default withTranslation()(ForwardingMethod);
