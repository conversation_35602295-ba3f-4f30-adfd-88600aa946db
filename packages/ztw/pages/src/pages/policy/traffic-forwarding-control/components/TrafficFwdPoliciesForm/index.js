// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import i18n from 'utils/i18n';
import { reduxForm } from 'redux-form';
import Modal from 'components/modal';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop, cloneDeep, isEmpty } from 'utils/lodash';
import * as TrafficFwdPoliciesSelectors from 'ducks/trafficFwdPolicies/selectors';
import * as SourceIPSelectors from 'ducks/sourceIPGroups/selectors';
import * as DestinationIPSelectors from 'ducks/destinationIpGroups/selectors';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';

import { updatedCountry, toggleForm as destinationIpGroupsToggleForm } from 'ducks/destinationIpGroups';
import { toggleForm as sourceIpGroupsToggleForm } from 'ducks/sourceIPGroups';
import { toggleClose as networkServiceToggleClose } from 'ducks/networkServices';
import validateAppSegmentAndGroup from 'utils/helpers/validateAppSegmentAndGroup';
import {
  saveForm,
  toggleClose,
  toggleDeleteForm,
  deletePolicy,
} from 'ducks/trafficFwdPolicies';

import { SourceIPForm } from 'pages/administration/source-ip-groups/components';
import { DestinationIPForm } from 'pages/administration/destination-ip-groups/components';

import {
  NetworkServiceForm,
  NetworkServiceGroupForm,
} from 'pages/administration/network-services/components';

// eslint-disable-next-line import/no-cycle
import TrafficFwdPoliciesFragment from '../TrafficFwdPoliciesFragment';
 
export function BasicCustomAppForm(props) {
  const {
    valid,
    actions,
    t,
    handleClose,
    handleSubmit,
    appData,
    addPolicy,
    duplicateRow,
    viewOnly,
    showDeleteForm,
    selectedRowID,
    modalLoading,
    sourceFormMode,
    sourceIpFormTitle,
    showSourceIpForm,
    destinationFormMode,
    destinationIpFormTitle,
    showDestinationIpForm,
    networkServicesFormTitle,
    showNetworkServicesForm,
    ipAddressesList,
    networkServiceTab,
    updatedCountryValues,
  } = props;

  const {
    order, defaultRule, predefined, isDisablablePredefinied,
  } = appData;
  const isNetworkServiceTab = networkServiceTab === 'NETWORK_SERVICES';

  const {
    handleDelete,
    handleDeleteConfirmationForm,
    toggleDestinationIPForm,
    toggleSourceIPForm,
    networkServiceToggleCloseForm,
  } = actions;

  const onSubmit = () => {
    actions.saveTrafficFwdPolicy();
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form firewall-control-dns-policy-edit">
        <div className="form-sections-container">
          <Loading {...props} />
          <TrafficFwdPoliciesFragment />
        </div>
        <div className="dialog-footer white-background">
          <div className="dialog-footer-left">
            {!viewOnly && (
              <button
                type="submit"
                disabled={!valid}
                // onClick={onSubmit}
                className={`submit ${!valid ? 'disabled' : ''}`}>
                {t('SAVE')}
              </button>
            )}
            <button
              type="button"
              className="cancel"
              onClick={handleClose}>
              {t('CANCEL')}
            </button>
          </div>
          {/* <div className="dialog-footer-right">
            {!viewOnly && !addPolicy && !duplicateRow && !defaultRule
              && !predefined && order > 0 && !isDisablablePredefinied
           && (
             <button
               type="button"
               className="button big delete"
               onClick={() => handleDeleteConfirmationForm(true, appData)}>
               {t('DELETE')}
             </button>
           )}
          </div> */}
        </div>
      </form>
      <Modal
        title={t('DELETE_CONFIRMATION')}
        isOpen={showDeleteForm}
        closeModal={() => handleDeleteConfirmationForm(false)}>
        <DeleteConfirmationForm
          modalLoading={modalLoading}
          selectedRowID={selectedRowID}
          handleCancel={() => handleDeleteConfirmationForm(false)}
          handleDelete={handleDelete} />
      </Modal>

      <Modal
        title={t(networkServicesFormTitle)}
        isOpen={showNetworkServicesForm}
        closeModal={() => networkServiceToggleCloseForm()}>
        {isNetworkServiceTab ? <NetworkServiceForm /> : <NetworkServiceGroupForm />}
      </Modal>

      <Modal
        title={t(destinationIpFormTitle)}
        isOpen={showDestinationIpForm}
        styleClass="destination-ip-form"
        closeModal={() => toggleDestinationIPForm(null, false)}>
        <DestinationIPForm
          formMode={destinationFormMode}
          ipAddressesList={ipAddressesList}
          updatedCountryValues={updatedCountryValues} />
      </Modal>

      <Modal
        title={t(sourceIpFormTitle)}
        isOpen={showSourceIpForm}
        closeModal={() => toggleSourceIPForm(null, false)}>
        <SourceIPForm formMode={sourceFormMode} ipAddressesList={ipAddressesList} />
      </Modal>
    </>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    addDestIpGroups: PropTypes.func,
    cancelHandle: PropTypes.func,
    handleDelete: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    networkServiceToggleCloseForm: PropTypes.func,
    saveTrafficFwdPolicy: PropTypes.func,
    toggleDestinationIPForm: PropTypes.func,
    toggleSourceIPForm: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  handleClose: PropTypes.func,
  trafficFwdPolicies: PropTypes.shape({}),
  appData: PropTypes.shape({
    order: PropTypes.number,
    defaultRule: PropTypes.bool,
    predefined: PropTypes.bool,
    isDisablablePredefinied: PropTypes.bool,
  }),
  addPolicy: PropTypes.bool,
  duplicateRow: PropTypes.bool,
  viewOnly: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  selectedRowID: PropTypes.number,
  modalLoading: PropTypes.bool,
  sourceFormMode: PropTypes.string,
  sourceIpFormTitle: PropTypes.string,
  showSourceIpForm: PropTypes.bool,
  destinationFormMode: PropTypes.string,
  destinationIpFormTitle: PropTypes.string,
  showDestinationIpForm: PropTypes.bool,
  showNetworkServicesForm: PropTypes.bool,
  networkServicesFormTitle: PropTypes.string,
  ipAddressesList: PropTypes.arrayOf(PropTypes.string),
  networkServiceTab: PropTypes.string,
  updatedCountryValues: PropTypes.func,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    addDestIpGroups: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: null,
  handleClose: null,
  trafficFwdPolicies: {},
  appData: {},
  addPolicy: false,
  duplicateRow: false,
  viewOnly: true,
  showDeleteForm: false,
  selectedRowID: null,
  modalLoading: false,
  sourceFormMode: '',
  sourceIpFormTitle: '',
  showSourceIpForm: false,
  destinationFormMode: '',
  destinationIpFormTitle: '',
  showDestinationIpForm: false,
  showNetworkServicesForm: false,
  networkServicesFormTitle: '',
  ipAddressesList: [],
  networkServiceTab: '',
  updatedCountryValues: noop,
};

const BasicCustomForm = reduxForm({
  form: 'addEditTrafficFwdFilteringRules',
  validate: validateAppSegmentAndGroup,
})(BasicCustomAppForm);

const TrafficFwdPoliciesForm = connect(
  (state) => {
    const {
      numberOfOrderedLines,
      appData,
      addPolicy,
      duplicateRow,
    } = TrafficFwdPoliciesSelectors.baseSelector(state);

    const { cachedData: countries } = state.countryDropdown || [];
    const appDataClone = cloneDeep(appData);
    
    const {
      order,
      name,
      state: status,
      ecGroups = [],
      forwardMethod,
      proxyGateway = {},
      locationGroups = [],
      locations = [],
      nwServices = [],
      nwServiceGroups = [],
      sourceIpGroupExclusion,
      srcIps = [],
      srcIpGroups = [],
      destAddresses = [],
      destCountries = [],
      destIpGroups = [],
      destWorkloadGroups = [],
      srcWorkloadGroups = [],
      zpaApplicationSegments = [],
      zpaApplicationSegmentGroups = [],
      description = '',
      appServiceGroups,
      wanSelection,
    } = appDataClone;

    const nextRule = addPolicy || duplicateRow
      ? numberOfOrderedLines + 1
      : order;

    const nextName = addPolicy ? `FWD_${numberOfOrderedLines + 1}` : name;
    const isNewRule = addPolicy && !duplicateRow;
    const newDestIpGroups = destIpGroups.map((x) => ({ ...x, parent: 'ORIGINAL' }));
    let updatedEcGroups = ecGroups;
    let updatedLocations = locations;
    if (!isEmpty(destWorkloadGroups)) {
      updatedEcGroups = ecGroups?.map(
        (item) => ({ ...item, extensions: { deployType: 'CLOUD' } }),
      );
    }
    if (!isEmpty(destWorkloadGroups)) {
      updatedLocations = locations?.map(
        (item) => ({ ...item, ccLocation: true }),
      );
    }

    return ({
      initialValues: {
        ruleOrder: { id: nextRule, name: nextRule },
        ruleName: nextName,
        forwardingMethod: isNewRule ? {} : { id: forwardMethod, name: forwardMethod },
        ruleStatus: isNewRule
          ? { id: 'enabled', name: 'ENABLED' }
          : { id: status && status.toLowerCase(), name: status },
        locationGroup: isNewRule ? [] : locationGroups,
        locationName: isNewRule ? [] : updatedLocations,
        bcGroups: isNewRule ? [] : updatedEcGroups,
        gateway: isNewRule ? {} : proxyGateway,
        networkServices: isNewRule ? [] : nwServices,
        networkServicesGroup: isNewRule ? [] : nwServiceGroups,
        pageTabs: 'GENERAL',
        destinationIpAddresses: isNewRule ? [] : destAddresses,
        countries: isNewRule ? [] : countries.filter((x) => destCountries.includes(x.name)),
        // eslint-disable-next-line max-len
        ipDestinationGroup: isNewRule ? [] : newDestIpGroups,
        sourceIpAddresses: isNewRule ? [] : srcIps,
        sourceIpGroupExclusion: isNewRule || !sourceIpGroupExclusion ? 'INCLUDE' : 'EXCLUDE',
        ipSourceGroup: isNewRule ? [] : srcIpGroups,
        destinationWorkloadGroups: isNewRule ? [] : destWorkloadGroups,
        sourceWorkloadGroups: isNewRule ? [] : srcWorkloadGroups,
        isPageTabFullwidth: true,
        allAppSegments: isNewRule ? true : zpaApplicationSegments.some((x) => x.zpaId === 1),
        zpaApplicationSegments: isNewRule ? [] : zpaApplicationSegments.filter((x) => x.zpaId !== 1),
        zpaApplicationSegmentGroups: isNewRule ? [] : zpaApplicationSegmentGroups,
        description,
        appServiceGroups: isNewRule ? [] : appServiceGroups,
        wanSelection: isNewRule || !wanSelection
          ? ({ id: 'NONE', name: i18n.t('NONE') })
          : ({ id: wanSelection, name: i18n.t('wanSelection') }),
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveTrafficFwdPolicy: saveForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deletePolicy,
    toggleSourceIPForm: sourceIpGroupsToggleForm,
    toggleDestinationIPForm: destinationIpGroupsToggleForm,
    networkServiceToggleCloseForm: networkServiceToggleClose,
    updatedCountryValues: updatedCountry,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...state.trafficFwdPolicies,
  sourceFormMode: SourceIPSelectors.formModeSelector(state),
  showSourceIpForm: SourceIPSelectors.showFormSelector(state),
  sourceIpFormTitle: SourceIPSelectors.formTitleSelector(state),
  destinationFormMode: DestinationIPSelectors.formModeSelector(state),
  showDestinationIpForm: DestinationIPSelectors.showFormSelector(state),
  destinationIpFormTitle: DestinationIPSelectors.formTitleSelector(state),
  ipAddressesList: DestinationIPSelectors.ipAddressesSelector(state),
  showNetworkServicesForm: NetworkServicesSelectors.showFormSelector(state),
  networkServicesFormTitle: NetworkServicesSelectors.modalTitleSelector(state),
  networkServiceTab: NetworkServicesSelectors.getPageTab(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(TrafficFwdPoliciesForm));
