/* eslint-disable no-nested-ternary */
import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import TrafficFwdPoliciesDropDown from 'commonConnectedComponents/dropdown/TrafficFwdPoliciesDropdown';
import {
  required,
} from 'utils/validations';

function RuleOrder({ value, viewOnly, t }) {
  return (
    <div className="g-left">
      {!viewOnly
        ? (
          <>
            <FormFieldLabel text={t('RULE_ORDER')} tooltip={t('TOOLTIP_POLICY_FIREWALL_RULE_ORDER')} />
            <Field
              id="ruleOrder"
              name="ruleOrder"
              component={TrafficFwdPoliciesDropDown}
              validate={[
                required,
              ]}
              styleClass="max-width" />
          </>
        ) : (
          <>
            <FormFieldLabel text={t('RULE_ORDER')} tooltip={t('TOOLTIP_POLICY_FIREWALL_RULE_ORDER')} />
            <p className="disabled-input">{((value === -1) ? t('ORDER_DEFAULT') : (value <= -2) ? t('PREDEFINED') : value)}</p>
          </>
        )}
    </div>
  );
}

RuleOrder.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
RuleOrder.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
};

export default withTranslation()(RuleOrder);
