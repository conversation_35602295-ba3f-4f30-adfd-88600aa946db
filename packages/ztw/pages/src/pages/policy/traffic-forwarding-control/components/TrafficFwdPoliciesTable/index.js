import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop, isEmpty } from 'utils/lodash';
import { getReadOnly, verifyConfigData } from 'utils/helpers';
import { isRuleOrder5g, TRAFFIC_FWD_POLICIES_TABLE_CONFIGS } from 'ducks/trafficFwdPolicies/constants';
import { dropdownActions as countryActions } from 'ducks/dropdowns/country';
import { dropdownActions as destinationGroupActions } from 'ducks/dropdowns/ip-destination-group';
import * as loginSelectors from 'ducks/login/selectors';
import * as TrafficFwdPoliciesSelector from 'ducks/trafficFwdPolicies/selectors';
import {
  deletePolicy,
  handleOnSearchFilter,
  handlePageNumber,
  handlePageSize,
  loader,
  toggleAddForm,
  toggleClearTable,
  toggleClose,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleEditForm,
  toggleForm,
  toggleRefreshTable,
  toggleSortBy,
  toggleViewForm,
} from 'ducks/trafficFwdPolicies';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';

function TrafficFwdPoliciesTable(props) {
  const {
    accessPermissions,
    authType,
    handleDeleteConfirmationForm,
    handleSearchFilter,
    handleSortBy,
    handleToggleDuplicateForm,
    handleToggleEditCopyForm,
    handleToggleEditForm,
    handleToggleViewForm,
    load,
    loading,
    numberOfLines,
    onHandlePageNumber,
    onHandlePageSize,
    pageNumber,
    pageSize,
    searchData,
    sortDirection,
    sortField,
    t,
    trafficFwdPoliciesTableData,
  } = props;
  
  const dispatch = useDispatch();

  useEffect((e) => {
    dispatch(handleSearchFilter(e, ''));
    dispatch(countryActions.load());
    dispatch(destinationGroupActions.load());
    // load(true, 1);
  }, []);
  
  useEffect(() => {
    if (loading) return;
    load(true, 1);
  }, [sortField, sortDirection]);
  
  const getTableData = () => {
    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_FORWARDING, authType);

    const local = localStorage.getItem('configData');
    const configData = (local && local.length) ? JSON.parse(local) : {};
    const enablePredefinedPolicy = verifyConfigData({ configData, key: 'enablePredefinedPolicy' });
      
    const tableData = trafficFwdPoliciesTableData
    //   .filter(
    //   row => row.name.toLowerCase().includes(searchData.toLowerCase())
    //     || row.order.toString().toLowerCase().includes(searchData.toLowerCase()),
    // )
      .map((row) => {
        const {
          // applications, protocols, zpaIpGroup, proxyGateway,
          ecGroups, locations, locationGroups,
          zpaAppSegments, destAddresses, destCountries, destIpGroups, nwServiceGroups, nwServices,
          srcIps, srcIpGroups, zpaApplicationSegments, zpaApplicationSegmentGroups, order,
          appServiceGroups, destWorkloadGroups, srcWorkloadGroups,
        } = row || {};
        const additionalLine = 60;
          
        const rowHeight = (isEmpty(ecGroups) ? 0 : additionalLine)
          + (isEmpty(zpaAppSegments) ? 0 : additionalLine)
          + (isEmpty(destAddresses) ? 0 : additionalLine)
          + (isEmpty(destCountries) ? 0 : additionalLine)
          + (isEmpty(destIpGroups) ? 0 : additionalLine)
          + (isEmpty(destWorkloadGroups) ? 0 : additionalLine)
          + (isEmpty(locations) ? 0 : additionalLine)
          + (isEmpty(locationGroups) ? 0 : additionalLine)
          + (isEmpty(appServiceGroups) ? 0 : additionalLine)
          + (isEmpty(nwServiceGroups) ? 0 : additionalLine)
          + (isEmpty(nwServices) ? 0 : additionalLine)
          + (isEmpty(srcIps) ? 0 : additionalLine)
          + (isEmpty(srcIpGroups) ? 0 : additionalLine)
          + (isEmpty(srcWorkloadGroups) ? 0 : additionalLine)
          + (isEmpty(zpaApplicationSegments) ? 0 : additionalLine)
          + (isEmpty(zpaApplicationSegmentGroups) ? 0 : additionalLine)
          + (isRuleOrder5g(order) ? additionalLine : 0);
          
        const isRowReadOnly = isReadOnly || row.is5GPredefined || row.name === 'ZPA Pool For Stray Traffic';
          
        return {
          ...row,
          id: row.id,
          isEditable: !(isRowReadOnly
            || (row.isDisableablePredefiniedZTW3626 && !enablePredefinedPolicy)),
          isDeletable: !isRowReadOnly && !row.isDisablablePredefinied
            && !row.defaultRule && row.order > 0,
          isCopyable: !isRowReadOnly && !row.defaultRule
                       && !row.predefined && !row.is5GPredefined && !row.isDisablablePredefinied,
          tooltip: row.predefined ? t('TOOLTIP_POLICY_FIREWALL_RULE_ZPA_RESOLVER') : '',
          isReadOnly: isRowReadOnly
            || (row.isDisableablePredefiniedZTW3626 && !enablePredefinedPolicy),
          rowHeight: rowHeight > additionalLine ? rowHeight : additionalLine,
        };
      });
    return tableData;
  };
      
  return (
    <ConfigTableWithPaginationAndSort
      {...TRAFFIC_FWD_POLICIES_TABLE_CONFIGS}
      key={searchData}
      data={getTableData()}
      tableHeight={20000}
      maxTableHeight="20000px"
      onHandleRowDelete={(e) => handleDeleteConfirmationForm(true, e)}
      onHandleRowView={(e) => handleToggleViewForm(true, e)}
      onHandleRowEdit={(e) => handleToggleEditForm(true, e)}
      onHandleDuplicateRow={(e) => handleToggleDuplicateForm(true, e)}
      handleEditAction={handleToggleEditCopyForm}
      handleCopyAction={handleToggleEditCopyForm}
      onHandleSortBy={handleSortBy}
      isApiPagination
      numberOfLines={numberOfLines}
      pageChangeHandler={(x) => onHandlePageNumber(x)}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(x) => onHandlePageSize(x)}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection} />
    
  );
}

const mapStateToProps = (state) => ({
  ...TrafficFwdPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    handleDelete: deletePolicy,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSearchFilter: handleOnSearchFilter,
    handleSortBy: toggleSortBy,
    handleToggleAddForm: toggleAddForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleViewForm: toggleViewForm,
    load: loader,
    onHandlePageNumber: handlePageNumber,
    onHandlePageSize: handlePageSize,
  }, dispatch);
  return actions;
};

TrafficFwdPoliciesTable.propTypes = {
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  actions: PropTypes.shape({}),
  addPolicy: PropTypes.bool,
  authType: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  cancelHandle: PropTypes.func,
  duplicateRow: PropTypes.bool,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  handleSortBy: PropTypes.func,
  handleToggleDuplicateForm: PropTypes.func,
  handleToggleEditCopyForm: PropTypes.func,
  handleToggleEditForm: PropTypes.func,
  handleToggleForm: PropTypes.func,
  handleToggleViewForm: PropTypes.func,
  load: PropTypes.func,
  loading: PropTypes.bool,
  modalLoading: PropTypes.bool,
  numberOfLines: PropTypes.number,
  onHandlePageNumber: PropTypes.func,
  onHandlePageSize: PropTypes.func,
  pageNumber: PropTypes.number,
  pageSize: PropTypes.number,
  searchData: PropTypes.string,
  selectedRowID: PropTypes.number,
  showDeleteForm: PropTypes.bool,
  showForm: PropTypes.bool,
  sortDirection: PropTypes.string,
  sortField: PropTypes.string,
  t: PropTypes.func,
  trafficFwdPoliciesTableData: PropTypes.arrayOf(PropTypes.shape()),
};

TrafficFwdPoliciesTable.defaultProps = {
  accessPermissions: {},
  actions: {},
  addPolicy: false,
  authType: '',
  cachedData: [],
  cancelHandle: noop,
  duplicateRow: false,
  handleClearTable: noop,
  handleDelete: noop,
  handleDeleteConfirmationForm: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  handleSortBy: noop,
  handleToggleDuplicateForm: noop,
  handleToggleEditCopyForm: noop,
  handleToggleEditForm: noop,
  handleToggleForm: noop,
  handleToggleViewForm: noop,
  load: noop,
  loading: true,
  modalLoading: false,
  numberOfLines: 0,
  onHandlePageNumber: noop,
  onHandlePageSize: noop,
  pageNumber: 0,
  pageSize: 0,
  searchData: '',
  selectedRowID: null,
  showDeleteForm: false,
  showForm: false,
  sortDirection: '',
  sortField: '',
  t: (str) => str,
  trafficFwdPoliciesTableData: [],
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(TrafficFwdPoliciesTable),
);
