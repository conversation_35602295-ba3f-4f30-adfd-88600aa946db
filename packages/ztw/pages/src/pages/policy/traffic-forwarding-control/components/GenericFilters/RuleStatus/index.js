import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import StatusDropdown from 'commonConnectedComponents/dropdown/StatusDropdown.js';
import {
  required,
} from 'utils/validations';

function RuleStatus({ value, viewOnly, t }) {
  return (
    <div className="g-left">
      <FormFieldLabel text={t('RULE_STATUS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_RULE_STATUS')} />
      {!viewOnly
        ? (
          <Field
            id="ruleStatus"
            name="ruleStatus"
            component={StatusDropdown}
            validate={[required]} />
        ) : (
          <p className="disabled-input">{ value === 'DISABLED' ? t('DISABLED') : t('ENABLED')}</p>
        )}
    </div>
  );
}
  
RuleStatus.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
RuleStatus.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
};

export default withTranslation()(RuleStatus);
