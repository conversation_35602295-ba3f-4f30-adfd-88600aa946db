// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import ApplicationsServiceGroupDropdown from 'commonConnectedComponents/dropdown/ApplicationServiceGroupDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function NetworkServices({ viewOnly, t }) {
  return (
    <div className="g-left">
      <FormFieldLabel
        text={t('APPLICATION_SERVICE_GROUPS')}
        tooltip={t('APPLICATION_SERVICE_GROUPS_TOOLTIP')} />
      <Field
        id="appServiceGroups"
        name="appServiceGroups"
        component={ApplicationsServiceGroupDropdown}
        props={{
          isViewOnly: viewOnly,
          label: t('---'),
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

NetworkServices.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};

NetworkServices.defaultProps = {
  viewOnly: false,
  t: (str) => str,
};

export default (withTranslation()(NetworkServices));
