import React, { useEffect } from 'react';
import { PropTypes } from 'prop-types';
import { connect, useDispatch, useSelector } from 'react-redux';
import { Field } from 'redux-form';
import PageTabs from 'components/navTabs/PageTabs';
import { isEmpty, get } from 'utils/lodash';
import ECRadioGroup from 'components/ecRadioGroup';
import * as trafficFwdPoliciesSelectors from 'ducks/trafficFwdPolicies/selectors';
import * as gatewayZia from 'ducks/dropdowns/gatewayZia';
import * as loginSelectors from 'ducks/login/selectors';
import {
  handleForwardMethodChange,
  handleSourceIpGroupExclusionChange,
  handleUpdateFormTabConfiguration,
} from 'ducks/trafficFwdPolicies';
import { isZPA, verifyConfigData, getSubscriptionLicenses } from 'utils/helpers';

import { withTranslation, useTranslation } from 'react-i18next';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import { bindActionCreators } from 'redux';

// eslint-disable-next-line import/no-cycle
import {
  ApplicationSegment,
  ApplicationServiceGroup,
  BCGroups,
  // Country,
  Description,
  Gateway,
  ForwardingMethod,
  ForwardToZpa,
  IpSourceGroup,
  SourceWorkloadGroups,
  IpSourceList,
  IpDestinationGroup,
  DestinationWorkloadGroups,
  IpDestinationList,
  Location,
  NetworkServices,
  NetworkServicesGroup,
  RuleOrder,
  RuleName,
  RuleStatus,
  WanSelection,
} from '..';

export function TrafficFwdPoliciesFragment(props) {
  const dispatch = useDispatch();
  const { trafficFwdPolicies } = useSelector((state) => state) || {};
  const { data: gatewayZiaData } = useSelector((state) => state.gatewayZiaDropdown);
  const accessDetailSubscriptions = useSelector(
    (state) => loginSelectors.accessDetailSubscriptionSelector(state),
  );
  const configData = useSelector((state) => loginSelectors.configDataSelector(state));
  const { forwardingMethod } = useSelector(
    (state) => trafficFwdPoliciesSelectors.formValuesSelector(state),
  ) || {};
  const {
    viewOnly, form, formMeta, formSyncErrors, actions,
  } = props;
  const { pageTabs, allAppSegments, sourceIpGroupExclusion } = form || {};
  const { appData } = trafficFwdPolicies || {};
  const {
    name, order, state, proxyGateway, defaultRule, forwardMethod,
    wanSelection, isDisablablePredefinied, isDisableablePredefiniedZTW3626,
    // is5GPredefined,
    destIpGroups,
  } = appData || {};
  const { t } = useTranslation();
  const enableAppSegment = verifyConfigData({ configData, key: 'enableAppSegmentFeature' });
  const disableExcludeSourceIp = verifyConfigData({ configData, key: 'disableExcludeSourceIp' });
  const isDeployAsGatewayAvailable = verifyConfigData({ configData, key: 'deployAsGateway' });
  const enablePredefinedPolicy = verifyConfigData({ configData, key: 'enablePredefinedPolicy' });
  const bcDevice400Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_400');
  const bcDevice600Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_600');
  const bcDevice800Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_800');
  const hasAtLeastOneSubscrption = bcDevice400Licenses > 0
  || bcDevice600Licenses > 0
  || bcDevice800Licenses > 0;
  const bcGroups = get(form, 'bcGroups', []);
  const locationName = get(form, 'locationName', []);
  const enableWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableWorkloadDiscoveryService' });
  const enableAzureWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableAzureWorkloadDiscoveryService' });
  const enableGcpWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableGcpWorkloadDiscoveryService' });

  const enableEastWestTrafficFwd = verifyConfigData({ configData, key: 'enableEastWestTrafficFwd' }) && (
    enableWorkloadDiscoveryService || enableAzureWorkloadDiscoveryService || enableGcpWorkloadDiscoveryService
  );
  const hasWorkloadEnabled = ['DROP', 'LOCAL_SWITCH'].includes(forwardingMethod?.id)
  && ((bcGroups.length > 0 && bcGroups?.every((item) => item?.extensions?.deployType === 'CLOUD'))
  || (locationName.length > 0 && locationName?.every((item) => item?.ccLocation)));

  useEffect(() => {
    actions.handleUpdateFormTabConfiguration();
    if (isEmpty(gatewayZiaData)) dispatch(gatewayZia.dropdownActions.load());
  }, [forwardingMethod]);

  return (
    <div>
      <FormSectionLabel text={t('FORWARDING_RULE')} />
      <div className="form-section">
        <div className="g-row">
          <RuleOrder
            value={order}
            viewOnly={viewOnly || defaultRule} />
          <span className="email-input-view-at">{' '}</span>
          <RuleName
            value={name}
            viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
        </div>
        <div className="g-row">
          <RuleStatus
            value={state}
            viewOnly={viewOnly || defaultRule} />
          <span className="email-input-view-at">{' '}</span>
          <ForwardingMethod
            actions={actions}
            value={forwardMethod}
            onChange={actions.handleForwardMethodChange}
            viewOnly={viewOnly || isDisablablePredefinied}
            meta={formMeta.forwardingMethod}
            error={formSyncErrors.forwardingMethod} />
        </div>
      </div>

      <FormSectionLabel text={t('CRITERIA')} />
      <div className="form-tab-group">
        <Field
          id="pageTabs"
          name="pageTabs"
          component={PageTabs} />
      </div>
      <div className="form-section">
        {pageTabs === 'GENERAL' && (
          <>
            <div className="g-row">
              <Location
                viewOnly={(viewOnly || defaultRule || isDisablablePredefinied)
                    && (!(isDisableablePredefiniedZTW3626 && enablePredefinedPolicy))}
                meta={formMeta.locationName}
                disabled={!isEmpty(form.bcGroups)}
                error={formSyncErrors.locationName} />
              {/* <span className="center-logical-label">{t('OR')}</span>
              <LocationGroup
                disabled={true || !isEmpty(form.bcGroups)}
                viewOnly={viewOnly || defaultRule} /> */}
            </div>
            <div className="g-row">
              <span className="center-logical-label">{t('OR')}</span>
              <BCGroups
                disabled={!isEmpty(form.locationName)}
                viewOnly={(viewOnly || defaultRule || isDisablablePredefinied)
                  && (!(isDisableablePredefiniedZTW3626 && enablePredefinedPolicy))} />
            </div>
          </>
        )}
        <div className={pageTabs === 'SERVICES' ? '' : 'hidden'}>
          <div className="g-row">
            <NetworkServices viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
            <span className="center-logical-label">{t('OR')}</span>
            <NetworkServicesGroup viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
          </div>
        </div>

        <div className={pageTabs === 'APPLICATIONS' ? '' : 'hidden'}>
          <div className="g-row">
            <ApplicationServiceGroup
              viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
          </div>
        </div>

        <div className={pageTabs === 'SOURCE' ? 'traffic-source' : 'hidden'}>
          {!disableExcludeSourceIp && (
            <ECRadioGroup
              id="sourceIpGroupExclusion"
              name="sourceIpGroupExclusion"
              disabled={
                isZPA(forwardingMethod.id) || viewOnly || defaultRule || isDisablablePredefinied
              }
              onChange={actions.handleSourceIpGroupExclusionChange}
              options={[{
                name: 'sourceIpGroupExclusion',
                value: 'INCLUDE',
                checked: sourceIpGroupExclusion === 'INCLUDE',
                label: t('INCLUDE'),
              },
              {
                name: 'sourceIpGroupExclusion',
                value: 'EXCLUDE',
                checked: sourceIpGroupExclusion === 'EXCLUDE',
                label: t('EXCLUDE'),
              },
              ]} />
          )}
          <IpSourceGroup
            isZpa={!disableExcludeSourceIp && isZPA(forwardingMethod.id)}
            viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
          <IpSourceList viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
          <SourceWorkloadGroups
            hasWorkloadEnabled={enableEastWestTrafficFwd}
            viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
        </div>
      </div>

      <div className={pageTabs === 'DESTINATION' ? '' : 'hidden'}>
        <div className="form-section">
          {enableAppSegment && forwardingMethod && (isZPA(forwardingMethod.id) || forwardingMethod.id === 'DROP') && (
            <ApplicationSegment
              allAppSegmentsEnabled={allAppSegments}
              viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
          )}
          {!enableAppSegment && forwardingMethod && isZPA(forwardingMethod.id) && (
            <div className="g-row">
              <div className="g-left">
                <FormFieldLabel text={t('APPLICATION_SEGMENT')} tooltip={t('TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT')} />
                <p className="disabled-input">{t('ZPA_EDGE_APP_SEGMENT')}</p>
              </div>
            </div>
          )}
          {!isZPA(forwardingMethod.id) ? (
            <>
              {forwardingMethod.id === 'DROP' && (
                <div className="separator-line inline-position-relative">
                  <span className="center-logical-label label-and">{t('AND')}</span>
                </div>
              )}
              <div className="g-row">
                <IpDestinationGroup
                  destIpGroups={destIpGroups}
                  ruleName={name}
                  viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
              </div>
              <IpDestinationList
                viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
              <div className="g-row">
                <DestinationWorkloadGroups
                  hasWorkloadEnabled={enableEastWestTrafficFwd && hasWorkloadEnabled}
                  viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />
              </div>
            </>
          ) : ''}
        </div>
      </div>

      {forwardingMethod && forwardingMethod.id === 'ZIA' && (
        <>
          <FormSectionLabel text={t('ACTION')} />
          <div className="form-section">
            <div className="g-row">
              <Gateway
                value={proxyGateway ? proxyGateway.name : ''}
                viewOnly={viewOnly || isDisablablePredefinied}
                meta={formMeta.gateway}
                error={formSyncErrors.gateway} />
              {isDeployAsGatewayAvailable && (
                <WanSelection
                  value={wanSelection ? wanSelection.name : ''}
                  viewOnly={viewOnly || isDisablablePredefinied} />
              )}
            </div>
          </div>
        </>
      )}

      {isZPA(forwardingMethod?.id) && (
        <>
          <FormSectionLabel text={t('ACTION')} />
          <div className="form-section">
            <div className="g-row">
              <ForwardToZpa
                t={t}
                value={proxyGateway ? proxyGateway.name : ''}
                formMeta={formMeta}
                formSyncErrors={formSyncErrors}
                viewOnly={viewOnly || isDisablablePredefinied} />
              {isDeployAsGatewayAvailable && (
                <WanSelection
                  value={wanSelection ? wanSelection.name : ''}
                  viewOnly={viewOnly || isDisablablePredefinied} />
              )}
            </div>
          </div>
        </>
      )}

      {!isZPA(forwardingMethod?.id) && !(forwardingMethod?.id === 'ZIA')
        && forwardingMethod.id !== 'DROP' && forwardingMethod.id !== 'LOCAL_SWITCH' && isDeployAsGatewayAvailable
 && (
   <>
     <FormSectionLabel text={t('ACTION')} />
     <div className="form-section">
       <div className="g-row">
         <WanSelection
           value={wanSelection ? wanSelection.name : ''}
           viewOnly={viewOnly || isDisablablePredefinied} />
       </div>
     </div>
   </>
 )}

      <Description
        t={t}
        error={formSyncErrors.description}
        viewOnly={viewOnly || defaultRule || isDisablablePredefinied} />

    </div>
    
  );
}

TrafficFwdPoliciesFragment.propTypes = {
  viewOnly: PropTypes.bool,
  form: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  actions: PropTypes.shape({
    handleUpdateFormTabConfiguration: PropTypes.func,
  }),
};
    
TrafficFwdPoliciesFragment.defaultProps = {
  viewOnly: true,
  form: {},
  formMeta: {},
  formSyncErrors: {},
  actions: ({
    handleUpdateFormTabConfiguration: null,
  }),
};

const mapStateToProps = (state) => ({
  viewOnly: trafficFwdPoliciesSelectors.viewOnlySelector(state),
  form: trafficFwdPoliciesSelectors.formValuesSelector(state),
  formMeta: trafficFwdPoliciesSelectors.formMetaSelector(state),
  formSyncErrors: trafficFwdPoliciesSelectors.formSyncErrorsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleForwardMethodChange,
    handleUpdateFormTabConfiguration,
    handleSourceIpGroupExclusionChange,
  }, dispatch);

  return {
    actions,
  };
};
// eslint-disable-next-line max-len
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(TrafficFwdPoliciesFragment));
