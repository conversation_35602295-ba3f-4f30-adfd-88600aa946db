// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import SourceWorkloadGroupsDropdown from 'commonConnectedComponents/dropdown/SourceWorkloadGroupsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function IpSourceWorkloadGroup({ viewOnly, t, hasWorkloadEnabled }) {
  if (!hasWorkloadEnabled) return <></>;

  return (
    <div className="g-row">
      <span className="center-logical-label">{t('OR')}</span>
      <div className="g-left">
        <FormFieldLabel text={t('SOURCE_WORKLOAD_GROUPS')} tooltip={t('SOURCE_WORKLOAD_GROUPS_TOOLTIP')} />
        <Field
          id="sourceWorkloadGroups"
          name="sourceWorkloadGroups"
          component={SourceWorkloadGroupsDropdown}
          props={{
            isViewOnly: viewOnly,
            label: t('---'),
          }}
          parse={(values = []) => values.map(parseDropdownValues)} />
      </div>
    </div>
  );
}

IpSourceWorkloadGroup.propTypes = {
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
  hasWorkloadEnabled: PropTypes.bool,
};

IpSourceWorkloadGroup.defaultProps = {
  t: (str) => str,
  viewOnly: false,
  hasWorkloadEnabled: false,
};

export default (withTranslation()(IpSourceWorkloadGroup));
