// @flow

import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { ipAddressesOrRanges, ipAddressesOrRangesList } from 'utils/validations';
import ListBuilder from 'components/listBuilder';
import * as TrafficFwdPoliciesSelectors from 'ducks/trafficFwdPolicies/selectors';
import { isEmpty } from 'utils/lodash';

import {
  handleSourceIpAddresses,
} from 'ducks/trafficFwdPolicies';

export function IpSourceList(props) {
  const {
    viewOnly, t, actions, sourceIpAddresses,
  } = props;
  const hasError = !isEmpty(sourceIpAddresses) && sourceIpAddresses.some((x) => ipAddressesOrRanges(x) && ipAddressesOrRanges(x) !== '');
  const toolTipSourceIPAdresses = t('TOOLTIP_SOURCE_IP_ADDRESSES').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {hasError && (
        <b className="alert-red">
          {t('VALIDATION_ERROR_INVALID_ARRAY_ELEMENTS')}
          <br />
          <br />
        </b>
      )}
      {toolTipSourceIPAdresses[0]}
      <ul className="tooltip-text-list">
        <li>{toolTipSourceIPAdresses[1]}</li>
        <li>{toolTipSourceIPAdresses[2]}</li>
        <li>{toolTipSourceIPAdresses[3]}</li>
      </ul>
      {toolTipSourceIPAdresses[4]}
      <b>
        {toolTipSourceIPAdresses[5]}
      </b>
      {toolTipSourceIPAdresses[6]}
    </>
  );

  return (
    <div className="g-row">
      <div className="g-left">
        <FormFieldLabel styleClass={hasError ? 'invalid' : ''} text={t('SOURCE_IP_ADDRESSES')} tooltip={toolTipJSX} />
        <Field
          id="sourceIpAddresses"
          name="sourceIpAddresses"
          attribute="sourceIpAddresses"
          props={{
            t,
            hasSearch: true,
            value: sourceIpAddresses,
            action: actions.onipAddresses,
            disabled: viewOnly,
            removeItemsText: t('REMOVE_ALL'),
            addButtonText: t('ADD_ITEMS'),
            placeholder: t('ADD_ITEMS'),
            styleConfig: { inputMarginRight: 10 },
            validation: ipAddressesOrRanges,
          }}
          validate={[ipAddressesOrRangesList]}
          component={ListBuilder} />
      </div>
    </div>
  );
}

IpSourceList.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  sourceIpAddresses: PropTypes.arrayOf(PropTypes.string),
  actions: PropTypes.shape({
    onipAddresses: PropTypes.func,
  }),
};

IpSourceList.defaultProps = {
  viewOnly: false,
  t: (str) => str,
  sourceIpAddresses: [],
  actions: { onipAddresses: null },
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    onipAddresses: handleSourceIpAddresses,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  sourceIpAddresses: TrafficFwdPoliciesSelectors.formValuesSelector(state)?.sourceIpAddresses,
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(IpSourceList));
