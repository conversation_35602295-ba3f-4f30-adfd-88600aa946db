// @flow

import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import DestinationWorlkoadGroupsDropdown from 'commonConnectedComponents/dropdown/DestinationWorkloadGroupsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function IpDestinationWorkloadGroup({ viewOnly, t, hasWorkloadEnabled }) {
  if (!hasWorkloadEnabled) return <></>;

  return (
    <div className="g-row">
      <span className="center-logical-label">{t('OR')}</span>
      <div className="g-left">
        <FormFieldLabel text={t('DESTINATION_WORKLOAD_GROUPS')} tooltip={t('TOOLTIP_DESTINATION_WORKLOAD_GROUPS')} />
        <Field
          id="destinationWorkloadGroups"
          name="destinationWorkloadGroups"
          component={DestinationWorlkoadGroupsDropdown}
          props={{
            isViewOnly: viewOnly,
            label: t('---'),
          }}
          parse={(values = []) => values.map(parseDropdownValues)} />
      </div>
    </div>
  );
}

IpDestinationWorkloadGroup.propTypes = {
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
  hasWorkloadEnabled: PropTypes.bool,
};

IpDestinationWorkloadGroup.defaultProps = {
  t: (str) => str,
  viewOnly: false,
  hasWorkloadEnabled: false,
};

export default (withTranslation()(IpDestinationWorkloadGroup));
