// @flow

import React from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import NetworkServicesGroupDropdown from 'commonConnectedComponents/dropdown/NetworkServicesGroupDropdown';
import { changePageTab, toggleAddForm, loadData } from 'ducks/networkServices';

const parseDropdownValues = (value) => get(value, 'original', value);

export function NetworkServicesGroup({ viewOnly, t }) {
  const dispatch = useDispatch();

  return (
    <div className="g-right">
      <FormFieldLabel text={t('NETWORK_SERVICES_GROUP')} tooltip={t('TOOLTIP_POLICY_CC_TF_CRITERIA_NW_SERVICE_GROUPS')} />
      <Field
        id="networkServicesGroup"
        name="networkServicesGroup"
        component={NetworkServicesGroupDropdown}
        props={{
          place: 'right',
          label: t('---'),
          isViewOnly: viewOnly,
          addButton: true,
          onAddButtonClick: () => {
            dispatch(loadData());
            dispatch(changePageTab('NETWORK_SERVICE_GROUPS'));
            dispatch(toggleAddForm(true, 'ADD_NETWORK_SERVICE_GROUP'));
          },
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

NetworkServicesGroup.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};

NetworkServicesGroup.defaultProps = {
  viewOnly: false,
  t: (str) => str,
};

export default (withTranslation()(NetworkServicesGroup));
