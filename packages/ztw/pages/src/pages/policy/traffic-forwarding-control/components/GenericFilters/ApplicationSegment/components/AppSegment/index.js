import React from 'react';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import AppSegmentsDropdown from 'commonConnectedComponents/dropdown/AppSegmentsDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export function Location({
  t, viewOnly, error, disabled,
}) {
  const hasError = !!error;

  return (
    <div className={`g-left ${hasError ? 'error' : ''} `}>
      <FormFieldLabel
        text={t('APPLICATION_SEGMENTS')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        error={hasError ? (t(error)).replace('{0}', '8') : null}
        tooltip={t('TOOLTIP_POLICY_APP_SEGMENT')} />
      <Field
        id="zpaApplicationSegments"
        name="zpaApplicationSegments"
        component={AppSegmentsDropdown}
        props={{
          disabled,
          isViewOnly: viewOnly,
          label: t('---'),
          classes: hasError ? ['error'] : [],
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Location.propTypes = {
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
  error: PropTypes.string,
  disabled: PropTypes.bool,
};

Location.defaultProps = {
  t: (str) => str,
  viewOnly: false,
  error: null,
  disabled: false,
};

export default (withTranslation()(Location));
