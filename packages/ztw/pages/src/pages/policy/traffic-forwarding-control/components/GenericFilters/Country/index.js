// @flow

import React from 'react';
import { useSelector } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import CountryDropdown from 'commonConnectedComponents/dropdown/CountryDropdown';
import * as DestinationIPSelectors from 'ducks/destinationIpGroups/selectors';

const parseDropdownValues = (value) => {
  return get(value, 'original', value);
};

export function Country({ viewOnly, t }) {
  const showDestinationIpForm = useSelector(
    (state) => DestinationIPSelectors.showFormSelector(state),
  );
  if (showDestinationIpForm) return <> </>;

  return (
    <div className="g-right">
      <FormFieldLabel text={t('DESTINATION_COUNTRY')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_COUNTRY')} />
      <Field
        id="countries"
        name="countries"
        component={CountryDropdown}
        props={{
          isViewOnly: viewOnly,
          label: t('----'),
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

Country.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};
  
Country.defaultProps = {
  viewOnly: false,
  t: (str) => str,
};

export default (withTranslation()(Country));
