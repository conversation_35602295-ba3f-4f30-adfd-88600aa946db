// @flow

import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import {
  ipAddressesOrRangesOrFqdn, ipAddressesOrRangesOrFqdnList,
  ipAddressesOrRangesOrFqdnNoWildCard, ipAddressesOrRangesOrFqdnListNoWildCard,
} from 'utils/validations';
import ListBuilder from 'components/listBuilder';
import * as TrafficFwdPoliciesSelectors from 'ducks/trafficFwdPolicies/selectors';
import { isEmpty } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';

import {
  handleDestinationIpAddresses,
} from 'ducks/trafficFwdPolicies';

export function IpDestinationList(props) {
  const {
    viewOnly, t, actions, destinationIpAddresses,
  } = props;
  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const disableWfqdn = verifyConfigData({ configData, key: 'disableWfqdn' });

  const hasError = disableWfqdn
    ? !isEmpty(destinationIpAddresses) && destinationIpAddresses.some((x) => ipAddressesOrRangesOrFqdnNoWildCard(x) && ipAddressesOrRangesOrFqdnNoWildCard(x) !== '')
    : !isEmpty(destinationIpAddresses) && destinationIpAddresses.some((x) => ipAddressesOrRangesOrFqdn(x) && ipAddressesOrRangesOrFqdn(x) !== '');

  return (
    <div className="g-row">
      <span className="center-logical-label">{t('OR')}</span>

      {disableWfqdn && (
        <div className="g-left">
          <FormFieldLabel styleClass={hasError ? 'invalid' : ''} text={t('DESTINATION_IP_AND_FQDN_ACCDRESSES')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES')} />
          <Field
            id="destinationIpAddresses"
            name="destinationIpAddresses"
            attribute="destinationIpAddresses"
            props={{
              t,
              hasSearch: true,
              value: destinationIpAddresses,
              action: actions.onipAddresses,
              disabled: viewOnly,
              removeItemsText: t('REMOVE_ALL'),
              addButtonText: t('ADD_ITEMS'),
              placeholder: t('ADD_ITEMS'),
              styleConfig: { inputMarginRight: 10 },
              validation: ipAddressesOrRangesOrFqdnNoWildCard,
            }}
            validate={[ipAddressesOrRangesOrFqdnListNoWildCard]}
            component={ListBuilder} />
        </div>
      )}

      {!disableWfqdn && (
        <div className="g-left">
          <FormFieldLabel styleClass={hasError ? 'invalid' : ''} text={t('IP_ADDRESS_OR_WILDCARD_FQDN')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_AND_FQDN_ACCDRESSES')} />
          <Field
            id="destinationIpAddresses"
            name="destinationIpAddresses"
            attribute="destinationIpAddresses"
            props={{
              t,
              hasSearch: true,
              value: destinationIpAddresses,
              action: actions.onipAddresses,
              disabled: viewOnly,
              removeItemsText: t('REMOVE_ALL'),
              addButtonText: t('ADD_ITEMS'),
              placeholder: t('ADD_ITEMS'),
              styleConfig: { inputMarginRight: 10 },
              validation: ipAddressesOrRangesOrFqdn,
            }}
            validate={[ipAddressesOrRangesOrFqdnList]}
            component={ListBuilder} />
        </div>
      )}

    </div>
  );
}

IpDestinationList.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  destinationIpAddresses: PropTypes.arrayOf(PropTypes.string),
  actions: PropTypes.shape({
    onipAddresses: PropTypes.func,
  }),
};

IpDestinationList.defaultProps = {
  viewOnly: false,
  t: (str) => str,
  destinationIpAddresses: [],
  actions: { onipAddresses: null },
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    onipAddresses: handleDestinationIpAddresses,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  // eslint-disable-next-line max-len
  destinationIpAddresses: TrafficFwdPoliciesSelectors.formValuesSelector(state).destinationIpAddresses,
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(IpDestinationList));
