import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import GatewayDropdownList from 'commonConnectedComponents/dropdown/GatewayDropdownList';
import {
  requiredId,
} from 'utils/validations';

function GatewayDropdown({
  value, viewOnly, t, meta, error,
}) {
  const hasError = meta.touched && !!error;

  return (
    <div className="g-left">
      <FormFieldLabel
        error={hasError ? t(error) : null}
        styleClass={`${hasError ? 'invalid' : ''}`}
        text={t('FORWARD_TO_PROXY_GATEWAY')} />
      {!viewOnly
        ? (
          <Field
            id="gatewayZPA"
            name="gatewayZPA"
            component={GatewayDropdownList}
            validate={[requiredId]} />
        ) : (
          <p className="disabled-input">{ value }</p>
        )}
    </div>
  );
}
  
GatewayDropdown.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  meta: PropTypes.shape({
    touched: PropTypes.bool,
  }),
  error: PropTypes.string,
};
  
GatewayDropdown.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
  meta: {},
  error: null,
};

export default withTranslation()(GatewayDropdown);
