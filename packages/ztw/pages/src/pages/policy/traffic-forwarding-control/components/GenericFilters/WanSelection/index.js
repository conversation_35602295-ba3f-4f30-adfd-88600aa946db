import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import WanSelectionDropdownList from 'commonConnectedComponents/dropdown/WanSelectionDropdownList';
import {
  requiredId,
} from 'utils/validations';

function WanSelection({
  value, viewOnly, t, meta, error,
}) {
  const hasError = meta.touched && !!error;

  return (
    <div className="g-right wan-selection">
      <FormFieldLabel
        error={hasError ? t(error) : null}
        tooltip={t('TOOLTIP_WAN_SELECTION')}
        styleClass={`${hasError ? 'invalid' : ''}`}
        text={t('WAN_SELECTION')} />
      {!viewOnly
        ? (
          <Field
            id="wanSelection"
            name="wanSelection"
            component={WanSelectionDropdownList}
            validate={[requiredId]} />
        ) : (
          <p className="disabled-input">{ value }</p>
        )}
    </div>
  );
}
  
WanSelection.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
  meta: PropTypes.shape({
    touched: PropTypes.bool,
  }),
  error: PropTypes.string,
};
  
WanSelection.defaultProps = {
  value: '',
  viewOnly: false,
  t: (str) => str,
  meta: {},
  error: null,
};

export default withTranslation()(WanSelection);
