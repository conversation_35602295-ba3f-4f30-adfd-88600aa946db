import React from 'react';
import PropTypes from 'prop-types';
import { ToggleCheckBox } from 'components/ecToggle';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { Field } from 'redux-form';
import { AppSegment, AppSegmentGroup } from './components';

function ApplicationSegment({ t, viewOnly, allAppSegmentsEnabled }) {
  return (
    <>
      <div className="g-row">
        <div className="g-left">
          <FormFieldLabel text={t('APPLY_TO_ALL_APP_SEGMENTS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_APPLICATION_SEGMENT')} />
          <Field
            id="allAppSegments"
            name="allAppSegments"
            component={ToggleCheckBox}
            props={{ disabled: viewOnly }}
            type="checkbox"
            styleClass="ec-toggle-checkbox" />
        </div>
      </div>
      {!allAppSegmentsEnabled && (
        <div className="g-row">
          <AppSegment viewOnly={viewOnly} />
          <span className="center-logical-label">{t('OR')}</span>
          <AppSegmentGroup viewOnly={viewOnly} />
        </div>
      )}
    </>
  );
}

ApplicationSegment.propTypes = {
  t: PropTypes.func,
  viewOnly: PropTypes.bool,
  allAppSegmentsEnabled: PropTypes.bool,
};
  
ApplicationSegment.defaultProps = {
  t: (str) => str,
  viewOnly: false,
  allAppSegmentsEnabled: false,
};

export default withTranslation()(ApplicationSegment);
