import React from 'react';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { withTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { accessSubscriptionSelector } from 'ducks/login/selectors';
import has5Gsku from 'utils/helpers/has5Gsku';

function ForwardToZpa(props) {
  const { t } = props;

  const accessSubscriptions = useSelector(accessSubscriptionSelector);
  
  return (
    <>
      <div className="g-left">
        <FormFieldLabel text={t('FORWARD_TO_ZPA_GATEWAY')} />
        <p className="disabled-input">{t('DEFAULT_GATEWAY')}</p>
      </div>
      {has5Gsku(accessSubscriptions) && (
        <span className="center-logical-label no-background"> </span>
      )}
    </>
  );
}

ForwardToZpa.propTypes = {
  t: PropTypes.func,
};

ForwardToZpa.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(ForwardToZpa);
