// @flow

import React from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { Field } from 'redux-form';
import { NavLink } from 'react-router-dom';
import { HELP_ARTICLES } from 'config';
import { setShowHelp, loadArticle } from 'ducks/help';
import { withTranslation } from 'react-i18next';
import NetworkServicesDropdown from 'commonConnectedComponents/dropdown/NetworkServicesDropdown';
import { changePageTab, toggleAddForm } from 'ducks/networkServices';

const parseDropdownValues = (value) => get(value, 'original', value);

export function NetworkServices({ viewOnly, t }) {
  const dispatch = useDispatch();

  const handleClickNwService = () => {
    dispatch(loadArticle(HELP_ARTICLES.NETWORK_SERVICE));
    dispatch(setShowHelp(true));
  };

  const toolTipText = t('TOOLTIP_POLICY_FIREWALL_CRITERIA_NW_SERVICES').split(/{[0-9]}/g);
  const toolTipJSX = (
    <>
      {toolTipText[0]}
      <b>{toolTipText[1]}</b>
      {toolTipText[2]}
      <NavLink className="tooltip-navlink" to="?" onClick={handleClickNwService}>
        <b>{toolTipText[3]}</b>
      </NavLink>
      {toolTipText[4]}
    </>
  );

  return (
    <div className="g-left">
      <FormFieldLabel text={t('NETWORK_SERVICES')} tooltip={toolTipJSX} />
      <Field
        id="networkServices"
        name="networkServices"
        component={NetworkServicesDropdown}
        props={{
          isViewOnly: viewOnly,
          label: t('---'),
          addButton: !viewOnly,
          onAddButtonClick: () => {
            dispatch(changePageTab('NETWORK_SERVICES'));
            dispatch(toggleAddForm(true, 'ADD_NETWORK_SERVICE'));
          },
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

NetworkServices.propTypes = {
  viewOnly: PropTypes.bool,
  t: PropTypes.func,
};

NetworkServices.defaultProps = {
  viewOnly: false,
  t: (str) => str,
};

export default (withTranslation()(NetworkServices));
