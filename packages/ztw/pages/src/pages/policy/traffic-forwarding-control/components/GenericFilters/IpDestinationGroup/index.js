// @flow

import React from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';
import { FormFieldLabel } from 'components/label';
import { get } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';
import { Field } from 'redux-form';
import { withTranslation } from 'react-i18next';
import IpDestinationGroupDropdown from 'commonConnectedComponents/dropdown/IpDestinationGroupDropdown';
import IpDestinationGroupDropdownWithWildcard from 'commonConnectedComponents/dropdown/IpDestinationGroupDropdownWithWildcard';
import { toggleForm } from 'ducks/destinationIpGroups';

const parseDropdownValues = (value) => get(value, 'original', value);

export function IpDestinationGroup({
  viewOnly, t, destIpGroups, ruleName,
}) {
  const dispatch = useDispatch();
  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const disableWfqdn = verifyConfigData({ configData, key: 'disableWfqdn' });

  const enablePredefinedPolicy = verifyConfigData({ configData, key: 'enablePredefinedPolicy' });
    
  if (!enablePredefinedPolicy && ['Direct rule for LAN Destinations Group',
    'Direct rule for WAN Destinations Group'].includes(ruleName)) {
    return (
      <div className="g-left">
        {!disableWfqdn && <FormFieldLabel text={t('DESTINATION_IPV4_GROUPS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS')} />}
        {disableWfqdn && <FormFieldLabel text={t('DESTINATION_IP_AND_FQDN_GROUPS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS')} />}
        <p className="disabled-input">{ t(destIpGroups[0]?.name) }</p>
      </div>
    );
  }

  return (
    <div className="g-left">
      {!disableWfqdn && <FormFieldLabel text={t('DESTINATION_IPV4_GROUPS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS')} />}
      {disableWfqdn && <FormFieldLabel text={t('DESTINATION_IP_AND_FQDN_GROUPS')} tooltip={t('TOOLTIP_POLICY_FIREWALL_DESTINATION_IP_GROUPS')} />}
      <Field
        id="ipDestinationGroup"
        name="ipDestinationGroup"
        component={disableWfqdn
          ? IpDestinationGroupDropdown
          : IpDestinationGroupDropdownWithWildcard}
        props={{
          label: t('---'),
          isViewOnly: viewOnly,
          addButton: !viewOnly,
          onAddButtonClick: () => dispatch(toggleForm(null, true, 'ADD_DESTINATION_IP')),
        }}
        parse={(values = []) => values.map(parseDropdownValues)} />
    </div>
  );
}

IpDestinationGroup.propTypes = {
  destIpGroups: PropTypes.arrayOf({
    name: PropTypes.string,
  }),
  viewOnly: PropTypes.bool,
  ruleName: PropTypes.string,
  t: PropTypes.func,
};

IpDestinationGroup.defaultProps = {
  destIpGroups: [],
  viewOnly: false,
  ruleName: '',
  t: (str) => str,
};

export default (withTranslation()(IpDestinationGroup));
