## Connected Components

Connected components wire Presentational components with state in redux. They also pass redux wired callback to Presentational components for interactions. 

```
// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { noop } from 'utils/lodash';

import { getUxScoreOverTime } from ducks/chart';
import Chart from 'components/chart';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors';

class UxScoreOverTime extends Component {
    static propTypes = {
      loading: PropTypes.bool,
      error: PropTypes.shape({}),
      data: PropsTypes.shape({ PropTypes.array }),
      actions: PropTypes.shape({
        load: PropTypes.func,
      }),
    };

    static defaultProps = {
      data: [],
      loading: true, // default state
      error: null,
      actions: {
        load: noop // default behavior is noop (no operation)
      }
    };

    componentDidMount() {
      // note load points to getUxScoreOverTime
      // testing becomes easier if we follow this.
      const { actions: { load } } = this.props;
      load();
    }

    render() {
      const { data } = this.props;
      return (
        <div className="chart_container">
          <Loading {...this.props}>
            <ServerError {...this.props}>
              <Chart data={data} />
            </ServerError>
          </Loading>
        </div>
      );
    }
}

const mapStateToProps = (state, ownProps) => {
  return {
    ...state.uxscoreovertime
  };
};

const mapDispatchToProps = (dispatch, ownProps) => {
  const actions = bindActionCreators({
        load: getUxScoreOverTime,
    }, dispatch);
    
    return {
      actions,
      //other things compponent may need but does not need to disptched
    };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(UxScoreOverTime);

```
