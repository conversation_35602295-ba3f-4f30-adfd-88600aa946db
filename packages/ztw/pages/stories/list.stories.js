import React from 'react';
import { storiesOf } from '@storybook/react';
import '../src/css/rapp.css';
import Dropdown from '../src/components/list/Dropdown';
// import DropdownSearch from '../src/components/list/DropdownSearch';
import * as MyProfileConsts from '../src/ducks/myProfile/constants';

export const locNames = {
  'en-US': 'English (US)',
  'fr-FR': 'Français',
  'de-DE': 'Deutsch',
  'ja-JP': '日本語',
  'es-ES': 'Español',
  'zh-CN': '中文',
};

export const locTime = {
  'US/Pacific': 'GMT+08:00',
  'GMT+10:30': 'GMT+10:30',
  'GMT+12:00': 'GMT+12:00',
  'GMT-03:30': 'GMT-03:30',
  'GMT-05:00': 'GMT-05:00',
  'GMT-08:00': 'GMT-08:00',
};

const style = {
  paddingLeft: '10px',
  paddingTop: '10px',
};

storiesOf('List', module)
  .add('Dropdown', () => {
    return (
      <div style={style}>
        <Dropdown
          def="en-US"
          list={MyProfileConsts.locNames}
          actionCallback={() => undefined} />
      </div>
    );
  });


// storiesOf('List', module)
//   .add('Dropdown with Search', () => {
//     return (
//       <DropdownSearch
//         def="(GMT+00:00) Atlantic/Faeroe"
//         list={MyProfileConsts.locTime}
//         actionCallback={() => undefined} />
//     );
//   });
