import React from 'react';
import { storiesOf } from '@storybook/react';
import ToggleSwitch from '../src/components/toggle/ToggleSwitch';

storiesOf('ToggleSwitch', module)
  .add('Toggle ON', () => {
    return (
      <ToggleSwitch label="Toggle ON" value propertyName="autoDashboardRefresh" actionCallback={() => undefined} />
    );
  });

storiesOf('ToggleSwitch', module)
  .add('Toggle OFF', () => {
    return (
      <ToggleSwitch label="Toggle OFF" value={false} propertyName="autoDashboardRefresh" actionCallback={() => undefined} />
    );
  });
