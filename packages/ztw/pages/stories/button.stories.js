import React from 'react';
import { storiesOf } from '@storybook/react';
import Button from '../src/components/button/Button';
import RadioButton from '../src/components/button/RadioButton';

storiesOf('Button', module)
  .add('Primary Button', () => {
    return (
      <Button label="Primary Button" enable />
    );
  });


storiesOf('Button', module)
  .add('Save Button', () => {
    return (
      <Button label="Big Button" cclass="big" />
    );
  });

storiesOf('Button', module)
  .add('Cancel Button', () => {
    return (
      <Button label="Cancel Button" cclass="big no-bkgrd" />
    );
  });

storiesOf('Button', module)
  .add('Cancel Disabled', () => {
    return (
      <Button label="Cancel Button" cclass="big no-bkgrd disabled" />
    );
  });

const list = [
  {
    id: 1, displayText: 'Enable', value: true, classNames: ['active'],
  },
  {
    id: 2, displayText: 'Disable', value: true, classNames: [''],
  },
];

storiesOf('Button', module)
  .add('Radio Button', () => {
    return (
      <RadioButton propertyName="Radio Button" list={list} actionCallback={() => undefined} />
    );
  });
