{"name": "one-ui", "version": "1.7.0", "private": true, "scripts": {"dev": "next dev", "dev:multicloud": "pnpm gen:multicloud && pnpm dev", "gen:multicloud": "ts-node ./scripts/multicloud.ts --verbose --clouds 5 --weight 0.6 && prettier --write --log-level silent ./configs/data/cloud_simulation.ts", "routing:breadcrumbs": "tsx ./scripts/zstream/export-routes.ts --verbose", "zuxp-service": "docker-compose -f ../../../docker/service/docker-compose.yml up", "build:local": "NEXT_PUBLIC_LOCAL=1 pnpm build", "build": "next build", "build:debug": "next build --debug", "build:heap-analysis": "next build --experimental-debug-memory-usage", "postbuild": "cp -r public .next/standalone/packages/xc/app/public && cp -r .next/static .next/standalone/packages/xc/app/.next/static", "serve": "node .next/standalone/packages/xc/app/server.js", "lint": "NODE_OPTIONS=--max-old-space-size=8192 next lint", "mockery": "mockery --dir ./mock_routes", "mockery:unhappy": "mockery --dir ./mock_routes --unhappy", "prettier": "pnpm prettier:config --check", "prettier:info": "prettier -v", "prettier:config": "prettier '{app,components,configs,context,hoc,hooks,types,utils,telemetry,modules,scripts}/**/*.{md,json,css,js,jsx,ts,tsx}'", "format:code": "pnpm prettier:config --write", "format": "pnpm prettier:config --write && next lint --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook-test": "storybook build --test", "build-analyze": "ANALYZE=true pnpm build", "verify": "pnpm prettier && pnpm lint", "verify:strict": "pnpm prettier && pnpm lint --max-warnings=0", "test:ci": "vitest run --config ./vitest.config.mjs", "test": "vitest run --config ./vitest.config.mjs --reporter=default", "test:ui": "vitest --ui --config ./vitest.config.mjs --reporter=default", "test:watch": "vitest --watch --config ./vitest.config.mjs --reporter=default", "test:coverage": "vitest run --coverage --config ./vitest.config.mjs", "generate:ztds-types": "pwagger-typescript-api -p types/ztds/swagger.json -o types/ztds --unwrap-response-data --type-prefix ZTDS --sort-types --module-name-first-tag --modular --single-http-client"}, "dependencies": {"@aae/pages": "workspace:*", "@amcharts/amcharts5": "5.10.5", "@amcharts/amcharts5-geodata": "5.1.4", "@flipt-io/flipt-client-browser": "0.4.0", "@floating-ui/react": "0.26.24", "@fortawesome/fontawesome-pro": "6.5.1", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/free-brands-svg-icons": "6.6.0", "@fortawesome/free-solid-svg-icons": "6.6.0", "@fortawesome/pro-light-svg-icons": "6.5.1", "@fortawesome/pro-regular-svg-icons": "6.5.1", "@fortawesome/pro-solid-svg-icons": "6.5.1", "@fortawesome/react-fontawesome": "0.2.2", "@ma/core": "workspace:*", "@opentelemetry/api": "1.9.0", "@opentelemetry/context-zone": "1.2.0", "@opentelemetry/exporter-trace-otlp-http": "0.55.0", "@opentelemetry/instrumentation": "0.55.0", "@opentelemetry/instrumentation-document-load": "0.42.0", "@opentelemetry/instrumentation-fetch": "0.55.0", "@opentelemetry/resources": "1.28.0", "@opentelemetry/sdk-trace-web": "1.28.0", "@opentelemetry/semantic-conventions": "1.27.0", "@reduxjs/toolkit": "2.2.7", "@sentry/nextjs": "^9.13.0", "@up/components": "workspace:*", "@up/navigation": "workspace:*", "@up/performance": "workspace:*", "@up/std": "workspace:*", "@up/stores": "workspace:*", "@up/telemetry": "workspace:*", "@up/ztb": "workspace:*", "@xc/common": "workspace:*", "@xc/homepage": "workspace:*", "@zia/combined-packages": "workspace:*", "@zia/rbac": "workspace:*", "@zid/pages": "workspace:*", "@zms/pages": "workspace:*", "@zms/utils": "workspace:*", "@zs-nimbus/core": "catalog:nimbus", "@zs-nimbus/dataviz-colors": "catalog:nimbus", "@zs-nimbus/foundations": "catalog:nimbus", "@zscaler/copilot": "0.0.31", "@zscaler/ec-domain": "1.0.0-62", "@zscaler/zui-component-library": "2.15.0", "@ztds/airgap": "npm:@ztds/airgap@8.0.2", "@zuxp/edge-ux": "6.2.95", "@zuxp/zdx-prod-oneui": "0.1.0-110", "@zuxp/risk360": "1.0.15", "@zuxp/zpa": "25.294.1", "@ztw/pages": "workspace:*", "d3": "7.9.0", "@types/d3": "7.4.3", "axios": "1.8.1", "classnames": "2.5.1", "clsx": "2.1.1", "dayjs": "1.11.13", "flag-icons": "7.2.3", "i18next": "23.15.1", "i18next-browser-languagedetector": "8.0.0", "i18next-http-backend": "2.6.2", "lodash-es": "4.17.21", "lodash.debounce": "4.0.8", "lodash.merge": "4.6.2", "next": "14.2.28", "node-forge": "1.3.1", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "4.1.2", "react-i18next": "15.0.2", "react-redux": "9.1.2", "react-router-dom": "6.26.2", "recharts": "2.15.1", "swr": "2.2.5", "tailwind-merge": "2.6.0", "ts-key-enum": "3.0.13", "userpilot": "1.3.8", "uuid": "11.1.0", "valtio": "2.1.3"}, "devDependencies": {"@dnd-kit/core": "6.1.0", "@next/bundle-analyzer": "14.2.28", "@storybook/addon-designs": "catalog:storybook", "@storybook/addon-essentials": "catalog:storybook", "@storybook/addon-interactions": "catalog:storybook", "@storybook/addon-links": "catalog:storybook", "@storybook/addon-mdx-gfm": "catalog:storybook", "@storybook/nextjs": "catalog:storybook", "@storybook/react": "catalog:storybook", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@types/lodash": "4.17.15", "@types/lodash-es": "4.17.12", "@types/node": "22.13.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/uuid": "10.0.0", "@types/yargs": "17.0.33", "@up/eslint-plugin-unified-platform": "workspace:*", "@up/prettier-config": "workspace:*", "@vitejs/plugin-react": "4.3.4", "@vitest/coverage-istanbul": "3.1.1", "@vitest/ui": "3.0.3", "@xc/legacy-components": "workspace:*", "@xc/mockery-server": "0.0.10", "@xc/webpack-copy-plugin": "workspace:*", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-next": "14.2.28", "eslint-config-prettier": "10.0.2", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-storybook": "0.11.3", "eslint-plugin-typescript-sort-keys": "3.3.0", "jsdom": "26.0.0", "next-compose-plugins": "2.2.1", "postcss": "8.5.3", "postcss-url": "10.1.3", "prettier": "3.5.2", "prettier-plugin-tailwindcss": "0.6.11", "storybook": "catalog:storybook", "swagger-typescript-api": "13.0.23", "tailwindcss": "3.4.17", "timezone-soft": "1.5.2", "ts-node": "10.9.2", "tsx": "4.19.4", "typescript": "5.7.3", "typescript-eslint": "8.25.0", "valtio": "2.1.2", "vite-tsconfig-paths": "5.1.4", "vitest": "3.1.1", "webpack": "5.94.0", "yargs": "17.7.2"}, "volta": {"extends": "../../../package.json"}, "prettier": "@up/prettier-config"}